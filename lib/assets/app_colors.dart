import 'package:flutter/material.dart';

class AppColors {
  static const Color bgColor = Colors.white;
  static const Color appBarColor = Color(0xFFF8F7FC);
  static const Color primaryColor = Color(0xFF907F8A);
  static const Color primaryRaised = Color(0xFFF78684);
  static const Color filterColor = Color(0xFFFF77CE);
  static const Color mainTextColor33 = Color(0xFF333333);
  static const Color subTextColor66 = Color(0xFF666666);
  static const Color thridTextColor99 = Color(0xFF999999);
  static const Color borderColor = Color(0xFFF9C006);
  static const Color borderColor2 = Color(0xFFFEAC577);
  static const Color borderColor1F1 = Color(0xFFF1F1F1);
  static const Color lineColor = Color(0xFF939496);
  static const Color grey6a = Color(0xFF6a6a6a);
  static const Color textColor77 = Color(0xFFFF7777);
  static const Color primaryDisable = Color(0xFF202136);
  static const Color divideColor = Color(0xFF9493B1);
  static const Color color0xff4e4e4e = Color(0xFF4E4E4E);
  static const Color back0xff383838 = Color(0xff383838);
  static const Color color0xffFFCC33 = Color(0xffFFCC33);
  static const Color color0x9E8D5A = Color(0xff9E8D5A);


  /// 主背景色调
  static const backGroundRaiseds = <Color>[
    Color(0xFFF3F5F9),
    Color(0xFFF3F5F9)
  ];
  /// 主背景色调
  static const backGround =  Color(0xFFF3F5F9);

  /// 主背景色调
  static const backGroundRaiseds2 = <Color>[
    Color(0xFF000000),
    Color(0xFF000000)
  ];

  /// 背景色
  static const Color backGroundColorBF2 = Color(0xFFF6EBF2);
  static const Color backGroundColor7FD = Color(0xFFFFF7FD);
  static const Color backGroundColor5F5 = Color(0xFFFAF5F5);
  static const Color backGroundColor606 = Color(0xFF241606);
  static const Color backGroundColor8ED = Color(0xFFFFF8ED);

  /// 字体色
  static const Color textColorAD2 = Color(0xFFD6CAD2);
  static const Color textColorEFE = Color(0xFFFFFEFE);
  static const Color textColor4DD = Color(0xFFBDC4DD);
  static const Color textColor362 = Color(0xFFFFD362);
  static const Color textColor3AA = Color(0xFFAFA3AA);
  static const Color textColor9DA = Color(0xFFFFF9DA);
  static const Color textColor6DF = Color(0xFFFFF6DF);
  static const Color textColor253 = Color(0xFFE6A253);
  static const Color textColorC85 = Color(0xFFB09C85);
  static const Color textColor43A = Color(0xFFEBA43A);
  static const Color textColor353 = Color(0xFFE6A353);
  static const Color textColor369 = Color(0xFF9C8369);
  static const Color textColor6A6 = Color(0xFFA6A6A6);
  static const Color textColor8FF = Color(0xFF2388FF);
  static const Color textColor8FB = Color(0xFFC5D8FB);

  /// 按钮背景色
  static const Color btnColor919 = Color(0xFFE91919);
  static const Color btnColorB52 = Color(0xFFD99B52);
  static const Color btnColor7C0 = Color(0xFFF6E7C0);
  static const Color btnColor739 = Color(0xFF524739);
  static const Color btnColor51D = Color(0xFF4B351D);
  static const Color btnColor43B = Color(0xFF88643B);
  static const Color btnColor036 = Color(0xFF7E6036);

  /// icons背景色
  static const Color iconsColor67E = Color(0xFFD6B67E);
  static const Color iconsColor43F = Color(0xFF9E743F);
  static const Color iconsColorCD2 = Color(0xFFE6DCD2);

  /// 阴影色
  static const Color shadowColor6E8 = Color(0xFFF0D6E8);

  /// 边框色
  static const Color borderColorB51 = Color(0xFF826B51);

  /// lines
  static const Color lineColor0F0 = Color(0xFFF0F0F0);

  /// 主板色，凸出部分/前景色带渐变，主凸板色
  static const primaryRaiseds = <Color>[Color(0xFFFF6B00), Color(0xFFFF3D00)];
  static const vipBackRaiseds = <Color>[
    AppColors.backGroundColor606,
    AppColors.backGroundColor606
  ];
  static const vipItemRaiseds = <Color>[Color(0xFF2E1C08), Color(0xFF382209)];
  static const titleBgRaiseds = <Color>[Color(0xFFC19E21), Color(0xFF995E1B)];

  static const Color menuBackground = Color(0xFF090912);
  static const Color itemsBackground = Color(0xFF1B2339);
  static const Color pageBackground = Color(0xFF282E45);
  static const Color mainTextColor1 = Colors.white;
  static const Color mainTextColor2 = Colors.white70;
  static const Color mainTextColor3 = Colors.white38;
  static const Color mainGridLineColor = Colors.white10;
  static const Color gridLinesColor = Color(0x11FFFFFF);

  static const Color contentColorBlack = Colors.black;
  static const Color contentColorWhite = Colors.white;
  static const Color contentColorBlue = Color(0xFF2196F3);
  static const Color contentColorYellow = Color(0xFFFFC300);
  static const Color contentColorOrange = Color(0xFFFF683B);
  static const Color contentColorGreen = Color(0xFF3BFF49);
  static const Color contentColorPurple = Color(0xFF6E1BFF);
  static const Color contentColorPink = Color(0xFFFF3AF2);
  static const Color contentColorRed = Color(0xFFE80054);
  static const Color contentColorCyan = Color(0xFF50E4FF);


  static const Color color_FF16B760 = Color(0xFF16B760);
  static const Color color_FFF7F8FA = Color(0xFFF7F8FA);
  static const Color color_99000000 = Color(0x99000000);

}
