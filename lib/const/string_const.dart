import 'package:fluwx/fluwx.dart';

const kUser = "KUSER";
const kLandBaseInfo = "kLandBaseInfo";
const kTempUser = "kTempUser";
const kUserRoleList = "kUserRoleList";
const kSystemCode = "digital-agriculture-app";
const kWechatAuthData = "kWechatCode";
const kWechatAuthUserData = "kWechatAuthUserData";
const kDeviceNameiOS = "kDeviceNameiOS";
const kDeviceNameAndroid = "kDeviceNameAndroid";
const kDefaultCity = "哈尔滨市";

//默认包名
const kAndroidPackageName = "com.beidahuangxinxi.app";
const kIosPackageName = "com.beidahuangxinxi.app";

//DaHing包名
const kAndroidPackageNameDaHing = "com.danongnongfu.app";
const kIosPackageNameDaHing = "com.danongnongfu.app";

//新疆包名
const kAndroidPackageNameXinJiang = "com.xjnongfu.app";
const kIosPackageNameXinJiang = "com.xjnongfu.app";

//deeplink host
const kAppLinkHost = "app.bdhic.com";

//android电话认证秘钥:
const kAndroidPhoneAuthKey =
    "oWagHuaZpK2RPpTF25HeNuqx6tJn8RYBtrU5b/MWRJ+//IbuGUMG/MpSanW1scF2BvrbWqI6j7DioKOaFzZxehh3vsxFbxSeBaA9VE1Gmur/kZqKt34PSBOZEgPupqM7kGhpeDb5+wXxno6jzv8HbaIdB7RIr1LqMW1mup0YsQSIJAuEbg41i2BV5sgXqSXbCydy1aoe+GyUVCseN6YhJRe4w9GvXczZV0droNw+1mO+9xOx6g5zu3guIos5xvt1E7hAgmonS7ALCq0OG/nmeCb3z0Bw716M8wu7p6z2JVqBskU+8BA5Qw==";
//ios电话认证秘钥:
const kIosPhoneAuthKey =
    "hCDQozwsElaCm0Eg2dBj+FtD0ZsTaqZ3ZAXNKucowAQcHmr/214xxiOWaMdBimKRVbvDyPcJxGDiqHtGi23QcrkR0DRcUDAu1+vdVcGKAewk0HGfHTqdcqjVyrvJwvrz6xhQrwrsFnOfo3TBOwrBG9UjX6/t/eONkE715q7iPfaQDdDEC2alMEgFINnDK51+UiZPLiATRYqaQC5vRIbkQmZzIeQX+BlRhC9NQB5DvZC2I523V8/iezaNEdeJCKEcmNMdbUOUR/Q=";

final Fluwx fluwx = Fluwx();
