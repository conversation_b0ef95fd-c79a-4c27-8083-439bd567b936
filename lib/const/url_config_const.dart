import 'package:bdh_smart_agric_app/model/url_config_model.dart';

//开发环境
const urlConfigDev = UrlConfig(
    envType: EnvType.bdh,
    isDebug: true,
    fieldApi: 'http://10.11.14.211:31300',
    commonApi: 'http://10.11.14.211:30879',
    ssoApi: 'http://10.11.14.211:31084',
    baseApi: 'http://10.11.14.211:30419',
    microfront: "http://10.11.14.211:30879",
    mesApi: 'http://10.11.14.211:30884/stage-api',
    jzApi: 'http://10.11.14.211:30759/stage-api',
    plotApi: 'http://10.11.14.211:30155/sysagriculturalsituation', // 种植
    // plotApi: 'http://10.11.90.161:9551', // 种植
    highApi: 'http://10.11.14.211:30924/stage-api', // 高标
    threePartyApi: 'http://10.11.14.211:30414', // 三方接口
    soilTestingApi: 'http://10.11.14.211:30155/bdh-soil-testing', // 土壤测评
    // threePartyApi: 'http://10.11.91.104:8989', // 三方接口
    threeCensusesApi: 'http://10.11.14.211:30698', // 三方普查
    h5: "http://10.11.14.211:30918",
    authApi: 'http://10.11.14.211:30414',
    dahingGanLingApi: "",
    waterMangeApi: 'http://10.11.14.211:30155/bdh-weather-platform',
    androidApkId: "1864147615310938112",
    chatApi: 'https://lm.bdhic.com',
    licenseKeyX5:
        "n3KCmuB9ZZYh6tRi2Fuy5NzFY660TqsZzGSSWdiiA/BOy9tlKRJ61EU1XiuPIJKrfG6X2zS+3y6WmNlxD4pswk1MTwXKi7bd90UN7PYz6fLhHBSBfWiJeXZ7RVTUPIdW",
    fertilizApi: 'http://10.11.14.211:30155',
    farmfellApi: 'http://10.11.14.211:30402/stage-api',
    expotrApi: "http://192.168.2.203:13007",
    financeApi: 'http://10.11.14.211:30620',
    loanApi: 'http://10.11.14.214:30963',
    weatherApi: 'http://10.11.14.211:30430/stage-api',
    leafAgeApi: 'http://10.11.14.211:31450/stage-api');

//测试环境
const urlConfigTest = UrlConfig(
    envType: EnvType.bdh,
    isDebug: true,
    commonApi: 'https://busgateway.bdhic.com',
    fieldApi: 'http://10.11.14.211:31300',
    ssoApi: 'https://sso.bdhic.net',
    baseApi: 'https://lc.bdhic.net',
    // baseApi: 'http://10.11.69.83:8990',
    plotApi: 'https://as.bdhic.net/stage-api', // 种植
    microfront: 'http://10.11.14.215:31879',
    mesApi: 'https://newsinfo.bdhic.net',
    jzApi: 'https://daapp.bdhic.net',
    busiApi: "http://10.11.14.214:31884/stage-api",
    h5: '',
    authApi: 'https://fadada-auth.bdhic.net',
    threeCensusesApi: 'https://app.bdhic.net/soilCensus', // 三方普查
    dahingGanLingApi: "http://10.11.14.214:31832",
    waterMangeApi: '',
    androidApkId: "1875807820373295104",
    chatApi: 'https://lm.bdhic.com',
    samicConfirmApi: "http://10.11.14.211:31938/stage-api",
    stageApi: "http://10.11.14.214:31404/stage-api",
    licenseKeyX5:
        "n3KCmuB9ZZYh6tRi2Fuy5NzFY660TqsZzGSSWdiiA/BOy9tlKRJ61EU1XiuPIJKrfG6X2zS+3y6WmNlxD4pswk1MTwXKi7bd90UN7PYz6fLhHBSBfWiJeXZ7RVTUPIdW",
    financeApi: 'https://finance.bdhic.net',
    fertilizApi: 'http://10.11.14.214:31770/stage-api',
    costanalysisApi: 'https://costanalysis.bdhic.net',
    farmfellApi: 'https://as.bdhic.net',
    expotrApi: "https://plant.bdhic.net",
    weatherApi: 'https://weather.bdhic.net',
    leafAgeApi: 'http://10.11.14.211:31450/stage-api');

//数字北大荒-生产环境
const urlConfigProd = UrlConfig(
    isDebug: false,
    appApi: 'https://app.bdhic.com',
    envType: EnvType.bdh,
    commonApi: 'https://busgateway.bdhic.com',
    busiApi: 'https://newsinfo.bdhic.com', // 业务消息
    fieldApi: 'https://busgateway.bdhic.com',
    ssoApi: 'https://sso-pro.bdhic.com',
    baseApi: 'https://lc.bdhic.com',
    microfront: 'https://app.bdhic.com/microfront',
    mesApi: 'https://newsinfo.bdhic.com',
    // jzApi: 'https://daapp.bdhic.com/bdh-tech-park-api',
    jzApi: 'https://daapp.bdhic.com',
    h5: 'https://smartagric.bdhic.com',
    authApi: "https://fadada-auth.bdhic.com",
    threeCensusesApi:
        'https://busgateway.bdhic.com/bdh-soil-census-api', // 三方普查
    dahingGanLingApi: "https://lc.bdhic.com",
    androidApkId: "1925499145427091456",
    chatApi: 'https://lm.bdhic.com',
    samicConfirmApi: "https://app.bdhic.com/samicConfirm",
    stageApi: "https://lc.bdhic.com",
    licenseKeyX5:
        "n3KCmuB9ZZYh6tRi2Fuy5NzFY660TqsZzGSSWdiiA/BOy9tlKRJ61EU1XiuPIJKrfG6X2zS+3y6WmNlxD4pswk1MTwXKi7bd90UN7PYz6fLhHBSBfWiJeXZ7RVTUPIdW",
    financeApi: 'https://finance.bdhic.com',
    fertilizApi: 'https://app.bdhic.com/microfront/soil-testing-api',
    loanApi: 'https://risk.bdhic.com',
    waterMangeApi:
        'https://busgateway.bdhic.com/bdh-portal/api/bdh-weather-platform', // 天气接口
    soilTestingApi:
        "https://busgateway.bdhic.com/bdh-portal/api/bdh-soil-testing", // 土壤测评
    highApi:
        "https://busgateway.bdhic.com/bdh-portal/api/bdh-weather-platform", // 高标
    plotApi:
        "https://busgateway.bdhic.com/bdh-portal/api/sysagriculturalsituation", // 种植 农情管理
    threePartyApi: "https://fadada-auth.bdhic.com", // 三方
    finaApi: 'https://finance.bdhic.com',
    costanalysisApi: 'https://costanalysis.bdhic.com',
    farmfellApi: 'https://agsituation.bdhic.com', //专家巡田
    expotrApi: "https://plant.bdhic.com", //农情
    zjxtApi: 'https://app.bdhic.com',
    encyApi: 'https://knowledge.bdhic.net/stage-api',
    agricultureApi: 'https://app.bdhic.com/agMachineManager',
    weatherApi: 'https://weather.bdhic.com',
    leafAgeApi: 'https://leafage.bdhic.com/api');

UrlConfig get urlConfig => _urlConfig;

//默认配置,debug模式修改此处
// UrlConfig _urlConfig = urlConfigDev; // dev bdh
// UrlConfig _urlConfig = urlConfigProd; // prod bdh
UrlConfig _urlConfig = urlConfigTest;// test bdh


