import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_segment_line.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';

class BdhProveImagePicker extends FormField<IDCardItem> {
  final FormItem item;
  final bool? showTitle;
  BdhProveImagePicker(
      {super.key,
      super.initialValue,
      super.validator,
      super.onSaved,
      required this.item,
      this.showTitle})
      : super(builder: (field) {
          openImage(ImageSource type, ImageType imageType) {
            ImagePicker picker = ImagePicker();
            picker.pickImage(source: type, imageQuality: 10).then((image) {
              Navigator.of(field.context).pop();
              //图片上传
              if (image != null) {
                image.readAsBytes().then((bytes) {
                  String? mimeType = lookupMimeType(image.name);
                  if (mimeType != null) {
                    FormData postData = FormData.fromMap({
                      "file": MultipartFile.fromBytes(bytes,
                          filename: image.name,
                          contentType: MediaType(mimeType.split("/").first,
                              mimeType.split("/").last))
                    });

                    BDHResponsitory.uploadFile(postData).then((value) {
                      if (imageType == ImageType.portrait) {
                        field.value!.portrait = value;
                        field.didChange(field.value);
                      } else {
                        field.value!.nationalEmblem = value;
                        field.didChange(field.value);
                      }
                    });
                  } else {
                    showToast("未知文件");
                  }
                });
              }
            });
          }

          return Container(
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        width: 1.px,
                        color: (showTitle ?? false)
                            ? const Color.fromRGBO(226, 235, 231, 0.6)
                            : Colors.transparent))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 10.px),
                  child: Text.rich(TextSpan(children: [
                    TextSpan(
                      text: (item.isRequired ?? false) ? "*" : "",
                      style: TextStyle(
                          color: Colors.red,
                          fontSize: 16.px,
                          fontWeight: FontWeight.w500),
                    ),
                    TextSpan(
                      text: item.title,
                      style: TextStyle(
                          fontSize: 16.px, fontWeight: FontWeight.w500),
                    )
                  ])),
                ),
                SizedBox(
                  height: 10.px,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                            backgroundColor: Colors.transparent,
                            useSafeArea: true,
                            context: field.context,
                            builder: (ctx) {
                              return CameraPhotoSelect(sourceSelect: (e) {
                                openImage(e, ImageType.portrait);
                              });
                            });
                      },
                      child: field.value!.portrait == null
                          ? Image.asset(
                              width: 165.px,
                              ImageHelper.wrapAssets("add_id_card_0.png"))
                          : SizedBox(
                              width: 165.px,
                              height: 105.32.px,
                              child: Stack(
                                children: [
                                  Positioned(
                                      left: 0,
                                      bottom: 0,
                                      child: Image.network(
                                          width: 157.px,
                                          height: 97.32.px,
                                          fit: BoxFit.fitWidth,
                                          "${urlConfig.microfront}${field.value?.portrait?.url}")),
                                  Positioned(
                                      top: 0,
                                      right: 0,
                                      child: GestureDetector(
                                        onTap: () {
                                          field.value!.portrait = null;
                                          field.didChange(field.value);
                                        },
                                        child: Image.asset(
                                            width: 16.px,
                                            height: 16.px,
                                            ImageHelper.wrapAssets(
                                                "delete.png")),
                                      ))
                                ],
                              ),
                            ),
                    ),
                    GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                            backgroundColor: Colors.transparent,
                            useSafeArea: true,
                            context: field.context,
                            builder: (ctx) {
                              return CameraPhotoSelect(sourceSelect: (e) {
                                openImage(e, ImageType.nationalEmblem);
                              });
                            });
                      },
                      child: field.value!.nationalEmblem == null
                          ? Image.asset(
                              width: 165.px,
                              ImageHelper.wrapAssets("add_id_card_1.png"))
                          : SizedBox(
                              width: 165.px,
                              height: 105.32.px,
                              child: Stack(
                                children: [
                                  Positioned(
                                      left: 0,
                                      bottom: 0,
                                      child: Image.network(
                                          width: 157.px,
                                          height: 97.32.px,
                                          fit: BoxFit.fitWidth,
                                          "${urlConfig.microfront}${field.value?.nationalEmblem?.url}")),
                                  Positioned(
                                      top: 0,
                                      right: 0,
                                      child: GestureDetector(
                                        onTap: () {
                                          field.value!.nationalEmblem = null;
                                          field.didChange(field.value);
                                        },
                                        child: Image.asset(
                                            width: 16.px,
                                            height: 16.px,
                                            ImageHelper.wrapAssets(
                                                "delete.png")),
                                      ))
                                ],
                              ),
                            ),
                    )
                  ],
                ),
                SizedBox(
                  height: 15.px,
                ),
                BdhSegmentLine(width: 345.px),
                Visibility(
                    visible: field.errorText != null,
                    child: Text(
                      field.errorText ?? "",
                      style: const TextStyle(color: Colors.red),
                    ))
              ],
            ),
          );
        });
}

class IDCardItem {
  BDHFile? portrait;
  BDHFile? nationalEmblem;
  IDCardItem({this.portrait, this.nationalEmblem});
}

enum ImageType { portrait, nationalEmblem }
