// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

/*
 * <AUTHOR>
 * @description: 媒体上传组件
 * @date 2025/04/11 15:07:57
*/

import 'dart:io';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart' as http_parser;
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:video_player/video_player.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

typedef OnMediaChange = void Function(List<String>? fileList);

typedef OnMediaPickerCallback = Future Function(
    FormFieldState<List<MediaItem>> field,
    OnMediaChange? onChange,
    XFile? file,
    bool isVideo);

enum MediaType { image, video }

class MediaItem {
  String url; // 文件URL路径
  MediaType type; // 媒体类型
  String? thumbnailUrl; // 视频缩略图URL

  MediaItem({
    required this.url,
    required this.type,
    this.thumbnailUrl,
  });

  // 检查是否为网络URL
  bool get isNetworkUrl => url.startsWith('http');

  // 获取UI显示用的URL
  String get displayUrl => isNetworkUrl ? url : url;

  @override
  String toString() =>
      'MediaItem(url: $url, type: $type, thumbnailUrl: $thumbnailUrl)';

  @override
  bool operator ==(covariant MediaItem other) {
    if (identical(this, other)) return true;

    return other.url == url &&
        other.type == type &&
        other.thumbnailUrl == thumbnailUrl;
  }

  @override
  int get hashCode => url.hashCode ^ type.hashCode ^ thumbnailUrl.hashCode;
}

// 检查并请求权限
Future<bool> _checkAndRequestPermission(
    BuildContext context, Permission permission, String permissionName) async {
  // 先检查权限状态
  PermissionStatus status = await permission.status;

  if (status.isGranted) {
    return true;
  }

  // 如果权限被拒绝，请求权限
  status = await permission.request();

  if (status.isGranted) {
    return true;
  } else if (status.isPermanentlyDenied) {
    // 用户永久拒绝了权限，提示用户去设置中开启
    BrnDialogManager.showConfirmDialog(
      context,
      title: "权限提示",
      cancel: '取消',
      confirm: '前往设置',
      message: "需要$permissionName权限才能继续操作，请在设置中开启权限",
      onConfirm: () {
        Navigator.of(context).pop();
        openAppSettings();
      },
      onCancel: () {
        Navigator.of(context).pop();
      },
    );
    return false;
  } else {
    showToast("需要$permissionName权限才能继续操作");
    return false;
  }
}

// 默认上传文件回调
Future _defaultMediaPickerCallback(FormFieldState<List<MediaItem>> field,
    OnMediaChange? onChange, XFile? file, bool isVideo) async {
  if (file != null) {
    // 检查文件类型
    String? mimeType = lookupMimeType(file.name);
    if (mimeType == null) return;

    // 显示上传提示
    TDToast.showLoading(text: "上传中...", context: field.context);

    try {
      var bytes = await file.readAsBytes();
      Log.d("文件大小: ${bytes.length / 1024} kb");

      // 判断文件类型
      MediaType mediaType = isVideo ? MediaType.video : MediaType.image;

      // 创建表单数据
      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(bytes,
            filename: file.name,
            contentType: http_parser.MediaType(
                mimeType.split("/").first, mimeType.split("/").last))
      });

      // 上传文件到服务器
      var updateFileResult =
          await BDHResponsitory.uploadFileForResult2(postData);

      if (!field.context.mounted) {
        return;
      }

      TDToast.dismissLoading();

      if (updateFileResult.success != null) {
        // 如果是视频，生成缩略图
        String? thumbnailUrl;
        if (mediaType == MediaType.video) {
          try {
            final thumbnail = await VideoThumbnail.thumbnailData(
              video: file.path,
              quality: 25,
            );

            if (thumbnail != null) {
              FormData thumbPostData = FormData.fromMap({
                "file": MultipartFile.fromBytes(thumbnail,
                    filename: "${DateTime.now().millisecondsSinceEpoch}.jpg",
                    contentType: http_parser.MediaType("image", "jpeg"))
              });

              var thumbResult =
                  await BDHResponsitory.uploadFileForResult2(thumbPostData);
              if (thumbResult.success != null) {
                thumbnailUrl = thumbResult.data;
              }
            }
          } catch (e) {
            Log.e("视频缩略图生成失败: $e");
            // 视频缩略图生成失败不影响整体流程
          }
        }

        // 添加到列表
        MediaItem newItem = MediaItem(
          url: updateFileResult.data!,
          type: mediaType,
          thumbnailUrl: thumbnailUrl,
        );

        field.value!.add(newItem);
        field.didChange(field.value);

        // 仅提供URL列表给外部
        List<String> urls = field.value!.map((item) => item.url).toList();
        onChange?.call(urls);
      }
    } catch (e) {
      Log.e("文件上传错误: $e");
      TDToast.dismissLoading();
      showToast("上传失败: $e");
    }
  }
}

// farmApi上传文件回调
Future farmMediaPickerCallback(FormFieldState<List<MediaItem>> field,
    OnMediaChange? onChange, XFile? file, bool isVideo) async {
  if (file != null) {
    // 检查文件类型
    String? mimeType = lookupMimeType(file.name);
    if (mimeType == null) return;

    // 显示上传提示
    TDToast.showLoading(text: "上传中...", context: field.context);

    try {
      var bytes = await file.readAsBytes();
      Log.d("文件大小: ${bytes.length / 1024} kb");

      // 判断文件类型
      MediaType mediaType = isVideo ? MediaType.video : MediaType.image;

      // 创建表单数据
      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(bytes,
            filename: file.name,
            contentType: http_parser.MediaType(
                mimeType.split("/").first, mimeType.split("/").last))
      });

      // 上传文件到服务器
      var updateFileResult = await BDHResponsitory.threeUploadFile(postData);

      if (!field.context.mounted) {
        return;
      }

      TDToast.dismissLoading();

      if (updateFileResult.success != null) {
        // 如果是视频，生成缩略图
        String? thumbnailUrl;
        if (mediaType == MediaType.video) {
          try {
            final thumbnail = await VideoThumbnail.thumbnailData(
              video: file.path,
              quality: 25,
            );

            if (thumbnail != null) {
              FormData thumbPostData = FormData.fromMap({
                "file": MultipartFile.fromBytes(thumbnail,
                    filename: "${DateTime.now().millisecondsSinceEpoch}.jpg",
                    contentType: http_parser.MediaType("image", "jpeg"))
              });

              var thumbResult =
                  await BDHResponsitory.threeUploadFile(thumbPostData);
              if (thumbResult.success != null) {
                thumbnailUrl = thumbResult.data;
              }
            }
          } catch (e) {
            Log.e("视频缩略图生成失败: $e");
            // 视频缩略图生成失败不影响整体流程
          }
        }

        // 添加到列表
        MediaItem newItem = MediaItem(
          url: updateFileResult.data!,
          type: mediaType,
          thumbnailUrl: thumbnailUrl,
        );

        field.value!.add(newItem);
        field.didChange(field.value);

        // 仅提供URL列表给外部
        List<String> urls = field.value!.map((item) => item.url).toList();
        onChange?.call(urls);
      }
    } catch (e) {
      Log.e("文件上传错误: $e");
      TDToast.dismissLoading();
      showToast("上传失败: $e");
    }
  }
}

// 不上传文件回调 - 只返回本地路径
Future _localMediaPickerCallback(FormFieldState<List<MediaItem>> field,
    OnMediaChange? onChange, XFile? file, bool isVideo) async {
  if (file != null) {
    try {
      Log.d("选择的本地文件路径: ${file.path}");

      // 判断文件类型
      MediaType mediaType = isVideo ? MediaType.video : MediaType.image;

      // 直接使用本地路径创建MediaItem
      MediaItem newItem = MediaItem(
        url: file.path,
        type: mediaType,
      );

      field.value!.add(newItem);
      field.didChange(field.value);

      // 提供本地文件路径给外部
      List<String> urls = field.value!.map((item) => item.url).toList();
      onChange?.call(urls);

      Log.d("本地文件回调完成，路径: ${file.path}");
    } catch (e) {
      Log.e("本地文件处理错误: $e");
      showToast("文件处理失败: $e");
    }
  }
}

// 选择媒体类型的底部弹出菜单
class MediaTypeSelect extends StatelessWidget {
  final Function(ImageSource source, bool isVideo) sourceSelect;
  final bool allowImage;
  final bool allowVideo;

  const MediaTypeSelect(
      {super.key,
      required this.sourceSelect,
      this.allowImage = true,
      this.allowVideo = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(9.px), topRight: Radius.circular(9.px))),
      height: (allowImage && allowVideo
              ? 242.px
              : (allowImage || allowVideo)
                  ? 144.px
                  : 100.px) +
          MediaQuery.of(context).padding.bottom,
      child: Column(
        children: [
          // 图片选项
          if (allowImage) ...[
            GestureDetector(
              onTap: () {
                sourceSelect(ImageSource.camera, false);
              },
              child: Container(
                height: 44.px,
                color: Colors.transparent,
                alignment: Alignment.center,
                child: const Text(
                  "拍照",
                  style: TextStyle(
                      color: Color.fromRGBO(23, 151, 100, 1),
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                sourceSelect(ImageSource.gallery, false);
              },
              child: Container(
                color: Colors.transparent,
                height: 44.px,
                alignment: Alignment.center,
                child: const Text("从相册选择",
                    style: TextStyle(
                        color: Color.fromRGBO(23, 151, 100, 1),
                        fontWeight: FontWeight.w500)),
              ),
            ),
          ],

          // 视频选项
          if (allowVideo) ...[
            GestureDetector(
              onTap: () {
                sourceSelect(ImageSource.camera, true);
              },
              child: Container(
                height: 44.px,
                color: Colors.transparent,
                alignment: Alignment.center,
                child: const Text(
                  "录制视频",
                  style: TextStyle(
                      color: Color.fromRGBO(23, 151, 100, 1),
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                sourceSelect(ImageSource.gallery, true);
              },
              child: Container(
                color: Colors.transparent,
                height: 44.px,
                alignment: Alignment.center,
                child: const Text("从视频库选择",
                    style: TextStyle(
                        color: Color.fromRGBO(23, 151, 100, 1),
                        fontWeight: FontWeight.w500)),
              ),
            ),
          ],

          // 分割线
          Container(
            width: 345.px,
            height: 1.px,
            color: const Color.fromRGBO(226, 235, 231, 0.6),
          ),

          // 取消按钮
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              height: 55.px,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: const Text(
                "取消",
                style: TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
              ),
            ),
          )
        ],
      ),
    );
  }
}

// 多媒体选择器组件
class BdhMediaPicker extends FormField<List<MediaItem>> {
  final FormItem item;
  final bool? showTitle;
  final int? maxCount;
  final double? fontSize;
  final bool allowVideo;
  final bool allowImage;
  final bool checkState;
  final bool autoUpload; // 是否自动上传到服务器

  BdhMediaPicker({
    super.key,
    List<MediaItem>? initialValue,
    super.validator,
    super.onSaved,
    required this.item,
    this.showTitle,
    this.maxCount = 4,
    this.fontSize,
    this.allowVideo = true,
    this.allowImage = true,
    bool showBottomLine = true,
    this.checkState = false,
    TextStyle? titleStyle,
    this.autoUpload = true, // 默认自动上传
    OnMediaPickerCallback? onPickerCallback,
    OnMediaChange? onChange,
  }) : super(
          initialValue: initialValue ?? [],
          builder: (field) {
            _BdhMediaPickerState state = field as _BdhMediaPickerState;

            // 根据autoUpload选择对应的回调函数
            final callback = onPickerCallback ??
                (autoUpload
                    ? _defaultMediaPickerCallback
                    : _localMediaPickerCallback);

            // 选择媒体文件
            Future<void> pickMedia(ImageSource source, bool isVideo) async {
              // 检查相应权限
              bool hasPermission = false;

              // 检查相机权限
              if (source == ImageSource.camera) {
                hasPermission = await _checkAndRequestPermission(
                    field.context, Permission.camera, "相机");
                if (!hasPermission) return;
              }

              // 检查存储权限（根据SDK版本和文件类型）
              if (Platform.isAndroid) {
                // 安全地获取Android SDK版本
                bool isAndroid13OrAbove = false;
                try {
                  // 通过Platform.version获取版本信息，例如"Android 13"
                  String platformVersion = Platform.version.toLowerCase();
                  if (platformVersion.contains("android")) {
                    // 尝试提取版本号
                    RegExp regex = RegExp(r'android\s+(\d+)');
                    Match? match = regex.firstMatch(platformVersion);
                    if (match != null && match.groupCount >= 1) {
                      int? androidVersion = int.tryParse(match.group(1) ?? "0");
                      isAndroid13OrAbove = (androidVersion ?? 0) >= 13;
                    }
                  }
                } catch (e) {
                  Log.e("解析Android版本失败: $e");
                  // 出错时默认使用旧版本权限处理
                  isAndroid13OrAbove = false;
                }

                if (isAndroid13OrAbove) {
                  // Android 13及以上版本
                  if (isVideo) {
                    hasPermission = await _checkAndRequestPermission(
                        field.context, Permission.videos, "视频");
                  } else {
                    hasPermission = await _checkAndRequestPermission(
                        field.context, Permission.photos, "照片");
                  }
                } else {
                  // Android 13以下版本
                  hasPermission = await _checkAndRequestPermission(
                      field.context, Permission.storage, "存储");
                }
                if (!hasPermission) return;
              }

              try {
                XFile? file;

                ImagePicker picker = ImagePicker();
                if (isVideo) {
                  file = await picker.pickVideo(source: source);
                } else {
                  file = await picker.pickImage(
                    source: source,
                    maxHeight: 1080,
                    maxWidth: 1080,
                    imageQuality: 80,
                  );
                }

                if (!field.context.mounted) return;

                await callback(field, onChange, file, isVideo);
              } catch (e) {
                Log.e("选择媒体文件失败: $e");
                showToast("选择文件失败，请重试");
              }
            }

            // 显示媒体选择菜单
            void showMediaOptions() {
              if (item.isCanEdit == false) {
                showToast("只读状态无法编辑");
                return;
              }
              if (field.value!.length >= (maxCount ?? 4)) {
                showToast("已达到最大上传数量");
                return;
              }

              // 底部弹出选择菜单
              showModalBottomSheet(
                context: field.context,
                backgroundColor: Colors.transparent,
                builder: (context) {
                  return MediaTypeSelect(
                    allowImage: allowImage,
                    allowVideo: allowVideo,
                    sourceSelect: (source, isVideo) {
                      Navigator.of(field.context).pop();
                      pickMedia(source, isVideo);
                    },
                  );
                },
              );
            }

            // 预览媒体
            void previewMedia(MediaItem media) {
              if (media.type == MediaType.image) {
                // 图片预览
                Navigator.push(
                  field.context,
                  MaterialPageRoute(
                    builder: (context) =>
                        _ImagePreviewPage(imageUrl: media.url),
                  ),
                );
              } else if (media.type == MediaType.video) {
                // 视频预览
                Navigator.push(
                  field.context,
                  MaterialPageRoute(
                    builder: (context) =>
                        _VideoPreviewPage(videoUrl: media.url),
                  ),
                );
              }
            }

            return Container(
              padding: EdgeInsets.only(bottom: 4.px),
              width: double.infinity,
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          width: 1.px,
                          color: (showTitle ?? false) && showBottomLine
                              ? const Color.fromRGBO(226, 235, 231, 0.6)
                              : Colors.transparent))),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题部分
                  if (showTitle ?? false)
                    Container(
                      margin: EdgeInsets.only(top: 10.px),
                      child: Text.rich(TextSpan(children: [
                        TextSpan(
                          text: (item.isRequired ?? false) ? "*" : "",
                          style: TextStyle(
                              color: Colors.red,
                              fontSize: fontSize ?? 16.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: item.title,
                          style: titleStyle ??
                              TextStyle(
                                  fontSize: fontSize ?? 16.px,
                                  fontWeight: FontWeight.w500),
                        )
                      ])),
                    ),
                  SizedBox(height: 5.px),

                  // 媒体文件列表
                  Wrap(
                    spacing: 8.px,
                    runSpacing: 8.px,
                    children: [
                      // 已选择的媒体文件
                      ...field.value!.map((media) {
                        return SizedBox(
                          width: 80.px,
                          height: 80.px,
                          child: Stack(
                            children: [
                              // 媒体内容（图片或视频缩略图）
                              GestureDetector(
                                onTap: () => previewMedia(media),
                                child: Container(
                                  width: 80.px,
                                  height: 80.px,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4.px),
                                    border: Border.all(
                                      color: Colors.grey[300]!,
                                      width: 1,
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(4.px),
                                    child: media.type == MediaType.image
                                        ? _buildImageWidget(media.url)
                                        : _buildVideoThumbnail(media),
                                  ),
                                ),
                              ),

                              // 删除按钮
                              if (item.isCanEdit != false)
                                Positioned(
                                  top: 0,
                                  right: 0,
                                  child: GestureDetector(
                                    onTap: () {
                                      field.value!.remove(media);
                                      field.didChange(field.value);

                                      // 通知外部URL变化
                                      List<String> urls = field.value!
                                          .map((item) => item.url)
                                          .toList();
                                      onChange?.call(urls);
                                    },
                                    child: Container(
                                      width: 20.px,
                                      height: 20.px,
                                      decoration: BoxDecoration(
                                        color: Colors.black.withOpacity(0.5),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.close,
                                        size: 16.px,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          ),
                        );
                      }),

                      // 添加按钮
                      if (field.value!.length < (maxCount ?? 4) &&
                          item.isCanEdit != false)
                        GestureDetector(
                          onTap: showMediaOptions,
                          child: Container(
                            width: 80.px,
                            height: 80.px,
                            decoration: BoxDecoration(
                              color: const Color.fromRGBO(244, 245, 245, 1),
                              borderRadius: BorderRadius.circular(4.px),
                              border: Border.all(
                                color: Colors.grey[300]!,
                                width: 1,
                              ),
                            ),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.add,
                                  size: 28.px,
                                  color: Colors.grey[600],
                                ),
                                SizedBox(height: 4.px),
                                Text(
                                  "添加",
                                  style: TextStyle(
                                    fontSize: 12.px,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      if (field.value!.isEmpty && item.isCanEdit == false)
                        Container(
                          width: 80.px,
                          height: 80.px,
                          decoration: BoxDecoration(
                            color: const Color.fromRGBO(244, 245, 245, 1),
                            borderRadius: BorderRadius.circular(4.px),
                            border: Border.all(
                              color: Colors.grey[300]!,
                              width: 1,
                            ),
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.image_not_supported,
                                size: 28.px,
                                color: Colors.grey[600],
                              ),
                              SizedBox(height: 4.px),
                              Text(
                                "暂无数据",
                                style: TextStyle(
                                  fontSize: 12.px,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        )
                    ],
                  ),

                  SizedBox(height: 10.px),

                  // 提示信息
                  if (item.isCanEdit != false)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                            width: 80.px,
                            child: Text(
                              textAlign: TextAlign.center,
                              "${(field.value ?? []).length}/${maxCount ?? 4}",
                              style: const TextStyle(
                                  color: Color.fromRGBO(0, 0, 0, 0.4)),
                            )),
                        Text(
                          allowVideo && allowImage
                              ? "支持图片和视频,不能超过100M"
                              : allowVideo
                                  ? "仅支持视频,不能超过100M"
                                  : "仅支持图片,不能超过100M",
                          style: const TextStyle(
                              color: Color.fromRGBO(0, 0, 0, 0.4)),
                        ),
                      ],
                    ),
                ],
              ),
            );
          },
        );

  @override
  FormFieldState<List<MediaItem>> createState() => _BdhMediaPickerState();

  // 构建图片显示Widget
  static Widget _buildImageWidget(String url) {
    if (url.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: ImageHelper.networkUrl(url),
        fit: BoxFit.cover,
        placeholder: (context, url) => const Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
        errorWidget: (context, url, error) => const Center(
          child: Icon(Icons.error, color: Colors.red),
        ),
      );
    } else {
      return Image.file(
        File(url),
        fit: BoxFit.cover,
      );
    }
  }

  // 构建视频缩略图显示
  static Widget _buildVideoThumbnail(MediaItem media) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 缩略图
        if (media.thumbnailUrl != null)
          _buildImageWidget(media.thumbnailUrl!)
        else if (media.isNetworkUrl)
          Container(color: Colors.grey[800])
        else
          Container(color: Colors.grey[800]),

        // 播放图标
        Icon(
          Icons.play_circle_fill,
          color: Colors.white,
          size: 32.px,
        ),
      ],
    );
  }
}

class _BdhMediaPickerState extends FormFieldState<List<MediaItem>> {
  @override
  BdhMediaPicker get widget => super.widget as BdhMediaPicker;

  @override
  void didUpdateWidget(covariant BdhMediaPicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}

// 图片预览页面
class _ImagePreviewPage extends StatelessWidget {
  final String imageUrl;

  const _ImagePreviewPage({required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        title: const Text('图片预览', style: TextStyle(color: Colors.white)),
      ),
      body: Center(
        child: InteractiveViewer(
          minScale: 0.5,
          maxScale: 3.0,
          child: _buildImageWidget(imageUrl),
        ),
      ),
    );
  }

  Widget _buildImageWidget(String url) {
    if (url.startsWith('http')) {
      return CachedNetworkImage(
        imageUrl: ImageHelper.networkUrl(url),
        fit: BoxFit.contain,
        placeholder: (context, url) => const Center(
          child: CircularProgressIndicator(),
        ),
        errorWidget: (context, url, error) => const Center(
          child: Icon(Icons.error, color: Colors.red, size: 50),
        ),
      );
    } else {
      return Image.file(
        File(url),
        fit: BoxFit.contain,
      );
    }
  }
}

// 视频预览页面
class _VideoPreviewPage extends StatefulWidget {
  final String videoUrl;

  const _VideoPreviewPage({required this.videoUrl});

  @override
  _VideoPreviewPageState createState() => _VideoPreviewPageState();
}

class _VideoPreviewPageState extends State<_VideoPreviewPage> {
  late VideoPlayerController _controller;
  bool _isInitialized = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializeVideoPlayer();
  }

  Future<void> _initializeVideoPlayer() async {
    try {
      if (widget.videoUrl.startsWith('http')) {
        // 确保URL格式正确
        final videoUrl = ImageHelper.networkUrl(widget.videoUrl);
        Log.d("加载网络视频: $videoUrl");
        _controller = VideoPlayerController.networkUrl(
          Uri.parse(videoUrl),
        );
      } else {
        // 检查本地文件是否存在
        final file = File(widget.videoUrl);
        if (!await file.exists()) {
          setState(() {
            _errorMessage = "视频文件不存在";
          });
          return;
        }
        Log.d("加载本地视频: ${widget.videoUrl}");
        _controller = VideoPlayerController.file(file);
      }

      // 添加错误监听器
      _controller.addListener(() {
        if (_controller.value.hasError) {
          Log.e("视频播放错误: ${_controller.value.errorDescription}");
          if (mounted) {
            setState(() {
              _errorMessage = "视频播放错误: ${_controller.value.errorDescription}";
            });
          }
        }
      });

      // 初始化并播放
      await _controller.initialize();
      await _controller.setLooping(true);

      // 只有在组件仍然挂载时才更新状态
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
        _controller.play();
      }
    } catch (e) {
      Log.e("视频播放初始化失败: $e");
      if (mounted) {
        setState(() {
          _errorMessage = "无法播放视频: $e";
        });
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        title: const Text('视频预览', style: TextStyle(color: Colors.white)),
      ),
      body: _errorMessage != null
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, color: Colors.red, size: 50.px),
                  SizedBox(height: 16.px),
                  Text(
                    _errorMessage!,
                    style: TextStyle(color: Colors.white, fontSize: 16.px),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          : _isInitialized
              ? Stack(
                  alignment: Alignment.center,
                  children: [
                    // 视频播放器
                    GestureDetector(
                      onTap: () {
                        setState(() {
                          if (_controller.value.isPlaying) {
                            _controller.pause();
                          } else {
                            _controller.play();
                          }
                        });
                      },
                      child: Center(
                        child: AspectRatio(
                          aspectRatio: _controller.value.aspectRatio,
                          child: VideoPlayer(_controller),
                        ),
                      ),
                    ),

                    // 播放/暂停按钮
                    if (!_controller.value.isPlaying)
                      Container(
                        width: 60.px,
                        height: 60.px,
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.play_arrow,
                          size: 40.px,
                          color: Colors.white,
                        ),
                      ),
                  ],
                )
              : const Center(
                  child: CircularProgressIndicator(color: Colors.white),
                ),
    );
  }
}
