import 'package:bdh_smart_agric_app/components/bdh_network_image.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';

typedef OnUpload = void Function(BDHFile file);

typedef OnPickerCallback = Future Function(
    FormFieldState<BDHFile> field, OnUpload? onUpload, XFile? file);

//根据UrlConfig服务器环境配置上传
//BdhLandResponsitory.uploadFile
Future basePickerCallback(
    FormFieldState<BDHFile> field, OnUpload? onUpload, XFile? image) async {
  if (image != null) {
    //这里需要考虑几种图片的格式
    String? mimeType = lookupMimeType(image.name);

    if (mimeType != null) {
      BrnLoadingDialog.show(field.context,
          content: "上传中...  ", barrierDismissible: false);
      var bytes = await image.readAsBytes();
      Log.d(" 图片大小 ${bytes.length / 1024} kb");

      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(bytes,
            filename: image.name,
            contentType:
                MediaType(mimeType.split("/").first, mimeType.split("/").last))
      });

      BdhLandResponsitory.uploadFile(postData).then((value) {
        if (!field.context.mounted) {
          return;
        }
        BrnLoadingDialog.dismiss(field.context);
        if (onUpload != null) {
          onUpload(BDHFile(url: value.data));
        }
        field.didChange(BDHFile(url: value.data));
      }).onError((e, s) {
        Log.e("upload image error", error: e, stackTrace: s);
        var request = RequestException.handleError(e);
        if (request.isCancel) {
          return;
        }
        if (!field.context.mounted) {
          return;
        }
        BrnLoadingDialog.dismiss(field.context);
        showToast(request.message ?? "图片上传失败,请稍后重试");
      });
    } else {
      showToast("未知文件");
    }
  }
}

//文件会上传到 daHingHttp 默认？
//LandResponsitory.fileUpload
Future _defaultPickerCallback(
    FormFieldState<BDHFile> field, OnUpload? onUpload, XFile? image) async {
  if (image != null) {
    //这里需要考虑几种图片的格式
    String? mimeType = lookupMimeType(image.name);

    if (mimeType != null) {
      BrnLoadingDialog.show(field.context,
          content: "上传中...  ", barrierDismissible: false);
      var bytes = await image.readAsBytes();
      Log.d(" 图片大小 ${bytes.length / 1024} kb");

      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(bytes,
            filename: image.name,
            contentType:
                MediaType(mimeType.split("/").first, mimeType.split("/").last))
      });
      //上传图片到服务器 ?? 全部都上传到大兴服务器吗?
      LandResponsitory.fileUpload(postData).then((value) {
        if (!field.context.mounted) {
          return;
        }
        BrnLoadingDialog.dismiss(field.context);
        if (onUpload != null) {
          onUpload(BDHFile(url: value.data));
        }
        field.didChange(BDHFile(url: value.data));
      }).onError((e, s) {
        Log.e("upload image error", error: e, stackTrace: s);
        var request = RequestException.handleError(e);
        if (request.isCancel) {
          return;
        }
        if (!field.context.mounted) {
          return;
        }
        BrnLoadingDialog.dismiss(field.context);
        showToast(request.message ?? "图片上传失败,请稍后重试");
      });
    } else {
      showToast("未知文件");
    }
  }
}

class BdhSingleImagePicker extends FormField<BDHFile> {
  final bool checkState;
  BdhSingleImagePicker(
      {super.key,
      super.initialValue,
      super.validator,
      super.onSaved,
      required FormItem item,
      OnPickerCallback onPickerCallback = _defaultPickerCallback,
      double? width,
      double? height,
      double? maxImageWidth,
      double? maxImageHeight,
      OnUpload? onUpload,
      this.checkState = false})
      : super(builder: (field) {
          openPicker(ImageSource type) {
            ImagePicker picker = ImagePicker();
            picker
                .pickImage(
                    source: type,
                    maxHeight: maxImageHeight ?? 720,
                    maxWidth: maxImageWidth ?? 720)
                .then((image) {
              if (!field.context.mounted) {
                return;
              }
              onPickerCallback.call(field, onUpload, image);
            });
          }

          openImage(ImageSource type) async {
            if (type == ImageSource.camera) {
              PermissionUtil.requestCameraPermission(field.context, "头像")
                  .then((res) {
                if (res == true) {
                  openPicker(type);
                }
              });
            }
            if (type == ImageSource.gallery) {
              PermissionUtil.requestPhotosPermission(field.context, "头像")
                  .then((res) {
                if (res == true) {
                  openPicker(type);
                }
              });
            }
          }

          return GestureDetector(
              onTap: () {
                showModalBottomSheet(
                    backgroundColor: Colors.transparent,
                    useSafeArea: true,
                    context: field.context,
                    builder: (ctx) {
                      return CameraPhotoSelect(sourceSelect: (e) {
                        Navigator.of(field.context).pop();
                        openImage(e);
                      });
                    });
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  field.value == null
                      ? Container(
                          decoration: BoxDecoration(
                              color: const Color.fromRGBO(246, 247, 250, 1),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(5.px))),
                          width: width ?? 97.px,
                          height: height ?? 97.px,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add,
                                size: 33.px,
                                color: const Color.fromRGBO(125, 127, 131, 1),
                              ),
                              Text(
                                item.title ?? "",
                                style: TextStyle(
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500,
                                    color: const Color.fromRGBO(51, 51, 51, 1)),
                              )
                            ],
                          ),
                        )
                      : ClipRRect(
                          borderRadius: BorderRadius.all(Radius.circular(5.px)),
                          child: BdhNetworkImage(
                              width: width ?? 97.px,
                              height: height ?? 97.px,
                              url: field.value!.url!),
                        ),
                  SizedBox(
                    height: 15.px,
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ));
        });

  @override
  FormFieldState<BDHFile> createState() => _BdhSingleImagePickerState();
}

class _BdhSingleImagePickerState extends FormFieldState<BDHFile> {
  @override
  BdhSingleImagePicker get widget => super.widget as BdhSingleImagePicker;

  @override
  void didUpdateWidget(covariant BdhSingleImagePicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}
