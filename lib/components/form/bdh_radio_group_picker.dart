import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class BdhRadioGroupPicker extends FormField<String> {
  final FormItem item;
  final Function(String)? onChange;
  final Color selectIconColor;
  final double iconSize;
  final double fontSize;
  final Color titleColor;
  final Color radioTextColor;
  final double? minHeight;
  final bool showBottomLine;

  BdhRadioGroupPicker(
      {super.key,
      super.validator,
      super.onSaved,
      super.initialValue,
      required this.item,
      this.onChange,
      this.selectIconColor = const Color.fromRGBO(0, 152, 91, 1),
      this.iconSize = 20,
      this.titleColor = const Color.fromRGBO(44, 44, 52, 1),
      this.radioTextColor = const Color.fromRGBO(44, 44, 52, 1),
      this.minHeight,
      this.showBottomLine = true,
      this.fontSize = 20})
      : super(builder: (field) {
          BdhRadio buildRadioWidget(String id, String title) {
            return BdhRadio(
              id: id,
              title: title,
              iconSize: iconSize,
              spacing: 8.px,
              checkBoxLeftSpace: 0,
              selectColor: selectIconColor,
              titleFont: Font(
                  size: fontSize.toInt(),
                  lineHeight: fontSize.toInt(),
                  fontWeight: FontWeight.w600),
              titleColor: radioTextColor,
              radioStyle: TDRadioStyle.circle,
              showDivider: false,
            );
          }

          List<DictNode> dictNodeList = item.data;

          List<BdhRadio> radioWidgetList = dictNodeList.map<BdhRadio>((e) {
            return buildRadioWidget(e.code!, e.name!);
          }).toList();

          return Container(
            decoration: showBottomLine
                ? BoxDecoration(
                    border: Border(
                        bottom: BorderSide(
                            width: 1.px,
                            color: const Color.fromRGBO(226, 235, 231, 0.6))))
                : null,
            constraints: BoxConstraints(minHeight: minHeight ?? 52.px),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 92.px,
                      child: Text.rich(TextSpan(children: [
                        TextSpan(
                          text: "*",
                          style: TextStyle(
                              color: Colors.red,
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: item.title,
                          style: TextStyle(
                              fontSize: fontSize,
                              color: titleColor,
                              fontWeight: FontWeight.w500),
                        )
                      ])),
                    ),
                    const Spacer(),
                    SizedBox(
                        width: 220.px,
                        child: BdhRadioGroup(
                          selectId: field.value,
                          direction: Axis.horizontal,
                          rowCount: 1,
                          spacing: 2,
                          directionalTdRadios: radioWidgetList,
                          onRadioGroupChange: (selectedId) {
                            if (onChange != null && selectedId != null) {
                              onChange(selectedId);
                              field.didChange(selectedId);
                            }
                          },
                        ))
                  ],
                ),
                Visibility(
                    visible: field.errorText != null,
                    child: Text(
                      field.errorText ?? "",
                      style: const TextStyle(color: Colors.red),
                    ))
              ],
            ),
          );
        });
}
