import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';

class BdhTextCountArea extends FormField<String> {
  final FormItem item;
  final String? placeHolder;
  final String? unit;
  final bool? showTitle;
  final TextEditingController? controller;
  BdhTextCountArea(
      {super.key,
      super.validator,
      super.onSaved,
      super.initialValue,
      required this.item,
      this.controller,
      this.placeHolder,
      this.unit,
      this.showTitle})
      : super(builder: (field) {
          field as BdhTextInputState;

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("举报描述",
                  style:
                      TextStyle(fontSize: 16.px, fontWeight: FontWeight.bold)),
              SizedBox(
                height: 10.px,
              ),
              Text("请描述作品中存在的问题，如：涉及哪些受保护的动植物、违法行为或违禁品。",
                  style: TextStyle(
                      fontSize: 14.px,
                      fontWeight: FontWeight.w400,
                      color: const Color.fromRGBO(0, 0, 0, 0.6))),
              Container(
                margin: EdgeInsets.only(top: 10.px),
                width: 345.px,
                height: 132.px,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(4.px)),
                    border: Border.all(
                        color: const Color.fromRGBO(226, 235, 231, 1))),
                child: Stack(
                  children: [
                    Positioned(
                        top: 10.px,
                        left: 10.px,
                        child: Visibility(
                            visible: field.count == 0,
                            child: const Text(
                              "请详细填写，以提高举报成功率",
                              style: TextStyle(
                                  color: Color.fromRGBO(0, 0, 0, 0.4)),
                            ))),
                    Positioned(
                        child: SizedBox(
                      width: 345.px,
                      height: 110.px,
                      child: CupertinoTextField.borderless(
                        maxLines: 6,
                        maxLength: 200,
                        onChanged: (value) {
                          field.didChange(value);
                        },
                      ),
                    )),
                    Positioned(
                        right: 10.px,
                        bottom: 5.px,
                        child: Text("${field.count}/200",
                            style: const TextStyle(
                                color: Color.fromRGBO(0, 0, 0, 0.4))))
                  ],
                ),
              ),
            ],
          );
        });
  @override
  FormFieldState<String> createState() => BdhTextInputState();
}

class BdhTextInputState extends FormFieldState<String> {
  int count = 0;
  @override
  BdhTextCountArea get widget => super.widget as BdhTextCountArea;
  @override
  void initState() {
    super.initState();
    if (widget.controller == null) {
    } else {
      // widget.controller!.addListener(_handleControllerChanged);
    }
  }
}
