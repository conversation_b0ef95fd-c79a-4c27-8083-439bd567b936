import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BdhDropdownDateTimePicker extends FormField<DateTime> {
  final Function(DateTime?)? onChange;
  final bool checkState;
  BdhDropdownDateTimePicker(
      {super.key,
      super.initialValue,
      super.validator,
      super.autovalidateMode = AutovalidateMode.disabled,
      String? placeHolder,
      double? maxHeight,
      double? maxWidth,
      double? pickerHeight,
      this.onChange,
      String? title,
      TextStyle? textStyle,
      TextStyle? placeHolderStyle,
      CupertinoDatePickerMode datePickerMode =
          CupertinoDatePickerMode.monthYear,
      DateTime? minimumDate,
      DateTime? maximumDate,
      int minimumYear = 2024,
      int maximumYear = 2025,
      required DateFormat dateFormat,
      this.checkState = false,
      super.onSaved})
      : super(builder: (field) {
          textStyle ??= TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontSize: 14.px,
              fontWeight: FontWeight.w500);
          placeHolderStyle ??= TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontSize: 14.px,
              fontWeight: FontWeight.w500);

          void showBottomMultiSelectPicker(BuildContext context) {
            DateTime? cacheValue = initialValue ?? DateTime.now();
            showBottomPicker(context,
                title: title,
                contentWidget: SizedBox(
                    height: pickerHeight ?? 200.px,
                    child: CupertinoDatePicker(
                        mode: datePickerMode,
                        initialDateTime: initialValue,
                        minimumYear: minimumYear,
                        maximumYear: maximumYear,
                        minimumDate: minimumDate,
                        maximumDate: maximumDate,
                        onDateTimeChanged: (dateTime) {
                          cacheValue = dateTime;
                        })), onConfirm: () {
              Navigator.maybePop(field.context, cacheValue);
            }, onCancel: () {
              Navigator.maybePop(field.context);
            }).then((result) {
              if (result == initialValue || result == null) {
                return;
              }
              field.didChange(result);
              onChange?.call(result);
            });
          }

          return GestureDetector(
            onTap: () {
              showBottomMultiSelectPicker(field.context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                    constraints:
                        BoxConstraints(maxWidth: maxWidth ?? double.infinity),
                    child: Text(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        field.value != null
                            ? dateFormat.format(field.value!)
                            : placeHolder ?? "",
                        style: field.value == null
                            ? placeHolderStyle
                            : textStyle)),
                Icon(Icons.arrow_drop_down,
                    color: const Color.fromRGBO(51, 51, 51, 0.4), size: 20.px)
              ],
            ),
          );
        });

  @override
  FormFieldState<DateTime> createState() => _BdhDropdownDateTimePickerState();
}

class _BdhDropdownDateTimePickerState extends FormFieldState<DateTime> {
  @override
  BdhDropdownDateTimePicker get widget =>
      super.widget as BdhDropdownDateTimePicker;

  @override
  void didUpdateWidget(covariant BdhDropdownDateTimePicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}
