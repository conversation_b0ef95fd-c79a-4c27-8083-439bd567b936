import 'package:bdh_smart_agric_app/components/jh_cascade_tree_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class BdhDataPicker extends FormField {
  final Function(dynamic)? onChange;
  final FormItem item;
  BdhDataPicker(this.item,
      {super.key,
      super.onSaved,
      super.validator,
      super.initialValue,
      this.onChange})
      : super(builder: (field) {
          showBottomMultiSelectPicker(BuildContext context, data) {
            var tempData = [];
            for (var e in (item.data as List<DictNode>)) {
              tempData.add(e.toJson());
            }
            JhCascadeTreePicker.show(field.context,
                data: tempData,
                valueKey: "code",
                labelKey: "name",
                childrenKey: "children",
                clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
              field.didChange(ress);
              if (onChange != null) {
                onChange(ress);
              }
            });
          }

          return GestureDetector(
            onTap: () {
              showBottomMultiSelectPicker(field.context, item);
            },
            child: Container(
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          width: 1.px,
                          color: const Color.fromRGBO(226, 235, 231, 0.6)))),
              constraints: BoxConstraints(minHeight: 44.px),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text.rich(TextSpan(children: [
                        TextSpan(
                          text: "*",
                          style: TextStyle(
                              color: Colors.red,
                              fontSize: 16.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: item.title,
                          style: TextStyle(
                              fontSize: 16.px, fontWeight: FontWeight.w500),
                        )
                      ])),
                      Row(
                        children: [
                          Text(
                            field.value != null
                                ? (field.value! as List).map((e) {
                                    return e["name"];
                                  }).join("/")
                                : "",
                            style: TextStyle(
                                color: const Color.fromRGBO(22, 183, 96, 1),
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600),
                          ),
                          SizedBox(
                            width: 10.px,
                          ),
                          Image.asset(
                              width: 6.9.px,
                              height: 11.07.px,
                              ImageHelper.wrapAssets("arrow_right_black.png"))
                        ],
                      )
                    ],
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });
}
