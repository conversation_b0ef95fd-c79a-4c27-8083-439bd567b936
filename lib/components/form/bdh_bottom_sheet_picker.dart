import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

typedef FormFieldWidgetBuilder = Widget? Function(
    BuildContext context, FormFieldState<DictNode> field);

class BdhBottomSheetPicker extends FormField<DictNode> {
  final bool checkState;
  BdhBottomSheetPicker(
      {super.key,
      required FormItem item,
      String? placeHolder,
      double fontSize = 20,
      FormFieldWidgetBuilder? rightWidgetBuilder,
      String? sheetTitle,
      TextAlign textAlign = TextAlign.start,
      super.initialValue,
      super.validator,
      super.autovalidateMode = AutovalidateMode.disabled,
      Function(DictNode)? onChange,
      this.checkState = false,
      super.onSaved})
      : super(builder: (field) {
          return PickerContent(
            model: initialValue,
            item: item,
            field: field,
            fontSize: fontSize,
            sheetTitle: sheetTitle,
            textAlign: textAlign,
            rightWidgetBuilder: rightWidgetBuilder,
            placeHolder: placeHolder,
            onChange: (value) {
              field.didChange(value);
              onChange?.call(value);
            },
          );
        });

  @override
  FormFieldState<DictNode> createState() => _BdhBottomSheetPickerState();
}

class _BdhBottomSheetPickerState extends FormFieldState<DictNode> {
  @override
  BdhBottomSheetPicker get widget => super.widget as BdhBottomSheetPicker;

  @override
  void didUpdateWidget(covariant BdhBottomSheetPicker oldWidget) {
    Log.d(
        "_BdhBottomSheetPickerState didUpdateWidget ${oldWidget.initialValue?.toJson()}  ${widget.initialValue?.toJson()}");
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}

class PickerContent extends StatelessWidget {
  final DictNode? model;
  final FormItem item;
  final String? placeHolder;
  final double fontSize;
  final Function(DictNode) onChange;
  final FormFieldWidgetBuilder? rightWidgetBuilder;
  final FormFieldState<DictNode> field;
  final String? sheetTitle;
  final TextAlign textAlign;

  const PickerContent(
      {super.key,
      required this.model,
      required this.item,
      required this.fontSize,
      required this.field,
      required this.sheetTitle,
      this.placeHolder,
      this.rightWidgetBuilder,
      required this.onChange,
      required this.textAlign});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            showBottomMultiSelectPicker(context, sheetTitle, item.data);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        width: 1.px,
                        color: const Color.fromRGBO(226, 235, 231, 0.6)))),
            constraints: BoxConstraints(minHeight: 52.px),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 92.px,
                      child: Text.rich(TextSpan(children: [
                        TextSpan(
                          text: "*",
                          style: TextStyle(
                              color: (item.isRequired ?? false)
                                  ? Colors.red
                                  : Colors.transparent,
                              fontSize: fontSize,
                              fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: item.title,
                          style: TextStyle(
                              fontSize: fontSize,
                              color: const Color.fromRGBO(44, 44, 52, 1),
                              fontWeight: FontWeight.w500),
                        )
                      ])),
                    ),
                    Expanded(
                        child: Text.rich(
                            textAlign: textAlign,
                            TextSpan(
                              text: field.value?.name ?? placeHolder ?? "",
                              style: TextStyle(
                                  color: (field.errorText != null &&
                                          field.value == null)
                                      ? const Color(0xFFFFCDD2)
                                      : field.value == null
                                          ? const Color.fromRGBO(
                                              44, 44, 52, 0.35)
                                          : const Color.fromRGBO(44, 44, 52, 1),
                                  fontSize: fontSize,
                                  fontWeight: FontWeight.w400),
                            ))),
                    SvgPicture.asset(
                        ImageHelper.wrapAssets("arrow_forward.svg"),
                        width: 22.px,
                        height: 22.px,
                        colorFilter: const ColorFilter.mode(
                            Color.fromRGBO(204, 204, 204, 1), BlendMode.srcIn)),
                    rightWidgetBuilder == null
                        ? const SizedBox.shrink()
                        : rightWidgetBuilder!(context, field) ??
                            const SizedBox.shrink(),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  showBottomMultiSelectPicker(
      BuildContext context, String? title, List<DictNode> data) {
    BrnMultiDataPicker(
      context: context,
      title: title ?? "",
      delegate: Brn1RowDelegate(firstSelectedIndex: 0, list: data),
      confirmClick: (list) {
        DictNode result = data[list[0]];
        onChange(result);
      },
    ).show();
  }
}
