import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BdhSaleTypePicker extends FormField<SaleTypeModel> {
  final FormItem item;

  BdhSaleTypePicker(
      {super.key, required this.item, super.initialValue, super.onSaved})
      : super(builder: (field) {
          return PickerContent(
            model: initialValue!,
            onChange: (value) {
              field.didChange(value);
            },
          );
        });
}

class SaleTypeModel {
  DictNode? saleType;
  DateTime? saleTime;
  SaleTypeModel(this.saleType, this.saleTime);
}

class PickerContent extends StatefulWidget {
  final SaleTypeModel model;
  final Function(SaleTypeModel) onChange;
  const PickerContent({super.key, required this.model, required this.onChange});

  @override
  State<StatefulWidget> createState() => PickerContentState();
}

class PickerContentState extends State<PickerContent> {
  DateTime? saleTime;
  DictNode? saleType;
  bool showPreSale = false;
  @override
  void initState() {
    super.initState();

    saleType = widget.model.saleType;
    saleTime = widget.model.saleTime;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            showBottomMultiSelectPicker(context, [
              DictNode(name: "现货", code: "1"),
              DictNode(name: "预售", code: "2")
            ]);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        width: 1.px,
                        color: const Color.fromRGBO(226, 235, 231, 0.6)))),
            constraints: BoxConstraints(minHeight: 44.px),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text.rich(TextSpan(children: [
                      TextSpan(
                        text: "*",
                        style: TextStyle(
                            color: Colors.red,
                            fontSize: 16.px,
                            fontWeight: FontWeight.w500),
                      ),
                      TextSpan(
                        text: "销售类型",
                        style: TextStyle(
                            fontSize: 16.px, fontWeight: FontWeight.w500),
                      )
                    ])),
                    Row(
                      children: [
                        Text(
                          saleType?.name ?? "",
                          style: TextStyle(
                              color: const Color.fromRGBO(22, 183, 96, 1),
                              fontSize: 16.px,
                              fontWeight: FontWeight.w600),
                        ),
                        SizedBox(
                          width: 10.px,
                        ),
                        Image.asset(
                            width: 6.9.px,
                            height: 11.07.px,
                            ImageHelper.wrapAssets("arrow_right_black.png"))
                      ],
                    )
                  ],
                ),
              ],
            ),
          ),
        ),
        showPreSale
            ? GestureDetector(
                onTap: () {
                  showDatePicker(
                          context: context,
                          firstDate: DateTime.now(),
                          lastDate: DateTime.utc(2030))
                      .then((time) {
                    setState(() {
                      saleTime = time;
                    });
                    widget.onChange(SaleTypeModel(saleType, saleTime));
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1.px,
                              color:
                                  const Color.fromRGBO(226, 235, 231, 0.6)))),
                  constraints: BoxConstraints(minHeight: 44.px),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text.rich(TextSpan(children: [
                            TextSpan(
                              text: "*",
                              style: TextStyle(
                                  color: Colors.red,
                                  fontSize: 16.px,
                                  fontWeight: FontWeight.w500),
                            ),
                            TextSpan(
                              text: "预售日期",
                              style: TextStyle(
                                  fontSize: 16.px, fontWeight: FontWeight.w500),
                            )
                          ])),
                          Row(
                            children: [
                              Text(
                                saleTime != null ? timeToStr(saleTime!) : "",
                                style: TextStyle(
                                    color: const Color.fromRGBO(22, 183, 96, 1),
                                    fontSize: 16.px,
                                    fontWeight: FontWeight.w600),
                              ),
                              SizedBox(
                                width: 10.px,
                              ),
                              Image.asset(
                                  width: 6.9.px,
                                  height: 11.07.px,
                                  ImageHelper.wrapAssets(
                                      "arrow_right_black.png"))
                            ],
                          )
                        ],
                      ),
                    ],
                  ),
                ),
              )
            : Container(),
      ],
    );
  }

  timeToStr(DateTime time) {
    return DateFormat("yyyy-MM-dd").format(time);
  }

  showBottomMultiSelectPicker(BuildContext context, data) {
    BrnMultiDataPicker(
      context: context,
      title: "销售类型",
      delegate: Brn1RowDelegate(firstSelectedIndex: 0, list: data),
      confirmClick: (list) {
        setState(() {
          saleType = data[list[0]];
          if (saleType?.code == "1") {
            showPreSale = false;
          } else {
            showPreSale = true;
          }
        });
        widget.onChange(SaleTypeModel(saleType, saleTime));
      },
    ).show();
  }
}
