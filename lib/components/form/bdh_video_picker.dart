import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/video_thumbnail.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';

class BdhVideoPicker extends FormField<List<BDHFile>> {
  final FormItem item;
  final bool? showTitle;
  BdhVideoPicker(
      {super.key,
      super.initialValue,
      super.validator,
      super.onSaved,
      required this.item,
      this.showTitle})
      : super(builder: (field) {
          openImage(ImageSource type) {
            ImagePicker picker = ImagePicker();
            picker.pickVideo(source: type).then((image) {
              //视频上传
              if (image != null) {
                image.readAsBytes().then((bytes) {
                  String? mimeType = lookupMimeType(image.name);
                  if (mimeType != null) {
                    FormData postData = FormData.fromMap({
                      "file": MultipartFile.fromBytes(bytes,
                          filename: image.name,
                          contentType: MediaType(mimeType.split("/").first,
                              mimeType.split("/").last))
                    });

                    BDHResponsitory.uploadFile(postData).then((value) {
                      field.value!.add(value);
                      field.didChange(field.value);
                    });
                  } else {
                    showToast("未知文件");
                  }
                });
              }
            });
          }

          return Container(
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        width: 1.px,
                        color: (showTitle ?? false)
                            ? const Color.fromRGBO(226, 235, 231, 0.6)
                            : Colors.transparent))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text.rich(TextSpan(children: [
                  TextSpan(
                    text: (item.isRequired ?? false) ? "*" : "",
                    style: TextStyle(
                        color: Colors.red,
                        fontSize: 16.px,
                        fontWeight: FontWeight.w500),
                  ),
                  TextSpan(
                    text: item.title,
                    style:
                        TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
                  )
                ])),
                SizedBox(
                  height: 5.px,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    ...(field.value ?? []).map((e) {
                      return SizedBox(
                        width: 68.px,
                        height: 68.px,
                        child: Stack(
                          children: [
                            Positioned(
                                bottom: 0,
                                left: 0,
                                child: Container(
                                  width: 60.px,
                                  height: 60.px,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(4.px))),
                                  child: ClipRRect(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(4.px)),
                                    child: VideoThumbnailWidget(
                                      url: e.url ?? "",
                                      uploadSuccess: (url) {},
                                    ),
                                  ),
                                )),
                            // CachedNetworkImage(
                            //           height: 60.px,
                            //           width: 60.px,
                            //           fit: BoxFit.cover,
                            //           imageUrl:
                            //               "${urlConfig.microfront}${e.poster}",
                            //           errorWidget: (context, url, error) {
                            //             return Text("暂无封面");
                            //           },
                            //         )
                            Positioned(
                                top: 0,
                                right: 0,
                                child: GestureDetector(
                                  onTap: () {
                                    field.value!.remove(e);
                                    field.didChange(field.value);
                                  },
                                  child: Image.asset(
                                      width: 16.px,
                                      height: 16.px,
                                      ImageHelper.wrapAssets("delete.png")),
                                ))
                          ],
                        ),
                      );
                    }),
                    Visibility(
                        visible: (field.value ?? []).isEmpty,
                        child: GestureDetector(
                          onTap: () {
                            showModalBottomSheet(
                                backgroundColor: Colors.transparent,
                                useSafeArea: true,
                                context: field.context,
                                builder: (ctx) {
                                  return CameraPhotoSelect(sourceSelect: (e) {
                                    openImage(e);
                                  });
                                });
                          },
                          child: Container(
                            alignment: Alignment.center,
                            width: 60.px,
                            height: 60.px,
                            decoration: BoxDecoration(
                                color: const Color.fromRGBO(244, 245, 245, 1),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(4.px))),
                            child: Image.asset(
                                width: 32.px,
                                height: 32.px,
                                ImageHelper.wrapAssets("camera.png")),
                          ),
                        ))
                  ],
                ),
                SizedBox(
                  height: 10.px,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "     ${(field.value ?? []).length}/1",
                      style:
                          const TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
                    ),
                    const Text(
                      "单个视频最大不超过10M",
                      style: TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
                    )
                  ],
                ),
                SizedBox(
                  height: 15.px,
                )
              ],
            ),
          );
        });
}

// class BDHFile {
//   String? url;
//   String? poster;
//   String? fileName;

//   BDHFile({this.url, this.poster, this.fileName});

//   BDHFile.fromJson(Map<String, dynamic> json) {
//     url = json['url'];
//     poster = json['poster'];
//     fileName = json['fileName'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['url'] = url;
//     data['poster'] = poster;
//     data['fileName'] = fileName;
//     return data;
//   }
// }

class CameraPhotoSelect extends StatelessWidget {
  final Function(ImageSource) sourceSelect;
  const CameraPhotoSelect({super.key, required this.sourceSelect});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(9.px), topRight: Radius.circular(9.px))),
      height: 144.px + MediaQuery.of(context).padding.bottom,
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              sourceSelect(ImageSource.camera);
            },
            child: Container(
              height: 44.px,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: const Text(
                "录制",
                style: TextStyle(
                    color: Color.fromRGBO(23, 151, 100, 1),
                    fontWeight: FontWeight.w500),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              sourceSelect(ImageSource.gallery);
            },
            child: Container(
              color: Colors.transparent,
              height: 44.px,
              alignment: Alignment.center,
              child: const Text("从相册选择",
                  style: TextStyle(
                      color: Color.fromRGBO(23, 151, 100, 1),
                      fontWeight: FontWeight.w500)),
            ),
          ),
          Container(
            width: 345.px,
            height: 1.px,
            color: const Color.fromRGBO(226, 235, 231, 0.6),
          ),
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              height: 55.px,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: const Text(
                "取消",
                style: TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
              ),
            ),
          )
        ],
      ),
    );
  }
}
