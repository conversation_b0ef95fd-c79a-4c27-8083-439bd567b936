import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BdhDateRangePicker extends FormField<DateTimeRange> {
  final FormItem item;

  BdhDateRangePicker(
      {super.key,
      required this.item,
      super.initialValue,
      super.onSaved,
      super.validator})
      : super(builder: (field) {
          timeToStr(DateTimeRange time) {
            return "${DateFormat("yyyy-MM-dd").format(time.start)}~${DateFormat("yyyy-MM-dd").format(time.end)}";
          }

          return GestureDetector(
            onTap: () {
              showDateRangePicker(
                      context: field.context,
                      firstDate: DateTime.utc(2000),
                      lastDate: DateTime.utc(2030))
                  .then((timeRange) {
                field.didChange(timeRange);
              });
            },
            child: Container(
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          width: 1.px,
                          color: const Color.fromRGBO(226, 235, 231, 0.6)))),
              constraints: BoxConstraints(minHeight: 44.px),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text.rich(TextSpan(children: [
                        TextSpan(
                          text: item.isRequired == true ? "*" : "",
                          style: TextStyle(
                              color: Colors.red,
                              fontSize: 16.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: item.title ?? "",
                          style: TextStyle(
                              fontSize: 16.px, fontWeight: FontWeight.w500),
                        )
                      ])),
                      Row(
                        children: [
                          Text(
                            field.value != null
                                ? timeToStr(field.value!)
                                : "请选择日期区间",
                            style: TextStyle(
                                color: const Color.fromRGBO(22, 183, 96, 1),
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600),
                          ),
                          SizedBox(
                            width: 10.px,
                          ),
                          Image.asset(
                              width: 6.9.px,
                              height: 11.07.px,
                              ImageHelper.wrapAssets("arrow_right_black.png"))
                        ],
                      )
                    ],
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });
}
