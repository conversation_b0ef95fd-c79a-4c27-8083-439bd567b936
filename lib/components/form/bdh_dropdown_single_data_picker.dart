import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BdhDropDownSingleDataPicker extends FormField<DictNode> {
  final FormItem item;
  final bool checkState;
  final Function(DictNode?)? onChange;
  BdhDropDownSingleDataPicker(
      {super.key,
      super.initialValue,
      super.validator,
      super.autovalidateMode = AutovalidateMode.disabled,
      String? placeHolder,
      double? maxHeight,
      double? minHeight,
      double? maxWidth,
      double? minWidth,
      double? itemExtent,
      required this.item,
      this.onChange,
      String? title,
      TextStyle? textStyle,
      TextStyle? placeHolderStyle,
      Color? iconColor,
      CupertinoDatePickerMode datePickerMode =
          CupertinoDatePickerMode.monthYear,
      this.checkState = false,
      MainAxisAlignment mainAxisAlignment = MainAxisAlignment.center,
      super.onSaved})
      : super(builder: (field) {
          textStyle ??= TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontSize: 14.px,
              fontWeight: FontWeight.w500);
          placeHolderStyle ??= TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontSize: 14.px,
              fontWeight: FontWeight.w500);

          void showBottomMultiSelectPicker(
              BuildContext context, List<DictNode> data) {
            var cacheValue = data.isEmpty ? null : data[0];
            showBottomPicker<DictNode>(
              field.context,
              title: title,
              contentWidget: Container(
                  constraints: BoxConstraints(
                    maxHeight: maxHeight ?? 200.px,
                    minHeight: minHeight ?? 0,
                    maxWidth: maxWidth ?? 200.px,
                    minWidth: minWidth ?? 0,
                  ),
                  child: CupertinoPicker.builder(
                      itemExtent: itemExtent ?? 32.px,
                      onSelectedItemChanged: (index) {
                        cacheValue = data[index];
                      },
                      itemBuilder: (context, index) {
                        return Center(
                            child: Text(
                          data[index].name ?? "",
                          style: TextStyle(
                              color: const Color.fromRGBO(51, 51, 51, 1),
                              fontSize: 16.px,
                              fontWeight: FontWeight.w500),
                        ));
                      },
                      childCount: item.data.length)),
              onConfirm: () {
                Navigator.maybePop(field.context, cacheValue);
              },
              onCancel: () {
                Navigator.maybePop(field.context);
              },
            ).then((result) {
              if (result == initialValue || result == null) {
                return;
              }
              field.didChange(result);
              onChange?.call(result);
            });
          }

          return GestureDetector(
            onTap: () {
              showBottomMultiSelectPicker(field.context, item.data);
            },
            child: Row(
              mainAxisAlignment: mainAxisAlignment,
              children: [
                Container(
                    constraints:
                        BoxConstraints(maxWidth: maxWidth ?? double.infinity),
                    child: Text(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        field.value?.name ?? placeHolder ?? "",
                        style: field.value == null
                            ? placeHolderStyle
                            : textStyle)),
                Icon(Icons.arrow_drop_down,
                    color: iconColor ?? const Color.fromRGBO(51, 51, 51, 0.4),
                    size: 20.px)
              ],
            ),
          );
        });

  @override
  FormFieldState<DictNode> createState() => _BdhDropDownDatePickerState();
}

class _BdhDropDownDatePickerState extends FormFieldState<DictNode> {
  @override
  BdhDropDownSingleDataPicker get widget =>
      super.widget as BdhDropDownSingleDataPicker;

  @override
  void didUpdateWidget(covariant BdhDropDownSingleDataPicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}

class BdhDropDownDatePicker extends FormField<String> {
  final FormItem item;

  final bool showBottomLine;
  final bool showArrow;
  final TextAlign textAlign;
  final double? valueSpace;
  final double? minHeight;
  final double? titleWidth;
  final String? valueStart;
  final TextStyle? textStyle;
  final TextStyle? titleStyle;
  final TextStyle? placeholderStyle;
  final String? placeholder;
  final bool autoValidate;
  final Function(String?)? onChange;

  BdhDropDownDatePicker({
    super.key,
    required this.item,
    super.initialValue,
    super.onSaved,
    super.validator,
    this.showBottomLine = true,
    this.showArrow = true,
    this.autoValidate = true,
    this.textAlign = TextAlign.right,
    this.valueSpace,
    this.minHeight,
    this.valueStart,
    this.textStyle,
    this.titleStyle,
    this.titleWidth,
    this.placeholderStyle,
    this.placeholder,
    this.onChange,
    double? maxHeight,
    double? maxWidth,
  }) : super(builder: (field) {
          String? timeToStr(DateTime? time) {
            if (time == null) {
              return null;
            }
            return DateFormat("yyyy-MM-dd").format(time);
          }

          Widget titleWidget = Text.rich(TextSpan(children: [
            TextSpan(
              text: item.isRequired == true ? "*" : "",
              style: TextStyle(
                  color: Colors.red,
                  fontSize: 16.px,
                  fontWeight: FontWeight.w500),
            ),
            TextSpan(
              text: item.title ?? "",
              style: titleStyle ??
                  TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
            )
          ]));

          if (titleWidth != null) {
            titleWidget = SizedBox(width: titleWidth, child: titleWidget);
          }

          return GestureDetector(
            onTap: () {
              showDatePicker(
                      context: field.context,
                      initialEntryMode: DatePickerEntryMode.calendarOnly,
                      firstDate: DateTime.utc(2024),
                      lastDate: DateTime.utc(2030))
                  .then((time) {
                var str = timeToStr(time);
                field.didChange(str);
                onChange?.call(str);
                if (autoValidate) field.validate();
              });
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                    constraints:
                        BoxConstraints(maxWidth: maxWidth ?? double.infinity),
                    child: Text(
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        field.value ?? placeholder ?? "请选择日期",
                        style: field.value == null
                            ? placeholderStyle
                            : textStyle)),
                Icon(Icons.arrow_drop_down,
                    color: const Color.fromRGBO(51, 51, 51, 0.4), size: 20.px)
              ],
            ),
          );
        });

  @override
  FormFieldState<String> createState() => _BdhDatePickerState();
}

class _BdhDatePickerState extends FormFieldState<String> {
  @override
  BdhDropDownDatePicker get widget => super.widget as BdhDropDownDatePicker;

  @override
  void didUpdateWidget(covariant BdhDropDownDatePicker oldWidget) {
    if (value != widget.initialValue) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}
