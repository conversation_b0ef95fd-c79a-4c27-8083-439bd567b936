import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BdhCountPicker extends FormField<int> {
  final FormItem item;
  final Function(int?)? onChange;

  final bool showBottomLine;
  final bool showArrow;
  final TextAlign textAlign;
  final double? valueSpace;
  final double? minHeight;
  final String? valueStart;
  final String? valueEnd;
  final TextStyle? textStyle;
  final TextStyle? titleStyle;
  final TextStyle? valueStyle;
  final double? titleWidth;
  final TextStyle? placeholderStyle;
  final String? placeholder;
  final bool autoValidate;
  final Function(BuildContext, FormFieldState<int>)? suffixBuilder;
  final String title;

  BdhCountPicker(
      {super.key,
      super.onSaved,
      super.validator,
      super.initialValue,
      required this.item,
      this.showBottomLine = true,
      this.showArrow = true,
      this.autoValidate = true,
      this.textAlign = TextAlign.right,
      this.title = "请选择",
      this.valueSpace,
      this.minHeight,
      this.valueStart,
      this.valueEnd,
      this.textStyle,
      this.titleStyle,
      this.valueStyle,
      this.placeholderStyle,
      this.placeholder,
      this.titleWidth,
      this.suffixBuilder,
      this.onChange})
      : super(builder: (field) {
          void showBottomMultiSelectPicker(BuildContext context) {
            var cacheValue = 0;
            showBottomPicker<int>(
              field.context,
              title: title,
              contentWidget: Container(
                  constraints: BoxConstraints(maxHeight: 200.px),
                  child: CupertinoPicker.builder(
                      itemExtent: 32.px,
                      onSelectedItemChanged: (index) {
                        cacheValue = index;
                      },
                      itemBuilder: (context, index) {
                        if (index < 1) {
                          return null;
                        }
                        return Center(
                            child: Text(
                          "$index",
                          style: TextStyle(
                              color: const Color.fromRGBO(51, 51, 51, 1),
                              fontSize: 16.px,
                              fontWeight: FontWeight.w500),
                        ));
                      })),
              onConfirm: () {
                Navigator.maybePop(field.context, cacheValue);
              },
              onCancel: () {
                Navigator.maybePop(field.context);
              },
            ).then((result) {
              if (result == initialValue || result == null) {
                return;
              }
              field.didChange(result);
              onChange?.call(result);
              if (autoValidate) field.validate();
            });
          }

          Widget titleWidget = Text.rich(TextSpan(children: [
            TextSpan(
              text: item.isRequired == true ? "*" : "",
              style: TextStyle(
                  color: Colors.red,
                  fontSize: 16.px,
                  fontWeight: FontWeight.w500),
            ),
            TextSpan(
              text: item.title,
              style: titleStyle ??
                  TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
            )
          ]));

          if (titleWidth != null) {
            titleWidget = SizedBox(width: titleWidth, child: titleWidget);
          }

          return GestureDetector(
            onTap: () {
              showBottomMultiSelectPicker(field.context);
            },
            child: Container(
              decoration: showBottomLine
                  ? BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1.px,
                              color: const Color.fromRGBO(226, 235, 231, 0.6))))
                  : null,
              constraints: BoxConstraints(minHeight: minHeight ?? 52.px),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      titleWidget,
                      Expanded(
                          child: Row(
                        children: [
                          SizedBox(
                            width: valueSpace ?? 10.px,
                          ),
                          if (textAlign == TextAlign.right) const Spacer(),
                          field.value != null
                              ? Text.rich(
                                  TextSpan(children: [
                                    TextSpan(
                                        text: valueStart ?? "",
                                        style: valueStyle),
                                    TextSpan(text: "${field.value ?? ""}"),
                                    TextSpan(
                                        text: valueEnd ?? "",
                                        style: valueStyle),
                                  ]),
                                  style: textStyle ??
                                      TextStyle(
                                          color: const Color.fromRGBO(
                                              22, 183, 96, 1),
                                          fontSize: 16.px,
                                          fontWeight: FontWeight.w600),
                                )
                              : Text(
                                  placeholder ?? "请选择${item.title}",
                                  style: placeholderStyle ??
                                      TextStyle(
                                          color: const Color.fromRGBO(
                                              131, 149, 142, 1),
                                          fontSize: 14.px,
                                          fontWeight: FontWeight.w400),
                                ),
                          if (textAlign == TextAlign.left) const Spacer(),
                          if (showArrow) ...[
                            SizedBox(
                              width: 10.px,
                            ),
                            Image.asset(
                                width: 6.9.px,
                                height: 11.07.px,
                                ImageHelper.wrapAssets("arrow_right_black.png"))
                          ],
                          if (suffixBuilder != null) ...[
                            SizedBox(
                              width: 10.px,
                            ),
                            suffixBuilder.call(field.context, field)
                          ]
                        ],
                      ))
                    ],
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });
}

class Brn1RowDelegate implements BrnMultiDataPickerDelegate {
  int firstSelectedIndex = 0;
  int secondSelectedIndex = 0;
  int thirdSelectedIndex = 0;
  List<DictNode> list = [];

  Brn1RowDelegate(
      {this.firstSelectedIndex = 0,
      this.secondSelectedIndex = 0,
      required this.list});

  @override
  int numberOfComponent() {
    return 1;
  }

  @override
  int numberOfRowsInComponent(int component) {
    return list.length;
  }

  @override
  String titleForRowInComponent(int component, int index) {
    return list[index].name ?? "";
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  selectRowInComponent(int component, int row) {
    if (0 == component) {
      firstSelectedIndex = row;
    }
  }

  @override
  int initSelectedRowForComponent(int component) {
    if (0 == component) {
      return firstSelectedIndex;
    }
    return 0;
  }
}
