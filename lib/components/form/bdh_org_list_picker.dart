import 'package:bdh_smart_agric_app/components/bdh_no_data.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/video_search.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/provider_widget.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_refresh_list_model.dart';
import 'package:bdh_smart_agric_app/utils/request/rice_price_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class BdhOrgListPicker extends FormField<OrgTreeItem> {
  final FormItem item;
  final OrgType orgType;
  BdhOrgListPicker(this.item,
      {super.key,
      super.onSaved,
      super.validator,
      super.initialValue,
      required this.orgType})
      : super(builder: (field) {
          return GestureDetector(
            onTap: () {
              Navigator.of(field.context)
                  .push(CupertinoPageRoute(builder: (ctx) {
                return OrgListView(
                  orgType: orgType,
                  onSelect: (v) {
                    field.didChange(v);
                  },
                );
              }));
            },
            child: Container(
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          width: 1.px,
                          color: const Color.fromRGBO(226, 235, 231, 0.6)))),
              constraints: BoxConstraints(minHeight: 44.px),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text.rich(TextSpan(children: [
                        TextSpan(
                          text: "*",
                          style: TextStyle(
                              color: Colors.red,
                              fontSize: 16.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: item.title,
                          style: TextStyle(
                              fontSize: 16.px, fontWeight: FontWeight.w500),
                        )
                      ])),
                      Row(
                        children: [
                          Text(
                            field.value?.orgName ?? "",
                            style: TextStyle(
                                color: const Color.fromRGBO(22, 183, 96, 1),
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600),
                          ),
                          SizedBox(
                            width: 10.px,
                          ),
                          Image.asset(
                              width: 6.9.px,
                              height: 11.07.px,
                              ImageHelper.wrapAssets("arrow_right_black.png"))
                        ],
                      )
                    ],
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });
}

enum OrgType { farm, precinct }

class OrgListView extends StatefulWidget {
  final OrgType orgType;
  final Function(OrgTreeItem) onSelect;
  const OrgListView({super.key, required this.onSelect, required this.orgType});

  @override
  State<StatefulWidget> createState() => OrgListViewState();
}

class OrgListViewState extends State<OrgListView> {
  @override
  Widget build(BuildContext context) {
    return ProviderWidget(
      builder: (ctx, ChangeNotifier model, child) {
        model as OrgListViewModel;

        return Scaffold(
          appBar: SearchAppBar(
            focusScopeNode: FocusScopeNode(),
            onSearch: (v) {
              model.keyword = v;
              model.refresh();
            },
          ),
          body: model.list.isEmpty
              ? const Center(
                  child: BdhNoData(desc: "暂无发布"),
                )
              : SmartRefresher(
                  enablePullUp: true,
                  onRefresh: model.refresh,
                  onLoading: model.loadMore,
                  controller: model.refreshController,
                  child: ListView.builder(
                      itemCount: model.list.length,
                      itemBuilder: (ctx, idx) {
                        return OrgTreeItemView(
                          item: model.list[idx],
                          onSelect: (item) {
                            widget.onSelect(item);
                            Navigator.of(context).pop();
                          },
                        );
                      }),
                ),
        );
      },
      model: OrgListViewModel(),
      onModelReady: (ChangeNotifier model) {
        model as OrgListViewModel;
        model.orgType = widget.orgType;
        model.initData();
      },
    );
  }
}

class OrgListViewModel extends ViewStateRefreshListModel {
  String keyword = "";
  OrgType orgType = OrgType.precinct;

  @override
  Future<List<OrgTreeItem>> loadData({int? pageNum}) async {
    if (orgType == OrgType.precinct) {
      var items = await PriceResponsitory.getSaleOrg(
          {"keyword": keyword, "pageNo": pageNum, "pageSize": 20});
      return items.data?.dataList ?? [];
    } else {
      var items = await PriceResponsitory.getBuyOrg(
          {"keyword": keyword, "pageNo": pageNum, "pageSize": 20});
      return items.data?.dataList ?? [];
    }
  }
}

class OrgTreeItemView extends StatelessWidget {
  final Function(OrgTreeItem) onSelect;
  final OrgTreeItem item;
  const OrgTreeItemView(
      {super.key, required this.item, required this.onSelect});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onSelect(item);
      },
      child: Container(
        width: 375.px,
        height: 44.px,
        padding: EdgeInsets.only(left: 10.px),
        alignment: Alignment.centerLeft,
        decoration: const BoxDecoration(
            border: Border(
                bottom: BorderSide(color: Color.fromRGBO(226, 235, 231, 0.4)))),
        child: Text(item.orgName ?? ""),
      ),
    );
  }
}
