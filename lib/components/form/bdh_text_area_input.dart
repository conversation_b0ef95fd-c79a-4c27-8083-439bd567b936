import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';

class BdhTextAreaInput extends FormField<String> {
  final TextEditingController? controller;
  final ValueChanged<String?> onChanged;
  BdhTextAreaInput(
      {super.key,
      required int lines,
      int? maxLength,
      required this.onChanged,
      required FormItem item,
      String? placeholder,
      double? height,
      bool showBottomLine = true,
      this.controller,
      super.autovalidateMode = AutovalidateMode.disabled,
      super.validator,
      super.initialValue,
      super.onSaved})
      : super(builder: (field) {
          field as _BdhTextAreaInputState;
          return _BdhTextInputWidget(
              lines: lines,
              maxLength: maxLength,
              placeholder: placeholder,
              height: height,
              field: field,
              item: item,
              showBottomLine: showBottomLine,
              onChanged: (value) {
                field.didChange(value);
                onChanged.call(value);
              });
        });

  @override
  FormFieldState<String> createState() => _BdhTextAreaInputState();
}

class _BdhTextAreaInputState extends FormFieldState<String> {
  TextEditingController? _controller;
  TextEditingController? get _effectiveController =>
      widget.controller ?? _controller;
  @override
  BdhTextAreaInput get widget => super.widget as BdhTextAreaInput;
  @override
  void initState() {
    super.initState();
    if (widget.controller == null) {
      _controller = TextEditingController(text: widget.initialValue ?? "");
    }
    _effectiveController?.addListener(_handleControllerChanged);
  }

  @override
  void dispose() {
    super.dispose();
    _effectiveController?.removeListener(_handleControllerChanged);
    _controller?.dispose();
  }

  _handleControllerChanged() {
    didChange(_effectiveController?.text);
    widget.onChanged.call(_effectiveController?.text);
  }

  @override
  void didUpdateWidget(covariant BdhTextAreaInput oldWidget) {
    if (_effectiveController?.text != widget.initialValue) {
      _effectiveController?.text = widget.initialValue ?? "";
    }
    super.didUpdateWidget(oldWidget);
  }
}

class _BdhTextInputWidget extends StatefulWidget {
  final int lines;
  final int? maxLength;
  final ValueChanged<String> onChanged;
  final String? placeholder;
  final double? height;
  final _BdhTextAreaInputState field;
  final FormItem item;
  final bool showBottomLine;
  const _BdhTextInputWidget({
    super.key,
    required this.lines,
    required this.onChanged,
    required this.item,
    this.maxLength,
    this.placeholder,
    this.height,
    required this.field,
    required this.showBottomLine,
  });

  @override
  State<_BdhTextInputWidget> createState() => __BdhTextInputWidgetState();
}

class __BdhTextInputWidgetState extends State<_BdhTextInputWidget> {
  int count = 0;

  late final controller = widget.field._effectiveController;

  @override
  void initState() {
    super.initState();
    _countChange();
  }

  void _countChange() {
    setState(() {
      count = controller?.text.length ?? 0;
    });
  }

  Widget _widgetTitle(String title) {
    return _widgetRow(showBottomLine: false, children: [
      Expanded(
          child: Text(
        title,
        style: TextStyle(
            color: const Color.fromRGBO(44, 44, 44, 1),
            fontWeight: FontWeight.w500,
            fontSize: 16.px),
      )),
    ]);
  }

  Widget _widgetRow(
      {required List<Widget> children, bool showBottomLine = true}) {
    return Container(
        height: 44.px,
        decoration: BoxDecoration(
          border: showBottomLine
              ? Border(
                  bottom: BorderSide(
                      color: const Color.fromRGBO(226, 235, 231, 0.6),
                      width: 1.px))
              : null,
        ),
        child: Row(
          children: children,
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(bottom: 7.px),
        decoration: widget.showBottomLine
            ? BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                      color: const Color.fromRGBO(226, 235, 231, 0.6),
                      width: 1.px),
                ),
              )
            : null,
        child: Column(
          children: [
            _widgetTitle(widget.item.title ?? ""),
            Container(
                height: widget.height ?? (widget.lines + 1) * 20.px,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(4.px)),
                  border: Border.all(
                      color: const Color.fromRGBO(226, 235, 231, 0.6),
                      width: 1.px),
                ),
                child: Stack(
                  children: [
                    CupertinoTextField.borderless(
                      controller: controller,
                      onChanged: (value) {
                        widget.onChanged.call(value);
                        _countChange();
                      },
                      placeholder: widget.placeholder,
                      maxLength: widget.maxLength,
                      maxLines: widget.lines,
                      minLines: widget.lines,
                      enabled: widget.item.isCanEdit != false,
                      keyboardType: TextInputType.text,
                      padding: EdgeInsets.all(5.px),
                      style: TextStyle(
                        fontSize: 14.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(44, 44, 52, 1),
                      ),
                      placeholderStyle: TextStyle(
                        fontSize: 14.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(44, 44, 52, 0.35),
                      ),
                    ),
                    Positioned(
                        right: 5.px,
                        bottom: 5.px,
                        child: Text(
                          widget.maxLength == null
                              ? "$count"
                              : "$count/${widget.maxLength}",
                          style: TextStyle(
                              color: const Color.fromRGBO(44, 44, 44, 0.2),
                              fontWeight: FontWeight.w500,
                              fontSize: 12.px),
                        ))
                  ],
                ))
          ],
        ));
  }
}
