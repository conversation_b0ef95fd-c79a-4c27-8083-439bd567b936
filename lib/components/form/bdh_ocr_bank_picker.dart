import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input_small.dart';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bank_service.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:bruno/bruno.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';

typedef OnPickerCallback = Future Function(
    BuildContext context,
    FormFieldState<String> field,
    TextEditingController? controller,
    XFile? file,
    {ValueChanged<String?>? onScanSuccess});

//土地承包,暂时未用到,因为只是识别服务,图片的地址除了识别并没有其他意义
//先上传到baseHttp，然后调用 authHttp 的识别服务
Future _landContractBankOcrPickerCallback(
    BuildContext context,
    FormFieldState<String> field,
    TextEditingController? controller,
    XFile? image,
    {ValueChanged<String?>? onScanSuccess}) async {
  if (image != null) {
    //这里需要考虑几种图片的格式
    String? mimeType = lookupMimeType(image.name);

    if (mimeType != null) {
      BrnLoadingDialog.show(context,
          content: "识别中...  ", barrierDismissible: false);
      var bytes = await image.readAsBytes();
      Log.d(" 图片压缩前大小 ${bytes.length / 1024} kb");
      //图片太大，需要对图片进行压缩,压缩后变成image/jpeg,
      //如果压缩失败则直接上上传未压缩的图片
      try {
        bytes = await FlutterImageCompress.compressWithList(bytes,
            minHeight: 720, minWidth: 720, format: CompressFormat.jpeg);
        //经过压缩之后图片格式已经转换成了 jpeg
        mimeType = "image/jpeg";
      } catch (e, s) {
        Log.e("图片压缩失败", error: e, stackTrace: s);
      }
      Log.d(
          " 图片压缩后大小: ${bytes.length / 1024} ,图片名称:${image.name} ,mime:$mimeType");
      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(bytes,
            filename: image.name,
            contentType:
                MediaType(mimeType!.split("/").first, mimeType.split("/").last))
      });
      //先上传图片到服务器

      BdhLandResponsitory.uploadFile(postData).then((result) async {
        if (!context.mounted) {
          return;
        }
        if (result.code == 0 && result.success == true && result.data != null) {
          UserInfo? userInfo = StorageUtil.userInfo();
          //图片识别调用
          BankService.common()
              .bankCardOcr(
                  picUrl: result.data,
                  accountId: userInfo?.data?.id,
                  systemId: "systemlandcontract")
              .then((result) {
            if (!context.mounted) {
              return;
            }

            BrnLoadingDialog.dismiss(context);
            if (result.isError) {
              var exception = result.error;
              if (exception?.isCancel == false) {
                showToast(exception?.message ?? "请求失败,请稍后重试");
              }
              return;
            }
            var response = result.success;
            if (response?.code == 0 && response?.success == true) {
              if (response?.data?["bank_card_number"] != null) {
                Log.d(
                    "bankCardOcr success3 ${response?.data["bank_card_number"]}");
                showToast("识别成功");
                controller?.text = response?.data["bank_card_number"];
                onScanSuccess?.call(controller?.text);
              }
            }
          }).onError((e, s) {
            Log.e("upload image error", error: e, stackTrace: s);
            var request = RequestException.handleError(e);
            if (request.isCancel) {
              return;
            }
            if (!context.mounted) {
              return;
            }
            BrnLoadingDialog.dismiss(context);
            showToast(request.message ?? "图片识别失败,请稍后重试");
          });

          if (!context.mounted) {
            return;
          }
        }
      }).onError((e, s) {
        Log.e("upload image error", error: e, stackTrace: s);
        var request = RequestException.handleError(e);
        if (request.isCancel) {
          return;
        }
        if (!context.mounted) {
          return;
        }
        BrnLoadingDialog.dismiss(context);
        showToast(request.message ?? "图片上传失败,请稍后重试");
      });
    } else {
      showToast("未知文件");
    }
  }
}

//银行卡管理里用的
//先上传到微服务，然后调用 authHttp 的识别服务
Future _defaultPickerCallback(
    BuildContext context,
    FormFieldState<String> field,
    TextEditingController? controller,
    XFile? image,
    {ValueChanged<String?>? onScanSuccess}) async {
  if (image != null) {
    //这里需要考虑几种图片的格式
    String? mimeType = lookupMimeType(image.name);

    if (mimeType != null) {
      BrnLoadingDialog.show(context,
          content: "识别中...  ", barrierDismissible: false);
      var bytes = await image.readAsBytes();
      Log.d(" 图片压缩前大小 ${bytes.length / 1024} kb");
      //图片太大，需要对图片进行压缩,压缩后变成image/jpeg,
      //如果压缩失败则直接上上传未压缩的图片
      try {
        bytes = await FlutterImageCompress.compressWithList(bytes,
            minHeight: 720, minWidth: 720, format: CompressFormat.jpeg);
        //经过压缩之后图片格式已经转换成了 jpeg
        mimeType = "image/jpeg";
      } catch (e, s) {
        Log.e("图片压缩失败", error: e, stackTrace: s);
      }
      Log.d(
          " 图片压缩后大小: ${bytes.length / 1024} ,图片名称:${image.name} ,mime:$mimeType");
      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(bytes,
            filename: image.name,
            contentType:
                MediaType(mimeType!.split("/").first, mimeType.split("/").last))
      });
      //先上传图片到服务器
      //上传到 microfront
      var updateFileResult =
          await BDHResponsitory.uploadFileForResult(postData);
      if (!context.mounted) {
        return;
      }

      //图片上传失败
      if (updateFileResult.isError) {
        var exception = updateFileResult.error;

        //如果不是用户主动放弃则弹出提示
        if (exception?.isCancel == false) {
          BrnLoadingDialog.dismiss(context);
          showToast(exception?.message ?? "图片上传失败,请稍后重试");
        }
        return;
      }
      var uploadedFile = updateFileResult.success;

      //图片上传成功,但是返回地址为空
      if (uploadedFile?.url == null) {
        BrnLoadingDialog.dismiss(context);
        showToast("图片上传失败,请稍后重试[2]");
        return;
      }

      UserInfo? userInfo = StorageUtil.userInfo();
      var picUrl = "${urlConfig.microfront}${uploadedFile?.url}";
      Log.d(" 图片上传成功地址为: $picUrl");

      //图片识别调用
      var result = await BankService.common().bankCardOcr(
          picUrl: picUrl,
          accountId: userInfo?.data?.id,
          systemId: "systemlandcontract");
      if (!context.mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);

      if (result.isError) {
        var exception = result.error;
        if (exception?.isCancel == false) {
          showToast(exception?.message ?? "请求失败,请稍后重试");
        }
        return;
      }
      var response = result.success;
      if (response?.code == 0 && response?.success == true) {
        if (response?.data?["bank_card_number"] != null) {
          Log.d("bankCardOcr success3 ${response?.data["bank_card_number"]}");
          controller?.text = response?.data["bank_card_number"];
          onScanSuccess?.call(controller?.text);
        }
      }
    } else {
      showToast("未知文件");
    }
  }
}

class BdhOcrBankPicker extends BdhTextInputSmall {
  final ValueChanged<String?>? onScanSuccess;
  BdhOcrBankPicker(
      {super.key,
      required super.item,
      super.placeHolder,
      super.fontSize,
      required super.controller,
      super.initialValue,
      super.validator,
      super.onChange,
      super.focusNode,
      bool showScan = false,
      OnPickerCallback onPickerCallback = _defaultPickerCallback,
      this.onScanSuccess,
      super.textAlign,
      super.onSaved})
      : super(
            rightWidgetBuilder: showScan
                ? (context, field) {
                    pickImage(ImageSource type) async {
                      ImagePicker picker = ImagePicker();
                      var image = await picker.pickImage(
                          source: type, maxHeight: 720, maxWidth: 720);
                      if (!context.mounted) {
                        return;
                      }
                      await onPickerCallback.call(
                          context, field, controller, image,
                          onScanSuccess: onScanSuccess);
                    }

                    openImage(ImageSource type) async {
                      if (type == ImageSource.camera) {
                        PermissionUtil.requestCameraPermission(
                                field.context, "银行卡识别")
                            .then((res) {
                          if (res == true) {
                            pickImage(type);
                          }
                        });
                      }
                      if (type == ImageSource.gallery) {
                        PermissionUtil.requestPhotosPermission(
                                field.context, "银行卡识别")
                            .then((res) {
                          if (res == true) {
                            pickImage(type);
                          }
                        });
                      }
                    }

                    return GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                            backgroundColor: Colors.transparent,
                            useSafeArea: true,
                            context: field.context,
                            builder: (ctx) {
                              return CameraPhotoSelect(sourceSelect: (e) {
                                Navigator.of(field.context).pop();
                                focusNode?.unfocus();
                                openImage(e);
                              });
                            });
                      },
                      child: Padding(
                          padding: EdgeInsets.only(left: 15.px),
                          child: Icon(
                            Icons.photo_camera_outlined,
                            size: 24.px,
                          )),
                    );
                  }
                : null);
}
