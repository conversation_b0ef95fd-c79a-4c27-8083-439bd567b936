import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';

class BdhSingleDataPicker extends FormField<DictNode> {
  final FormItem item;
  final Function(DictNode)? onChange;

  final bool showBottomLine;
  final bool showArrow;
  final TextAlign textAlign;
  final double? valueSpace;
  final double? minHeight;
  final String? valueStart;
  final TextStyle? textStyle;
  final TextStyle? titleStyle;
  final double? titleWidth;
  final TextStyle? placeholderStyle;
  final String? placeholder;
  final bool autoValidate;
  final bool useCode;
  final bool checkState;
  final Widget Function(BuildContext, FormFieldState<DictNode>)? suffixBuilder;
  final bool Function()? canShowPicker;
  final bool enable;

  BdhSingleDataPicker(
      {super.key,
      super.onSaved,
      super.validator,
      super.initialValue,
      required this.item,
      this.showBottomLine = true,
      this.showArrow = true,
      this.autoValidate = false,
      this.textAlign = TextAlign.right,
      this.valueSpace,
      this.minHeight,
      this.valueStart,
      this.textStyle,
      this.titleStyle,
      this.placeholderStyle,
      this.placeholder,
      this.titleWidth,
      this.useCode = false,
      this.suffixBuilder,
      this.checkState = false,
      this.canShowPicker,
      this.enable = true,
      this.onChange})
      : super(builder: (field) {
          showBottomMultiSelectPicker(BuildContext context, data) {
            BrnMultiDataPicker(
              context: context,
              title: item.title ?? "",
              delegate: Brn1RowDelegate(firstSelectedIndex: 0, list: item.data),
              confirmClick: (list) {
                if ((item.data as List).isEmpty) {
                  return;
                }
                field.didChange((item.data as List)[list[0]]);
                if (onChange != null) {
                  onChange((item.data as List)[list[0]]);
                  if (autoValidate) field.validate();
                }
              },
            ).show();
          }

          Widget titleWidget = Text.rich(TextSpan(children: [
            TextSpan(
              text: item.isRequired == true ? "*" : "",
              style: TextStyle(
                  color: Colors.red,
                  fontSize: 16.px,
                  fontWeight: FontWeight.w500),
            ),
            TextSpan(
              text: item.title,
              style: titleStyle ??
                  TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
            )
          ]));

          if (titleWidth != null) {
            titleWidget = SizedBox(width: titleWidth, child: titleWidget);
          }

          Widget textWidget = field.value != null
              ? Text(
                  "${valueStart ?? ""}${useCode ? (field.value as DictNode).code : (field.value as DictNode).name ?? ""}",
                  textAlign: textAlign,
                  style: textStyle ??
                      TextStyle(
                          color: const Color.fromRGBO(22, 183, 96, 1),
                          fontSize: 16.px,
                          fontWeight: FontWeight.w600),
                )
              : Text(
                  placeholder ?? "请选择${item.title}",
                  textAlign: textAlign,
                  style: placeholderStyle ??
                      TextStyle(
                          color: const Color.fromRGBO(131, 149, 142, 1),
                          fontSize: 14.px,
                          fontWeight: FontWeight.w400),
                );

          textWidget = Expanded(child: textWidget);

          return GestureDetector(
            onTap: () {
              if (canShowPicker?.call() ?? enable) {
                showBottomMultiSelectPicker(field.context, item);
              }
            },
            child: Container(
              decoration: showBottomLine
                  ? BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1.px,
                              color: const Color.fromRGBO(226, 235, 231, 0.6))))
                  : null,
              constraints: BoxConstraints(minHeight: minHeight ?? 52.px),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      titleWidget,
                      Expanded(
                          child: Row(
                        children: [
                          SizedBox(
                            width: valueSpace ?? 10.px,
                          ),
                          // if (textAlign == TextAlign.right) const Spacer(),
                          textWidget,
                          // if (textAlign == TextAlign.left) const Spacer(),
                          if (showArrow) ...[
                            SizedBox(
                              width: 10.px,
                            ),
                            Image.asset(
                                width: 6.9.px,
                                height: 11.07.px,
                                ImageHelper.wrapAssets("arrow_right_black.png"))
                          ],
                          if (suffixBuilder != null) ...[
                            SizedBox(
                              width: 10.px,
                            ),
                            suffixBuilder.call(field.context, field)
                          ]
                        ],
                      ))
                    ],
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });

  @override
  FormFieldState<DictNode> createState() => _BdhSingleDataPickerState();
}

class _BdhSingleDataPickerState extends FormFieldState<DictNode> {
  @override
  BdhSingleDataPicker get widget => super.widget as BdhSingleDataPicker;

  @override
  void didUpdateWidget(covariant BdhSingleDataPicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}

class Brn1RowDelegate implements BrnMultiDataPickerDelegate {
  int firstSelectedIndex = 0;
  int secondSelectedIndex = 0;
  int thirdSelectedIndex = 0;
  List<DictNode> list = [];

  Brn1RowDelegate(
      {this.firstSelectedIndex = 0,
      this.secondSelectedIndex = 0,
      required this.list});

  @override
  int numberOfComponent() {
    return 1;
  }

  @override
  int numberOfRowsInComponent(int component) {
    return list.length;
  }

  @override
  String titleForRowInComponent(int component, int index) {
    return list[index].name ?? "";
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  selectRowInComponent(int component, int row) {
    if (0 == component) {
      firstSelectedIndex = row;
    }
  }

  @override
  int initSelectedRowForComponent(int component) {
    if (0 == component) {
      return firstSelectedIndex;
    }
    return 0;
  }
}
