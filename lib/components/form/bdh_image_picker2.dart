import 'dart:io';

import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:permission_handler/permission_handler.dart';

import 'bdh_image_picker.dart';

typedef OnChange = void Function(List<String>? fileList);

typedef OnPickerCallback = Future Function(
    FormFieldState<List<String>> field, OnChange? onChange, XFile? file);


//默认是上传到微服务
//BDHResponsitory.uploadFileForResult
Future _defaultPickerCallback(FormFieldState<List<String>> field,
    OnChange? onChange, XFile? image) async {
  if (image != null) {
    //这里需要考虑几种图片的格式
    String? mimeType = lookupMimeType(image.name);

    if (mimeType != null) {
      // BrnLoadingDialog.show(field.context,
      //     content: "上传中...  ", barrierDismissible: false);
      var bytes = await image.readAsBytes();
      Log.d(" 图片大小 ${bytes.length / 1024} kb");

      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(bytes,
            filename: image.name,
            contentType:
            MediaType(mimeType
                .split("/")
                .first, mimeType
                .split("/")
                .last))
      });
      //先上传图片到服务器
      var updateFileResult =
      await BDHResponsitory.uploadFileForResult2(postData);
      if (!field.context.mounted) {
        return;
      }
      // BrnLoadingDialog.dismiss(field.context);
      if (updateFileResult.success != null) {
        field.value!.add(updateFileResult.data!);
        field.didChange(field.value);
        onChange?.call(field.value!);
      }
    }}
}

class BdhImagePicker2 extends FormField<List<String>> {
  final FormItem item;
  final bool? showTitle;
  final int? maxCount;
  final double? fontSize;
  final String permissionDesc;
  BdhImagePicker2(
      {super.key,
      super.initialValue,
      super.validator,
      super.onSaved,
      required this.item,
      this.showTitle,
      this.maxCount,
      this.fontSize,
      this.permissionDesc = "头像",
      OnPickerCallback onPickerCallback = _defaultPickerCallback,
      OnChange? onChange})
      : super(builder: (field) {
          pickImage(ImageSource type) async {
            ImagePicker picker = ImagePicker();
            var image = await picker.pickImage(
                source: type, maxHeight: 720, maxWidth: 720);
            if (!field.context.mounted) {
              return;
            }
            await onPickerCallback(field, onChange, image);
          }

          openImage(ImageSource type) async {
            if (type == ImageSource.camera) {
              PermissionUtil.requestCameraPermission(field.context, permissionDesc)
                  .then((res) {
                if (res == true) {
                  pickImage(type);
                }
              });
            }
            if (type == ImageSource.gallery) {
              PermissionUtil.requestPhotosPermission(field.context, permissionDesc)
                  .then((res) async {
                if (res == true) {
                  pickImage(type);
                } else {
                  if (Platform.isIOS) {
                    return BrnDialogManager.showConfirmDialog(field.context,
                        title: "提示",
                        cancel: '取消',
                        confirm: '确定',
                        message: "需要您开启相册权限, 是否去开启相册权限？", onConfirm: () {
                      Navigator.of(field.context).pop();
                      openAppSettings();
                    }, onCancel: () {
                      Navigator.of(field.context).pop();
                    });
                  }
                }
              });
            }
          }

          return Container(
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        width: 1.px,
                        color: (showTitle ?? false)
                            ? const Color.fromRGBO(226, 235, 231, 0.6)
                            : Colors.transparent))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Visibility(
                    visible: showTitle ?? false,
                    child: Container(
                      margin: EdgeInsets.only(top: 10.px),
                      child: Text.rich(TextSpan(children: [
                        TextSpan(
                          text: (item.isRequired ?? false) ? "*" : "",
                          style: TextStyle(
                              color: Colors.red,
                              fontSize: fontSize ?? 16.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: item.title,
                          style: TextStyle(
                              fontSize: fontSize ?? 16.px,
                              fontWeight: FontWeight.w500),
                        )
                      ])),
                    )),
                SizedBox(
                  height: 5.px,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    ...(field.value ?? []).map((e) {
                      return SizedBox(
                        width: 68.px,
                        height: 68.px,
                        child: Stack(
                          children: [
                            Positioned(
                                bottom: 0,
                                left: 0,
                                child: Container(
                                  width: 60.px,
                                  height: 60.px,
                                  decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(4.px))),
                                  child: ClipRRect(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(4.px)),
                                    child: Image.network(
                                      width: 60.px,
                                      height: 60.px,
                                      ImageHelper.networkUrl(e),
                                      fit: BoxFit.fitWidth,
                                    ),
                                  ),
                                )),
                            Positioned(
                                top: 0,
                                right: 0,
                                child: GestureDetector(
                                  onTap: () {
                                    field.value!.remove(e);
                                    field.didChange(field.value);
                                    onChange?.call(field.value);
                                  },
                                  child: Image.asset(
                                      width: 16.px,
                                      height: 16.px,
                                      ImageHelper.wrapAssets("delete.png")),
                                ))
                          ],
                        ),
                      );
                    }),
                    Visibility(
                        visible: (field.value ?? []).length < (maxCount ?? 4),
                        child: GestureDetector(
                          onTap: () {
                            showModalBottomSheet(
                                backgroundColor: Colors.transparent,
                                useSafeArea: true,
                                context: field.context,
                                builder: (ctx) {
                                  return CameraPhotoSelect(sourceSelect: (e) {
                                    Navigator.of(field.context).pop();
                                    openImage(e);
                                  });
                                });
                          },
                          child: Container(
                            alignment: Alignment.center,
                            width: 60.px,
                            height: 60.px,
                            decoration: BoxDecoration(
                                color: const Color.fromRGBO(244, 245, 245, 1),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(4.px))),
                            child: Image.asset(
                                width: 32.px,
                                height: 32.px,
                                ImageHelper.wrapAssets("camera.png")),
                          ),
                        ))
                  ],
                ),
                SizedBox(
                  height: 10.px,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "     ${(field.value ?? []).length}/${maxCount ?? 4}",
                      style:
                          const TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
                    ),
                    const Text(
                      "单张照片最大不超过10M",
                      style: TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
                    )
                  ],
                ),
                SizedBox(
                  height: 15.px,
                ),
                Visibility(
                    visible: field.errorText != null,
                    child: Text(
                      field.errorText ?? "",
                      style: const TextStyle(color: Colors.red),
                    ))
              ],
            ),
          );
        });
}


class CameraPhotoSelect extends StatelessWidget {
  final Function(ImageSource) sourceSelect;
  const CameraPhotoSelect({super.key, required this.sourceSelect});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(9.px), topRight: Radius.circular(9.px))),
      height: 144.px + MediaQuery.of(context).padding.bottom,
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              sourceSelect(ImageSource.camera);
            },
            child: Container(
              height: 44.px,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: const Text(
                "拍照",
                style: TextStyle(
                    color: Color.fromRGBO(23, 151, 100, 1),
                    fontWeight: FontWeight.w500),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              sourceSelect(ImageSource.gallery);
            },
            child: Container(
              color: Colors.transparent,
              height: 44.px,
              alignment: Alignment.center,
              child: const Text("从相册选择",
                  style: TextStyle(
                      color: Color.fromRGBO(23, 151, 100, 1),
                      fontWeight: FontWeight.w500)),
            ),
          ),
          Container(
            width: 345.px,
            height: 1.px,
            color: const Color.fromRGBO(226, 235, 231, 0.6),
          ),
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              height: 55.px,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: const Text(
                "取消",
                style: TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
              ),
            ),
          )
        ],
      ),
    );
  }
}
