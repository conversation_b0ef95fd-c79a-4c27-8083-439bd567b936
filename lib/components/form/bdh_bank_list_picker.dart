import 'package:bdh_smart_agric_app/components/form/bdh_bottom_sheet_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/widgets.dart';

//从银行列表中选择固定银行
class BdhBankListPicker extends BdhBottomSheetPicker {
  BdhBankListPicker(
      {super.key,
      required super.item,
      super.placeHolder,
      super.fontSize,
      super.initialValue,
      String? cardNumber,
      bool showScan = true,
      super.onChange,
      super.validator,
      Function(FormFieldState<DictNode> field)? onScan,
      super.textAlign,
      super.onSaved})
      : super(
            rightWidgetBuilder: showScan
                ? (context, field) {
                    Widget child = Padding(
                        padding: EdgeInsets.only(left: 15.px),
                        child: Text.rich(TextSpan(
                          text: "获取",
                          style: TextStyle(
                              color: const Color.fromRGBO(10, 174, 108, 1),
                              fontSize: 14.px,
                              fontWeight: FontWeight.w600),
                        )));

                    return GestureDetector(
                      onTap: () {
                        onScan?.call(field);
                      },
                      child: child,
                    );
                  }
                : null);
}
