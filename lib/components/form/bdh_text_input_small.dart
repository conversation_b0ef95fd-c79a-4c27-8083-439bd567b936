import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

typedef FormFieldWidgetBuilder = Widget Function(
    BuildContext context, FormFieldState<String> field);

class BdhTextInputSmall extends FormField<String> {
  final TextEditingController? controller;

  final ValueChanged<String?>? onChange;
  BdhTextInputSmall(
      {super.key,
      required FormItem item,
      String? placeHolder,
      double fontSize = 20,
      FormFieldWidgetBuilder? rightWidgetBuilder,
      FormFieldWidgetBuilder? suffixBuilder,
      TextInputType? textInputType,
      this.onChange,
      FocusNode? focusNode,
      bool obscureText = false,
      this.controller,
      super.initialValue,
      super.validator,
      TextAlign textAlign = TextAlign.start,
      super.autovalidateMode = AutovalidateMode.disabled,
      TextStyle? titleStyle,
      TextStyle? textStyle,
      TextStyle? placeholderStyle,
      double? titleWidth,
      double? minHeight,
      double? valueSpace,
      bool showBottomLine = true,
      int? maxLength,
      String valueStart = "",
      OverlayVisibilityMode? clearButtonBode,
      super.onSaved})
      : super(builder: (field) {
          field as _BdhTextInputSmallState;
          return _BdhTextInputSmallWidget(
            model: initialValue,
            item: item,
            field: field,
            fontSize: fontSize,
            rightWidgetBuilder: rightWidgetBuilder,
            textInputType: textInputType,
            controller: controller,
            placeHolder: placeHolder,
            obscureText: obscureText,
            focusNode: focusNode,
            textAlign: textAlign,
            titleStyle: titleStyle,
            textStyle: textStyle,
            minHeight: minHeight,
            valueSpace: valueSpace,
            showBottomLine: showBottomLine,
            suffixBuilder: suffixBuilder,
            titleWidth: titleWidth,
            placeholderStyle: placeholderStyle,
            clearButtonBode: clearButtonBode,
            maxLength: maxLength,
            onChange: (value) {
              field.didChange(value);
              onChange?.call(value);
            },
          );
        });

  @override
  FormFieldState<String> createState() => _BdhTextInputSmallState();
}

class _BdhTextInputSmallState extends FormFieldState<String> {
  TextEditingController? _controller;
  TextEditingController? get _effectiveController =>
      widget.controller ?? _controller;
  @override
  BdhTextInputSmall get widget => super.widget as BdhTextInputSmall;
  @override
  void initState() {
    super.initState();
    if (widget.controller == null) {
      _controller = TextEditingController(text: widget.initialValue ?? "");
    }
    _effectiveController?.addListener(_handleControllerChanged);
  }

  @override
  void dispose() {
    super.dispose();
    _effectiveController?.removeListener(_handleControllerChanged);
    _controller?.dispose();
  }

  _handleControllerChanged() {
    didChange(_effectiveController?.text);
    widget.onChange?.call(_effectiveController?.text);
  }

  @override
  void didUpdateWidget(covariant BdhTextInputSmall oldWidget) {
    if (_effectiveController?.text != widget.initialValue) {
      _effectiveController?.text = widget.initialValue ?? "";
    }
    super.didUpdateWidget(oldWidget);
  }
}

class _BdhTextInputSmallWidget extends StatefulWidget {
  final String? model;
  final FormItem item;
  final String? placeHolder;
  final double fontSize;
  final Function(String) onChange;
  final FormFieldWidgetBuilder? rightWidgetBuilder;
  final FormFieldWidgetBuilder? suffixBuilder;

  final _BdhTextInputSmallState field;
  final TextEditingController? controller;

  final TextInputType? textInputType;

  final FocusNode? focusNode;

  final bool obscureText;

  final TextAlign textAlign;

  final TextStyle? titleStyle;
  final TextStyle? textStyle;
  final double? minHeight;
  final double? valueSpace;
  final bool showBottomLine;

  final TextStyle? placeholderStyle;
  final double? titleWidth;
  final OverlayVisibilityMode? clearButtonBode;
  final int? maxLength;

  const _BdhTextInputSmallWidget(
      {super.key,
      required this.model,
      required this.item,
      required this.fontSize,
      required this.field,
      this.placeHolder,
      this.rightWidgetBuilder,
      this.controller,
      this.textInputType,
      this.focusNode,
      required this.textAlign,
      required this.obscureText,
      required this.onChange,
      this.titleStyle,
      this.textStyle,
      this.minHeight,
      this.valueSpace,
      this.titleWidth,
      this.placeholderStyle,
      required this.showBottomLine,
      this.clearButtonBode,
      this.maxLength,
      this.suffixBuilder});

  @override
  State<StatefulWidget> createState() => __BdhTextInputSmallWidgetState();
}

class __BdhTextInputSmallWidgetState extends State<_BdhTextInputSmallWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Widget titleWidget = Text.rich(TextSpan(children: [
      if (widget.item.isRequired == true)
        TextSpan(
          text: "*",
          style: TextStyle(
              color: Colors.red,
              fontSize: widget.fontSize,
              fontWeight: FontWeight.w500),
        ),
      TextSpan(
        text: widget.item.title,
        style: widget.titleStyle ??
            TextStyle(
                fontSize: widget.fontSize,
                color: const Color.fromRGBO(44, 44, 52, 1),
                fontWeight: FontWeight.w500),
      )
    ]));

    if (widget.titleWidth != null) {
      titleWidget = SizedBox(width: widget.titleWidth, child: titleWidget);
    }

    Widget child = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (widget.item.title != null) titleWidget,
            SizedBox(
              width: widget.valueSpace ?? 10.px,
            ),
            Expanded(
                child: CupertinoTextField.borderless(
              focusNode: widget.focusNode,
              onChanged: widget.onChange,
              textAlign: widget.textAlign,
              maxLength: widget.maxLength,
              clearButtonMode:
                  widget.clearButtonBode ?? OverlayVisibilityMode.never,
              controller: widget.field._effectiveController,
              placeholder: widget.placeHolder ?? "请输入${widget.item.title}",
              keyboardType: widget.textInputType ?? TextInputType.text,
              padding: EdgeInsets.zero,
              obscureText: widget.obscureText,
              readOnly: !(widget.item.isCanEdit ?? true),
              suffix: widget.suffixBuilder?.call(context, widget.field),
              style: widget.textStyle ??
                  TextStyle(
                    fontSize: widget.fontSize,
                    fontWeight: FontWeight.w400,
                    color: const Color.fromRGBO(44, 44, 52, 1),
                  ),
              placeholderStyle: widget.placeholderStyle ??
                  TextStyle(
                      fontSize: widget.fontSize,
                      fontWeight: FontWeight.w400,
                      color: widget.field.errorText != null &&
                              (widget.field.errorText != null &&
                                  (widget.field.value == null ||
                                      widget.field.value!.isEmpty))
                          ? const Color(0xFFFFCDD2)
                          : const Color.fromRGBO(44, 44, 52, 0.35)),
            )),
          ],
        ),
        if (widget.field.errorText != null &&
            widget.field.errorText!.isNotEmpty) ...[
          Text(
            widget.field.errorText ?? "",
            style: const TextStyle(color: Colors.red),
          ),
          SizedBox(
            height: 5.px,
          )
        ]
      ],
    );

    return Container(
      decoration: widget.showBottomLine
          ? BoxDecoration(
              border: Border(
                  bottom: BorderSide(
                      width: 1.px,
                      color: const Color.fromRGBO(226, 235, 231, 0.6))))
          : null,
      constraints: BoxConstraints(minHeight: widget.minHeight ?? 52.px),
      child: Row(
        children: [
          Expanded(child: child),
          widget.rightWidgetBuilder == null
              ? const SizedBox.shrink()
              : widget.rightWidgetBuilder!(context, widget.field)
        ],
      ),
    );
  }
}
