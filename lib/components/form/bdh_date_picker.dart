import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class BdhDatePicker extends FormField<DateTime> {
  final FormItem item;

  final bool showBottomLine;
  final bool showArrow;
  final TextAlign textAlign;
  final double? valueSpace;
  final double? minHeight;
  final double? titleWidth;
  final String? valueStart;
  final TextStyle? textStyle;
  final TextStyle? titleStyle;
  final TextStyle? placeholderStyle;
  final String? placeholder;
  final bool autoValidate;
  final bool checkState;

  final FormFieldSetter<DateTime>? onChanged;

  final DatePickerEntryMode? datePickerEntryMode;

  final DateFormat? dateFormat;

  BdhDatePicker(
      {super.key,
      required this.item,
      super.initialValue,
      super.onSaved,
      super.validator,
      this.showBottomLine = true,
      this.showArrow = true,
      this.autoValidate = true,
      this.textAlign = TextAlign.right,
      this.valueSpace,
      this.minHeight,
      this.valueStart,
      this.textStyle,
      this.titleStyle,
      this.titleWidth,
      this.placeholderStyle,
      this.placeholder,
      this.datePickerEntryMode,
      this.dateFormat,
      this.onChanged,
      this.checkState = false,
      DateTime? minimumDate,
      DateTime? maximumDate,
      final bool Function()? canShowPicker})
      : super(builder: (field) {
          timeToStr(DateTime time) {
            var df = dateFormat ?? DateFormat("yyyy-MM-dd");
            return df.format(time);
          }

          Widget titleWidget = Text.rich(TextSpan(children: [
            TextSpan(
              text: item.isRequired == true ? "*" : "",
              style: TextStyle(
                  color: Colors.red,
                  fontSize: 16.px,
                  fontWeight: FontWeight.w500),
            ),
            TextSpan(
              text: item.title ?? "",
              style: titleStyle ??
                  TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
            )
          ]));

          if (titleWidth != null) {
            titleWidget = SizedBox(width: titleWidth, child: titleWidget);
          }

          return GestureDetector(
            onTap: () {
              if (canShowPicker?.call() ?? true) {
                showDatePicker(
                        context: field.context,
                        initialDate: initialValue,
                        initialEntryMode: datePickerEntryMode ??
                            DatePickerEntryMode.calendarOnly,
                        firstDate: minimumDate ?? DateTime.utc(2024),
                        lastDate: maximumDate ?? DateTime.utc(2030))
                    .then((time) {
                  if (time == null) {
                    return;
                  }
                  field.didChange(time);
                  onChanged?.call(time);
                  if (autoValidate) field.validate();
                });
              }
            },
            child: Container(
              decoration: showBottomLine
                  ? BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1.px,
                              color: const Color.fromRGBO(226, 235, 231, 0.6))))
                  : null,
              constraints: BoxConstraints(minHeight: minHeight ?? 44.px),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      titleWidget,
                      Expanded(
                          child: Row(
                        children: [
                          SizedBox(
                            width: valueSpace ?? 10.px,
                          ),
                          if (textAlign == TextAlign.right) const Spacer(),
                          if (field.value == null)
                            Text(
                              placeholder ?? "请选择日期",
                              style: placeholderStyle ??
                                  TextStyle(
                                      color:
                                          const Color.fromRGBO(22, 183, 96, 1),
                                      fontSize: 16.px,
                                      fontWeight: FontWeight.w600),
                            ),
                          if (field.value != null)
                            Text(
                              timeToStr(field.value!),
                              style: textStyle ??
                                  TextStyle(
                                      color:
                                          const Color.fromRGBO(22, 183, 96, 1),
                                      fontSize: 16.px,
                                      fontWeight: FontWeight.w600),
                            ),
                          if (textAlign == TextAlign.left) const Spacer(),
                          if (showArrow) ...[
                            SizedBox(
                              width: 10.px,
                            ),
                            Image.asset(
                                width: 6.9.px,
                                height: 11.07.px,
                                ImageHelper.wrapAssets("arrow_right_black.png"))
                          ]
                        ],
                      ))
                    ],
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });

  @override
  FormFieldState<DateTime> createState() => _BdhDatePickerState();
}

class _BdhDatePickerState extends FormFieldState<DateTime> {
  @override
  BdhDatePicker get widget => super.widget as BdhDatePicker;

  @override
  void didUpdateWidget(covariant BdhDatePicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}
