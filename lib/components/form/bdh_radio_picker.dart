import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class BdhRadioPicker extends FormField<RadioItem> {
  final FormItem item;
  BdhRadioPicker(
      {super.key,
      super.onSaved,
      super.validator,
      super.initialValue,
      required this.item})
      : super(builder: (field) {
          List<RadioItem> items = item.data;
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("请选择具体问题",
                  style:
                      TextStyle(fontSize: 16.px, fontWeight: FontWeight.bold)),
              SizedBox(
                height: 10.px,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: items.map((e) {
                  return GestureDetector(
                    onTap: () {
                      for (var d in items) {
                        if (d.name == e.name) {
                          d.isSelect = true;
                        } else {
                          d.isSelect = false;
                        }
                      }
                      field.didChange(e);
                    },
                    child: e.isSelect
                        ? Container(
                            width: 108.33.px,
                            height: 60.px,
                            decoration: BoxDecoration(
                                color: const Color.fromRGBO(182, 244, 207, 1),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(4.px)),
                                border: Border.all(
                                    width: 1.5.px,
                                    color: const Color.fromRGBO(
                                        141, 238, 191, 1))),
                            child: Stack(
                              children: [
                                Center(
                                  child: Text(
                                    e.name,
                                    style: const TextStyle(
                                        color: Color.fromRGBO(22, 183, 96, 1)),
                                  ),
                                ),
                                Positioned(
                                    right: 0,
                                    bottom: 0,
                                    child: Container(
                                      width: 20.px,
                                      height: 20.px,
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          color: const Color.fromRGBO(
                                              22, 183, 96, 1),
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(10.px),
                                              bottomRight:
                                                  Radius.circular(4.px))),
                                      child: Image.asset(
                                          width: 10.px,
                                          ImageHelper.wrapAssets("select.png")),
                                    ))
                              ],
                            ),
                          )
                        : Container(
                            width: 108.33.px,
                            height: 60.px,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(4.px)),
                                border: Border.all(
                                    width: 1.5.px,
                                    color: const Color.fromRGBO(
                                        226, 235, 231, 1))),
                            child: Text(
                              e.name,
                              style: const TextStyle(
                                  color: Color.fromRGBO(131, 149, 142, 1)),
                            ),
                          ),
                  );
                }).toList(),
              ),
            ],
          );
        });
}

class RadioItem {
  String name;
  String code;
  bool isSelect;
  RadioItem(this.name, this.code, this.isSelect);
}
