import 'package:bdh_smart_agric_app/components/jh_cascade_tree_picker.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class BdhOrgPicker extends FormField<OrgTreeItem> {
  final FormItem item;
  final Function(OrgTreeItem)? onChange;

  final bool showBottomLine;
  final bool showArrow;
  final TextAlign textAlign;
  final double? valueSpace;
  final double? minHeight;
  final String? valueStart;
  final TextStyle? textStyle;
  final TextStyle? titleStyle;
  final double? titleWidth;
  final TextStyle? placeholderStyle;
  final String? placeholder;
  final bool autoValidate;
  final bool useCode;
  final bool checkState;
  final Function(BuildContext, FormFieldState<OrgTreeItem>)? suffixBuilder;

  BdhOrgPicker(
      {super.key,
      super.onSaved,
      super.validator,
      super.initialValue,
      required this.item,
      this.showBottomLine = true,
      this.showArrow = true,
      this.autoValidate = false,
      this.textAlign = TextAlign.right,
      this.valueSpace,
      this.minHeight,
      this.valueStart,
      this.textStyle,
      this.titleStyle,
      this.placeholderStyle,
      this.placeholder,
      this.titleWidth,
      this.useCode = false,
      this.suffixBuilder,
      this.checkState = false,
      bool showFullName = false,
      this.onChange})
      : super(builder: (field) {
          showBottomMultiSelectPicker(BuildContext context, data) {
            var tempData = [];
            for (var e in (item.data as List<OrgTreeItem>)) {
              tempData.add(e.toJson());
            }
            JhCascadeTreePicker.show(field.context,
                data: tempData,
                valueKey: "orgCode",
                labelKey: "orgName",
                childrenKey: "list",
                clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
              var item = OrgTreeItem.fromJson(res);
              field.didChange(item);

              if (autoValidate) {
                if (field.validate()) {
                  onChange?.call(item);
                }
              } else {
                onChange?.call(item);
              }
            });
          }

          Widget titleWidget = Text.rich(TextSpan(children: [
            TextSpan(
              text: item.isRequired == true ? "*" : "",
              style: TextStyle(
                  color: Colors.red,
                  fontSize: 16.px,
                  fontWeight: FontWeight.w500),
            ),
            TextSpan(
              text: item.title,
              style: titleStyle ??
                  TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
            )
          ]));

          if (titleWidth != null) {
            titleWidget = SizedBox(width: titleWidth, child: titleWidget);
          }

          Widget textWidget = field.value != null
              ? Text(
                  showFullName
                      ? field.value?.orgFullName ?? placeholder ?? ""
                      : field.value?.orgName ?? placeholder ?? "",
                  textAlign: textAlign,
                  style: textStyle ??
                      TextStyle(
                          color: const Color.fromRGBO(22, 183, 96, 1),
                          fontSize: 16.px,
                          fontWeight: FontWeight.w600),
                )
              : Text(
                  placeholder ?? "请选择${item.title}",
                  textAlign: textAlign,
                  style: placeholderStyle ??
                      TextStyle(
                          color: const Color.fromRGBO(131, 149, 142, 1),
                          fontSize: 14.px,
                          fontWeight: FontWeight.w400),
                );

          textWidget = Expanded(child: textWidget);

          return GestureDetector(
            onTap: () {
              showBottomMultiSelectPicker(field.context, item);
            },
            child: Container(
              decoration: showBottomLine
                  ? BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1.px,
                              color: const Color.fromRGBO(226, 235, 231, 0.6))))
                  : null,
              constraints: BoxConstraints(minHeight: minHeight ?? 52.px),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      titleWidget,
                      Expanded(
                          child: Row(
                        children: [
                          SizedBox(
                            width: valueSpace ?? 10.px,
                          ),
                          // if (textAlign == TextAlign.right) const Spacer(),
                          textWidget,
                          // if (textAlign == TextAlign.left) const Spacer(),
                          if (showArrow) ...[
                            SizedBox(
                              width: 10.px,
                            ),
                            Image.asset(
                                width: 6.9.px,
                                height: 11.07.px,
                                ImageHelper.wrapAssets("arrow_right_black.png"))
                          ],
                          if (suffixBuilder != null) ...[
                            SizedBox(
                              width: 10.px,
                            ),
                            suffixBuilder.call(field.context, field)
                          ]
                        ],
                      ))
                    ],
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });

  @override
  FormFieldState<OrgTreeItem> createState() => _BdhOrgPickerState();
}

class _BdhOrgPickerState extends FormFieldState<OrgTreeItem> {
  @override
  BdhOrgPicker get widget => super.widget as BdhOrgPicker;

  @override
  void didUpdateWidget(covariant BdhOrgPicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}

class BdhDropdownOrgPicker extends FormField<OrgTreeItem> {
  final FormItem item;

  final bool showBottomLine;
  final double? minHeight;
  final TextStyle? textStyle;
  final TextStyle? titleStyle;
  final double? titleWidth;
  final TextStyle? placeholderStyle;
  final String? placeholder;
  final bool autoValidate;
  final double? valueSpace;
  final bool showFullName;
  final bool checkState;

  BdhDropdownOrgPicker({
    super.key,
    super.onSaved,
    super.validator,
    super.initialValue,
    FormFieldSetter? onChange,
    required this.item,
    this.showBottomLine = true,
    this.autoValidate = false,
    this.minHeight,
    this.textStyle,
    this.titleStyle,
    this.titleWidth,
    this.valueSpace,
    this.placeholderStyle,
    this.placeholder,
    double? maxHeight,
    double? maxWidth,
    this.showFullName = false,
    this.checkState = false,
  }) : super(builder: (field) {
          showBottomMultiSelectPicker(BuildContext context, data) {
            var tempData = [];
            for (var e in (item.data as List<OrgTreeItem>)) {
              tempData.add(e.toJson());
            }
            JhCascadeTreePicker.show(field.context,
                data: tempData,
                valueKey: "orgCode",
                labelKey: "orgName",
                childrenKey: "list",
                clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
              var item = OrgTreeItem.fromJson(res);
              field.didChange(item);

              if (autoValidate) {
                if (field.validate()) {
                  onChange?.call(item);
                }
              } else {
                onChange?.call(item);
              }
            });
          }

          return GestureDetector(
            onTap: () {
              showBottomMultiSelectPicker(field.context, item.data);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                    child: Text(
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        showFullName
                            ? field.value?.orgFullName ?? placeholder ?? ""
                            : field.value?.orgName ?? placeholder ?? "",
                        style: field.value == null
                            ? placeholderStyle
                            : textStyle)),
                Icon(Icons.arrow_drop_down,
                    color: const Color.fromRGBO(51, 51, 51, 0.4), size: 20.px)
              ],
            ),
          );
        });

  @override
  FormFieldState<OrgTreeItem> createState() => _BdhOrgDataPickerState();
}

class _BdhOrgDataPickerState extends FormFieldState<OrgTreeItem> {
  @override
  BdhDropdownOrgPicker get widget => super.widget as BdhDropdownOrgPicker;

  @override
  void didUpdateWidget(covariant BdhDropdownOrgPicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}
