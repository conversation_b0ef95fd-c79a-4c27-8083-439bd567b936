import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BdhSwitch extends FormField<bool> {
  final FormItem item;
  BdhSwitch({super.key, required this.item, super.onSaved})
      : super(builder: (field) {
          return Container(
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        width: 1.px,
                        color: const Color.fromRGBO(226, 235, 231, 0.6)))),
            constraints: BoxConstraints(minHeight: 44.px),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text.rich(TextSpan(children: [
                  TextSpan(
                    text: (item.isRequired ?? false) ? "*" : "",
                    style: TextStyle(
                        color: Colors.red,
                        fontSize: 16.px,
                        fontWeight: FontWeight.w500),
                  ),
                  TextSpan(
                    text: item.title,
                    style:
                        TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
                  )
                ])),
                Transform.scale(
                  scale: 0.8,
                  child: CupertinoSwitch(
                      value: field.value ?? false,
                      onChanged: (v) {
                        field.didChange(v);
                      }),
                )
              ],
            ),
          );
        });
}
