import 'dart:async';

import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

class BdhSms extends FormField<SmsItem> {
  final FormItem item;
    final bool isCanEdit;
  BdhSms({
    super.key,
    super.validator,
    super.onSaved,
    super.initialValue,
    required this.item,
    this.isCanEdit = true 
  }) : super(builder: (field) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SmsContainer(
                phoneNum: field.value?.phone,
                onChange: (v) {
                  field.didChange(v);
                },
                isCanEdit: isCanEdit, 
              ),
              Visibility(
                  visible: field.errorText != null,
                  child: Text(
                    field.errorText ?? "",
                    style: const TextStyle(color: Colors.red),
                  ))
            ],
          );
        });
}

class SmsContainer extends StatefulWidget {
  final String? phoneNum;
  final Function(SmsItem) onChange;
  final bool isCanEdit; 
  const SmsContainer({super.key, required this.onChange, this.phoneNum, this.isCanEdit = true });

  @override
  State<StatefulWidget> createState() => _SmsContainerState();
}

class _SmsContainerState extends State<SmsContainer> {
  int count = 60;
  String btnText = "获取验证码";
  bool isFirstClick = true;
  Timer? timer;

  TextEditingController phoneController = TextEditingController();
  TextEditingController smsController = TextEditingController();
  @override
  initState() {
    super.initState();
    phoneController.text = widget.phoneNum ?? "";
  }

  @override
  void dispose() {
    timer?.cancel();
    phoneController.dispose();
    smsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          decoration: BoxDecoration(
              border: Border(
                  bottom: BorderSide(
                      width: 1.px,
                      color: const Color.fromRGBO(226, 235, 231, 0.6)))),
          constraints: BoxConstraints(minHeight: 44.px),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text.rich(TextSpan(children: [
                if(widget.isCanEdit)
                TextSpan(
                  text: "*",
                  style: TextStyle(
                      color: Colors.red,
                      fontSize: 16.px,
                      fontWeight: FontWeight.w500),
                ),
                TextSpan(
                  text: "手机号码",
                  style:
                      TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
                )
              ])),
              Row(children: [
                SizedBox(
                  width: 120.px,
                  child: CupertinoTextField.borderless(
                    readOnly: !widget.isCanEdit,
                    onChanged: (v) {},
                    placeholder: "请输入手机号码",
                    controller: phoneController,
                    keyboardType: TextInputType.number,
                    padding: EdgeInsets.zero,
                    textAlign: TextAlign.end,
                    placeholderStyle: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(0, 0, 0, 0.4)),
                  ),
                ),
                SizedBox(
                  width: 10.px,
                ),
              ])
            ],
          ),
        ),
        Container(
          constraints: BoxConstraints(minHeight: 44.px),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text.rich(TextSpan(children: [
                TextSpan(
                  text: "*",
                  style: TextStyle(
                      color: Colors.red,
                      fontSize: 16.px,
                      fontWeight: FontWeight.w500),
                ),
                TextSpan(
                  text: "验证码",
                  style:
                      TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
                )
              ])),
              Row(children: [
                SizedBox(
                  width: 120.px,
                  child: CupertinoTextField.borderless(
                    controller: smsController,
                    onChanged: (v) {
                      widget.onChange(
                          SmsItem(phoneController.text, smsController.text));
                    },
                    placeholder: "请输入验证码",
                    keyboardType: TextInputType.number,
                    padding: EdgeInsets.zero,
                    textAlign: TextAlign.end,
                    placeholderStyle: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(0, 0, 0, 0.4)),
                  ),
                ),
                SizedBox(
                  width: 10.px,
                ),
                GestureDetector(
                  onTap: () {
                    getSmsCode();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 32.px,
                    padding: EdgeInsets.only(left: 5.px, right: 5.px),
                    decoration: BoxDecoration(
                        color: const Color.fromRGBO(22, 183, 96, 1),
                        borderRadius: BorderRadius.all(Radius.circular(2.px))),
                    child: Text(
                      btnText,
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.px,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
              ])
            ],
          ),
        ),
      ],
    );
  }

  getSmsCode() async {
    if (count < 60 && count > 0) {
      showToast("请等计时结束再试");
      return;
    }
    if (RegUtil.isPhoneNumber(phoneController.text)) {
      var result = await LandResponsitory.getFddSmsCode(
          {"phoneNo": phoneController.text, "template": ""});
      if (result.success ?? false) {
        showToast(result.msg ?? "");
        if (count < 60 && count > 0) {
          showToast("请等计时结束再试");
        } else {
          timer = Timer.periodic(const Duration(seconds: 1), (timer) {
            count--;
            if (count <= 0) {
              timer.cancel();
            }
            setState(() {
              if (count > 0) {
                btnText = "请$count秒后再试";
              } else {
                btnText = "重新发送";
                count = 60;
              }
            });
          });
        }
      } else {
        showToast(result.msg ?? "");
      }
    } else {
      showToast("请输入正确的手机号");
    }
  }
}

class SmsItem {
  String phone;
  String code;
  SmsItem(this.phone, this.code);
}
