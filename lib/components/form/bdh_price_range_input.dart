import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_segment_line.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BdhPriceRangeInput extends FormField<PriceModel> {
  final FormItem item;
  final String? placeHolder;
  final String? unit;
  final bool? showTitle;
  final TextEditingController? controller;
  final TextInputType? textInputType;
  BdhPriceRangeInput(
      {super.key,
      super.validator,
      super.onSaved,
      super.initialValue,
      required this.item,
      this.controller,
      this.placeHolder,
      this.textInputType,
      this.unit,
      this.showTitle})
      : super(builder: (field) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              RangeInput(
                model: field.value,
                onChange: (v) {
                  field.didChange(v);
                },
              ),
              Visibility(
                  visible: field.errorText != null,
                  child: Text(
                    field.errorText ?? "",
                    style: const TextStyle(color: Colors.red),
                  )),
              BdhSegmentLine(width: 345.px)
            ],
          );
        });
}

class PriceModel {
  num? min;
  num? max;
  PriceModel(this.min, this.max);
}

class RangeInput extends StatefulWidget {
  final PriceModel? model;
  final Function(PriceModel) onChange;
  const RangeInput({super.key, this.model, required this.onChange});

  @override
  State<StatefulWidget> createState() => RangeInputState();
}

class RangeInputState extends State<RangeInput> {
  final TextEditingController _lowTextEditingController =
      TextEditingController();
  final TextEditingController _highTextEditingController =
      TextEditingController();
  @override
  void initState() {
    super.initState();
    _lowTextEditingController.text = "${widget.model?.min ?? ""}";
    _highTextEditingController.text = "${widget.model?.max ?? ""}";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(minHeight: 44.px),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text.rich(TextSpan(children: [
                TextSpan(
                  text: "",
                  style: TextStyle(
                      color: Colors.red,
                      fontSize: 16.px,
                      fontWeight: FontWeight.w500),
                ),
                TextSpan(
                  text: "意向价格",
                  style:
                      TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
                )
              ])),
              Row(children: [
                SizedBox(
                  width: 80.px,
                  child: CupertinoTextField(
                    onChanged: (v) {
                      widget.onChange(PriceModel(
                          num.parse(_lowTextEditingController.text),
                          num.parse(_highTextEditingController.text)));
                    },
                    placeholder: "最小价格",
                    keyboardType: TextInputType.number,
                    controller: _lowTextEditingController,
                    padding: EdgeInsets.zero,
                    textAlign: TextAlign.center,
                    placeholderStyle: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(0, 0, 0, 0.4)),
                  ),
                ),
                Container(
                  width: 10.px,
                  height: 1.px,
                  color: Colors.grey,
                  margin: EdgeInsets.only(left: 5.px, right: 5.px),
                ),
                SizedBox(
                  width: 80.px,
                  child: CupertinoTextField(
                    onChanged: (v) {
                      widget.onChange(PriceModel(
                          num.parse(_lowTextEditingController.text),
                          num.parse(_highTextEditingController.text)));
                    },
                    placeholder: "最大价格",
                    keyboardType: TextInputType.number,
                    controller: _highTextEditingController,
                    padding: EdgeInsets.zero,
                    textAlign: TextAlign.center,
                    placeholderStyle: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(0, 0, 0, 0.4)),
                  ),
                ),
                SizedBox(
                  width: 5.px,
                ),
                const Text(
                  "元/斤",
                  style: TextStyle(fontWeight: FontWeight.w500),
                )
              ])
            ],
          ),
        ],
      ),
    );
  }
}
