import 'package:bdh_smart_agric_app/components/compass.dart';
import 'package:bdh_smart_agric_app/components/map_location_plugin.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/gps/gps_receiver.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/tile_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

class BdhMapPicker extends FormField<LatLng> {
  final FormItem item;
  final bool checkState;
  BdhMapPicker(
      {super.key,
      super.onSaved,
      super.validator,
      super.initialValue,
      TextStyle? titleStyle,
      TextStyle? placeholderStyle,
      TextStyle? textStyle,
      String? placeholder,
      double? minHeight,
      bool showBottomLine = true,
      this.checkState = false,
      Function(LatLng?)? onChange,
      bool autoValidate = false,
      required this.item})
      : super(builder: (field) {
          return GestureDetector(
            onTap: () {
              Navigator.of(field.context)
                  .push(CupertinoPageRoute(builder: (context) {
                return MapPage(
                  initLocate: field.value,
                  onSelect: (latlng) {
                    field.didChange(latlng);
                    if (autoValidate) {
                      field.validate();
                    }
                    onChange?.call(latlng);
                  },
                );
              }));
            },
            child: Container(
              decoration: showBottomLine
                  ? BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1.px,
                              color: const Color.fromRGBO(226, 235, 231, 0.6))))
                  : null,
              constraints: BoxConstraints(minHeight: minHeight ?? 52.px),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text.rich(TextSpan(children: [
                        TextSpan(
                          text: item.isRequired == true ? "*" : "",
                          style: TextStyle(
                              color: Colors.red,
                              fontSize: 16.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: item.title,
                          style: titleStyle ??
                              TextStyle(
                                  fontSize: 16.px, fontWeight: FontWeight.w500),
                        )
                      ])),
                      Row(
                        children: [
                          Text(
                            field.value == null
                                ? placeholder ?? "请点击地图选择坐标"
                                : "${field.value?.longitude.toStringAsFixed(6)},${field.value?.latitude.toStringAsFixed(6)}",
                            style: field.value == null
                                ? placeholderStyle ??
                                    TextStyle(
                                        color: const Color.fromRGBO(
                                            22, 183, 96, 1),
                                        fontSize: 16.px,
                                        fontWeight: FontWeight.w600)
                                : textStyle ??
                                    TextStyle(
                                        color: const Color.fromRGBO(
                                            22, 183, 96, 1),
                                        fontSize: 16.px,
                                        fontWeight: FontWeight.w600),
                          ),
                          SizedBox(
                            width: 10.px,
                          ),
                          Image.asset(
                              width: 6.9.px,
                              height: 11.07.px,
                              ImageHelper.wrapAssets("arrow_right_black.png"))
                        ],
                      )
                    ],
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });

  @override
  FormFieldState<LatLng> createState() => _BdhMapPickerState();
}

class _BdhMapPickerState extends FormFieldState<LatLng> {
  @override
  BdhMapPicker get widget => super.widget as BdhMapPicker;

  @override
  void didUpdateWidget(covariant BdhMapPicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}

class MapPage extends StatefulWidget {
  final LatLng? initLocate;
  final Function(LatLng?) onSelect;
  final String locationMessage;
  const MapPage(
      {super.key,
      required this.onSelect,
      this.initLocate,
      this.locationMessage = "需要您开启定位信息才能继续"});

  @override
  State<StatefulWidget> createState() => MapPageState();
}

class MapPageState extends State<MapPage> {
  var bounds = LatLngBounds(const LatLng(44, 121), const LatLng(52, 134));
  var flags = InteractiveFlag.all - InteractiveFlag.rotate;
  LatLng? currentLatLng;
  List<Marker>? locationMarkers;
  MapController mapController = MapController();

  bool showLoading = true;
  LatLng? location;
  @override
  void initState() {
    super.initState();
    if (widget.initLocate != null) {
      location = widget.initLocate;
      setState(() {
        showLoading = false;
      });
    } else {
      PermissionUtil.requestLocationPermission(context, widget.locationMessage)
          .then((result) {
        if (!mounted) {
          return;
        }
        if (result) {
          LocationResult? locationResult =
              GpsReceiver.getInstance().locationResult;
          //没有取得过任何定位数据需要等待
          if (locationResult == null) {
            bus.on("location", busLocation);
            bus.on("locationError", busLocationError);
          } else {
            location = LatLng((locationResult.latitude ?? 0).toDouble(),
                (locationResult.longitude ?? 0).toDouble());
            setState(() {
              showLoading = false;
            });
          }
        } else {
          Navigator.of(context).pop();
        }
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    bus.off("location", busLocation);
    bus.off("locationError", busLocationError);
  }

  void busLocation(e) {
    if (!mounted) {
      return;
    }
    LocationResult locationResult = e as LocationResult;
    location = LatLng((locationResult.latitude ?? 0).toDouble(),
        (locationResult.longitude ?? 0).toDouble());
    setState(() {
      showLoading = false;
    });
  }

  void busLocationError(e, {bool force = false}) {
    if (!mounted) {
      return;
    }
    Log.d("busLocationError : $e");
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("地图"),
        actions: [
          if (!showLoading)
            TextButton(
                onPressed: () {
                  widget.onSelect(currentLatLng);
                  Navigator.of(context).pop();
                },
                child: Text(
                  "确定",
                  style: TextStyle(
                      color: const Color.fromRGBO(22, 183, 96, 1),
                      fontSize: 16.px,
                      fontWeight: FontWeight.w600),
                ))
        ],
      ),
      body: showLoading
          ? _widgetLoading()
          : FlutterMap(
              mapController: mapController,
              options: MapOptions(
                  initialCenter: location!,
                  initialZoom: 18,
                  interactionOptions: InteractionOptions(flags: flags),
                  onMapEvent: (e) {
                    setState(() {
                      currentLatLng = e.camera.center;
                    });
                  },
                  onMapReady: () {
                    // Future.delayed(Duration(milliseconds: 500), () {
                    //   mapController.move(location!, 18);
                    // });
                  }),
              children: [
                  TileLayerUtil.tileLayer(TianDiTuType.bdh),
                  TileLayerUtil.tileLayer(TianDiTuType.cia),
                  MarkerLayer(
                      markers: locationMarkers ??
                          [
                            Marker(
                                width: 39.33.px,
                                height: 39.33.px,
                                point: location!,
                                child: const CompassView())
                          ]),
                  MapLocationButton(
                      xAlign: 0.9,
                      yAlign: 0.55,
                      autoLocate: true,
                      markerCallback: (marker) {
                        setState(() {
                          locationMarkers = [marker];
                        });
                      }),
                  Align(
                    alignment: const Alignment(-0.9, -0.95),
                    child: Container(
                      height: 30.px,
                      width: 187.px,
                      color: const Color.fromRGBO(0, 0, 0, 0.4),
                      padding: EdgeInsets.only(left: 10.px),
                      alignment: Alignment.centerLeft,
                      child: SelectableText(
                        "${currentLatLng?.longitude.toStringAsFixed(6)},${currentLatLng?.latitude.toStringAsFixed(6)}",
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                  Align(
                    child: Icon(
                      Icons.add,
                      size: 50,
                      color: HexColor("#2d55e5"),
                    ),
                  )
                ]),
    );
  }
}
