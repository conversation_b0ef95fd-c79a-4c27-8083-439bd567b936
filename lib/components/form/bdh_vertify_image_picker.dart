import 'package:bdh_smart_agric_app/components/bdh_network_image.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';

class BdhVertifyImagePicker extends FormField<BDHFile> {
  final FormItem item;
  final Function(BDHFile)? onUpload;

  BdhVertifyImagePicker(
      {super.key,
      super.initialValue,
      super.validator,
      super.onSaved,
      required this.item,
      this.onUpload})
      : super(builder: (field) {
          openImage(ImageSource type) {
            ImagePicker picker = ImagePicker();
            picker
                .pickImage(
              source: type,
              maxHeight: 720,
              maxWidth: 720,
            )
                .then((image) {
              Navigator.of(field.context).pop();
              //图片上传
              if (image != null) {
                image.readAsBytes().then((bytes) {
                  String? mimeType = lookupMimeType(image.name);
                  if (mimeType != null) {
                    FormData postData = FormData.fromMap({
                      "file": MultipartFile.fromBytes(bytes,
                          filename: image.name,
                          contentType: MediaType(mimeType.split("/").first,
                              mimeType.split("/").last))
                    });

                    LandResponsitory.fileUpload(postData).then((value) {
                      if (onUpload != null) {
                        onUpload(BDHFile(url: value.data));
                      }
                      field.didChange(BDHFile(url: value.data));
                    });
                  } else {
                    showToast("未知文件");
                  }
                });
              }
            });
          }

          return GestureDetector(
            onTap: () {
              showModalBottomSheet(
                  backgroundColor: Colors.transparent,
                  useSafeArea: true,
                  context: field.context,
                  builder: (ctx) {
                    return CameraPhotoSelect(sourceSelect: (e) {
                      openImage(e);
                    });
                  });
            },
            child: field.value == null
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            color: const Color.fromRGBO(246, 247, 250, 1),
                            borderRadius:
                                BorderRadius.all(Radius.circular(5.px))),
                        width: 97.px,
                        height: 97.px,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.add,
                              size: 33.px,
                              color: const Color.fromRGBO(125, 127, 131, 1),
                            ),
                            Text(
                              item.title ?? "",
                              style: TextStyle(
                                  fontSize: 12.px,
                                  fontWeight: FontWeight.w500,
                                  color: const Color.fromRGBO(51, 51, 51, 1)),
                            )
                          ],
                        ),
                      ),
                      Visibility(
                          visible: field.errorText != null,
                          child: Text(
                            field.errorText ?? "",
                            style: const TextStyle(color: Colors.red),
                          ))
                    ],
                  )
                : ClipRRect(
                    borderRadius: BorderRadius.all(Radius.circular(5.px)),
                    child: BdhNetworkImage(
                        width: 97.px, height: 97.px, url: field.value!.url!),
                  ),
          );
        });
}

class CameraPhotoSelect extends StatelessWidget {
  final Function(ImageSource) sourceSelect;
  const CameraPhotoSelect({super.key, required this.sourceSelect});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(9.px), topRight: Radius.circular(9.px))),
      height: 144.px + MediaQuery.of(context).padding.bottom,
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              sourceSelect(ImageSource.camera);
            },
            child: Container(
              height: 44.px,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: const Text(
                "拍照",
                style: TextStyle(
                    color: Color.fromRGBO(23, 151, 100, 1),
                    fontWeight: FontWeight.w500),
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              sourceSelect(ImageSource.gallery);
            },
            child: Container(
              color: Colors.transparent,
              height: 44.px,
              alignment: Alignment.center,
              child: const Text("从相册选择",
                  style: TextStyle(
                      color: Color.fromRGBO(23, 151, 100, 1),
                      fontWeight: FontWeight.w500)),
            ),
          ),
          Container(
            width: 345.px,
            height: 1.px,
            color: const Color.fromRGBO(226, 235, 231, 0.6),
          ),
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              height: 55.px,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: const Text(
                "取消",
                style: TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
              ),
            ),
          )
        ],
      ),
    );
  }
}
