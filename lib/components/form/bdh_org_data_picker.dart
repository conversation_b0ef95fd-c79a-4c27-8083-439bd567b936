import 'package:bdh_smart_agric_app/components/jh_cascade_tree_picker.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class BdhOrgDataPicker extends FormField {
  final FormItem item;

  final bool showBottomLine;
  final double? minHeight;
  final TextStyle? textStyle;
  final TextStyle? titleStyle;
  final double? titleWidth;
  final TextStyle? placeholderStyle;
  final String? placeholder;
  final bool autoValidate;
  final double? valueSpace;
  final bool showFullName;
  final bool checkState;

  BdhOrgDataPicker({
    super.key,
    super.onSaved,
    super.validator,
    super.initialValue,
    FormFieldSetter? onChange,
    required this.item,
    this.showBottomLine = true,
    this.autoValidate = false,
    this.minHeight,
    this.textStyle,
    this.titleStyle,
    this.titleWidth,
    this.valueSpace,
    this.placeholderStyle,
    this.placeholder,
    this.showFullName = false,
    this.checkState = false,
  }) : super(builder: (field) {
          showBottomMultiSelectPicker(BuildContext context, data) {
            var tempData = [];
            for (var e in (item.data as List<OrgTreeItem>)) {
              tempData.add(e.toJson());
            }
            JhCascadeTreePicker.show(field.context,
                data: tempData,
                valueKey: "orgCode",
                labelKey: "orgName",
                childrenKey: "list",
                clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
              field.didChange(ress);

              if (autoValidate) {
                if (field.validate()) {
                  onChange?.call(ress);
                }
              } else {
                onChange?.call(ress);
              }
            });
          }

          Widget titleWidget = Text.rich(TextSpan(children: [
            TextSpan(
              text: item.isRequired == true ? "*" : "",
              style: TextStyle(
                  color: Colors.red,
                  fontSize: 16.px,
                  fontWeight: FontWeight.w500),
            ),
            TextSpan(
              text: item.title,
              style: titleStyle ??
                  TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
            )
          ]));

          if (titleWidth != null) {
            titleWidget = SizedBox(width: titleWidth, child: titleWidget);
          }

          return GestureDetector(
            onTap: () {
              showBottomMultiSelectPicker(field.context, item);
            },
            child: Container(
                decoration: showBottomLine
                    ? BoxDecoration(
                        border: Border(
                            bottom: BorderSide(
                                width: 1.px,
                                color:
                                    const Color.fromRGBO(226, 235, 231, 0.6))))
                    : null,
                constraints: BoxConstraints(minHeight: minHeight ?? 52.px),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        titleWidget,
                        SizedBox(
                          width: valueSpace ?? 10.px,
                        ),
                        Expanded(
                            child: Row(
                          children: [
                            Expanded(
                              child: field.value != null
                                  ? Text(
                                      textAlign: TextAlign.right,
                                      (field.value! as List).map((e) {
                                        return showFullName
                                            ? e["orgFullName"]
                                            : e["orgName"];
                                      }).join("/"),
                                      style: textStyle ??
                                          TextStyle(
                                              color: const Color.fromRGBO(
                                                  22, 183, 96, 1),
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w600),
                                    )
                                  : Text(
                                      "请选择${item.title}",
                                      textAlign: TextAlign.end,
                                      style: placeholderStyle ??
                                          TextStyle(
                                              color: const Color.fromRGBO(
                                                  131, 149, 142, 1),
                                              fontSize: 14.px,
                                              fontWeight: FontWeight.w400),
                                    ),
                            ),
                            SizedBox(
                              width: 10.px,
                            ),
                            Image.asset(
                                width: 6.9.px,
                                height: 11.07.px,
                                ImageHelper.wrapAssets("arrow_right_black.png"))
                          ],
                        ))
                      ],
                    ),
                    Visibility(
                        visible: field.errorText != null,
                        child: Text(
                          field.errorText ?? "",
                          style: const TextStyle(color: Colors.red),
                        ))
                  ],
                )),
          );
        });

  @override
  FormFieldState createState() => _BdhOrgDataPickerState();
}

class _BdhOrgDataPickerState extends FormFieldState {
  @override
  BdhOrgDataPicker get widget => super.widget as BdhOrgDataPicker;

  @override
  void didUpdateWidget(covariant BdhOrgDataPicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}
