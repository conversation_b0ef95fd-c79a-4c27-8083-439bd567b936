import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_check.dart';
import 'package:bdh_smart_agric_app/pages/product/landcontract/bidding/widget/search_widget.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BdhSearchableSingleDataPicker extends FormField<DictNode> {
  final FormItem item;
  final Function(DictNode)? onChange;

  final bool showBottomLine;
  final bool showArrow;
  final TextAlign textAlign;
  final double? valueSpace;
  final double? minHeight;
  final String? valueStart;
  final TextStyle? textStyle;
  final TextStyle? titleStyle;
  final double? titleWidth;
  final TextStyle? placeholderStyle;
  final String? placeholder;
  final bool autoValidate;
  final bool useCode;
  final bool checkState;
  final Function(BuildContext, FormFieldState<DictNode>)? suffixBuilder;
  final bool Function()? canShowPicker;
  final Widget Function(BuildContext, int, DictNode, bool,
      void Function(DictNode item, bool checked))? pickerItemBuilder;
  BdhSearchableSingleDataPicker(
      {super.key,
      super.onSaved,
      super.validator,
      super.initialValue,
      required this.item,
      this.showBottomLine = true,
      this.showArrow = true,
      this.autoValidate = false,
      this.textAlign = TextAlign.right,
      this.valueSpace,
      this.minHeight,
      this.valueStart,
      this.textStyle,
      this.titleStyle,
      this.placeholderStyle,
      this.placeholder,
      this.titleWidth,
      this.useCode = false,
      this.suffixBuilder,
      this.checkState = false,
      this.canShowPicker,
      this.onChange,
      double? maxHeight,
      String? title,
      double? diameterRatio,
      double? squeeze,
      double? itemExtent,
      double? magnification,
      this.pickerItemBuilder})
      : super(builder: (field) {
          void showBottomMultiSelectPicker(
              BuildContext context, List<DictNode> data) {
            var cacheValue = data.isEmpty ? null : initialValue;
            showBottomSheetPicker<DictNode>(
              field.context,
              title: title,
              contentWidget: SearchablePicker(
                initialValue: cacheValue,
                diameterRatio: diameterRatio,
                squeeze: squeeze,
                itemExtent: itemExtent,
                magnification: magnification,
                onChange: (DictNode? value) {
                  cacheValue = value;
                },
                pickerItemBuilder: pickerItemBuilder,
                data: data,
              ),
              onConfirm: () {
                Navigator.maybePop(field.context, cacheValue);
              },
              onCancel: () {
                Navigator.maybePop(field.context);
              },
            ).then((result) {
              if (result == initialValue || result == null) {
                return;
              }
              field.didChange(result);
              onChange?.call(result);
            });
          }

          Widget titleWidget = Text.rich(TextSpan(children: [
            TextSpan(
              text: item.isRequired == true ? "*" : "",
              style: TextStyle(
                  color: Colors.red,
                  fontSize: 16.px,
                  fontWeight: FontWeight.w500),
            ),
            TextSpan(
              text: item.title,
              style: titleStyle ??
                  TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
            )
          ]));

          if (titleWidth != null) {
            titleWidget = SizedBox(width: titleWidth, child: titleWidget);
          }

          Widget textWidget = field.value != null
              ? Text(
                  "${valueStart ?? ""}${useCode ? (field.value as DictNode).code : (field.value as DictNode).name ?? ""}",
                  textAlign: textAlign,
                  style: textStyle ??
                      TextStyle(
                          color: const Color.fromRGBO(22, 183, 96, 1),
                          fontSize: 16.px,
                          fontWeight: FontWeight.w600),
                )
              : Text(
                  placeholder ?? "请选择${item.title}",
                  textAlign: textAlign,
                  style: placeholderStyle ??
                      TextStyle(
                          color: const Color.fromRGBO(131, 149, 142, 1),
                          fontSize: 14.px,
                          fontWeight: FontWeight.w400),
                );

          textWidget = Expanded(child: textWidget);

          return GestureDetector(
            onTap: () {
              if (canShowPicker?.call() ?? true) {
                showBottomMultiSelectPicker(field.context, item.data);
              }
            },
            child: Container(
              decoration: showBottomLine
                  ? BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1.px,
                              color: const Color.fromRGBO(226, 235, 231, 0.6))))
                  : null,
              constraints: BoxConstraints(minHeight: minHeight ?? 52.px),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      titleWidget,
                      Expanded(
                          child: Row(
                        children: [
                          SizedBox(
                            width: valueSpace ?? 10.px,
                          ),
                          // if (textAlign == TextAlign.right) const Spacer(),
                          textWidget,
                          // if (textAlign == TextAlign.left) const Spacer(),
                          if (showArrow) ...[
                            SizedBox(
                              width: 10.px,
                            ),
                            Image.asset(
                                width: 6.9.px,
                                height: 11.07.px,
                                ImageHelper.wrapAssets("arrow_right_black.png"))
                          ],
                          if (suffixBuilder != null) ...[
                            SizedBox(
                              width: 10.px,
                            ),
                            suffixBuilder.call(field.context, field)
                          ]
                        ],
                      ))
                    ],
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });

  @override
  FormFieldState<DictNode> createState() => _BdhDropDownDatePickerState();
}

class _BdhDropDownDatePickerState extends FormFieldState<DictNode> {
  @override
  BdhSearchableSingleDataPicker get widget =>
      super.widget as BdhSearchableSingleDataPicker;

  @override
  void didUpdateWidget(covariant BdhSearchableSingleDataPicker oldWidget) {
    if (value != widget.initialValue && widget.checkState) {
      setValue(widget.initialValue);
    }
    super.didUpdateWidget(oldWidget);
  }
}

class SearchablePicker extends StatefulWidget {
  final List<DictNode> data;
  final DictNode? initialValue;
  final ValueChanged<DictNode?> onChange;
  final Widget Function(BuildContext, int, DictNode, bool,
      void Function(DictNode item, bool checked))? pickerItemBuilder;
  final double? diameterRatio;
  final double? squeeze;
  final double? itemExtent;
  final double? magnification;
  const SearchablePicker(
      {super.key,
      required this.data,
      this.initialValue,
      required this.onChange,
      this.pickerItemBuilder,
      this.diameterRatio,
      this.squeeze,
      this.itemExtent,
      this.magnification});

  @override
  State<SearchablePicker> createState() => _SearchablePickerState();
}

class _SearchablePickerState extends State<SearchablePicker> {
  late List<DictNode> showData = widget.data;
  String? searchText;
  DictNode? checkedNode;
  @override
  void initState() {
    super.initState();
    checkedNode = widget.initialValue;
  }

  void onSearchTextChange(String? value) {
    setState(() {
      searchText = value;
      if (searchText == null || searchText!.isEmpty) {
        showData = widget.data;
      } else {
        showData = widget.data
            .where((test) => test.name?.contains(searchText!) ?? false)
            .toList();
      }
      if (showData.isNotEmpty) {
        widget.onChange(showData[0]);
      } else {
        widget.onChange(null);
      }
    });
  }

  bool isChecked(DictNode item) {
    return checkedNode == item;
  }

  void setChecked(DictNode item, bool checked) {
    setState(() {
      if (checked) {
        checkedNode = item;
      } else if (checkedNode == item) {
        checkedNode = null;
      }
    });
    widget.onChange(item);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
            padding: EdgeInsets.only(left: 7.px, right: 7.px, top: 7.px),
            child: SearchWidget(
              initialValue: searchText,
              backgroundColor: Colors.transparent,
              inputColor: const Color.fromRGBO(44, 44, 52, 0.05),
              placeholderStyle: TextStyle(
                fontSize: 14.px,
                color: const Color.fromRGBO(44, 44, 52, 0.4),
              ),
              textStyle: TextStyle(
                fontSize: 14.px,
                color: const Color.fromRGBO(44, 44, 52, 0.6),
              ),
              height: 30.px,
              margin: EdgeInsets.zero,
              placeholder: "搜索内容",
              onChanged: (String? value) {
                onSearchTextChange(value);
              },
              onSubmitted: (String? value) {
                onSearchTextChange(value);
              },
            )),
        SizedBox(
          height: 14.px,
        ),
        Expanded(
            child: ListView.separated(
          padding: EdgeInsets.zero,
          key: ValueKey(showData.hashCode),
          itemBuilder: (context, index) {
            var item = showData[index];
            bool checked = isChecked(item);
            return widget.pickerItemBuilder
                    ?.call(context, index, item, checked, setChecked) ??
                GestureDetector(
                  onTap: () {
                    setChecked(item, !checked);
                  },
                  child: Container(
                      margin: EdgeInsets.only(left: 7.px, right: 7.px),
                      padding: EdgeInsets.all(7.px),
                      decoration: BoxDecoration(
                          color: checked
                              ? const Color.fromRGBO(44, 44, 44, 0.05)
                              : Colors.transparent,
                          border: Border.all(
                              width: 1.px,
                              color: checked
                                  ? const Color.fromARGB(255, 94, 139, 245)
                                  : Colors.transparent),
                          borderRadius:
                              BorderRadius.all(Radius.circular(8.px))),
                      child: Row(
                        children: [
                          Expanded(
                              child: Text(
                            item.name ?? "",
                            style: TextStyle(
                              fontSize: 16.px,
                              color: checked
                                  ? const Color.fromARGB(255, 94, 139, 245)
                                  : const Color.fromRGBO(44, 44, 44, 0.8),
                            ),
                          )),
                          SizedBox(
                            width: 7.px,
                          ),
                          BdhRadio(
                            backgroundColor: Colors.transparent,
                            selectColor:
                                const Color.fromARGB(255, 94, 139, 245),
                            checked: checked,
                            iconSize: 20.px,
                            onCheckBoxChanged: (c) {
                              setChecked(item, c);
                            },
                          ),
                        ],
                      )),
                );
          },
          itemCount: showData.length,
          separatorBuilder: (BuildContext context, int index) {
            return SizedBox(
              height: 7.px,
            );
          },
        )),
      ],
    );
  }
}
