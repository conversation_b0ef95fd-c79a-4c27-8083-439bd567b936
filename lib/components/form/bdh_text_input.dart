import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BdhTextInput extends FormField<String> {
  final FormItem item;
  final String? placeHolder;
  final String? unit;
  final bool? showTitle;
  final double? inputLength;
  final TextEditingController? controller;
  final TextInputType? textInputType;
  final Function(String)? onChange;
  BdhTextInput(
      {super.key,
      super.validator,
      super.onSaved,
      super.initialValue,
      required this.item,
      this.controller,
      this.placeHolder,
      this.textInputType,
      this.inputLength,
      this.unit,
      this.onChange,
      this.showTitle,
      double? fontSize,
      TextAlign textAlign = TextAlign.end})
      : super(builder: (field) {
          field as BdhTextInputState;
          if (showTitle == false) {
            return CupertinoTextField.borderless(
              onChanged: (v) {
                field.didChange(v);
                if (onChange != null) {
                  onChange(v);
                }
              },
              controller: field._effectiveController,
              placeholder: placeHolder ?? "请输入${item.title}",
              maxLines: 6,
              keyboardType: textInputType ?? TextInputType.text,
              // inputFormatters: <TextInputFormatter>[
              //   FilteringTextInputFormatter.digitsOnly
              // ],
              padding: EdgeInsets.zero,
              textAlign: textAlign,
              readOnly: !(item.isCanEdit ?? true),
              // enabled: item.isCanEdit ?? true,
              placeholderStyle: TextStyle(
                  fontSize: fontSize ?? 16.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 0, 0, 0.4)),
            );
          }
          return Container(
            decoration: BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        width: 1.px,
                        color: const Color.fromRGBO(226, 235, 231, 0.6)))),
            constraints: BoxConstraints(minHeight: 44.px),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text.rich(TextSpan(children: [
                      TextSpan(
                        text: "*",
                        style: TextStyle(
                            color: (item.isRequired ?? false)
                                ? Colors.red
                                : Colors.transparent,
                            fontSize: fontSize ?? 16.px,
                            fontWeight: FontWeight.w500),
                      ),
                      TextSpan(
                        text: item.title,
                        style: TextStyle(
                            fontSize: fontSize ?? 16.px,
                            fontWeight: FontWeight.w500),
                      )
                    ])),
                    Row(children: [
                      SizedBox(
                        width: inputLength ?? 190.px,
                        child: CupertinoTextField.borderless(
                          readOnly: !(item.isCanEdit == null
                              ? true
                              : item.isCanEdit!),
                          //   enabled: item.isCanEdit ?? true,
                          onChanged: (v) {
                            field.didChange(v);
                            if (onChange != null) {
                              onChange(v);
                            }
                          },
                          controller: field._effectiveController,
                          placeholder: placeHolder ?? "请输入${item.title}",
                          keyboardType: textInputType ?? TextInputType.text,
                          // inputFormatters: <TextInputFormatter>[
                          //   FilteringTextInputFormatter.digitsOnly
                          // ],
                          padding: EdgeInsets.zero,
                          textAlign: textAlign,
                          placeholderStyle: TextStyle(
                              fontSize: fontSize,
                              fontWeight: FontWeight.w400,
                              color: field.errorText != null &&
                                      (field.errorText != null &&
                                          (field.value == null ||
                                              field.value!.isEmpty))
                                  ? const Color(0xFFFFCDD2)
                                  : const Color.fromRGBO(44, 44, 52, 0.35)),
                        ),
                      ),
                      SizedBox(
                        width: 10.px,
                      ),
                      Text(
                        unit ?? "",
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      )
                    ])
                  ],
                ),
                Visibility(
                    visible:
                        field.errorText != null && field.errorText!.isNotEmpty,
                    child: Text(
                      field.errorText ?? "",
                      style: const TextStyle(color: Colors.red),
                    ))
              ],
            ),
          );
        });
  @override
  FormFieldState<String> createState() => BdhTextInputState();
}

class BdhTextInputState extends FormFieldState<String> {
  TextEditingController? _controller;
  TextEditingController? get _effectiveController =>
      widget.controller ?? _controller;
  @override
  BdhTextInput get widget => super.widget as BdhTextInput;
  @override
  void initState() {
    super.initState();
    if (widget.controller == null) {
      _controller = TextEditingController(text: widget.initialValue ?? "");
    }
    _effectiveController?.addListener(_handleControllerChanged);
  }

  @override
  void dispose() {
    super.dispose();
    _effectiveController?.removeListener(_handleControllerChanged);
    _controller?.dispose();
  }

  _handleControllerChanged() {
    didChange(_effectiveController?.text);
  }
}
