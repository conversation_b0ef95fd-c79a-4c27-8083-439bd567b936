# BdhMediaPicker 使用说明

## 概述
BdhMediaPicker 是一个多媒体文件选择器组件，支持图片和视频的选择、预览、上传功能。

## 新增参数: autoUpload

### 参数说明
- **autoUpload**: `bool` 类型，默认值为 `true`
  - `true`: 选择文件后自动上传到服务器，返回网络URL
  - `false`: 选择文件后只回显，不上传，返回本地文件路径

### 使用示例

#### 1. 自动上传模式（默认）
```dart
BdhMediaPicker(
  item: FormItem(title: "照片上传"),
  maxCount: 3,
  autoUpload: true, // 可省略，默认为true
  onChange: (urls) {
    // urls为网络URL列表，例如：
    // ["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
    print('上传后的网络URLs: $urls');
  },
)
```

#### 2. 本地路径模式
```dart
BdhMediaPicker(
  item: FormItem(title: "照片选择"),
  maxCount: 3,
  autoUpload: false, // 不自动上传
  onChange: (urls) {
    // urls为本地文件路径列表，例如：
    // ["/storage/emulated/0/DCIM/Camera/IMG_20241201_123456.jpg"]
    print('本地文件路径: $urls');
    
    // 可以将这些路径保存起来，稍后批量上传
    saveLocalPathsForLater(urls);
  },
)
```

### 使用场景

#### 自动上传模式 (autoUpload: true)
- 适用于需要立即上传文件的场景
- 网络状况良好，用户希望快速完成上传
- 单个文件上传，对用户体验要求较高

#### 本地路径模式 (autoUpload: false)
- 适用于需要批量处理的场景
- 网络状况不佳，希望稍后统一上传
- 需要在提交表单时一次性上传所有文件
- 离线模式，先保存本地路径到数据库

### 日志输出示例

#### 自动上传模式日志
```
I/flutter: 自动上传模式 - 返回的URLs: [https://example.com/upload/image123.jpg]
I/flutter: 自动上传模式 - 第1个文件:
I/flutter:   - URL: https://example.com/upload/image123.jpg
I/flutter:   - 类型: 网络URL
```

#### 本地路径模式日志
```
I/flutter: 选择的本地文件路径: /storage/emulated/0/DCIM/Camera/IMG_20241201_123456.jpg
I/flutter: 本地文件回调完成，路径: /storage/emulated/0/DCIM/Camera/IMG_20241201_123456.jpg
I/flutter: 本地路径模式 - 返回的URLs: [/storage/emulated/0/DCIM/Camera/IMG_20241201_123456.jpg]
I/flutter: 本地路径模式 - 第1个文件:
I/flutter:   - URL: /storage/emulated/0/DCIM/Camera/IMG_20241201_123456.jpg
I/flutter:   - 类型: 本地路径
I/flutter:   - 本地文件路径可用于后续批量上传: /storage/emulated/0/DCIM/Camera/IMG_20241201_123456.jpg
```

### 注意事项

1. **兼容性**: 现有代码无需修改，默认行为保持不变（自动上传）
2. **文件预览**: 两种模式下都支持文件预览功能
3. **文件类型**: 支持图片和视频，通过 `allowImage` 和 `allowVideo` 参数控制
4. **本地路径**: 当 `autoUpload: false` 时，返回的是设备本地文件路径，可用于后续上传操作

### 测试页面
可以使用 `MediaPickerTestPage` 来测试两种模式的区别，该页面同时展示了自动上传和本地路径两种模式的效果。 