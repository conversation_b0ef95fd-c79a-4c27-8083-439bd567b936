import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BdhTextArea extends FormField<String> {
  final FormItem item;
  final String? placeHolder;
  final int? maxCount;
  BdhTextArea(
      {super.key,
      super.initialValue,
      super.onSaved,
      super.validator,
      required this.item,
      this.placeHolder,
      this.maxCount})
      : super(builder: (field) {
          return GestureDetector(
            onTap: () {
              Navigator.of(field.context)
                  .push(CupertinoPageRoute(builder: (ctx) {
                return TextEditPage(
                  title: item.title ?? "",
                  value: field.value ?? "",
                  maxCount: maxCount,
                  onSave: (v) {
                    field.didChange(v);
                  },
                );
              }));
            },
            child: Container(
              decoration: BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          width: 1.px,
                          color: const Color.fromRGBO(226, 235, 231, 0.6)))),
              constraints: BoxConstraints(minHeight: 100.px),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 15.px),
                    child: Text.rich(TextSpan(children: [
                      TextSpan(
                        text: (item.isRequired ?? false) ? "*" : "",
                        style: TextStyle(
                            color: Colors.red,
                            fontSize: 16.px,
                            fontWeight: FontWeight.w500),
                      ),
                      TextSpan(
                        text: item.title,
                        style: TextStyle(
                            fontSize: 16.px, fontWeight: FontWeight.w500),
                      )
                    ])),
                  ),
                  Container(
                    margin: EdgeInsets.only(bottom: 15.px),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        (field.value != null && field.value!.isNotEmpty)
                            ? SizedBox(
                                width: 320.px,
                                child: Text(
                                  field.value!,
                                  style: const TextStyle(
                                      overflow: TextOverflow.ellipsis),
                                  maxLines: 3,
                                ),
                              )
                            : SizedBox(
                                width: 320.px,
                                child: Text(
                                  placeHolder ?? "请输入${item.title}",
                                  style: const TextStyle(
                                      color: Color.fromRGBO(
                                        0,
                                        0,
                                        0,
                                        0.4,
                                      ),
                                      overflow: TextOverflow.ellipsis),
                                  maxLines: 2,
                                ),
                              ),
                        Image.asset(
                            width: 6.9.px,
                            height: 11.07.px,
                            ImageHelper.wrapAssets("arrow_right_black.png"))
                      ],
                    ),
                  ),
                  Visibility(
                      visible: field.errorText != null,
                      child: Text(
                        field.errorText ?? "",
                        style: const TextStyle(color: Colors.red),
                      ))
                ],
              ),
            ),
          );
        });
}

class TextEditPage extends StatefulWidget {
  final String title;
  final String value;
  final int? maxCount;
  final Function(String) onSave;
  const TextEditPage(
      {super.key,
      required this.onSave,
      required this.value,
      required this.title,
      required this.maxCount});

  @override
  State<StatefulWidget> createState() => _TextEditPageState();
}

class _TextEditPageState extends State<TextEditPage> {
  TextEditingController controller = TextEditingController();
  bool showError = false;
  bool showClear = false;
  @override
  void initState() {
    super.initState();

    controller.addListener(() {
      if (controller.text.length >= (widget.maxCount ?? 300)) {
        showError = true;
      } else {
        showError = false;
      }
      setState(() {});
    });
    controller.text = widget.value;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          GestureDetector(
            onTap: () {
              widget.onSave(controller.text);
              Navigator.of(context).pop();
            },
            child: Container(
              margin: EdgeInsets.only(right: 15.px),
              child: BDHButtonGreen(width: 60.px, height: 24.px, title: "保存"),
            ),
          )
        ],
      ),
      body: Container(
        padding: EdgeInsets.all(15.px),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "请输入${widget.title}",
              style: const TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
            ),
            Row(
              children: [
                Container(
                    // color: Colors.red,
                    width: 325.px,
                    margin: EdgeInsets.only(top: 5.px),
                    child: CupertinoTextField.borderless(
                      autofocus: true,
                      controller: controller,
                      maxLength: widget.maxCount ?? 300,
                      maxLines: 10,
                    )),
                Visibility(
                    visible: controller.text.isNotEmpty,
                    child: GestureDetector(
                      onTap: () {
                        controller.clear();
                      },
                      child: Image.asset(
                          width: 18.75.px,
                          height: 18.75.px,
                          ImageHelper.wrapAssets("clear_gray.png")),
                    ))
              ],
            ),
            Visibility(
                visible: showError,
                child: Text(
                  "${widget.title}最多输入${widget.maxCount ?? 300}个字",
                  style: const TextStyle(color: Colors.red),
                ))
          ],
        ),
      ),
    );
  }
}
