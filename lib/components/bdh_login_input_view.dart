import 'dart:async';

import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

// ignore: must_be_immutable
class BdhLoginInputView extends StatelessWidget {
  String titleName;
  String tipName;
  BorderRadius? borderRadius;
  TextEditingController controller;
  BdhLoginInputView(
      {super.key,
      required this.titleName,
      required this.tipName,
      this.borderRadius,
      required this.controller});

  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.only(top: 30.px),
      padding: EdgeInsets.only(left: 20.px, right: 2.px),
      width: 295.px,
      height: 54.px,
      decoration: BoxDecoration(
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(122, 193, 171, 0.1),
              offset: Offset(0, 4),
              blurRadius: 15.5,
            ),
          ],
          color: Colors.white,
          borderRadius:
              borderRadius ?? BorderRadius.all(Radius.circular(40.px)),
          border: Border.all(
              width: 0.5.px, color: const Color.fromRGBO(226, 235, 231, 0.8))),
      child: Row(
        children: [
          Text(
            titleName,
            style: TextStyle(
                fontSize: 16.px,
                fontWeight: FontWeight.w400,
                color: const Color.fromRGBO(0, 0, 0, 0.2)),
          ),
          Container(
            margin: EdgeInsets.only(left: 10.px, right: 10.px),
            width: 1.px,
            height: 24.px,
            color: const Color.fromRGBO(226, 235, 231, 0.4),
          ),
          Expanded(
            child: Container(
              // decoration: BoxDecoration(border: Border.all(width: 1)),
              child: CupertinoTextField.borderless(
                padding: EdgeInsets.zero,
                controller: controller,
                placeholder: tipName,
                placeholderStyle: TextStyle(
                    fontSize: 16.px,
                    fontWeight: FontWeight.w400,
                    color: const Color.fromRGBO(0, 0, 0, 0.2)),
              ),
            ),
          ),
          SizedBox(width: 10.px)
          // Container(
          //   decoration: BoxDecoration(border: Border.all(width: 1)),
          //   width: 200.px,
          //   child: CupertinoTextField.borderless(
          //     padding: EdgeInsets.zero,
          //     controller: controller,
          //     placeholder: tipName,
          //     placeholderStyle: TextStyle(
          //         fontSize: 16.px,
          //         fontWeight: FontWeight.w400,
          //         color: const Color.fromRGBO(0, 0, 0, 0.2)),
          //   ),
          // )
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class BDHLoginInputPWDView extends StatefulWidget {
  String titleName;
  String tipName;
  BorderRadius? borderRadius;
  TextEditingController controller;
  BDHLoginInputPWDView(
      {super.key,
      required this.titleName,
      required this.tipName,
      this.borderRadius,
      required this.controller});

  @override
  State<BDHLoginInputPWDView> createState() => _BDHLoginInputPWDViewState();
}

class _BDHLoginInputPWDViewState extends State<BDHLoginInputPWDView> {
  bool isShowPassWord = false;
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 20.px, right: 2.px),
      width: 295.px,
      height: 54.px,
      decoration: BoxDecoration(
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(122, 193, 171, 0.1),
              offset: Offset(0, 4),
              blurRadius: 15.5,
            ),
          ],
          color: Colors.white,
          borderRadius:
              widget.borderRadius ?? BorderRadius.all(Radius.circular(40.px)),
          border: Border.all(
              width: 0.5.px, color: const Color.fromRGBO(226, 235, 231, 0.8))),
      child: Row(
        children: [
          Text(
            widget.titleName,
            style: TextStyle(
                fontSize: 16.px,
                fontWeight: FontWeight.w400,
                color: const Color.fromRGBO(0, 0, 0, 0.2)),
          ),
          Container(
            margin: EdgeInsets.only(left: 10.px, right: 10.px),
            width: 1.px,
            height: 24.px,
            color: const Color.fromRGBO(226, 235, 231, 0.4),
          ),
          Expanded(
              child: Container(
            // decoration: BoxDecoration(border: Border.all(width: 1)),
            // width: 180.px,
            child: CupertinoTextField.borderless(
              padding: EdgeInsets.zero,
              controller: widget.controller,
              obscureText: !isShowPassWord,
              placeholder: widget.tipName,
              placeholderStyle: TextStyle(
                  fontSize: 16.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 0, 0, 0.2)),
            ),
          )),
          GestureDetector(
              onTap: () {
                setState(() {
                  isShowPassWord = !isShowPassWord;
                });
              },
              child: isShowPassWord
                  ? Image.asset(
                      width: 24.px,
                      height: 24.px,
                      ImageHelper.wrapAssets("openPWDImg.png"))
                  : Image.asset(
                      width: 24.px,
                      height: 24.px,
                      ImageHelper.wrapAssets("colsePWDImg.png"))),
          SizedBox(width: 20.px)
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class BdhLoginPhoneNumberInputView extends StatefulWidget {
  String tipName;
  BorderRadius? borderRadius;
  TextEditingController controller;
  BdhLoginPhoneNumberInputView(
      {super.key,
      required this.tipName,
      required this.controller,
      this.borderRadius});

  @override
  State<BdhLoginPhoneNumberInputView> createState() =>
      _BdhLoginPhoneNumberInputViewState();
}

class _BdhLoginPhoneNumberInputViewState
    extends State<BdhLoginPhoneNumberInputView> {
  bool needShowClearBtn = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(() {
      setState(() {
        needShowClearBtn = widget.controller.text.isEmpty ? false : true;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // margin: EdgeInsets.only(top: 30.px),
      padding: EdgeInsets.only(left: 20.px, right: 20.px),
      width: 295.px,
      height: 54.px,
      decoration: BoxDecoration(
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(122, 193, 171, 0.1),
              offset: Offset(0, 4),
              blurRadius: 15.5,
            ),
          ],
          color: Colors.white,
          borderRadius:
              widget.borderRadius ?? BorderRadius.all(Radius.circular(40.px)),
          border: Border.all(
              width: 0.5.px, color: const Color.fromRGBO(226, 235, 231, 0.8))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            // decoration: BoxDecoration(border: Border.all(width: 1)),
            width: 200.px,
            child: CupertinoTextField.borderless(
              padding: EdgeInsets.zero,
              controller: widget.controller,
              keyboardType: TextInputType.number,
              placeholder: widget.tipName,
              placeholderStyle: TextStyle(
                  fontSize: 16.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 0, 0, 0.2)),
            ),
          ),
          needShowClearBtn
              ? GestureDetector(
                  onTap: () {
                    widget.controller.text = "";
                  },
                  child: Image.asset(
                      width: 24.px,
                      height: 24.px,
                      ImageHelper.wrapAssets("clearInputBtn.png")))
              : Container(),
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class BDHInputSMSCodeView extends StatefulWidget {
  String titleName;
  String tipName;
  TextEditingController controller;
  TextEditingController phoneNumcontroller;
  bool? isCheckedServiceAgreement;
  bool? isResetPWD;
  BDHInputSMSCodeView(
      {super.key,
      required this.titleName,
      required this.tipName,
      required this.controller,
      required this.phoneNumcontroller,
      this.isResetPWD,
      this.isCheckedServiceAgreement});

  @override
  State<BDHInputSMSCodeView> createState() => _BDHInputSMSCodeViewState();
}

class _BDHInputSMSCodeViewState extends State<BDHInputSMSCodeView> {
  int count = 60;
  String btnText = "获取验证码";
  Timer? timer;

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 20.px, right: 2.px),
      width: 295.px,
      height: 54.px,
      decoration: BoxDecoration(
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(122, 193, 171, 0.1),
              offset: Offset(0, 4),
              blurRadius: 15.5,
            ),
          ],
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px)),
          border: Border.all(
              width: 0.5.px, color: const Color.fromRGBO(226, 235, 231, 0.8))),
      child: Row(
        children: [
          Text(
            widget.titleName,
            style: TextStyle(
                fontSize: 16.px,
                fontWeight: FontWeight.w400,
                color: const Color.fromRGBO(0, 0, 0, 0.2)),
          ),
          Container(
            margin: EdgeInsets.only(left: 10.px, right: 10.px),
            width: 1.px,
            height: 24.px,
            color: const Color.fromRGBO(226, 235, 231, 0.4),
          ),
          Expanded(
              child: Container(
            // decoration: BoxDecoration(border: Border.all(width: 1)),
            // width: 180.px,
            child: CupertinoTextField.borderless(
              padding: EdgeInsets.zero,
              controller: widget.controller,
              // obscureText: !isShowPassWord,
              placeholder: "请输入验证码",
              placeholderStyle: TextStyle(
                  fontSize: 16.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 0, 0, 0.2)),
            ),
          )),
          GestureDetector(
            onTap: () {
              widget.isResetPWD ?? false ? getResetSmsCode() : getSmsCode();
            },
            child: Container(
              alignment: Alignment.center,
              height: 36.px,
              width: 92.px,
              decoration: BoxDecoration(
                  gradient: const LinearGradient(
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                      colors: [
                        Color.fromRGBO(0, 127, 255, 1),
                        Color.fromRGBO(61, 156, 255, 1)
                        // Color.fromRGBO(14, 202, 139, 1),
                        // Color.fromRGBO(19, 230, 121, 1)
                      ]),
                  // color: color ?? const Color.fromRGBO(22, 183, 96, 1),
                  borderRadius: BorderRadius.all(Radius.circular(8.px))),
              child: Text(
                btnText,
                style: TextStyle(
                    fontSize: 13.px,
                    fontWeight: FontWeight.w400,
                    fontFamily: 'PingFang SC',
                    color: Colors.white),
              ),
            ),
          ),
          SizedBox(width: 10.px),
        ],
      ),
    );
  }

//获取验证码
  getSmsCode() async {
    if (!(widget.isCheckedServiceAgreement ?? true)) {
      showToast("请先阅读并同意用户协议和隐私协议");
      return;
    }
    if (RegUtil.isPhoneNumber(widget.phoneNumcontroller.text)) {
      var result = await BDHResponsitory.getRegisterSmsCode(
          {"phone": widget.phoneNumcontroller.text, "template": ""});
      if (result.success ?? false) {
        // if (true) {
        showToast(result.msg ?? "");
        if (count < 60 && count > 0) {
          showToast("请等计时结束再试");
        } else {
          timer = Timer.periodic(const Duration(seconds: 1), (timer) {
            count--;
            if (count <= 0) {
              timer.cancel();
            }
            setState(() {
              if (count > 0) {
                btnText = "请$count秒后再试";
              } else {
                btnText = "重新发送";
                count = 60;
              }
            });
          });
        }
      } else {
        showToast(result.msg ?? "");
      }
    } else {
      showToast("请输入正确的手机号");
    }
  }

//重置密码 获取验证码
  getResetSmsCode() async {
    if (!(widget.isCheckedServiceAgreement ?? true)) {
      showToast("请先阅读并同意用户协议和隐私协议");
      return;
    }

    if (count < 60 && count > 0) {
      showToast("请等计时结束再试");
      return;
    }

    if (RegUtil.isPhoneNumber(widget.phoneNumcontroller.text)) {
      var result = await BDHResponsitory.getSmsCode(
          {"phone": widget.phoneNumcontroller.text, "template": ""});
      if (result.success ?? false) {
        showToast(result.msg ?? "");
        if (count < 60 && count > 0) {
          showToast("请等计时结束再试");
        } else {
          timer = Timer.periodic(const Duration(seconds: 1), (timer) {
            count--;
            if (count <= 0) {
              timer.cancel();
            }
            setState(() {
              if (count > 0) {
                btnText = "请$count秒后再试";
              } else {
                btnText = "重新发送";
                count = 60;
              }
            });
          });
        }
      } else {
        showToast(result.msg ?? "");
      }
    } else {
      showToast("请输入正确的手机号");
    }
  }
}
