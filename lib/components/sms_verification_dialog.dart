import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_land.dart';
import 'package:oktoast/oktoast.dart';

class SmsVerificationDialog extends StatefulWidget {
  final List<String> rationPlanIds;
  final VoidCallback? onSuccess;
  final VoidCallback? onCancel;

  const SmsVerificationDialog({
    Key? key,
    required this.rationPlanIds,
    this.onSuccess,
    this.onCancel,
  }) : super(key: key);

  @override
  State<SmsVerificationDialog> createState() => _SmsVerificationDialogState();
}

class _SmsVerificationDialogState extends State<SmsVerificationDialog> {
  final TextEditingController _codeController = TextEditingController();
  String phoneNumber = '';
  bool isLoading = false;
  bool isGettingCode = false;
  int countdown = 0;

  @override
  void initState() {
    super.initState();
    _getPhoneNumber();
  }

  @override
  void dispose() {
    _codeController.dispose();
    super.dispose();
  }

  // 获取手机号
  Future<void> _getPhoneNumber() async {
    setState(() {
      isLoading = true;
    });

    try {
      final response = await BdhDigitalLand.phoneMessageCheck({
        'phoneMsgType': 1,
      });

      if (response.success == true) {
        setState(() {
          phoneNumber = response.msg?.toString() ?? '';
        });
      } else {
        showToast(response.msg ?? '获取手机号失败');
      }
    } catch (e) {
      showToast('获取手机号失败: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // 获取验证码
  Future<void> _getVerificationCode() async {
    if (phoneNumber.isEmpty) {
      showToast('请先获取手机号');
      return;
    }

    setState(() {
      isGettingCode = true;
    });

    try {
      final response = await BdhDigitalLand.phoneMessageCheck({
        'phoneMsgType': 2,
      });

      if (response.success == true) {
        showToast('验证码已发送');
        _startCountdown();
      } else {
        showToast(response.msg ?? '获取验证码失败');
      }
    } catch (e) {
      showToast('获取验证码失败: $e');
    } finally {
      setState(() {
        isGettingCode = false;
      });
    }
  }

  // 开始倒计时
  void _startCountdown() {
    setState(() {
      countdown = 60;
    });

    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        setState(() {
          countdown--;
        });
        return countdown > 0;
      }
      return false;
    });
  }

  // 验证验证码并审核通过
  Future<void> _verifyCodeAndApprove() async {
    if (_codeController.text.trim().isEmpty) {
      showToast('请输入验证码');
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      // 先验证验证码
      final verifyResponse = await BdhDigitalLand.phoneMessageCheck({
        'phoneMsgType': 3,
        'phoneMsgCode': _codeController.text.trim(),
      });

      if (verifyResponse.success == true) {
        // 验证码正确，执行审核通过
        return null;
        final approveResponse =
            await BdhDigitalLand.contractPlanAppAcceptByIds({
          'beforeFaceCheckDataFlag': 0,
          'rationPlanIds': widget.rationPlanIds,
        });

        if (approveResponse.success == true) {
          showToast('审核通过成功');
          Navigator.of(context).pop();
          widget.onSuccess?.call();
        } else {
          showToast(approveResponse.msg ?? '审核通过失败');
        }
      } else {
        showToast(verifyResponse.msg ?? '验证码错误');
      }
    } catch (e) {
      showToast('操作失败: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // 审核拒绝
  Future<void> _rejectApproval() async {
    setState(() {
      isLoading = true;
    });
    return null;
    try {
      final response = await BdhDigitalLand.contractPlanAppppRejectByIds({
        'beforeFaceCheckDataFlag': 0,
        'rationPlanIds': widget.rationPlanIds,
      });

      if (response.success == true) {
        showToast('审核拒绝成功');
        Navigator.of(context).pop();
        widget.onSuccess?.call();
      } else {
        showToast(response.msg ?? '审核拒绝失败');
      }
    } catch (e) {
      showToast('操作失败: $e');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  String _formatPhoneNumber(String phone) {
    if (phone.length >= 11) {
      return '${phone.substring(0, 3)}****${phone.substring(7)}';
    }
    return phone;
  }

  @override
  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        padding: EdgeInsets.only(
          top: 15.px,
          left: 15.px,
          right: 15.px,
          bottom: 15.px,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFFCEDBF9), // #cedbf9
              const Color(0xFFFFFFFF), // #FFFFFF
            ],
            stops: const [0.01, 0.3], // 1% 到 30%
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题和关闭按钮
            // 标题和关闭按钮
            Column(
              children: [
                // 关闭按钮行 - 右对齐
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                          widget.onCancel?.call();
                        },
                        child: const Icon(
                          Icons.close,
                          color: Colors.grey,
                          size: 24,
                        ),
                      ),
                    ),
                  ],
                ),
                // 标题行 - 居中
                Text(
                  '短信验证码确认',
                  textAlign: TextAlign.center, // 文字居中
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black,
                  ),
                ),
                const SizedBox(height: 24), // 原有间距保留
              ],
            ),

            const SizedBox(height: 24),

            // 手机号显示
            // 手机号显示
            if (phoneNumber.isNotEmpty)
              Text(
                '已发送至手机号码 ${_formatPhoneNumber(phoneNumber)}',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),

            const SizedBox(height: 20),
            // 验证码输入框和获取验证码按钮
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _codeController,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(6),
                    ],
                    decoration: const InputDecoration(
                      hintText: '请输入短信验证码',
                      border: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.grey),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderSide: BorderSide(color: Colors.blue),
                      ),
                      contentPadding:
                          EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                GestureDetector(
                  onTap: countdown > 0 || isGettingCode
                      ? null
                      : _getVerificationCode,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: countdown > 0 || isGettingCode
                          ? Colors.grey[300]
                          : Colors.blue,
                      borderRadius: BorderRadius.circular(4),
                      border:
                          Border.all(color: Colors.blue, width: 1), // 添加蓝色边框
                    ),
                    child: Text(
                      isGettingCode
                          ? '发送中...'
                          : countdown > 0
                              ? '${countdown}s'
                              : '获取验证码',
                      style: TextStyle(
                        color: countdown > 0 || isGettingCode
                            ? Colors.grey[600]
                            : Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // 操作按钮
            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: isLoading ? null : _verifyCodeAndApprove,
                    child: Container(
                      height: 44,
                      decoration: BoxDecoration(
                        color: isLoading ? Colors.grey[300] : Colors.blue,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Center(
                        child: isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                            : const Text(
                                '确认',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                      ),
                    ),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
