import 'dart:ui';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class BdhAlertDialog extends StatelessWidget {
  final String title;
  final String subTitle;
  final String cancelText;
  final String ensureText;
  final Function() onEnsure;
  const BdhAlertDialog(
      {super.key,
      required this.title,
      required this.subTitle,
      required this.cancelText,
      required this.ensureText,
      required this.onEnsure});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Center(
        child: Container(
          width: 280.px,
          height: 222.px,
          decoration: BoxDecoration(
              color: const Color.fromRGBO(241, 245, 243, 1),
              borderRadius: BorderRadius.all(Radius.circular(4.px))),
          child: Column(
            children: [
              SizedBox(
                height: 40.px,
              ),
              Text(
                title,
                style: TextStyle(fontSize: 20.px, fontWeight: FontWeight.w500),
              ),
              SizedBox(
                height: 20.px,
              ),
              Text(
                subTitle,
                style: TextStyle(fontSize: 16.px, fontWeight: FontWeight.w400),
              ),
              SizedBox(
                height: 20.px,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: BDHButtonWhite(
                        width: 100.px, height: 40.px, title: cancelText),
                  ),
                  SizedBox(
                    width: 20.px,
                  ),
                  GestureDetector(
                    onTap: () {
                      onEnsure();
                    },
                    child: BDHButtonGreen(
                        width: 100.px, height: 40.px, title: ensureText),
                  )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}

class BdhDialog extends AlertDialog {
  final Widget? child;
  final BoxDecoration? backgroundDecoration;
  const BdhDialog({super.key, this.backgroundDecoration, this.child});
  @override
  Widget build(BuildContext context) {
    Widget c = Container(
      decoration: backgroundDecoration ??
          BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(6.px)),
              gradient: const LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.center,
                  colors: [
                    Color.fromRGBO(200, 236, 218, 1),
                    Color.fromRGBO(255, 255, 255, 1)
                  ])),
      child: child,
    );

    final DialogTheme dialogTheme = DialogTheme.of(context) as DialogTheme;
    final EdgeInsets effectivePadding = MediaQuery.viewInsetsOf(context) +
        (insetPadding ??
            dialogTheme.insetPadding ??
            EdgeInsets.symmetric(horizontal: 48.px, vertical: 24.px));
    final DialogTheme defaults = _BdhDialogTheme(context);

    Widget dialogChild = Align(
      alignment: alignment ?? dialogTheme.alignment ?? defaults.alignment!,
      child: ConstrainedBox(
        constraints: BoxConstraints(minWidth: 280.px, minHeight: 100.px),
        child: Material(
          color: Colors.transparent,
          elevation: elevation ?? dialogTheme.elevation ?? defaults.elevation!,
          shadowColor:
              shadowColor ?? dialogTheme.shadowColor ?? defaults.shadowColor,
          surfaceTintColor: surfaceTintColor ??
              dialogTheme.surfaceTintColor ??
              defaults.surfaceTintColor,
          shape: shape ?? dialogTheme.shape ?? defaults.shape!,
          type: MaterialType.card,
          child: c,
        ),
      ),
    );

    return AnimatedPadding(
      padding: effectivePadding,
      duration: const Duration(milliseconds: 100),
      curve: Curves.decelerate,
      child: MediaQuery.removeViewInsets(
        removeLeft: true,
        removeTop: true,
        removeRight: true,
        removeBottom: true,
        context: context,
        child: dialogChild,
      ),
    );
  }
}

class _BdhDialogTheme extends DialogTheme {
  _BdhDialogTheme(this.context)
      : super(
          alignment: Alignment.center,
          elevation: 6.0,
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(28.0))),
        );

  final BuildContext context;
  late final ColorScheme _colors = Theme.of(context).colorScheme;
  late final TextTheme _textTheme = Theme.of(context).textTheme;

  @override
  Color? get iconColor => _colors.secondary;

  @override
  Color? get backgroundColor => _colors.surfaceContainerHigh;

  @override
  Color? get shadowColor => Colors.transparent;

  @override
  Color? get surfaceTintColor => Colors.transparent;

  @override
  TextStyle? get titleTextStyle => _textTheme.headlineSmall;

  @override
  TextStyle? get contentTextStyle => _textTheme.bodyMedium;

  @override
  EdgeInsetsGeometry? get actionsPadding =>
      const EdgeInsets.only(left: 24.0, right: 24.0, bottom: 24.0);
}

class BdhSimpleAlertDialog extends AlertDialog {
  final TextAlign? titleTextAlign;
  final BoxDecoration? backgroundDecoration;
  const BdhSimpleAlertDialog({
    super.key,
    super.icon,
    super.iconPadding,
    super.iconColor,
    super.title,
    super.titlePadding,
    super.titleTextStyle,
    this.titleTextAlign,
    super.content,
    super.contentPadding,
    super.contentTextStyle,
    super.actions,
    super.actionsPadding,
    super.actionsAlignment,
    super.actionsOverflowAlignment,
    super.actionsOverflowDirection,
    super.actionsOverflowButtonSpacing,
    super.buttonPadding,
    super.backgroundColor,
    super.elevation,
    super.shadowColor,
    super.surfaceTintColor,
    super.semanticLabel,
    super.insetPadding,
    super.shape,
    super.alignment,
    super.scrollable = false,
    this.backgroundDecoration,
  });

  @override
  Widget build(BuildContext context) {
    assert(debugCheckHasMaterialLocalizations(context));
    final ThemeData theme = Theme.of(context);

    final DialogTheme dialogTheme = DialogTheme.of(context);
    final DialogTheme defaults = _BdhDialogTheme(context);

    String? label = semanticLabel;
    switch (theme.platform) {
      case TargetPlatform.iOS:
      case TargetPlatform.macOS:
        break;
      case TargetPlatform.android:
      case TargetPlatform.fuchsia:
      case TargetPlatform.linux:
      case TargetPlatform.windows:
        label ??= MaterialLocalizations.of(context).alertDialogLabel;
    }

    // The paddingScaleFactor is used to adjust the padding of Dialog's
    // children.
    const double fontSizeToScale = 14.0;
    final double effectiveTextScale =
        MediaQuery.textScalerOf(context).scale(fontSizeToScale) /
            fontSizeToScale;
    final double paddingScaleFactor = _scalePadding(effectiveTextScale);
    final TextDirection? textDirection = Directionality.maybeOf(context);

    Widget? iconWidget;
    Widget? titleWidget;
    Widget? contentWidget;
    Widget? actionsWidget;

    if (icon != null) {
      final bool belowIsTitle = title != null;
      final bool belowIsContent = !belowIsTitle && content != null;
      final EdgeInsets defaultIconPadding = EdgeInsets.only(
        left: 24.0,
        top: 24.0,
        right: 24.0,
        bottom: belowIsTitle
            ? 16.0
            : belowIsContent
                ? 0.0
                : 24.0,
      );
      final EdgeInsets effectiveIconPadding =
          iconPadding?.resolve(textDirection) ?? defaultIconPadding;
      iconWidget = Padding(
        padding: EdgeInsets.only(
          left: effectiveIconPadding.left * paddingScaleFactor,
          right: effectiveIconPadding.right * paddingScaleFactor,
          top: effectiveIconPadding.top * paddingScaleFactor,
          bottom: effectiveIconPadding.bottom,
        ),
        child: IconTheme(
          data: IconThemeData(
            color: iconColor ?? dialogTheme.iconColor ?? defaults.iconColor,
          ),
          child: icon!,
        ),
      );
    }

    if (title != null) {
      final EdgeInsets defaultTitlePadding = EdgeInsets.only(
        left: 24.0,
        top: icon == null ? 24.0 : 0.0,
        right: 24.0,
        bottom: content == null ? 20.0 : 0.0,
      );
      final EdgeInsets effectiveTitlePadding =
          titlePadding?.resolve(textDirection) ?? defaultTitlePadding;
      titleWidget = Padding(
        padding: EdgeInsets.only(
          left: effectiveTitlePadding.left * paddingScaleFactor,
          right: effectiveTitlePadding.right * paddingScaleFactor,
          top: icon == null
              ? effectiveTitlePadding.top * paddingScaleFactor
              : effectiveTitlePadding.top,
          bottom: effectiveTitlePadding.bottom,
        ),
        child: DefaultTextStyle(
          style: titleTextStyle ??
              dialogTheme.titleTextStyle ??
              defaults.titleTextStyle!,
          textAlign: titleTextAlign ?? TextAlign.center,
          child: Semantics(
            // For iOS platform, the focus always lands on the title.
            // Set nameRoute to false to avoid title being announce twice.
            namesRoute: label == null && theme.platform != TargetPlatform.iOS,
            container: true,
            child: title,
          ),
        ),
      );
    }

    if (content != null) {
      final EdgeInsets defaultContentPadding = EdgeInsets.only(
        left: 24.0,
        top: theme.useMaterial3 ? 16.0 : 20.0,
        right: 24.0,
        bottom: 24.0,
      );
      final EdgeInsets effectiveContentPadding =
          contentPadding?.resolve(textDirection) ?? defaultContentPadding;
      contentWidget = Padding(
        padding: EdgeInsets.only(
          left: effectiveContentPadding.left * paddingScaleFactor,
          right: effectiveContentPadding.right * paddingScaleFactor,
          top: title == null && icon == null
              ? effectiveContentPadding.top * paddingScaleFactor
              : effectiveContentPadding.top,
          bottom: effectiveContentPadding.bottom,
        ),
        child: DefaultTextStyle(
          style: contentTextStyle ??
              dialogTheme.contentTextStyle ??
              defaults.contentTextStyle!,
          child: Semantics(
            container: true,
            child: content,
          ),
        ),
      );
    }

    if (actions != null) {
      final double spacing = (buttonPadding?.horizontal ?? 16) / 2;
      actionsWidget = Padding(
        padding: actionsPadding ??
            dialogTheme.actionsPadding ??
            (theme.useMaterial3
                ? defaults.actionsPadding!
                : defaults.actionsPadding!.add(EdgeInsets.all(spacing))),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: actions!,
        ),
      );
    }

    List<Widget> columnChildren;
    if (scrollable) {
      columnChildren = <Widget>[
        if (title != null || content != null)
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  if (icon != null) iconWidget!,
                  if (title != null) titleWidget!,
                  if (content != null) contentWidget!,
                ],
              ),
            ),
          ),
        if (actions != null) actionsWidget!,
      ];
    } else {
      columnChildren = <Widget>[
        if (icon != null) iconWidget!,
        if (title != null) titleWidget!,
        if (content != null) Flexible(child: contentWidget!),
        if (actions != null) actionsWidget!,
      ];
    }

    Widget dialogChild = IntrinsicWidth(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: columnChildren,
      ),
    );

    if (label != null) {
      dialogChild = Semantics(
        scopesRoute: true,
        explicitChildNodes: true,
        namesRoute: true,
        label: label,
        child: dialogChild,
      );
    }

    return BdhDialog(
        backgroundDecoration: backgroundDecoration, child: dialogChild);
  }

  double _scalePadding(double textScaleFactor) {
    final double clampedTextScaleFactor =
        clampDouble(textScaleFactor, 1.0, 2.0);
    // The final padding scale factor is clamped between 1/3 and 1. For example,
    // a non-scaled padding of 24 will produce a padding between 24 and 8.
    return lerpDouble(1.0, 1.0 / 3.0, clampedTextScaleFactor - 1.0)!;
  }
}

Future<bool?> showSimpleConfirmDialog(BuildContext context,
    {required String title,
    String? cancelText,
    String? confirmText,
    Widget? content}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext dialogContext) {
      return BdhSimpleAlertDialog(
        scrollable: true,
        title: Text(
          title,
          strutStyle: StrutStyle(fontSize: 17.px),
          style: TextStyle(
              color: const Color.fromRGBO(41, 41, 52, 1),
              fontSize: 17.px,
              fontWeight: FontWeight.w600),
        ),
        titleTextAlign: TextAlign.center,
        actionsAlignment: MainAxisAlignment.center,
        content: content,
        actions: [
          Expanded(
              child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: 40.px,
                    maxHeight: 40.px,
                  ),
                  child: TextButton(
                    style: ButtonStyle(
                        shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.px))),
                        backgroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.disabled)) {
                            return Colors.grey;
                          } else if (states.contains(WidgetState.pressed)) {
                            return const Color.fromRGBO(203, 204, 210, 0.8);
                          } else {
                            return const Color.fromRGBO(203, 204, 210, 1);
                          }
                        }),
                        overlayColor:
                            WidgetStateProperty.all(Colors.transparent)),
                    onPressed: () {
                      Navigator.maybeOf(context)?.pop(false);
                    },
                    child: Text(
                      cancelText ?? "取消",
                      style: TextStyle(
                          fontSize: 14.px,
                          decoration: TextDecoration.none,
                          color: Colors.white),
                    ),
                  ))),
          SizedBox.square(dimension: 15.px),
          Expanded(
              child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: 40.px,
                    maxHeight: 40.px,
                  ),
                  child: TextButton(
                    style: ButtonStyle(
                        shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20.px))),
                        backgroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.disabled)) {
                            return Colors.grey;
                          } else if (states.contains(WidgetState.pressed)) {
                            return const Color.fromRGBO(23, 156, 102, 0.8);
                          } else {
                            return const Color.fromRGBO(2, 139, 93, 1);
                          }
                        }),
                        overlayColor:
                            WidgetStateProperty.all(Colors.transparent)),
                    onPressed: () {
                      Navigator.maybeOf(context)?.pop(true);
                    },
                    child: Text(
                      confirmText ?? "确认",
                      style: TextStyle(
                          fontSize: 14.px,
                          decoration: TextDecoration.none,
                          color: Colors.white),
                    ),
                  ))),
        ],
      );
    },
  );
}
