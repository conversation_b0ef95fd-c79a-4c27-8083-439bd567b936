import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view_gallery.dart';

class BdhGalleryPage extends StatefulWidget {
  final List<String> images;
  final int idx;

  const BdhGalleryPage({super.key, required this.images, required this.idx});

  @override
  State<StatefulWidget> createState() => _BdhGalleryPageState();
}

class _BdhGalleryPageState extends State<BdhGalleryPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        iconTheme: const IconThemeData(color: Colors.white),
        backgroundColor: Colors.transparent,
        centerTitle: false,
        title: Text(
          "图片预览",
          style: TextStyle(
              color: Colors.white,
              fontSize: 16.px,
              fontWeight: FontWeight.w600),
        ),
      ),
      body: PhotoViewGallery.builder(
          enableRotation: true,
          itemCount: widget.images.length,
          wantKeepAlive: true,
          builder: (ctx, idx) {
            return PhotoViewGalleryPageOptions(
                imageProvider: NetworkImage(
                    "${urlConfig.microfront}${widget.images[idx]}"));
          }),
    );
  }
}
