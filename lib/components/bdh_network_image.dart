import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class BdhNetworkImage extends StatelessWidget {
  final String url;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final String? errorImage;
  final Duration? fadeInDuration;
  final Duration? fadeOutDuration;
  final Widget Function(BuildContext, String)? placeholderBuilder;
  const BdhNetworkImage(
      {super.key,
      required this.url,
      this.width,
      this.height,
      this.fit,
      this.errorImage,
      this.fadeInDuration,
      this.placeholderBuilder,
      this.fadeOutDuration});

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: url,
      width: width,
      height: height,
      fit: fit ?? BoxFit.fitWidth,
      placeholder: placeholderBuilder,
      fadeInDuration: fadeInDuration ?? const Duration(milliseconds: 500),
      fadeOutDuration: fadeOutDuration ?? const Duration(milliseconds: 500),
      placeholderFadeInDuration:
          fadeInDuration ?? const Duration(milliseconds: 500),
      errorWidget: (context, url, error) {
        return Container(
          color: const Color.fromRGBO(241, 245, 243, 1),
          width: width,
          height: height,
          alignment: Alignment.center,
          child: Image.asset(
              width: (width ?? 0) > 48.px ? 48.px : width,
              ImageHelper.wrapAssets(errorImage ?? "image_error.png")),
        );
      },
    );
  }
}
