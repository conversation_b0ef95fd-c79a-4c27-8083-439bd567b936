import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class BDHButtonGreen extends StatelessWidget {
  final double width;
  final double height;
  final String title;
  final double? borderRadius;
  final String? icon;
  final Color? color;

  const BDHButtonGreen({
    super.key,
    required this.width,
    required this.height,
    required this.title,
    this.borderRadius,
    this.icon,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
          color: color ?? const Color.fromRGBO(22, 183, 96, 1),
          borderRadius:
              BorderRadius.all(Radius.circular(borderRadius ?? 4.px))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon != null
              ? Row(
                  children: [
                    Image.asset(width: 24.px, ImageHelper.wrapAssets(icon!)),
                    Sized<PERSON>ox(
                      width: 10.px,
                    )
                  ],
                )
              : Container(),
          Text(
            title,
            style: TextStyle(
                fontFamily: "PingFang SC",
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: 16.px),
          )
        ],
      ),
    );
  }
}

class BDHButtonWhite extends StatelessWidget {
  final double width;
  final double height;
  final String title;
  final String? icon;
  final Color? borderColor;
  final Color? textColor;

  const BDHButtonWhite({
    super.key,
    required this.width,
    required this.height,
    required this.title,
    this.icon,
    this.borderColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
          border: Border.all(
              width: 1.px,
              color: borderColor ?? const Color.fromRGBO(226, 235, 231, 1)),
          borderRadius: BorderRadius.all(Radius.circular(4.px))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon != null
              ? Row(
                  children: [
                    Image.asset(width: 24.px, ImageHelper.wrapAssets(icon!)),
                    SizedBox(
                      width: 10.px,
                    )
                  ],
                )
              : Container(),
          Text(
            title,
            style: TextStyle(
                fontFamily: "PingFang SC",
                color: textColor ?? Colors.black,
                fontWeight: FontWeight.w500,
                fontSize: 16.px),
          )
        ],
      ),
    );
  }
}

class BDHButtonWhiteRect extends StatelessWidget {
  final double width;
  final double height;
  final String title;
  final String? icon;

  const BDHButtonWhiteRect({
    super.key,
    required this.width,
    required this.height,
    required this.title,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
          border: Border.all(
              width: 0.5.px, color: const Color.fromRGBO(0, 0, 0, 0.2)),
          borderRadius: BorderRadius.all(Radius.circular(16.px))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon != null
              ? Row(
                  children: [
                    Image.asset(width: 24.px, ImageHelper.wrapAssets(icon!)),
                    SizedBox(
                      width: 10.px,
                    )
                  ],
                )
              : Container(),
          Text(
            title,
            style: TextStyle(
                fontFamily: "PingFang SC",
                color: Colors.black,
                fontWeight: FontWeight.w500,
                fontSize: 16.px),
          )
        ],
      ),
    );
  }
}

class BDHButtonGradient extends StatelessWidget {
  final double width;
  final double height;
  final String title;
  final double? borderRadius;
  final String? icon;

  const BDHButtonGradient({
    super.key,
    required this.width,
    required this.height,
    required this.title,
    this.borderRadius,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
          gradient: const LinearGradient(colors: [
            Color.fromRGBO(251, 204, 112, 1),
            Color.fromRGBO(240, 171, 77, 1)
          ]),
          borderRadius:
              BorderRadius.all(Radius.circular(borderRadius ?? 4.px))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon != null
              ? Row(
                  children: [
                    Image.asset(width: 24.px, ImageHelper.wrapAssets(icon!)),
                    SizedBox(
                      width: 10.px,
                    )
                  ],
                )
              : Container(),
          Text(
            title,
            style: TextStyle(
                fontFamily: "PingFang SC",
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: 16.px),
          )
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class BdHLoginOptionButton extends StatelessWidget {
  String buttonName;
  String imageName;
  BdHLoginOptionButton({
    super.key,
    required this.buttonName,
    required this.imageName,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      // decoration: BoxDecoration(border: Border.all(width: 1)),
      padding: EdgeInsets.only(left: 11.px, right: 11.px),
      child: Column(
        children: [
          Image.asset(
              width: 44.px,
              height: 44.px,
              ImageHelper.wrapAssets(
                  imageName.isEmpty ? 'userRegistImg.png' : imageName)),
          SizedBox(height: 5.px),
          Text(
            buttonName,
            style: TextStyle(
                color: const Color.fromRGBO(0, 0, 0, 0.4),
                fontSize: 12.px,
                fontWeight: FontWeight.w400),
          )
        ],
      ),
    );
  }
}

class BDHUserLoginButton extends StatelessWidget {
  final double width;
  final double height;
  final String title;
  final double? borderRadius;
  final String? icon;
  final Color? color;
  final double? marginTop;
  final double? marginBottom;
  final List<Color>? bgLinearGradientColors;

  const BDHUserLoginButton({
    super.key,
    required this.width,
    required this.height,
    required this.title,
    this.borderRadius,
    this.icon,
    this.color,
    this.marginBottom,
    this.marginTop,
    this.bgLinearGradientColors,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: marginTop ?? 0, bottom: marginBottom ?? 0),
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
          gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: bgLinearGradientColors == null
                  ? [
                      Color.fromRGBO(14, 202, 139, 1),
                      Color.fromRGBO(19, 230, 121, 1)
                    ]
                  : bgLinearGradientColors!),
          // color: color ?? const Color.fromRGBO(22, 183, 96, 1),
          borderRadius:
              BorderRadius.all(Radius.circular(borderRadius ?? 4.px))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon != null
              ? Row(
                  children: [
                    Image.asset(width: 24.px, ImageHelper.wrapAssets(icon!)),
                    SizedBox(
                      width: 10.px,
                    )
                  ],
                )
              : Container(),
          Text(
            title,
            style: TextStyle(
                fontFamily: "PingFang SC",
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: 16.px),
          )
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class BDHBackActionBtn extends StatelessWidget {
  String imageName;
  Function() clicekdCallBack;
  BDHBackActionBtn({
    super.key,
    required this.imageName,
    required this.clicekdCallBack,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        alignment: Alignment.centerLeft,
        child: Image.asset(
            width: 42.px,
            height: 42.px,
            ImageHelper.wrapAssets(
                imageName.isEmpty ? "closePageBtnImg.png" : imageName)),
      ),
    );
  }
}

// ignore: must_be_immutable
class BDHAppBarTitleView extends StatelessWidget {
  String titleName;
  TextStyle? style;
  BDHAppBarTitleView({
    super.key,
    required this.titleName,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      child: Text(
        titleName,
        style: style ??
            TextStyle(
              fontSize: 20.px,
              fontWeight: FontWeight.w500,
              color: const Color.fromRGBO(7, 44, 29, 1),
            ),
      ),
    );
  }
}

// ignore: must_be_immutable
class BDHCustomAppBarView extends StatelessWidget {
  Widget? leftView;
  Widget? centerView;
  Widget? rightView;
  EdgeInsetsGeometry? padding;
  BDHCustomAppBarView({
    super.key,
    this.leftView,
    this.centerView,
    this.rightView,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 375.px,
      padding: padding ?? EdgeInsets.only(left: 20.px, right: 20.px),
      child: Row(
        children: [
          Expanded(flex: 1, child: leftView ?? Container()),
          Expanded(
              flex: 2,
              child: Center(
                child: centerView ?? Container(),
              )),
          Expanded(
              flex: 1,
              child: Container(
                child: rightView ?? Container(),
              )),
        ],
      ),
    );
  }
}

class BdhTextButton extends StatelessWidget {
  final String text;
  final double textSize;
  final Color? textColor;
  final FontWeight textFontWeight;
  final VoidCallback? onPressed;
  final BorderRadius? borderRadius;
  final Color pressedBackgroundColor;
  final Color disableBackgroundColor;
  final Color backgroundColor;
  final Color pressedForegroundColor;
  final Color disableForegroundColor;
  final Color foregroundColor;
  final BorderSide? side;
  final BorderSide? disableSide;
  final BorderSide? pressedSide;
  final double? width;
  final double? height;
  final EdgeInsets? padding;

  const BdhTextButton(
      {super.key,
      required this.text,
      this.textSize = 14,
      this.textFontWeight = FontWeight.w500,
      this.onPressed,
      this.borderRadius,
      this.pressedBackgroundColor = const Color.fromRGBO(1, 1, 1, 0.1),
      this.disableBackgroundColor = const Color.fromRGBO(1, 1, 1, 0.1),
      this.backgroundColor = Colors.transparent,
      this.side,
      this.disableSide,
      this.pressedSide,
      this.pressedForegroundColor = const Color.fromRGBO(2, 139, 93, 1),
      this.disableForegroundColor = Colors.white,
      this.foregroundColor = const Color.fromRGBO(2, 139, 93, 1),
      this.width,
      this.height,
      this.padding,
      this.textColor});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: width,
        height: height,
        child: TextButton(
          style: ButtonStyle(
              shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                  borderRadius: borderRadius ?? BorderRadius.circular(9))),
              backgroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.disabled)) {
                  return disableBackgroundColor;
                } else if (states.contains(WidgetState.pressed)) {
                  return pressedBackgroundColor;
                } else {
                  return backgroundColor;
                }
              }),
              padding: WidgetStateProperty.all(EdgeInsets.zero),
              side: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.disabled)) {
                  return disableSide;
                } else if (states.contains(WidgetState.pressed)) {
                  return pressedSide;
                } else {
                  return side;
                }
              }),
              foregroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.disabled)) {
                  return disableForegroundColor;
                } else if (states.contains(WidgetState.pressed)) {
                  return pressedForegroundColor;
                } else {
                  return foregroundColor;
                }
              }),
              overlayColor: WidgetStateProperty.all(Colors.transparent)),
          onPressed: onPressed,
          child: padding != null
              ? Padding(
                  padding: padding!,
                  child: Text(
                    text,
                    style: TextStyle(
                        fontSize: textSize,
                        decoration: TextDecoration.none,
                        color: textColor,
                        fontWeight: textFontWeight),
                  ),
                )
              : Text(
                  text,
                  style: TextStyle(
                      fontSize: textSize,
                      color: textColor,
                      decoration: TextDecoration.none,
                      fontWeight: textFontWeight),
                ),
        ));
  }
}

class BDHUserCommmonButton extends StatelessWidget {
  final double width;
  final double height;
  final String title;
  final double? borderRadius;
  final String? icon;
  final Color? color;
  final double? marginTop;
  final double? marginBottom;
  final List<Color>? gradientList;

  const BDHUserCommmonButton({
    super.key,
    required this.width,
    required this.height,
    required this.title,
    this.borderRadius,
    this.icon,
    this.color,
    this.marginBottom,
    this.marginTop,
    this.gradientList,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: marginTop ?? 0, bottom: marginBottom ?? 0),
      width: width,
      height: height,
      alignment: Alignment.center,
      decoration: BoxDecoration(
          gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: gradientList ??
                  [
                    Color.fromRGBO(14, 202, 139, 1),
                    Color.fromRGBO(19, 230, 121, 1)
                  ]),
          // color: color ?? const Color.fromRGBO(22, 183, 96, 1),
          borderRadius:
              BorderRadius.all(Radius.circular(borderRadius ?? 4.px))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon != null
              ? Row(
                  children: [
                    Image.asset(width: 24.px, ImageHelper.wrapAssets(icon!)),
                    SizedBox(
                      width: 10.px,
                    )
                  ],
                )
              : Container(),
          Text(
            title,
            style: TextStyle(
                fontFamily: "PingFang SC",
                color: Colors.white,
                fontWeight: FontWeight.w500,
                fontSize: 16.px),
          )
        ],
      ),
    );
  }
}
