import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class BdhRefreshFooter extends StatelessWidget {
  final LoadStatus mode;
  final bool isAllDataLoaded;
  final bool? isNOData;
  final Color textColor;
  const BdhRefreshFooter({
    super.key,
    required this.mode,
    this.isAllDataLoaded = false,
    this.isNOData,
    this.textColor = const Color.fromRGBO(1, 1, 1, 0.6),
  });

  @override
  Widget build(BuildContext context) {
    return CustomFooter(
      builder: (BuildContext context, LoadStatus? mode) {
        Widget body;
        if (mode == LoadStatus.idle) {
          body = isAllDataLoaded
              ? isNOData ?? false
                  ? const Text("")
                  : Text(
                      "已全部加载",
                      style: TextStyle(color: textColor),
                    )
              : const Text("上拉加载");
        } else if (mode == LoadStatus.loading) {
          body = const CupertinoActivityIndicator();
        } else if (mode == LoadStatus.failed) {
          body = Text(
            "加载失败！点击重试！",
            style: TextStyle(color: textColor),
          );
        } else if (mode == LoadStatus.canLoading) {
          body = Text(
            "松手,加载更多!",
            style: TextStyle(color: textColor),
          );
        } else {
          body = Text(
            "没有更多数据了!",
            style: TextStyle(color: textColor),
          );
        }
        return SizedBox(
          height: 55.0,
          child: Center(child: body),
        );
      },
    );
  }
}
