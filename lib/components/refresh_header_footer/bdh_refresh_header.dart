import 'package:flutter/material.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class BdhRefreshHeader extends StatelessWidget {
  final Color? refreshColor;
  const BdhRefreshHeader({super.key, this.refreshColor});

  @override
  Widget build(BuildContext context) {
    return CustomHeader(builder: (context, status) {
      if (status == RefreshStatus.idle) {
        return const SizedBox.shrink();
      }
      return Center(
        child: Container(
            width: 20,
            height: 30,
            padding: const EdgeInsets.only(
              bottom: 10,
            ),
            child: CircularProgressIndicator(
              strokeWidth: 2,
              // color: Colors.white
              // color: Color.fromRGBO(22, 183, 96, 1),
              color: refreshColor ?? const Color.fromRGBO(0, 0, 0, 1),
            )),
      );
    });
  }
}
