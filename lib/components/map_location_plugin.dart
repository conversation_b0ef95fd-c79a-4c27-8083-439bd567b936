import 'package:bdh_smart_agric_app/components/compass.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/gps/gps_receiver.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

class MapLocationButton extends StatefulWidget {
  final Function(Marker) markerCallback;
  final double? xAlign;
  final double? yAlign;
  final bool? autoLocate;

  const MapLocationButton({
    super.key,
    required this.markerCallback,
    this.xAlign,
    this.yAlign,
    this.autoLocate,
  });
  @override
  MapLocationButtonState createState() => MapLocationButtonState();
}

class MapLocationButtonState extends State<MapLocationButton> {
  // bool isActive = false;
  // Marker? currentMarker;
  Function(dynamic)? callback;
  var count = 0;

  @override
  void initState() {
    super.initState();

    //这里监听实时坐标
    // callback = (e) {
    //   var location = e;
    //   var locationMarker = Marker(
    //       width: 39.33.px,
    //       height: 39.33.px,
    //       point: LatLng((location?.latitude ?? 0).toDouble(),
    //           (location?.longitude ?? 0).toDouble()),
    //       child: const CompassView());
    //   widget.markerCallback(locationMarker);
    //   count = count + 1;
    //   // if (count < 2 && widget.autoLocate == true) {
    //   MapController.of(context).move(
    //       LatLng((location.latitude ?? 0).toDouble(),
    //           (location.longitude ?? 0).toDouble()),
    //       18);
    //   //}
    // };
    // bus.on("location", callback!);
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment(widget.xAlign ?? 0.95, widget.yAlign ?? 0.5),
      child: GestureDetector(
        onTap: () {
          LocationResult? locationResult =
              GpsReceiver.getInstance().locationResult;
          if (locationResult != null) {
            var location = locationResult;
            var locationMarker = Marker(
                width: 39.33.px,
                height: 39.33.px,
                point: LatLng((location.latitude ?? 0).toDouble(),
                    (location.longitude ?? 0).toDouble()),
                child: const CompassView());
            widget.markerCallback(locationMarker);
            MapController.of(context).move(
                LatLng((location.latitude ?? 0).toDouble(),
                    (location.longitude ?? 0).toDouble()),
                18);
          }
        },
        child: Container(
          width: 39.33.px,
          height: 39.33.px,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.67.px),
              color: Colors.white),
          alignment: Alignment.center,
          child: Image.asset(
            ImageHelper.wrapAssets("map_locate.png"),
            height: 20.px,
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    bus.off("location", callback);
    super.dispose();
  }
}
