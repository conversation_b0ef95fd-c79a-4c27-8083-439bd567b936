/*
 * <AUTHOR>
 * @description: 下拉菜单组件
 * @date 2025/04/08 14:01:27
*/
import 'package:flutter/material.dart';

/// 自定义下拉菜单方向
enum BdhDropdownMenuDirection {
  down,
  up,
}

/// 自定义下拉菜单选项
class BdhDropdownItemOption {
  /// 显示的标签
  final String label;
  
  /// 选项的值
  final String value;
  
  /// 是否选中
  final bool selected;
  
  BdhDropdownItemOption({
    required this.label,
    required this.value,
    this.selected = false,
  });
}

/// 自定义下拉菜单项
class BdhDropdownItem extends StatefulWidget {
  /// 标题/标签
  final String label;
  
  /// 选项列表
  final List<BdhDropdownItemOption> options;
  
  /// 选择变化时的回调
  final Function(List<String> values) onChange;

  const BdhDropdownItem({
    Key? key,
    required this.label,
    required this.options,
    required this.onChange,
  }) : super(key: key);

  @override
  State<BdhDropdownItem> createState() => _BdhDropdownItemState();
}

class _BdhDropdownItemState extends State<BdhDropdownItem> {
  String _selectedValue = '';
  String _selectedLabel = '';
  bool _isMenuOpened = false;

  @override
  void initState() {
    super.initState();
    _initSelectedValue();
  }

  @override
  void didUpdateWidget(BdhDropdownItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.options != widget.options) {
      _initSelectedValue();
    }
  }

  void _initSelectedValue() {
    for (var option in widget.options) {
      if (option.selected) {
        _selectedValue = option.value;
        _selectedLabel = option.label;
        return;
      }
    }
    
    // 如果没有默认选中项，选择第一个
    if (widget.options.isNotEmpty) {
      _selectedValue = widget.options[0].value;
      _selectedLabel = widget.options[0].label;
    }
  }
  
  void setMenuState(bool isOpened) {
    if (mounted) {
      setState(() {
        _isMenuOpened = isOpened;
      });
    } else {
      _isMenuOpened = isOpened;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 44,
      alignment: Alignment.center,
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            _selectedLabel.isEmpty ? widget.label : _selectedLabel,
            style: TextStyle(
              color: _isMenuOpened ? Theme.of(context).primaryColor : const Color(0xFF333333),
              fontSize: 14,
              fontWeight: _isMenuOpened ? FontWeight.bold : FontWeight.normal,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(width: 4),
          Icon(
            _isMenuOpened ? Icons.arrow_drop_up : Icons.arrow_drop_down,
            color: _isMenuOpened ? Theme.of(context).primaryColor : const Color(0xFF666666),
            size: 20,
          ),
        ],
      ),
    );
  }
}

/// 下拉菜单管理器
class BdhDropdownMenuController extends ChangeNotifier {
  int? _activeIndex;

  /// 当前打开的菜单索引
  int? get activeIndex => _activeIndex;

  /// 切换菜单状态
  void toggle(int index) {
    if (_activeIndex == index) {
      _activeIndex = null;
    } else {
      _activeIndex = index;
    }
    notifyListeners();
  }

  /// 关闭菜单
  void close() {
    _activeIndex = null;
    notifyListeners();
  }
}

/// 自定义下拉菜单
class BdhDropdownMenu extends StatefulWidget {
  /// 下拉菜单项列表
  final List<BdhDropdownItem> items;
  
  /// 下拉方向
  final BdhDropdownMenuDirection direction;
  
  /// 菜单打开时的回调
  final Function(int)? onMenuOpened;
  
  /// 菜单关闭时的回调
  final Function(int)? onMenuClosed;
  
  /// 自定义控制器
  final BdhDropdownMenuController? controller;

  const BdhDropdownMenu({
    Key? key,
    required this.items,
    this.direction = BdhDropdownMenuDirection.down,
    this.onMenuOpened,
    this.onMenuClosed,
    this.controller,
  }) : super(key: key);

  @override
  State<BdhDropdownMenu> createState() => _BdhDropdownMenuState();
}

class _BdhDropdownMenuState extends State<BdhDropdownMenu> {
  late BdhDropdownMenuController _controller;
  final List<GlobalKey<_BdhDropdownItemState>> _menuKeys = [];
  OverlayEntry? _overlayEntry;
  final LayerLink _layerLink = LayerLink();
  
  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? BdhDropdownMenuController();
    
    // 创建每个菜单项的Key
    for (int i = 0; i < widget.items.length; i++) {
      _menuKeys.add(GlobalKey<_BdhDropdownItemState>());
    }
    
    _controller.addListener(_handleControllerChanged);
  }
  
  @override
  void dispose() {
    _removeOverlay();
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_handleControllerChanged);
    }
    super.dispose();
  }
  
  void _handleControllerChanged() {
    // 更新所有菜单项的状态
    for (int i = 0; i < _menuKeys.length; i++) {
      if (i < widget.items.length && _menuKeys[i].currentState != null) {
        // 设置菜单项的打开状态
        _menuKeys[i].currentState!.setMenuState(_controller.activeIndex == i);
      }
    }
    
    if (_controller.activeIndex != null) {
      _showOverlay();
    } else {
      _removeOverlay();
    }
  }
  
  void _showOverlay() {
    _removeOverlay();
    
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final Size size = renderBox.size;
    
    _overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        width: size.width,
        child: CompositedTransformFollower(
          link: _layerLink,
          showWhenUnlinked: false,
          offset: Offset(0.0, size.height),
          child: Material(
            elevation: 4.0,
            child: GestureDetector(
              onTap: () {
                _controller.close();
              },
              child: _buildDropdownPanel(),
            ),
          ),
        ),
      ),
    );
    
    Overlay.of(context)?.insert(_overlayEntry!);
  }
  
  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
  
  void _handleMenuTap(int index) {
    if (_controller.activeIndex == index) {
      _controller.close();
      if (widget.onMenuClosed != null) {
        widget.onMenuClosed!(index);
      }
    } else {
      if (_controller.activeIndex != null && widget.onMenuClosed != null) {
        widget.onMenuClosed!(_controller.activeIndex!);
      }
      
      _controller.toggle(index);
      
      if (widget.onMenuOpened != null) {
        widget.onMenuOpened!(index);
      }
    }
  }

  void _handleOptionSelected(int menuIndex, String value) {
    final item = widget.items[menuIndex];
    // 调用onChange回调
    item.onChange([value]);
    // 关闭菜单
    _controller.close();
    if (widget.onMenuClosed != null) {
      widget.onMenuClosed!(menuIndex);
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: _buildMenuBar(),
    );
  }
  
  Widget _buildMenuBar() {
    return Container(
      height: 44,
      decoration: const BoxDecoration(
        color: Colors.transparent,
        border: Border(
          bottom: BorderSide(
            color: Color(0xFFE0E0E0),
            width: 1,
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: List.generate(widget.items.length, (index) {
          return Expanded(
            child: InkWell(
              onTap: () => _handleMenuTap(index),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  BdhDropdownItem(
                    key: _menuKeys[index],
                    label: widget.items[index].label,
                    options: widget.items[index].options,
                    onChange: widget.items[index].onChange,
                  ),
                  if (index < widget.items.length - 1)
                    Positioned(
                      right: 0,
                      top: 12,
                      bottom: 12,
                      child: Container(
                        width: 1,
                        color: const Color(0xFFEEEEEE),
                      ),
                    ),
                ],
              ),
            ),
          );
        }),
      ),
    );
  }
  
  Widget _buildDropdownPanel() {
    if (_controller.activeIndex == null) return const SizedBox();
    
    final activeIndex = _controller.activeIndex!;
    final activeItem = widget.items[activeIndex];
    final options = activeItem.options;
    
    return Stack(
      children: [
        // 半透明背景遮罩
        Positioned.fill(
          child: GestureDetector(
            onTap: () => _controller.close(),
            child: Container(
              color: Colors.black.withOpacity(0.3),
            ),
          ),
        ),
        Container(
          color: Colors.white,
          constraints: const BoxConstraints(maxHeight: 300),
          child: ListView.builder(
            padding: EdgeInsets.zero,
            shrinkWrap: true,
            itemCount: options.length,
            itemBuilder: (context, index) {
              final option = options[index];
              final isSelected = option.selected;
              
              return InkWell(
                onTap: () => _handleOptionSelected(activeIndex, option.value),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: index == options.length - 1 
                            ? Colors.transparent 
                            : const Color(0xFFEEEEEE),
                        width: 0.5,
                      ),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        option.label,
                        style: TextStyle(
                          color: isSelected ? Theme.of(context).primaryColor : const Color(0xFF333333),
                          fontSize: 14,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                      if (isSelected)
                        Icon(
                          Icons.check,
                          color: Theme.of(context).primaryColor,
                          size: 18,
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
} 