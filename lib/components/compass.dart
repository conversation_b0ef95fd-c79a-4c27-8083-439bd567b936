import 'dart:async';
import 'dart:math';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:sensors_plus/sensors_plus.dart';

class CompassView extends StatefulWidget {
  const CompassView({super.key});

  @override
  State<CompassView> createState() => CompoassViewState();
}

class CompoassViewState extends State<CompassView> {
  late StreamSubscription<MagnetometerEvent> _streamSubscription;
  double quarterTurns = 0;
  @override
  void initState() {
    super.initState();
    _streamSubscription =
        magnetometerEventStream().listen((MagnetometerEvent event) {
      double D = 360 - atan2(event.x, event.y) * (180 / pi);
      if (D < 0) {
        D += 360;
      }
      // print(D);
      setState(() {
        quarterTurns = (D - 360) / 180 * pi;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Transform.rotate(
      angle: quarterTurns,
      child: Image.asset(
        ImageHelper.wrapAssets("locate_nav.png"),
        width: 40.px,
        height: 40.px,
      ),
    );
  }

  @override
  void dispose() {
    _streamSubscription.cancel();
    super.dispose();
  }
}
