import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_segment_line.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

import '../utils/image_util.dart';

class BdhShareView extends StatefulWidget {
  final Function() shareWechatCallBack;
  final Function() shareWechatMomentsCallBack;
  const BdhShareView(
      {super.key,
      required this.shareWechatCallBack,
      required this.shareWechatMomentsCallBack});

  @override
  State<BdhShareView> createState() => _BdhShareViewState();
}

class _BdhShareViewState extends State<BdhShareView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          // border: Border.all(width: 1),
          // color: const Color.fromRGBO(255, 255, 255, 1),
          color: Colors.white,
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(9.px), topRight: Radius.circular(9.px))),
      height: 180.px + MediaQuery.of(context).padding.bottom,
      child: Column(
        children: [
          SizedBox(
            height: 20.px,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                // onTap: widget.shareWechatCallBack,
                onTap: () {
                  Future.delayed(const Duration(microseconds: 500), () {
                    Navigator.of(context).pop();
                    widget.shareWechatCallBack();
                  });
                },
                child: item('shareWechatIcons.png', '微信'),
              ),
              SizedBox(
                width: 68.px,
              ),
              GestureDetector(
                // onTap: widget.shareWechatMomentsCallBack,
                onTap: () {
                  Future.delayed(const Duration(microseconds: 500), () {
                    Navigator.of(context).pop();
                    widget.shareWechatMomentsCallBack();
                  });
                },
                child: item('shareWechatMomentIcons.png', '朋友圈'),
              ),
            ],
          ),

          SizedBox(
            height: 13.px,
          ),

          //线
          BdhSegmentLine(width: 375.px),
          //取消按钮
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              height: 55.px,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: Text(
                "取消",
                style: TextStyle(
                  color: const Color.fromRGBO(0, 0, 0, 0.4),
                  fontSize: 14.px,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          )
        ],
      ),
    );
  } // end build

// item
  Widget item(String imageName, String title) {
    return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(4.px)),
          // border: Border.all(width: 1),
        ),
        child: Column(
          children: [
            Image.asset(
              width: 48.px,
              height: 48.px,
              ImageHelper.wrapAssets(imageName),
            ),
            SizedBox(
              // decoration: BoxDecoration(border: Border.all(width: 1)),
              width: 48.px,
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 13.px,
                    fontWeight: FontWeight.w400,
                    color: const Color.fromRGBO(0, 0, 0, 1)),
              ),
            )
          ],
        ));
  }
} //end state

class BdhShareHorzView extends StatefulWidget {
  final Function() shareWechatCallBack;
  final Function() shareWechatMomentsCallBack;
  const BdhShareHorzView(
      {super.key,
      required this.shareWechatCallBack,
      required this.shareWechatMomentsCallBack});

  @override
  State<BdhShareHorzView> createState() => _BdhShareHorzViewState();
}

class _BdhShareHorzViewState extends State<BdhShareHorzView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          // border: Border.all(width: 1),
          color: const Color.fromRGBO(255, 255, 255, 0.95),
          borderRadius: BorderRadius.only(
              topLeft: Radius.circular(9.px), topRight: Radius.circular(9.px))),
      height: 40.vh + MediaQuery.of(context).padding.bottom,
      width: 100.vw,
      child: Column(
        children: [
          SizedBox(
            height: 5.vh,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                // onTap: widget.shareWechatCallBack,
                onTap: () {
                  Future.delayed(const Duration(microseconds: 500), () {
                    Navigator.of(context).pop();
                    widget.shareWechatCallBack();
                  });
                },
                child: item('shareWechatIcons.png', '微信'),
              ),
              SizedBox(
                width: 68.px,
              ),
              GestureDetector(
                // onTap: widget.shareWechatMomentsCallBack,
                onTap: () {
                  Future.delayed(const Duration(microseconds: 500), () {
                    Navigator.of(context).pop();
                    widget.shareWechatMomentsCallBack();
                  });
                },
                child: item('shareWechatMomentIcons.png', '朋友圈'),
              ),
            ],
          ),

          SizedBox(
            height: 5.vh,
          ),

          //线
          BdhSegmentLine(width: 100.vw),
          //取消按钮
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              height: 10.vh,
              color: Colors.transparent,
              alignment: Alignment.center,
              child: Text(
                "取消",
                style: TextStyle(
                  color: const Color.fromRGBO(0, 0, 0, 0.4),
                  fontSize: 4.vh,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ),
          )
        ],
      ),
    );
  } // end build

// item
  Widget item(String imageName, String title) {
    return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(4.px)),
          // border: Border.all(width: 1),
        ),
        child: Column(
          children: [
            Image.asset(
              width: 10.vh,
              height: 10.vh,
              ImageHelper.wrapAssets(imageName),
            ),
            SizedBox(
              // decoration: BoxDecoration(border: Border.all(width: 1)),
              width: 20.vh,
              child: Text(
                title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    fontSize: 5.vh,
                    fontWeight: FontWeight.w400,
                    color: const Color.fromRGBO(0, 0, 0, 1)),
              ),
            )
          ],
        ));
  }
} //end state