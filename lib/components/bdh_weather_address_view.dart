import 'package:bdh_smart_agric_app/components/bdh_tag.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/model/weather_model.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/gps/gps_receiver.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/request/home_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

class BdhWeatherAddressView extends StatefulWidget {
  bool onlyShowAddress;
  BdhWeatherAddressView({super.key, required this.onlyShowAddress});

  @override
  State<BdhWeatherAddressView> createState() => _BdhWeatherAddressViewState();
}

class _BdhWeatherAddressViewState extends State<BdhWeatherAddressView> {
  String address = '北屯市';
  num defalutLatitude = 45.76091999999998;
  num defalutLongitude = 126.63125300000002;
  WeatherModel? weatherModel;
  bool showlocate = false;
  String timeStr = '上午';
  @override
  void initState() {
    super.initState();
    timeStr = DateTime.now().hour < 12 ? '上午' : '下午';
    getWeather();
    bus.on("location", (e) {
      setState(() {
        showlocate = true;
      });
      LocationResult locationResult = e as LocationResult;
      getWeatherData(locationResult.latitude ?? 0,
          locationResult.longitude ?? 0, locationResult.addressCity ?? '北屯市');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: widget.onlyShowAddress
          ? Container(
              padding: EdgeInsets.only(bottom: 15.px),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Image.asset(
                        width: 24.px,
                        height: 24.px,
                        ImageHelper.wrapAssets('locate_empty_black.png'),
                      ),
                      SizedBox(width: 2.px),
                      Expanded(
                          child: Row(
                        children: [
                          showlocate
                              ? Text(
                                  address,
                                  maxLines: 1,
                                  style: TextStyle(
                                      overflow: TextOverflow.ellipsis,
                                      fontSize: 15.px,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black),
                                )
                              : GestureDetector(
                                  onTap: () {
                                    PermissionUtil.requestLocationPermission(
                                            context, "以便为您提供所处位置的天气信息")
                                        .then((res) {
                                      if (res == true) {
                                        GpsReceiver receiver =
                                            GpsReceiver.getInstance();
                                        receiver.start(true);
                                      } else {
                                        return BrnDialogManager
                                            .showConfirmDialog(context,
                                                title: "提示",
                                                cancel: '取消',
                                                confirm: '确定',
                                                message:
                                                    "需要您开启定位权限, 是否去开启定位权限？",
                                                onConfirm: () {
                                          Navigator.of(context).pop();
                                          openAppSettings();
                                        }, onCancel: () {
                                          Navigator.of(context).pop();
                                        });
                                      }
                                    });
                                  },
                                  child: const BdhTag(
                                      name: "点击开启定位", color: Colors.grey),
                                )
                        ],
                      ))
                    ],
                  ),
                ],
              ),
            )
          : Row(
              children: [
                SizedBox(
                  width: 139.px,
                  height: 139.px,
                  child: Image(
                    image: const AssetImage('assets/images/chat/ic_farmer.png'),
                    width: 139.px,
                    height: 139.px,
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Image.asset(
                          width: 24.px,
                          height: 24.px,
                          ImageHelper.wrapAssets('locate_empty_black.png'),
                          color: const Color.fromRGBO(120, 175, 189, 1),
                        ),
                        SizedBox(width: 2.px),
                        showlocate
                            ? Text(
                                address,
                                maxLines: 1,
                                style: TextStyle(
                                    overflow: TextOverflow.ellipsis,
                                    fontSize: 15.px,
                                    fontWeight: FontWeight.w500,
                                    color:
                                        const Color.fromRGBO(67, 135, 157, 1)),
                              )
                            : GestureDetector(
                                onTap: () {
                                  PermissionUtil.requestLocationPermission(
                                          context, "以便为您提供所处位置的天气信息")
                                      .then((res) {
                                    if (res == true) {
                                      GpsReceiver receiver =
                                          GpsReceiver.getInstance();
                                      receiver.start(true);
                                    } else {
                                      return BrnDialogManager.showConfirmDialog(
                                          context,
                                          title: "提示",
                                          cancel: '取消',
                                          confirm: '确定',
                                          message: "需要您开启定位权限, 是否去开启定位权限？",
                                          onConfirm: () {
                                        Navigator.of(context).pop();
                                        openAppSettings();
                                      }, onCancel: () {
                                        Navigator.of(context).pop();
                                      });
                                    }
                                  });
                                },
                                child: const BdhTag(
                                    name: "点击开启定位", color: Colors.grey),
                              )
                      ],
                    ),
                    //{下午}好,{晴天}，温度{9}度.下午晴天，温度9℃，记得带件外套，享受优秀的午后时光哟~
                    SizedBox(height: 10.px),
                    showlocate
                        ? SizedBox(
                            width: 209.px,
                            child: Text(
                              '${weatherModel?.weather ?? ''}，温度${weatherModel?.temp ?? ''}°C，愿您拥有愉快的一天！',
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                color: const Color.fromRGBO(16, 46, 83, 0.6),
                                fontSize: 14.px,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          )
                        : Container(),
                  ],
                ),
              ],
            ),
    );
  } //build

  getWeather() {
    Permission.locationWhenInUse.status.then((result) {
      if (result == PermissionStatus.granted) {
        setState(() {
          showlocate = true;
        });
      }
    });

    Future.delayed(const Duration(seconds: 1), () {
      GpsReceiver receiver = GpsReceiver.getInstance();
      if (receiver.locationResult != null) {
        // Logger().i('定位天气');
        getWeatherData(
            receiver.locationResult!.latitude ?? 0,
            receiver.locationResult!.longitude ?? 0,
            receiver.locationResult!.address ?? '北屯市');
      } else {
        // Logger().i('默认北屯天气');
        getWeatherData(defalutLatitude, defalutLongitude, address);
      }
    });
  }

  getWeatherData(num latitude, num longitude, String locationAddress) {
    HomeService.getWeatherData(latitude, longitude).then((res) {
      Logger().i('获取天气res= ');
      Logger().i(res);
      if (res['status'] == 200) {
        final weatherData = res['data'];
        WeatherModel model = WeatherModel.fromJson(weatherData);
        setState(() {
          weatherModel = model;
          address = locationAddress;
        });
      }
    });
  }
} //state
