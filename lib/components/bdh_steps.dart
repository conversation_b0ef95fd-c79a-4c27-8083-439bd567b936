import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';

class BdhStepsItemData {
  BdhStepsItemData({required this.title, this.content});

  /// 标题
  final String title;
  final String? content;
}

class BdhStepsHorizontalItem extends StatelessWidget {
  final BdhStepsItemData data;
  final int index;
  final int stepsCount;
  final int activeIndex;

  const BdhStepsHorizontalItem({
    super.key,
    required this.data,
    required this.index,
    required this.stepsCount,
    required this.activeIndex,
  });

  @override
  Widget build(BuildContext context) {
    /// 步骤条数字背景色

    /// 步骤条数字颜色
    var stepsNumberTextColor = Colors.white;

    /// 步骤条标题颜色
    var stepsTitleColor = Colors.black;

    /// 激活索引大于当前索引
    if (activeIndex > index) {
      stepsNumberTextColor = Colors.white;
      stepsTitleColor = Colors.black;
    } else if (activeIndex < index) {
      /// 激活索引小于当前索引
      stepsNumberTextColor = Colors.white;
      stepsTitleColor = Colors.black;
    }

    /// 步骤条icon图标组件，默认为索引文字
    Widget? stepsIconWidget = Text(
      (index + 1).toString(),
      style: TextStyle(
        color: stepsNumberTextColor,
        fontWeight: FontWeight.w700,
        fontSize: 10.px,
      ),
    );

    // icon组件容器大小
    // double iconContainerSize = 22;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              flex: 1,
              child: Opacity(
                opacity: index == 0 ? 0 : 1,
                child: DottedLine(
                  lineLength: double.infinity,
                  dashColor: activeIndex >= index ? Colors.green : Colors.grey,
                ),
              ),
            ),
            activeIndex < index
                ? Container(
                    width: 21.px,
                    height: 21.px,
                    margin: EdgeInsets.all(5.px),
                    alignment: Alignment.center,
                    child: Container(
                      width: 16.px,
                      height: 16.px,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          color: Colors.grey,
                          borderRadius:
                              BorderRadius.all(Radius.circular(8.px))),
                      child: stepsIconWidget,
                    ),
                  )
                : Container(
                    width: 21.px,
                    height: 21.px,
                    margin: EdgeInsets.all(5.px),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                        color: const Color.fromRGBO(10, 174, 108, 0.2),
                        borderRadius:
                            BorderRadius.all(Radius.circular(10.5.px))),
                    child: Container(
                      width: 16.px,
                      height: 16.px,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          color: const Color.fromRGBO(10, 174, 108, 1),
                          borderRadius:
                              BorderRadius.all(Radius.circular(8.px))),
                      child: stepsIconWidget,
                    ),
                  ),
            Expanded(
              flex: 1,
              child: Opacity(
                opacity: index == stepsCount - 1 ? 0 : 1,
                child: DottedLine(
                  lineLength: double.infinity,
                  dashColor: activeIndex >= index ? Colors.green : Colors.grey,
                ),
              ),
            ),
          ],
        ),
        Container(
          alignment: Alignment.center,
          child: Text(
            data.title,
            style: TextStyle(
              fontWeight: FontWeight.w400,
              color: stepsTitleColor,
              fontSize: 12.px,
            ),
          ),
        ),
      ],
    );
  }
}

class BdhStepsHorizontal extends StatelessWidget {
  final List<BdhStepsItemData> steps;
  final int activeIndex;

  const BdhStepsHorizontal({
    super.key,
    required this.steps,
    required this.activeIndex,
  });

  @override
  Widget build(BuildContext context) {
    final stepsCount = steps.length;
    List<Widget> stepsHorizontalItem = steps.asMap().entries.map((item) {
      return Expanded(
        flex: 1,
        child: BdhStepsHorizontalItem(
          index: item.key,
          data: item.value,
          stepsCount: stepsCount,
          activeIndex: activeIndex,
        ),
      );
    }).toList();

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: stepsHorizontalItem,
    );
  }
}

class BdhStepsVerticalItem extends StatelessWidget {
  final BdhStepsItemData data;
  final int index;
  final int stepsCount;
  final int activeIndex;

  const BdhStepsVerticalItem({
    super.key,
    required this.data,
    required this.index,
    required this.stepsCount,
    required this.activeIndex,
  });

  @override
  Widget build(BuildContext context) {
    /// 步骤条数字背景色

    /// 步骤条数字颜色
    var stepsNumberTextColor = Colors.green;

    /// 步骤条标题颜色
    var stepsTitleColor = Colors.black;

    /// 激活索引大于当前索引
    if (activeIndex > index) {
      stepsNumberTextColor = Colors.green;
      stepsTitleColor = Colors.black;
    } else if (activeIndex < index) {
      /// 激活索引小于当前索引
      stepsNumberTextColor = Colors.grey;
      stepsTitleColor = Colors.black;
    }

    /// 步骤条icon图标组件，默认为索引文字
    Widget? stepsIconWidget = Container(
      width: 9.px,
      height: 9.px,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(4.5.px)),
          border: Border.all(width: 2.px, color: stepsNumberTextColor)),
    );

    /// icon组件容器大小
    double iconContainerSize = 22;

    /// icon组件容器margin
    double iconMarginBottom = 8;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: IntrinsicHeight(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(minHeight: 30.px),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    width: iconContainerSize,
                    height: 22,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(bottom: iconMarginBottom),
                    child: stepsIconWidget,
                  ),
                  Expanded(
                    flex: 1,
                    child: Opacity(
                      opacity: index == stepsCount - 1 ? 0 : 1,
                      child: Container(
                        width: 1,
                        height: double.infinity,
                        color: activeIndex > index ? Colors.green : Colors.grey,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: 22,
                    margin: const EdgeInsets.only(bottom: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          data.title,
                          style: TextStyle(
                            fontWeight: (activeIndex == index)
                                ? FontWeight.w600
                                : FontWeight.w400,
                            color: stepsTitleColor,
                            fontSize: 14,
                            height: 1.2,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          data.content ?? "",
                          style: const TextStyle(
                            fontWeight: FontWeight.w400,
                            color: Colors.grey,
                            fontSize: 12,
                          ),
                        ),
                      ]),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class BdhStepsVertical extends StatelessWidget {
  final List<BdhStepsItemData> steps;
  final int activeIndex;

  const BdhStepsVertical({
    super.key,
    required this.steps,
    required this.activeIndex,
  });

  @override
  Widget build(BuildContext context) {
    final stepsCount = steps.length;
    List<Widget> stepsVerticalItem = steps.asMap().entries.map((item) {
      return BdhStepsVerticalItem(
        index: item.key,
        data: item.value,
        stepsCount: stepsCount,
        activeIndex: activeIndex,
      );
    }).toList();

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: stepsVerticalItem,
    );
  }
}
