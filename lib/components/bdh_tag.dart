import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class BdhTag extends StatelessWidget {
  final Color color;
  final String name;
  final EdgeInsets? padding;
  const BdhTag(
      {super.key, required this.name, required this.color, this.padding});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.only(left: 5.px, right: 5.px),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(2.px)),
          color: Color.fromRGBO(color.red, color.green, color.blue, 0.1)),
      child: Text(
        name,
        style: TextStyle(color: color, fontWeight: FontWeight.w500),
      ),
    );
  }
}
