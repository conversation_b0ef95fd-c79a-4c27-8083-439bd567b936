import 'package:flutter/widgets.dart';

class FloatingOverlayController extends ChangeNotifier {
  Offset _offset = Offset.zero;
  Offset get offset => _offset;

  set offset(Offset offset) {
    _offset = offset;
    notifyListeners();
  }

  bool _showing = false;
  bool get showing => _showing;
  set showing(bool showing) {
    _showing = showing;
    notifyListeners();
  }

  final BuildContext context;
  final WidgetBuilder widgetBuilder;
  final Size widgetSize;
  final EdgeInsets padding;

  late final OverlayState _overlay;

  OverlayEntry? _entry;

  FloatingOverlayController(this.context,
      {required this.widgetBuilder,
      required this.widgetSize,
      this.padding = EdgeInsets.zero,
      Offset? offset,
      bool showing = false}) {
    _offset = offset ?? Offset.zero;
    _showing = showing;

    _overlay = Overlay.of(context);
  }

  void show() {
    if (showing) {
      return;
    }
    _entry ??= OverlayEntry(
      builder: (context) {
        return _OverlayWidget(
          overlayWidgetBuilder: widgetBuilder,
          controller: this,
        );
      },
    );
    _overlay.insert(_entry!);
    showing = true;
  }

  void hide({bool dispose = false}) {
    if (!_showing) {
      return;
    }
    _entry?.remove();
    if (dispose) {
      _entry?.dispose();
      _entry = null;
    }
    showing = false;
  }

  void trigger() {
    if (showing) {
      hide(dispose: true);
    } else {
      show();
    }
  }

  @override
  void dispose() {
    super.dispose();
    hide(dispose: true);
  }
}

class _OverlayWidget extends StatefulWidget {
  final WidgetBuilder overlayWidgetBuilder;
  final FloatingOverlayController controller;
  const _OverlayWidget({
    required this.overlayWidgetBuilder,
    required this.controller,
  });

  @override
  State<_OverlayWidget> createState() => _OverlayWidgetState();
}

class _OverlayWidgetState extends State<_OverlayWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  Animation<Offset>? _animation;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_listener);

    _animationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 100));

    _animationController.addListener(_animateListener);
  }

  @override
  void dispose() {
    super.dispose();
    widget.controller.removeListener(_listener);
    _animationController.dispose();
  }

  void _animateListener() {
    if (_animation?.value != null) {
      widget.controller.offset = _animation!.value;
    }
  }

  void _listener() {
    setState(() {});
  }

  void _animationTo(Offset offset) {
    _animation = Tween<Offset>(
      begin: widget.controller.offset,
      end: offset,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.linear,
    ));
    _animationController
      ..reset()
      ..forward();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(child: LayoutBuilder(builder: (context, constraints) {
      double left = widget.controller.offset.dx;
      double top = widget.controller.offset.dy;

      if (left < widget.controller.padding.left) {
        left = widget.controller.padding.left;
      }

      if (top < widget.controller.padding.top) {
        top = widget.controller.padding.top;
      }

      if (left >
          constraints.maxWidth -
              widget.controller.widgetSize.width -
              widget.controller.padding.right) {
        left = constraints.maxWidth -
            widget.controller.widgetSize.width -
            widget.controller.padding.right;
      }

      if (top >
          constraints.maxHeight -
              widget.controller.widgetSize.height -
              widget.controller.padding.bottom) {
        top = constraints.maxHeight -
            widget.controller.widgetSize.height -
            widget.controller.padding.bottom;
      }

      return Stack(
        fit: StackFit.expand,
        children: [
          Positioned(
            left: left,
            top: top,
            child: GestureDetector(
                onTapDown: (details) {},
                onPanEnd: (details) {
                  final left = widget.controller.offset.dx +
                      widget.controller.widgetSize.width / 2;
                  final offsetLeft = (left > constraints.maxWidth / 2)
                      ? constraints.maxWidth -
                          widget.controller.widgetSize.width
                      : 0.0;
                  final offset =
                      Offset(offsetLeft, widget.controller.offset.dy);

                  _animationTo(offset);
                },
                onPanUpdate: (details) {
                  Offset newOffset = widget.controller.offset + details.delta;
                  // 限制边界
                  newOffset = Offset(
                    newOffset.dx.clamp(
                        0,
                        constraints.maxWidth -
                            widget.controller.widgetSize.width),
                    newOffset.dy.clamp(
                        0,
                        constraints.maxHeight -
                            widget.controller.widgetSize.height),
                  );
                  widget.controller.offset = newOffset;
                },
                child: SizedBox.fromSize(
                    size: widget.controller.widgetSize,
                    child: widget.overlayWidgetBuilder(context))),
          ),
        ],
      );
    }));
  }
}
