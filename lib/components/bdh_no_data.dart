import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class BdhNoData extends StatelessWidget {
  final String desc;
  final TextStyle? textStyle;
  final bool showImage;
  const BdhNoData(
      {super.key, required this.desc, this.textStyle, this.showImage = true});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (showImage) ...[
          Image.asset(
              width: 180.px,
              height: 151.px,
              ImageHelper.wrapAssets("no_data.png")),
          SizedBox(
            height: 10.px,
          )
        ],
        Text(
          desc,
          style:
              textStyle ?? const TextStyle(color: Color.fromRGBO(0, 0, 0, 0.4)),
        )
      ],
    );
  }
}
