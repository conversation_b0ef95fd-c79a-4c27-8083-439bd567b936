import 'dart:typed_data';

import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

class VideoThumbnailWidget extends StatefulWidget {
  final Function(String) uploadSuccess;
  final String url;
  const VideoThumbnailWidget(
      {super.key, required this.url, required this.uploadSuccess});

  @override
  State<StatefulWidget> createState() => _VideoThumbnailState();
}

class _VideoThumbnailState extends State<VideoThumbnailWidget> {
  Uint8List? data;
  @override
  void initState() {
    super.initState();
    getData();
  }

  @override
  Widget build(BuildContext context) {
    return data != null
        ? ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(4.px)),
            child: Image.memory(data!),
          )
        : const Center(
            child: Text("加载封面中..."),
          );
  }

  getData() {
    VideoThumbnail.thumbnailData(
            video: "${urlConfig.microfront}${widget.url}", quality: 5)
        .then((res) {
      setState(() {
        data = res;
      });
      //直接将封面上传
      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(res!,
            filename: "${DateTime.timestamp()}.png",
            contentType: MediaType("image", "png"))
      });

      BDHResponsitory.uploadFile(postData).then((value) {
        widget.uploadSuccess(value.url ?? "");
      });
    });
  }
}
