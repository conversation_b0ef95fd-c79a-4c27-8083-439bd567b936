import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import 'package:tdesign_flutter/src/util/auto_size.dart';

import '../utils/log.dart';

/// 单选框按钮,继承自TDCheckbox，字段含义与父类一致
class BdhRadio extends TDCheckbox {
  /// 单选框按钮样式
  final TDRadioStyle radioStyle;

  final double iconSize;

  const BdhRadio(
      {super.id,
      super.key,
      super.title,
      super.titleFont,
      super.subTitle,
      super.subTitleFont,
      super.enable,
      int super.subTitleMaxLine,
      int super.titleMaxLine = 1,
      super.selectColor,
      super.disableColor,
      super.customContentBuilder,
      super.spacing,
      bool? cardMode,
      bool? showDivider,
      super.size,
      this.radioStyle = TDRadioStyle.circle,
      this.iconSize = 24,
      super.contentDirection,
      super.customIconBuilder,
      super.titleColor,
      super.subTitleColor,
      super.backgroundColor,
      super.insetSpacing = 0,
      super.onCheckBoxChanged,
      super.checked,
      super.checkBoxLeftSpace})
      : super(cardMode: cardMode ?? false, showDivider: showDivider ?? true);

  @override
  Widget buildDefaultIcon(
      BuildContext context, TDCheckboxGroupState? groupState, bool isChecked) {
    if (cardMode == true) {
      return Container();
    }
    TDRadioStyle? style;
    if (groupState is BdhRadioGroupState) {
      style = (groupState.widget as BdhRadioGroup).radioCheckStyle;
    }

    style = style ?? radioStyle;

    final theme = TDTheme.of(context);

    // 由于镂空圆没有现成icon，因而自己画一个`
    if (style == TDRadioStyle.hollowCircle) {
      return SizedBox(
        width: iconSize,
        height: iconSize,
        child: CustomPaint(
          painter: BdhHollowCircle(!enable
              ? (isChecked ? theme.brandDisabledColor : theme.grayColor4)
              : isChecked
                  ? selectColor ?? theme.brandNormalColor
                  : theme.grayColor4),
        ),
      );
    }

    IconData? iconData;
    switch (style) {
      case TDRadioStyle.check:
        iconData = isChecked ? TDIcons.check : null;
        break;
      case TDRadioStyle.square:
        iconData =
            isChecked ? TDIcons.check_rectangle_filled : TDIcons.rectangle;
        break;
      default:
        iconData = isChecked ? TDIcons.check_circle_filled : TDIcons.circle;
        break;
    }
    if (iconData != null) {
      return Icon(iconData,
          size: iconSize,
          color: !enable
              ? (isChecked
                  ? (disableColor ?? theme.brandDisabledColor)
                  : theme.grayColor4)
              : isChecked
                  ? selectColor ?? theme.brandNormalColor
                  : theme.grayColor4);
    } else {
      return SizedBox(
        width: iconSize,
        height: iconSize,
      );
    }
  }

  @override
  State<StatefulWidget> createState() {
    return BdhRadioState();
  }
}

class BdhRadioState extends TDCheckboxState {
  @override
  Widget build(BuildContext context) {
    // 检查是否包含在FuiCheckBoxGroup内，如果是的话，状态由Group管理
    final groupState = TDCheckboxGroupInherited.of(context)?.state;
    if (groupState is BdhRadioGroupState) {
      final strictMode = (groupState.widget as BdhRadioGroup).strictMode;
      // 严格模式下不能取消选项，只能切换
      if (strictMode == true) {
        canNotCancel = true;
      }
    }
    return super.build(context);
  }
}

class BdhHollowCircle extends CustomPainter {
  BdhHollowCircle(this.color);

  // 绘制颜色
  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint()
      ..color = color
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke;
    canvas.drawCircle(const Offset(10.5, 10.5), 10.5, paint);
    paint.style = PaintingStyle.fill;
    canvas.drawCircle(const Offset(10.5, 10.5), 6, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// RadioGroup分组对象，继承自TDCheckboxGroup，字段含义与父类一致
/// RadioGroup应该嵌套在RadioGroup内，所有在RadioGroup的RadioButton只能有一个被选中
///
/// cardMode: 使用卡片样式，需要配合direction 和 directionalTdRadios 使用，
/// 组合为横向、纵向卡片，同时需要在每个TDRadio上设置cardMode参数。
class BdhRadioGroup extends TDCheckboxGroup {
  /// 严格模式下，用户不能取消勾选，只能切换选择项，
  final bool strictMode;

  /// 勾选样式
  final TDRadioStyle? radioCheckStyle;

  /// 是否显示下划线
  final bool showDivider;

  /// 自定义下划线
  final Widget? divider;

  ///每行几列
  final int rowCount;

  BdhRadioGroup({
    super.key,
    Widget? child, // 使用child 则请勿设置direction
    Axis? direction, // direction 对 directionalTdRadios 起作用
    List<BdhRadio>? directionalTdRadios,
    String? selectId, // 默认选择项的id
    bool? passThrough, // 非通栏单选样式 用于使用child 或 direction == Axis.vertical 场景
    bool cardMode = false,
    this.strictMode = true,
    this.radioCheckStyle,
    super.titleMaxLine, // item的行数
    super.customIconBuilder,
    super.customContentBuilder,
    super.spacing, // icon和文字距离
    this.rowCount = 1,
    super.contentDirection,
    OnRadioGroupChange? onRadioGroupChange, // 切换监听
    this.showDivider = false,
    this.divider,
  })  : assert(() {
          // 使用direction属性则必须配合directionalTdRadios，child字段无效
          if (direction != null && directionalTdRadios == null) {
            throw FlutterError(
                '[BdhRadioGroup] direction and directionalTdRadios must set at the same time');
          }
          // 未使用direction则必须设置child
          if (direction == null && child == null) {
            throw FlutterError(
                '[BdhRadioGroup] direction means use child as the exact one, but child is null');
          }
          // 横向单选框 每个选项有字数限制
          if (direction == Axis.horizontal && directionalTdRadios != null) {
            for (var element in directionalTdRadios) {
              if (element.subTitle != null) {
                throw FlutterError(
                    'horizontal radios style should not have subTilte, '
                    'because there left no room for it');
              }
            }
            var maxWordCount = 2;
            var tips =
                '[TDRadioGroup] radio title please not exceed $maxWordCount words.\n'
                '2tabs: 7words maximum\n'
                '3tabs: 4words maximum\n'
                '4tabs: 2words maximum';
            if (directionalTdRadios.length == 2) {
              maxWordCount = 7;
            }
            if (directionalTdRadios.length == 3) {
              maxWordCount = 4;
            }
            if (directionalTdRadios.length == 4) {
              maxWordCount = 2;
            }
            for (var radio in directionalTdRadios) {
              if ((radio.title?.length ?? 0) > maxWordCount) {
                throw FlutterError(tips);
              }
            }
          }
          // 卡片模式要求每个TDRadio必须设置cardMode属性为true，且不能有子标题（空间不够）
          if (cardMode == true) {
            assert(direction != null && directionalTdRadios != null);
            directionalTdRadios!.forEach((element) {
              // if use cardMode at TDRadioGroup, then every TDRadio should
              // set it's own carMode to true.
              if (element.cardMode == false) {
                throw FlutterError(
                    'if use cardMode at TDRadioGroup, then every '
                    'TDRadio should set it\'s own carMode to true.');
              }
              if (element.subTitle != null && direction == Axis.horizontal) {
                throw FlutterError(
                    'horizontal card style should not have subTilte, '
                    'because there left no room for it');
              }
            });
          }
          return true;
        }()),
        super(
          child: Container(
            clipBehavior: (passThrough ?? false) && direction != Axis.horizontal
                ? Clip.hardEdge
                : Clip.none,
            decoration: (passThrough ?? false) && direction != Axis.horizontal
                ? BoxDecoration(borderRadius: BorderRadius.circular(10))
                : null,
            margin: (passThrough ?? false) && direction != Axis.horizontal
                ? const EdgeInsets.symmetric(horizontal: 16)
                : null,
            child: direction == null
                ? child!
                : (direction == Axis.vertical
                    ? ListView.separated(
                        padding: const EdgeInsets.all(0),
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        itemBuilder: (BuildContext context, int index) {
                          return Container(
                            margin: cardMode
                                ? const EdgeInsets.symmetric(horizontal: 16)
                                : null,
                            height: cardMode ? 82 : null,
                            child: directionalTdRadios[index],
                          );
                        },
                        itemCount: directionalTdRadios!.length,
                        separatorBuilder: (BuildContext context, int index) {
                          if (cardMode) {
                            return const SizedBox(
                              height: 12,
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      )
                    : Container(
                        margin: cardMode
                            ? const EdgeInsets.symmetric(horizontal: 16)
                            : null,
                        height: cardMode
                            ? (directionalTdRadios!.length / rowCount).ceil() *
                                (56 + 10)
                            : null,
                        // height: 56,
                        alignment: cardMode ? Alignment.topLeft : null,
                        child: cardMode
                            ? GridView.builder(
                                itemCount: directionalTdRadios!.length,
                                gridDelegate:
                                    SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisSpacing: 10.0,
                                  mainAxisSpacing: 10.0,
                                  crossAxisCount: rowCount, //一行的 Widget 数量
                                  mainAxisExtent: 56,
                                ),
                                itemBuilder: (BuildContext context, int index) {
                                  return SizedBox(
                                    width: 160.scale,
                                    height: 56,
                                    child: directionalTdRadios[index],
                                  );
                                })
                            : Column(
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: directionalTdRadios!
                                        .map((e) => Expanded(child: e))
                                        .toList(),
                                  ),
                                  if (showDivider)
                                    divider ??
                                        const TDDivider(
                                          margin: EdgeInsets.only(left: 16),
                                        )
                                ],
                              ),
                      )),
          ),
          onChangeGroup: (ids) {
            onRadioGroupChange?.call(ids.isNotEmpty ? ids[0] : null);
          },
          controller: null,
          checkedIds: selectId != null ? [selectId] : null,
          maxChecked: 1,
          style: null,
        );

  @override
  State<StatefulWidget> createState() {
    return BdhRadioGroupState();
  }
}

class BdhRadioGroupState extends TDCheckboxGroupState {
  @override
  bool toggle(String id, bool check, [bool notify = false]) {
    checkBoxStates.forEach((key, value) {
      checkBoxStates[key] = false;
    });
    return super.toggle(id, check, true);
  }
}
