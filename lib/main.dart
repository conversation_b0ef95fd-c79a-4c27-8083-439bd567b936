import 'package:bdh_smart_agric_app/manager/provider_manager.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/utils/appcontext.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_config.dart';
import 'package:bdh_smart_agric_app/viewmodel/locale_model.dart';
import 'package:bdh_smart_agric_app/viewmodel/theme_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:loader_overlay/loader_overlay.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import 'package:leak_tracker/leak_tracker.dart';

void main() async {
  //_initLeakTracking();
  //开启默认日志
  // Log.enableDefault();
  //开启网络请求日志
  // Log.enableApi();
  Provider.debugCheckInvalidValueType = null;
  WidgetsFlutterBinding.ensureInitialized();
  await StorageManager.init();
  // await initUrlConfig();
  GlobalServiceView.initKey();
  runApp(const MyApp());

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  // 设置系统底部导航栏颜色
  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    systemNavigationBarColor: Colors.black,
  ));
}

//在 debug 模式下会持续检查内存溢出情况,其他模式下无效果
//https://github.com/dart-lang/leak_tracker/blob/main/doc/leak_tracking/CONCEPTS.md
void _initLeakTracking() {
  LeakTracking.phase = const PhaseSettings(
      ignoredLeaks: IgnoredLeaks(
          //很多都是第三方插件提示的,暂时先屏蔽掉吧
          notDisposed: IgnoredLeaksSet.byClass({
        //package:bruno/src/components/picker/brn_multi_picker.dart:212:75
        "FixedExtentScrollController": null,
        //FadeWidget
        "CurvedAnimation": null,
        //TDToast
        "OverlayEntry": null,
        "ValueNotifier<_OverlayEntryWidgetState?>": null,
        "TextPainter": null,
        //package:audio_video_progress_bar/audio_video_progress_bar.dart
        "_EagerHorizontalDragGestureRecognizer": null
      })),
      //打印保存堆栈信息,用于跟踪内存泄露的位置
      leakDiagnosticConfig: LeakDiagnosticConfig(
          collectRetainingPathForNotGCed: true,
          collectStackTraceOnStart: true,
          collectStackTraceOnDisposal: true));
  LeakTracking.start(config: LeakTrackingConfig(onLeaks: (s) async {
    //打印当前内存溢出整体情况
    debugPrint("onLeak: ${s.toJson()}");

    //收集所有内存溢出异常,调用这个方法后会清空LeakTracking里的所有已收集的异常
    final leaks = await LeakTracking.collectLeaks();

    //一些组件需要在 Widget 回收之后调用其 dispose 方法，通过此可以检测出未调用 dispose 方法的组件
    //a disposable object was GCed, without being disposed first.
    //This means that the object's disposable content is using memory after the object is no longer needed.
    for (var n in leaks.notDisposed) {
      debugPrint(n.toYaml("内存溢出检查 notDisposed:", phasesAreTests: true));
    }
    //统计未进行内存回收的对象
    //an object was disposed, but not GCed after certain number of GC events.
    //This means that a reference to the object is preventing it from being garbage collected after it's no longer needed.
    for (var n in leaks.notGCed) {
      debugPrint(n.toYaml("内存溢出检查 notGCed:", phasesAreTests: true));
    }
    //统计对象进行了内存回收,但是回收时间不及时
    //an object was disposed and then GCed, but GC happened later than expected.
    //This means the retaining path was holding the object in memory for some period, but then disappeared.
    for (var n in leaks.gcedLate) {
      debugPrint(n.toYaml("内存溢出检查gcedLate:", phasesAreTests: true));
    }
  }));
  FlutterMemoryAllocations.instance.addListener(
      (ObjectEvent event) => LeakTracking.dispatchObjectEvent(event.toMap()));
}

final RouteObserver<ModalRoute> routeObserver = RouteObserver<ModalRoute>();

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    AppContext().setContext(context);
    return ScreenConfig(
      builder: () {
        return OKToast(
            duration: const Duration(milliseconds: 1500),
            child: MultiProvider(
              providers: providers,
              child: Consumer2<ThemeModel, LocaleModel>(
                builder: (context, themeModel, localeModel, child) {
                  return RefreshConfiguration(
                    child: ScreenConfig(
                      builder: () {
                        return LoaderOverlay(
                            closeOnBackButton: true,
                            useDefaultLoading: false,
                            overlayWidgetBuilder: (_) {
                              //ignored progress for the moment
                              return const Center(
                                // 圆形
                                // child: CircularProgressIndicator(),
                                child: SpinKitCubeGrid(
                                  color: Colors.green,
                                  size: 50.0,
                                ),
                              );
                            },
                            child: MaterialApp(
                              title: "数字北大荒",
                              debugShowCheckedModeBanner: false,
                              localizationsDelegates: const [
                                RefreshLocalizations.delegate,
                                GlobalMaterialLocalizations.delegate,
                                GlobalWidgetsLocalizations.delegate,
                                GlobalCupertinoLocalizations.delegate
                              ],
                              locale: const Locale('zh', 'CN'),
                              supportedLocales: const [
                                Locale('en', 'US'), // 美国英语
                                Locale('zh', 'CN'), // 中文简体
                              ],
                              theme: themeModel.themeData(),
                              darkTheme: themeModel.themeData(),
                              onGenerateRoute: MyRouter.generateRoute,
                              initialRoute: RouteName.splash,
                              navigatorKey: GlobalServiceView.globalServiceKey,
                              navigatorObservers: [routeObserver],
                            ));
                      },
                    ),
                  );
                },
              ),
            ));
      },
    );
  }
}
