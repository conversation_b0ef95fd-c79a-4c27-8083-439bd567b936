import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:flutter/material.dart';

class UserModel extends ChangeNotifier {
  //用户信息
  UserInfo? _user;
  UserInfo? get user => _user;
  bool get hasUser => user != null;

  saveUser(UserInfo user) {
    _user = user;
  }
  //黑白主题

  bool? _isDark;
  bool? get isDark => _isDark;
  setIsDark(bool dark) {
    _isDark = dark;
  }

  //粮食大类字典
  DictList? _cropList;
  DictList? get cropList => _cropList;
  setCropList(DictList list) {
    _cropList = list;
  }

//粮食大类字典
  DictList? _partTypeList;
  DictList? get partyType => _partTypeList;
  setPartyType(DictList list) {
    _partTypeList = list;
  }

  //当前组织机构
  Map<dynamic, dynamic>? _currentOrg;
  Map<dynamic, dynamic>? get currentOrg => _currentOrg;
  setCurrentOrg(Map<dynamic, dynamic> org) {
    _currentOrg = org;
  }

  notify() {
    notifyListeners();
  }
}
