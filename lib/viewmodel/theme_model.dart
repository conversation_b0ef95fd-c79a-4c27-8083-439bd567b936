import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class ThemeModel extends ChangeNotifier {
  themeData() {
    var themeData = ThemeData(
      brightness: Brightness.light,
      // primaryColor: Colors.green,
      primaryColor: const Color.fromRGBO(47, 149, 255, 1),
      primarySwatch: Colors.primaries[9],
      dialogBackgroundColor: const Color.fromRGBO(47, 149, 255, 1),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: Color.fromRGBO(47, 149, 255, 1),
      ),
      fontFamily: "PingFang SC",
      appBarTheme: AppBarTheme(
          scrolledUnderElevation: 0,
          centerTitle: true,
          titleTextStyle: TextStyle(
              fontSize: 16.px,
              color: Colors.black,
              fontWeight: FontWeight.w600),
          backgroundColor: Colors.white,
          iconTheme:
              const IconThemeData(color: Colors.black, size: 18, weight: 600)),
      iconTheme: const IconThemeData(color: Colors.grey),
      indicatorColor: const Color.fromRGBO(47, 149, 255, 1),
      scaffoldBackgroundColor: Colors.white,
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      platform: TargetPlatform.iOS,
    );

    return themeData;
  }

  themeDataDark() {
    var themeData = ThemeData(
        brightness: Brightness.dark,
        primaryColor: const Color.fromARGB(255, 48, 48, 48),
        primarySwatch: Colors.primaries[9],
        dialogBackgroundColor: Colors.black,
        fontFamily: "PingFang SC",
        appBarTheme: const AppBarTheme(
            centerTitle: true,
            titleTextStyle: TextStyle(fontSize: 18, color: Colors.white),
            backgroundColor: Color.fromARGB(255, 48, 48, 48),
            iconTheme: IconThemeData(color: Colors.white, size: 18)),
        iconTheme: const IconThemeData(color: Colors.grey),
        indicatorColor: const Color.fromRGBO(47, 149, 255, 1),
        scaffoldBackgroundColor: const Color.fromARGB(255, 48, 48, 48));

    return themeData;
  }
}
