import 'dart:ui';
import 'dart:ui' as ui;
import 'dart:typed_data';

import 'package:flutter/services.dart';

class ImageUtils {
  static Future<ui.Image> getImage(String asset) async {
    ByteData data = await rootBundle.load(asset);
    Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List());
    FrameInfo fi = await codec.getNextFrame();
    return fi.image;
  }

  static Future<Uint8List> getImageUint8List(String asset) async {
    // ByteData bytes = await rootBundle.load(asset);
    // var imageList = bytes.buffer.asUint8List();
    // return imageList;
    try {
      ByteData imageData = await rootBundle.load(asset);
      return imageData.buffer.asUint8List();
    } catch (e) {
      print("Failed to load image from path $asset: $e");
      throw e;
    }
  }
}
