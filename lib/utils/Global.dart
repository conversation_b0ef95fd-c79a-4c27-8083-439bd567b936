import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/services.dart';

import 'ImageUtils.dart';
import 'enum_util.dart';
import 'image_util.dart';

class Global {
  static String appVersion = "";


  static Map<weatherCategory, ui.Image> weatherImages = {};



  void fetchWeatherImages() async {
    // ignore: avoid_function_literals_in_foreach_calls
    for (var element in weatherCategory.values) {
      weatherImages[element] = await ImageUtils.getImage(element.getWeatherImage());
    }
  }


  String getDeviceName(String iotDeviceType) {
    switch (iotDeviceType) {
      case '101002':
      case '105001':
      case '105003':
      case '105005':
        return '墒情';
      case '101001':
      case '101005':
      case '101006':
      case '101007':
        return '气象';
      case '103002':
        return '孢子';
      case '103001':
        return '虫情';
      case '108001':
      case '108002':
        return '摄像头';
      case '105012':
      case '101010':
        return '养分';
      case '105006':
        return '闸门';
      case '105008':
        return '蝶阀';
      case '土壤取样':
        return '土壤取样';
      default:
        return '默认图片';
    }
  }
}

///墒情
// 101002 土壤探测器
// 105001 水位检测设备
// 105003 泥温传感器
// 105005 渗压

// 气象
// 101001 气象站(小型)
// 101005 微风
// 101006 雨量计
// 101007 环境监测

// 孢子
// 103002 孢子仪

// 虫情
// 103001 虫情

// 性诱

// 摄像头
// 108001 视频摄像头
// 108002 仓储摄像头

// 养分
// 105012 水质检测

// 闸门
// 105006 水闸

// 蝶阀
// 105008 电磁阀(水肥)

// 虫情
// 103001 虫情

// 101001 气象站(小型)
// 101002 土壤探测器
// 101003 气体监测设备
// 101004 霜冻
// 101005 微风
// 101006 雨量计
// 101007 环境监测
// 101011 粮堆测温设备
// 102001 叶龄诊断设备
// 102002 作物监测
// 102003 多光谱
// 103001 虫情
// 103002 孢子仪
// 103003 马铃薯晚疫病监测
// 103004 小麦赤霉病监测
// 104001 大棚环境监测
// 104002 放风机
// 105001 水位检测设备
// 105002 水温传感器
// 105003 泥温传感器
// 105004 液位传感器
// 105005 渗压
// 105006 水闸
// 105007 水泵
// 105008 电磁阀(水肥)
// 105009 流量计
// 105010 水肥设备
// 105011 控制柜
// 105012 水质检测
// 105013 水位流量计
// 106001 无人机
// 107001 定位
// 107002 产量分析
// 107003 播种监测
// 107004 无人农机
// 107005 智能喷雾监测
// 107006 深翻设备
// 108001 视频摄像头
// 108002 仓储摄像头
