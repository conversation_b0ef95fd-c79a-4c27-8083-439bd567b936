import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/utils/collection_extensions.dart';

class DicUtil {
  static String dictNameToCode(String name, List<DictNode> nodes) {
    var code = "xxx";
    for (DictNode node in nodes) {
      if (node.name == name) {
        code = node.code ?? "";
      }
    }
    return code;
  }

  static String dictCodeToName(String code, List<DictNode> nodes) {
    var name = "xxx";
    for (DictNode node in nodes) {
      if (node.code == code) {
        name = node.name ?? "";
      }
    }
    return name;
  }

  static DictNode? codeToDict(String? code, List<DictNode> list) {
    if (code == null) {
      return null;
    }
    DictNode? getBankByCode =
        list.firstWhereOrNull((test) => test.code == code.toString());

    return getBankByCode;
  }

  DictNode? getDickByCode(String? code, List<DictNode> list) {
    if (code == null) {
      return null;
    }
    DictNode? dict =
        list.firstWhereOrNull((test) => test.code == code.toString());

    return dict;
  }
}
