import 'dart:io';

import 'package:bdh_smart_agric_app/utils/gps/gps_receiver.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications_plus/flutter_local_notifications_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:device_info_plus/device_info_plus.dart';

class PermissionUtil {
  //相机权限
  static Future<bool> requestCameraPermission(
      BuildContext ctx, String desc) async {
    var status = await Permission.camera.status;
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      showDialog(
          context: ctx,
          builder: (ctx) {
            return Align(
              alignment: Alignment.topCenter,
              child: Container(
                padding: EdgeInsets.all(8.px),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(8.px))),
                width: 300.px,
                height: 100.px,
                child: Text("数字北大荒正在申请您的相机权限，以便让您通过相机来拍摄$desc"),
              ),
            );
          });
      status = await Permission.camera.request();
      if (status == PermissionStatus.granted) {
        Navigator.of(ctx).pop();
        return true;
      } else {
        Navigator.of(ctx).pop();
        return false;
      }
    }
  }

  //麦克风权限
  static Future<bool> requestMicroPhonePermission(
      BuildContext ctx, String desc) async {
    var status = await Permission.microphone.status;
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      showDialog(
          context: ctx,
          builder: (ctx) {
            return Align(
              alignment: Alignment.topCenter,
              child: Container(
                padding: EdgeInsets.all(8.px),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(8.px))),
                width: 300.px,
                height: 100.px,
                child: Text("数字北大荒正在申请您的麦克风权限，以便让您通过麦克风来$desc"),
              ),
            );
          });
      status = await Permission.microphone.request();
      if (status == PermissionStatus.granted) {
        Navigator.of(ctx).pop();
        return true;
      } else {
        Navigator.of(ctx).pop();
        return false;
      }
    }
  }

  //相册权限
  static Future<bool> requestPhotosPermission(
      BuildContext ctx, String desc) async {
    var status;
    if (Platform.isIOS) {
      status = await Permission.photos.status;
    } else if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkInt = androidInfo.version.sdkInt;
      status = await (sdkInt >= 33
          ? Permission.photos.status
          : Permission.storage.status); // 解决低版本安卓手机无法从相册选择图片问题
    }
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      showDialog(
          context: ctx,
          builder: (ctx) {
            return Align(
                alignment: Alignment.topCenter,
                child: Material(
                  color: Colors.transparent,
                  child: Container(
                    padding: EdgeInsets.all(8.px),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(Radius.circular(8.px))),
                    width: 300.px,
                    height: 100.px,
                    child: Text("数字北大荒正在申请您的相册权限，以便让您通过相册来选取$desc"),
                  ),
                ));
          });
      if (Platform.isIOS) {
        status = await Permission.photos.request();
      } else if (Platform.isAndroid) {
        final androidInfo = await DeviceInfoPlugin().androidInfo;
        final sdkInt = androidInfo.version.sdkInt;
        status = await (sdkInt >= 33
            ? Permission.photos.request()
            : Permission.storage.request()); // 解决低版本安卓手机无法从相册选择图片问题
      }
      if (status == PermissionStatus.granted) {
        Navigator.of(ctx).pop();
        return true;
      } else {
        Navigator.of(ctx).pop();
        return false;
      }
    }
  }

  //定位权限
  static Future<bool> requestLocationPermission(
      BuildContext ctx, String desc) async {
    var status = await Permission.locationWhenInUse.status;
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      showDialog(
          context: ctx,
          builder: (ctx) {
            return Align(
                alignment: Alignment.topCenter,
                child: Material(
                  color: Colors.transparent,
                  child: Container(
                    padding: EdgeInsets.all(8.px),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(Radius.circular(8.px))),
                    width: 300.px,
                    height: 100.px,
                    // child: Text("数字北大荒正在申请您的定位权限，以便让您通过位置信息来获取$desc"),
                    child: Text("数字北大荒正在申请您的定位权限，$desc"),
                  ),
                ));
          });
      status = await Permission.locationWhenInUse.request();
      if (status == PermissionStatus.granted) {
        GpsReceiver receiver = GpsReceiver.getInstance();
        receiver.start(true);
        Navigator.of(ctx).pop();
        return true;
      } else {
        Navigator.of(ctx).pop();
        return false;
      }
    }
  }

  //通知权限
  static Future<bool> requestNotificationPermission() async {
    var status = await Permission.notification.status;
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      // sendTestNotification();
      return false;
    }
  }
}
