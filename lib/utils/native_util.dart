import 'package:bdh_smart_agric_app/pages/user/we_chat_auth/native_paramter_back.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'dart:io';

import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logger/web.dart';

import 'log.dart';

class NativeUtil {
  static const MethodChannel _channel = MethodChannel('com.bdh.smart');

  NativeUtil._();

  static getParamsFromNative(BuildContext context) {
    _channel.setMethodCallHandler((call) async {
      Log.i(
          '收到参数(NativeUtil): _channel.setMethodCallHandler: ${call.arguments}}');
      if (call.method == "readOffLineNotification") {
        NativeParamterBack.dealWithUMPushRounte(
            call, context); //友盟消息 iOS and android
      } else if (call.method == "sendDeviceToken") {
        NativeParamterBack.dealWithSendDeviceToken(call);
      } else if (call.method == "GetVirtualGPSLocaionJudgementResult") {
        bus.emit('GetVirtualGPSLocaionJudgementResult', call.arguments);
      } else if (call.method == "fddClosed") {
        bus.emit('fddClosed', call.arguments);
      }
    });
  }

  static Future getToken() async {
    return await _channel.invokeMethod("getParams");
  }

  static pop() {
    _channel.invokeMethod("quit");
  }

  //杀死当前应用
  static quit() {
    _channel.invokeMethod("");
  }

  static openUni(Map<String, dynamic> params) {
    var token = StorageUtil.token();
    var userInfo = StorageUtil.userInfo()!.toJson();
    _channel.invokeMethod(
        "openMenu", {...params, "token": token, "userInfo": userInfo});
  }

  static openFdd(url) {
    Logger().i(url);
    _channel.invokeMethod("openFdd", {"url": url});
  }

  static openAndroidLocationSecureSettings() {
    _channel.invokeMethod("openAndroidLocationSecureSettings");
  }

  static closeFdd() {
    _channel.invokeMethod("closeFdd");
  }

  // 跳转农业银行支付
  static openABCToPay(Map<String, dynamic> params) {
    _channel.invokeMethod("openABCToPay", params);
  }

  // 跳转建设银行支付
  static openCCBToPay(Map<String, dynamic> params) {
    _channel.invokeMethod("openCCBToPay", params);
  }

  // 跳转工行支付
  static openICBCToPay(Map<String, dynamic> params) {
    _channel.invokeMethod("openICBCToPay", params);
  }

  // 跳转腾讯人脸
  static Future openTxFace(Map<String, dynamic> params) async {
    return await _channel.invokeMethod("openTxFace", params);
  }

  // 跳转wechatlogin
  static Future openWechatLoginForiOS(Map<String, dynamic> params) async {
    return await _channel.invokeMethod("openWechatLoginForiOS", params);
  }

  //pos机付款
  static Future openPosRecharge(Map<String, dynamic> params) async {
    return await _channel.invokeMethod("openPosRecharge", params);
  }

  static Future readOffLineNotification(Map<String, dynamic> params) async {
    return await _channel.invokeMethod("readOffLineNotification", params);
  }

  // 友盟推送init
  static Future initUmengPush(Map<String, dynamic> params) async {
    return await _channel.invokeMethod("initUmengPush", params);
  }

  // 是否虚拟定位判断
  static Future virtualGPSLocaionJudgement(Map<String, dynamic> params) async {
    return await _channel.invokeMethod("virtualGPSLocaionJudgement", params);
  }

  //腾讯课堂-初始化
  static bool x5CoreInitd = false;

  static Future<String> tcicInitX5Core(String licenseKey) {
    if (x5CoreInitd) {
      return Future.value("INIT_SUCCEED");
    }

    if (Platform.isAndroid) {
      return _channel.invokeMethod<String>(
          'tcicInitX5Core', {"licenseKey": licenseKey}).then((result) {
        if (result == 'INIT_SUCCEED') {
          x5CoreInitd = true;
        }
        return result ?? "INIT_FAILED";
      });
    }
    return Future.value("INIT_SUCCEED");
  }

  //腾讯课堂-进入教室
  static Future<String?> tcicJoinClass({
    required int schoolId,
    required String userId,
    required String token,
    required int classId,
    String? language,
    String? scene,
    bool? preferPortrait,
  }) async {
    if (urlConfig.licenseKeyX5?.isEmpty ?? true) {
      Log.d("tcicInitX5Core licenseKeyX5 is null ${urlConfig.licenseKeyX5}");
      return "failed";
    }
    String result = await tcicInitX5Core(urlConfig.licenseKeyX5!);
    if (result != 'INIT_SUCCEED') {
      Log.d("tcicInitX5Core failed");
      return "failed";
    }
    final args = {
      'schoolId': schoolId,
      'userId': userId,
      'token': token,
      'classId': classId,
      'language': language,
      'scene': scene,
      'preferPortrait': preferPortrait,
    };
    return await _channel.invokeMethod<String?>('tcicJoinClass', args);
  }
}
