import 'dart:io';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:logger/web.dart';

class GetDeviceInfo {
  static String _mapIosDeviceModel(String identifier) {
    const deviceMapping = {
      'iPhone1,1': 'iPhone',
      'iPhone1,2': 'iPhone 3G',
      'iPhone2,1': 'iPhone 3GS',
      'iPhone3,1': 'iPhone 4',
      'iPhone3,2': 'iPhone 4',
      'iPhone3,3': 'iPhone 4',
      'iPhone4,1': 'iPhone 4s',
      'iPhone5,1': 'iPhone 5',
      'iPhone5,2': 'iPhone 5',
      'iPhone5,3': 'iPhone 5c',
      'iPhone5,4': 'iPhone 5c',
      'iPhone6,1': 'iPhone 5s',
      'iPhone6,2': 'iPhone 5s',
      'iPhone7,1': 'iPhone 6 Plus',
      'iPhone7,2': 'iPhone 6',
      'iPhone8,1': 'iPhone 6s',
      'iPhone8,2': 'iPhone 6s Plus',
      'iPhone8,4': 'iPhone SE (1st generation)',
      'iPhone9,1': 'iPhone 7',
      'iPhone9,2': 'iPhone 7 Plus',
      'iPhone9,3': 'iPhone 7',
      'iPhone9,4': 'iPhone 7 Plus',
      'iPhone10,1': 'iPhone 8',
      'iPhone10,2': 'iPhone 8 Plus',
      'iPhone10,3': 'iPhone X',
      'iPhone10,4': 'iPhone 8',
      'iPhone10,5': 'iPhone 8 Plus',
      'iPhone10,6': 'iPhone X',
      'iPhone11,2': 'iPhone XS',
      'iPhone11,4': 'iPhone XS Max',
      'iPhone11,6': 'iPhone XS Max',
      'iPhone11,8': 'iPhone XR',
      'iPhone12,1': 'iPhone 11',
      'iPhone12,3': 'iPhone 11 Pro',
      'iPhone12,5': 'iPhone 11 Pro Max',
      'iPhone12,8': 'iPhone SE (2nd generation)',
      'iPhone13,1': 'iPhone 12 mini',
      'iPhone13,2': 'iPhone 12',
      'iPhone13,3': 'iPhone 12 Pro',
      'iPhone13,4': 'iPhone 12 Pro Max',
      'iPhone14,2': 'iPhone 13 Pro',
      'iPhone14,3': 'iPhone 13 Pro Max',
      'iPhone14,4': 'iPhone 13 mini',
      'iPhone14,5': 'iPhone 13',
      'iPhone14,6': 'iPhone SE (3rd generation)',
      'iPhone14,7': 'iPhone 14',
      'iPhone14,8': 'iPhone 14 Plus',
      'iPhone15,2': 'iPhone 14 Pro',
      'iPhone15,3': 'iPhone 14 Pro Max',
      'iPhone15,4': 'iPhone 15',
      'iPhone15,5': 'iPhone 15 Plus',
      'iPhone16,1': 'iPhone 15 Pro',
      'iPhone16,2': 'iPhone 15 Pro Max',
      // iPhone 16 系列 (2024年9月发布)
      'iPhone17,1': 'iPhone 16',
      'iPhone17,2': 'iPhone 16 Plus',
      'iPhone17,3': 'iPhone 16 Pro',
      'iPhone17,4': 'iPhone 16 Pro Max',

      // iPhone 16e (2025年2月发布)
      'iPhone17,5': 'iPhone 16e',

      // 预计的 iPhone 17 系列 (2025年秋季发布 - 基于传言)
      // 注意：以下标识符是基于传言和预测，可能会有变化
      'iPhone18,1': 'iPhone 17',
      'iPhone18,2': 'iPhone 17 Air', // 替代 Plus 系列
      'iPhone18,3': 'iPhone 17 Pro',
      'iPhone18,4': 'iPhone 17 Pro Max',
    };

    return deviceMapping[identifier] ?? 'Unknown iPhone ($identifier)';
  }

  static Future<bool?> getDeviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      String modelAndroid = androidInfo.model;
      Logger().i('设备型号: $modelAndroid');
      bool? res = await StorageManager.sharedPreferences
          ?.setString(kDeviceNameAndroid, modelAndroid);
      return res;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      final modeliOS = _mapIosDeviceModel(iosInfo.utsname.machine);
      Logger().i('设备型号: $modeliOS');
      bool? res = await StorageManager.sharedPreferences
          ?.setString(kDeviceNameiOS, modeliOS);
      return res;
    } else {
      return false;
    }
  }
}
