import 'package:encrypt/encrypt.dart';
import 'package:pointycastle/export.dart';

class EncryptUtil {
  static String rsaEncrypt(String text) {
    var publicKey = '''
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDMs3sqZu8K6dZ4eNWB+LJdm+Io
8uaqttFWesfoxWfKUz8B9uDWD37rfdlIllnwvC6EpIQltzCE4uF7mDL16/XrUoIV
bUO9e6d6WU60/ceJpT7jjZjyh+Aqt67paUS/F9Sba0VtlnShG/QPK2Z6Z+lBf4Pk
AEAHTeIJ6LMxruGtQwIDAQAB
-----END PUBLIC KEY----- 
    ''';
    final keyParser = RSAKeyParser();
    final encrypter = Encrypter(RSA(
        publicKey: keyParser.parse(publicKey) as RS<PERSON><PERSON><PERSON><PERSON><PERSON>,
        privateKey: null));
    return encrypter.encrypt(text).base64;
  }

  static String rsaEncryptLong(String text) {
    var publicKey = '''
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAh+EbjB0xOGdIzYrTshSjPsrELnXIp6ml8gEd
ftB8gAvyPl8mkmrJXHrAcWNoSPMOvqaRk6mUmLYfrBwhV4HrsFl7boJ3/r63RuJP4wbWuWjeB/UNtL2V
uVar6xwFr3aCgDYqEC9dWQqXo9QkKzGxeDXhGfAAVTEuMio5ULC28x523kez6h7l77/I3Gk0s3DYopF4
kvz7Q9QUizLzg0Bi5rz4f9yuNnabvr3yrGDDMjYkoxoOm/NqYo8Uwby0x37EC3BF9UAyOng7WibgwbQv
VApk0YW0OsLZ+weiJqHyjvjLik73a98NA/G+pfv5N0O4PB1rIbXotHtR//6J6Z6EowIDAQAB
-----END PUBLIC KEY----- 
    ''';
    final keyParser = RSAKeyParser();
    final encrypter = Encrypter(RSA(
        publicKey: keyParser.parse(publicKey) as RSAPublicKey,
        privateKey: null));
    return encrypter.encrypt(text).base64;
  }
}
