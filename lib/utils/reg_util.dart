class RegUtil {
  ///是否是身份证
  static bool isIdCard(String value) {
    return RegExp(
            r"^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$")
        .hasMatch(value);
  }

  ///是否是电话号码
  static bool isPhoneNumber(String value) {
    return RegExp(r"^1[3-9]\d{9}$").hasMatch(value);
  }

  ///是否是正数
  static bool isPositiveNumber(String value) {
    return RegExp(r"^(([1-9]{1}\d*)|(0{1}))(\.\d{1,1})?$").hasMatch(value);
  }

  static bool isPositive2Number(String value) {
    return RegExp(r"^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$").hasMatch(value);
  }

  /// 银行卡号校验
  static bool isBankNumber(String value) {
    return RegExp(r"^\d{4,6}([- ]?\d{3,6}){2,4}$").hasMatch(value);
  }

  /// 长度8 汉字字符串
  static bool isOnlyHanZiString(String value) {
    return RegExp(r'^[\u4e00-\u9fa5]{1,8}$').hasMatch(value);
  }

  /// 长度不超过16个字符，每个汉字占用2个字符
  static bool isValidNikeNameLength(String value) {
    int length = value.runes.fold(0, (int length, int rune) {
      return length + (rune > 0x4e00 && rune < 0x9fa5 ? 2 : 1);
    });
    return length <= 16;
  }

  /// 数字北大荒 注册账号 请输入8到16位数字与字母组合
  static bool isValidAccountName(String value) {
    return RegExp(r'^(?=.*[a-zA-Z])[a-zA-Z0-9]{8,16}').hasMatch(value);
  }

  /// 数字北大荒 密码 密码不符合规范:需包含一个特殊字符[!@#$%^&*()_?<>{}
  static bool isValidPWD(String value) {
    return RegExp(r'[!@#$%^&*()_?<>{}]{1}').hasMatch(value);
  }

  /// 数字北大荒 密码 密码不符合规范:长度需为8-18位!
  static bool isValidPWDLength(String value) {
    return RegExp(r'([a-zA-Z0-9!@#$%^&*()_?<>{}]){8,18}').hasMatch(value);
  }

  /// 数字北大荒 密码 密码不符合规范:需含有字母[a-zA-Z]
  static bool isValidPWDHaveAaZz(String value) {
    return RegExp(r'[a-zA-Z]+').hasMatch(value);
  }

  /// 数字北大荒 密码 密码不符合规范:需含有数字[0-9]]
  static bool isValidPWDHaveNumber(String value) {
    return RegExp(r'[0-9]+').hasMatch(value);
  }

//是否是数字金额字符串
  static bool isNumericAmount(String input) {
    // 正则表达式解释：
    // ^\d+：以一个或多个数字开头
    // (\.\d{1,2})?$：可选的小数部分，小数点后最多两位数字
    // RegExp regex = RegExp(r'^\d+(\.\d{1,2})?$');

    // 正则表达式解释：
    // ^0$：单独的0是有效的
    // |：或者
    // ^[0-9]\d*：以非零数字开头，后面可以有任意数量的数字
    // (\.\d{1,2})?$：可选的小数部分，小数点后最多两位数字
    RegExp regex = RegExp(r'^0$|^[0-9]\d*(\.\d{1,2})?$');
    return regex.hasMatch(input);
  }
}
