import 'dart:async';

import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:dio/dio.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/widgets.dart';
import 'package:oktoast/oktoast.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import 'auto_dispose_state_extension.dart';

/// 轻量级的状态管理，几行代码实现链式响应
///
/// late final c1 = use(0);
/// late final c2 = use(2);
/// late final c3 = useComputed(() {
///    return c1.value + c2.value;
/// });
/// late final counter = useComputed(() {
///    return c2.value + c3.value;
/// });
/// print("counter value is ${counter.value}")
/// c1.value ++;
/// print("counter value is ${counter.value}")
///
/// UseBuilder((context){
///   print("c1值改变=>c3值改变=>counter值改变=>页面局部刷新")
///   return Text("counter value is ${counter.value}")
/// })
///
/// url：https://juejin.cn/post/7491853446532087834
///
///
///
Node? _currentNode;

typedef DisposeFn<T> = void Function(T?);
typedef ChangeFn<T> = void Function(T?);
typedef CreateFn<T> = T? Function();
typedef ComputeFn<T> = T? Function();

typedef TransformFn<T, U> = U? Function(T?);

abstract class Node {
  final Set<Node> children = {};

  //添加节点改变监听
  void _subscribeToNode(Node node) {
    //已经有则返回
    if (children.contains(node)) {
      return;
    }
    //添加监听
    addListener(node.notifyListeners);
    //父节点添加孩子节点
    node.children.add(this);
  }

  //子节点取消监听父节点
  void unSubscribeToNode(Node node) {
    if (children.contains(node)) {
      node.removeListener(notifyListeners);
      children.remove(node);
    }
  }

  void addListener(void Function() listener);

  void removeListener(void Function() listener);

  void dispose();

  void notifyListeners();

  //清空自己的孩子节点
  void _unsubscribe() {
    for (var child in children) {
      child.removeListener(notifyListeners);
    }
    children.clear();
  }
}

class UseCompute<T> extends Node {
  DisposeFn<T>? onDispose;
  ChangeFn<T>? onChange;
  final CreateFn<T> onCreate;

  final Set<void Function()> _listeners = {};
  T? _value;
  UseCompute(this.onCreate);

  T? get value {
    //如果全局节点不为空则将自己添加为全局节点的子节点，如果自己发生改变则全局节点也会发生改变，这是链式反应的核心思想
    if (_currentNode != null) {
      _subscribeToNode(_currentNode!);
    }
    //清除自己的子节点监听
    _unsubscribe();
    //缓存全局节点
    var oldCurrentNode = _currentNode;
    try {
      //将全局节点设置成自己
      _currentNode = this;
      //调用计算方法，在计算方法中所有node.value都会绑定到自己身上，子节点监听会重新回来
      var newValue = onCreate.call();

      //如果值有改变则回调
      if (_value != newValue) {
        onChange?.call(newValue);
      }
      _value = newValue;
      return _value;
    } catch (error) {
      rethrow;
    } finally {
      //将全局节点重置回之前的
      _currentNode = oldCurrentNode;
    }
  }

  T? peek() {
    return _value;
  }

  @override
  void addListener(void Function() listener) {
    _listeners.add(listener);
  }

  @override
  void removeListener(void Function() listener) {
    _listeners.remove(listener);
  }

  @override
  void dispose() {
    _listeners.clear();
    _unsubscribe();
    onDispose?.call(_value);
    _value = null;
  }

  @override
  void notifyListeners() {
    for (var listener in _listeners) {
      listener.call();
    }
  }
}

class UseCreate<T> extends UseCompute<T> {
  UseCreate(super.onCreate);

  @override
  T? get value {
    if (_currentNode != null) {
      _subscribeToNode(_currentNode!);
    }

    _value ??= onCreate.call();

    return _value;
  }

  set value(T? newValue) {
    if (_value == newValue) {
      return;
    }
    _value = newValue;
    notifyListeners();
    onChange?.call(_value);
  }

  void forceNotify() {
    notifyListeners();
    onChange?.call(_value);
  }
}

UseBatch? _batchNode;

class UseBatch extends Node {
  final VoidCallback fn;
  final Set<void Function()> _listeners = {};
  UseBatch(this.fn);
  @override
  void dispose() {
    _listeners.clear();
    _unsubscribe();
  }

  @override
  void notifyListeners() {
    for (var listener in _listeners) {
      listener.call();
    }
  }

  @override
  void addListener(void Function() listener) {
    _listeners.add(listener);
  }

  @override
  void removeListener(void Function() listener) {
    _listeners.remove(listener);
  }

  void call() {
    //判断是否已经处在batch，如果是则停止执行
    if (_batchNode != null) {
      return;
    }
    //将batch节点设置成自己
    _batchNode = this;
    try {
      //触发batch函数
      fn.call();
    } catch (e) {
      rethrow;
    } finally {
      //重置batch函数
      _batchNode = null;
      //触发batch回调函数列表
      notifyListeners();
      //清理
      dispose();
    }
  }
}

class UseCreateList<T> extends UseCreate<List<T>> {
  UseCreateList(super.onCreate);

  T operator [](int index) {
    var list = value ?? [];
    return list[index];
  }

  void operator []=(int index, T v) {
    var list = value ?? [];
    list[index] = v;
    forceNotify();
  }

  int get length => value?.length ?? 0;

  void add(T t) {
    List<T> list = value ?? [];
    list.add(t);
    forceNotify();
  }

  void remove(T t) {
    List<T> list = value ?? [];
    list.remove(t);
    forceNotify();
  }

  void removeAt(int index) {
    List<T> list = value ?? [];
    list.removeAt(index);
    forceNotify();
  }
}

abstract class MixinUseState<W extends StatefulWidget> extends State<W> {
  final Set<void Function()> _disposeFnSet = {};

  void autoDispose(VoidCallback callback) {
    if (_disposeFnSet.contains(callback)) {
      return;
    }
    _disposeFnSet.add(callback);
  }

  UseCreate<T> useCreate<T>(CreateFn<T> createFn, {bool autoNotify = false}) {
    var refCreate = UseCreate<T>(createFn);
    autoDispose(refCreate.dispose);
    if (autoNotify) {
      refCreate.addListener(_notifyListeners);
    }
    return refCreate;
  }

  UseCreate<T> use<T>(T? value, {bool autoNotify = false}) {
    return useCreate(() => value, autoNotify: autoNotify);
  }

  void batch(VoidCallback fn) {
    UseBatch(fn)();
  }

  UseCreateList<T> useCreateList<T>(CreateFn<List<T>> createFn,
      {bool autoNotify = false}) {
    var refCreate = UseCreateList<T>(createFn);
    autoDispose(refCreate.dispose);
    if (autoNotify) {
      refCreate.addListener(_notifyListeners);
    }
    return refCreate;
  }

  UseCreateList<T> useList<T>({List<T>? initValue, bool autoNotify = false}) {
    return useCreateList(() => initValue ?? [], autoNotify: autoNotify);
  }

  UseCompute<T> useCompute<T>(ComputeFn<T> computeFn,
      {bool autoNotify = false}) {
    var refCompute = UseCompute<T>(
      computeFn,
    );
    autoDispose(refCompute.dispose);

    if (autoNotify) {
      refCompute.addListener(_notifyListeners);
    }

    return refCompute;
  }

  T useDispose<T>(T value, void Function() disposeFn) {
    autoDispose(disposeFn);
    return value;
  }

  T useController<T extends UseController>(T value) {
    return useDispose(value, value.dispose);
  }

  Widget useWidget(WidgetBuilder builder) {
    return UseBuilder(builder);
  }

  @override
  void dispose() {
    super.dispose();
    for (var dispose in _disposeFnSet) {
      dispose.call();
    }
  }

  void _notifyListeners() {
    //判断当前是否在batch函数下，如果是则将UI改变暂存，因为listeners是set，相同的回调函数只会回调一次
    if (_batchNode != null) {
      _batchNode?.addListener(_notifyListeners);
      return;
    }
    setState(() {});
  }

  @override
  void setState(VoidCallback fn) {
    if (mounted) {
      super.setState(fn);
    }
    super.setState(fn);
  }
}

//局部刷新
class UseBuilder extends StatefulWidget {
  final WidgetBuilder builder;
  const UseBuilder(this.builder, {super.key});

  @override
  State<UseBuilder> createState() => _UseBuilderState<UseBuilder>();
}

class _UseBuilderState<T extends UseBuilder> extends MixinUseState<T> {
  late final result = useCompute(() {
    return widget.builder(context);
  }, autoNotify: true);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return result.value ?? const SizedBox.shrink();
  }

  @override
  void reassemble() {
    super.reassemble();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      result.notifyListeners();
      if (mounted) setState(() {});
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    result.notifyListeners();
  }

  @override
  void didUpdateWidget(covariant T oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.builder != widget.builder) {
      result.notifyListeners();
    }
  }
}

//UI与逻辑分离
abstract class UseController {
  final BuildContext context;

  final Set<void Function()> _disposeFnSet = {};

  void autoDispose(VoidCallback callback) {
    if (_disposeFnSet.contains(callback)) {
      return;
    }
    _disposeFnSet.add(callback);
  }

  UseController(this.context);

  UseCreate<T> useCreate<T>(CreateFn<T> createFn) {
    var refCreate = UseCreate<T>(createFn);
    autoDispose(refCreate.dispose);

    return refCreate;
  }

  UseCreate<T> use<T>(T? value) {
    return useCreate(() => value);
  }

  UseCreateList<T> useCreateList<T>(CreateFn<List<T>> createFn) {
    var refCreate = UseCreateList<T>(createFn);
    autoDispose(refCreate.dispose);

    return refCreate;
  }

  UseCreateList<T> useList<T>({List<T>? initValue}) {
    return useCreateList(() => initValue ?? []);
  }

  T useDispose<T>(T value, void Function() disposeFn) {
    autoDispose(disposeFn);
    return value;
  }

  UseCompute<T> useCompute<T>(ComputeFn<T> computeFn) {
    var refCompute = UseCompute<T>(
      computeFn,
    );
    autoDispose(refCompute.dispose);

    return refCompute;
  }

  void dispose() {
    for (var dispose in _disposeFnSet) {
      dispose.call();
    }
  }
}

mixin SingleCheckUseController<T> on UseController {
  late final itemCheckedIndex = use<int>(null);

  void onItemCheck(int index, bool selected) {
    Log.d("onItemCheck $index $selected");
    if (itemCheckedIndex.value == index && !selected) {
      itemCheckedIndex.value = null;
    } else if (selected) {
      itemCheckedIndex.value = index;
    }
  }

  bool isChecked(int index) {
    return itemCheckedIndex.value == index;
  }
}

mixin MultiCheckUseController<T> on UseController {
  late final items = use<List<T>>([]);
  late final checkedItems = use<List<T>>([]);

  bool get checkedAll {
    if (items.value?.isEmpty ?? true) {
      return false;
    }
    return items.value?.length == checkedItems.value?.length;
  }

  bool get unCheckedAll {
    return checkedItems.value?.isEmpty ?? true;
  }

  bool isChecked(int index) {
    var element = items.value?[index];
    var cItems = checkedItems.value;
    return cItems?.contains(element) ?? false;
  }

  void checkChangeCallback() {}

  void onItemCheck(int index, bool checked) {
    var element = items.value?[index];
    if (element == null) {
      return;
    }
    if (checked == true && !(checkedItems.value?.contains(element) ?? false)) {
      checkedItems.value?.add(element);
    } else if (checked == false &&
        (checkedItems.value?.contains(element) ?? false)) {
      checkedItems.value?.remove(element);
    }
    checkedItems.forceNotify();
    checkChangeCallback();
  }

  //选择全部
  void checkAll() {
    checkedItems.value = List.from(items.value ?? []);
    checkChangeCallback();
  }

  //取消选择全部
  void uncheckAll() {
    checkedItems.value = [];
    checkChangeCallback();
  }
}

//分页
mixin LoadMoreUseController<T> on UseController {
  //加载状态
  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  bool get isLoading =>
      loadingStatus.value == LoadingStatus.loading ||
      loadingStatus.value == LoadingStatus.init;
  late final refreshController = useRefreshController(RefreshController());
  late final items = use<List<T>>([]);

  //加载更多时间
  DateTime loadMoreTime = DateTime.now();
  //当前页
  int page = 1;
  //总数
  int total = 0;
  //分页，每页显示 10 条
  int row = 10;

  //是否需要加载更多
  bool get needLoadMore {
    return total > (items.peek()?.length ?? 0);
  }

  //加载更多
  void loadMore() {
    var nowTime = DateTime.now();

    //加载更多防呆，1s之内只能有一次请求
    var diffSeconds = nowTime.difference(loadMoreTime).inSeconds;
    if (diffSeconds > 1) {
      loadMoreTime = nowTime;
      if (!needLoadMore) {
        refreshController.loadNoData();
        return;
      }
      if (loadingStatus.peek() == LoadingStatus.loading ||
          loadingStatus.peek() == LoadingStatus.loadingMore) {
        refreshController.loadComplete();
        return;
      }
      page++;

      reload(loadingMore: true).whenComplete(() {
        refreshController.loadComplete();
      });
    } else {
      loadMoreTime = nowTime;

      refreshController.loadComplete();
    }
  }

  Future reloadFuture({
    required bool showLoading,
    required bool loadingMore,
    required bool refresh,
  });

  //下拉刷新
  Future refresh() {
    refreshController.headerMode?.value = RefreshStatus.refreshing;
    refreshController.footerMode?.value = LoadStatus.idle;
    return reload(refresh: true).whenComplete(() {
      refreshController.refreshCompleted();
    });
  }

  //重新加载
  Future reload({
    bool showLoading = false, //不显示数据直接显示 loading
    bool loadingMore = false, //显示加载更多的提示
    bool refresh = false,
  }) {
    if (showLoading) {
      page = 1;
      loadingStatus.value = LoadingStatus.loading;
    }
    if (loadingMore) {
      loadingStatus.value = LoadingStatus.loadingMore;
    }

    if (refresh) {
      loadingStatus.value = LoadingStatus.refreshing;
      page = 1;
    }

    return reloadFuture.call(
        showLoading: showLoading, loadingMore: loadingMore, refresh: refresh);
  }
}

//需要跟随页面销毁的组件汇总
extension UseControllerExt on UseController {
  TextEditingController createTextController({String? text}) {
    return useTextController(TextEditingController(text: text));
  }

  TextEditingController useTextController(TextEditingController controller) {
    return useDispose(controller, controller.dispose);
  }

  FocusScopeNode createFocusScopeNode() {
    return _useChangeNotifier(FocusScopeNode());
  }

  FocusNode createFocusNode() {
    return _useChangeNotifier(FocusNode());
  }

  ScrollController createScrollController() {
    return _useChangeNotifier(ScrollController());
  }

  FocusScopeNode useFocusScopeNode(FocusScopeNode controller) {
    return _useChangeNotifier(controller);
  }

  U _useChangeNotifier<U extends ChangeNotifier>(U notifier) {
    return useDispose(notifier, notifier.dispose);
  }

  U useValueNotifier<U extends ValueNotifier>(U notifier) {
    return useDispose(notifier, notifier.dispose);
  }

  CancelToken useCancelToken(CancelToken cancelToken) {
    return useDispose(cancelToken, cancelToken.cancel);
  }

  RefreshController useRefreshController(RefreshController refreshController) {
    return useDispose(refreshController, refreshController.dispose);
  }

  TapGestureRecognizer useTapGestureRecognizer(
      TapGestureRecognizer tapGestureRecognizer) {
    return useDispose(tapGestureRecognizer, tapGestureRecognizer.dispose);
  }

  Timer useTimer(Timer timer) {
    return useDispose(timer, timer.cancel);
  }

  CancelToken createCancelToken() {
    return useCancelToken(CancelToken());
  }

  Null handleError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);

    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }
}

extension UseStateMixinExt on MixinUseState {
  TextEditingController createTextController({String? text}) {
    return useTextController(TextEditingController(text: text));
  }

  ScrollController createScrollController() {
    return _useChangeNotifier(ScrollController());
  }

  AnimationController createAnimationController(TickerProvider vsync) {
    var controller = AnimationController(vsync: vsync);
    return useDispose(controller, controller.dispose);
  }

  TextEditingController useTextController(TextEditingController controller) {
    return useDispose(controller, controller.dispose);
  }

  FocusScopeNode createFocusScopeNode() {
    return _useChangeNotifier(FocusScopeNode());
  }

  FocusNode createFocusNode() {
    return _useChangeNotifier(FocusNode());
  }

  FocusScopeNode useFocusScopeNode(FocusScopeNode controller) {
    return _useChangeNotifier(controller);
  }

  U _useChangeNotifier<U extends ChangeNotifier>(U notifier) {
    return useDispose(notifier, notifier.dispose);
  }

  U useValueNotifier<U extends ValueNotifier>(U notifier) {
    return useDispose(notifier, notifier.dispose);
  }

  CancelToken useCancelToken(CancelToken cancelToken) {
    return useDispose(cancelToken, cancelToken.cancel);
  }

  RefreshController useRefreshController(RefreshController refreshController) {
    return useDispose(refreshController, refreshController.dispose);
  }

  TapGestureRecognizer useTapGestureRecognizer(
      TapGestureRecognizer tapGestureRecognizer) {
    return useDispose(tapGestureRecognizer, tapGestureRecognizer.dispose);
  }

  Timer useTimer(Timer timer) {
    return useDispose(timer, timer.cancel);
  }

  CancelToken createCancelToken() {
    return useCancelToken(CancelToken());
  }

  Null handleError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);

    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }
}

//倒计时
class SecondDown extends ValueNotifier<int> {
  SecondDown() : super(0);
  Timer? _timer;

  void startTimer(int waitSeconds) {
    cancelTimer();
    value = waitSeconds;
    //记时开始时间
    final nowTime = DateTime.now();
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      int diffSeconds = DateTime.now().difference(nowTime).inSeconds;
      if (diffSeconds >= waitSeconds) {
        _timer?.cancel();
        value = 0;
      } else {
        value = waitSeconds - diffSeconds;
      }
    });
  }

  bool get isActive {
    return _timer != null && _timer!.isActive;
  }

  void cancelTimer() {
    if (_timer != null && _timer!.isActive) {
      _timer?.cancel();
    }
    _timer = null;
  }

  @override
  void dispose() {
    super.dispose();
    cancelTimer();
  }
}
