import 'dart:async';

import 'package:flutter/services.dart';

class AppLinksMethodChannel {
  /// Channel handlers
  final _method = const MethodChannel('com.bdh.smart.app_links/messages');
  final _event = const EventChannel('com.bdh.smart.app_links/events');

  Future<Uri?> getInitialLink() async {
    final result = await getInitialLinkString();
    return result != null ? Uri.tryParse(result) : null;
  }

  Future<String?> getInitialLinkString() async {
    final link = await _method.invokeMethod<String?>('getInitialLink');
    return link != null && link.isNotEmpty ? link : null;
  }

  Future<Uri?> getLatestLink() async {
    final result = await getLatestLinkString();
    return result != null ? Uri.tryParse(result) : null;
  }

  Future<String?> getLatestLinkString() async {
    final link = await _method.invokeMethod<String?>('getLatestLink');
    return link != null && link.isNotEmpty ? link : null;
  }

  Stream<String> get stringLinkStream => _event
      .receiveBroadcastStream()
      .where((link) => link != null && link.isNotEmpty)
      .map<String>((dynamic link) => link as String);

  Stream<Uri> get uriLinkStream {
    return stringLinkStream.transform<Uri>(
      StreamTransformer<String, Uri>.fromHandlers(
        handleData: (String uri, EventSink<Uri> sink) {
          sink.add(Uri.parse(uri));
        },
      ),
    );
  }
}

class AppLinks {
  static final AppLinks _instance = AppLinks._();

  factory AppLinks() => _instance;

  AppLinks._();

  StreamController<String>? _stringStreamController;
  StreamController<Uri>? _uriStreamController;

  AppLinksMethodChannel channel = AppLinksMethodChannel();

  Future<Uri?> getInitialLink() {
    return channel.getInitialLink();
  }

  Future<String?> getInitialLinkString() {
    return channel.getInitialLinkString();
  }

  Future<Uri?> getLatestLink() {
    return channel.getLatestLink();
  }

  Future<String?> getLatestLinkString() {
    return channel.getLatestLinkString();
  }

  Stream<String> get stringLinkStream {
    if (_stringStreamController == null) {
      _stringStreamController = StreamController.broadcast();

      _initController<String>(
        _stringStreamController!,
        channel.stringLinkStream,
        onCancel: () => _stringStreamController = null,
      );
    }

    return _stringStreamController!.stream;
  }

  Stream<Uri> get uriLinkStream {
    if (_uriStreamController == null) {
      _uriStreamController = StreamController.broadcast();

      _initController<Uri>(
        _uriStreamController!,
        channel.uriLinkStream,
        onCancel: () => _uriStreamController = null,
      );
    }

    return _uriStreamController!.stream;
  }

  void _initController<T>(
    StreamController<T> controller,
    Stream<T> stream, {
    required void Function() onCancel,
  }) {
    final subscription = stream.listen(
      controller.add,
      onError: controller.addError,
    );

    // Broadcast controller doesn't support pause/resume
    //
    // Forward cancel event when there's no more listeners
    // and dispose controller
    controller.onCancel = () async {
      await subscription.cancel();
      await controller.close();
      onCancel();
    };
  }
}
