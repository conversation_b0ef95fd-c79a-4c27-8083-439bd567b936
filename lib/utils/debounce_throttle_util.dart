import 'dart:async';

import 'package:flutter/material.dart';

import 'log.dart';

class Debouncer {
  final int milliseconds;
  VoidCallback? action;
  Timer? _timer;
  Debouncer({required this.milliseconds});
  run(VoidCallback action) {
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer(Duration(milliseconds: milliseconds), action);
  }
}

class DebounceValueListener<T> {
  late final StreamController _controller;
  late final Stream<T> _debouncedStream;
  Timer? _debounceTimer;

  ValueChanged<T>? valueChanged;

  DebounceValueListener(Duration duration) {
    _controller = StreamController<T>();
    _controller.stream.listen((event) {
      _debounceTimer?.cancel();
      _debounceTimer = Timer(duration, () {
        Log.d("DebounceValueListener call $event");
        valueChanged?.call(event);
      });
    });
  }

  Stream<T> get stream => _debouncedStream;

  void add(T value) {
    //Log.d("DebounceValueListener add $value");
    _controller.add(value);
  }

  void dispose() {
    _controller.close();
    _debounceTimer?.cancel();
    valueChanged = null;
  }
}

class Throttler {
  final int throttleGapInMillis;
  int? lastActionTime;
  Throttler({required this.throttleGapInMillis});

  void run(VoidCallback action) {
    if (lastActionTime == null) {
      action();
      lastActionTime = DateTime.now().millisecondsSinceEpoch;
    } else {
      // //print(
      //     "上次点击:$lastActionTime,当前时间:${DateTime.now().millisecondsSinceEpoch}");
      if (DateTime.now().millisecondsSinceEpoch - lastActionTime! >
          (throttleGapInMillis)) {
        action();
        lastActionTime = DateTime.now().millisecondsSinceEpoch;
      }
    }
  }
}
