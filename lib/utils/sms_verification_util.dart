import 'package:flutter/material.dart';
import 'package:bdh_smart_agric_app/components/sms_verification_dialog.dart';

class SmsVerificationUtil {
  /// 显示短信验证码确认弹窗
  ///
  /// [context] - 上下文
  /// [rationPlanIds] - 选中项的batchId集合
  /// [onSuccess] - 成功回调
  /// [onCancel] - 取消回调
  static void showSmsVerificationDialog(
    BuildContext context, {
    required List<String> rationPlanIds,
    VoidCallback? onSuccess,
    VoidCallback? onCancel,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return SmsVerificationDialog(
          rationPlanIds: rationPlanIds,
          onSuccess: onSuccess,
          onCancel: onCancel,
        );
      },
    );
  }
}
