import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

// 默认日志标签
const defaultLogTag = "Default";
// 数据请求
const apiLogTag = "Api";

// 日志工具类
class Log {
  // 存储调试、信息和警告日志级别的标签
  static final debugTags = [];
  static final infoTags = [];
  static final warnTags = [];

  // 初始化日志记录器，配置输出和打印格式
  static final Logger _logger = Logger(
    output: _ConsoleOutput(),
    printer: PrefixPrinter(
      PrettyPrinter(
        methodCount: 2,
        lineLength: 80,
        colors: false,
      ),
    ),
  );

  static void enable(String tag) {
    debugTags.add(tag);
    infoTags.add(tag);
    warnTags.add(tag);
  }

  static void disable(String tag) {
    debugTags.remove(tag);
    infoTags.remove(tag);
    warnTags.remove(tag);
  }

  // 启用默认日志标签
  static void enableDefault() {
    enable(defaultLogTag);
  }

  // 禁用默认日志标签
  static void disableDefault() {
    disable(defaultLogTag);
  }

  static void enableApi() {
    enable(apiLogTag);
  }

  static void disableApi() {
    disable(apiLogTag);
  }

  static bool isActive(String tag) {
    return debugTags.contains(tag);
  }

  static bool isActiveApi() {
    return isActive(apiLogTag);
  }

  // 调试日志方法
  static void d(dynamic message, {String module = defaultLogTag}) {
    if (debugTags.contains(module)) {
      _logger.d('[$module]${message.toString()}');
    }
  }

  // 信息日志方法
  static void i(dynamic message, {String module = defaultLogTag}) {
    if (infoTags.contains(module)) {
      _logger.i('[$module]${message.toString()}');
    }
  }

  // 警告日志方法
  static void w(dynamic message, {String module = defaultLogTag}) {
    if (warnTags.contains(module)) {
      _logger.w('[$module]${message.toString()}');
    }
  }

  // 错误日志方法，包含异常和堆栈跟踪信息
  static void e(dynamic message,
      {String module = "Default", Object? error, StackTrace? stackTrace}) {
    String errorString = '[$module]${message.toString()}';
    _logger.e(errorString, error: error, stackTrace: stackTrace);

    // TODO 数据上报
    //if (kReleaseMode) {
    //  FlutterBugly.uploadException(
    //      message: '[$module] error', detail: errorString);
    //}
  }
}

// 自定义日志输出类，确保在安全的环境下输出日志
class _ConsoleOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    // 仅在调试和性能测试模式下输出日志
    if (kDebugMode || kProfileMode) {
      event.lines.forEach(debugPrint);
    }

    // TODO 加密存储
  }
}
