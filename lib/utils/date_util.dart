import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class DateUtil {
  //String 转String
  static String getTimeStrByDateFormat(String text, String format) {
    if (text.isEmpty || text == "null") {
      return '';
    }
    String originalDate = text;

    // 使用 DateFormat 来格式化日期
    DateTime date = DateTime.parse(originalDate);
    String formattedDate = DateFormat(format).format(date);

    return formattedDate;
  }

  static int getNowYear() {
    return DateTime.now().year;
  }

  //DateTime 根据 format 转String
  static String dateTimeFormat(DateTime? dateTime, String format) {
    if (dateTime == null) {
      return '';
    }
    // 使用 DateFormat 来格式化日期
    DateTime date = dateTime;
    String formattedDate = DateFormat(format).format(date);
    return formattedDate;
  }

  //获取今年的第一天
  static DateTime getFirstDayOfYear() {
    return DateTime(DateTime.now().year, 1, 1);
  }

  //获取前一年的当前日期
  static DateTime getNowDayOfLastYear() {
    return DateTime(
        DateTime.now().year - 1, DateTime.now().month, DateTime.now().day);
  }

  //获取今年的最后一天
  static DateTime getLastDayOfYear() {
    return DateTime(DateTime.now().year, 12, 31);
  }

  //时间戳转String
  static String formatTimestamp(int timestamp, String format) {
    if (timestamp == 0) {
      return "";
    }
    // LogUtil.d("timestamp:${timestamp}");
    // LogUtil.d("format:${format}");
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat(format).format(dateTime);
  }

//比较是不是今天，是返回今，否则返回时间
  static String getformatTimestamp(int endTime, String format) {
    if (endTime == 0) {
      return "";
    }
    // 获取当前日期
    DateTime now = DateTime.now();
    // 设置时分秒为0
    DateTime midnight = DateTime(now.year, now.month, now.day);
    // 转换为时间戳（毫秒）
    int timestamp = midnight.millisecondsSinceEpoch;
    if (endTime > timestamp) {
      return '今';
    }
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(endTime);
    return DateFormat(format).format(dateTime);
  }

  //获取当前时间前一天的年月日字符串
  static String getPreviousDayDateString(String format) {
    // 获取当前日期
    DateTime now = DateTime.now();
    // 减去一天
    DateTime previousDay = now.subtract(const Duration(days: 1));
    // 格式化日期
    final DateFormat formatter = DateFormat(format);
    String dateString = formatter.format(previousDay);
    return dateString;
  }

  //获取星期中文名称
  static String getWeekday(DateTime date) {
    // 获取星期几
    int weekday = date.weekday;
    // 根据星期几返回对应的中文名称
    switch (weekday) {
      case DateTime.monday:
        return '周一';
      case DateTime.tuesday:
        return '周二';
      case DateTime.wednesday:
        return '周三';
      case DateTime.thursday:
        return '周四';
      case DateTime.friday:
        return '周五';
      case DateTime.saturday:
        return '周六';
      case DateTime.sunday:
        return '周天';
      default:
        return "";
    }
  }

  //获取时间是当前月份的第几周
  static String getWeekOfMonth(DateTime now) {
    final DateTime firstDayOfMonth = new DateTime(now.year, now.month, 1);
    return (((now.difference(firstDayOfMonth).inDays +
                        firstDayOfMonth.weekday) /
                    7)
                .floor() +
            1)
        .toString();
  }

  //判断一个时间是否在 一个时间段内
  static bool isWithinTimeRange(DateTime target, DateTime start, DateTime end) {
    final bool isAfterStart = target.isAfter(start) || target == start;
    final bool isBeforeEnd = target.isBefore(end) || target == end;
    return isAfterStart && isBeforeEnd;
  }

  //获取7天之前日期
  static DateTime getSevenDaysAgo() {
    final now = DateTime.now();
    return now.subtract(const Duration(days: 7));
  }

  //获取指定日期1天之前日期
  static DateTime getBeforOneDaysAgo(DateTime dateTime) {
    return dateTime.subtract(const Duration(days: 1));
  }

  //计算某个时间距离今天相差多少天
  static int getAfterNowDayNum(DateTime specificDate) {
    // 计算今天与特定日期之间的差异
    int differenceInDays = DateTime.now().difference(specificDate).inDays;
    return differenceInDays;
  }

  ///计算某个时间往前X个月的具体日期
  ///beforeMonth  月数 例如1、3、5、12（1年）、36（3年）
  ///anchorDateTime 锚定日期，比如2022-7-6
  ///如往前X个月没有有当前天则返回前x月+1个月的1号（例如3月30往前1个月，没有2月30，则返回3月1号）
  static DateTime getBeforeMonthTotalDays(
      int beforeMonth, DateTime anchorDateTime) {
    DateTime startTime;
    //1--先计算目标年月
    if (anchorDateTime.month <= beforeMonth) {
      //月份不足，计算年份
      int year = anchorDateTime.year - beforeMonth ~/ 12; //计算年数
      int month =
          beforeMonth ~/ 12 * 12 + anchorDateTime.month - beforeMonth; //计算月份
      if (month == 0) {
        //0月改为上一年的12月
        year -= 1;
        month = 12;
      }
      startTime = DateTime(year, month);
    } else {
      //月份足，直接计算月份
      startTime =
          DateTime(anchorDateTime.year, anchorDateTime.month - beforeMonth);
    }
    //2--计算那个月有多少天，根据该1号和次月1号相差得出
    int totalDays = 0;
    totalDays = startTime.month == 12
        ? DateTime(startTime.year + 1, 1, 1).difference(startTime).inDays
        : DateTime(startTime.year, startTime.month + 1, 1)
            .difference(startTime)
            .inDays;

    //3--计算目标日，根据目标月是否含有目标日，如果没有则返回次月1号
    if (totalDays >= anchorDateTime.day) {
      // print("该月天数有当前天，直接返回那天");
      //开始日期那个月有那一天
      startTime = DateTime(startTime.year, startTime.month, anchorDateTime.day);
    } else {
      // print("该月天数没有当前天，返回该月次月1号");
      //没有那一天，则变成次月1号
      if (startTime.month == 12) {
        //12月，改成次年1月1日
        startTime = DateTime(startTime.year + 1, 1, 1);
      } else {
        //其他月份直接+1
        startTime = DateTime(startTime.year, startTime.month + 1, 1);
      }
    }
    print(
        "$anchorDateTime 的最近$beforeMonth个月的开始时间为$startTime,总间隔天数${anchorDateTime.difference(startTime).inDays}");
    return startTime;
  }
}
