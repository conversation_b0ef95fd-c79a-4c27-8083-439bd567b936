import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:oktoast/oktoast.dart';
import 'package:uuid/uuid.dart';

AppApiHttp get appApiHttp => AppApiHttp();
BusiHttp get buisHttp => BusiHttp();
SSOHttp get ssoHttp => SSOHttp();
CommonHttp get baseHttp => CommonHttp();
FieldHttp get fieldHttp => FieldHttp();
CommonHttpApi get commonHttp => CommonHttpApi();
MicroHttp get microHttp => MicroHttp();
MesHttp get mesHttp => MesHttp();
PlotHttp get plotHttp => PlotHttp();
HighHttp get highHttp => HighHttp();
AuthHttp get authHttp => AuthHttp();
DaHingHttp get daHingHttp => DaHingHttp();
ChatHttp get chatHttp => ChatHttp();
ThreePartyHttp get threePartyHttp => ThreePartyHttp();
SoilTestingHttp get soilTestingHttp => SoilTestingHttp();
WaterManageHttp get waterManageHttp => WaterManageHttp();
WaterManageDownloadHttp get waterManageDownloadHttp =>
    WaterManageDownloadHttp();
JZHttp get jzHttp => JZHttp();
FertilizeHttp get fertilizeHttp => FertilizeHttp();
LoanHttp get loanHttp => LoanHttp();
SamicHttp get samicHttp => SamicHttp();
FinanceHttp get financeHttp => FinanceHttp();
StageHttp get stageHttp => StageHttp();
CostanalysisHttp get costanalysisHttp => CostanalysisHttp();
FinaHttp get finaHttp => FinaHttp();
ZjxtHttp get zjxtHttp => ZjxtHttp();
EncyApi get encyApi => EncyApi();
AgricultureHttp get agricultureHttp => AgricultureHttp();
WeatherApi get weatherApi => WeatherApi();

FarmfellHttp get farmfellHttp => FarmfellHttp();
ExpotrHttp get expotrHttp => ExpotrHttp();

ThreeCensusesHttp get threeCensusesHttp => ThreeCensusesHttp(); // 三普

LeafAgeHttp get leafAgeHttp => LeafAgeHttp();
final Dio dio = Dio();

class AppApiHttp extends BaseHttp {
  static AppApiHttp? _instance;
  factory AppApiHttp() => _instance ??= AppApiHttp._internal();

  AppApiHttp._internal() : super();

  @override
  void init() {
    var baseUrl = urlConfig.appApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class SSOHttp extends BaseHttp {
  static SSOHttp? _instance;
  factory SSOHttp() => _instance ??= SSOHttp._internal();

  SSOHttp._internal() : super();

  @override
  void init() {
    var baseUrl = urlConfig.ssoApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class CommonHttpApi extends BaseHttp {
  static CommonHttpApi? _instance;
  factory CommonHttpApi() => _instance ??= CommonHttpApi._internal();

  CommonHttpApi._internal() : super();

  @override
  void init() {
    var baseUrl = urlConfig.commonApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());

    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class ChatHttp extends BaseHttp {
  static ChatHttp? _instance;
  factory ChatHttp() => _instance ??= ChatHttp._internal();

  ChatHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.chatApi;
    options.baseUrl = baseUrl;
    // interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class ThreePartyHttp extends BaseHttp {
  static ThreePartyHttp? _instance;
  factory ThreePartyHttp() => _instance ??= ThreePartyHttp._internal();

  ThreePartyHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.threePartyApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class SoilTestingHttp extends BaseHttp {
  static SoilTestingHttp? _instance;
  factory SoilTestingHttp() => _instance ??= SoilTestingHttp._internal();

  SoilTestingHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.soilTestingApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());

    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class CommonHttp extends BaseHttp {
  static CommonHttp? _instance;
  factory CommonHttp() => _instance ??= CommonHttp._internal();

  CommonHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.baseApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor(bdhCode: "bdh-app-gathering"));
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class FieldHttp extends BaseHttp {
  static FieldHttp? _instance;
  factory FieldHttp() => _instance ??= FieldHttp._internal();

  FieldHttp._internal() : super();

  @override
  void init() {
    var baseUrl = urlConfig.fieldApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());

    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class JZHttp extends BaseHttp {
  static JZHttp? _instance;
  factory JZHttp() => _instance ??= JZHttp._internal();

  JZHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.jzApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class MesHttp extends BaseHttp {
  static MesHttp? _instance;
  factory MesHttp() => _instance ??= MesHttp._internal();

  MesHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.mesApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class PlotHttp extends BaseHttp {
  static PlotHttp? _instance;
  factory PlotHttp() => _instance ??= PlotHttp._internal();

  PlotHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.plotApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());

    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

// class WeatherHttp extends BaseHttp {
//   static WeatherHttp? _instance;
//   factory WeatherHttp() => _instance ??= WeatherHttp._internal();
//
//   WeatherHttp._internal() : super();
//   @override
//   void init() {
//     var baseUrl = urlConfig.waterMangeApi;
//     options.baseUrl = baseUrl!;
//     interceptors.add(ApiInterceptor());
//     interceptors.add(ApiInterceptor2());
//     (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
//         (cert, host, port) {
//       return true;
//     };
//   }
// }
// 假设 ApiInterceptor 类的定义如下

class HighHttp extends BaseHttp {
  static HighHttp? _instance;
  factory HighHttp() => _instance ??= HighHttp._internal();

  HighHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.highApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());

    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class ThreeCensusesHttp extends BaseHttp {
  static ThreeCensusesHttp? _instance;
  factory ThreeCensusesHttp() => _instance ??= ThreeCensusesHttp._internal();

  ThreeCensusesHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.threeCensusesApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());
    // interceptors.add(ApiInterceptor2());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class MicroHttp extends BaseHttp {
  static MicroHttp? _instance;
  factory MicroHttp() => _instance ??= MicroHttp._internal();

  MicroHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.microfront;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class StageHttp extends BaseHttp {
  static StageHttp? _instance;
  factory StageHttp() => _instance ??= StageHttp._internal();

  StageHttp._internal() : super();
  @override
  void init() {
    enableLog();
    var baseUrl = urlConfig.stageApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class BusiHttp extends BaseHttp {
  static BusiHttp? _instance;
  factory BusiHttp() => _instance ??= BusiHttp._internal();

  BusiHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.busiApi;
    options.baseUrl = baseUrl ?? "";
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class AgricultureHttp extends BaseHttp {
  static AgricultureHttp? _instance;
  factory AgricultureHttp() => _instance ??= AgricultureHttp._internal();

  AgricultureHttp._internal() : super();
  @override
  void init() {
    enableLog();
    var baseUrl = urlConfig.agricultureApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class AuthHttp extends BaseHttp {
  static AuthHttp? _instance;
  factory AuthHttp() => _instance ??= AuthHttp._internal();

  AuthHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.authApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class DaHingHttp extends BaseHttp {
  static DaHingHttp? _instance;
  factory DaHingHttp() => _instance ??= DaHingHttp._internal();

  DaHingHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.dahingGanLingApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class WaterManageHttp extends BaseHttp {
  static WaterManageHttp? _instance;
  factory WaterManageHttp() => _instance ??= WaterManageHttp._internal();

  WaterManageHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.waterMangeApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor(bdhCode: "bdh-water-fee"));

    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class SamicHttp extends BaseHttp {
  static SamicHttp? _instance;
  factory SamicHttp() => _instance ??= SamicHttp._internal();

  SamicHttp._internal() : super();
  @override
  void init() {
    enableLog();
    var baseUrl = urlConfig.samicConfirmApi;
    options.baseUrl = baseUrl!;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class WaterManageDownloadHttp extends DioForNative {
  static WaterManageDownloadHttp? _instance;
  factory WaterManageDownloadHttp() =>
      _instance ??= WaterManageDownloadHttp._internal();

  WaterManageDownloadHttp._internal() {
    init();
  }

  void init() {
    var baseUrl = urlConfig.waterMangeApi;
    options.baseUrl = baseUrl;
    interceptors.add(HeaderInterceptor());
    interceptors.add(ApiInterceptor(bdhCode: "bdh-water-fee"));

    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class FinanceHttp extends BaseHttp {
  static FinanceHttp? _instance;
  factory FinanceHttp() => _instance ??= FinanceHttp._internal();

  FinanceHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.financeApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class LoanHttp extends BaseHttp {
  static LoanHttp? _instance;
  factory LoanHttp() => _instance ??= LoanHttp._internal();

  LoanHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.loanApi.toString();
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class CostanalysisHttp extends BaseHttp {
  static CostanalysisHttp? _instance;
  factory CostanalysisHttp() => _instance ??= CostanalysisHttp._internal();

  CostanalysisHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.costanalysisApi!;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor(bdhCode: "bdh-app"));
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class FinaHttp extends BaseHttp {
  static FinaHttp? _instance;
  factory FinaHttp() => _instance ??= FinaHttp._internal();

  FinaHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.finaApi!;
    options.baseUrl = baseUrl;

    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class ZjxtHttp extends BaseHttp {
  static ZjxtHttp? _instance;
  factory ZjxtHttp() => _instance ??= ZjxtHttp._internal();

  ZjxtHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.zjxtApi!;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class EncyApi extends BaseHttp {
  static EncyApi? _instance;
  factory EncyApi() => _instance ??= EncyApi._internal();

  EncyApi._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.encyApi!;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class FarmfellHttp extends BaseHttp {
  static FarmfellHttp? _instance;
  factory FarmfellHttp() => _instance ??= FarmfellHttp._internal();

  FarmfellHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.farmfellApi!;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class ExpotrHttp extends BaseHttp {
  static ExpotrHttp? _instance;
  factory ExpotrHttp() => _instance ??= ExpotrHttp._internal();

  ExpotrHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.expotrApi!;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
  }
}

class WeatherApi extends BaseHttp {
  static WeatherApi? _instance;
  factory WeatherApi() => _instance ??= WeatherApi._internal();

  WeatherApi._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.weatherApi!;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class ApiInterceptor extends InterceptorsWrapper {
  final String? bdhCode;

  ApiInterceptor(
      {super.onRequest, super.onResponse, super.onError, this.bdhCode});
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    var token = StorageUtil.userInfo()?.data?.token;
    if (token != null) {
      var uuid = const Uuid();
      Log.d(
          "requestUrl is ${options.baseUrl}${options.path}---token is $token");
      // 保留已有的 headers
      var existingHeaders = options.headers;
      options.headers = {
        ...existingHeaders,
        "cop-token": token,
        "app": 1,
        "uuid": uuid.v1(),
        "bdh-code": bdhCode ?? "digital-agriculture-app",
        "Content-Type": "application/json",
      };
    }
    if (kDebugMode) {
      print("token is $token");
    }

    handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    //token过期无效
    if (response.data["code"] == 200004) {
      String requestUrl =
          '${response.requestOptions.baseUrl}${response.requestOptions.path}';
      Log.d("requestUrl is $requestUrl");
      showToast(response.data["msg"], duration: const Duration(seconds: 3));
      bus.emit("token_invalid");
      return;
    }
    if (response.data["code"] != 0) {
      assert(() {
        debugPrint("error : ${response.data}");
        return true;
      }());
      if (response.data["msg"] != "您没有施肥建议卡") {
        showToast(response.data["msg"], duration: const Duration(seconds: 3));
      }
      // if(!response.data["msg"].toString().contains("500")){
      //   if(!response.data["msg"].toString().contains("当前算法")){
      //     // showToast(response.data["msg"], duration: const Duration(seconds: 3));
      //   }
      // }
    }
    handler.next(response);
  }
}

class FertilizeHttp extends BaseHttp {
  static FertilizeHttp? _instance;
  factory FertilizeHttp() => _instance ??= FertilizeHttp._internal();

  FertilizeHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.fertilizApi.toString();
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}

class LeafAgeHttp extends BaseHttp {
  static LeafAgeHttp? _instance;
  factory LeafAgeHttp() => _instance ??= LeafAgeHttp._internal();

  LeafAgeHttp._internal() : super();
  @override
  void init() {
    var baseUrl = urlConfig.leafAgeApi;
    options.baseUrl = baseUrl;
    interceptors.add(ApiInterceptor());
    (httpClientAdapter as IOHttpClientAdapter).validateCertificate =
        (cert, host, port) {
      return true;
    };
  }
}
