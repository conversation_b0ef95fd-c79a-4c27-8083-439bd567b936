class RequestNoData {
  bool? success;
  int? code;
  String? msg;
  dynamic data;

  RequestNoData({this.success, this.code, this.msg, this.data});

  RequestNoData.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    data['msg'] = msg;
    data['data'] = this.data;
    return data;
  }

  @override
  String toString() {
    return "RequestNoData(success:$success,code:$code,msg:$msg,data:$data)";
  }
}
