import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/enterprise_info_result_model.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/query_contract_list_model.dart';
import 'package:bdh_smart_agric_app/model/query_farmer_result_model.dart';
import 'package:bdh_smart_agric_app/model/query_my_contract_result_model.dart';
import 'package:bdh_smart_agric_app/model/query_partner_result_model.dart';
import 'package:bdh_smart_agric_app/model/query_transfer_config_result_model.dart';

import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/model/transfer_application_result_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

class LandResponsitory {
  //文件上传
  static Future<RequestNoData> fileUpload(dynamic params) async {
    var response = await daHingHttp.post("/file/upload", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //实名认证查询
  static Future<RequestNoData> getRealNameStatus(dynamic accountId) async {
    var response = await authHttp.post(
      "/authentication/thirdPartyAuthentication/getRealNameStatus/$accountId",
    );
    return RequestNoData.fromJson(response.data);
  }

  //法大大验证码
  static Future<RequestNoData> getFddSmsCode(dynamic params) async {
    var response = await authHttp.post(
      "/authentication/thirdPartyAuthentication/sendVerifyCode",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //法大大身份证识别
  static Future<RequestNoData> ocrIdCard(dynamic params) async {
    var response = await authHttp.post(
      "/ocr/word/idCardOcr",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //判断身份证是否已经存在
  static Future<RequestNoData> existsFarmer(dynamic params) async {
    var response = await daHingHttp.post(
      "/bdhapp/farmer/existsFarmer",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //基本信息查询
  static Future<LandBaseInfoResult> getResidentInfo(
      {CancelToken? cancelToken}) async {
    var response = await daHingHttp.post("/bdhapp/farmer/getResidentInfo",
        cancelToken: cancelToken);
    return LandBaseInfoResult.fromJson(response.data);
  }

  //基本信息更新
  static Future<RequestNoData> updateResidentInfo(data) async {
    var response =
        await daHingHttp.post("/bdhapp/farmer/updateFarmFlag", data: data);
    return RequestNoData.fromJson(response.data);
  }

  //实名认证信息提交-大兴安岭
  static Future<RequestNoData> authDataVerification(
      Map<String, dynamic> data) async {
    var response = await daHingHttp.post("/bdhapp/farmer/authDataVerification",
        data: data);
    return RequestNoData.fromJson(response.data);
  }

  //实名认证状态轮询
  static Future<RequestNoData> getResidentInfoStatus(int accountId) async {
    var response = await authHttp.post(
      "/authentication/thirdPartyAuthentication/personCertificationStatusSimple/$accountId",
    );
    return RequestNoData.fromJson(response.data);
  }

  //查询企业信息
  static Future<EnterpriseInfoResult> queryEnterpriseInfo(
      Map<String, dynamic> data) async {
    var response =
        await daHingHttp.post("/bdhapp/partner/getPartnerInfo", data: data);
    return EnterpriseInfoResult.fromJson(response.data);
  }

  //注册企业信息
  static Future<RequestNoData> insertEnterpriseInfo(
      Map<String, dynamic> data) async {
    var response = await daHingHttp.post("/bdhapp/partner/insert", data: data);
    return RequestNoData.fromJson(response.data);
  }

  //修改企业信息
  static Future<RequestNoData> updateEnterpriseInfo(
      Map<String, dynamic> data) async {
    var response = await daHingHttp.post("/bdhapp/partner/update", data: data);
    return RequestNoData.fromJson(response.data);
  }

  //字典接口
  static Future<DictList> getDicByKey(String key) async {
    var response = await ssoHttp.post("/sso/dict/list/$key");
    return DictList.fromJson(response.data);
  }

  //组织机构查询
  static Future<OrgTreeResult> getOrgData({CancelToken? cancelToken}) async {
    var response = await daHingHttp.post("/org/amporg/queryOrgTreeByUserOrg",
        cancelToken: cancelToken);
    return OrgTreeResult.fromJson(response.data);
  }

  //查询流转
  static Future<TransferApplicationResult> queryMyTransfer(params) async {
    var response = await daHingHttp.post(
        "/bdhapp/contract/transfer/queryMyTransfer",
        queryParameters: params);
    return TransferApplicationResult.fromJson(response.data);
  }

  //查询我的合同
  static Future<QueryMyContractResult> queryMyContract(params) async {
    var response = await daHingHttp.post(
        "/bdhapp/contract/transfer/queryMyContract",
        queryParameters: params);
    return QueryMyContractResult.fromJson(response.data);
  }

  //查询农户
  static Future<QueryFarmerResult> queryFarmer(params) async {
    var response = await daHingHttp
        .post("/bdhapp/contract/transfer/queryFarmer", queryParameters: params);
    return QueryFarmerResult.fromJson(response.data);
  }

  //查询企业
  static Future<QueryPartnerResult> queryPartner(params) async {
    var response = await daHingHttp.post(
        "/bdhapp/contract/transfer/queryPartner",
        queryParameters: params);
    return QueryPartnerResult.fromJson(response.data);
  }

  //新增流转
  static Future<RequestNoData> saveTransfer(params) async {
    var response = await daHingHttp
        .post("/bdhapp/contract/transfer/saveTransfer", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //查询合同
  static Future<QueryContractListResult> queryContractList(params) async {
    var response = await daHingHttp.post("/bdhapp/contract/queryContractList",
        data: params);
    return QueryContractListResult.fromJson(response.data);
  }

  //查询合同返回
  static Future<RequestNoData> manualSignPage(params) async {
    var response =
        await daHingHttp.post("/bdhapp/contract/manualSignPage", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //查看合同链接
  static Future<RequestNoData> scanContract(params) async {
    var response = await authHttp.post(
        "/authentication/thirdPartyAuthentication/viewContract",
        data: params);
    return RequestNoData.fromJson(response.data);
  }

  //查询合同流转默认配置
  static Future<QueryTransferConfigResult> queryDefaultTransConfig(
      params) async {
    var response = await daHingHttp
        .post("/bdhapp/contract/transfer/queryDefault", data: params);
    return QueryTransferConfigResult.fromJson(response.data);
  }
}
