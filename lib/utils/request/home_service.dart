import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:logger/web.dart';

//***************** home list **********************************/
class HomeService {
//v2 获取9个分公司 树
  static Future getOrgListTreeNew(data) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/org/getOrgListTreeNew", data: data);
    return response.data;
  }

  //获取天气
  static Future getWeatherData(num latitude, num longitude) async {
    //经度110.1067494°，纬度31.3142408°
    var response = await dio.get(
        'http://data-api.91weather.com/bdh/realtime?lat=$latitude&lon=$longitude');
    return response.data;
  }

//----------------------------
// 频道树数据获取 v1
  static Future getTreeChannel(orgCode) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/app/channel/queryTreeChannel",
      queryParameters: orgCode,
    );
    return response.data;
  }

//频道点击量
  static Future getChannleClickedStatistc(params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/app/channel/click",
        queryParameters: params);
    return response.data;
  }

// banner
  static Future getBanner(params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/content/module/channel/rotate/list",
      data: params,
    );
    return response.data;
  }

  //首屏置顶 3条
  static Future getHomeTopNews(params) async {
    var respones = await microHttp.post(
      '/agric-app-gathering-api/content/module/top/list',
      data: params,
    );
    return respones.data;
  }

  //首屏推位+常规
  static Future getHomeRecommend(params) async {
    var respones = await microHttp.post(
      '/agric-app-gathering-api/content/module/home/<USER>',
      data: params,
    );
    return respones.data;
  }

  //内容详情 /content/module/detail/{contentUid}
  static Future getDetail(contentUid) async {
    var respones = await microHttp.post(
      '/agric-app-gathering-api/content/module/detail',
      queryParameters: {"contentUid": contentUid},
    );
    return respones.data;
  }

  //内容详情 /content/module/detail/{contentUid}
  static Future advertisement(contentId) async {
    var respones = await microHttp.post(
      '/agric-app-gathering-api/advertisement/click/$contentId',
      // queryParameters: {"advertisementId": contentId},
    );
    return respones.data;
  }

  //内容详情 点赞
  static Future photoArticalClickeLike(params) async {
    var respones = await microHttp.post(
      '/agric-app-gathering-api/content/module/likes/operation',
      data: params,
    );
    return respones.data;
  }

  //田间视频点赞
  static Future plantVideoClickeStar(params) async {
    var respones = await microHttp.post(
      '/agric-app-gathering-api/app/interact/likesOperation',
      data: params,
    );
    return respones.data;
  }

//***************** search **********************************/
//获取 搜索历史
  static Future getSeachHistoryRecords(params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/content/module/query/records",
        data: params);
    return response.data;
  }

  //模糊搜索
  // /content/module/fuzzy/search
  static Future seachResult(params) async {
    var response = await microHttp.post(
        '/agric-app-gathering-api/content/module/fuzzy/search',
        data: params);
    return response.data;
  }

//批量删除搜索记录  /content/module/deleteBatch/records

  static Future deletaAllSearchRecords(params) async {
    var response = await microHttp.post(
        '/agric-app-gathering-api/content/module/deleteBatch/records',
        data: params);
    return response.data;
  }

//留言:新增留言
  static Future addMessage(params) async {
    var response = await microHttp
        .post('/agric-app-gathering-api/app/channel/add/message', data: params);
    return response.data;
  }

  //留言:列表分页查询
  static Future getLeaveMessage(params) async {
    var response = await microHttp
        .post('/agric-app-gathering-api/app/channel/getByPage', data: params);
    return response.data;
  }

  // 留言:查询留言详情
  static Future getLeaveMsgDitail(messageUid) async {
    var respones = await microHttp.post(
      '/agric-app-gathering-api/app/channel/getDetail',
      queryParameters: {"messageUid": messageUid},
    );
    return respones.data;
  }

  // 日报url
  static Future getBDHDaliyReportUrl(configKey) async {
    var respones = await microHttp.post(
      // '/agric-app-gathering-api/app/user/home/<USER>',
      '/agric-app-gathering-api/nacos/getConfig',
      queryParameters: {"configKey": configKey},
    );
    return respones.data;
  }

  static Future<Uint8List?> getImageData(String url) async {
    try {
      // var dio = await Dio()
      //     .get(url, options: Options(responseType: ResponseType.bytes));
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        if (response.bodyBytes.length > 50000) {
          final responseCompressed =
              await CompressUtil.u8ToU8(response.bodyBytes);
          return responseCompressed;
        } else {
          return response.bodyBytes;
        }
      }
    } catch (e) {
      Logger().e('Failed to load image: $e');
    }
    return null;
  }
}
