import 'package:bdh_smart_agric_app/model/bank_model.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:bdh_smart_agric_app/utils/request/daxing_bank_service.dart';
import 'package:dio/dio.dart';

abstract class BankService {
  BankService();

  //普通
  factory BankService.common() {
    return _common;
  }

  //大兴
  factory BankService.daxing() {
    return _daxing;
  }

  static final BankService _common = BankServiceImpl();

  static final BankService _daxing = DaxingBankServiceImpl();

  //我的银行卡列表
  Future<RequestResult<BankList, RequestException>> queryBank(
      {CancelToken? cancelToken});
  //添加银行卡
  Future<RequestResult<RequestNoData, RequestException>> addBankcard(
      {required String bankAccount,
      required String bankName,
      required int bankcardTypeNo,
      String? lineNumber,
      String? bankcardPhotoPath,
      String? network,
      String? bankAccountId,
      CancelToken? cancelToken});

  //修改银行卡
  Future<RequestResult<RequestNoData, RequestException>> updateBank(
      {required String bankAccountId,
      required String bankName,
      required int bankcardTypeNo,
      required String bankAccount,
      String? bankcardPhotoPath,
      String? lineNumber,
      String? network,
      CancelToken? cancelToken});
  //删除银行卡
  Future<RequestResult<RequestNoData, RequestException>> removeBankcard(
      {required String bankAccountId, CancelToken? cancelToken});
  //删除银行卡
  Future<RequestResult<RequestNoData, RequestException>> setDefault(
      {required String bankAccountId, CancelToken? cancelToken});
  //查找归属银行
  Future<RequestResult<RequestNoData, RequestException>> queryBankName(
      {required String bankAccount, CancelToken? cancelToken});
  //银行卡号识别
  Future<RequestResult<RequestNoData, RequestException>> bankCardOcr(
      {required String picUrl,
      required accountId,
      required String systemId,
      CancelToken? cancelToken});
  //获取银行卡类型字典
  Future<RequestResult<DictList, RequestException>> bankcardTypeNo(
      {CancelToken? cancelToken});
  //获取银行名称字典
  Future<RequestResult<DictList, RequestException>> bankName(
      {CancelToken? cancelToken});
  //字典接口
  Future<RequestResult<DictList, RequestException>> getDicByKey(String key,
      {CancelToken? cancelToken});
}

class BankServiceImpl extends BankService {
  //我的银行卡列表
  //https://lc.bdhic.com/bankcard/landcontractbankcard/queryBank
  @override
  Future<RequestResult<BankList, RequestException>> queryBank(
      {CancelToken? cancelToken}) async {
    var result = await baseHttp.postForResult(
        "/bankcard/landcontractbankcard/queryBank",
        cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(BankList.fromJson(r));
    });
  }

  //添加银行卡
  //https://lc.bdhic.com/bankcard/landcontractbankcard/addBankcard
  //bankAccount: "***************" 银行卡号
  //bankName: "1" //银行 id
  //bankcardTypeNo: 0 //银行卡类型
  //lineNumber: "************"  //联行号
  //network: "武汉" //开户网点
  //bankcardPhotoPath 存折图片路径

  @override
  Future<RequestResult<RequestNoData, RequestException>> addBankcard(
      {required String bankAccount,
      required String bankName,
      required int bankcardTypeNo,
      String? lineNumber,
      String? bankcardPhotoPath,
      String? network,
      String? bankAccountId,
      CancelToken? cancelToken}) async {
    var data = {
      "bankAccount": bankAccount,
      "bankName": bankName,
      "bankcardTypeNo": bankcardTypeNo
    };
    if (lineNumber != null) {
      data["lineNumber"] = lineNumber;
    }
    if (bankcardPhotoPath != null) {
      data["bankcardPhotoPath"] = bankcardPhotoPath;
    }
    if (network != null) {
      data["network"] = network;
    }
    if (bankAccountId != null) {
      data["bankAccountId"] = bankAccountId;
    }
    var requestUrl = "/bankcard/landcontractbankcard/addBankcard";
    if (bankAccountId != null) {
      requestUrl = "/bankcard/landcontractbankcard/updateBank";
    }

    Log.d("addBank request data is $data");
    var result = await baseHttp.postForResult(requestUrl,
        data: data, cancelToken: cancelToken);

    return result.transformSuccess<RequestNoData>((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //修改银行卡
  //https://lc.bdhic.com/bankcard/landcontractbankcard/updateBank
  //bankAccount: "***************" 银行卡号
  //bankName: "1" //银行 id
  //bankcardTypeNo: 0 //银行卡类型
  //lineNumber: "************"  //联行号
  //network: "武汉" //开户网点
  //bankcardPhotoPath 存折图片路径

  @override
  Future<RequestResult<RequestNoData, RequestException>> updateBank(
      {required String bankAccountId,
      required String bankName,
      required int bankcardTypeNo,
      required String bankAccount,
      String? bankcardPhotoPath,
      String? lineNumber,
      String? network,
      CancelToken? cancelToken}) async {
    var data = {
      "bankAccountId": bankAccountId,
      "bankAccount": bankAccount,
      "bankName": bankName,
      "bankcardTypeNo": bankcardTypeNo
    };
    if (lineNumber != null) {
      data["lineNumber"] = lineNumber;
    }
    if (bankcardPhotoPath != null) {
      data["bankcardPhotoPath"] = bankcardPhotoPath;
    }
    if (network != null) {
      data["network"] = network;
    }
    var result = await baseHttp.postForResult(
        "/bankcard/landcontractbankcard/updateBank",
        data: data,
        cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //删除银行卡
  //https://lc.bdhic.com/bankcard/landcontractbankcard/removeBankcard/$bankAccountId

  @override
  Future<RequestResult<RequestNoData, RequestException>> removeBankcard(
      {required String bankAccountId, CancelToken? cancelToken}) async {
    var result = await baseHttp.postForResult(
        "/bankcard/landcontractbankcard/removeBankcard/$bankAccountId",
        cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //设置默认银行卡？
  //https://lc.bdhic.com/bankcard/landcontractbankcard/setDefault/${bankAccountId}
  @override
  Future<RequestResult<RequestNoData, RequestException>> setDefault(
      {required String bankAccountId, CancelToken? cancelToken}) async {
    var result = await baseHttp.postForResult(
        "/bankcard/landcontractbankcard/setDefault/$bankAccountId",
        cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //https://lc.bdhic.com/bankcard/landcontractbankcard/queryBankNanme
  //查找归属银行
  //bankAccount: "***************"

  @override
  Future<RequestResult<RequestNoData, RequestException>> queryBankName(
      {required String bankAccount, CancelToken? cancelToken}) async {
    var result = await baseHttp.postForResult(
        "/bankcard/landcontractbankcard/queryBankNanme",
        data: {"bankAccount": bankAccount},
        cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //https://lc.bdhic.com/ocr/word/bankCardOcr
  //银行卡号识别
  //bankAccount: "***************"

  @override
  Future<RequestResult<RequestNoData, RequestException>> bankCardOcr(
      {required String picUrl,
      required accountId,
      required String systemId,
      CancelToken? cancelToken}) async {
    var data = {"picUrl": picUrl, "systemId": systemId, "accountId": accountId};
    var response = await authHttp.postForResult("/ocr/word/bankCardOcr",
        data: data, cancelToken: cancelToken);

    return response.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //获取银行卡类型字典
  //https://sso-pro.bdhic.com/sso/dict/list/bankcard_type_no

  @override
  Future<RequestResult<DictList, RequestException>> bankcardTypeNo(
      {CancelToken? cancelToken}) {
    return getDicByKey("bankcard_type_no", cancelToken: cancelToken);
  }

  //https://sso-pro.bdhic.com/sso/dict/list/bank_name
  //获取银行名称字典

  @override
  Future<RequestResult<DictList, RequestException>> bankName(
      {CancelToken? cancelToken}) {
    return getDicByKey("bank_name", cancelToken: cancelToken);
  }

  @override
  Future<RequestResult<DictList, RequestException>> getDicByKey(String key,
      {CancelToken? cancelToken}) async {
    var response = await ssoHttp.postForResult("/sso/dict/list/$key",
        cancelToken: cancelToken);

    return response.transformSuccess((r) {
      return ResultSuccess(DictList.fromJson(r));
    });
  }
}
