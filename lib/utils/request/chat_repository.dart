import 'dart:convert';
import 'dart:io';

import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import 'package:logger/logger.dart';

import '../../const/url_config_const.dart';
import '../../model/user_info_model.dart';
import '../storage_util.dart';

/// @file chat_repository.dart
///
/// <AUTHOR>
/// @date 2025/2/14 16:53
/// @description  Chat 相关接口
///
///
class ChatRepository {
  // 查询问题答案
  static Future<Response> queryChatMessages(
      String question, String conversationId) async {
    UserInfo? userInfo = StorageUtil.userInfo();
    Map<String, dynamic>? queryParameters = {
      "inputs": {},
      "query": question,
      "response_mode": "streaming",
      "conversation_id": conversationId,
      "user": userInfo?.data?.id
    };
    Options options = Options(
      // baseUrl: urlConfig.chatApi,
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer app-VrvoRwleWJs4MXGS5cBMgI6Z",
      },
      receiveTimeout: const Duration(milliseconds: 50000),
      responseType: ResponseType.stream,
    );

    // // 创建Dio实例
    // Dio dio = Dio(options);
    //
    // // 设置响应类型为stream
    // dio.options.responseType = ResponseType.stream;

    var response = await chatHttp.post("/v1/chat-messages",
        data: jsonEncode(queryParameters), options: options);
    // response.data.stream.listen((data) {
    //   Uint8List dataUint8List = Uint8List.fromList(data);
    //   String str = String.fromCharCodes(dataUint8List);
    //   print(str);
    // }, onDone: () {
    //   print("done");
    // });
    return response;
  }

  // 查询历史会话
  static Future<Response> queryHistorySession() async {
    UserInfo? userInfo = StorageUtil.userInfo();
    Map<String, dynamic>? queryParameters = {"user": userInfo?.data?.id};
    Options options = Options(
      // baseUrl: urlConfig.chatApi,
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer app-VrvoRwleWJs4MXGS5cBMgI6Z",
      },
      receiveTimeout: const Duration(milliseconds: 50000),
    );
    var response = await chatHttp.get("/v1/conversations",
        queryParameters: queryParameters, options: options);
    return response;
  }

  // 查询历史会话消息
  static Future<Response> queryHistoryMessage(String conversationId) async {
    UserInfo? userInfo = StorageUtil.userInfo();
    Map<String, dynamic>? queryParameters = {
      "user": userInfo?.data?.id,
      "conversation_id": conversationId
    };
    Options options = Options(
      // baseUrl: urlConfig.chatApi,
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer app-VrvoRwleWJs4MXGS5cBMgI6Z",
      },
      receiveTimeout: const Duration(milliseconds: 50000),
    );
    var response = await chatHttp.get("/v1/messages",
        queryParameters: queryParameters, options: options);
    return response;
  }

  // 查询停止消息响应
  static Future<Response> stopQuestionMessage(String taskId) async {
    UserInfo? userInfo = StorageUtil.userInfo();
    Map<String, dynamic>? queryParameters = {
      "user": userInfo?.data?.id,
    };
    Options options = Options(
      // baseUrl: urlConfig.chatApi,
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer app-VrvoRwleWJs4MXGS5cBMgI6Z",
      },
      receiveTimeout: const Duration(milliseconds: 50000),
    );
    var response = await chatHttp.post("/v1/chat-messages/$taskId/stop",
        options: options, data: jsonEncode(queryParameters));
    return response;
  }

  // 获取问题字典配置
  static Future<Response> queryQuestionDictionary() async {
    UserInfo? userInfo = StorageUtil.userInfo();
    Map<String, dynamic>? queryParameters = {
      "code": "",
      "description": "",
      "dictId": 0,
      "dictKey": "",
      "image": "",
      "level": 0,
      "name": "",
      "orderNum": 0,
      "parentCode": "",
      "question": "",
      "state": ""
    };
    Options options = Options(
      // baseUrl: urlConfig.chatApi,
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer app-VrvoRwleWJs4MXGS5cBMgI6Z",
      },
      receiveTimeout: const Duration(milliseconds: 50000),
    );
    var response = await microHttp.post(
        "/agric-app-gathering-api/chat/dict/getDictListTree",
        data: jsonEncode(queryParameters));
    return response;
  }

  /// 语音转文字
  static Future<Response> getVoiceOcr({required File audioFile}) async {
    var token = StorageUtil.userInfo()?.data?.token!;
    FormData formData = FormData.fromMap({
      'audioFile': await MultipartFile.fromFile(audioFile.path),
      'contentType': MediaType('audio', 'x-m4a'),
    });
      Options options = Options(
        headers: {
          "Content-Type": "multipart/form-data",
          "app": 1,
          "cop-token": token
        },
      );
    var response = await threePartyHttp.post(
        "/ocr/word/asr",
        data: formData,
        options: options
    );
    return response;
  }
}
