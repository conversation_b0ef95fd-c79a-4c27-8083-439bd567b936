import 'package:dio/dio.dart';
import 'package:bdh_smart_agric_app/model/cost_analysis_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:bdh_smart_agric_app/model/query_all_model.dart';
import 'package:bdh_smart_agric_app/model/query_home_total_model.dart';
import 'package:bdh_smart_agric_app/model/query_ledger_cost_detail_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_model.dart';
import 'package:bdh_smart_agric_app/model/farmer_info_model.dart';
import 'package:bdh_smart_agric_app/model/sys_model.dart';
import '../../model/query_contract_model.dart';
import 'dart:convert';

class CostAnalysisResponsitory {
  // 查询账本统计数据接口
  // params {"yearNo":2024,"organizationNo":"860788"}
  static Future<LedgerTotalResponse> queryLedgerTotal(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppLedger/queryLedgerTotal',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return LedgerTotalResponse.fromJson(response.data);
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查询合同接口
  // params {"yearNo":2024,"organizationNo":"860788"}
  static Future<QueryContractModel> queryContract(
      Map<String, dynamic> params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppLedger/queryContract',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );

    final queryContractModel = QueryContractModel.fromJson(response.data);
    if (queryContractModel.success) {
      return queryContractModel;
    }
    throw Exception(queryContractModel.msg);
  }

  // 新建账本接口
  // params {
  //   "ledgerName": "测试账本",
  //   "description": "测试描述",
  //   "organizationNo": "860788"
  // }
  static Future<LedgerResponseModel> insertLedger(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppLedger/insert',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    return LedgerResponseModel.fromJson(response.data);
  }

  // 删除账本接口
  static Future<DeleteLedgerResponseModel> logicDeleteByIds(List<dynamic> ids) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppLedger/logicDeleteByIds',
      data: ids,
      options: Options(
        headers: {
          'COSTANALYSIS_API': true,
          'Content-Type': 'application/json;charset=UTF-8'
        },
      ),
    );
    return DeleteLedgerResponseModel.fromJson(response.data);
  }

  // 新建记一笔接口
  // params {
  //   "ledgerId": "1",
  //   "type": "income",
  //   "amount": 100,
  //   "description": "测试收入",
  //   "category": "工资"
  // }
  static Future<RememberModel> insertRemember(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppCost/insert',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return RememberModel.fromJson(response.data['data']);
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查询全部账本接口
  // params {"organizationNo":"860788"}
  static Future<List<LedgerModel>> queryAllLedger(params,
      {CancelToken? cancelToken}) async {
    var response =
        await costanalysisHttp.post('/ledger/farmClothesAppLedger/queryAll',
            data: params,
            options: Options(
              headers: {'COSTANALYSIS_API': true},
            ),
            cancelToken: cancelToken);
    if (response.data['success'] == true) {
      return (response.data['data'] as List)
          .map((json) => LedgerModel.fromJson(json))
          .toList();
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查看记一笔详情接口
  // params: 记录ID
  static Future<RememberModel> infoRemember(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppCost/info/$params',
      data: {},
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return RememberModel.fromJson(response.data['data']);
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查看记一笔详情接口(财务核算）
  // params {"id": "1"}
  static Future<RememberModel> financialDetails(params) async {
    var response = await costanalysisHttp.post(
      '/farmer/cost/ledger/farmClothesAppCost/info',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return RememberModel.fromJson(response.data['data']);
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查看账本详情接口
  // params: 账本ID
  static Future<CropLedgerModel> infoLedger(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppLedger/info/$params',
      data: {},
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return CropLedgerModel.fromJson(response.data['data']);
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 删除记一笔接口
  // params {"ids": ["1", "2", "3"]}
  static Future<bool> deleteRememberIds(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppCost/logicDeleteByIds',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return true;
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查询首页统计数据接口
  // params {"yearNo":2024,"organizationNo":"860788"}
  static Future<QueryHomeModel> queryHomeTotal(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppCost/queryHomeTotal',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return QueryHomeModel.fromJson(response.data);
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查询首页列表接口
  // params {"yearNo":2024,"organizationNo":"860788","page":1,"size":10}
  static Future<List<QueryListModel>> queryHome(params) async {
    print('queryHome params: $params');
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppCost/queryHome',
      data: params,
    );
    print('queryHome response: ${response.data}');
    
    if (response.data['success'] == true) {
      // 直接使用整个响应创建QueryListModel
      return [QueryListModel.fromJson(response.data)];
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查询收支明细列表接口
  // params {"ledgerId":"1","page":1,"size":10}
  static Future<List<CostDetailModel>> queryLedgerCostDetail(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppCost/queryLedgerCostDetail',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return (response.data['data'] as List)
          .map((json) => CostDetailModel.fromJson(json))
          .toList();
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查询对比分析接口
  static Future<WorkDetailResponse> costCompare(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppCost/costCompare',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return WorkDetailResponse.fromJson(response.data);
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查询农时阶段树
  // params {"organizationNo":"860788"}
  static Future<List<Map<String, dynamic>>> getDetailTree(params) async {
    var response = await costanalysisHttp.post(
      '/project/projectWorkDetail/queryProjectWorkDetailTree',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return List<Map<String, dynamic>>.from(response.data['data']);
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

// 获取当前账号下的组织机构
  static Future<OrgTreeModel> getUserOrg([Map<String, dynamic>? params]) async {
    print('开始获取组织机构数据...');
    print('请求参数: $params');

    var response = await costanalysisHttp.post(
      '/org/amporg/queryOrgStationTreeByUserOrg',
      data: params ?? {},
      options: Options(
        headers: {
          'COSTANALYSIS_API': 'true',
          'app': '1',
          'bdh-code': 'bdh-app'
        },
      ),
    );

    print('请求头: ${response.requestOptions.headers}');
    print('getUserOrg response: ${response.data}');

    if (response.data == null) {
      throw Exception('返回数据为空');
    }
    if (response.data['success'] == true) {
      return OrgTreeModel.fromJson(response.data);
    }
    throw Exception(
        response.data['msg'] ?? '获取组织机构失败，错误码：${response.data['code']}');
  }

  // 判断是否注册成功
  static Future<FarmerInfoModel> lcfamerbyUserId(
      Map<String, dynamic> params) async {
    var response = await baseHttp.post(
      '/farmer/landcontractfarmers/lcfamerbyUserId',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    print('lcfamerbyUserId response: ${response.data}');

    if (response.data != null) {
      if (response.data['success'] == true) {
        return FarmerInfoModel.fromJson(response.data);
      }

      // 处理权限错误
      if (response.data['code'] == -1) {
        print('权限错误: ${response.data['msg']}');
        throw Exception(response.data['msg'] ?? '您的账号没有此数据的访问权限');
      }
    }

    throw Exception(response.data?['msg'] ?? '获取组织信息失败');
  }



  // 分账-删除记一笔接口
  // params {"ids": ["1", "2", "3"]}
  static Future<bool> deleteSubRememberIds(params) async {
    var response = await costanalysisHttp.post(
      '/farmer/cost/ledger/farmClothesAppCost/logicDeleteByIds',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return true;
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

  // 查询工作详情列表接口
  // params {"yearNo":2024,"organizationNo":"860788"}
  static Future<WorkDetailResponse> queryWorkDetailList(params) async {
    var response = await costanalysisHttp.post(
      '/ledger/farmClothesAppCost/queryWorkDetailList',
      data: params,
      options: Options(
        headers: {'COSTANALYSIS_API': true},
      ),
    );
    if (response.data['success'] == true) {
      return WorkDetailResponse.fromJson(response.data);
    }
    throw Exception(response.data['msg'] ?? '请求失败');
  }

   // 分账-查询全部账本接口
  static Future<CropLedgerResult> querySubAllLedger(dynamic params) async {
    var response = await costanalysisHttp.post(
      "/farmer/cost/ledger/farmClothesAppLedger/queryAll",
      data: params,
    );
    return CropLedgerResult.fromJson(response.data);
  }

  // 分账-查询首页统计数据接口
  static Future<LedgerCostResult> querySubHomeTotal(dynamic params) async {
    var response = await costanalysisHttp.post(
      "/farmer/cost/ledger/farmClothesAppCost/queryHomeTotal",
      data: params,
    );
    return LedgerCostResult.fromJson(response.data);
  }

  // 分账-查询收支明细列表接口
  static Future<LedgerCostDetailResult> querySubLedgerCostDetail(
      Map<String, dynamic> params) async {
    var response = await costanalysisHttp.post(
      "/farmer/cost/ledger/farmClothesAppCost/queryLedgerCostDetail",
      data: params,
    );
    return LedgerCostDetailResult.fromJson(response.data);
  }

   // 获取数据字典
  static Future<CropResponse> getDict(String dictKey,
      [String? organizationNo]) async {
    String dictUrl;
    Options options;

    // 根据dictKey和organizationNo构建URL
    if (organizationNo != null) {
      dictUrl = dictKey == 'bank_name'
          ? '/config/bankConfig/queryBanksById?organizationNo=$organizationNo'
          : '/sso/dict/list/$dictKey';
    } else {
      dictUrl = dictKey == 'bank_name'
          ? '/config/bankConfig/queryBanksById'
          : '/sso/dict/list/$dictKey';
    }

    // 根据dictKey设置不同的API header
    options = Options(
      headers: dictKey == 'bank_name' ? {'BASE_API': true} : {'SSO_API': true},
    );
    try {
      // 发送请求
      print('正在发送请求...');
      var response = await ssoHttp.post(
        // 改用ssoHttp
        dictUrl,
        data: {},
        options: options,
      );
      if (response.data == null) {
        throw Exception('返回数据为空');
      }

      // 使用CropResponse模型解析响应数据

      final cropResponse = CropResponse.fromJson(response.data);

      if (cropResponse.data.isNotEmpty) {}

      print('=================== 字典数据获取完成 ===================');

      if (cropResponse.success) {
        return cropResponse;
      }
      throw Exception(cropResponse.msg);
    } catch (e) {
      rethrow;
    }
  }

}
