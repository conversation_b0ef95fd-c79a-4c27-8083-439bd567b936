import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/app_menu_result_model.dart';
import 'package:bdh_smart_agric_app/model/bdh_app_query_person_result_model.dart';
import 'package:bdh_smart_agric_app/model/bdh_land_base_Info_result_model.dart';
import 'package:bdh_smart_agric_app/model/bdh_my_rotation_plan_result_model.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/my_validate_history_result_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import 'package:oktoast/oktoast.dart';

import '../../model/fertilize_suggest_model.dart';
import '../../model/land_model.dart';
import '../../pages/product/subsidy/entity/IC_card_list_model.dart';
import '../../pages/product/loanapply/model/quit_person_model.dart';
import '../../pages/product/loanapply/model/unlock_audit_model.dart';

//北大荒默认环境
class BdhLandResponsitory {
  //1.获取土地承包基本菜单
  static Future<AppRoleMenuResult> findAppRoleMenu(
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/farmer/landcontractfarmers/findAppRoleMenu",
        cancelToken: cancelToken);

    return AppRoleMenuResult.fromJson(response.data);
  }

  //2.字典接口
  static Future<DictList> getDicByKey(String key,
      {CancelToken? cancelToken}) async {
    var response =
        await ssoHttp.post("/sso/dict/list/$key", cancelToken: cancelToken);
    return DictList.fromJson(response.data);
  }

  //3.组织机构查询
  static Future<OrgTreeResult> getOrgData({CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/org/amporg/queryOrgTreeByUserOrg",
        cancelToken: cancelToken);
    return OrgTreeResult.fromJson(response.data);
  }

  //4.基本信息查询
  static Future<BdhLandBaseInfoResult> getResidentInfo(
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/farmer/landcontractfarmers/getResidentInfo",
        cancelToken: cancelToken);
    return BdhLandBaseInfoResult.fromJson(response.data);
  }

  //5.获取人脸
  static Future<RequestNoData> appNfRationApplyCheck(params,
      {CancelToken? cancelToken}) async {
    var response = await dio.post(
        "${urlConfig.baseApi}/ration/insert/appNfRationApplyCheck",
        options: Options(headers: {
          "app": 1,
          "cop-token": StorageUtil.userInfo()?.data?.token!
        }),
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  //6.法大大身份证识别
  static Future<RequestNoData> ocrIdCard(dynamic params,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post("/ocr/word/idCardOcr",
        data: params, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //7.基本信息更新
  static Future<RequestNoData> updateResidentInfo(data,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/farmer/landcontractfarmers/updateFarmFlag",
        data: data,
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //8.基本田申请个人信息查询
  static Future<AppQueryPersonInfoResult> appQueryPersonInfo(data,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/ration/rationplan/appQueryPersonInfo",
        data: data, cancelToken: cancelToken);
    return AppQueryPersonInfoResult.fromJson(response.data);
  }

  //9.基本田申请个人信息查询
  static Future<MyRationPlanResult> myrationplan(data,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/ration/rationplan/myrationplan",
        data: data, cancelToken: cancelToken);
    return MyRationPlanResult.fromJson(response.data);
  }

  //10.银行接口
  static Future<DictList> queryBanksById(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/config/bankConfig/queryBanksById",
        queryParameters: params, cancelToken: cancelToken);
    return DictList.fromJson(response.data);
  }

  //11.视频上传
  static Future<RequestNoData> uploadObsMp4File(
      String filePath, String fileName, MediaType? contentType,
      {CancelToken? cancelToken}) async {
    FormData formData = FormData.fromMap({
      "file": MultipartFile.fromFileSync(filePath,
          filename: fileName, contentType: contentType)
    });
    var response = await baseHttp.post("/file/uploadObsMp4",
        data: formData, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //12.人工审核信息保存
  static Future<RequestNoData> appNfSaveRationVerifyManual(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/rationverify/manual/appNfSaveRationVerifyManual",
        data: params,
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //13.直接保存
  static Future<RequestNoData> rationplanInsertSave(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/ration/rationplan/insert",
        data: params, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //14.新增单条刷脸数据
  static Future<RequestNoData> appNfSaveRationFaceDetail(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/rationface/detail/appNfSaveRationFaceDetail",
        data: params,
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //15.基本田资格认证历史
  static Future<MyValidateHistoryResult> myValidateHistory(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/rationvalidate/validate/myValidateHistory",
        data: params,
        cancelToken: cancelToken);
    return MyValidateHistoryResult.fromJson(response.data);
  }

  //16.基本田资格认证
  static Future<RequestNoData> appCheckPromiseCantract(params,
      {CancelToken? cancelToken}) async {
    var token = StorageUtil.userInfo()?.data?.token!;
    var response = await dio.post(
        "${urlConfig.baseApi}/ration/promise/appCheckPromiseCantractV2",
        data: params,
        options: Options(headers: {"app": 1, "cop-token": token}),
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //17.实名认证提交-数字北大荒
  static Future<RequestNoData> authDataVerification(params,
      {CancelToken? cancelToken}) async {
    var token = StorageUtil.userInfo()?.data?.token!;
    var response = await dio.post(
        "${urlConfig.baseApi}/farmer/landcontractfarmers/authDataVerification",
        data: params,
        options: Options(headers: {"app": 1, "cop-token": token}),
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //17.重新实名认证提交-数字北大荒
  static Future<RequestNoData> reAuthentication(params,
      {CancelToken? cancelToken}) async {
    var token = StorageUtil.userInfo()?.data?.token!;
    var response = await dio.post(
        "${urlConfig.baseApi}/farmer/landcontractfarmers/reAuthentication",
        data: params,
        options: Options(headers: {"app": 1, "cop-token": token}),
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //17.实名认证信息查询
  static Future<RequestNoData> getRealNameStatus(int accountId,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post(
        "/authentication/thirdPartyAuthentication/getRealNameStatus/$accountId",
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //18.实名认证信息查询(数字北大荒 new)
  static Future<RequestNoData> getRealNameStatusBDHDigtal(int accountId,
      {CancelToken? cancelToken}) async {
    var response = await ssoHttp.post(
        "/mob/auth/getFaceResultCommon/$accountId",
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }
  //Response ({"success":true,"code":0,"msg":"实名认证成功","data":"2"})

  //18.人脸轮询
  static Future<RequestNoData> personCertificationStatusSimple(int accountId,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post(
        "/authentication/thirdPartyAuthentication/personCertificationStatusSimple/$accountId",
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //18-1.认证返回结果轮训 把本手机的认证给新手机号
  static Future<RequestNoData> getFaceH5Result(
      {CancelToken? cancelToken}) async {
    var token = StorageUtil.userInfo()?.data?.token!;
    var response = await dio.post(
        "${urlConfig.authApi}/cloudProduct/api/getFaceH5Result",
        data: {},
        options: Options(headers: {"app": 1, "cop-token": token}),
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //19.获取人工审核手指个数
  static Future<RequestNoData> getGestureNumber(params,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post(
        "/authentication/thirdPartyAuthentication/getGestureNumber",
        data: params,
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //20.人工审核提交
  static Future<RequestNoData> uploadGesturePhoto(params,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post(
        "/authentication/thirdPartyAuthentication/uploadGesturePhoto",
        data: params,
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //21.判断身份证是否已经存在
  static Future<RequestNoData> existsFarmer(dynamic params) async {
    var response = await baseHttp.post(
      "/farmer/landcontractfarmers/existsFarmer",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //22.判断身份证是否已经存在
  static Future<RequestNoData> appSetSuccessTime(dynamic params) async {
    var response = await authHttp.post(
      "/ration/promise/appSetSuccessTime",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //23.机动地竞价 机动地地块查询 条件查询
  static Future<RequestNoData> getMarketLandList(dynamic params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
      "/market/app/getMarketLandList",
      data: params,
      cancelToken: cancelToken,
    );
    return RequestNoData.fromJson(response.data);
  }

  //24.机动地竞价 机动地地块申请竞拍
  static Future<RequestNoData> submitBid(dynamic params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
      "/market/app/applyAuction",
      data: params,
      cancelToken: cancelToken,
    );
    return RequestNoData.fromJson(response.data);
  }

  //25.机动地竞价 待支付 支付-选择支付方式 线下和线上
  static Future<RequestNoData> auctionPay(dynamic params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
      "/market/app/auctionPay",
      data: params,
      cancelToken: cancelToken,
    );
    return RequestNoData.fromJson(response.data);
  }

  //26.机动地竞价 取消地块申请
  static Future<RequestNoData> cancelApplication(dynamic params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
      "/market/app/applyCancel",
      data: params,
      cancelToken: cancelToken,
    );
    return RequestNoData.fromJson(response.data);
  }

  //27.机动地竞价 结果确认列表分页
  static Future<RequestNoData> getResultsList(dynamic params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
      "/market/app/getConfirmtLandList",
      data: params,
      cancelToken: cancelToken,
    );
    return RequestNoData.fromJson(response.data);
  }

  //28.机动地竞价 农户待确认确认
  static Future<RequestNoData> farmersSubmit(dynamic params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
      "/market/app/confirmtAuction",
      data: params,
      cancelToken: cancelToken,
    );
    return RequestNoData.fromJson(response.data);
  }

  //29.机动地竞价 结果确认数量
  static Future<RequestNoData> resultsNumber(dynamic params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
      "/market/app/getConfirmtLandTotal",
      data: params,
      cancelToken: cancelToken,
    );
    return RequestNoData.fromJson(response.data);
  }

  //30.查询合同签署状态
  static Future<RequestNoData> contractAuthStatus(
      String contractNo, String accountId) async {
    var response = await authHttp.post(
      "/authentication/thirdPartyAuthentication/neoSignerStatus/$contractNo/$accountId",
    );
    return RequestNoData.fromJson(response.data);
  }

  // 文件上传: 签字图片和照片
  static Future<RequestNoData> uploadFile(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/file/upload",
        data: params, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  static Future<DictList> getBankNameAllDict({CancelToken? cancelToken}) {
    return getDicByKey("bank_name");
  }

  static Future<DictList> getBankTypeNoDict({CancelToken? cancelToken}) {
    return getDicByKey("bankcard_type_no", cancelToken: cancelToken);
  }

  static Future<DictList> getFarmerIdentityDict({CancelToken? cancelToken}) {
    return getDicByKey("farmer_identity", cancelToken: cancelToken);
  }

  static Future<DictList> getNationDict({CancelToken? cancelToken}) {
    return getDicByKey("nation", cancelToken: cancelToken);
  }

  static Future<DictList> getRelationDict({CancelToken? cancelToken}) {
    return getDicByKey("relations", cancelToken: cancelToken);
  }

  static Future<DictList> getSexDict({CancelToken? cancelToken}) {
    return getDicByKey("sex", cancelToken: cancelToken);
  }

  //审核状态字典
  static Future<DictList> getAprovalStatusNoDict({CancelToken? cancelToken}) {
    return getDicByKey("aproval_status_no", cancelToken: cancelToken);
  }

  static Future<RequestNoData> addressSplit(String address,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post("/analize/addressSplit",
        data: {"address": address}, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //获取合同地块列表(合同签订入口)
  static Future<LandModel> getLandList(Map<String, dynamic> params) async {
    var response = await baseHttp.post(
      "/contract/import/queryContractDetails",
      data: params,
    );
    return LandModel.fromJson(response.data);
  }

  //获取合同地块列表(首页施肥卡提醒弹窗入口)
  static Future<LandModel> getSignedLandListFrom(
      Map<String, dynamic> params) async {
    var response = await baseHttp.post(
      "/contract/import/app/spreadCard/details",
      data: params,
    );
    return LandModel.fromJson(response.data);
  }

  //获取施肥建议卡
  static Future<FertilizeSuggestModel> getFertilizeSuggest(List params) async {
    var response = await fertilizeHttp.post(
      "/advice/soilFertAdviceCard/getCardByPlotNo",
      data: params,
    );
    return FertilizeSuggestModel.fromJson(response.data);
  }

  //获取合同地块列表
  static Future<RequestNoData> getFertilizeNotice(
      Map<String, dynamic> params) async {
    var response = await baseHttp.post(
      "/contract/import/app/spreadCard/notice",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //校验版本-土地承包
  static Future<RequestNoData> getAppVersionConfig(
      Map<String, dynamic> params) async {
    var response = await baseHttp.post(
      "/appNf/globalConfig/getAppVersionConfig",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //拦截审核列表
  static Future<UnlockAuditModel> getInterceptList(
      Map<String, dynamic> params) async {
    var response = await loanHttp.post(
      "/risk/loanIntercept/appInterceptRecord",
      data: params,
    );
    return UnlockAuditModel.fromJson(response.data);
  }

  //拦截审核发送验证码
  static Future<RequestNoData> sendInterceptSmsCode() async {
    var response = await loanHttp.post(
      "/risk/loanIntercept/appInterceptSendMessage",
    );
    return RequestNoData.fromJson(response.data);
  }

  //补贴反馈
  static Future<RequestNoData> subsidyFeedback(
      Map<String, dynamic> params) async {
    var response = await baseHttp.post(
      "/subsidy/apply/app/feedback",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  static Future<RequestNoData> interceptRecover(
      Map<String, dynamic> params) async {
    var response = await loanHttp.post(
      "/risk/loanIntercept/appInterceptRecover",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //银行卡列表
  static Future<IcCardListModel> getICCardList(
      Map<String, dynamic> params) async {
    var response = await baseHttp.post(
      "/subsidy/apply/app/getBankCard",
      data: params,
    );
    return IcCardListModel.fromJson(response.data);
  }

  //更新银行卡
  static Future<RequestNoData> updateBankCard(
      Map<String, dynamic> params) async {
    var response = await baseHttp.post(
      "/subsidy/apply/app/updateBankCard",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //退出人员列表
  static Future<QuitPersonModel> getQuitPersonList(
      Map<String, dynamic> params) async {
    var response = await loanHttp.post(
      "/risk/loanPullOutFarmer/appQueryQuitList",
      data: params,
    );
    QuitPersonModel model = QuitPersonModel.fromJson(response.data);
    return model;
  }
}
