import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

import 'request_no_data.dart';

class LiveService {
  factory LiveService() => _instance;

  static final LiveService _instance = LiveService._internal();

  LiveService._internal();

  Future<RequestNoData> _requestNoData(String url, dynamic data,
      {CancelToken? cancelToken}) {
    return microHttp
        .post(url, data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  //查询直播
  Future<RequestNoData> getList(dynamic data, {CancelToken? cancelToken}) =>
      _requestNoData("/agric-app-gathering-api/live/getList", data,
          cancelToken: cancelToken);

  //分享
  Future<RequestNoData> forward(dynamic uid, {CancelToken? cancelToken}) =>
      _requestNoData("/agric-app-gathering-api/live/forward/$uid", null,
          cancelToken: cancelToken);

  //新增直播观看记录
  Future<RequestNoData> watched(dynamic liveUid, {CancelToken? cancelToken}) =>
      _requestNoData("/agric-app-gathering-api/live/watched/$liveUid", null,
          cancelToken: cancelToken);

  //uid获取直播详情
  Future<RequestNoData> getByUid(dynamic uid, {CancelToken? cancelToken}) =>
      _requestNoData("/agric-app-gathering-api/live/getByUid/$uid", null,
          cancelToken: cancelToken);

  //首页获取浮窗直播roomId
  Future<RequestNoData> getShowLive(dynamic data, {CancelToken? cancelToken}) =>
      _requestNoData("/agric-app-gathering-api/live/getShowLive", data,
          cancelToken: cancelToken);
}
