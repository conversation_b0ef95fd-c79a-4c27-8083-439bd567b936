import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/release_bank_sms_model.dart';
import 'package:bdh_smart_agric_app/model/release_bank_submit_model.dart';
import 'package:bdh_smart_agric_app/model/release_target_submit_sms_model.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:dio/dio.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';

class Releaseaudit {
  //获取数据字典
  //字典接口
  static Future<DictList> getDict(String key) async {
    try {
      print('Getting dict for key: $key');
      var response = await ssoHttp.post("/sso/dict/list/$key");
      print('Dict API Response: $response');
      print('Dict API Response data: ${response.data}');
      
      if (response.data == null) {
        print('Warning: API response data is null');
        return DictList(success: false, code: -1, msg: 'No data received', data: []);
      }
      
      var result = DictList.fromJson(response.data);
      print('Parsed DictList: $result');
      
      if (result.data == null || result.data!.isEmpty) {
        print('Warning: Parsed DictList has no data');
      } else {
        print('DictList contains ${result.data!.length} items');
        for (var item in result.data!) {
          print('Item: code=${item.code}, name=${item.name}');
        }
      }
      
      return result;
    } catch (e, stackTrace) {
      print('Error getting dict for key $key: $e');
      print('Stack trace: $stackTrace');
      return DictList(success: false, code: -1, msg: 'Error getting dict', data: []);
    }
  }

  //获取解锁审核列表
  static Future<dynamic> getSourceReleaseList(params) async {
    try {
      print('Request params: $params');
      print('Request URL: /getSourceReleaseList');
      var token = StorageUtil.userInfo()?.data?.token;
      var response = await finaHttp.post("/app/api/getSourceReleaseList",
          options: Options(headers: {
            "cop-token": token,
            "app": 1,
            "bdh-code": "bdh-app-gathering"
          }),
          data: params);

      print('API Response11: ${response}');
      print('API Response status: ${response.statusCode}');
      print('API Response headers: ${response.headers}');
      print('API Response data: ${response.data}');

      return response.data;
    } catch (e) {
      print('API Error: $e');
      print('API Error stack: ${e is Error ? e.stackTrace : ''}');
      rethrow;
    }
  }

  //获取解锁审核记录列表
  static Future<dynamic> getTargetReleaseList(
      Map<String, dynamic> params) async {
    var token = StorageUtil.userInfo()?.data?.token;
    var response = await finaHttp.post("/app/api/getTargetReleaseList",
        options: Options(headers: {
          "cop-token": token,
          "app": 1,
          "bdh-code": "bdh-app-gathering"
        }),
        data: params);

    print('------------> $response.data');
    return response.data;
  }

  //提交解锁审核
  static Future<ReleaseTargetSubmitSmsResponse> releaseTargetSubmitSmS(
      ReleaseTargetSubmitSmsRequest request) async {
    var token = StorageUtil.userInfo()?.data?.token;
    var response = await finaHttp.post(
      "/api/app/releaseTargetSubmitSmS",
      data: request.toJson(),
      options: Options(headers: {
        "cop-token": token,
        "app": 1,
        "bdh-code": "bdh-app-gathering"
      }),
    );
    return ReleaseTargetSubmitSmsResponse.fromJson(response.data);
  }

  // 发送解锁短信验证码
  static Future<ReleaseBankSmsResponse> releaseBankSMS(
      ReleaseBankSmsRequest request) async {
    try {
      var token = StorageUtil.userInfo()?.data?.token;
      var response = await finaHttp.post(
        "/app/api/releaseBankSMS",
        data: request.toJson(),
        options: Options(headers: {
          "cop-token": token,
          "app": 1,
          "bdh-code": "bdh-app-gathering"
        }),
      );
      return ReleaseBankSmsResponse.fromJson(response.data);
    } catch (e) {
      print('Error sending SMS: $e');
      return ReleaseBankSmsResponse(
        code: -1,
        msg: '发送短信失败',
        data: null,
      );
    }
  }

  // 提交解锁审核
  static Future<ReleaseBankSubmitResponse> releaseBankSubmit(
      ReleaseBankSubmitRequest request) async {
    try {
      var token = StorageUtil.userInfo()?.data?.token;
      var response = await finaHttp.post(
        '/app/api/releaseBankSubmit',
        data: request.toJson(),
        options: Options(headers: {
          "cop-token": token,
          "app": 1,
          "bdh-code": "bdh-app-gathering"
        }),
      );
      return ReleaseBankSubmitResponse.fromJson(response.data);
    } catch (e) {
      print('Error submitting: $e');
      return ReleaseBankSubmitResponse(
        code: -1,
        msg: '提交失败',
        data: null,
      );
    }
  }
}
