import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';

class OnlinePayResponsitory {
  // 查询年份
  static Future<RequestNoData> querySelectYears(params) async {
    var response = await ssoHttp.post("/sso/dict/list/year_cd");
    return RequestNoData.fromJson(response.data);
  }

  // 查询银行列表
  static Future<RequestNoData> queryBankList(params) async {
    var response = await ssoHttp.post("/sso/dict/list/bank_name");
    return RequestNoData.fromJson(response.data);
  }

  // 文件上传: 签字图片和照片
  static Future<RequestNoData> uploadFile(params) async {
    var response = await baseHttp.post("/file/upload", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //查询待支付
  //params {"yearNo":2024,"organizationNo":null}
  static Future<RequestNoData> queryUnpaySubjectSummary(params) async {
    var response = await baseHttp.post(
        "/onLinecharge/chargePayment/queryUnpaySubjectSummary",
        data: params);
    return RequestNoData.fromJson(response.data);
  }

  //查询待支付详情
  //params {"yearNo":2024,"organizationNo":"860788"}
  static Future<RequestNoData> queryOnLineUnpaySubjectDetail(params) async {
    var response = await baseHttp.post(
        "/onLinecharge/chargePayment/queryOnLineUnpaySubjectDetail",
        data: params);
    return RequestNoData.fromJson(response.data);
  }

  //返回银行卡
  //params [{"organizationNo":"************","organizationName":"齐齐哈尔分公司-云上农场-第一管理区-第一作业站","chargeSubjectCode":"contract_charge","chargeSubjectName":"承包费","bankAccountList":[],"yearNo":2024,"unpayedAmount":2,"payItemType":1,"chargeCategoryType":1},{"organizationNo":"************","organizationName":"齐齐哈尔分公司-云上农场-第一管理区-第一作业站","chargeSubjectCode":"3914110","chargeSubjectName":"种植作物保证金","bankAccountList":[],"yearNo":2024,"unpayedAmount":70,"payItemType":1,"chargeCategoryType":2},{"organizationNo":"************","organizationName":"齐齐哈尔分公司-云上农场-第一管理区-第一作业站","chargeSubjectCode":"4474874","chargeSubjectName":"大豆复混肥","bankAccountList":[],"yearNo":2024,"unpayedAmount":110,"payItemType":1,"chargeCategoryType":2}]
  static Future<RequestNoData> cardValidentOrg(params) async {
    var response = await baseHttp
        .post("/onLinecharge/chargePayment/cardValidentOrg", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //支付确定
  // {
  //               "corporateBankCode":3,
  //               "farmerBankCode":4,
  //               "chargeAmount": 182,
  //               "telephone": ,
  //               "yearNo": 2024,
  //               "organizationNo": 86,
  //               "payOrderDetailList":"this.payOrderDetailList"
  //           }
  static Future<RequestNoData> chargePayment(params) async {
    var response = await baseHttp
        .post("/onLinecharge/chargePayment/saveOnLinePayOrder", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //查询待确认
  static Future<RequestNoData> querychargePayment(params) async {
    var response = await baseHttp.post("/onLinecharge/chargePayment/noaffirm",
        data: params);
    return RequestNoData.fromJson(response.data);
  }

  //获取支付状态
  static Future<RequestNoData> getpayOrderState(params) async {
    var response = await baseHttp
        .post("/onLinecharge/chargePayment/payOrderState", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //获取已支付列表
  static Future<RequestNoData> getColumnModel(params) async {
    var response = await baseHttp
        .post("/charge/phoneSerialPrint/getColumnModel", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //签字
  static Future<RequestNoData> updateSignPath(params) async {
    var response = await baseHttp
        .post("/charge/phoneSerialPrint/updateSignPath", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //拍照
  static Future<RequestNoData> updatePhoto(params) async {
    var response = await baseHttp.post("/charge/phoneSerialPrint/updatePhoto",
        data: params);
    return RequestNoData.fromJson(response.data);
  }

  //票据
  static Future<RequestNoData> printBillBatch(params) async {
    var response = await baseHttp
        .post("/charge/phoneSerialPrint/printBillBatch", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //获取是否存在该票据
  static Future<RequestNoData> isPhoneSerialPrint(params) async {
    var response = await baseHttp
        .post("/charge/phoneSerialPrint/printBillBatch", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //获取缴费状态--合同状态
  static Future<RequestNoData> getContractSignStatusList() async {
    var response = await baseHttp.post(
        "/onLinecharge/chargePayment/getContractSignStatusList",
        data: {});
    return RequestNoData.fromJson(response.data);
  }
}
