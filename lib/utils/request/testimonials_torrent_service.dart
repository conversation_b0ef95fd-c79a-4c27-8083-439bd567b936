import 'package:bdh_smart_agric_app/model/ped_detail_model.dart';
import 'package:bdh_smart_agric_app/model/ped_recommend_model.dart';
import 'package:bdh_smart_agric_app/model/recommend_model.dart';
import 'package:bdh_smart_agric_app/model/testimonials_torrent_base_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';

/// 荐种清单请求工具类
class TestimonialsTorrentService {
  /// 获取生态区字典接口
  static Future<List<TestimonialsTorrentBaseModel>> getZoology() async {
    var response =
        await jzHttp.post("/bdh-tech-park-api/client/dict/list/zoology_area");
    var jsonData = response.data['data'];
    if (jsonData == null) return [];
    List<TestimonialsTorrentBaseModel> resultData =
        jsonData.map<TestimonialsTorrentBaseModel>((item) {
      return TestimonialsTorrentBaseModel.fromJson(item);
    }).toList();
    return resultData;
  }

  /// 获取作物字典接口
  static Future<List<TestimonialsTorrentBaseModel>> getCropList() async {
    var response =
        await jzHttp.post("/bdh-tech-park-api/client/dict/list/sys_crop_phe");
    var jsonData = response.data['data'];
    if (jsonData == null) return [];
    List<TestimonialsTorrentBaseModel> resultData =
        jsonData.map<TestimonialsTorrentBaseModel>((item) {
      return TestimonialsTorrentBaseModel.fromJson(item);
    }).toList();
    return resultData;
  }

  /// 获取荐种列表接口
  static Future<List<PedRecommendModel>> getRecommendList(param) async {
    var response = await jzHttp
        .post("/bdh-tech-park-api/app/farming/queryRecommendList", data: param);
    var jsonData = response.data['data'];
    if (jsonData == null) return [];
    List<PedRecommendModel> resultData =
        jsonData.map<PedRecommendModel>((item) {
      return PedRecommendModel.fromJson(item);
    }).toList();
    return resultData;
  }

  /// 获取荐种任务详情接口
  static Future<RecommendModel?> recommend(pedRecommendId) async {
    var response =
        await jzHttp.get("/bdh-tech-park-api/ped/recommend/$pedRecommendId");
    var jsonData = response.data['data'];
    if (jsonData == null) return null;
    return RecommendModel.fromJson(jsonData);
  }

  /// 获取品种详情接口
  static Future<PedDetailModel?> getPedDetail(param) async {
    var response = await jzHttp.post(
        "/bdh-tech-park-api/pedigree/pedigreeInfo/detailByBreedCd",
        queryParameters: param);
    var jsonData = response.data['data'];
    print('当前返回的啥? $jsonData');
    if (jsonData == null) return null;
    return PedDetailModel.fromJson(jsonData);
  }
}
