import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';

class MessageNewResponsitory {
//获取消息列表all ok
  static Future getMessageListAllDate(params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/news/message/getAll", data: params);
    return response.data;
  }

//获取授权角色列表
  static Future getRoleList() async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/role/getList",
    );
    return response.data;
  }

  //获取消息列表 分页 ok
  static Future getMessageListDate(params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/news/message/getByPage", data: params);
    return response.data;
  }

  //获取'发布历史'列表 分页 ok
  static Future getMessageRecordListDate(params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/news/notice/getByPage", data: params);
    return response.data;
  }

  //未读消息数量 ok
  static Future getUnReadCnt() async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/news/message/getUnReadCnt",
    );
    return response.data;
  }

  //获取消息详情[消息列表 已读/未读] ok
  static Future getMessageDitailInfo(params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/news/message/getDetail",
        queryParameters: params);
    return response.data;
  }

  //获取消息详情[发布详情]
  static Future getPublishMessageDitailInfo(params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/news/notice/getDetail",
        queryParameters: params);
    return response.data;
  }

  //消息已读 ok
  static Future messageRead(messageId) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/news/message/read/$messageId",
    );
    return response.data;
  }

//通知范围组织 ok
  static Future getCurOrgListTree(params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/org/getChildList", data: params);
    return response.data;
  }

  //新增发布接口 ok
  static Future insertAndPublish(params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/news/notice/add", data: params);
    return response.data;
  }

  //修改通知接口 /news/notice/update
  static Future updateMessage(params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/news/notice/update", data: params);
    return response.data;
  }

// 提交审核
  static Future commitMsg(noticeId) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/news/notice/commit/$noticeId",
    );
    return response.data;
  }

  //撤回通知
  static Future revokeMes(noticeId) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/news/notice/rescind/$noticeId",
    );
    return response.data;
  }

  //删除通知
  static Future deletMes(noticeId) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/news/notice/delete/$noticeId",
    );
    return response.data;
  }
}
