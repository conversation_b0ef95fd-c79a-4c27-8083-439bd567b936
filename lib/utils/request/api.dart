import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import '../appcontext.dart';

_parseAndDecode(String response) {
  return jsonDecode(response);
}

parseJson(String text) {
  return compute(_parseAndDecode, text);
}

abstract class BaseHttp extends DioForNative {
  final dioLogger = PrettyDioLogger(
      requestHeader: true,
      requestBody: true,
      responseHeader: true,
      responseBody: true);
  BaseHttp() {
    (transformer as BackgroundTransformer).jsonDecodeCallback = parseJson;
    interceptors.add(HeaderInterceptor());
    init();
    if ((kDebugMode || kProfileMode) && Log.isActiveApi()) {
      enableLog();
    }
    // enableLog();
  }

  void enableLog() {
    if (interceptors.contains(dioLogger)) {
      return;
    }
    interceptors.add(dioLogger);
  }

  void disableLog() {
    interceptors.remove(dioLogger);
  }

  void init();

  //返回成功或错误
  Future<RequestResult<T, RequestException>> postForResult<T>(
    String path, {
    Object? data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      Response<T> response = await super.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
      if (response.data == null) {
        Log.e("postForResult error response.data == null");
        return const ResultError(RequestException.formatError);
      }
      Log.d("postForResult $path data is :${response.data}");
      return ResultSuccess(response.data as T);
    } catch (error, stackTrace) {
      Log.e("postForResult error", error: error, stackTrace: stackTrace);
      return ResultError(RequestException.handleError(error));
    }
  }
}

typedef TransformSuccessCallback<R1, R2, E1> = RequestResult<R2, E1> Function(
  R1 result,
);

typedef TransformCallback<R1, R2, E1, E2> = RequestResult<R2, E2> Function(
    R1?, E1?);

typedef SuccessCallback<R1> = void Function(
  R1 result,
);

typedef ErrorCallback<E1> = void Function(
  E1 error,
);

//请求结果，可能请求成功也可能请求失败
sealed class RequestResult<R1, E1> {
  const RequestResult();

  //返回成功结果
  R1? get success;
  //返回错误
  E1? get error;
  //是否错误
  bool get isError;
  //是否成功
  bool get isSuccess;
  //转换结果实体
  RequestResult<R2, E2> transform<R2, E2>(
      TransformCallback<R1, R2, E1, E2> callback);
  //转换成功结果实体
  RequestResult<R2, E1> transformSuccess<R2>(
      TransformSuccessCallback<R1, R2, E1> callback);
  //如果成功则触发
  RequestResult<R1, E1> onSuccess(SuccessCallback<R1> callback);
  //如果是错误则触发
  RequestResult<R1, E1> onError(ErrorCallback<E1> callback);
}

final class ResultSuccess<R1, E1> extends RequestResult<R1, E1> {
  final R1 _success;

  const ResultSuccess(this._success);

  @override
  bool get isError => false;

  @override
  bool get isSuccess => true;

  @override
  E1? get error => null;

  @override
  R1? get success => _success;

  @override
  int get hashCode => _success.hashCode;

  @override
  RequestResult<R2, E2> transform<R2, E2>(
          TransformCallback<R1, R2, E1, E2> callback) =>
      callback(_success, null);

  @override
  bool operator ==(Object other) {
    return other is ResultSuccess && other._success == _success;
  }

  @override
  RequestResult<R1, E1> onError(ErrorCallback<E1> callback) => this;

  @override
  RequestResult<R1, E1> onSuccess(SuccessCallback<R1> callback) {
    callback(_success);
    return this;
  }

  @override
  String toString() {
    return "ResultSuccess(_success:$_success)";
  }

  @override
  RequestResult<R2, E1> transformSuccess<R2>(
      TransformSuccessCallback<R1, R2, E1> callback) {
    return callback(_success);
  }
}

final class ResultError<R1, E1> extends RequestResult<R1, E1> {
  final E1 _error;

  const ResultError(this._error);

  @override
  bool get isError => true;

  @override
  bool get isSuccess => false;

  @override
  E1? get error => _error;

  @override
  R1? get success => null;

  @override
  int get hashCode => _error.hashCode;

  @override
  bool operator ==(Object other) {
    return other is ResultError && other._error == _error;
  }

  @override
  RequestResult<R2, E2> transform<R2, E2>(
          TransformCallback<R1, R2, E1, E2> callback) =>
      callback(null, _error);

  @override
  RequestResult<R1, E1> onError(ErrorCallback<E1> callback) {
    callback(_error);
    return this;
  }

  @override
  RequestResult<R1, E1> onSuccess(SuccessCallback<R1> callback) => this;

  @override
  String toString() {
    return "ResultError(_error:$_error)";
  }

  @override
  RequestResult<R2, E1> transformSuccess<R2>(
          TransformSuccessCallback<R1, R2, E1> callback) =>
      ResultError(_error);
}

class RequestException implements Exception {
  final int code;
  final String? message;

  const RequestException({required this.code, this.message});

  @override
  int get hashCode => code.hashCode;

  @override
  bool operator ==(Object other) {
    return other is RequestException && other.code == code;
  }

  bool get isCancel {
    return code == cancel.code;
  }

  @override
  String toString() {
    return "RequestException(code:$code,message:$message)";
  }

  static const RequestException unknownError =
      RequestException(code: -30000, message: "请求失败,请稍后再试[-30000]");
  static const RequestException formatError =
      RequestException(code: -30001, message: "网络传输错误,请稍后再试[-30001]");
  static const RequestException socketError =
      RequestException(code: -30002, message: "网络连接错误,请稍后再试[-30002]");
  static const RequestException httpError =
      RequestException(code: -30003, message: "请求失败,请稍后再试[-30003]");
  static const RequestException ioTimeout =
      RequestException(code: -30004, message: "请求超时,请稍后再试[-30004]");
  static const RequestException connectionTimeout =
      RequestException(code: -30005, message: "连接超时,请稍后再试[-30005]");
  static const RequestException badCertificate =
      RequestException(code: -30006, message: "请求失败,请稍后再试[-30006]");
  static const RequestException badResponse =
      RequestException(code: -30007, message: "请求失败,请稍后再试[-30007]");
  static const RequestException cancel = RequestException(code: -30008);
  static const RequestException connectionError =
      RequestException(code: -30009, message: "连接错误,请稍后再试[-30009]");
  static const RequestException sendTimeout =
      RequestException(code: -30010, message: "请求超时,请稍后再试[-30010]");
  static const RequestException receiveTimeout =
      RequestException(code: -30011, message: "请求超时,请稍后再试[-30011]");
  static const RequestException argumentError =
      RequestException(code: -30012, message: "网络传输错误,请稍后再试[-30012]");

  static RequestException handleDioError(DioException error) {
    return switch (error.type) {
      DioExceptionType.connectionTimeout => RequestException.connectionTimeout,
      //证书错误
      DioExceptionType.badCertificate => RequestException.badCertificate,
      //404,500等错误码
      DioExceptionType.badResponse => RequestException(
          code: (error.response?.statusCode ?? 0) + 30000,
          message: "请求失败,请稍后再试[${(error.response?.statusCode ?? 0) + 30000}]"),

      //用户主动取消请求，通过 cancelToken
      DioExceptionType.cancel => RequestException.cancel,
      DioExceptionType.connectionError => RequestException.connectionError,
      DioExceptionType.receiveTimeout => RequestException.receiveTimeout,
      DioExceptionType.sendTimeout => RequestException.sendTimeout,
      DioExceptionType.unknown => error.error == null
          ? RequestException.unknownError
          : handleError(error.error!)
    };
  }

  static RequestException handleError(Object? error) {
    if (error is DioException) {
      return handleDioError(error);
    } else if (error is SocketException) {
      return RequestException.socketError;
    } else if (error is HttpException) {
      return RequestException.httpError;
    } else if (error is FormatException) {
      //一般是服务器返回的数据没办法序列化成对于的 model
      return RequestException.formatError;
    } else if (error is ArgumentError) {
      //传入的参数错误，例如服务器地址，表单数据等
      return RequestException.argumentError;
    } else if (error is IOException) {
      return RequestException.ioTimeout;
    } else if (error is RequestException) {
      return error;
    } else {
      Log.d(" handleError unknown ${error.runtimeType.toString()}");
      return RequestException.unknownError;
    }
  }
}

class HeaderInterceptor extends InterceptorsWrapper {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    options.connectTimeout = const Duration(seconds: 30);
    options.receiveTimeout = const Duration(seconds: 30);
    handler.next(options);
  }
}
