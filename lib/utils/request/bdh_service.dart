import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/amap_search_result_model.dart';
import 'package:bdh_smart_agric_app/model/customer_service_info_model.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/privicy_request_model.dart';
import 'package:bdh_smart_agric_app/model/query_my_contract_result_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/model/sms_code_request_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/model/version_result.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:dio/dio.dart';

import '../../model/CommonDataModel.dart';

class BDHResponsitory {
  //----------------------注册登录接口------------------------
  //隐私协议、服务协议
  static Future<PrivicyRequestModel> getPrivicyAgreement() async {
    var response =
        await baseHttp.post("/policy/landcontractpolicy/getPolicyInfo/1");

    return PrivicyRequestModel.fromJson(response.data);
  }

  //登录接口
  // static Future<UserInfo> login(data, {CancelToken? cancelToken}) async {
  //   var response =
  //       await ssoHttp.post("/mob/login", data: data, cancelToken: cancelToken);
  //   return UserInfo.fromJson(response.data);
  // }
  static Future login(data, {CancelToken? cancelToken}) async {
    var response =
        await ssoHttp.post("/mob/login", data: data, cancelToken: cancelToken);
    return response.data;
  }

  //获取登录验证码
  static Future<SmsCodeRequestModel> getSmsCode(data) async {
    var response = await ssoHttp.post("/mob/smsCode", data: data);
    return SmsCodeRequestModel.fromJson(response.data);
  }

  //更新用户密码
  static Future<UserInfo> updatePwd(data, {CancelToken? cancelToken}) async {
    var response = await ssoHttp.post("/mob/update/pwd",
        data: data, cancelToken: cancelToken);
    return UserInfo.fromJson(response.data);
  }

  //更新用户信息
  static Future<UserInfo> updateUserInfo(data) async {
    var response = await ssoHttp.post("/sso/userinfo/update", data: data);
    return UserInfo.fromJson(response.data);
  }

  //获取注册验证码
  static Future<SmsCodeRequestModel> getRegisterSmsCode(data) async {
    var response = await ssoHttp.post("/mob/registerSmsCode", data: data);
    return SmsCodeRequestModel.fromJson(response.data);
  }

  //注册账号
  static Future<UserInfo> register(data, {CancelToken? cancelToken}) async {
    var response = await ssoHttp.post("/mob/register",
        data: data, cancelToken: cancelToken);
    return UserInfo.fromJson(response.data);
  }

  //退出登录
  static Future<UserInfo> logout(data) async {
    var response = await ssoHttp.get("/mob/logout");
    return UserInfo.fromJson(response.data);
  }

  //修改手机号
  static Future updateTel(data) async {
    var response = await ssoHttp.post("/sso/userinfo/updateTel", data: data);
    return response.data;
  }

  //修改土地承包手机号
  static Future updatePhone(String phone) async {
    var response = await baseHttp.post(
      "/farmer/landcontractfarmers/updatePhoneById/$phone",
    );
    return response.data;
  }

  //----------------------通用接口------------------------
  //文件上传
  static Future<BDHFile> uploadFile(dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/obs/file/upload",
      data: data,
    );
    return BDHFile.fromJson(response.data["data"]);
  }

  //文件上传,如果上传失败则返回错误
  static Future<RequestResult<BDHFile, RequestException>> uploadFileForResult(
      dynamic data) async {
    var result = await microHttp.postForResult(
      "/agric-app-gathering-api/obs/file/upload",
      data: data,
    );

    return result.transformSuccess((r) {
      return ResultSuccess(BDHFile.fromJson(r["data"]));
    });
  }

  static Future<CommonDataModel> uploadFileForResult2(dynamic data) async {
    var response = await plotHttp.post(
      "/file/upload",
      data: data,
    );
    return CommonDataModel.fromJson(response.data);
  }

  // 三普上传
  static Future<CommonDataModel> threeUploadFile(dynamic data) async {
    var response = await threeCensusesHttp.post(
      "/file/upload",
      data: data,
    );
    return CommonDataModel.fromJson(response.data);
  }

  //列表字典
  static Future<DictList> getDictList(dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/comm/dict/getDictListByKey",
      data: data,
    );
    return DictList.fromJson(response.data);
  }

  //树形字典
  static Future<DictTree> getDictTree(dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/comm/dict/getDictListTree",
      data: data,
    );
    return DictTree.fromJson(response.data);
  }

  //组织机构
  static Future<OrgTreeResult> getOrgTree(dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/org/getOrgListTree",
      data: data,
    );
    return OrgTreeResult.fromJson(response.data);
  }
  //高德地理反编码

  static Future<AMapSearchResult> getAMapSearch(keyword) async {
    var response = await dio
        .get("https://restapi.amap.com/v3/geocode/regeo", queryParameters: {
      "key": "820fcf9056b76fcbe6c2be266bf7ee76",
      "location": keyword,
    });
    return AMapSearchResult.fromJson(response.data);
  }

  //更新用户扩展信息
  static Future<RequestNoData> updateUserExtendInfo(dynamic data) async {
    var response = await microHttp.post(
      // "/agric-app-gathering-api/user/home/<USER>",
      "/agric-app-gathering-api/digital/user/device/update",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //api 发送消息 debug
  static Future<RequestNoData> sendNotification(dynamic data) async {
    var response = await microHttp.post(
      // "/agric-app-gathering-api/news/outside/v1/send",
      '/agric-app-gathering-api/digital/news/outside/v1/send',
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //更新用户扩展信息
  static Future<RequestNoData> shareToWeChat(dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/content/module/forward",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //游客登录
  static Future<UserInfo> touristLogin({CancelToken? cancelToken}) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/tourist/login",
        cancelToken: cancelToken);
    return UserInfo.fromJson(response.data);
  }

  //获取系统配置菜单
  static Future<MenuConfigResult> getConfig() async {
    var response =
        await microHttp.post("/agric-app-gathering-api/menu/getSubTree", data: {
      "authCodes": ["digMainMenu"]
    });
    return MenuConfigResult.fromJson(response.data);
  }

  //获取粮食交易菜单配置/menu/
  static Future<MenuConfigResult> getFoodMenuConfig() async {
    var response =
        await microHttp.post("/agric-app-gathering-api/menu/getSubTree", data: {
      "authCodes": ["ricetransaction1"]
    });

    return MenuConfigResult.fromJson(response.data);
  }

  //获取粮食交易菜单配置/menu/
  static Future<MenuConfigResult> getSubMenuConfig(String authCode,
      {CancelToken? cancelToken}) async {
    var response =
        await microHttp.post("/agric-app-gathering-api/menu/getSubTree",
            data: {
              "authCodes": [authCode]
            },
            cancelToken: cancelToken);

    return MenuConfigResult.fromJson(response.data);
  }

  //获取apk版本
  static Future<VersionResult> getVersionInfo() async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/app/version/detail",
        data: {"appId": urlConfig.androidApkId});
    return VersionResult.fromJson(response.data);
  }

  //账号预注销
  static Future<RequestNoData> preLogOff() async {
    var token = StorageUtil.userInfo()?.data?.token;
    var response = await ssoHttp.get("/mobile/preLogoff",
        options: Options(headers: {
          "cop-token": token,
          "app": 1,
          "bdh-code": "bdh-app-gathering"
        }));
    return RequestNoData.fromJson(response.data);
  }

  //账号注销
  static Future<RequestNoData> logOff() async {
    var token = StorageUtil.userInfo()?.data?.token;
    var response = await ssoHttp.get("/mobile/logoff",
        options: Options(headers: {
          "cop-token": token,
          "app": 1,
          "bdh-code": "bdh-app-gathering"
        }));
    return RequestNoData.fromJson(response.data);
  }

  //获取客服配置
  static Future<MenuConfigResult> getCustomerServiceConfig() async {
    var response = await microHttp
        .post("/agric-app-gathering-api/app/customer/getMenuTree");
    return MenuConfigResult.fromJson(response.data);
  }

  //查询客服信息
  static Future<CustomerServiceInfoModel> getCustomerServiceInfo(
      dynamic data) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/app/customer/queryCustomerService",
        data: data);
    // return MenuConfigResult.fromJson(response.data);
    return CustomerServiceInfoModel.fromJson(response.data);
  }

  //拨打客服电话上报
  static Future<MenuConfigResult> callRecordReport(dynamic data) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/app/customer/callRecordReport",
        data: data);
    return response.data;
  }

  // 获取txface 参数
  static Future<RequestNoData> facialAuth(data) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/user/authentication/facial/credential",
        data: data);
    return RequestNoData.fromJson(response.data);
  }

  //统计菜单点击
  static Future<RequestNoData> saveMenuUse(data) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/report/menu-click/create", data: data);
    return RequestNoData.fromJson(response.data);
  }

//获取数据字典
  static Future<dynamic> getDict(String dictKey,
      {String? organizationNo}) async {
    try {
      String dictUrl;

      // 根据是否有 organizationNo 构建 URL
      if (organizationNo != null) {
        dictUrl = dictKey == 'bank_name'
            ? "/config/bankConfig/queryBanksById?organizationNo=$organizationNo"
            : "/sso/dict/list/$dictKey";
      } else {
        dictUrl = dictKey == 'bank_name'
            ? "/config/bankConfig/queryBanksById"
            : "/sso/dict/list/$dictKey";
      }

      print('Dict request URL: $dictUrl');
      print('Dict request key: $dictKey');

      // 根据 dictKey 选择不同的 HTTP 实例
      var response = dictKey == 'bank_name'
          ? await baseHttp.post(dictUrl)
          : await ssoHttp.post(dictUrl);

      print('Dict response: ${response.data}');

      return response.data['data'];
    } catch (e) {
      print('Dict API Error: $e');
      rethrow;
    }
  }
}
