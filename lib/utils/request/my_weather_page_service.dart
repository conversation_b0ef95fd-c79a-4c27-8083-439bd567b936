import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/weather_layer_result_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

import '../../model/weather_day_info_model.dart';

//预测40天天气数据
class MyWeatherPageService {
  //天气实时
  static Future realTimeDataquery(data) async {
    var response = await waterManageHttp
        .post("/wpWetMsgDetail/wpWetMsgDetail/realTimeDataquery", data: data);
    return response.data;
  }

  //天气实时地块
  static Future<WeatherDayInfoModel> queryWeaInfo(dynamic data) async {
    var response = await waterManageHttp.post(
      "/wpWetdayInfo/wpWetdayInfo/queryWeaInfo",
      data: data,
    );
    return WeatherDayInfoModel.fromJson(response.data);
  }

  //天气未来5天
  static Future forecastWeatherByDay(data) async {
    var response = await waterManageHttp.post(
        "/wpWetMsgDetail/wpWetMsgDetail/forecastWeatherByDay",
        data: data);
    return response.data;
  }

  //天气48小时
  static Future forecastWeatherByhour(data) async {
    var response = await waterManageHttp.post(
        "/wpWetMsgDetail/wpWetMsgDetail/forecastWeatherByhour",
        data: data);
    return response.data;
  }

  //温度统计
  static Future accuTempeStatic(data) async {
    var response = await waterManageHttp
        .post("/wpWetdayInfo/wpWetdayInfo/accuTempeStatic", data: data);
    return response.data;
  }

  //时间范围内查询积温接口
  static Future queryWetCount(data) async {
    var response = await waterManageHttp
        .post("/wpWetdayInfo/wpWetdayInfo/queryWetCount", data: data);
    return response.data;
  }

  //降雨统计
  static Future accuRainStatic(data) async {
    var response = await waterManageHttp
        .post("/wpWetdayInfo/wpWetdayInfo/accuRainStatic", data: data);
    return response.data;
  }

  //范围降雨
  static Future queryRainCount(data) async {
    var response = await waterManageHttp
        .post("/wpWetdayInfo/wpWetdayInfo/queryRainCount", data: data);
    return response.data;
  }

  //日照统计
  static Future meteorologyBySunshine(data) async {
    var response = await waterManageHttp.post(
        "/wpWetMsgDetail/wpWetMsgDetail/meteorologyBySunshine",
        data: data);
    return response.data;
  }

  //范围日照
  static Future sumDaySunshine(data) async {
    var response = await waterManageHttp
        .post("/wpWetMsgDetail/wpWetMsgDetail/sumDaySunshine", data: data);
    return response.data;
  }

  //霜
  static Future frostFreePeriod(data) async {
    var response = await waterManageHttp
        .post("/wpWetMsgInfo/wpWetMsgInfo/frostFreePeriod", data: data);
    return response.data;
  }

  //生育期
  static Future growthPeriod(data) async {
    var response = await waterManageHttp
        .post("/wpWetdayInfo/wpWetdayInfo/growthPeriod", data: data);
    return response.data;
  }

  //每日积温度 //降雨
  static Future dailyAccuTemp(data) async {
    var response = await waterManageHttp
        .post("/wpWetdayInfo/wpWetdayInfo/dailyAccuTemp", data: data);
    return response.data;
  }

  //每日日照
  static Future dailySunshine(data) async {
    var response = await waterManageHttp
        .post("/wpWetMsgDetail/wpWetMsgDetail/dailySunshine", data: data);
    return response.data;
  }

  //累计日照折线
  static Future accuDaySunshine(data) async {
    var response = await waterManageHttp
        .post("/wpWetMsgDetail/wpWetMsgDetail/accuDaySunshine", data: data);
    return response.data;
  }

  //累计活动积温折线
  static Future accuDayTemp(data) async {
    var response = await waterManageHttp
        .post("/wpWetdayInfo/wpWetdayInfo/accuDayTemp", data: data);
    return response.data;
  }

  //累计降雨量折线
  static Future accuDayRain(data) async {
    var response = await waterManageHttp
        .post("/wpWetdayInfo/wpWetdayInfo/accuDayRain", data: data);
    return response.data;
  }

  //累计降雨量
  static Future<WeatherLayerResult> nowcastRainLayer() async {
    var response = await dio.get(
      "http://data-api.91weather.com/bdh/nowcast/rain/layer",
    );
    return WeatherLayerResult.fromJson(response.data);
  }
}
