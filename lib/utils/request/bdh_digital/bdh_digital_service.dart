import 'dart:convert';

import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/main_layout_Model_bdh_digital.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/sms_code_request_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/model/bdh_digital_face_verify_result_model.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/model/bdh_digital_identity_card_data_model.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/model/bdh_digital_modify_pwd_face_verify_result_model.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/model/bdh_digital_user_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/all_message_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/notice_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/notice_ditail_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/system_ditail_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:bdh_smart_agric_app/utils/request/request_no_data.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:dio/dio.dart';

class BdhDigitalService {
  //----------------------通用接口------------------------
  //文件上传
  static Future<BDHFile> uploadFileBdhDigital(dynamic data) async {
    var response = await ssoHttp.post(
      "/sso/file/upload?type=0&systemCode=digital-agriculture-app",
      data: data,
    );
    return BDHFile.fromJson(response.data["data"]);
  }

  //文件上传 身份识别
  static Future<BdhDigitalIdentityCardDataModel>
      authIdentityUploadFileBdhDigital(dynamic data) async {
    var response = await ssoHttp.post(
      "/sso/file/upload?type=1&systemCode=$kSystemCode",
      data: data,
    );

    return BdhDigitalIdentityCardDataModel.fromJson(response.data);
  }

  //组织机构
  static Future<OrgTreeResult> getCodeList(dynamic data) async {
    var response = await ssoHttp.post(
      "/sso/org/listByParent",
      data: data,
    );
    return OrgTreeResult.fromJson(response.data);
  }

//获取验证码
  static Future<SmsCodeRequestModel> getRegisterSmsCodeBdhDigital(data) async {
    var response = await ssoHttp.post("/mob/auth/registerSmsCode", data: data);
    return SmsCodeRequestModel.fromJson(response.data);
  }

  //获取 忘记密码 验证码
  static Future<SmsCodeRequestModel> getForgetPWDSmsCodeBDHDigital(data) async {
    var response = await ssoHttp.post("/mob/sendCode", data: data);
    return SmsCodeRequestModel.fromJson(response.data);
  }

  //修改手机号 验证码
  static Future<SmsCodeRequestModel> getModifyTelephoneSmsCodeBDHDigital(
      data) async {
    var response =
        await ssoHttp.post("/sso/userinfo/sendCodeByNewPhone", data: data);
    return SmsCodeRequestModel.fromJson(response.data);
  }

  //修改手机号 信息接口(人脸失败链接)
  static Future<BdhDigitalUserModel> getModifyTelephoneInfoBDHDigital(
      data) async {
    var response =
        await ssoHttp.post("/sso/userinfo/checkUser4UpdatePhone", data: data);
    return BdhDigitalUserModel.fromJson(response.data);
  }

//修改手机号 > faceVerfiy result
  static Future<BdhDigitalModifyPWDFaceVerifyResultModel>
      getModidfyTelephoneFaceResultBdhDigital(dynamic data) async {
    var response = await ssoHttp.post(
      "/sso/userinfo/getFaceResult4UpdatePhone",
      data: data,
    );
    return BdhDigitalModifyPWDFaceVerifyResultModel.fromJson(response.data);
  }

  // 修改手机号> 提交修改
  static Future<BdhDigitalUserModel> updateTelephonNumberBdhDigital(
      dynamic data) async {
    var response = await ssoHttp.post(
      "/sso/userinfo/updateTelByFace",
      data: data,
    );
    return BdhDigitalUserModel.fromJson(response.data);
  }

//  /mob/auth/checkUpdatePassword
  static Future<BdhDigitalUserModel> checkUpdatePasswordBDHDigital(data) async {
    var response =
        await ssoHttp.post("/mob/auth/checkUpdatePassword", data: data);
    return BdhDigitalUserModel.fromJson(response.data);
  }

//忘记密码 > faceVerfiy result
  static Future<BdhDigitalModifyPWDFaceVerifyResultModel>
      getModifyPWDFaceResultBdhDigital(dynamic data) async {
    var response = await ssoHttp.post(
      "/mob/auth/getFaceResult4Password",
      data: data,
    );
    return BdhDigitalModifyPWDFaceVerifyResultModel.fromJson(response.data);
  }

  // 忘记密码> 更新密码
  static Future<BdhDigitalUserModel> updatePasswordBdhDigital(
      dynamic data) async {
    var response = await ssoHttp.post(
      "/mob/auth/updatePassword",
      data: data,
    );
    return BdhDigitalUserModel.fromJson(response.data);
  }

  //注册接口
  static Future<BdhDigitalUserModel> registerBdhDigital(dynamic data) async {
    var response = await ssoHttp.post(
      "/mob/auth/register",
      data: data,
    );
    return BdhDigitalUserModel.fromJson(response.data);
  }

//获取faceVerfiy result
  static Future<BdhDigitalFaceVerifyResultModel> getFaceResultBdhDigital(
      dynamic data) async {
    var response = await ssoHttp.post(
      "/mob/auth/getFaceResult",
      data: data,
    );
    return BdhDigitalFaceVerifyResultModel.fromJson(response.data);
  }

//实名认证信息查询
  static Future<RequestNoData> getRealNameStatusBdhDigital(
      int accountId) async {
    var response =
        await ssoHttp.post("/mob/auth/getFaceResultCommon/$accountId");
    return RequestNoData.fromJson(response.data);
  }

  //实名认证提交-数字北大荒
  static Future<RequestNoData> authDataVerificationBdhDigital(params) async {
    var response = await baseHttp.post(
      "/farmer/farmermanage/authDataVerification",
      data: params,
    );
    // var token = StorageUtil.userInfo()?.data?.token!;
    // var response = await dio.post(
    //   "${urlConfig.baseApi}/farmer/farmermanage/authDataVerification",
    //   data: params,
    //   options: Options(headers: {"app": 1, "cop-token": token}),
    // );
    return RequestNoData.fromJson(response.data);
  }

  //查询是否绑定oa
  static Future<RequestNoData> oaIsBindBdhDigital() async {
    var response = await authHttp.post(
      "/seeyon/api/isBindingSeeyonAccount",
    );
    return RequestNoData.fromJson(response.data);
  }

//查询已经认证用户信息
  static Future<UserInfo> getStaffInfoBdhDigital(dynamic data) async {
    var response = await ssoHttp.post(
      "/sso/data/getStaffInfoForOrgChg",
      data: data,
    );
    return UserInfo.fromJson(response.data);
  }

  //申请 变更组织
  static Future<RequestNoData> getChangeOrgApplyBdhDigital(dynamic data) async {
    var response = await ssoHttp.post(
      "/sso/data/orgChangeApply",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //查询菜单> 通讯录
  static Future<MenuConfigResult> getAppCenterMenus() async {
    var response = await authHttp.post(
      "/seeyon/api/getAppCenterMenus",
    );
    return MenuConfigResult.fromJson(response.data);
  }

  //查询配置菜单 > 获取'协同办公'item
  static Future<MainLayoutModelBdhDigital> getMainLayoutBdhDigital() async {
    var response = await jzHttp.post("/layout/getMainLayout");
    if (response.data['code'] == 0 && response.data['success'] == true) {
      return MainLayoutModelBdhDigital.fromJson(response.data);
    } else {
      return MainLayoutModelBdhDigital(
          success: true,
          code: 0,
          data: MainLayoutData(categories: []),
          msg: '');
    }
    // return MainLayoutModelBdhDigital.fromJson(response.data);
  }

  // 二级菜单
  static Future<RequestNoData> getMenuBdhDigital(
      {Object? data, CancelToken? cancelToken}) async {
    return jzHttp
        .post("/auth/userMenu/getMenu", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // {
  //   menuName:item.text,//菜单
  //   component: item.path,//页面路径
  //   operateType:'in',
  //   parMenuName: this.name?this.name.split(':')[1]:'农情服务',//父级菜单
  // }
  static Future<RequestNoData> saveUsageBdhDigital(
      {Object? data, CancelToken? cancelToken}) async {
    return jzHttp
        .post("/usage/saveUsage", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  //扫码成功
  static Future<UserInfo> scanSuccessBdhDigital(dynamic data) async {
    var response = await ssoHttp.post(
      "/mob/scan",
      data: data,
    );
    return UserInfo.fromJson(response.data);
  }

  //扫码登录成功
  static Future<UserInfo> scanLoginSuccessBdhDigital(dynamic data) async {
    var response = await ssoHttp.post(
      "/mob/scan/login",
      data: data,
    );
    return UserInfo.fromJson(response.data);
  }

  //消息, 获取'全部' [1,协同办公, 2.新闻公告  全部数据]
  static Future<AllMessageBdhDigitalModel> getAllMessagesBdhDigital(
      Map<String, dynamic> params) async {
    var response = await authHttp.post(
      "/seeyon/api/getAllMessages",
      data: params,
    );
    // String json = jsonEncode(response.data);
    AllMessageBdhDigitalModel model =
        AllMessageBdhDigitalModel.fromJson(response.data);

    return model;
  }

//获取'未读' 数据[1,协同办公, 2.新闻公告, 未读数据]
  static Future<AllMessageBdhDigitalModel> getUnReadMessagesBdhDigital(
      Map<String, dynamic> params) async {
    var response = await authHttp.post(
      "/seeyon/api/getAllUnReadMessages",
      data: params,
    );
    // String json = jsonEncode(response.data);
    AllMessageBdhDigitalModel model =
        AllMessageBdhDigitalModel.fromJson(response.data);

    return model;
  }

//获取 业务通知
  static Future<NoticeBdhDigitalModel> getNoticeListBdhDigital(
      Map<String, dynamic> params) async {
    var response = await buisHttp.post(
      "/publish/app/queryByPageAppNoticeList",
      data: params,
    );
    NoticeBdhDigitalModel model = NoticeBdhDigitalModel.fromJson(response.data);
    return model;

    ///    "noticeContent" -> "<p>测试</p>"
  }

//获取 业务消息
  static Future<NoticeBdhDigitalModel> getSystemListBdhDigital(
      Map<String, dynamic> params) async {
    var response = await buisHttp.post(
      "/publish/app/queryByPageAppSystemList",
      data: params,
    );
    NoticeBdhDigitalModel model = NoticeBdhDigitalModel.fromJson(response.data);
    return model;

    ///    "noticeContent" -> "<p>测试</p>"
  }

//设置为消息已读
  static Future<AllMessageBdhDigitalModel> changeMessageToReadBdhDigital(
      Map<String, dynamic> params) async {
    var response = await authHttp.post(
      "/seeyon/api/changeMessageToRead",
      data: params,
    );
    AllMessageBdhDigitalModel model =
        AllMessageBdhDigitalModel.fromJson(response.data);

    return model;
  }

//获取'业务通知'详情
  static Future<NoticeDitailBdhDigitalModel> noticeDitailInfoBdhDigital(
      int msgID) async {
    var response = await buisHttp.post(
      "/publish/app/noticeInfo/$msgID",
    );
    return NoticeDitailBdhDigitalModel.fromJson(response.data);
  }

//获取'业务消息'详情
  static Future<SystemDitailBdhDigitalModel> systemDitailInfoBdhDigital(
      int msgID) async {
    var response = await buisHttp.post(
      "/publish/app/systemInfo/$msgID",
    );
    return SystemDitailBdhDigitalModel.fromJson(response.data);
  }

  //获取业务通知未读
  static Future<RequestNoData> getUnreadNoticeBdhDigital(
      Map<String, dynamic> params) async {
    var response = await buisHttp.post(
      "/publish/public/queryUnread",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //获取系统通知未读
  static Future<RequestNoData> getUnreadSystemBdhDigital(
      Map<String, dynamic> params) async {
    var response = await buisHttp.post(
      "/publish/public/queryUnreadSys",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //清除所有未读消息 step1
  static Future<RequestNoData> changeAllUnReadMessageToRead(
      Map<String, dynamic> params) async {
    var response = await authHttp.post(
      "/seeyon/api/changeAllUnReadMessageToRead",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //清除所有未读消息 step2
  static Future<RequestNoData> handleClearAllBdhDigital(
      Map<String, dynamic> params) async {
    var response = await buisHttp.post(
      "/publish/app/readAll",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //星级排行
  static Future<RankBdhDigitalModel> getRankBdhDigital(
      Map<String, dynamic> params) async {
    var response = await appApiHttp.post(
      "/eval/publicity/app/farmer/rank",
      data: params,
    );
    RankBdhDigitalModel model = RankBdhDigitalModel.fromJson(response.data);
    return model;
  }

  //字典接口
  static Future<DictList> getDicByKey(String key) async {
    var response = await ssoHttp.post("/sso/dict/list/$key");
    return DictList.fromJson(response.data);
  }
}
