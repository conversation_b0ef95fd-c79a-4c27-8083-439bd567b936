import 'package:bdh_smart_agric_app/model/dia_apply_filed_model.dart';
import 'package:bdh_smart_agric_app/model/dia_authent_model.dart';
import 'package:bdh_smart_agric_app/model/examine_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/model/secondary_menu_modal.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';

class BdhDigitalLand {
  //获取是否可以点击签字和拍照
  static Future<RequestNoData> secondaryMenu(dynamic data) async {
    var response = await baseHttp.post(
      "/charge/chargePayment/getPrepareBillSignData",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //点击预收承包费计划按钮
  static Future<RequestNoData> appGetCurrentAuditUser(dynamic data) async {
    var response = await baseHttp.post(
      "/charge/contractPlan/appGetCurrentAuditUser",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //分页查询预收承包费
  static Future<ExamineModel> appQueryByPage(dynamic data) async {
    var response = await baseHttp.post(
      "/charge/contractPlan/appQueryByPage",
      data: data,
    );
    return ExamineModel.fromJson(response.data);
  }

  //预收承包费审核通过
  static Future<RequestNoData> appAcceptByIds(dynamic data) async {
    var response = await baseHttp.post(
      "/charge/contractPlan/appAcceptByIds",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //预收承包费审核拒绝
  static Future<RequestNoData> appRejectByIds(dynamic data) async {
    var response = await baseHttp.post(
      "/charge/contractPlan/appRejectByIds",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //数字北大荒app-基本田申请列表
  static Future<DiaApplyFiledModel> appSzRationPlanList(dynamic data) async {
    var response = await baseHttp.post(
      "/appSz/rationPlan/queryByPageApply",
      data: data,
    );
    return DiaApplyFiledModel.fromJson(response.data);
  }

  //数字北大荒app-基本田申请列表---拒绝
  static Future<RequestNoData> appSzAuditCancelByIdsApply(dynamic data) async {
    var response = await baseHttp.post(
      "/appSz/rationPlan/auditCancelByIdsApply",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //数字北大荒app-开始申请基本田
  static Future<RequestNoData> appSzStartRationPlanApply(dynamic data) async {
    var response = await baseHttp.post(
      "/appSz/rationPlan/startRationPlanApply",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //数字北大荒app-新增单挑刷脸记录
  static Future<RequestNoData> appSzSaveRationPlanFaceDetailApply(
      dynamic data) async {
    var response = await baseHttp.post(
      "/appSz/rationPlan/saveRationFaceDetailApply",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //数字北大荒app-基本田认证列表
  static Future<DiaAuthentModel> appSzValidateList(dynamic data) async {
    var response = await baseHttp.post(
      "/appSz/rationPlan/queryByPageValidate",
      data: data,
    );
    return DiaAuthentModel.fromJson(response.data);
  }

  //数字北大荒app-开始认证基本田
  static Future<RequestNoData> appSzStartRationPlanValidate(
      dynamic data) async {
    var response = await baseHttp.post(
      "/appSz/rationPlan/startRationPlanValidate",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //数字北大荒app-认证新增单挑刷脸记录
  static Future<RequestNoData> appSzSaveRationFaceDetailValidate(
      dynamic data) async {
    var response = await baseHttp.post(
      "/appSz/rationPlan/saveRationFaceDetailValidate",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //数字北大荒app-基本田判断是否需要管理人员刷脸
  static Future<RequestNoData> getRationVerifyConfig(dynamic data) async {
    var response = await baseHttp.post(
      "/appSz/rationPlan/getRationVerifyConfig",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //数字北大荒签字
  static Future<RequestNoData> saveBillSignData(dynamic data) async {
    var response = await baseHttp.post(
      "/charge/chargePayment/saveBillSignData",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //1获取手机号 2 获取验证码 3 验证验证码
  static Future<RequestNoData> phoneMessageCheck(dynamic data) async {
    var response = await baseHttp.post(
      "/lc/global/phoneMessageCheck",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //审核通过
  static Future<RequestNoData> contractPlanAppAcceptByIds(dynamic data) async {
    var response = await baseHttp.post(
      "/charge/contractPlan/appAcceptByIds",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }

  //审核拒绝
  static Future<RequestNoData> contractPlanAppppRejectByIds(
      dynamic data) async {
    var response = await baseHttp.post(
      "/charge/contractPlan/appRejectByIds",
      data: data,
    );
    return RequestNoData.fromJson(response.data);
  }
}
