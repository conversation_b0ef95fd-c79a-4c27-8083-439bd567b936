import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

import '../../model/three_pointquery_info.dart';
import '../../model/three_received_list.dart';
import '../../model/three_received_list_infodart.dart';
import '../../model/three_samplingPoint_queryAll_model.dart';

class ThreeCensusesPageService {
  // 获取采样点列表
  static Future<ThreeSamplingPointQueryAllModel> samplingPointAll(dynamic data) async {
    var response = await threeCensusesHttp.post(
      "/soil/app/api/samplingPoint/queryAll",
      data: data,
    );
    return ThreeSamplingPointQueryAllModel.fromJson(response.data);
  }

  // 地图展示
  static Future<ThreeSamplingPointQueryAllModel> queryByBoundary(dynamic data) async {
    var response = await threeCensusesHttp.post(
      "/soil/app/api/samplingPoint/queryByBoundary",
      data: data,
    );
    return ThreeSamplingPointQueryAllModel.fromJson(response.data);
  }

  // 采样点详情
  static Future<ThreePointqueryInfo> queryByIdInfo(dynamic data) async {
    var response = await threeCensusesHttp.post(
      "/soil/app/api/samplingPoint/queryById",
      data: data,
    );
    return ThreePointqueryInfo.fromJson(response.data);
  }
  // 样品接收列表
  static Future<ThreeReceivedList> getSampleConsignList(dynamic data) async {
    var response = await threeCensusesHttp.post(
      "/soil/app/api/sampleConsign/getSampleConsignByPage",
      data: data,
    );
    return ThreeReceivedList.fromJson(response.data);
  }

  // 样品问题提交
  static Future getSampleConsignSave(dynamic data) async {
    var response = await threeCensusesHttp.post(
      "/soil/app/api/sampleInspect/save",
      data: data,
    );
    return response.data;
  }


  // 样品详情
  static Future<ThreeReceivedListInfodart> getSampleConsignInfo(String batchCode) async {
    var response = await threeCensusesHttp.post(
      "/soil/app/api/sampleConsign/info/$batchCode",
    );
    return ThreeReceivedListInfodart.fromJson(response.data);
  }


  // 签收
  static Future sampleConsignSubmit(dynamic data) async {
    var response = await threeCensusesHttp.post(
      "/soil/app/api/sampleConsign/receive",
      data: data,
    );
    return response.data;
  }
  // 采样点提交
  static Future threePointSubmit(dynamic data) async {
    var response = await threeCensusesHttp.post(
      "/soil/app/api/samplingPoint/submit",
      data: data,
    );
    return response.data;
  }



  //获取字典
  static Future<DictList> getDicByKey(String key,
      {CancelToken? cancelToken}) async {
    var response =
    await ssoHttp.post("/sso/dict/list/$key", cancelToken: cancelToken);
    return DictList.fromJson(response.data);
  }



}
