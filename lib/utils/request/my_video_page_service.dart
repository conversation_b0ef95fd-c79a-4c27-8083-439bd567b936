import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

import '../../model/pic_diff_model.dart';
import '../../model/video_pager_model.dart';

class MyVideoPageService {
  //获取rtsp视频流地址
  static Future wetIotDeviceInfoLive(data) async {
    var response = await waterManageHttp
        .post("/wetIotDeviceInfo/wetIotDeviceInfo/live", data: data);
    return response.data;
  }

  //获取rtsp视频流地址 分页
  static Future<VideoPagerModel> videoEvent(data) async {
    var response = await waterManageHttp
        .post("/wetIotDeviceInfo/wetIotDeviceInfo/videoEvent", data: data);
    return VideoPagerModel.fromJson(response.data);
  }

  //获取rtsp视频流地址回放
  static Future videoPlayback(data) async {
    var response = await waterManageHttp
        .post("/wetIotDeviceInfo/wetIotDeviceInfo/videoPlayback", data: data);
    return response.data;
  }

  //获取rtsp视频videoChannel
  static Future videoChannel(data) async {
    var response = await waterManageHttp
        .post("/wetIotDeviceInfo/wetIotDeviceInfo/videoChannel", data: data);
    return response.data;
  }

  //控制开始
  static Future controlStart(data) async {
    var response = await waterManageHttp
        .post("/wetIotDeviceInfo/wetIotDeviceInfo/controlStart", data: data);
    return response.data;
  }

  //控制结束
  static Future controlStop(data) async {
    var response = await waterManageHttp
        .post("/wetIotDeviceInfo/wetIotDeviceInfo/controlStop", data: data);
    return response.data;
  }

  //图片对比
  static Future<PicDiffModel> videoPhotoView(data) async {
    // var response = await baseHttp
    //     .post("/planting/plantingOperationTask/app/queryPlotList", data: data);
    // var response =
    //     await baseHttp.post("/planting/archive/queryByPage", data: data);
    var response = await waterManageHttp
        .post("/wetIotDeviceInfo/wetIotDeviceInfo/videoPhotoView", data: data);
    return PicDiffModel.fromJson(response.data);
  }
}
