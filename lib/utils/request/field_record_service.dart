import 'package:bdh_smart_agric_app/model/CommonDataModel.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/video_detail_model.dart';
import 'package:bdh_smart_agric_app/model/video_page_result_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';

import '../../model/field_patrol_detail_model.dart';
import '../../model/fild_map_list_model.dart';
import '../../model/fild_patrol_list_model.dart';

class FieldRecordService {

  //查询列表巡田
  static Future<FildPatrolListModel> queryFarmerByPage(dynamic data) async {
    var response = await plotHttp.post(
      "/openapi/plot/myland/asLandPatrol/queryFarmerByPage",
      data: data,
    );
    return FildPatrolListModel.fromJson(response.data);
  }

  //查询列表巡田全部
  static Future<FildMapListModel> queryFarmerByAll(dynamic data) async {
    var response = await plotHttp.post(
      "/openapi/plot/myland/asLandPatrol/queryFarmerByAll",
      data: data,
    );
    return FildMapListModel.fromJson(response.data);
  }
  //巡田添加
  static Future<CommonDataModel> farmerInsert(dynamic data) async {
    var response = await plotHttp.post(
      "/openapi/plot/myland/asLandPatrol/insert",
      data: data,
    );
    return CommonDataModel.fromJson(response.data);
  }
  //获取详情
  static Future<FieldPatrolDetailModel> getOneFarmer(dynamic data) async {
    var response = await plotHttp.post(
      "/openapi/plot/myland/asLandPatrol/getOne",
      data: data,
    );
    return FieldPatrolDetailModel.fromJson(response.data);
  }
  //更新
  static Future<FieldPatrolDetailModel> updateFarmer(dynamic data) async {
    var response = await plotHttp.post(
      "/openapi/plot/myland/asLandPatrol/update",
      data: data,
    );
    return FieldPatrolDetailModel.fromJson(response.data);
  }
  //删除
  static Future<CommonDataModel> deleteById(dynamic data) async {
    var response = await plotHttp.post(
      "/openapi/plot/myland/asLandPatrol/deleteById",
      data: data,
    );
    return CommonDataModel.fromJson(response.data);
  }
}
