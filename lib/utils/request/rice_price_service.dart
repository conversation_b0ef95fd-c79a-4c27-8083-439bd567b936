import 'dart:convert';

import 'package:bdh_smart_agric_app/model/approve_query_result_model.dart';
import 'package:bdh_smart_agric_app/model/org_list_model.dart';
import 'package:bdh_smart_agric_app/model/person_price_result_model.dart';
import 'package:bdh_smart_agric_app/model/price_detail_model.dart';
import 'package:bdh_smart_agric_app/model/price_publish_model.dart';
import 'package:bdh_smart_agric_app/model/publish_orglist_model.dart';
import 'package:bdh_smart_agric_app/model/qualification_result_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/model/rice_user_home_result_model.dart';
import 'package:bdh_smart_agric_app/model/transaction_page_result_model.dart';
import 'package:bdh_smart_agric_app/model/transaction_result_model.dart';
import 'package:bdh_smart_agric_app/model/transaction_single_result_model.dart';
import 'package:bdh_smart_agric_app/pages/user/works_star/works_stars_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';

class PriceResponsitory {
  //----------------------粮价模块接口------------------------
  //获取发布单
  static Future<PricePublishResult> getRicePricePublishList(
      dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/transaction/stat/list",
      data: data,
    );
    return PricePublishResult.fromJson(response.data);
  }

  //获取农场关联收粮点
  static Future<PublishOrgListResult> getOrgCompanyList(String position) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/app/org/queryByPositionWithinDistance",
      // queryParameters: {"position": position}
    );
    return PublishOrgListResult.fromJson(response.data);
  }

  //获取挂价单
  static Future<PersonPriceResult> getRicePriceSheetList(
      Map<String, dynamic> params) async {
    jsonEncode(params);
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/price/sheet/getByPage",
      data: params,
    );
    return PersonPriceResult.fromJson(response.data);
  }

  //挂价单管理
  static Future<PersonPriceResult> getMyManageSheetList(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/price/manage/sheetList",
      queryParameters: params,
    );
    return PersonPriceResult.fromJson(response.data);
  }
  //我的发布

  static Future<PersonPriceResult> getMyRicePriceSheetList(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/price/user/sheetList",
      queryParameters: params,
    );
    return PersonPriceResult.fromJson(response.data);
  }

  //新增收粮单
  static Future<RequestNoData> addBuyRicePriceSheet(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/price/sheet/buy/add",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //新增卖粮单
  static Future<RequestNoData> addSaleRicePriceSheet(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/price/sheet/sell/add",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //粮价首页<old>
  static Future<RiceUserHomeResult> riceUserHome(
      Map<String, dynamic> params) async {
    var response =
        await microHttp.post("/agric-app-gathering-api/user/home/<USER>",
            // "/agric-app-gathering-api/user/home/<USER>",
            queryParameters: params);
    return RiceUserHomeResult.fromJson(response.data);
  }

  //粮价首页<new>
  static Future<RiceUserHomeResult> riceUserHomev1(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/user/home/<USER>/riceHome",
        queryParameters: params);
    return RiceUserHomeResult.fromJson(response.data);
  }

  //user首页
  static Future<RiceUserHomeResult> userHome(
      Map<String, dynamic> params) async {
    var response = await microHttp.post("/agric-app-gathering-api/user/home",
        // "/agric-app-gathering-api/user/home/<USER>",
        queryParameters: params);
    return RiceUserHomeResult.fromJson(response.data);
  }

  static Future<WorksStarsModel> userMyLikes(
      Map<String, dynamic> params) async {
    // var response = await microHttp.post(
    //     "/agric-app-gathering-api/user/home/<USER>",
    //     queryParameters: params);
    var response = await microHttp
        .post("/agric-app-gathering-api/user/home/<USER>", data: params);
    return WorksStarsModel.fromJson(response.data);
  }

  //获取粮价收藏列表
  static Future<PersonPriceResult> getMyCollect(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/user/home/<USER>",
      queryParameters: params,
    );
    return PersonPriceResult.fromJson(response.data);
  }

  //新增粮价收藏
  static Future<RequestNoData> addCollect(Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/collect/collectOperation",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //获取挂价单详情
  static Future<PriceDetailModel> getPriceDetail(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/price/sheet/getById",
      queryParameters: params,
    );
    return PriceDetailModel.fromJson(response.data);
  }

  //挂价单编辑
  static Future<RequestNoData> ricePriceUpdate(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/price/sheet/update",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //删除收藏挂价单
  static Future<RequestNoData> deleteCollectPrice(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/collect/delete/batch",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //收粮资质认证
  static Future<RequestNoData> addCertification(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/certification/approve/add",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //挂价单上下架
  static Future<RequestNoData> updatePriceStats(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/price/sheet/updateStats",
      queryParameters: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //新增合作交流
  static Future<RequestNoData> addCooperation(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/user/cooperation/add",
      data: params,
    );
    return RequestNoData.fromJson(response.data);
  }

  //收粮资质查询
  static Future<ApproveQueryResult> approveQuery(orgCode) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/rice/certification/approve/queryByOrgCode",
        queryParameters: {"orgCode": orgCode});
    return ApproveQueryResult.fromJson(response.data);
  }

  //更新收粮资质
  static Future<RequestNoData> updateApprove(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/rice/certification/approve/update",
        data: params);
    return RequestNoData.fromJson(response.data);
  }

  //查询成交记录
  static Future<TransactionsResult> findTransactionNotice(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/rice/transaction/find",
        queryParameters: params);
    return TransactionsResult.fromJson(response.data);
  }

  //查询成交(分页)
  static Future<TransactionPageResult> findTransactionByPage(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/rice/transaction/manage/getByPage",
        queryParameters: params);
    return TransactionPageResult.fromJson(response.data);
  }

  //查询我的成交(分页)
  static Future<TransactionPageResult> findMyTransactionByPage(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/rice/transaction/user/getByPage",
        queryParameters: params);
    return TransactionPageResult.fromJson(response.data);
  }

  //新增成交上报
  static Future<RequestNoData> addTransactionNotice(
      Map<String, dynamic> params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/rice/transaction/add", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //删除成交记录
  static Future<RequestNoData> deleteTransaction(id) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/rice/transaction/delete/$id",
    );
    return RequestNoData.fromJson(response.data);
  }

  //查询单个成交记录
  static Future<TransactionSingleResult> queryTransactionById(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/rice/transaction/findById",
        queryParameters: params);
    return TransactionSingleResult.fromJson(response.data);
  }

  //查询单个成交记录
  static Future<TransactionSingleResult> updateTransactionById(
      Map<String, dynamic> params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/rice/transaction/update", data: params);
    return TransactionSingleResult.fromJson(response.data);
  }
  //获取卖粮用户所属机构（数组）

  static Future<OrgListResult> getSaleOrg(Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/rice/price/sell/userOrgInfo",
        data: params);
    return OrgListResult.fromJson(response.data);
  }
  //获取收粮用户所属组织机构 (数组)

  static Future<OrgListResult> getBuyOrg(Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/rice/price/buy/userOrgInfo",
        data: params);
    return OrgListResult.fromJson(response.data);
  }

//我的资质审核列表
  static Future<QualificationListResult> getQualficationList(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/rice/certification/approve/staffApproveList",
        data: params);
    return QualificationListResult.fromJson(response.data);
  }

  //删除挂价单
  static Future<RequestNoData> deletePrice(Map<String, dynamic> params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/rice/price/deleteBatch", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //实名认证 add ,
  static Future<RequestNoData> realNameCertification(
      Map<String, dynamic> params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/user/authentication/add", data: params);
    return RequestNoData.fromJson(response.data);
  }

  //实名认证 update ,
  static Future<RequestNoData> realNameUpDateCertification(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/user/authentication/update",
        data: params);
    return RequestNoData.fromJson(response.data);
  }

  //实名认证 撤回
  static Future<RequestNoData> withdrawCertificaiton(
      Map<String, dynamic> params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/user/authentication/delete",
        queryParameters: params);
    return RequestNoData.fromJson(response.data);
  }

  // 实名认证 info
  static Future getUserAuthenticationInfo(Map<String, dynamic> params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/user/authentication/find");
    return response.data;
  }
}
