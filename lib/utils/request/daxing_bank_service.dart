import 'package:bdh_smart_agric_app/model/bank_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

import 'bank_service.dart';

//大兴银行卡绑定
class DaxingBankServiceImpl extends BankServiceImpl {
  //我的银行卡列表
  //https://lc.bdhic.com/bankcard/landcontractbankcard/queryBank
  @override
  Future<RequestResult<BankList, RequestException>> queryBank(
      {CancelToken? cancelToken}) async {
    var result = await daHingHttp.postForResult("/bdhapp/bankcard/queryBank",
        cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(BankList.fromJson(r));
    });
  }

  //添加银行卡
  //https://lc.bdhic.com/bankcard/landcontractbankcard/addBankcard
  //bankAccount: "***************" 银行卡号
  //bankName: "1" //银行 id
  //bankcardTypeNo: 0 //银行卡类型
  //lineNumber: "************"  //联行号
  //network: "武汉" //开户网点
  //bankcardPhotoPath 存折图片路径
  @override
  Future<RequestResult<RequestNoData, RequestException>> addBankcard(
      {required String bankAccount,
      required String bankName,
      required int bankcardTypeNo,
      String? lineNumber,
      String? bankcardPhotoPath,
      String? network,
      String? bankAccountId,
      CancelToken? cancelToken}) async {
    var data = {
      "bankAccount": bankAccount,
      "bankName": bankName,
      "bankcardTypeNo": bankcardTypeNo
    };
    if (lineNumber != null) {
      data["lineNumber"] = lineNumber;
    }
    if (bankcardPhotoPath != null) {
      data["bankcardPhotoPath"] = bankcardPhotoPath;
    }
    if (network != null) {
      data["network"] = network;
    }
    if (bankAccountId != null) {
      data["bankAccountId"] = bankAccountId;
    }
    var requestUrl = "/bdhapp/bankcard/addBankcard";
    if (bankAccountId != null) {
      requestUrl = "/bdhapp/bankcard/updateBank";
    }

    Log.d("addBank request data is $data");
    var result = await daHingHttp.postForResult(requestUrl,
        data: data, cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //修改银行卡
  //https://lc.bdhic.com/bankcard/landcontractbankcard/updateBank
  //bankAccount: "***************" 银行卡号
  //bankName: "1" //银行 id
  //bankcardTypeNo: 0 //银行卡类型
  //lineNumber: "************"  //联行号
  //network: "武汉" //开户网点
  //bankcardPhotoPath 存折图片路径

  @override
  Future<RequestResult<RequestNoData, RequestException>> updateBank(
      {required String bankAccountId,
      required String bankName,
      required int bankcardTypeNo,
      required String bankAccount,
      String? bankcardPhotoPath,
      String? lineNumber,
      String? network,
      CancelToken? cancelToken}) async {
    var data = {
      "bankAccountId": bankAccountId,
      "bankAccount": bankAccount,
      "bankName": bankName,
      "bankcardTypeNo": bankcardTypeNo
    };
    if (lineNumber != null) {
      data["lineNumber"] = lineNumber;
    }
    if (bankcardPhotoPath != null) {
      data["bankcardPhotoPath"] = bankcardPhotoPath;
    }
    if (network != null) {
      data["network"] = network;
    }
    var result = await daHingHttp.postForResult("/bdhapp/bankcard/updateBank",
        data: data, cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //删除银行卡
  //https://lc.bdhic.com/bankcard/landcontractbankcard/removeBankcard/$bankAccountId
  @override
  Future<RequestResult<RequestNoData, RequestException>> removeBankcard(
      {required String bankAccountId, CancelToken? cancelToken}) async {
    var result = await daHingHttp.postForResult(
        "/bdhapp/bankcard/removeBankcard/$bankAccountId",
        cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //设置默认银行卡？
  //https://lc.bdhic.com/bankcard/landcontractbankcard/setDefault/${bankAccountId}
  @override
  Future<RequestResult<RequestNoData, RequestException>> setDefault(
      {required String bankAccountId, CancelToken? cancelToken}) async {
    var result = await daHingHttp.postForResult(
        "/bdhapp/bankcard/setDefault/$bankAccountId",
        cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }

  //https://lc.bdhic.com/bankcard/landcontractbankcard/queryBankNanme
  //查找归属银行
  //bankAccount: "***************"
  @override
  Future<RequestResult<RequestNoData, RequestException>> queryBankName(
      {required String bankAccount, CancelToken? cancelToken}) async {
    var result = await daHingHttp.postForResult(
        "/bankcard/landcontractbankcard/queryBankNanme",
        data: {"bankAccount": bankAccount},
        cancelToken: cancelToken);

    return result.transformSuccess((r) {
      return ResultSuccess(RequestNoData.fromJson(r));
    });
  }
}

@deprecated
class DaxingBankService {
  //我的银行卡列表
  //http://localhost:9999/bdhapp/bankcard/queryBank
  static Future<BankList> queryBank({CancelToken? cancelToken}) async {
    var response = await daHingHttp.post("/bdhapp/bankcard/queryBank",
        cancelToken: cancelToken);
    return BankList.fromJson(response.data);
  }

  //添加银行卡
  //http://localhost:9999/bdhapp/bankcard/addBankcard
  //bankAccount: "***************" 银行卡号
  //bankName: "1" //银行 id
  //bankcardTypeNo: 0 //银行卡类型
  //lineNumber: "************"  //联行号
  //network: "武汉" //开户网点
  //bankcardPhotoPath 存折图片路径
  //orderNumber
  static Future<RequestNoData> addBankcard(
      {required String bankAccount,
      required String bankName,
      required int bankcardTypeNo,
      String? lineNumber,
      String? bankcardPhotoPath,
      String? network,
      String? bankAccountId,
      String? orderNumber,
      CancelToken? cancelToken}) async {
    var data = {
      "bankAccount": bankAccount,
      "bankName": bankName,
      "bankcardTypeNo": bankcardTypeNo
    };
    if (lineNumber != null) {
      data["lineNumber"] = lineNumber;
    }
    if (bankcardPhotoPath != null) {
      data["bankcardPhotoPath"] = bankcardPhotoPath;
    }
    if (network != null) {
      data["network"] = network;
    }
    if (bankAccountId != null) {
      data["bankAccountId"] = bankAccountId;
    }
    if (orderNumber != null) {
      data["orderNumber"] = orderNumber;
    }
    var requestUrl = "/bdhapp/bankcard/addBankcard";
    if (bankAccountId != null) {
      requestUrl = "/bdhapp/bankcard/updateBank";
    }
    var response =
        await daHingHttp.post(requestUrl, data: data, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  //修改银行卡
  //https://lc.bdhic.com/bankcard/landcontractbankcard/updateBank
  //bankAccount: "***************" 银行卡号
  //bankName: "1" //银行 id
  //bankcardTypeNo: 0 //银行卡类型
  //lineNumber: "************"  //联行号
  //network: "武汉" //开户网点
  //bankcardPhotoPath 存折图片路径
  //orderNumber,
  static Future<RequestNoData> updateBank(
      {required String bankAccountId,
      required String bankName,
      required int bankcardTypeNo,
      required String bankAccount,
      String? bankcardPhotoPath,
      String? lineNumber,
      String? network,
      String? orderNumber,
      CancelToken? cancelToken}) async {
    var data = {
      "bankAccountId": bankAccountId,
      "bankAccount": bankAccount,
      "bankName": bankName,
      "bankcardTypeNo": bankcardTypeNo
    };
    if (lineNumber != null) {
      data["lineNumber"] = lineNumber;
    }
    if (bankcardPhotoPath != null) {
      data["bankcardPhotoPath"] = bankcardPhotoPath;
    }
    if (network != null) {
      data["network"] = network;
    }
    if (orderNumber != null) {
      data["orderNumber"] = orderNumber;
    }
    var response = await daHingHttp.post("/bdhapp/bankcard/updateBank",
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  //删除银行卡
  //http://localhost:9999/bdhapp/bankcard/removeBankcard/${bankAccountId}
  static Future<RequestNoData> removeBankcard(
      {required String bankAccountId, CancelToken? cancelToken}) async {
    var response = await daHingHttp.post(
        "/bdhapp/bankcard/removeBankcard/$bankAccountId",
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  //设置默认银行卡？
  //http://localhost:9999/bdhapp/bankcard/setDefault/${bankAccountId}
  static Future<RequestNoData> setDefault(
      {required String bankAccountId, CancelToken? cancelToken}) async {
    var response = await daHingHttp.post(
        "/bdhapp/bankcard/setDefault/$bankAccountId",
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }
}
