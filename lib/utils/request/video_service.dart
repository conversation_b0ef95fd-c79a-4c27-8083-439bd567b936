import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/video_detail_model.dart';
import 'package:bdh_smart_agric_app/model/video_page_result_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

class VideoResponsitory {
//----------------------视频模块接口------------------------

  //获取视频分页列表
  static Future<VideoPageResult> getVideoByPage(dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/app/video/getByPage",
      data: data,
    );
    return VideoPageResult.fromJson(response.data);
  }

  //视频举报
  static Future videoApproveAdd(dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/app/video/report/add",
      data: data,
    );
    return response.data;
  }

  //新增视频
  static Future addNewVideo(dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/app/video/insert",
      data: data,
    );
    return response.data;
  }

  //视频点赞
  static Future videoLike(dynamic data) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/app/interact/likesOperation",
      data: data,
    );
    return response.data;
  }

  //视频转发
  static Future videoForward(dynamic data, {CancelToken? cancelToken}) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/app/interact/forward",
        data: data,
        cancelToken: cancelToken);
    return response.data;
  }

  //视频详情
  static Future<VideoDetailResult> videoDetail(int videoId) async {
    var response = await microHttp.post(
      "/agric-app-gathering-api/app/video/detail/$videoId",
    );
    return VideoDetailResult.fromJson(response.data);
  }

  //视频用户关注
  static Future videoUserFollow(params) async {
    var response = await microHttp
        .post("/agric-app-gathering-api/app/interact/follows", data: params);
    return response.data;
  }

  //视频浏览记录
  static Future videoScanHistory(params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/app/video/watched/insert",
        data: params);
    return response.data;
  }

  //获取视频二级分类
  static Future<DictList> videoSecondCategory(params) async {
    var response = await microHttp.post(
        "/agric-app-gathering-api/app/video/getCategoryList",
        data: params);
    return DictList.fromJson(response.data);
  }
}
