import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';

class MesResponsitory {
  //获取用户身份
  static Future getLoginStaffinfo() async {
    var response = await mesHttp.post("/system/source/getLoginStaffInfo");
    return response.data;
  }

  //获取系统通知列表
  static Future queryByPageAppNoticeList(params) async {
    var response = await mesHttp.post("/publish/app/queryByPageAppNoticeList",
        data: params);
    return response.data;
  }

  //获取系统消息列表
  static Future queryByPageAppSystemList(params) async {
    var response = await mesHttp.post("/publish/app/queryByPageAppSystemList",
        data: params);
    return response.data;
  }

  //获取系统'通知'详情
  static Future getMesDetail(mesId) async {
    var response = await mesHttp.post(
      "/publish/app/detail/$mesId",
    );
    return response.data;
  }

  //获取系统'消息'详情
  static Future systemInfo(mesId) async {
    var response = await mesHttp.post(
      "/publish/app/systemInfo/$mesId",
    );
    return response.data;
  }

  //获取用户所属作业站
  static Future getUserOrgByLevel() async {
    var response = await mesHttp.post(
      "/org/amporg/getUserOrgByLevel",
    );
    return response.data;
  }

  //新增发布接口
  static Future insertAndPublish(params) async {
    var response =
        await mesHttp.post("/publish/app/insertAndPublish", data: params);
    return response.data;
  }

  //查询消息已读未读人数
  static Future getIsReadPersonList(noticeId) async {
    var response = await mesHttp.post(
        "/publish/app/noticeInfo/getIsReadPersonList",
        queryParameters: {"noticeId": noticeId});
    return response.data;
  }

  //更改消息已读未读状态 readStatus
  static Future changeNoticeReadStatus(params) async {
    var response = await mesHttp.post(
        "/publish/app/noticeInfo/changeNoticeReadStatus",
        queryParameters: params);
    // var response = await mesHttp.post(
    //     "/publish/app/noticeInfo/changeNoticeReadStatus",
    //     queryParameters: {"noticeId": noticeId});
    return response.data;
  }

  //消息全部已读
  static Future readAll(noticeId) async {
    var response = await mesHttp.post("/publish/app/readAll");
    return response.data;
  }

  //消息撤回
  static Future revokeMes(noticeId) async {
    var response =
        await mesHttp.post("/publish/newReleaseNotice/revokeById/$noticeId");
    return response.data;
  }

  //查看已发布消息详情
  static Future publishMesInfoDetail(msgId) async {
    var response = await mesHttp.post("/publish/newReleaseNotice/info/$msgId");
    return response.data;
  }

  //已经发布消息列表
  static Future releaseNotifyListInfo(params) async {
    var response = await mesHttp.post('/publish/newReleaseNotice/queryByPage',
        data: params);
    return response.data;
  }
}
