import 'package:bdh_smart_agric_app/model/abandon_model.dart';
import 'package:bdh_smart_agric_app/model/contract_agrement_result_model.dart';
import 'package:bdh_smart_agric_app/model/contract_apply_model.dart';
import 'package:bdh_smart_agric_app/model/contract_getfdd_model.dart';
import 'package:bdh_smart_agric_app/model/contract_sign_model.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';

import 'package:bdh_smart_agric_app/model/other_sign_model.dart';

import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/model/sign_info_page_mode.dart';

import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

//域内土地承包
class IntradomainSignService {
  //字典接口
  static Future<DictList> getDicByKey(String key) async {
    var response = await ssoHttp.post("/sso/dict/list/$key");
    return DictList.fromJson(response.data);
  }

  //组织机构
  static Future<OrgTreeResult> getOrgData() async {
    var response = await baseHttp.post("/org/amporg/queryOrgTreeByUserOrg");
    return OrgTreeResult.fromJson(response.data);
  }

  //基本信息查询
  static Future<LandBaseInfoResult> getResidentInfo(
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
      "/farmer/landcontractfarmers/getResidentInfo",
      cancelToken: cancelToken,
    );
    return LandBaseInfoResult.fromJson(response.data);
  }

  //承包权申请
  //获取查询列表
  static Future<LandContractApplyListResult> getLandInfo() async {
    var response = await baseHttp.post("/landapp/input/landInfo", data: {});
    return LandContractApplyListResult.fromJson(response.data);
  }

  //获取合同链接
  static Future<RequestNoData> viewPromise(dynamic map) async {
    var response =
        await baseHttp.post("/landapp/promise/viewPromise", data: map);
    return RequestNoData.fromJson(response.data);
  }

  //获取协议
  static Future<ContractAgreementResult> getAgreement(
      num type, String org) async {
    var response = await baseHttp
        .post("/policy/landcontractpolicy/getPolicyInfo/$type/$org", data: {});
    return ContractAgreementResult.fromJson(response.data);
  }

  //根据 landAppId 提交申请书
  static Future<ContractAgreementResult> submitByLandAppId(
      num landAppId) async {
    var response = await baseHttp
        .post("/landapp/input/submitByLandAppId/$landAppId", data: {});
    return ContractAgreementResult.fromJson(response.data);
  }

  //获取是否开启签署协议配置
  static Future<SignInfoPageResult> getPromiseSignInfo(dynamic map) async {
    var response =
        await baseHttp.post("/landapp/promise/getPromiseSignInfo", data: map);
    return SignInfoPageResult.fromJson(response.data);
  }

  //新增(更新)土地承包申请书数据
  static Future<RequestNoData> insertLandApp(dynamic map) async {
    var response =
        await baseHttp.post("/landapp/input/insertLandApp", data: map);
    return RequestNoData.fromJson(response.data);
  }

  //获取发大大协议地址
  static Future<ContractGetfddResult> farmerSartToSignContract(
      dynamic map) async {
    var response = await baseHttp
        .post("/landapp/promise/farmerSartToSignContract", data: map);
    return ContractGetfddResult.fromJson(response.data);
  }

  //放弃
  //获取查询列表
  static Future<AbandonListResult> queryAbandonList() async {
    var response = await baseHttp
        .post("/contractrightabandon/import/queryAppList", data: {});
    return AbandonListResult.fromJson(response.data);
  }

  //获取放弃合同链接
  static Future<RequestNoData> manualAbandonSignPage(dynamic map) async {
    var response = await baseHttp
        .post("/contractrightabandon/import/manualSignPage", data: map);
    return RequestNoData.fromJson(response.data);
  }

  //合同
  //获取合同数据
  static Future<ContractSignListResult> queryContractList(
      Map<String, String?> map) async {
    var response =
        await baseHttp.post("/contract/import/queryContractList", data: map);
    return ContractSignListResult.fromJson(response.data);
  }

  // 获取签订状态
  static Future<RequestNoData> appLocationOnOffGlobal(
      Map<String, String?> map) async {
    var response = await baseHttp.post("/ration/promise/appLocationOnOffGlobal",
        data: map);
    return RequestNoData.fromJson(response.data);
  }

// 获取合同链接
  static Future<RequestNoData> manualSignPage(Map<String, String?> map) async {
    var response =
        await baseHttp.post("/contract/import/manualSignPage", data: map);
    return RequestNoData.fromJson(response.data);
  }

  // 获取社政合同链接
  static Future<RequestNoData> generateManualSignPage(
      Map<String, String?> map) async {
    var response = await baseHttp
        .post("/politics/contractchargeplan/manualSignPage", data: map);
    return RequestNoData.fromJson(response.data);
  }

  // 获取解除合同链接
  static Future<RequestNoData> manualSignPageForWxApplet(
      Map<String, String?> map) async {
    var response =
        await baseHttp.post("/contractjc/import/manualSignPage", data: map);
    return RequestNoData.fromJson(response.data);
  }

  // 获取解除合同链接
  static Future<RequestNoData> manualSignPageContractDissolution(
      Map<String, String?> map) async {
    var response = await baseHttp
        .post("/politics/contractDissolution/manualSignPage", data: map);
    return RequestNoData.fromJson(response.data);
  }

// 其他合同
  //其他合同信息查询
  static Future<OtherSignListResult> getOtherSignInfo(
      Map<String, String?> map) async {
    var response = await baseHttp
        .post("/otheragreement/import/queryContractList", data: map);
    return OtherSignListResult.fromJson(response.data);
  }

  //查看其他合同合同链接
  static Future<RequestNoData> scanContract(params) async {
    var response = await authHttp.post(
        "/authentication/thirdPartyAuthentication/viewContract",
        data: params);
    return RequestNoData.fromJson(response.data);
  }

  //获取其他合同合同链接
  static Future<RequestNoData> otheragreeSignPage(dynamic params) async {
    var response = await baseHttp.post("/otheragreement/import/manualSignPage",
        data: params);
    return RequestNoData.fromJson(response.data);
  }

  //轮训其他合同 和 合同 和 放弃 的状态
  static Future<RequestNoData> contractAuthStatus(
      dynamic contractNo, accountId) async {
    var response = await authHttp.post(
        "/authentication/thirdPartyAuthentication/neoSignerStatus/$contractNo/$accountId");
    return RequestNoData.fromJson(response.data);
  }
}
