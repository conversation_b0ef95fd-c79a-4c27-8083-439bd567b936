import 'package:bdh_smart_agric_app/model/subsidyDetailType.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

import '../../model/dict_list_model.dart';
import '../../model/request_no_data.dart';

class SubsidyService {
  ///
  static Future<RequestNoData> mySubsidyPaymentDetail(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/subsidy/subsidyPaymentDetail/app/mySubsidyPaymentDetail",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  // 补贴详情
  static Future<RequestNoData> mySubsidyPaymentDetailInfo(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/subsidy/subsidyPaymentDetail/app/mySubsidyPaymentDetailInfo",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  static Future<RequestNoData> getMySubsidyList(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/subsidy/apply/app/queryByPage",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 补贴公示
  static Future<RequestNoData> queryAnnounceList(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/subsidy/subsidyAnnounceConfig/app/queryAnnounceList",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 公示反馈
  static Future<RequestNoData> feedback(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/subsidy/subsidyAnnounceConfig/app/feedback",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 补贴公示详情
  ///
  /// 粮改饲 url: "/subsidy/subsidyAnnounceConfig/app/queryAgmachinePaymentDetailList"
  ///
  static Future<RequestNoData> queryAnnounceDetailList(params,
      {CancelToken? cancelToken,
      String url =
          '/subsidy/subsidyAnnounceConfig/app/queryAgmachinePaymentDetailList'}) async {
    var response =
        await baseHttp.post(url, data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 粮改饲补贴详情
  static Future<RequestNoData> queryPublicityDetailList(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/subsidy/subsidyAnnounceConfig/app/queryPaymentDetailList",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 补贴详情点击查看  subsidyClassify == 1
  static Future<SubsidyPaymentDetailType> queryPaymentDetail(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/subsidy/subsidyAnnounceConfig/app/queryPaymentDetail",
        data: params,
        cancelToken: cancelToken);

    return SubsidyPaymentDetailType.fromJson(response.data);
  }

  /// 补贴详情点击查看  subsidyClassify == 1   subsidyAnnounceType ==2
  static Future<SubsidyPaymentDetailType> queryPaymentApplyDetail(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/subsidy/subsidyAnnounceConfig/app/queryPaymentApplyDetail",
        data: params,
        cancelToken: cancelToken);

    return SubsidyPaymentDetailType.fromJson(response.data);
  }

  /// 补贴明细
  static Future<RequestNoData> querySubsidyDetail(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/subsidy/apply/app/details",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 补贴明细-地块详情
  static Future<RequestNoData> querySubsidyLandDetail(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/subsidy/apply/app/landdetail",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 获取已上传的文件
  static Future<RequestNoData> querySubsidyUploadedFile(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/subsidy/apply/app/queryFile",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 删除文件
  static Future<RequestNoData> toSubsidyDelfile(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/subsidy/apply/app/delfile",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 文件上传
  static Future<RequestNoData> toUploadFileData(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/file/upload",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 保存申请协议附件
  static Future<RequestNoData> toSaveSubsidyAttachments(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/subsidy/apply/app/file/insert",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 申请补贴
  static Future<RequestNoData> toApplySubsidy(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/subsidy/apply/manualSignPage",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 查看申请书
  static Future<RequestNoData> toApplyContract(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post("/subsidy/apply/viewContract",
        data: params, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  static Future<RequestNoData> queryColumnApp(params,
      {CancelToken? cancelToken}) async {
    var response = await mesHttp.post("/publish/app/queryColumnApp",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  static Future<RequestNoData> queryColumn2App(params,
      {CancelToken? cancelToken}) async {
    var response = await mesHttp.post("/publish/app/queryColumn2App",
        data: params, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  static Future<RequestNoData> getAppNewsByPage(params,
      {CancelToken? cancelToken}) async {
    var response = await mesHttp.post("/publish/app/queryAppByPage",
        data: params, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  static Future<RequestNoData> getAppNewsDetail(String releaseInfoId,
      {CancelToken? cancelToken}) async {
    var response = await mesHttp.post("/publish/app/infoApp/$releaseInfoId",
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  //字典接口
  static Future<DictList> getDicByKey(String key) async {
    var response = await ssoHttp.post("/sso/dict/list/$key");
    return DictList.fromJson(response.data);
  }

  //控制版本号
  static Future<RequestNoData> getVersion(params,
      {CancelToken? cancelToken}) async {
    var response = await baseHttp.post(
        "/subsidy/subsidyAnnounceConfig/app/subsidy/version",
        data: params,
        cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }
}
