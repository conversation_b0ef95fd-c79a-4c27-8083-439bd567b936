import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

import '../../model/my_field_device_list_model.dart';
import '../../model/my_field_map_ai_model.dart';
import '../../model/my_field_soil_sexual_msg_info_model.dart';
import '../../model/myfield_monitorcatalogtree_model.dart';

class MyFieldPageService {
  //获取地块列表
  static Future getMyFieldPlotList(data) async {
    var response = await plotHttp
        .post("/openapi/plot/myland/queryPlotByStaffId", data: data);
    return response.data;
  }

  //获取种植作物列表
  static Future getMyFieldCropsList(data) async {
    var response = await plotHttp
        .post("/openapi/plot/myland/queryCropsByStaffId", data: data);
    return response.data;
  }

  //获取作业环节数据
  static Future getMyFieldPlantingPlan(data) async {
    var response = await plotHttp
        .post("/openapi/plot/myland/records/queryByPageLink", data: data);
    return response.data;
  }

  // //获取地块硬件设备
  // static Future getMyFieldDeviceList(data) async {
  //   var response = await highHttp.post(
  //       "/wetIotDeviceInfo/wetIotDeviceInfo/queryDevInfoByPlotNo",
  //       data: data);
  //   return response.data;
  // }
  //天气实时地块
  static Future<MyFieldDeviceListModel> getMyFieldDeviceList(
      dynamic data) async {
    var response = await highHttp.post(
      "/wetIotDeviceInfo/wetIotDeviceInfo/queryDevInfoByPlotNo",
      data: data,
    );
    return MyFieldDeviceListModel.fromJson(response.data);
  }

  //获取土壤设备详情
  static Future getMyFieldSoilInfo(data) async {
    var response = await highHttp
        .post("/wpSoilMsgInfo/wpSoilMsgInfo/querySoinDesc", data: data);
    return response.data;
  }

  //获取土壤设备统计
  static Future getMyFieldSoilStatisticsInfo(data) async {
    var response = await highHttp
        .post("/wpSoilMsgInfo/wpSoilMsgInfo/soilStatistics", data: data);
    return response.data;
  }

  //获取土壤养分统计
  static Future getMyFieldSoilNutrientInfo(data) async {
    var response = await highHttp.post(
        "/wpSoilNutrientMsgInfo/wpSoilNutrientMsgInfo/soilStatistics",
        data: data);
    return response.data;
  }

  //获取虫害设备详情
  static Future getMyFieldPestInfo(data) async {
    var response = await highHttp
        .post("/wpInsectMsgInfo/wpInsectMsgInfo/queryInsecLatest", data: data);
    return response.data;
  }

  //获取虫害统计照片
  static Future getMyFieldPestStatisByPhoto(data) async {
    var response = await highHttp
        .post("/wpInsectMsgInfo/wpInsectMsgInfo/pestStatisByPhoto", data: data);
    return response.data;
  }

  //获取虫害统计分析
  static Future getMyFieldPestStatisByAnalysis(data) async {
    var response = await highHttp.post(
        "/wpInsectMsgInfo/wpInsectMsgInfo/pestStatisByAnalysis",
        data: data);
    return response.data;
  }

  //获取虫害统计虫情初见
  static Future getMyFieldPestStatisInsectAnalysis(data) async {
    var response = await highHttp
        .post("/wpInsectMsgInfo/wpInsectMsgInfo/insectAnalysis", data: data);
    return response.data;
  }

  //获取虫害统计虫子类型
  static Future getMyFieldInsectType(data) async {
    var response = await highHttp.post("/wpInsectType/wpInsectType/queryByPage",
        data: data);
    return response.data;
  }

  //获取土壤养分设备详情
  static Future getMyFieldNutrientInfo(data) async {
    var response = await highHttp.post(
        "/wpSoilNutrientMsgInfo/wpSoilNutrientMsgInfo/querySoinDesc",
        data: data);
    return response.data;
  }

  //获取土壤养分生育期列表
  static Future getMyFieldsoilGrowList(data) async {
    var response = await highHttp.post(
        "/wpSoilNutrientMsgInfo/wpSoilNutrientMsgInfo/soilGrow",
        data: data);
    return response.data;
  }

  //获取孢子设备信息
  static Future getMyFieldSoilSporeInfo(data) async {
    var response = await highHttp
        .post("/wpSporeMsgInfo/wpSporeMsgInfo/querySporeDesc", data: data);
    return response.data;
  }

  //获取孢子统计信息
  static Future getMyFieldSoilSporeStatistics(data) async {
    var response = await highHttp
        .post("/wpSporeMsgInfo/wpSporeMsgInfo/querySporeHis", data: data);
    return response.data;
  }

  //获取性诱设备实时信息
  static Future<MyFieldSoilSexualMsgInfoModel> getMyFieldSoilSexualMsgInfo(
      data) async {
    var response = await highHttp
        .post("/wpSexualMsgInfo/wpSexualMsgInfo/querySexDesc", data: data);
    return MyFieldSoilSexualMsgInfoModel.fromJson(response.data);
  }

  //获取性诱设备统计数据
  static Future getMyFieldSoilSexualMsgStatistic(data) async {
    var response = await highHttp
        .post("/wpSexualMsgInfo/wpSexualMsgInfo/sexualStatistic", data: data);
    return response.data;
  }

  //获取农事提醒列表
  static Future getMyFieldSoilRemind(data) async {
    var response = await waterManageHttp
        .post("/wpArgiRemind/bindRel/queryAdviceByPlot", data: data);
    return response.data;
  }

  //地块画像数据
  static Future getMyFieldPlotInfo(data) async {
    var response = await plotHttp.post("/openapi/plot/myland/queryLandProfile",
        data: data);
    return response.data;
  }

  // 种植上传
  static Future myFieldUploadFile(data) async {
    var response = await plotHttp.post("/file/upload", data: data);
    return response.data;
  }

  // 投入品分类
  static Future myFieldInputClassification(data) async {
    var response = await plotHttp
        .post("/openapi/plot/myland/records/queryMeansType", data: data);
    return response.data;
  }

  // 施肥建议卡
  // static Future getMyFieldSoilFertAdviceCard(data) async {
  //   var response = await soilTestingHttp
  //       .post("/advice/soilFertAdviceCard/getCardByPlotNo", data: data);
  //   return response.data;
  // }
  static Future getMyFieldSoilFertAdviceCard(Map<String, dynamic> data) async {
    var response = await soilTestingHttp
        .post("/advice/soilFertAdviceCard/getCardByCrop", data: data);
    return response.data;
  }

  //获取字典
  static Future<DictList> getDicByKey(String key,
      {CancelToken? cancelToken}) async {
    var response =
        await ssoHttp.post("/sso/dict/list/$key", cancelToken: cancelToken);
    return DictList.fromJson(response.data);
  }

  //获取字典
  static Future getDictlist(String key, {CancelToken? cancelToken}) async {
    var response =
        await ssoHttp.post("/sso/dict/list/$key", cancelToken: cancelToken);
    return response.data;
  }

  static Future<MyfieldMonitorcatalogtreeModel> monitorCatalogTree(
      dynamic data) async {
    var response = await commonHttp.post(
      urlConfig.isDebug!
          ? "/geospatial-api/openapi/onemap/monitorCatalogTree"
          : "/bdh-portal/api/geospatial/openapi/onemap/monitorCatalogTree",
      data: data,
    ); // "/bdh-portal/api/geospatial/openapi/onemap/monitorCatalogTree"
    return MyfieldMonitorcatalogtreeModel.fromJson(response.data);
  }

  static Future nodeMonitorMapData(dynamic data) async {
    var response = await commonHttp.post(
      urlConfig.isDebug!
          ? "/geospatial-api/openapi/onemap/nodeMonitorMapData"
          : "/bdh-portal/api/geospatial/openapi/onemap/nodeMonitorMapData",
      data: data,
    ); //"/bdh-portal/api/geospatial/openapi/onemap/nodeMonitorMapData"
    return response.data;
  }

  static Future<MyFieldMapAiModel> byLabel(dynamic data) async {
    var response = await commonHttp.get(
      "/bdh-portal/api/thenorthdata/description/get/byLabel",
      queryParameters: data,
    );
    return MyFieldMapAiModel.fromJson(response.data);
  }

//施肥推荐
  static Future<Map<String, dynamic>> intfResPlotFertRecGeoJson(
      dynamic data) async {
    var response = await fieldHttp.post(
      "/bdh-portal/api/bdh-intelligent-fertilization-api/fertilizationManagement/intfResPlotFertRec/geoJson",
      data: data,
    );

    return response.data;
  }

  //追肥推荐
  static Future<Map<String, dynamic>> intfResPlotFertAddGeoJson(
      dynamic data) async {
    var response = await fieldHttp.post(
      "/bdh-portal/api/bdh-intelligent-fertilization-api/fertilizationManagement/intfResPlotFertAdd/geoJson",
      data: data,
    );

    return response.data;
  }

//旱田施肥推荐
  static Future<Map<String, dynamic>> recJsonByPlotNoDry(dynamic data) async {
    var response = await fieldHttp.post(
      "/bdh-portal/api/bdh-intelligent-fertilization-api/dry/geo/recJsonByPlotNo",
      data: data,
    );

    return response.data;
  }

  //旱田追肥推荐
  static Future<Map<String, dynamic>> addJsonByPlotNoDry(dynamic data) async {
    var response = await fieldHttp.post(
      "/bdh-portal/api/bdh-intelligent-fertilization-api/dry/geo/addJsonByPlotNo",
      data: data,
    );

    return response.data;
  }
}
