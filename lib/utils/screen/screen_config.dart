import 'package:flutter/widgets.dart';

import 'screen_tool.dart';

class ScreenConfig extends StatelessWidget {
  const ScreenConfig({
    required this.builder,
    this.designSize = ScreenTool.defaultSize,
    this.allowFontScaling = false,
    super.key,
  });

  final Widget Function() builder;

  /// 设计稿尺寸，单位px
  final Size designSize;

  /// 是否允许字体大小缩放
  final bool allowFontScaling;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (_, BoxConstraints constraints) {
        //TODO 部分 dialog 和toast也需要适配
        MediaQueryData data =
            MediaQuery.of(context).copyWith(textScaler: TextScaler.noScaling);

        if (constraints.maxWidth != 0) {
          ScreenTool().init(constraints,
              designSize: designSize,
              allowFontScaling: allowFontScaling,
              data: data);
        }
        return MediaQuery(data: data, child: builder());
      },
    );
  }
}
