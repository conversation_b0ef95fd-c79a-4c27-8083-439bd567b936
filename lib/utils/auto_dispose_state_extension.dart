import 'dart:async';

import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:oktoast/oktoast.dart';

import 'log.dart';
import 'provider/view_state_widget.dart';

enum LoadingStatus {
  init,
  loading,
  loadingMore,
  refreshing,
  success,
  error,
  cancel
}

mixin MixinDefaultWidget<T extends StatefulWidget> on State<T> {
  //加载中
  Widget widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget widgetEmpty() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            ImageHelper.wrapAssets("icon_nodata.svg"),
            width: 201.5.px,
            height: 100.px,
          ),
          SizedBox.square(
            dimension: 45.px,
          ),
          Text(
            "未找到任何记录",
            style: TextStyle(
                color: const Color.fromRGBO(44, 44, 52, 1), fontSize: 14.px),
          ),
        ],
      ),
    );
  }
}

mixin LoadMoreChangeNotifier<T> on AutoDisposeChangeNotifier {
  //加载状态
  LoadingStatus loadingStatus = LoadingStatus.init;

  bool get isLoading =>
      loadingStatus == LoadingStatus.loading ||
      loadingStatus == LoadingStatus.init;

  //加载更多时间
  DateTime loadMoreTime = DateTime.now();
  List<T> get items;
  //当前页
  int page = 1;
  //总数
  int total = 0;
  //分页，每页显示 10 条
  final row = 10;

  //是否需要加载更多
  bool get needLoadMore {
    //return true;
    return total > items.length;
  }

  //加载更多
  void loadMore() {
    var nowTime = DateTime.now();

    //加载更多防呆，1s之内只能有一次请求
    var diffSeconds = nowTime.difference(loadMoreTime).inSeconds;
    if (diffSeconds > 1) {
      loadMoreTime = nowTime;
      if (!needLoadMore) {
        Log.d("_total <= items.length so return");
        return;
      }
      if (loadingStatus == LoadingStatus.loading ||
          loadingStatus == LoadingStatus.loadingMore) {
        return;
      }
      page++;
      Log.d("loadMore  loadMore $diffSeconds");
      reload(loadingMore: true);
    } else {
      loadMoreTime = nowTime;
      Log.d("loadMore skip loadMore $diffSeconds");
    }
  }

  Future reloadFuture({
    required bool showLoading,
    required bool loadingMore,
    required bool refresh,
  });

  //下拉刷新
  Future refresh() {
    return reload(refresh: true);
  }

  //重新加载
  Future reload({
    bool showLoading = false, //不显示数据直接显示 loading
    bool loadingMore = false, //显示加载更多的提示
    bool refresh = false,
  }) {
    if (showLoading) {
      loadingStatus = LoadingStatus.loading;
      page = 1;
      notifyListeners();
    }
    if (loadingMore) {
      loadingStatus = LoadingStatus.loadingMore;
      notifyListeners();
    }

    if (refresh) {
      loadingStatus = LoadingStatus.refreshing;
      page = 1;
    }

    return reloadFuture.call(
        showLoading: showLoading, loadingMore: loadingMore, refresh: refresh);
  }
}

mixin SingleCheckChangeNotifier<T> on AutoDisposeChangeNotifier {
  int? itemCheckedIndex;

  void onItemCheck(int index, bool selected) {
    Log.d("onItemCheck $index $selected");
    if (itemCheckedIndex == index && !selected) {
      itemCheckedIndex = null;
    } else if (selected) {
      itemCheckedIndex = index;
    }
    notifyListeners();
  }

  bool isChecked(int index) {
    return itemCheckedIndex == index;
  }
}

mixin MultiCheckChangeNotifier<T> on AutoDisposeChangeNotifier {
  List<T> get multiCheckableItems;

  List<T> checkedItems = [];

  bool get checkedAll {
    if (multiCheckableItems.isEmpty) {
      return false;
    }
    return checkedItems.length == multiCheckableItems.length;
  }

  bool isChecked(int index) {
    var element = multiCheckableItems[index];
    return checkedItems.contains(element);
  }

  void checkChangeCallback() {}

  void onItemCheck(int index, bool checked) {
    var element = multiCheckableItems[index];
    if (checked == true && !checkedItems.contains(element)) {
      checkedItems.add(element);
    } else if (checked == false && checkedItems.contains(element)) {
      checkedItems.remove(element);
    }
    checkChangeCallback();
    notifyListeners();
  }

  //选择全部
  void checkAll() {
    checkedItems.clear();
    checkedItems.addAll(multiCheckableItems);
    checkChangeCallback();
    notifyListeners();
  }

  //取消选择全部
  void uncheckAll() {
    checkedItems.clear();
    checkChangeCallback();
    notifyListeners();
  }
}

abstract class AutoDisposeChangeNotifier with ChangeNotifier {
  BuildContext get context;
  final Set<VoidCallback> _disposeSet = <VoidCallback>{};

  void autoDispose(VoidCallback callback) {
    if (_disposeSet.contains(callback)) {
      return;
    }
    _disposeSet.add(callback);
  }

  TextEditingController createTextController({String? text}) {
    return useValueNotifier(TextEditingController(text: text));
  }

  TextEditingController useTextController(TextEditingController controller) {
    return useValueNotifier(controller);
  }

  FocusScopeNode createFocusScopeNode() {
    return useChangeNotifier(FocusScopeNode());
  }

  FocusNode createFocusNode() {
    return useChangeNotifier(FocusNode());
  }

  FocusScopeNode useFocusScopeNode(FocusScopeNode controller) {
    return useChangeNotifier(controller);
  }

  U useChangeNotifier<U extends ChangeNotifier>(U notifier) {
    autoDispose(notifier.dispose);
    return notifier;
  }

  U useValueNotifier<U extends ChangeNotifier>(U notifier) {
    autoDispose(notifier.dispose);
    return notifier;
  }

  CancelToken useCancelToken(CancelToken cancelToken) {
    autoDispose(cancelToken.cancel);
    return cancelToken;
  }

  CancelToken createCancelToken() {
    return useCancelToken(CancelToken());
  }

  bool disposed = false;
  @override
  @mustCallSuper
  void dispose() {
    disposed = true;
    for (var f in _disposeSet) {
      f();
    }
    _disposeSet.clear();

    super.dispose();
  }

  Future futureWithError(Future future, {VoidCallback? errorDo}) {
    return future.onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: errorDo);
    });
  }

  Null handleError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);

    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }
}

//state 在 dispose 的时候需要自动关闭一些控制器，避免内存溢出
mixin AutoDisposeStateMixin<T extends StatefulWidget> on State<T> {
  final Set<VoidCallback> _disposeSet = <VoidCallback>{};

  void autoDispose(VoidCallback callback) {
    if (_disposeSet.contains(callback)) {
      return;
    }
    _disposeSet.add(callback);
  }

  U useChangeNotifier<U extends ChangeNotifier>(U notifier) {
    autoDispose(notifier.dispose);
    return notifier;
  }

  U useValueNotifier<U extends ChangeNotifier>(U notifier) {
    autoDispose(notifier.dispose);
    return notifier;
  }

  U useGestureRecognizer<U extends GestureRecognizer>(U notifier) {
    autoDispose(notifier.dispose);
    return notifier;
  }

  ScrollController useScrollController(ScrollController controller) {
    return useChangeNotifier(controller);
  }

  AnimationController useAnimationController(AnimationController controller) {
    autoDispose(controller.dispose);
    return controller;
  }

  PageController usePageController(PageController controller) {
    return useChangeNotifier(controller);
  }

  TabController useTabController(TabController controller) {
    return useChangeNotifier(controller);
  }

  TextEditingController useTextController(TextEditingController controller) {
    return useValueNotifier(controller);
  }

  WidgetStatesController useWidgetStatesController(
      WidgetStatesController controller) {
    return useChangeNotifier(controller);
  }

  TransformationController useTransformationController(
      TransformationController controller) {
    return useChangeNotifier(controller);
  }

  SearchController useSearchController(SearchController controller) {
    return useChangeNotifier(controller);
  }

  FocusScopeNode useFocusScopeNode(FocusScopeNode controller) {
    return useChangeNotifier(controller);
  }

  FocusNode useFocusNode(FocusNode controller) {
    return useChangeNotifier(controller);
  }

  FixedExtentScrollController useFixedExtentScrollController(
      FixedExtentScrollController controller) {
    return useChangeNotifier(controller);
  }

  DraggableScrollableController useDraggableScrollableController(
      DraggableScrollableController controller) {
    return useChangeNotifier(controller);
  }

  TapGestureRecognizer useTapGestureRecognizer(
      TapGestureRecognizer tapGestureRecognizer) {
    return useGestureRecognizer(tapGestureRecognizer);
  }

  Timer useTimer(Timer timer) {
    autoDispose(timer.cancel);
    return timer;
  }

  Ticker useTicker(Ticker tinker) {
    autoDispose(tinker.dispose);
    return tinker;
  }

  CancelToken useCancelToken(CancelToken cancelToken) {
    autoDispose(cancelToken.cancel);
    return cancelToken;
  }

  CancelToken createCancelToken() {
    return useCancelToken(CancelToken());
  }

  Null handleError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);

    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  @override
  @mustCallSuper
  void setState(VoidCallback fn) {
    if (mounted) {
      super.setState(fn);
    }
  }

  bool disposed = false;

  @override
  @mustCallSuper
  void dispose() {
    for (var f in _disposeSet) {
      f();
    }
    _disposeSet.clear();
    disposed = true;
    super.dispose();
  }
}
