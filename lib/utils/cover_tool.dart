import 'package:intl/intl.dart';
import 'package:logger/logger.dart';

class FormatTool {
//格式化时间 时间戳转字符串hh:mm:ss
  static String formatDuration(int seconds) {
    var duration = Duration(seconds: seconds);
    var hh = duration.inHours.toString().padLeft(2, '0');
    var mm = (duration.inMinutes % 60).toString().padLeft(2, '0');
    var ss = (duration.inSeconds % 60).toString().padLeft(2, '0');
    if (duration.inHours == 0) {
      return "$mm:$ss";
    } else {
      return "$hh:$mm:$ss";
    }
  }

// 时间戳 格式化 输出
  static String timeFormat(
      {required int timestamp, String formate = 'yyyy-MM-dd HH:mm:ss'}) {
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    String timeString = DateFormat(formate).format(dateTime);
    // Logger().i('时间戳 格式化 输出 == ' + timeString);
    return timeString;
  }

//时间字符串 转 时间戳
  static int timeStrToInt({required String timeString}) {
    // String timeString = "2022-01-01 12:34:56";
    DateTime dateTime = DateTime.parse(timeString);
    int timestamp = dateTime.millisecondsSinceEpoch;
    return timestamp;
  }

//格式化显示 消息
  static String formateOutputTimesStamp(int timestamp) {
    String formateTimeStr = '';
    final now = DateTime.now();
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);

    // 1年以前> 去年及以前展示--X年X月X日
    if (now.year != date.year) {
      formateTimeStr = timeFormat(timestamp: timestamp, formate: 'yyyy-MM-dd');
    }

    // 当年
    else {
      //当月
      if (now.month == date.month) {
        //当日> 展示--时：分
        if (now.day == date.day) {
          formateTimeStr = timeFormat(timestamp: timestamp, formate: 'HH:mm');
        } else if (DateTime.now().difference(date).inDays <= 7) {
          //前7日内> 展示--星期X
          var weekdayIndex = date.weekday;
          switch (weekdayIndex) {
            case 1:
              formateTimeStr = '星期一';
              break;
            case 2:
              formateTimeStr = '星期二';
              break;
            case 3:
              formateTimeStr = '星期三';
              break;
            case 4:
              formateTimeStr = '星期四';
              break;
            case 5:
              formateTimeStr = '星期五';
              break;
            case 6:
              formateTimeStr = '星期六';
              break;
            case 7:
              formateTimeStr = '星期日';
              break;
            default:
              formateTimeStr = '';
              break;
          }
        } else {
          formateTimeStr = timeFormat(timestamp: timestamp, formate: 'MM-dd');
        }
      } else {
        formateTimeStr = timeFormat(timestamp: timestamp, formate: 'MM-dd');
      }
    }
    Logger().i('格式化时间输出:$formateTimeStr');
    return formateTimeStr;
  }

  //根据时间戳 判断是否是今天
  static bool isToday(int timestamp) {
    final now = DateTime.now();
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return now.year == date.year &&
        now.month == date.month &&
        now.day == date.day;
  }

  //根据时间戳 判断是否7天内
  static bool isWithinLastSevenDays(int timestamp) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(timestamp);
    bool isWithinSevenDays = DateTime.now().difference(date).inDays <= 7;
    return isWithinSevenDays;
  }

  //根据时间戳 判断是在当月
  static bool isInCurrentMonth(int timestamp) {
    final now = DateTime.now();
    final target = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return now.year == target.year && now.month == target.month;
  }

  //判断当前时间是否超过2025.1.31
  bool isBefore20250131() {
    final targetDate = DateTime(2025, 1, 31);
    final now = DateTime.now();
    return now.isBefore(targetDate);
    // return false;
  }

  //获取当前时间字符串
  static String getCurrentTimeString() {
    // final now = DateTime.now();
    // final formatter = DateFormat('yyyy-MM-dd HH:mm:ss');
    // final formatted = formatter.format(now);

    int timestamp = DateTime.now().millisecondsSinceEpoch;
    String currentTime = FormatTool.timeFormat(timestamp: timestamp);
    return currentTime;
  }
}
