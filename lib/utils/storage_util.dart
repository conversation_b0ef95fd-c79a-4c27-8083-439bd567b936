import 'dart:convert';

import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/model/role_model.dart';

class StorageUtil {
  static UserInfo? userInfo() {
    if (StorageManager.storage!.getItem(kUser) != null) {
      UserInfo? userInfo = UserInfo.fromJson(
          jsonDecode(StorageManager.storage!.getItem(kUser)!));
      return userInfo;
    } else {
      return null;
    }
  }

  static LandBaseInfo? landbaseInfo() {
    if (StorageManager.storage!.getItem(kLandBaseInfo) != null) {
      LandBaseInfo? landBaseInfo = LandBaseInfo.fromJson(
          jsonDecode(StorageManager.storage!.getItem(kLandBaseInfo)!));
      return landBaseInfo;
    } else {
      return null;
    }
  }

  static String? token() {
    UserInfo? userInfo =
        UserInfo.fromJson(jsonDecode(StorageManager.storage!.getItem(kUser)!));
    return userInfo.data?.token;
  }

  static String? orgCode() {
    UserInfo? userInfo =
        UserInfo.fromJson(jsonDecode(StorageManager.storage!.getItem(kUser)!));
    //  PluginInfo? pluginInfo = userInfo.data?.pluginInfo;
    // List<OrgInfo>? orgInfos = pluginInfo?.orgInfos;
    // OrgInfo? orgInfo = orgInfos?.first;
    return userInfo.data?.pluginInfo?.orgInfos?.first.orgCode;
  }

//角色code
  static String? roleCode() {
    UserInfo? userInfo =
        UserInfo.fromJson(jsonDecode(StorageManager.storage!.getItem(kUser)!));
    return userInfo.data?.roleCode;
  }

  //telephone
  static String? telephone() {
    UserInfo? userInfo =
        UserInfo.fromJson(jsonDecode(StorageManager.storage!.getItem(kUser)!));
    return userInfo.data?.telephone;
  }

  //loginName
  static String? loginName() {
    UserInfo? userInfo =
        UserInfo.fromJson(jsonDecode(StorageManager.storage!.getItem(kUser)!));
    return userInfo.data?.staffName;
  }

//用户角色列表: 拼接字符串
  static String getRoleCodeListStr() {
    final res = StorageManager.storage!.getItem(kUserRoleList)!;
    // Logger().i(res);
    final resObj = jsonDecode(res);
    List dataList = resObj['data'];
    List<RoleModel> tempModelList = [];
    String roleStr = '';
    for (int i = 0; i < dataList.length; i++) {
      RoleModel model = RoleModel.fromJson(dataList[i]);
      tempModelList.add(model);
      roleStr += (model.roleCode ?? '') + ',';
    }
    return roleStr;
  }

  //通过'用户角色' 判断是否需要实名认证 [非农户, 非管理员 需要实名认证]
  static bool isUserNeedCertification() {
    bool needCertification = true;
    final res = StorageManager.storage!.getItem(kUserRoleList)!;
    // Logger().i(res);
    final resObj = jsonDecode(res);
    List dataList = resObj['data'];
    List<RoleModel> tempModelList = [];
    String roleStr = '';
    for (int i = 0; i < dataList.length; i++) {
      RoleModel model = RoleModel.fromJson(dataList[i]);
      tempModelList.add(model);
      roleStr += (model.roleCode ?? '');
    }

    needCertification = (roleStr.contains('gathering-manager') ||
            roleStr.contains('gathering-farmer'))
        ? false
        : true;

    return needCertification;
  }

  static void clear() {
    return StorageManager.storage?.clear();
  }

  static void saveNewVersionState(bool haveNewVersion) {
    StorageManager.sharedPreferences!.setBool('haveNewVersion', haveNewVersion);
  }

  static bool readNewVersionState() {
    bool? newVersioinState =
        StorageManager.sharedPreferences!.getBool('haveNewVersion');
    return newVersioinState ?? false;
  }

  static void saveServiceItem(String itemStr) {
    StorageManager.sharedPreferences!.setString('KServiceItem', itemStr);
  }

  static void saveDeviceToken(String deviceToken) {
    StorageManager.sharedPreferences!.setString('kDeviceToken', deviceToken);
  }

  static String readDeviceToken() {
    String? deviceToken =
        StorageManager.sharedPreferences!.getString('kDeviceToken');
    return deviceToken ?? '';
  }

  static MenuConfigItem? readServiceItem() {
    String? itemStr =
        StorageManager.sharedPreferences!.getString('KServiceItem');
    if (itemStr != null) {
      MenuConfigItem configItem = MenuConfigItem.fromJson(jsonDecode(itemStr));
      return configItem;
    } else {
      return null;
    }
  }
}
