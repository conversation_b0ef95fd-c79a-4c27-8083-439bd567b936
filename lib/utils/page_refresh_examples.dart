import 'package:flutter/material.dart';
import 'package:bdh_smart_agric_app/utils/auto_refresh_mixin.dart';
import 'package:bdh_smart_agric_app/utils/page_visibility_mixin.dart';

/*
 * 页面刷新功能使用示例
 * 
 * Flutter 中实现类似小程序 onShow 的功能有多种方式：
 * 
 * 1. 使用 AutoRefreshMixin - 简单易用
 * 2. 使用 PageVisibilityMixin - 功能更完整
 * 3. 手动实现 - 最灵活
 */

// ================== 示例 1: 使用 AutoRefreshMixin ==================
class SimpleListPage extends StatefulWidget {
  const SimpleListPage({super.key});

  @override
  State<SimpleListPage> createState() => _SimpleListPageState();
}

class _SimpleListPageState extends State<SimpleListPage> 
    with WidgetsBindingObserver, AutoRefreshMixin<SimpleListPage> {
  
  List<String> _data = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData(); // 初始加载
  }

  /// 实现 AutoRefreshMixin 要求的方法
  @override
  void onRefreshData() {
    _loadData();
  }

  Future<void> _loadData() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟网络请求
      await Future.delayed(const Duration(seconds: 1));
      
      if (mounted) {
        setState(() {
          _data = ['数据1', '数据2', '数据3', '更新时间: ${DateTime.now()}'];
        });
      }
    } catch (e) {
      // 处理错误
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('自动刷新示例')),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : ListView.builder(
            itemCount: _data.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(_data[index]),
              );
            },
          ),
    );
  }
}

// ================== 示例 2: 使用 PageVisibilityMixin ==================
class AdvancedListPage extends StatefulWidget {
  const AdvancedListPage({super.key});

  @override
  State<AdvancedListPage> createState() => _AdvancedListPageState();
}

class _AdvancedListPageState extends State<AdvancedListPage> 
    with WidgetsBindingObserver, PageVisibilityMixin<AdvancedListPage> {
  
  List<String> _data = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData(); // 初始加载
  }

  /// 实现 PageVisibilityMixin 要求的方法
  @override
  void onPageShow() {
    print('页面显示了，刷新数据');
    _loadData();
  }

  @override
  void onPageHide() {
    print('页面隐藏了，可以做一些清理工作');
    // 比如取消正在进行的网络请求
  }

  Future<void> _loadData() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟网络请求
      await Future.delayed(const Duration(seconds: 1));
      
      if (mounted) {
        setState(() {
          _data = ['高级数据1', '高级数据2', '高级数据3', '更新时间: ${DateTime.now()}'];
        });
      }
    } catch (e) {
      // 处理错误
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('高级刷新示例')),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : ListView.builder(
            itemCount: _data.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(_data[index]),
              );
            },
          ),
    );
  }
}

// ================== 示例 3: 手动实现 ==================
class ManualRefreshPage extends StatefulWidget {
  const ManualRefreshPage({super.key});

  @override
  State<ManualRefreshPage> createState() => _ManualRefreshPageState();
}

class _ManualRefreshPageState extends State<ManualRefreshPage> with WidgetsBindingObserver {
  List<String> _data = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadData(); // 初始加载
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed && mounted) {
      print('应用回到前台，刷新数据');
      _loadData();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        print('从其他页面返回，刷新数据');
        _loadData();
      }
    });
  }

  Future<void> _loadData() async {
    if (_isLoading) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟网络请求
      await Future.delayed(const Duration(seconds: 1));
      
      if (mounted) {
        setState(() {
          _data = ['手动数据1', '手动数据2', '手动数据3', '更新时间: ${DateTime.now()}'];
        });
      }
    } catch (e) {
      // 处理错误
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('手动刷新示例')),
      body: _isLoading 
        ? const Center(child: CircularProgressIndicator())
        : ListView.builder(
            itemCount: _data.length,
            itemBuilder: (context, index) {
              return ListTile(
                title: Text(_data[index]),
              );
            },
          ),
    );
  }
}

/*
 * 使用建议：
 * 
 * 1. 简单的列表页面：使用 AutoRefreshMixin
 * 2. 需要更精细控制的页面：使用 PageVisibilityMixin  
 * 3. 特殊需求的页面：手动实现
 * 
 * 注意事项：
 * 
 * 1. 所有方法都会检查 mounted 状态，避免在页面销毁后调用 setState
 * 2. 建议在刷新方法中添加防重复调用的逻辑
 * 3. 网络请求失败时要有合适的错误处理
 * 4. 可以根据需要添加防抖或节流逻辑
 */ 