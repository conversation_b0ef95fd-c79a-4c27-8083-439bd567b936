import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

/// 自动刷新 Mixin - 简化版
/// 用于在页面显示时自动刷新数据
mixin AutoRefreshMixin<T extends StatefulWidget> on State<T>, WidgetsBindingObserver {
  final Logger _logger = Logger();
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用从后台回到前台时刷新数据
    if (state == AppLifecycleState.resumed && mounted) {
      _logger.i('应用回到前台，刷新页面数据');
      onRefreshData();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次页面依赖变化时刷新数据（包括从其他页面返回）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _logger.d('页面依赖变化，刷新数据');
        onRefreshData();
      }
    });
  }

  /// 子类需要实现的数据刷新方法
  void onRefreshData();
} 