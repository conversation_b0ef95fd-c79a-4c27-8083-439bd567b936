import 'package:flutter/material.dart';

class HexColor extends Color {
  static int _getColorFromHex(String hexColor) {
    hexColor = hexColor.toUpperCase().replaceAll("#", "");
    if (hexColor.length == 6) {
      hexColor = "FF$hexColor";
    }
    return int.parse(hexColor, radix: 16);
  }

  HexColor(final String hexColor) : super(_getColorFromHex(hexColor));
}

class BDHColor {
  // 白色
  static const Color white01 = Color(0xffffffff); // 白色不透明
  static const Color white06 = Color(0x99ffffff); // 白色0.6透明

  static const Color kClear = Color(0x00FFFFFF); //透明

  //黑色
  static const Color black00 = Color(0xff000000); // 黑色不透明
  static const Color black01 = Color.fromRGBO(0, 0, 0, 0.1); // 黑色 0.1透明
  static const Color black03 = Color.fromRGBO(0, 0, 0, 0.03); // 黑色 0.03透明
  static const Color black005 = Color.fromRGBO(0, 0, 0, 0.05); // 黑色 0.5透明

  static const Color black04 = Color.fromRGBO(0, 0, 0, 0.4); // 黑色 0.4透明
  static const Color black06 = Color.fromRGBO(0, 0, 0, 0.6); // 黑色 0.6透明

  //导航栏标题颜色
  static const Color blackNavTitelColor = Color(0xff000000); // 黑色不透明
  static const Color messagePageBGColor = Color(0xfff2f5f9); // 消息列表页 背景颜色

  //程序的背景颜色
  static const Color backgroundColor = Color.fromARGB(255, 243, 245, 249);
}
