import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';

Future<T?> showBottomPicker<T>(
  BuildContext context, {
  required Widget contentWidget,
  String? title,
  dynamic confirm,
  dynamic cancel,
  VoidCallback? onConfirm,
  VoidCallback? onCancel,
  bool barrierDismissible = true,
  bool showTitle = true,
}) {
  final ThemeData theme = Theme.of(context);
  return showGeneralDialog<T>(
    context: context,
    pageBuilder: (BuildContext buildContext, Animation<double> animation,
        Animation<double> secondaryAnimation) {
      final Widget pageChild = BrnBottomPickerWidget(
        contentWidget: contentWidget,
        confirm: confirm,
        cancel: cancel,
        onConfirmPressed: onConfirm,
        onCancelPressed: onCancel,
        barrierDismissible: barrierDismissible,
        pickerTitleConfig: BrnPickerTitleConfig(
          titleContent: title ?? '请选择',
          showTitle: showTitle,
        ),
      );
      return Theme(data: theme, child: pageChild);
    },
    barrierDismissible: barrierDismissible,
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
    barrierColor: Colors.black54,
    transitionDuration: const Duration(milliseconds: 150),
    transitionBuilder: (BuildContext context, Animation<double> animation,
        Animation<double> secondaryAnimation, Widget child) {
      return FadeTransition(
        opacity: CurvedAnimation(
          parent: animation,
          curve: Curves.easeOut,
        ),
        child: child,
      );
    },
    useRootNavigator: true,
  );
}

Future<T?> showBottomSheetPicker<T>(BuildContext context,
    {required Widget contentWidget,
    String? title,
    String? confirm,
    String? cancel,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    bool barrierDismissible = true,
    bool showTitle = true,
    double? height}) {
  return showModalBottomSheet<T>(
    context: context,
    backgroundColor: Colors.white,
    useSafeArea: true,
    isScrollControlled: true,
    shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16.0))),
    barrierColor: Colors.black54,
    builder: (BuildContext context) {
      final Widget child = Column(
        children: [
          SizedBox(
              height: 40.px,
              child: Row(
                children: [
                  SizedBox(
                    width: 54.px,
                  ),
                  Expanded(
                      child: Text(
                    textAlign: TextAlign.center,
                    title ?? "请选择",
                    style: TextStyle(
                        color: const Color.fromRGBO(44, 44, 44, 1),
                        fontSize: 16.px),
                  )),
                  SizedBox(
                      width: 54.px,
                      child: IconButton(
                          padding: EdgeInsets.zero,
                          onPressed: () {
                            Navigator.maybeOf(context)?.pop();
                          },
                          icon: Icon(
                            Icons.close,
                            color: const Color.fromRGBO(44, 44, 44, 0.5),
                            size: 20.px,
                          ))),
                ],
              )),
          Expanded(child: contentWidget),
          SizedBox(
            height: 7.px,
          ),
          Row(
            children: [
              SizedBox(
                width: 7.px,
              ),
              Expanded(
                  child: SizedBox(
                      height: 36.px,
                      child: TextButton(
                        style: ButtonStyle(
                            shape: WidgetStatePropertyAll(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.px))),
                            backgroundColor:
                                WidgetStateProperty.resolveWith((states) {
                              if (states.contains(WidgetState.disabled)) {
                                return Colors.white;
                              } else if (states.contains(WidgetState.pressed)) {
                                return const Color.fromRGBO(44, 44, 44, 0.2);
                              } else {
                                return const Color.fromRGBO(44, 44, 44, 0.3);
                              }
                            }),
                            foregroundColor:
                                WidgetStateProperty.resolveWith((states) {
                              return Colors.white;
                            }),
                            overlayColor:
                                WidgetStateProperty.all(Colors.transparent)),
                        onPressed: () {
                          if (onCancel != null) {
                            onCancel.call();
                          } else {
                            Navigator.maybeOf(context)?.pop();
                          }
                        },
                        child: Text(
                          cancel ?? "取消",
                          style: TextStyle(
                              fontSize: 14.px,
                              decoration: TextDecoration.none,
                              fontWeight: FontWeight.w500),
                        ),
                      ))),
              SizedBox(
                width: 7.px,
              ),
              Expanded(
                  child: SizedBox(
                      height: 36.px,
                      child: TextButton(
                        style: ButtonStyle(
                            shape: WidgetStatePropertyAll(
                                RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.px))),
                            backgroundColor:
                                WidgetStateProperty.resolveWith((states) {
                              if (states.contains(WidgetState.disabled)) {
                                return const Color.fromARGB(100, 94, 139, 245);
                              } else if (states.contains(WidgetState.pressed)) {
                                return const Color.fromARGB(200, 94, 139, 245);
                              } else {
                                return const Color.fromARGB(255, 94, 139, 245);
                              }
                            }),
                            overlayColor:
                                WidgetStateProperty.all(Colors.transparent)),
                        onPressed: () {
                          if (onConfirm != null) {
                            onConfirm.call();
                          } else {
                            Navigator.maybeOf(context)?.pop();
                          }
                        },
                        child: Text(
                          confirm ?? "确认",
                          style: TextStyle(
                              fontSize: 14.px,
                              decoration: TextDecoration.none,
                              color: Colors.white),
                        ),
                      ))),
              SizedBox(
                width: 7.px,
              ),
            ],
          ),
          SizedBox(
            height: 7.px,
          ),
        ],
      );
      var keyboardHeight = MediaQuery.viewInsetsOf(context).bottom;
      return Container(
        height: (height ?? 400.px) + keyboardHeight,
        padding: EdgeInsets.only(bottom: keyboardHeight),
        child: child,
      );
    },
    barrierLabel: MaterialLocalizations.of(context).modalBarrierDismissLabel,
  );
}

const String _loadingDialogTag = '_loadingDialogTag';

Future<T?> showLoading<T>(
  BuildContext context, {
  String? content,
  bool barrierDismissible = true,
  bool useRootNavigator = true,
}) {
  return BrnSafeDialog.show<T>(
      context: context,
      tag: _loadingDialogTag,
      barrierDismissible: barrierDismissible,
      useRootNavigator: useRootNavigator,
      builder: (_) {
        return PopScope(
            canPop: false,
            child: BrnLoadingDialog(
                content:
                    content ?? BrnIntl.of(context).localizedResource.loading));
      });
}

void hideLoading<T extends Object?>(BuildContext context, [T? result]) {
  BrnSafeDialog.dismiss<T>(
      context: context, tag: _loadingDialogTag, result: result);
}

Future<bool?> showConfirmDialog(
  BuildContext context, {
  String cancel = "取消",
  String confirm = "确定",
  bool showIcon = false,
  Image? iconWidget,
  String? title,
  Widget? titleWidget,
  String? message,
  Widget? messageWidget,
  String? warning,
  Widget? warningWidget,
  Widget? cancelWidget,
  Widget? conformWidget,
  GestureTapCallback? onCancel,
  GestureTapCallback? onConfirm,
  bool barrierDismissible = true,
  int titleMaxLines = cTitleMaxLines,
  BrnDialogConfig? themeData,
}) {
  List<Widget> actionsWidget = [];

  if (cancelWidget != null) {
    actionsWidget.add(cancelWidget);
  }
  if (conformWidget != null) {
    actionsWidget.add(conformWidget);
  }
  return showDialog<bool>(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (BuildContext dialogContext) {
      return BrnDialog(
        iconImage: iconWidget,
        showIcon: showIcon,
        titleText: title,
        titleWidget: titleWidget,
        messageText: message,
        contentWidget: messageWidget,
        warningWidget: warningWidget,
        warningText: warning,
        themeData: themeData,
        titleMaxLines: titleMaxLines,
        actionsText: [cancel, confirm],
        actionsWidget: actionsWidget,
        indexedActionCallback: (index) {
          if (index == 0) {
            if (onCancel != null) {
              onCancel();
            }
            Navigator.of(dialogContext).pop(false);
          } else if (index == 1) {
            if (onConfirm != null) {
              onConfirm();
            }
            Navigator.of(dialogContext).pop(true);
          }
        },
      );
    },
  );
}

Future<bool?> showNoticeDialog(
  BuildContext context, {
  String confirm = "确定",
  bool showIcon = false,
  Image? iconWidget,
  String? title,
  Widget? titleWidget,
  String? message,
  Widget? messageWidget,
  String? warning,
  Widget? warningWidget,
  Widget? conformWidget,
  GestureTapCallback? onConfirm,
  bool barrierDismissible = true,
  int titleMaxLines = cTitleMaxLines,
  BrnDialogConfig? themeData,
}) {
  List<Widget> actionsWidget = [];

  if (conformWidget != null) {
    actionsWidget.add(conformWidget);
  }
  return showDialog<bool>(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (BuildContext dialogContext) {
      return BrnDialog(
        iconImage: iconWidget,
        showIcon: showIcon,
        titleText: title,
        titleWidget: titleWidget,
        messageText: message,
        contentWidget: messageWidget,
        warningWidget: warningWidget,
        warningText: warning,
        themeData: themeData,
        titleMaxLines: titleMaxLines,
        actionsText: [confirm],
        actionsWidget: actionsWidget,
        indexedActionCallback: (index) {
          Navigator.of(dialogContext).pop();
        },
      );
    },
  );
}
