import 'dart:io';

import 'package:oktoast/oktoast.dart';
import 'package:url_launcher/url_launcher_string.dart';

class URLOpenUtils {
  /// 腾讯地图调用
  static Future<bool> openTencentMap(double longitude, double latitude,
      {required String address, bool showErr = true}) async {
    String url =
        'qqmap://map/routeplan?type=drive&fromcoord=CurrentLocation&tocoord=$latitude,$longitude&referer=FN4BZ-6E33P-LFTDB-VRZ4C-NTP3Z-RVFFK&debug=true&to=$address';
    if (Platform.isIOS) url = Uri.encodeFull(url);
    try {
      if (await canLaunchUrlString(url)) {
        await launchUrlString(url);
      } else {
        return false;
      }
      // await launchUrlString(url);
    } on Exception {
      if (showErr) showToast('无法调起腾讯地图');
      return false;
    }
    return true;
  }

  /// 高德地图调用
  static Future<bool> openAmap(double longitude, double latitude,
      {required String address, bool showErr = true}) async {
    String url =
        '${Platform.isAndroid ? 'android' : 'ios'}amap://navi?sourceApplication=amap&lat=$latitude&lon=$longitude&dev=0&style=2&poiname=$address';
    if (Platform.isIOS) url = Uri.encodeFull(url);
    try {
      if (await canLaunchUrlString(url)) {
        await launchUrlString(url);
      } else {
        return false;
      }
    } on Exception {
      if (showErr) showToast('无法调起高德地图');
      return false;
    }
    return true;
  }

  /// 百度地图
  static Future<bool> openBaiduMap(double longitude, double latitude,
      {required String address, bool showErr = true}) async {
    String url =
        'baidumap://map/marker?location=$latitude,$longitude&title=目的地&content=目的地&src=com.panda.flutter&coord_type=gcj02';
    if (Platform.isIOS) url = Uri.encodeFull(url);
    try {
      if (await canLaunchUrlString(url)) {
        await launchUrlString(url);
      } else {
        return false;
      }
    } on Exception {
      if (showErr) showToast('无法调起百度地图');
      return false;
    }
    return true;
  }

  /// 苹果地图
  static Future<bool> openAppleMap(longitude, latitude,
      {required String address, bool showErr = true}) async {
    String url =
        'http://maps.apple.com/?daddr=$latitude,$longitude&address=$address';
    if (Platform.isIOS) url = Uri.encodeFull(url);
    try {
      if (await canLaunchUrlString(url)) {
        await launchUrlString(url);
      } else {
        return false;
      }
      // await launchUrlString(url);
    } on Exception {
      if (showErr) showToast('无法调起高德地图');
      return false;
    }
    return true;
  }

  static Future<bool> openMap(longitude, latitude,
      {required String address, bool showErr = false}) async {
    bool flag = true;
    if (!await openAmap(longitude, latitude,
        address: address, showErr: showErr)) {
      if (!await openBaiduMap(longitude, latitude,
          address: address, showErr: showErr)) {
        if (!await openTencentMap(longitude, latitude,
            address: address, showErr: showErr)) {
          if (Platform.isIOS) {
            if (!await openAppleMap(longitude, latitude,
                address: address, showErr: showErr)) {
              flag = false;
              showToast('无法调用地图应用，请安装高德，百度，腾讯任意一种地图应用');
            }
          }
          // if (!await openAppleMap(longitude, latitude, address: address, showErr: showErr)) {
          flag = false;
          showToast('无法调用地图应用，请安装高德，百度，腾讯任意一种地图应用');
          // }
        }
      }
    }

    return flag;
  }
}
