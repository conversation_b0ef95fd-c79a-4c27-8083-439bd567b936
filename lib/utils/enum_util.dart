
import 'image_util.dart';

enum weatherCategory {
  clear(00), //晴
  cloudy(01), //多云
  yin(02), //阴
  shower(03), //阵雨
  thunderstorm(04), //雷阵雨
  thunderstormsAccompanied<PERSON>yhai<PERSON>(05), //
  sleet(06), //雨夹雪
  lightRain(07), //小雨
  moderateRain(08), //中雨
  heavyRain(09), //大雨
  storm(10), //暴雨
  heavyStorm(11), //大暴雨
  severeStorm(12), //特大暴雨
  snowShower(13), //阵雪
  lightSnow(14), //小雪
  moderateSnow(15), //中雪
  heavySnow(16), //大雪
  blizzard(17), //暴雪
  fog(18), //雾
  freezingRain(19), //冻雨
  sandstorm(20), //沙尘暴
  lightToModerateRain(21), //小到中雨
  moderateToHeavyRain(22), //中到大雨
  heavyToRainstorm(23), //大到暴雨
  heavyRainToStorm(24), //暴雨到大暴雨
  heavyRainstormToExtremelyHeavyRainstorm(25), //暴雨到大暴雨
  lightToModerateSnow(26), //小到中雪
  moderateToHeavySnow(27), //中到大雪
  heavySnowToBlizzard(28), //大到暴雪
  floatingDust(29), //浮尘
  yangsha(30), //扬沙
  strongSandstorm(31), //强沙尘暴
  thickFog(32), //浓雾
  snow(33), //雪
  weakHighBlowingSnow(34), //弱高吹雪
  lightMist(35), //轻雾
  strongFog(49), //强浓雾
  haze(53), //霾
  moderateHaze(54), //中度霾
  severeHaze(55), //重度霾
  seriousHaza(56), //严重霾
  heavyFog(57), //大雾
  moreStrongFog(58), //特强浓雾
  rain(301), //雨
  snowOther(302), //雪
  none(99);

  // 为枚举项定义一个公共属性来存储整数值
  final int value;

  // 构造函数是工厂构造函数，用于创建枚举实例
  const weatherCategory(this.value);

  factory weatherCategory.fromString(String value) {
    switch (value) {
      case '00':
        return clear;
      case '01':
        return cloudy;
      case '02':
        return yin;
      case '03':
        return shower;
      case '04':
        return thunderstorm;
      case '05':
        return thunderstormsAccompaniedByhail;
      case '06':
        return sleet;
      case '07':
        return lightRain;
      case '08':
        return moderateRain;
      case '09':
        return heavyRain;
      case '10':
        return storm;
      case '11':
        return heavyStorm;
      case '12':
        return severeStorm;
      case '13':
        return snowShower;
      case '14':
        return lightSnow;
      case '15':
        return moderateSnow;
      case '16':
        return heavySnow;
      case '17':
        return blizzard;
      case '18':
        return fog;
      case '19':
        return freezingRain;
      case '20':
        return sandstorm;
      case '21':
        return lightToModerateRain;
      case '22':
        return moderateToHeavyRain;
      case '23':
        return heavyToRainstorm;
      case '24':
        return heavyRainToStorm;
      case '25':
        return heavyRainstormToExtremelyHeavyRainstorm;
      case '26':
        return lightToModerateSnow;
      case '27':
        return moderateToHeavySnow;
      case '28':
        return heavySnowToBlizzard;
      case '29':
        return floatingDust;
      case '30':
        return yangsha;
      case '31':
        return strongSandstorm;
      case '32':
        return thickFog;
      case '33':
        return snow;
      case '34':
        return weakHighBlowingSnow;
      case '35':
        return lightMist;
      case '49':
        return strongFog;
      case '53':
        return haze;
      case '54':
        return moderateHaze;
      case '55':
        return severeHaze;
      case '56':
        return seriousHaza;
      case '57':
        return heavyFog;
      case '58':
        return moreStrongFog;
      case '301':
        return rain;
      case '302':
        return snowOther;
      default:
        return clear;
    }
  }

  factory weatherCategory.fromText(String value) {
    switch (value) {
      case '晴':
        return clear;
      case '多云':
        return cloudy;
      case '阴':
        return yin;
      case '阵雨':
        return shower;
      case '雷阵雨':
        return thunderstorm;
      case '雷阵雨伴有冰雹':
        return thunderstormsAccompaniedByhail;
      case '雨夹雪':
        return sleet;
      case '小雨':
        return lightRain;
      case '中雨':
        return moderateRain;
      case '大雨':
        return heavyRain;
      case '暴雨':
        return storm;
      case '大暴雨':
        return heavyStorm;
      case '特大暴雨':
        return severeStorm;
      case '阵雪':
        return snowShower;
      case '小雪':
        return lightSnow;
      case '中雪':
        return moderateSnow;
      case '大雪':
        return heavySnow;
      case '暴雪':
        return blizzard;
      case '雾':
        return fog;
      case '冻雨':
        return freezingRain;
      case '沙尘暴':
        return sandstorm;
      case '小到中雨':
        return lightToModerateRain;
      case '中到大雨':
        return moderateToHeavyRain;
      case '大到暴雨':
        return heavyToRainstorm;
      case '暴雨到大暴雨':
        return heavyRainToStorm;
      case '大暴雨到特大暴雨':
        return heavyRainstormToExtremelyHeavyRainstorm;
      case '小到中雪':
        return lightToModerateSnow;
      case '中到大雪':
        return moderateToHeavySnow;
      case '大到暴雪':
        return heavySnowToBlizzard;
      case '浮尘':
        return floatingDust;
      case '扬沙':
        return yangsha;
      case '强沙尘暴':
        return strongSandstorm;
      case '浓雾':
        return thickFog;
      case '雪':
        return snow;
      case '弱高吹雪':
        return weakHighBlowingSnow;
      case '轻雾':
        return lightMist;
      case '强浓雾':
        return strongFog;
      case '霾':
        return haze;
      case '中度霾':
        return moderateHaze;
      case '重度霾':
        return severeHaze;
      case '严重霾':
        return seriousHaza;
      case '大雾':
        return heavyFog;
      case '特强浓雾':
        return moreStrongFog;
      case '雨':
        return rain;
      // case '雪':
      //   return snowOther;
      default:
        return clear;
    }
  }

  String getWeatherImage() {
    switch (this) {
      case clear:
        return ImageHelper.weatherAssets('weather_clear.png');
      case cloudy:
        return ImageHelper.weatherAssets("weather_cloudy.png");
      case yin:
        return ImageHelper.weatherAssets("weather_yin.png");
      case shower:
        return ImageHelper.weatherAssets("weather_moderateRain.png");
      case thunderstorm:
        return ImageHelper.weatherAssets("weather_thunderstorm.png");
      case thunderstormsAccompaniedByhail:
        return ImageHelper.weatherAssets("weather_thunderstorm.png");
      case sleet:
        return ImageHelper.weatherAssets("weather_sleet.png");
      case lightRain:
        return ImageHelper.weatherAssets("weather_lightRain.png");
      case moderateRain:
        return ImageHelper.weatherAssets("weather_moderateRain.png");
      case heavyRain:
        return ImageHelper.weatherAssets("weather_heavyRain.png");
      case storm:
        return ImageHelper.weatherAssets("weather_storm.png");
      case heavyStorm:
        return ImageHelper.weatherAssets("weather_storm.png");
      case severeStorm:
        return ImageHelper.weatherAssets("weather_storm.png");
      case snowShower:
        return ImageHelper.weatherAssets("weather_lightSnow.png");
      case lightSnow:
        return ImageHelper.weatherAssets("weather_lightSnow.png");
      case moderateSnow:
        return ImageHelper.weatherAssets("weather_moderateSnow.png");
      case heavySnow:
        return ImageHelper.weatherAssets("weather_heavySnow.png");
      case blizzard:
        return ImageHelper.weatherAssets("weather_blizzard.png");
      case fog:
        return ImageHelper.weatherAssets("weather_fog.png");
      case freezingRain:
        return ImageHelper.weatherAssets("weather_freezingRain.png");
      case sandstorm:
        return ImageHelper.weatherAssets("weather_wind.png");
      case lightToModerateRain:
        return ImageHelper.weatherAssets("weather_moderateRain.png");
      case moderateToHeavyRain:
        return ImageHelper.weatherAssets("weather_heavyRain.png");
      case heavyToRainstorm:
        return ImageHelper.weatherAssets("weather_storm.png");
      case heavyRainToStorm:
        return ImageHelper.weatherAssets("weather_storm.png");
      case heavyRainstormToExtremelyHeavyRainstorm:
        return ImageHelper.weatherAssets("weather_storm.png");
      case lightToModerateSnow:
        return ImageHelper.weatherAssets("weather_moderateSnow.png");
      case moderateToHeavySnow:
        return ImageHelper.weatherAssets("weather_heavySnow.png");
      case heavySnowToBlizzard:
        return ImageHelper.weatherAssets("weather_blizzard.png");
      case floatingDust:
        return ImageHelper.weatherAssets("weather_wind.png");
      case yangsha:
        return ImageHelper.weatherAssets("weather_wind.png");
      case strongSandstorm:
        return ImageHelper.weatherAssets("weather_sandstorm.png");
      case thickFog:
        return ImageHelper.weatherAssets("weather_haze.png");
      case snow:
        return ImageHelper.weatherAssets("weather_lightSnow.png");
      case weakHighBlowingSnow:
        return ImageHelper.weatherAssets("weather_heavySnow.png");
      case lightMist:
        return ImageHelper.weatherAssets("weather_fog.png");
      case strongFog:
        return ImageHelper.weatherAssets("weather_haze.png");
      case haze:
        return ImageHelper.weatherAssets("weather_haze.png");
      case moderateHaze:
        return ImageHelper.weatherAssets("weather_haze.png");
      case severeHaze:
        return ImageHelper.weatherAssets("weather_haze.png");
      case seriousHaza:
        return ImageHelper.weatherAssets("weather_haze.png");
      case heavyFog:
        return ImageHelper.weatherAssets("weather_haze.png");
      case moreStrongFog:
        return ImageHelper.weatherAssets("weather_haze.png");
      case rain:
        return ImageHelper.weatherAssets("weather_heavyRain.png");
      case snowOther:
        return ImageHelper.weatherAssets("weather_lightSnow.png");
      default:
        return ImageHelper.noImageAssets();
    }
  }

  String getWeatherBgImage() {
    switch (this) {
      case clear:
        return ImageHelper.weatherAssets('weather_clear_bg.png');
      case cloudy:
        return ImageHelper.weatherAssets("weather_cloudy_bg.png");
      case yin:
        return ImageHelper.weatherAssets("weather_cloudy_bg.png");
      case shower:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case thunderstorm:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case thunderstormsAccompaniedByhail:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case sleet:
        return ImageHelper.weatherAssets("weather_heavySnow_bg.png");
      case lightRain:
        return ImageHelper.weatherAssets("weather_lightRain_bg.png");
      case moderateRain:
        return ImageHelper.weatherAssets("weather_moderateRain_bg.png");
      case heavyRain:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case storm:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case heavyStorm:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case severeStorm:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case snowShower:
        return ImageHelper.weatherAssets("weather_lightSnow_bg.png");
      case lightSnow:
        return ImageHelper.weatherAssets("weather_lightSnow_bg.png");
      case moderateSnow:
        return ImageHelper.weatherAssets("weather_moderateSnow_bg.png");
      case heavySnow:
        return ImageHelper.weatherAssets("weather_heavySnow_bg.png");
      case blizzard:
        return ImageHelper.weatherAssets("weather_heavySnow_bg.png");
      case fog:
        return ImageHelper.weatherAssets("weather_fog_bg.png");
      case freezingRain:
        return ImageHelper.weatherAssets("weather_heavySnow_bg.png");
      case sandstorm:
        return ImageHelper.weatherAssets("weather_sandstorm_bg.png");
      case lightToModerateRain:
        return ImageHelper.weatherAssets("weather_moderateRain_bg.png");
      case moderateToHeavyRain:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case heavyToRainstorm:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case heavyRainToStorm:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case heavyRainstormToExtremelyHeavyRainstorm:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case lightToModerateSnow:
        return ImageHelper.weatherAssets("weather_moderateSnow_bg.png");
      case moderateToHeavySnow:
        return ImageHelper.weatherAssets("weather_heavySnow_bg.png");
      case heavySnowToBlizzard:
        return ImageHelper.weatherAssets("weather_heavySnow_bg.png");
      case floatingDust:
        return ImageHelper.weatherAssets("weather_wind_bg.png");
      case yangsha:
        return ImageHelper.weatherAssets("weather_wind_bg.png");
      case strongSandstorm:
        return ImageHelper.weatherAssets("weather_sandstorm_bg.png");
      case thickFog:
        return ImageHelper.weatherAssets("weather_sandstorm_bg.png");
      case snow:
        return ImageHelper.weatherAssets("weather_lightSnow_bg.png");
      case weakHighBlowingSnow:
        return ImageHelper.weatherAssets("weather_heavySnow_bg.png");
      case lightMist:
        return ImageHelper.weatherAssets("weather_fog_bg.png");
      case strongFog:
        return ImageHelper.weatherAssets("weather_haze_bg.png");
      case haze:
        return ImageHelper.weatherAssets("weather_haze_bg.png");
      case moderateHaze:
        return ImageHelper.weatherAssets("weather_haze_bg.png");
      case severeHaze:
        return ImageHelper.weatherAssets("weather_haze_bg.png");
      case seriousHaza:
        return ImageHelper.weatherAssets("weather_haze_bg.png");
      case heavyFog:
        return ImageHelper.weatherAssets("weather_haze_bg.png");
      case moreStrongFog:
        return ImageHelper.weatherAssets("weather_haze_bg.png");
      case rain:
        return ImageHelper.weatherAssets("weather_heavyRain_bg.png");
      case snowOther:
        return ImageHelper.weatherAssets("weather_lightSnow_bg.png");
      default:
        return ImageHelper.noImageAssets();
    }
  }
}

