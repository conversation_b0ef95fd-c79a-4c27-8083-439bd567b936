import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../assets/app_colors.dart';

/// 获取统一通用的appbar
PreferredSizeWidget getCommonAppBar(String title,
    BuildContext context,
    {VoidCallback? onBack,
      bool centerTitle = true,
      Widget? leading,
      Color? bgColor,
      Color? titleColor,
      Color? backColor,
      Widget? titleChild,
      List<Widget>? actions,
      bool hideBackTitle = false,
      TabBar? bottom ,
      bool hideBack = false}) {
  return AppBar(
      centerTitle: centerTitle,
      toolbarHeight: kToolbarHeight,
      backgroundColor: bgColor ?? AppColors.bgColor,
      titleSpacing: .0,
      bottom:bottom,
      title: titleChild ??
          Text(title,
              style: TextStyle(
                  fontSize: 18,
                  height: 1.4,
                  color: titleColor ?? AppColors.mainTextColor33,
                  )),
      systemOverlayStyle: SystemUiOverlayStyle.dark,
      leading: leading ??
          (hideBack
              ? Container()
              : GestureDetector(
              child: Icon(Icons.arrow_back_ios_rounded,
                  color: backColor ?? Color(0xFF333333)),
              onTap: () {
                Navigator.pop(context!);
              })),
      actions: actions);
}

// /// 透明的appbar
// Widget transparentAppbar(String title,
//     {VoidCallback? onBack,
//     List<Widget>? action,
//     bool isShowIconBg = false,
//     Color? bgColor,
//     Color? titleColor,
//     Color? backBgColor,
//     Color? backColor}) {
//   return Container(
//       width: screen.screenWidth,
//       color: bgColor ?? Colors.transparent,
//       height: kToolbarHeight + screen.paddingTop,
//       padding: EdgeInsets.only(
//           right: AppPaddings.appMargin,
//           top: screen.paddingTop,
//           left: AppPaddings.appMargin),
//       child: Stack(alignment: Alignment.center, children: <Widget>[
//         Positioned(
//             left: 0,
//             child: IconButton(
//                 onPressed: onBack ?? () =>      Navigator.pop(context);,
//                 icon: isShowIconBg
//                     ? Container(
//                         width: 25,
//                         height: 25,
//                         padding: EdgeInsets.symmetric(
//                             horizontal: 7, vertical: 5),
//                         decoration: BoxDecoration(
//                             color: backBgColor ?? Colors.transparent,
//                             borderRadius: BorderRadius.circular(25)),
//                         child: Icon(Icons.arrow_back_ios_rounded,
//                             color: backColor ?? AppColors.mainTextColor33))
//                     : Icon(Icons.arrow_back_ios_rounded,
//                         color: backColor ?? AppColors.mainTextColor33))),
//         Container(
//             constraints: BoxConstraints(maxWidth: 230),
//             child: Text(title,
//                 maxLines: 1,
//                 overflow: TextOverflow.ellipsis,
//                 style: TextStyle(
//                     fontSize: 18,
//                     color: titleColor ?? AppColors.mainTextColor33,
//                     fontWeight: FontWeight.w400,
//                     decoration: TextDecoration.none))),
//         Positioned(right: 0, child: Row(children: action ?? []))
//       ]));
