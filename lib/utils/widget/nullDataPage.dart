import 'package:bdh_smart_agric_app/assets/app_colors.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../image_util.dart';
import '../screen/screen_tool.dart';

class NullDataPage extends StatefulWidget {
  final double? width;
  final double? height;
  final String? text;

  NullDataPage({super.key, this.text, this.width, this.height});

  @override
  State<NullDataPage> createState() => _NullDataPageState();
}

class _NullDataPageState extends State<NullDataPage> {
  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return const Center(
       child:  Text(
          textAlign: TextAlign.center,
          "" ?? "",
          style: TextStyle(fontSize: 16, color: Color(0xFF999999)),
        )
    );
  }
}
