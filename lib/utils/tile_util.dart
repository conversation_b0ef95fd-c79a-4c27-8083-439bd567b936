import 'package:flutter_map/flutter_map.dart';

class TileLayerUtil {
  static const String token = "e04b7f58f0a2a5b8a412ade93871c532";
  static const List<String> subdomains = [
    "t0",
    "t1",
    "t2",
    "t3",
    "t4",
    "t5",
    "t6",
    "t7",
  ];
  static TileLayer tileLayer(TianDiTuType type) {
    switch (type) {
      case TianDiTuType.vec:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=$token",
            subdomains: subdomains);

      case TianDiTuType.cva:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=$token",
            subdomains: subdomains);
      case TianDiTuType.eva:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.tianditu.gov.cn/eva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=eva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=$token",
            subdomains: subdomains);
      case TianDiTuType.img:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.tianditu.gov.cn/img_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=img&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=$token",
            subdomains: subdomains);
      case TianDiTuType.cia:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.tianditu.gov.cn/cia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=$token",
            subdomains: subdomains);
      case TianDiTuType.eia:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.tianditu.gov.cn/eia_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=eia&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=$token",
            subdomains: subdomains);
      case TianDiTuType.ter:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.tianditu.gov.cn/ter_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=ter&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=$token",
            subdomains: subdomains);
      case TianDiTuType.cta:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.tianditu.gov.cn/cta_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cta&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=$token",
            subdomains: subdomains);
      case TianDiTuType.amapi:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.is.autonavi.com/appmaptile?style=6&z={z}&y={y}&x={x}",
            subdomains: const ["webst01", "webst01", "webst02", "webst03"]);
      case TianDiTuType.bdh:
        return TileLayer(
          maxNativeZoom: 19,
          urlTemplate:
              "https://map-img.bdhic.com/WeServer/wmts/1.0.0/satellite1_3857/default/mct/{z}/{y}/{x}.jpg",
        );
      case TianDiTuType.bdh1:
        return TileLayer(
          tms: true,
          maxNativeZoom: 19,
          urlTemplate:
              "https://mapservice.bdhic.com/raster-static-api/cgwx/{z}/{x}/{y}.png",
        );
      case TianDiTuType.plot:
        return TileLayer(
          maxNativeZoom: 19,
          urlTemplate:
              "http://10.7.13.1:8243/gwc/service/wmts?layer=vector:res_parent_plot&style=&tilematrixset=EPSG:900913&Service=WMTS&Request=GetTile&Version=1.0.0&Format=image/png&TileMatrix=EPSG:900913:{z}&TileCol={x}&TileRow={y}",
        );

      default:
        return TileLayer(
            maxNativeZoom: 19,
            urlTemplate:
                "http://{s}.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILECOL={x}&TILEROW={y}&TILEMATRIX={z}&tk=$token",
            subdomains: subdomains);
    }
  }
}

enum TianDiTuType {
  ///矢量地图
  vec,

  ///矢量注记
  cva,

  ///矢量英文注记
  eva,

  ///影像地图
  img,

  ///影像注记
  cia,

  ///影像英文注记
  eia,

  ///地形渲染
  ter,

  ///地形注记
  cta,
  amapi,
  bdh,
  bdh1,
  plot
}
