import 'package:flutter/material.dart';

/// 页面可见性检测 Mixin
/// 用于实现类似小程序 onShow 的功能
mixin PageVisibilityMixin<T extends StatefulWidget> on State<T>, WidgetsBindingObserver {
  bool _isPageVisible = false;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // 页面初始化时认为是可见的
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onPageShow();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        // 应用回到前台
        if (!_isPageVisible) {
          _isPageVisible = true;
          _onPageShow();
        }
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        // 应用进入后台或非活跃状态
        if (_isPageVisible) {
          _isPageVisible = false;
          _onPageHide();
        }
        break;
      case AppLifecycleState.detached:
        // 应用被销毁
        _isPageVisible = false;
        _onPageHide();
        break;
      case AppLifecycleState.hidden:
        // 应用被隐藏
        if (_isPageVisible) {
          _isPageVisible = false;
          _onPageHide();
        }
        break;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 从其他页面返回时触发
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _onPageShow();
      }
    });
  }

  /// 页面显示时调用 - 类似小程序的 onShow
  void _onPageShow() {
    if (mounted) {
      onPageShow();
    }
  }

  /// 页面隐藏时调用 - 类似小程序的 onHide
  void _onPageHide() {
    if (mounted) {
      onPageHide();
    }
  }

  /// 子类需要实现的方法 - 页面显示时的回调
  void onPageShow();

  /// 子类可选实现的方法 - 页面隐藏时的回调
  void onPageHide() {}
} 