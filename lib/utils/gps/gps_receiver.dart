import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/gps/gd_gps_receiver.dart';
import 'package:flutter/material.dart';

abstract class GpsReceiver {
  static GpsReceiver? _instance;
  LocationResult? locationResult;
  Map<String, dynamic>? errorResult;

  void start(bool? isOnce);
  void stop();

  static GpsReceiver getInstance() {
    _instance ??= GDGpsReceiver();
    return _instance!;
  }

  void setLastLocation(LocationResult result) {
    locationResult = result;
    debugPrintStack(label: "${result.latitude},${result.longitude}");
    bus.emit("location", locationResult);
  }

  void setErrorLocation(Map<String, Object> result) {
    errorResult = result;
    bus.emit("locationError", result);
  }

  void cleanErrorLocation() {
    errorResult = null;
  }
}

class LocationResult {
  num? latitude;
  num? longitude;
  num? altitude;
  num? accuracy;
  String? address;
  String? addressCity;
  num? gcj02lat;
  num? gcj02lng;
  LocationResult(this.latitude, this.longitude, this.altitude, this.accuracy,
      this.address, this.addressCity, this.gcj02lat, this.gcj02lng);
}
