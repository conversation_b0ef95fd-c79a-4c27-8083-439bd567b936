// ignore_for_file: unused_field, cancel_subscriptions

import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:amap_flutter_location/amap_location_option.dart';
import 'package:bdh_smart_agric_app/utils/gps/gps_receiver.dart';
import 'package:bdh_smart_agric_app/utils/gps_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:permission_handler/permission_handler.dart';

class GDGpsReceiver extends GpsReceiver {
  // StreamSubscription<Map<String, Object>>? _locationListener;
  bool _isInit = false;

  final AMapFlutterLocation _locationPlugin = AMapFlutterLocation();

  @override
  void start(isOnce) {
    // this.convertor = con;

    if (!_isInit) {
      initAmapLocation();
    }

    _startLocation(isOnce: isOnce ?? false);
  }

  initAmapLocation() async {
    _isInit = true;

    AMapFlutterLocation.setApiKey(
        "037b9f222bb729e496fdcfbb63ca68b9", "e48eee4e9465adda8c3d64a1de787f6e");
    AMapFlutterLocation.updatePrivacyShow(true, true);
    AMapFlutterLocation.updatePrivacyAgree(true);

    /// 动态申请定位权限
    requestPermission();

    if (Platform.isIOS) {
      requestAccuracyAuthorization();
    }

    ///注册定位结果监听
    _locationPlugin.onLocationChanged().listen((Map<String, Object> result) {
      if (result['errorCode'] != null) {
        setErrorLocation(result);
        return;
      }
      cleanErrorLocation();
      num lat = 0;
      num lng = 0;
      if (Platform.isAndroid) {
        lat = result["latitude"] as num;
        lng = result["longitude"] as num;
      } else if (Platform.isIOS) {
        lat = double.parse(result["latitude"] as String);
        lng = double.parse(result["longitude"] as String);
      }
      //先转换下坐标
      List<num> coordinates = GpsUtil.gcj02ToGps84(lat, lng);
      BDHResponsitory.getAMapSearch(
              "${lng.toStringAsFixed(5)},${lat.toStringAsFixed(5)}")
          .then((res) {
        LocationResult location = LocationResult(
            coordinates[0],
            coordinates[1],
            result["altitude"] as num,
            result["accuracy"] as num,
            "${res.regeocode?.addressComponent?.province}${res.regeocode?.addressComponent?.city}${res.regeocode?.addressComponent?.district}",
            "${res.regeocode?.addressComponent?.city}",
            lat,
            lng);
        locationResult = location;
        setLastLocation(location);
      });
    });
  }

  @override
  stop() {
    _stopLocation();
  }

  ///获取iOS native的accuracyAuthorization类型
  void requestAccuracyAuthorization() async {
    AMapAccuracyAuthorization currentAccuracyAuthorization =
        await _locationPlugin.getSystemAccuracyAuthorization();
    if (currentAccuracyAuthorization ==
        AMapAccuracyAuthorization.AMapAccuracyAuthorizationFullAccuracy) {
      ////print("精确定位类型");
    } else if (currentAccuracyAuthorization ==
        AMapAccuracyAuthorization.AMapAccuracyAuthorizationReducedAccuracy) {
      ////print("模糊定位类型");
    } else {
      //print("未知定位类型");
    }
  }

  /// 动态申请定位权限
  void requestPermission() async {
    // 申请权限
    bool hasLocationPermission = await requestLocationPermission();
    if (hasLocationPermission) {
      //print("定位权限申请通过");
      _startLocation(isOnce: false);
    } else {
      //print("定位权限申请不通过");
    }
  }

  /// 申请定位权限
  /// 授予定位权限返回true， 否则返回false
  Future<bool> requestLocationPermission() async {
    //获取当前的权限
    var status = await Permission.location.status;
    if (status == PermissionStatus.granted) {
      //已经授权
      return true;
    } else {
      //未授权则发起一次申请
      status = await Permission.location.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }

  ///设置定位参数
  void _setLocationOption({required bool isOnce, int? interval}) {
    AMapLocationOption locationOption = AMapLocationOption();
    locationOption.onceLocation = isOnce;
    locationOption.needAddress = false;
    locationOption.geoLanguage = GeoLanguage.DEFAULT;
    locationOption.desiredLocationAccuracyAuthorizationMode =
        AMapLocationAccuracyAuthorizationMode.ReduceAccuracy;
    locationOption.fullAccuracyPurposeKey = "AMapLocationScene";
    locationOption.locationMode = AMapLocationMode.Battery_Saving;
    locationOption.desiredAccuracy = DesiredAccuracy.ThreeKilometers;
    locationOption.pausesLocationUpdatesAutomatically = false;
    locationOption.locationInterval = interval ?? 30000;

    _locationPlugin.setLocationOption(locationOption);
  }

  ///开始定位
  void _startLocation({bool isOnce = false, int? interval}) {
    _setLocationOption(isOnce: isOnce, interval: interval);
    _locationPlugin.startLocation();
  }

  ///停止定位
  void _stopLocation() {
    _locationPlugin.stopLocation();
    //_locationListener = null;
  }
}
