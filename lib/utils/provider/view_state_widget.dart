import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class ViewStateBusyWidget extends StatelessWidget {
  const ViewStateBusyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 150.px,
        child: Lottie.asset("assets/json/motion3.json"),
      ),
    );
  }
}

/// 基础Widget
class ViewStateWidget extends StatelessWidget {
  final String title;
  final String message;
  final Widget image;
  final Widget buttonText;
  final String buttonTextData;
  final VoidCallback onPressed;

  const ViewStateWidget(
      {super.key,
      required this.image,
      required this.title,
      required this.message,
      required this.buttonText,
      required this.onPressed,
      required this.buttonTextData});

  @override
  Widget build(BuildContext context) {
    var titleStyle =
        Theme.of(context).textTheme.titleMedium!.copyWith(color: Colors.grey);
    var messageStyle = titleStyle.copyWith(
        color: titleStyle.color!.withOpacity(0.7), fontSize: 14);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        image,
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 30),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              Text(
                title,
                style: titleStyle,
              ),
              const SizedBox(height: 20),
              ConstrainedBox(
                constraints:
                    const BoxConstraints(maxHeight: 200, minHeight: 150),
                child: SingleChildScrollView(
                  child: Text(message, style: messageStyle),
                ),
              ),
            ],
          ),
        ),
        Center(
          child: ViewStateButton(
            textData: buttonTextData,
            onPressed: onPressed,
            child: buttonText,
          ),
        ),
      ],
    );
  }
}

// ErrorWidget
class ViewStateErrorWidget extends StatelessWidget {
  final ViewStateError error;
  final String? title;
  final String? message;
  final Widget? image;
  final Widget? buttonText;
  final String? buttonTextData;
  final VoidCallback onPressed;

  const ViewStateErrorWidget({
    super.key,
    required this.error,
    this.image,
    this.title,
    this.message,
    this.buttonText,
    this.buttonTextData,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    Widget defaultImage;
    String defaultTitle;
    String defaultTextData = "错误";
    switch (error.errorType!) {
      case ViewStateErrorType.networkTimeOutError:
        defaultImage = Transform.translate(
          offset: const Offset(-50, 0),
          child: const Icon(Icons.network_check_sharp,
              size: 100, color: Colors.grey),
        );
        defaultTitle = "网络异常";
        // errorMessage = ''; // 网络异常移除message提示
        break;
      case ViewStateErrorType.defaultError:
        defaultImage = const Icon(Icons.error, size: 100, color: Colors.grey);
        defaultTitle = "默认错误";
        break;

      case ViewStateErrorType.unauthorizedError:
        return ViewStateUnAuthWidget(
          image: image,
          message: message,
          buttonText: buttonText,
          onPressed: onPressed,
        );
    }

    return ViewStateWidget(
      onPressed: onPressed,
      image: image ?? defaultImage,
      title: title ?? defaultTitle,
      message: message ?? defaultTextData,
      buttonTextData: buttonTextData ?? defaultTextData,
      buttonText: buttonText ?? const Text(""),
    );
  }
}

class ViewStateEmptyWidget extends StatelessWidget {
  final String? message;
  final Widget? image;
  final Widget? buttonText;
  final VoidCallback onPressed;

  const ViewStateEmptyWidget(
      {super.key,
      this.image,
      this.message,
      this.buttonText,
      required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return ViewStateWidget(
      onPressed: onPressed,
      image: image ??
          const Icon(Icons.hourglass_empty, size: 100, color: Colors.grey),
      title: message ?? "无数据",
      buttonText: buttonText ?? const Text("再试试"),
      buttonTextData: "刷新一下",
      message: '',
    );
  }
}

/// 页面未授权
class ViewStateUnAuthWidget extends StatelessWidget {
  final String? message;
  final Widget? image;
  final Widget? buttonText;
  final VoidCallback onPressed;

  const ViewStateUnAuthWidget(
      {super.key,
      this.image,
      this.message,
      this.buttonText,
      required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return ViewStateWidget(
      onPressed: onPressed,
      image: image ?? const ViewStateUnAuthImage(),
      title: message ?? "无权限",
      buttonText: buttonText!,
      buttonTextData: "请登录",
      message: '',
    );
  }
}

/// 未授权图片
class ViewStateUnAuthImage extends StatelessWidget {
  const ViewStateUnAuthImage({super.key});

  @override
  Widget build(BuildContext context) {
    return Hero(
      tag: 'loginLogo',
      child: Image.asset(
        ImageHelper.wrapAssets('logo.png'),
        width: 130,
        height: 100,
        fit: BoxFit.fitWidth,
        color: Theme.of(context).colorScheme.surface,
        colorBlendMode: BlendMode.srcIn,
      ),
    );
  }
}

class ViewStateButton extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget child;
  final String textData;

  const ViewStateButton(
      {super.key,
      required this.onPressed,
      required this.child,
      required this.textData});

  @override
  Widget build(BuildContext context) {
    return OutlinedButton(
      onPressed: onPressed,
      child: child,
    );
  }
}
