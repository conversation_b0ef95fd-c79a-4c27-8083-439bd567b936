import 'dart:io';

import 'package:flutter_tts/flutter_tts.dart';

class TTSService {
  static final TTSService _instance = TTSService._();

  final Set<TTSListener> listeners = {};

  TTSListener? _currentListener;
  String? _speckingText;

  TTSService._() {
    _init();
  }

  factory TTSService() => _instance;

  final FlutterTts tts = FlutterTts();

  bool _isInit = false;

  void _init() async {
    if (_isInit) {
      return;
    }
    _isInit = true;
    if (Platform.isIOS) {
      await tts.setSharedInstance(true);
      await tts.setIosAudioCategory(
          IosTextToSpeechAudioCategory.ambient,
          [
            IosTextToSpeechAudioCategoryOptions.allowBluetooth,
            IosTextToSpeechAudioCategoryOptions.allowBluetoothA2DP,
            IosTextToSpeechAudioCategoryOptions.mixWithOthers
          ],
          IosTextToSpeechAudioMode.voicePrompt);
    }

    await tts.setLanguage("zh-CN");

    tts.setStartHandler(_setStartHandler);
    tts.setCompletionHandler(_setCompletionHandler);
    tts.setProgressHandler(_setProgressHandler);
    tts.setErrorHandler(_setErrorHandler);
    tts.setCancelHandler(_setCancelHandler);
    tts.setPauseHandler(_setPauseHandler);
    tts.setContinueHandler(_setContinueHandler);
  }

  Future speck(String text, TTSListener listener) async {
    if (_currentListener != null) {
      _currentListener?.onCancel(_speckingText);
      _currentListener = null;
      _speckingText = null;
      await tts.stop();
    }
    _currentListener = listener;
    _speckingText = text;
    _currentListener?.onStart(text);
    return tts.speak(text);
  }

  Future pause() {
    return tts.pause();
  }

  void clear() {
    _currentListener = null;
  }

  void _setStartHandler() {
    _currentListener?.onStart(_speckingText);
  }

  void _setCompletionHandler() {
    _currentListener?.onComplete(_speckingText);
    _currentListener = null;
    _speckingText = null;
  }

  void _setProgressHandler(
      String text, int startOffset, int endOffset, String word) {
    _currentListener?.onProgress(text, startOffset, endOffset, word);
  }

  void _setErrorHandler(msg) {
    for (var action in listeners) {
      action.onError(msg);
    }
  }

  void _setCancelHandler() {
    // _currentListener?.onCancel(_speckingText);
    // _currentListener = null;
    // _speckingText = null;
  }

  void _setPauseHandler() {
    _currentListener?.onPause();
  }

  void _setContinueHandler() {
    _currentListener?.onContinue();
  }
}

mixin TTSListener {
  void onStart(String? text) {}

  void onComplete(String? text) {}

  void onProgress(String text, int startOffset, int endOffset, String word) {}

  void onError(msg) {}

  void onCancel(String? text) {}

  void onPause() {}

  void onContinue() {}
}
