import 'dart:math';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;

import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/widgets.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';

class ImageHelper {
  static const String baseUrl = '';
  static String noImageAssets() {
    return "assets/images/weather/defultImage.png";
  }

  static String weatherAssets(String url) {
    return "assets/images/weather/$url";
  }

  static String wrapAssets(String url) {
    return "assets/images/$url";
  }
 static String wrapThreeAssets(String url) {
    return "assets/images/three/$url";
  }

  static String wrapOldAssets(String url) {
    return "assets/images/menu/$url";
  }

  static String wrapWeatherAssets(String url) {
    return "assets/images/weather/$url";
  }

  static String wrapGuideAssets(String url) {
    return "assets/images/guideImage/$url";
  }

  static String wrapServiceApplicationAssets(String url) {
    return "assets/images/serviceApplicationIcon/$url";
  }

  static String wrapBdhDigitalImagesAssets(String url) {
    return "assets/images/bdhDigitalImages/$url";
  }

  static Widget placeHolder({required double width, required double height}) {
    return SizedBox(
        width: width,
        height: height,
        child: CupertinoActivityIndicator(radius: min(10.0, width / 3)));
  }

  static Widget error(
      {required double width, required double height, double? size}) {
    return SizedBox(
        width: width,
        height: height,
        child: Icon(
          Icons.error_outline,
          size: size,
        ));
  }

  static String randomUrl(
      {int width = 100, int height = 100, Object key = ''}) {
    return 'http://placeimg.com/$width/$height/${key.hashCode.toString() + key.toString()}';
  }

  static String networkUrl(String? url) {
    //Log.d("url is ${url}");
    if (url?.startsWith("http") == true) {
      return url!;
    }

    url = "${urlConfig.microfront}$url";
    //Log.d("url 2 is ${url}");
    return url;
  }
}

class IconFonts {
  IconFonts._();

  /// iconfont:flutter base
  static const String fontFamily = 'iconfont';

  static const IconData pageEmpty = IconData(0xe63c, fontFamily: fontFamily);
  static const IconData pageError = IconData(0xe600, fontFamily: fontFamily);
  static const IconData pageNetworkError =
      IconData(0xe678, fontFamily: fontFamily);
  static const IconData pageUnAuth = IconData(0xe65f, fontFamily: fontFamily);
}

// ------------------------------------------------------
// usage ：图片压缩工具类
// ------------------------------------------------------

class CompressUtil {
  static int minHeight = 100; // 指定最小分辨率的高度
  static int minWidth = 100; // 指定最小分辨率的宽度

  /// 压缩方式一 Uint8List -> Uint8List
  static Future<Uint8List> u8ToU8(Uint8List list) async {
    int quality = imageQuality(list.length);
    Uint8List result = await FlutterImageCompress.compressWithList(
      list,
      minWidth: minWidth,
      minHeight: minHeight,
      quality: quality,
    );
    debugPrint("压缩后图片的大小：${size(result.length)}");
    return result;
  }

  /// 压缩方式二 File -> File
  static Future<File?> fileToFile(File file) async {
    // 图片质量
    int quality = imageQuality(file.readAsBytesSync().length);
    // 缓存路径
    Directory cache = await getTemporaryDirectory();
    int time = DateTime.now().millisecondsSinceEpoch;
    String savePath = cache.path + "/AllenSu_$time.jpg";
    File? result = (await FlutterImageCompress.compressAndGetFile(
      file.path,
      savePath,
      minWidth: minWidth,
      minHeight: minHeight,
      quality: quality,
    )) as File?;
    if (result != null) {
      debugPrint("压缩后图片的大小：${size(result.readAsBytesSync().length)}");
    }
    return result;
  }

  /// 压缩方式三 File -> Uint8List
  static Future<Uint8List?> fileToU8(File file) async {
    // 图片质量
    int quality = imageQuality(file.readAsBytesSync().length);
    Uint8List? result = await FlutterImageCompress.compressWithFile(
      file.path,
      minWidth: minWidth,
      minHeight: minHeight,
      quality: quality,
    );
    if (result != null) {
      debugPrint("压缩后图片的大小：${size(result.length)}");
    }
    return result;
  }

  /// 压缩方式四 Asset -> Uint8List
  static Future<Uint8List?> assetToU8(String assetName) async {
    File file = File(assetName);
    // 图片质量
    int quality = imageQuality(file.readAsBytesSync().length);
    Uint8List? result = await FlutterImageCompress.compressAssetImage(
      assetName,
      minWidth: minWidth,
      minHeight: minHeight,
      quality: quality,
    );
    if (result != null) {
      debugPrint("压缩后图片的大小：${size(result.length)}");
    }
    return result!;
  }

  /// 根据传入的图片字节长度，返回指定的图片质量
  static int imageQuality(int length) {
    debugPrint("压缩前图片的大小：${size(length)}");
    int quality = 100; // 图片质量指数
    int m = 1024 * 1024; // 1 兆
    if (length < 0.5 * m) {
      quality = 70;
      debugPrint("图片小于 0.5 兆，质量设置为 70");
    } else if (length >= 0.5 * m && length < 1 * m) {
      quality = 60;
      debugPrint("图片大于 0.5 兆小于 1 兆，质量设置为 60");
    } else if (length >= 1 * m && length < 2 * m) {
      quality = 50;
      debugPrint("图片大于 1 兆小于 2 兆，质量设置为 50");
    } else if (length >= 2 * m && length < 3 * m) {
      quality = 40;
      debugPrint("图片大于 2 兆小于 3 兆，质量设置为 40");
    } else if (length >= 3 * m && length < 4 * m) {
      quality = 30;
      debugPrint("图片大于 3 兆小于 4 兆，质量设置为 30");
    } else {
      quality = 20;
      debugPrint("图片大于 4 兆，质量设置为 20");
    }
    return quality;
  }

  /// 根据传入的字节长度，转换成相应的 M 和 KB
  static String size(int length) {
    String res = "";
    final int unit = 1024;
    final int m = unit * unit; // 1M
    // 如果小于 1 兆，显示 KB
    if (length < 1 * m) {
      res = (length / unit).toStringAsFixed(0) + " KB";
    }
    // 如果大于 1 兆，显示 MB，并保留一位小数
    if (length >= 1 * m) {
      res = (length / m).toStringAsFixed(1) + " MB";
    }
    return res;
  }

  /// 将Assets图片转换为Uint8List
  static Future<Uint8List> assetToUint8List(String assetPath) async {
    final byteData = await rootBundle.load(assetPath);
    return byteData.buffer.asUint8List();
  }
}

class RotateImageUtil {
  static Future<XFile> rotateImageIfNeeded(File imageFile) async {
    try {
      // 读取图片数据
      Uint8List imageBytes = await imageFile.readAsBytes();
      // 解码图片
      img.Image? originalImage = img.decodeImage(imageBytes);
      if (originalImage == null) {
        throw Exception('无法解码图片');
      }
      // 检查图片是否为竖向（高度大于宽度）
      bool isPortrait = originalImage.height > originalImage.width;
      img.Image processedImage;
      if (isPortrait) {
        // 如果是竖向，旋转90度变为横向
        processedImage = img.copyRotate(originalImage, angle: 90);
        print('检测到竖向图片，已旋转90度');
      } else {
        // 如果已经是横向，不需要旋转
        processedImage = originalImage;
        print('图片已经是横向，无需旋转');
      }

      // 编码为JPEG
      List<int> processedBytes = img.encodeJpg(processedImage, quality: 85);

      // // 创建新的临时文件
      // String originalPath = imageFile.path;
      // String directory =
      //     originalPath.substring(0, originalPath.lastIndexOf('/'));
      // String fileName =
      //     'processed_${DateTime.now().millisecondsSinceEpoch}.jpg';
      // String newPath = '$directory/$fileName';

      // File processedFile = File(newPath);
      // await processedFile.writeAsBytes(processedBytes);

      // return processedFile;

      // 创建新的临时文件
      String originalPath = imageFile.path;
      String directory =
          originalPath.substring(0, originalPath.lastIndexOf('/'));
      String fileName =
          'processed_${DateTime.now().millisecondsSinceEpoch}.jpg';
      String newPath = '$directory/$fileName';

      File processedFile = File(newPath);
      await processedFile.writeAsBytes(processedBytes);

      return XFile(newPath);
    } catch (e) {
      print('处理图片时出错: $e');
      throw Exception('图片处理失败: $e');
    }
  }
}
