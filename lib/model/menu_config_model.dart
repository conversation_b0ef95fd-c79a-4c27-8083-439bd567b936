class MenuConfigResult {
  bool? success;
  int? code;
  String? msg;
  List<MenuConfigItem>? data;

  MenuConfigResult({this.success, this.code, this.msg, this.data});

  MenuConfigResult.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    if (json['data'] != null) {
      data = <MenuConfigItem>[];
      json['data'].forEach((v) {
        data!.add(MenuConfigItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> json = <String, dynamic>{};
    json['success'] = success;
    json['code'] = code;
    json['msg'] = msg;

    if (data != null) {
      json['data'] = data!.map((e) {
        return e.toJson();
      }).toList();
    }

    return json;
  }
}

class MenuConfigItem {
  String? path;
  int? authId;
  int? parentId;
  String? authCode;
  String? authName;
  String? authType;
  String? url;
  String? icon;
  bool? isNetIcon;
  int? orderNum;
  String? menuUrl;
  String? menuIconUrl;
  String? menuName;
  String? menuCode;
  String? enable;

  List<MenuConfigItem>? children;

  MenuConfigItem({
    this.path,
    this.authId,
    this.parentId,
    this.authCode,
    this.authName,
    this.authType,
    this.url,
    this.orderNum,
    this.children,
    this.icon,
    this.isNetIcon = false,
    this.menuUrl,
    this.menuIconUrl,
    this.menuName,
    this.menuCode,
    this.enable,
  });

  MenuConfigItem.fromJson(Map<String, dynamic> json) {
    path = json['path'];
    authId = json['authId'];
    isNetIcon = json['isNetIcon'];
    parentId = json['parentId'];
    authCode = json['authCode'];
    authName = json['authName'];
    authType = json['authType'];
    url = json['url'];
    orderNum = json['orderNum'];
    icon = json['icon'];

    menuUrl = json['menuUrl'];
    menuIconUrl = json['menuIconUrl'];
    menuName = json['menuName'];
    menuCode = json['menuCode'];
    enable = json['enable'];

    if (json['children'] != null) {
      children = <MenuConfigItem>[];
      json['children'].forEach((v) {
        children!.add(MenuConfigItem.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['path'] = path;
    data['authId'] = authId;
    data['parentId'] = parentId;
    data['authCode'] = authCode;
    data['authName'] = authName;
    data['authType'] = authType;
    data['url'] = url;
    data['orderNum'] = orderNum;
    data['icon'] = icon;

    data['menuUrl'] = menuUrl;
    data['menuIconUrl'] = menuIconUrl;
    data['menuName'] = menuName;
    data['menuCode'] = menuCode;
    data['enable'] = enable;

    if (children != null) {
      data['children'] = children!.map((e) {
        return e.toJson();
      }).toList();
    }
    return data;
  }
}
