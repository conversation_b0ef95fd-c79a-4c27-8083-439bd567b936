class SecondaryMenuModel {
  bool? success;
  int? code;
  String? msg;
  Data? data;

  SecondaryMenuModel({this.success, this.code, this.msg, this.data});

  SecondaryMenuModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = success;
    data['code'] = code;
    data['msg'] = msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<AppMenuList>? appMenuList;
  List<String>? ampCodeList;
  dynamic groupList;
  dynamic collectApplicatList;

  Data(
      {this.appMenuList,
      this.ampCodeList,
      this.groupList,
      this.collectApplicatList});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['appMenuList'] != null) {
      appMenuList = <AppMenuList>[];
      json['appMenuList'].forEach((v) {
        appMenuList!.add(AppMenuList.fromJson(v));
      });
    }
    ampCodeList = json['ampCodeList'].cast<String>();
    groupList = json['groupList'];
    collectApplicatList = json['collectApplicatList'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (appMenuList != null) {
      data['appMenuList'] = appMenuList!.map((v) => v.toJson()).toList();
    }
    data['ampCodeList'] = ampCodeList;
    data['groupList'] = groupList;
    data['collectApplicatList'] = collectApplicatList;
    return data;
  }
}

class AppMenuList {
  String? text;
  dynamic group;
  String? path;
  String? icon;
  String? parentText;
  dynamic others;
  bool? hidden;

  AppMenuList(
      {this.text,
      this.group,
      this.path,
      this.icon,
      this.parentText,
      this.others,
      this.hidden});

  AppMenuList.fromJson(Map<String, dynamic> json) {
    text = json['text'];
    group = json['group'];
    path = json['path'];
    icon = json['icon'];
    parentText = json['parentText'];
    others = json['others'];
    hidden = json['hidden'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['text'] = text;
    data['group'] = group;
    data['path'] = path;
    data['icon'] = icon;
    data['parentText'] = parentText;
    data['others'] = others;
    data['hidden'] = hidden;
    return data;
  }
}
