import 'dart:core';

class DiaAuthentModel {
  bool? success;
  num? code; // 将 int 改为 num
  String? msg;
  Data? data;

  DiaAuthentModel({this.success, this.code, this.msg, this.data});

  DiaAuthentModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = success;
    data['code'] = code;
    data['msg'] = msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<DiaAuthentRecords>? records;
  num? total; // 将 int 改为 num
  num? size; // 将 int 改为 num
  num? current; // 将 int 改为 num
  List<dynamic>? orders; // 将 List<Null> 改为 List<dynamic>
  bool? optimizeCountSql;
  bool? hitCount;
  dynamic countId; // 将 Null 改为 dynamic
  dynamic maxLimit; // 将 Null 改为 dynamic
  bool? searchCount;
  num? pages; // 将 int 改为 num

  Data(
      {this.records,
      this.total,
      this.size,
      this.current,
      this.orders,
      this.optimizeCountSql,
      this.hitCount,
      this.countId,
      this.maxLimit,
      this.searchCount,
      this.pages});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['records'] != null) {
      records = <DiaAuthentRecords>[];
      json['records'].forEach((v) {
        records!.add(new DiaAuthentRecords.fromJson(v));
      });
    }
    total = json['total'];
    size = json['size'];
    current = json['current'];
    if (json['orders'] != null) {
      orders = <dynamic>[]; // 修复类型错误
      json['orders'].forEach((v) {
        orders!.add(v); // 直接添加动态类型，不需要调用 fromJson
      });
    }
    optimizeCountSql = json['optimizeCountSql'];
    hitCount = json['hitCount'];
    countId = json['countId'];
    maxLimit = json['maxLimit'];
    searchCount = json['searchCount'];
    pages = json['pages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (records != null) {
      data['records'] = records!.map((v) => v.toJson()).toList();
    }
    data['total'] = total;
    data['size'] = size;
    data['current'] = current;
    if (orders != null) {
      data['orders'] = orders; // 直接赋值动态类型列表
    }
    data['optimizeCountSql'] = optimizeCountSql;
    data['hitCount'] = hitCount;
    data['countId'] = countId;
    data['maxLimit'] = maxLimit;
    data['searchCount'] = searchCount;
    data['pages'] = pages;
    return data;
  }
}

class DiaAuthentRecords {
  num? lcRationValidateId; // 将 int 改为 num
  num? farmerId; // 将 int 改为 num
  String? name;
  String? idNumber;
  dynamic createBy; // 将 Null 改为 dynamic
  String? createTime;
  dynamic updateBy; // 将 Null 改为 dynamic
  dynamic updateTime; // 将 Null 改为 dynamic
  dynamic statusCd; // 将 Null 改为 dynamic
  dynamic version; // 将 Null 改为 dynamic
  dynamic creatorName; // 将 Null 改为 dynamic
  dynamic updaterName; // 将 Null 改为 dynamic
  String? organizationNo;
  String? organizationName;
  String? longitude;
  String? latitude;
  String? validateNum;
  num? yearNo; // 将 int 改为 num
  dynamic params; // 将 Null 改为 dynamic
  dynamic age; // 将 Null 改为 dynamic
  dynamic sexNo; // 将 Null 改为 dynamic
  dynamic rationRight; // 将 Null 改为 dynamic
  dynamic householderRelation; // 将 Null 改为 dynamic
  dynamic validateCount; // 将 Null 改为 dynamic
  dynamic idCrad; // 将 Null 改为 dynamic
  num? auditAFlag; // 将 int 改为 num
  String? auditAFlagName;
  dynamic auditAId; // 将 Null 改为 dynamic
  dynamic auditAName; // 将 Null 改为 dynamic
  dynamic auditATime; // 将 Null 改为 dynamic
  dynamic farmerNameOrCard; // 将 Null 改为 dynamic

  DiaAuthentRecords(
      {this.lcRationValidateId,
      this.farmerId,
      this.name,
      this.idNumber,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.statusCd,
      this.version,
      this.creatorName,
      this.updaterName,
      this.organizationNo,
      this.organizationName,
      this.longitude,
      this.latitude,
      this.validateNum,
      this.yearNo,
      this.params,
      this.age,
      this.sexNo,
      this.rationRight,
      this.householderRelation,
      this.validateCount,
      this.idCrad,
      this.auditAFlag,
      this.auditAFlagName,
      this.auditAId,
      this.auditAName,
      this.auditATime,
      this.farmerNameOrCard});

  DiaAuthentRecords.fromJson(Map<String, dynamic> json) {
    lcRationValidateId = json['lcRationValidateId'];
    farmerId = json['farmerId'];
    name = json['name'];
    idNumber = json['idNumber'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    updateBy = json['updateBy'];
    updateTime = json['updateTime'];
    statusCd = json['statusCd'];
    version = json['version'];
    creatorName = json['creatorName'];
    updaterName = json['updaterName'];
    organizationNo = json['organizationNo'];
    organizationName = json['organizationName'];
    longitude = json['longitude'];
    latitude = json['latitude'];
    validateNum = json['validateNum'];
    yearNo = json['yearNo'];
    params = json['params'];
    age = json['age'];
    sexNo = json['sexNo'];
    rationRight = json['rationRight'];
    householderRelation = json['householderRelation'];
    validateCount = json['validateCount'];
    idCrad = json['idCrad'];
    auditAFlag = json['auditAFlag'];
    auditAFlagName = json['auditAFlagName'];
    auditAId = json['auditAId'];
    auditAName = json['auditAName'];
    auditATime = json['auditATime'];
    farmerNameOrCard = json['farmerNameOrCard'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['lcRationValidateId'] = lcRationValidateId;
    data['farmerId'] = farmerId;
    data['name'] = name;
    data['idNumber'] = idNumber;
    data['createBy'] = createBy;
    data['createTime'] = createTime;
    data['updateBy'] = updateBy;
    data['updateTime'] = updateTime;
    data['statusCd'] = statusCd;
    data['version'] = version;
    data['creatorName'] = creatorName;
    data['updaterName'] = updaterName;
    data['organizationNo'] = organizationNo;
    data['organizationName'] = organizationName;
    data['longitude'] = longitude;
    data['latitude'] = latitude;
    data['validateNum'] = validateNum;
    data['yearNo'] = yearNo;
    data['params'] = params;
    data['age'] = age;
    data['sexNo'] = sexNo;
    data['rationRight'] = rationRight;
    data['householderRelation'] = householderRelation;
    data['validateCount'] = validateCount;
    data['idCrad'] = idCrad;
    data['auditAFlag'] = auditAFlag;
    data['auditAFlagName'] = auditAFlagName;
    data['auditAId'] = auditAId;
    data['auditAName'] = auditAName;
    data['auditATime'] = auditATime;
    data['farmerNameOrCard'] = farmerNameOrCard;
    return data;
  }
}
