// To parse this JSON data, do
//
//     final mainLayoutModelBdhDigital = mainLayoutModelBdhDigitalFromJson(jsonString);

// import 'dart:convert';

// MainLayoutModelBdhDigital mainLayoutModelBdhDigitalFromJson(String str) => MainLayoutModelBdhDigital.fromJson(json.decode(str));

// String mainLayoutModelBdhDigitalToJson(MainLayoutModelBdhDigital data) => json.encode(data.toJson());

class MainLayoutModelBdhDigital {
  bool success;
  int code;
  String msg;
  MainLayoutData data;

  MainLayoutModelBdhDigital({
    required this.success,
    required this.code,
    required this.msg,
    required this.data,
  });

  factory MainLayoutModelBdhDigital.fromJson(Map<String, dynamic> json) =>
      MainLayoutModelBdhDigital(
        success: json["success"],
        code: json["code"],
        msg: json["msg"],
        data: MainLayoutData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "code": code,
        "msg": msg,
        "data": data.toJson(),
      };
}

class MainLayoutData {
  // List<dynamic> noticeList;
  List<Categor> categories;
  // List<SwiperList> swiperList;
  // List<dynamic> newsList;
  // List<CollectApplicatList> collectApplicatList;
  // List<Group> groupList;

  MainLayoutData({
    // required this.noticeList,
    required this.categories,
    // required this.swiperList,
    // required this.newsList,
    // required this.collectApplicatList,
    // required this.groupList,
  });

  factory MainLayoutData.fromJson(Map<String, dynamic> json) => MainLayoutData(
        // noticeList: List<dynamic>.from(json["noticeList"].map((x) => x)),
        categories: List<Categor>.from(
            json["categories"].map((x) => Categor.fromJson(x))),
        // swiperList: List<SwiperList>.from(
        //     json["swiperList"].map((x) => SwiperList.fromJson(x))),
        // newsList: List<dynamic>.from(json["newsList"].map((x) => x)),
        // collectApplicatList: List<CollectApplicatList>.from(
        //     json["collectApplicatList"]
        //         .map((x) => CollectApplicatList.fromJson(x))),
        // groupList:
        //     List<Group>.from(json["groupList"].map((x) => groupValues.map[x]!)),
      );

  Map<String, dynamic> toJson() => {
        "categories": List<dynamic>.from(categories.map((x) => x.toJson())),
        // "noticeList": List<dynamic>.from(noticeList.map((x) => x)),
        // "swiperList": List<dynamic>.from(swiperList.map((x) => x.toJson())),
        // "newsList": List<dynamic>.from(newsList.map((x) => x)),
        // "collectApplicatList":
        //     List<dynamic>.from(collectApplicatList.map((x) => x.toJson())),
        // "groupList":
        //     List<dynamic>.from(groupList.map((x) => groupValues.reverse[x])),
      };
}

class Categor {
  String? cuIcon;
  String? group;
  String? name;
  String? path;
  dynamic order;
  bool hidden;
  bool collected;

  Categor({
    required this.cuIcon,
    required this.group,
    required this.name,
    required this.path,
    required this.order,
    required this.hidden,
    required this.collected,
  });

  factory Categor.fromJson(Map<String, dynamic> json) => Categor(
        cuIcon: json["cuIcon"],
        group: json["group"],
        name: json["name"],
        path: json["path"],
        order: json["order"],
        hidden: json["hidden"],
        collected: json["collected"],
      );

  Map<String, dynamic> toJson() => {
        "cuIcon": cuIcon,
        "group": group,
        "name": name,
        "path": path,
        "order": order,
        "hidden": hidden,
        "collected": collected,
      };
}

enum Group { EMPTY, FLUFFY, GROUP, PURPLE, TENTACLED }

final groupValues = EnumValues({
  "经营管理": Group.EMPTY,
  "协同办公": Group.FLUFFY,
  "农业生产": Group.GROUP,
  "生活服务": Group.PURPLE,
  "农业资源": Group.TENTACLED
});

class CollectApplicatList {
  int? collectApplicatId;
  int? staffId;
  Group? groupName;
  String menuName;
  dynamic orderNum;
  int? createBy;
  DateTime? createTime;
  dynamic updateBy;
  dynamic updateTime;
  int? statusCd;
  dynamic remark;
  Categor categories;

  CollectApplicatList({
    required this.collectApplicatId,
    required this.staffId,
    required this.groupName,
    required this.menuName,
    required this.orderNum,
    required this.createBy,
    required this.createTime,
    required this.updateBy,
    required this.updateTime,
    required this.statusCd,
    required this.remark,
    required this.categories,
  });

  factory CollectApplicatList.fromJson(Map<String, dynamic> json) =>
      CollectApplicatList(
        collectApplicatId: json["collectApplicatId"],
        staffId: json["staffId"],
        groupName: groupValues.map[json["groupName"]]!,
        menuName: json["menuName"],
        orderNum: json["orderNum"],
        createBy: json["createBy"],
        createTime: json["createTime"] == null
            ? null
            : DateTime.parse(json["createTime"]),
        updateBy: json["updateBy"],
        updateTime: json["updateTime"],
        statusCd: json["statusCd"],
        remark: json["remark"],
        categories: Categor.fromJson(json["categories"]),
      );

  Map<String, dynamic> toJson() => {
        "collectApplicatId": collectApplicatId,
        "staffId": staffId,
        "groupName": groupValues.reverse[groupName],
        "menuName": menuName,
        "orderNum": orderNum,
        "createBy": createBy,
        "createTime": createTime?.toIso8601String(),
        "updateBy": updateBy,
        "updateTime": updateTime,
        "statusCd": statusCd,
        "remark": remark,
        "categories": categories.toJson(),
      };
}

class SwiperList {
  int appSwiperId;
  String image;
  String title;
  int isShow;
  int createBy;
  DateTime createTime;
  int updateBy;
  DateTime updateTime;
  int statusCd;
  dynamic remark;
  String organizationNo;
  String organizationName;
  int orderNum;
  int type;

  SwiperList({
    required this.appSwiperId,
    required this.image,
    required this.title,
    required this.isShow,
    required this.createBy,
    required this.createTime,
    required this.updateBy,
    required this.updateTime,
    required this.statusCd,
    required this.remark,
    required this.organizationNo,
    required this.organizationName,
    required this.orderNum,
    required this.type,
  });

  factory SwiperList.fromJson(Map<String, dynamic> json) => SwiperList(
        appSwiperId: json["appSwiperId"],
        image: json["image"],
        title: json["title"],
        isShow: json["isShow"],
        createBy: json["createBy"],
        createTime: DateTime.parse(json["createTime"]),
        updateBy: json["updateBy"],
        updateTime: DateTime.parse(json["updateTime"]),
        statusCd: json["statusCd"],
        remark: json["remark"],
        organizationNo: json["organizationNo"],
        organizationName: json["organizationName"],
        orderNum: json["orderNum"],
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "appSwiperId": appSwiperId,
        "image": image,
        "title": title,
        "isShow": isShow,
        "createBy": createBy,
        "createTime": createTime.toIso8601String(),
        "updateBy": updateBy,
        "updateTime": updateTime.toIso8601String(),
        "statusCd": statusCd,
        "remark": remark,
        "organizationNo": organizationNo,
        "organizationName": organizationName,
        "orderNum": orderNum,
        "type": type,
      };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
