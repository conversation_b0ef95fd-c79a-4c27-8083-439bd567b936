/// code : 0
/// data : {"current":0,"hitCount":true,"pages":0,"records":[{"batchCode":"","consignId":0,"consignOrgName":"","consignStatus":0,"consignTime":"","qualifiedSampleQuantity":0,"receiverTestingLabName":"","receiverTime":"","sampleQuantity":0,"unqualifiedSampleQuantity":0}],"searchCount":true,"size":0,"total":0}
/// msg : ""
/// success : true

class ThreeReceivedList {
  ThreeReceivedList({
      num? code, 
      Data? data, 
      String? msg, 
      bool? success,}){
    _code = code;
    _data = data;
    _msg = msg;
    _success = success;
}

  ThreeReceivedList.fromJson(dynamic json) {
    _code = json['code'];
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
    _msg = json['msg'];
    _success = json['success'];
  }
  num? _code;
  Data? _data;
  String? _msg;
  bool? _success;
ThreeReceivedList copyWith({  num? code,
  Data? data,
  String? msg,
  bool? success,
}) => ThreeReceivedList(  code: code ?? _code,
  data: data ?? _data,
  msg: msg ?? _msg,
  success: success ?? _success,
);
  num? get code => _code;
  Data? get data => _data;
  String? get msg => _msg;
  bool? get success => _success;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['code'] = _code;
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    map['msg'] = _msg;
    map['success'] = _success;
    return map;
  }

}

/// current : 0
/// hitCount : true
/// pages : 0
/// records : [{"batchCode":"","consignId":0,"consignOrgName":"","consignStatus":0,"consignTime":"","qualifiedSampleQuantity":0,"receiverTestingLabName":"","receiverTime":"","sampleQuantity":0,"unqualifiedSampleQuantity":0}]
/// searchCount : true
/// size : 0
/// total : 0

class Data {
  Data({
      num? current, 
      bool? hitCount, 
      num? pages, 
      List<Records>? records, 
      bool? searchCount, 
      num? size, 
      num? total,}){
    _current = current;
    _hitCount = hitCount;
    _pages = pages;
    _records = records;
    _searchCount = searchCount;
    _size = size;
    _total = total;
}

  Data.fromJson(dynamic json) {
    _current = json['current'];
    _hitCount = json['hitCount'];
    _pages = json['pages'];
    if (json['records'] != null) {
      _records = [];
      json['records'].forEach((v) {
        _records?.add(Records.fromJson(v));
      });
    }
    _searchCount = json['searchCount'];
    _size = json['size'];
    _total = json['total'];
  }
  num? _current;
  bool? _hitCount;
  num? _pages;
  List<Records>? _records;
  bool? _searchCount;
  num? _size;
  num? _total;
Data copyWith({  num? current,
  bool? hitCount,
  num? pages,
  List<Records>? records,
  bool? searchCount,
  num? size,
  num? total,
}) => Data(  current: current ?? _current,
  hitCount: hitCount ?? _hitCount,
  pages: pages ?? _pages,
  records: records ?? _records,
  searchCount: searchCount ?? _searchCount,
  size: size ?? _size,
  total: total ?? _total,
);
  num? get current => _current;
  bool? get hitCount => _hitCount;
  num? get pages => _pages;
  List<Records>? get records => _records;
  bool? get searchCount => _searchCount;
  num? get size => _size;
  num? get total => _total;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['current'] = _current;
    map['hitCount'] = _hitCount;
    map['pages'] = _pages;
    if (_records != null) {
      map['records'] = _records?.map((v) => v.toJson()).toList();
    }
    map['searchCount'] = _searchCount;
    map['size'] = _size;
    map['total'] = _total;
    return map;
  }

}

/// batchCode : ""
/// consignId : 0
/// consignOrgName : ""
/// consignStatus : 0
/// consignTime : ""
/// qualifiedSampleQuantity : 0
/// receiverTestingLabName : ""
/// receiverTime : ""
/// sampleQuantity : 0
/// unqualifiedSampleQuantity : 0

class Records {
  Records({
      String? batchCode, 
      num? consignId, 
      String? consignOrgName, 
      num? consignStatus, 
      String? consignTime, 
      num? qualifiedSampleQuantity, 
      String? receiverTestingLabName, 
      String? receiverTime, 
      num? sampleQuantity, 
      num? unqualifiedSampleQuantity,}){
    _batchCode = batchCode;
    _consignId = consignId;
    _consignOrgName = consignOrgName;
    _consignStatus = consignStatus;
    _consignTime = consignTime;
    _qualifiedSampleQuantity = qualifiedSampleQuantity;
    _receiverTestingLabName = receiverTestingLabName;
    _receiverTime = receiverTime;
    _sampleQuantity = sampleQuantity;
    _unqualifiedSampleQuantity = unqualifiedSampleQuantity;
}

  Records.fromJson(dynamic json) {
    _batchCode = json['batchCode'];
    _consignId = json['consignId'];
    _consignOrgName = json['consignOrgName'];
    _consignStatus = json['consignStatus'];
    _consignTime = json['consignTime'];
    _qualifiedSampleQuantity = json['qualifiedSampleQuantity'];
    _receiverTestingLabName = json['receiverTestingLabName'];
    _receiverTime = json['receiverTime'];
    _sampleQuantity = json['sampleQuantity'];
    _unqualifiedSampleQuantity = json['unqualifiedSampleQuantity'];
  }
  String? _batchCode;
  num? _consignId;
  String? _consignOrgName;
  num? _consignStatus;
  String? _consignTime;
  num? _qualifiedSampleQuantity;
  String? _receiverTestingLabName;
  String? _receiverTime;
  num? _sampleQuantity;
  num? _unqualifiedSampleQuantity;
Records copyWith({  String? batchCode,
  num? consignId,
  String? consignOrgName,
  num? consignStatus,
  String? consignTime,
  num? qualifiedSampleQuantity,
  String? receiverTestingLabName,
  String? receiverTime,
  num? sampleQuantity,
  num? unqualifiedSampleQuantity,
}) => Records(  batchCode: batchCode ?? _batchCode,
  consignId: consignId ?? _consignId,
  consignOrgName: consignOrgName ?? _consignOrgName,
  consignStatus: consignStatus ?? _consignStatus,
  consignTime: consignTime ?? _consignTime,
  qualifiedSampleQuantity: qualifiedSampleQuantity ?? _qualifiedSampleQuantity,
  receiverTestingLabName: receiverTestingLabName ?? _receiverTestingLabName,
  receiverTime: receiverTime ?? _receiverTime,
  sampleQuantity: sampleQuantity ?? _sampleQuantity,
  unqualifiedSampleQuantity: unqualifiedSampleQuantity ?? _unqualifiedSampleQuantity,
);
  String? get batchCode => _batchCode;
  num? get consignId => _consignId;
  String? get consignOrgName => _consignOrgName;
  num? get consignStatus => _consignStatus;
  String? get consignTime => _consignTime;
  num? get qualifiedSampleQuantity => _qualifiedSampleQuantity;
  String? get receiverTestingLabName => _receiverTestingLabName;
  String? get receiverTime => _receiverTime;
  num? get sampleQuantity => _sampleQuantity;
  num? get unqualifiedSampleQuantity => _unqualifiedSampleQuantity;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['batchCode'] = _batchCode;
    map['consignId'] = _consignId;
    map['consignOrgName'] = _consignOrgName;
    map['consignStatus'] = _consignStatus;
    map['consignTime'] = _consignTime;
    map['qualifiedSampleQuantity'] = _qualifiedSampleQuantity;
    map['receiverTestingLabName'] = _receiverTestingLabName;
    map['receiverTime'] = _receiverTime;
    map['sampleQuantity'] = _sampleQuantity;
    map['unqualifiedSampleQuantity'] = _unqualifiedSampleQuantity;
    return map;
  }

}