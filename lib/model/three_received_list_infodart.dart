/// success : true
/// code : 0
/// msg : "success"
/// data : {"consignId":12,"batchCode":"20250528094259992","sampleQuantity":6,"consignOrgCode":"860101","consignOrgName":"二九〇农场","consignTime":"2025-05-28","consignStaffName":"送样232323","consignPhone":"213667788877","receiverTestingLabCode":"05671","receiverTestingLabName":"实验室","receiverTime":null,"receiverStaffName":"接受","receiverPhone":"232478374832","consignStatus":0,"list":[{"sampleInspectId":114,"batchCode":"20250528094259992","sampleNumber":"SAMPLE-002","sampleType":"surface","landUseType":"0201","sampleWeight":2,"pointCode":"w2g4uHMbWa","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":7,"orgCode":"860101"},{"sampleInspectId":115,"batchCode":"20250528094259992","sampleNumber":"SAMPLE-003","sampleType":"surface","landUseType":"0201","sampleWeight":5,"pointCode":"w2g4uHMbWa","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":7,"orgCode":"860101"},{"sampleInspectId":116,"batchCode":"20250528094259992","sampleNumber":"20250521064654","sampleType":"surface","landUseType":"0101","sampleWeight":5,"pointCode":"tRb0hTgIGX","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":6,"orgCode":"860101"},{"sampleInspectId":117,"batchCode":"20250528094259992","sampleNumber":"2025052985223","sampleType":"section","landUseType":"0101","sampleWeight":123,"pointCode":"tRb0hTgIGX","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":6,"orgCode":"860101"},{"sampleInspectId":118,"batchCode":"20250528094259992","sampleNumber":"SAMPLE-004","sampleType":"2","landUseType":"0103","sampleWeight":221,"pointCode":"mAJEtODcnK","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":1,"orgCode":"860101"},{"sampleInspectId":119,"batchCode":"20250528094259992","sampleNumber":"SAMPLE-002","sampleType":"1","landUseType":"0103","sampleWeight":28,"pointCode":"mAJEtODcnK","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":1,"orgCode":"860101"}]}

class ThreeReceivedListInfodart {
  ThreeReceivedListInfodart({
      bool? success, 
      num? code, 
      String? msg, 
      Data? data,}){
    _success = success;
    _code = code;
    _msg = msg;
    _data = data;
}

  ThreeReceivedListInfodart.fromJson(dynamic json) {
    _success = json['success'];
    _code = json['code'];
    _msg = json['msg'];
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }
  bool? _success;
  num? _code;
  String? _msg;
  Data? _data;
ThreeReceivedListInfodart copyWith({  bool? success,
  num? code,
  String? msg,
  Data? data,
}) => ThreeReceivedListInfodart(  success: success ?? _success,
  code: code ?? _code,
  msg: msg ?? _msg,
  data: data ?? _data,
);
  bool? get success => _success;
  num? get code => _code;
  String? get msg => _msg;
  Data? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    map['code'] = _code;
    map['msg'] = _msg;
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    return map;
  }

}

/// consignId : 12
/// batchCode : "20250528094259992"
/// sampleQuantity : 6
/// consignOrgCode : "860101"
/// consignOrgName : "二九〇农场"
/// consignTime : "2025-05-28"
/// consignStaffName : "送样232323"
/// consignPhone : "213667788877"
/// receiverTestingLabCode : "05671"
/// receiverTestingLabName : "实验室"
/// receiverTime : null
/// receiverStaffName : "接受"
/// receiverPhone : "232478374832"
/// consignStatus : 0
/// list : [{"sampleInspectId":114,"batchCode":"20250528094259992","sampleNumber":"SAMPLE-002","sampleType":"surface","landUseType":"0201","sampleWeight":2,"pointCode":"w2g4uHMbWa","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":7,"orgCode":"860101"},{"sampleInspectId":115,"batchCode":"20250528094259992","sampleNumber":"SAMPLE-003","sampleType":"surface","landUseType":"0201","sampleWeight":5,"pointCode":"w2g4uHMbWa","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":7,"orgCode":"860101"},{"sampleInspectId":116,"batchCode":"20250528094259992","sampleNumber":"20250521064654","sampleType":"surface","landUseType":"0101","sampleWeight":5,"pointCode":"tRb0hTgIGX","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":6,"orgCode":"860101"},{"sampleInspectId":117,"batchCode":"20250528094259992","sampleNumber":"2025052985223","sampleType":"section","landUseType":"0101","sampleWeight":123,"pointCode":"tRb0hTgIGX","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":6,"orgCode":"860101"},{"sampleInspectId":118,"batchCode":"20250528094259992","sampleNumber":"SAMPLE-004","sampleType":"2","landUseType":"0103","sampleWeight":221,"pointCode":"mAJEtODcnK","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":1,"orgCode":"860101"},{"sampleInspectId":119,"batchCode":"20250528094259992","sampleNumber":"SAMPLE-002","sampleType":"1","landUseType":"0103","sampleWeight":28,"pointCode":"mAJEtODcnK","salineAlkaliFlag":1,"adminArea":"860101","taskYear":"2025","taskId":1,"orgCode":"860101"}]

class Data {
  Data({
      num? consignId, 
      String? batchCode, 
      num? sampleQuantity, 
      String? consignOrgCode, 
      String? consignOrgName, 
      String? consignTime, 
      String? consignStaffName, 
      String? consignPhone, 
      String? receiverTestingLabCode, 
      String? receiverTestingLabName, 
      dynamic receiverTime, 
      String? receiverStaffName, 
      String? receiverPhone, 
      num? consignStatus, 
      List<SampleItem>? list,}){
    _consignId = consignId;
    _batchCode = batchCode;
    _sampleQuantity = sampleQuantity;
    _consignOrgCode = consignOrgCode;
    _consignOrgName = consignOrgName;
    _consignTime = consignTime;
    _consignStaffName = consignStaffName;
    _consignPhone = consignPhone;
    _receiverTestingLabCode = receiverTestingLabCode;
    _receiverTestingLabName = receiverTestingLabName;
    _receiverTime = receiverTime;
    _receiverStaffName = receiverStaffName;
    _receiverPhone = receiverPhone;
    _consignStatus = consignStatus;
    _list = list;
}

  Data.fromJson(dynamic json) {
    _consignId = json['consignId'];
    _batchCode = json['batchCode'];
    _sampleQuantity = json['sampleQuantity'];
    _consignOrgCode = json['consignOrgCode'];
    _consignOrgName = json['consignOrgName'];
    _consignTime = json['consignTime'];
    _consignStaffName = json['consignStaffName'];
    _consignPhone = json['consignPhone'];
    _receiverTestingLabCode = json['receiverTestingLabCode'];
    _receiverTestingLabName = json['receiverTestingLabName'];
    _receiverTime = json['receiverTime'];
    _receiverStaffName = json['receiverStaffName'];
    _receiverPhone = json['receiverPhone'];
    _consignStatus = json['consignStatus'];
    if (json['list'] != null) {
      _list = [];
      json['list'].forEach((v) {
        _list?.add(SampleItem.fromJson(v));
      });
    }
  }
  num? _consignId;
  String? _batchCode;
  num? _sampleQuantity;
  String? _consignOrgCode;
  String? _consignOrgName;
  String? _consignTime;
  String? _consignStaffName;
  String? _consignPhone;
  String? _receiverTestingLabCode;
  String? _receiverTestingLabName;
  dynamic _receiverTime;
  String? _receiverStaffName;
  String? _receiverPhone;
  num? _consignStatus;
  List<SampleItem>? _list;
Data copyWith({  num? consignId,
  String? batchCode,
  num? sampleQuantity,
  String? consignOrgCode,
  String? consignOrgName,
  String? consignTime,
  String? consignStaffName,
  String? consignPhone,
  String? receiverTestingLabCode,
  String? receiverTestingLabName,
  dynamic receiverTime,
  String? receiverStaffName,
  String? receiverPhone,
  num? consignStatus,
  List<SampleItem>? list,
}) => Data(  consignId: consignId ?? _consignId,
  batchCode: batchCode ?? _batchCode,
  sampleQuantity: sampleQuantity ?? _sampleQuantity,
  consignOrgCode: consignOrgCode ?? _consignOrgCode,
  consignOrgName: consignOrgName ?? _consignOrgName,
  consignTime: consignTime ?? _consignTime,
  consignStaffName: consignStaffName ?? _consignStaffName,
  consignPhone: consignPhone ?? _consignPhone,
  receiverTestingLabCode: receiverTestingLabCode ?? _receiverTestingLabCode,
  receiverTestingLabName: receiverTestingLabName ?? _receiverTestingLabName,
  receiverTime: receiverTime ?? _receiverTime,
  receiverStaffName: receiverStaffName ?? _receiverStaffName,
  receiverPhone: receiverPhone ?? _receiverPhone,
  consignStatus: consignStatus ?? _consignStatus,
  list: list ?? _list,
);
  num? get consignId => _consignId;
  String? get batchCode => _batchCode;
  num? get sampleQuantity => _sampleQuantity;
  String? get consignOrgCode => _consignOrgCode;
  String? get consignOrgName => _consignOrgName;
  String? get consignTime => _consignTime;
  String? get consignStaffName => _consignStaffName;
  String? get consignPhone => _consignPhone;
  String? get receiverTestingLabCode => _receiverTestingLabCode;
  String? get receiverTestingLabName => _receiverTestingLabName;
  dynamic get receiverTime => _receiverTime;
  String? get receiverStaffName => _receiverStaffName;
  String? get receiverPhone => _receiverPhone;
  num? get consignStatus => _consignStatus;
  List<SampleItem>? get list => _list;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['consignId'] = _consignId;
    map['batchCode'] = _batchCode;
    map['sampleQuantity'] = _sampleQuantity;
    map['consignOrgCode'] = _consignOrgCode;
    map['consignOrgName'] = _consignOrgName;
    map['consignTime'] = _consignTime;
    map['consignStaffName'] = _consignStaffName;
    map['consignPhone'] = _consignPhone;
    map['receiverTestingLabCode'] = _receiverTestingLabCode;
    map['receiverTestingLabName'] = _receiverTestingLabName;
    map['receiverTime'] = _receiverTime;
    map['receiverStaffName'] = _receiverStaffName;
    map['receiverPhone'] = _receiverPhone;
    map['consignStatus'] = _consignStatus;
    if (_list != null) {
      map['list'] = _list?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// sampleInspectId : 114
/// batchCode : "20250528094259992"
/// sampleNumber : "SAMPLE-002"
/// sampleType : "surface"
/// landUseType : "0201"
/// sampleWeight : 2
/// pointCode : "w2g4uHMbWa"
/// salineAlkaliFlag : 1
/// adminArea : "860101"
/// taskYear : "2025"
/// taskId : 7
/// orgCode : "860101"

class SampleItem {
  SampleItem({
      num? sampleInspectId, 
      String? batchCode, 
      String? sampleNumber, 
      String? sampleType, 
      String? landUseType, 
      num? sampleWeight, 
      String? pointCode, 
      num? salineAlkaliFlag, 
      String? adminArea, 
      String? taskYear, 
      num? taskId, 
      String? orgCode,}){
    _sampleInspectId = sampleInspectId;
    _batchCode = batchCode;
    _sampleNumber = sampleNumber;
    _sampleType = sampleType;
    _landUseType = landUseType;
    _sampleWeight = sampleWeight;
    _pointCode = pointCode;
    _salineAlkaliFlag = salineAlkaliFlag;
    _adminArea = adminArea;
    _taskYear = taskYear;
    _taskId = taskId;
    _orgCode = orgCode;
}

  SampleItem.fromJson(dynamic json) {
    _sampleInspectId = json['sampleInspectId'];
    _batchCode = json['batchCode'];
    _sampleNumber = json['sampleNumber'];
    _sampleType = json['sampleType'];
    _landUseType = json['landUseType'];
    _sampleWeight = json['sampleWeight'];
    _pointCode = json['pointCode'];
    _salineAlkaliFlag = json['salineAlkaliFlag'];
    _adminArea = json['adminArea'];
    _taskYear = json['taskYear'];
    _taskId = json['taskId'];
    _orgCode = json['orgCode'];
  }
  num? _sampleInspectId;
  String? _batchCode;
  String? _sampleNumber;
  String? _sampleType;
  String? _landUseType;
  num? _sampleWeight;
  String? _pointCode;
  num? _salineAlkaliFlag;
  String? _adminArea;
  String? _taskYear;
  num? _taskId;
  String? _orgCode;
SampleItem copyWith({  num? sampleInspectId,
  String? batchCode,
  String? sampleNumber,
  String? sampleType,
  String? landUseType,
  num? sampleWeight,
  String? pointCode,
  num? salineAlkaliFlag,
  String? adminArea,
  String? taskYear,
  num? taskId,
  String? orgCode,
}) => SampleItem(  sampleInspectId: sampleInspectId ?? _sampleInspectId,
  batchCode: batchCode ?? _batchCode,
  sampleNumber: sampleNumber ?? _sampleNumber,
  sampleType: sampleType ?? _sampleType,
  landUseType: landUseType ?? _landUseType,
  sampleWeight: sampleWeight ?? _sampleWeight,
  pointCode: pointCode ?? _pointCode,
  salineAlkaliFlag: salineAlkaliFlag ?? _salineAlkaliFlag,
  adminArea: adminArea ?? _adminArea,
  taskYear: taskYear ?? _taskYear,
  taskId: taskId ?? _taskId,
  orgCode: orgCode ?? _orgCode,
);
  num? get sampleInspectId => _sampleInspectId;
  String? get batchCode => _batchCode;
  String? get sampleNumber => _sampleNumber;
  String? get sampleType => _sampleType;
  String? get landUseType => _landUseType;
  num? get sampleWeight => _sampleWeight;
  String? get pointCode => _pointCode;
  num? get salineAlkaliFlag => _salineAlkaliFlag;
  String? get adminArea => _adminArea;
  String? get taskYear => _taskYear;
  num? get taskId => _taskId;
  String? get orgCode => _orgCode;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['sampleInspectId'] = _sampleInspectId;
    map['batchCode'] = _batchCode;
    map['sampleNumber'] = _sampleNumber;
    map['sampleType'] = _sampleType;
    map['landUseType'] = _landUseType;
    map['sampleWeight'] = _sampleWeight;
    map['pointCode'] = _pointCode;
    map['salineAlkaliFlag'] = _salineAlkaliFlag;
    map['adminArea'] = _adminArea;
    map['taskYear'] = _taskYear;
    map['taskId'] = _taskId;
    map['orgCode'] = _orgCode;
    return map;
  }

}