class DiaApplyFiledModel {
  bool? success;
  num? code;
  String? msg;
  Data? data;

  DiaApplyFiledModel({this.success, this.code, this.msg, this.data});

  DiaApplyFiledModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['code'] = this.code;
    data['msg'] = this.msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<Records>? records;
  num? total;
  num? size;
  num? current;
  List<dynamic>? orders;
  bool? optimizeCountSql;
  bool? hitCount;
  dynamic countId;
  dynamic maxLimit;
  bool? searchCount;
  num? pages;

  Data(
      {this.records,
      this.total,
      this.size,
      this.current,
      this.orders,
      this.optimizeCountSql,
      this.hitCount,
      this.countId,
      this.maxLimit,
      this.searchCount,
      this.pages});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['records'] != null) {
      records = <Records>[];
      json['records'].forEach((v) {
        records!.add(new Records.fromJson(v));
      });
    }
    total = json['total'];
    size = json['size'];
    current = json['current'];
    if (json['orders'] != null) {
      orders = List<Null>.from(json['orders']);
    }
    optimizeCountSql = json['optimizeCountSql'];
    hitCount = json['hitCount'];
    countId = json['countId'];
    maxLimit = json['maxLimit'];
    searchCount = json['searchCount'];
    pages = json['pages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.records != null) {
      data['records'] = this.records!.map((v) => v.toJson()).toList();
    }
    data['total'] = this.total;
    data['size'] = this.size;
    data['current'] = this.current;
    if (this.orders != null) {
      data['orders'] = this.orders!.map((v) => v.toJson()).toList();
    }
    data['optimizeCountSql'] = this.optimizeCountSql;
    data['hitCount'] = this.hitCount;
    data['countId'] = this.countId;
    data['maxLimit'] = this.maxLimit;
    data['searchCount'] = this.searchCount;
    data['pages'] = this.pages;
    return data;
  }
}

class Records {
  num? yearNo;
  String? auditAFlagName;
  String? organizationNo;
  num? farmerType;
  String? name;
  String? idNumber;
  num? sexNo;
  num? age;
  String? idAddress;
  num? householderRelation;
  num? bankName;
  num? bankcardTypeNo;
  String? bankAccount;
  dynamic validateCount;
  String? issuingAuthority;
  String? validDate;
  String? phone;
  String? birthday;
  num? farmerIdentityNo;
  dynamic precinctName;
  String? organizationName;
  num? rationPlanId;
  num? createBy;
  String? createTime;
  num? updateBy;
  String? updateTime;
  dynamic statusCd;
  dynamic creatorName;
  dynamic updaterName;
  num? farmerId;
  dynamic precinctNo;
  num? sourceNo;
  dynamic rationPrecinctNo;
  String? rationPrecinctName;
  num? isCollect;
  dynamic params;
  num? auditAFlag;
  num? auditAId;
  String? auditAName;
  String? auditATime;
  num? auditBFlag;
  num? auditBId;
  String? auditBName;
  String? auditBTime;
  dynamic auditCFlag;
  dynamic auditCId;
  dynamic auditCName;
  dynamic auditCTime;
  num? approvalStatusNo;
  dynamic area;
  num? scaleArea;
  num? rationArea;
  dynamic updateFlag;
  String? approvalRemark;
  dynamic auditDFlag;
  dynamic auditDId;
  dynamic auditDName;
  dynamic auditDTime;
  dynamic auditEFlag;
  dynamic auditEId;
  dynamic auditEName;
  dynamic auditETime;
  dynamic auditFFlag;
  dynamic auditFId;
  dynamic auditFName;
  dynamic auditFTime;
  dynamic auditGFlag;
  dynamic auditGId;
  dynamic auditGName;
  dynamic auditGTime;
  dynamic auditHFlag;
  dynamic auditHId;
  dynamic auditHName;
  dynamic auditHTime;
  num? currentAuditRoleId;
  String? currentAuditRoleName;
  num? auditLevel;
  dynamic archiveAge;
  dynamic ********************;
  num? signType;
  String? signTime;
  String? fddContractNo;
  num? serviceType;
  dynamic startSignTime;
  dynamic endSignTime;
  num? ifSignCommitment;
  num? signVerifyMode;
  dynamic longitude;
  dynamic latitude;
  dynamic currentRequestIp;
  dynamic ipAddressName;
  dynamic ipReturnJson;
  dynamic appLongitude;
  dynamic appLatitude;
  dynamic idCrad;
  dynamic faceSuccessTime;
  dynamic appLocationOnOffGlobalStatus;
  dynamic remarkParam;
  dynamic appVersionCode;

  Records(
      {this.yearNo,
      this.auditAFlagName,
      this.organizationNo,
      this.farmerType,
      this.name,
      this.idNumber,
      this.sexNo,
      this.age,
      this.idAddress,
      this.householderRelation,
      this.bankName,
      this.bankcardTypeNo,
      this.bankAccount,
      this.validateCount,
      this.issuingAuthority,
      this.validDate,
      this.phone,
      this.birthday,
      this.farmerIdentityNo,
      this.precinctName,
      this.organizationName,
      this.rationPlanId,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.statusCd,
      this.creatorName,
      this.updaterName,
      this.farmerId,
      this.precinctNo,
      this.sourceNo,
      this.rationPrecinctNo,
      this.rationPrecinctName,
      this.isCollect,
      this.params,
      this.auditAFlag,
      this.auditAId,
      this.auditAName,
      this.auditATime,
      this.auditBFlag,
      this.auditBId,
      this.auditBName,
      this.auditBTime,
      this.auditCFlag,
      this.auditCId,
      this.auditCName,
      this.auditCTime,
      this.approvalStatusNo,
      this.area,
      this.scaleArea,
      this.rationArea,
      this.updateFlag,
      this.approvalRemark,
      this.auditDFlag,
      this.auditDId,
      this.auditDName,
      this.auditDTime,
      this.auditEFlag,
      this.auditEId,
      this.auditEName,
      this.auditETime,
      this.auditFFlag,
      this.auditFId,
      this.auditFName,
      this.auditFTime,
      this.auditGFlag,
      this.auditGId,
      this.auditGName,
      this.auditGTime,
      this.auditHFlag,
      this.auditHId,
      this.auditHName,
      this.auditHTime,
      this.currentAuditRoleId,
      this.currentAuditRoleName,
      this.auditLevel,
      this.archiveAge,
      this.********************,
      this.signType,
      this.signTime,
      this.fddContractNo,
      this.serviceType,
      this.startSignTime,
      this.endSignTime,
      this.ifSignCommitment,
      this.signVerifyMode,
      this.longitude,
      this.latitude,
      this.currentRequestIp,
      this.ipAddressName,
      this.ipReturnJson,
      this.appLongitude,
      this.appLatitude,
      this.idCrad,
      this.faceSuccessTime,
      this.appLocationOnOffGlobalStatus,
      this.remarkParam,
      this.appVersionCode});

  Records.fromJson(Map<String, dynamic> json) {
    yearNo = json['yearNo'];
    auditAFlagName = json['auditAFlagName'];
    organizationNo = json['organizationNo'];
    farmerType = json['farmerType'];
    name = json['name'];
    idNumber = json['idNumber'];
    sexNo = json['sexNo'];
    age = json['age'];
    idAddress = json['idAddress'];
    householderRelation = json['householderRelation'];
    bankName = json['bankName'];
    bankcardTypeNo = json['bankcardTypeNo'];
    bankAccount = json['bankAccount'];
    validateCount = json['validateCount'];
    issuingAuthority = json['issuingAuthority'];
    validDate = json['validDate'];
    phone = json['phone'];
    birthday = json['birthday'];
    farmerIdentityNo = json['farmerIdentityNo'];
    precinctName = json['precinctName'];
    organizationName = json['organizationName'];
    rationPlanId = json['rationPlanId'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    updateBy = json['updateBy'];
    updateTime = json['updateTime'];
    statusCd = json['statusCd'];
    creatorName = json['creatorName'];
    updaterName = json['updaterName'];
    farmerId = json['farmerId'];
    precinctNo = json['precinctNo'];
    sourceNo = json['sourceNo'];
    rationPrecinctNo = json['rationPrecinctNo'];
    rationPrecinctName = json['rationPrecinctName'];
    isCollect = json['isCollect'];
    params = json['params'];
    auditAFlag = json['auditAFlag'];
    auditAId = json['auditAId'];
    auditAName = json['auditAName'];
    auditATime = json['auditATime'];
    auditBFlag = json['auditBFlag'];
    auditBId = json['auditBId'];
    auditBName = json['auditBName'];
    auditBTime = json['auditBTime'];
    auditCFlag = json['auditCFlag'];
    auditCId = json['auditCId'];
    auditCName = json['auditCName'];
    auditCTime = json['auditCTime'];
    approvalStatusNo = json['approvalStatusNo'];
    area = json['area'];
    scaleArea = json['scaleArea'];
    rationArea = json['rationArea'];
    updateFlag = json['updateFlag'];
    approvalRemark = json['approvalRemark'];
    auditDFlag = json['auditDFlag'];
    auditDId = json['auditDId'];
    auditDName = json['auditDName'];
    auditDTime = json['auditDTime'];
    auditEFlag = json['auditEFlag'];
    auditEId = json['auditEId'];
    auditEName = json['auditEName'];
    auditETime = json['auditETime'];
    auditFFlag = json['auditFFlag'];
    auditFId = json['auditFId'];
    auditFName = json['auditFName'];
    auditFTime = json['auditFTime'];
    auditGFlag = json['auditGFlag'];
    auditGId = json['auditGId'];
    auditGName = json['auditGName'];
    auditGTime = json['auditGTime'];
    auditHFlag = json['auditHFlag'];
    auditHId = json['auditHId'];
    auditHName = json['auditHName'];
    auditHTime = json['auditHTime'];
    currentAuditRoleId = json['currentAuditRoleId'];
    currentAuditRoleName = json['currentAuditRoleName'];
    auditLevel = json['auditLevel'];
    archiveAge = json['archiveAge'];
    ******************** = json['********************'];
    signType = json['signType'];
    signTime = json['signTime'];
    fddContractNo = json['fddContractNo'];
    serviceType = json['serviceType'];
    startSignTime = json['startSignTime'];
    endSignTime = json['endSignTime'];
    ifSignCommitment = json['ifSignCommitment'];
    signVerifyMode = json['signVerifyMode'];
    longitude = json['longitude'];
    latitude = json['latitude'];
    currentRequestIp = json['currentRequestIp'];
    ipAddressName = json['ipAddressName'];
    ipReturnJson = json['ipReturnJson'];
    appLongitude = json['appLongitude'];
    appLatitude = json['appLatitude'];
    idCrad = json['idCrad'];
    faceSuccessTime = json['faceSuccessTime'];
    appLocationOnOffGlobalStatus = json['appLocationOnOffGlobalStatus'];
    remarkParam = json['remarkParam'];
    appVersionCode = json['appVersionCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['yearNo'] = this.yearNo;
    data['auditAFlagName'] = this.auditAFlagName;
    data['organizationNo'] = this.organizationNo;
    data['farmerType'] = this.farmerType;
    data['name'] = this.name;
    data['idNumber'] = this.idNumber;
    data['sexNo'] = this.sexNo;
    data['age'] = this.age;
    data['idAddress'] = this.idAddress;
    data['householderRelation'] = this.householderRelation;
    data['bankName'] = this.bankName;
    data['bankcardTypeNo'] = this.bankcardTypeNo;
    data['bankAccount'] = this.bankAccount;
    data['validateCount'] = this.validateCount;
    data['issuingAuthority'] = this.issuingAuthority;
    data['validDate'] = this.validDate;
    data['phone'] = this.phone;
    data['birthday'] = this.birthday;
    data['farmerIdentityNo'] = this.farmerIdentityNo;
    data['precinctName'] = this.precinctName;
    data['organizationName'] = this.organizationName;
    data['rationPlanId'] = this.rationPlanId;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['updateBy'] = this.updateBy;
    data['updateTime'] = this.updateTime;
    data['statusCd'] = this.statusCd;
    data['creatorName'] = this.creatorName;
    data['updaterName'] = this.updaterName;
    data['farmerId'] = this.farmerId;
    data['precinctNo'] = this.precinctNo;
    data['sourceNo'] = this.sourceNo;
    data['rationPrecinctNo'] = this.rationPrecinctNo;
    data['rationPrecinctName'] = this.rationPrecinctName;
    data['isCollect'] = this.isCollect;
    data['params'] = this.params;
    data['auditAFlag'] = this.auditAFlag;
    data['auditAId'] = this.auditAId;
    data['auditAName'] = this.auditAName;
    data['auditATime'] = this.auditATime;
    data['auditBFlag'] = this.auditBFlag;
    data['auditBId'] = this.auditBId;
    data['auditBName'] = this.auditBName;
    data['auditBTime'] = this.auditBTime;
    data['auditCFlag'] = this.auditCFlag;
    data['auditCId'] = this.auditCId;
    data['auditCName'] = this.auditCName;
    data['auditCTime'] = this.auditCTime;
    data['approvalStatusNo'] = this.approvalStatusNo;
    data['area'] = this.area;
    data['scaleArea'] = this.scaleArea;
    data['rationArea'] = this.rationArea;
    data['updateFlag'] = this.updateFlag;
    data['approvalRemark'] = this.approvalRemark;
    data['auditDFlag'] = this.auditDFlag;
    data['auditDId'] = this.auditDId;
    data['auditDName'] = this.auditDName;
    data['auditDTime'] = this.auditDTime;
    data['auditEFlag'] = this.auditEFlag;
    data['auditEId'] = this.auditEId;
    data['auditEName'] = this.auditEName;
    data['auditETime'] = this.auditETime;
    data['auditFFlag'] = this.auditFFlag;
    data['auditFId'] = this.auditFId;
    data['auditFName'] = this.auditFName;
    data['auditFTime'] = this.auditFTime;
    data['auditGFlag'] = this.auditGFlag;
    data['auditGId'] = this.auditGId;
    data['auditGName'] = this.auditGName;
    data['auditGTime'] = this.auditGTime;
    data['auditHFlag'] = this.auditHFlag;
    data['auditHId'] = this.auditHId;
    data['auditHName'] = this.auditHName;
    data['auditHTime'] = this.auditHTime;
    data['currentAuditRoleId'] = this.currentAuditRoleId;
    data['currentAuditRoleName'] = this.currentAuditRoleName;
    data['auditLevel'] = this.auditLevel;
    data['archiveAge'] = this.archiveAge;
    data['********************'] = this.********************;
    data['signType'] = this.signType;
    data['signTime'] = this.signTime;
    data['fddContractNo'] = this.fddContractNo;
    data['serviceType'] = this.serviceType;
    data['startSignTime'] = this.startSignTime;
    data['endSignTime'] = this.endSignTime;
    data['ifSignCommitment'] = this.ifSignCommitment;
    data['signVerifyMode'] = this.signVerifyMode;
    data['longitude'] = this.longitude;
    data['latitude'] = this.latitude;
    data['currentRequestIp'] = this.currentRequestIp;
    data['ipAddressName'] = this.ipAddressName;
    data['ipReturnJson'] = this.ipReturnJson;
    data['appLongitude'] = this.appLongitude;
    data['appLatitude'] = this.appLatitude;
    data['idCrad'] = this.idCrad;
    data['faceSuccessTime'] = this.faceSuccessTime;
    data['appLocationOnOffGlobalStatus'] = this.appLocationOnOffGlobalStatus;
    data['remarkParam'] = this.remarkParam;
    data['appVersionCode'] = this.appVersionCode;
    return data;
  }
}
