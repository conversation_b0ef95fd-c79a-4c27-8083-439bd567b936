class ExamineModel {
  bool? success;
  int? code;
  String? msg;
  ExamineModelData? data;

  ExamineModel({
    this.success,
    this.code,
    this.msg,
    this.data,
  });

  ExamineModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? ExamineModelData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    data['msg'] = msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class ExamineModelData {
  List<LandContractRecord>? records;
  int? total;
  int? size;
  int? current;
  List<dynamic>? orders;
  bool? optimizeCountSql;
  bool? hitCount;
  dynamic countId;
  dynamic maxLimit;
  bool? searchCount;
  int? pages;

  ExamineModelData({
    this.records,
    this.total,
    this.size,
    this.current,
    this.orders,
    this.optimizeCountSql,
    this.hitCount,
    this.countId,
    this.maxLimit,
    this.searchCount,
    this.pages,
  });

  ExamineModelData.fromJson(Map<String, dynamic> json) {
    if (json['records'] != null) {
      records = <LandContractRecord>[];
      json['records'].forEach((v) {
        records!.add(LandContractRecord.fromJson(v));
      });
    }
    total = json['total'];
    size = json['size'];
    current = json['current'];
    orders = json['orders'];
    optimizeCountSql = json['optimizeCountSql'];
    hitCount = json['hitCount'];
    countId = json['countId'];
    maxLimit = json['maxLimit'];
    searchCount = json['searchCount'];
    pages = json['pages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (records != null) {
      data['records'] = records!.map((v) => v.toJson()).toList();
    }
    data['total'] = total;
    data['size'] = size;
    data['current'] = current;
    data['orders'] = orders;
    data['optimizeCountSql'] = optimizeCountSql;
    data['hitCount'] = hitCount;
    data['countId'] = countId;
    data['maxLimit'] = maxLimit;
    data['searchCount'] = searchCount;
    data['pages'] = pages;
    return data;
  }
}

class LandContractRecord {
  dynamic auditGFlag;
  dynamic auditGName;
  dynamic auditGTime;
  dynamic auditHId;
  dynamic auditHFlag;
  dynamic auditHName;
  dynamic auditHTime;
  int? currentAuditRoleId;
  String? currentAuditRoleName;
  int? auditLevel;
  dynamic standardPrice;
  int? approvalStatusNo;
  String? approvalRemark;
  dynamic auditOpinion;
  dynamic approverId;
  dynamic approverName;
  dynamic approverDate;
  int? auditAFlag;
  int? auditAId;
  String? auditAName;
  String? auditATime;
  int? auditBFlag;
  dynamic auditBId;
  dynamic auditBName;
  dynamic auditBTime;
  dynamic auditCFlag;
  dynamic auditCId;
  dynamic auditCName;
  dynamic auditCTime;
  dynamic auditDId;
  dynamic auditDFlag;
  dynamic auditDName;
  dynamic auditDTime;
  dynamic auditEId;
  dynamic auditEFlag;
  dynamic auditEName;
  dynamic auditETime;
  dynamic auditFId;
  dynamic auditFFlag;
  dynamic auditFName;
  dynamic auditFTime;
  dynamic auditGId;
  int? landContractChargePlanId;
  int? batchId;
  int? yearNo;
  String? organizationNo;
  String? organizationName;
  int? planTypeNo;
  int? farmerId;
  int? contractSignType;
  String? farmerName;
  String? farmerIdNumber;
  String? loanData;
  int? plowlandNo;
  double? area;
  double? totalFee;
  double? paddyArea;
  double? paddyTotalFee;
  double? rentInKindTotal;
  double? uplandArea;
  double? uplandTotalFee;
  int? createBy;
  String? createTime;
  int? updateBy;
  String? updateTime;
  dynamic statusCd;
  dynamic remark;
  dynamic params;
  dynamic partnerName;
  dynamic partnerId;
  dynamic auditPassImageUrl;
  dynamic auditRejectImageUrl;
  dynamic bond;

  LandContractRecord({
    this.auditGFlag,
    this.auditGName,
    this.auditGTime,
    this.auditHId,
    this.auditHFlag,
    this.auditHName,
    this.auditHTime,
    this.currentAuditRoleId,
    this.currentAuditRoleName,
    this.auditLevel,
    this.standardPrice,
    this.approvalStatusNo,
    this.approvalRemark,
    this.auditOpinion,
    this.approverId,
    this.approverName,
    this.approverDate,
    this.auditAFlag,
    this.auditAId,
    this.auditAName,
    this.auditATime,
    this.auditBFlag,
    this.auditBId,
    this.auditBName,
    this.auditBTime,
    this.auditCFlag,
    this.auditCId,
    this.auditCName,
    this.auditCTime,
    this.auditDId,
    this.auditDFlag,
    this.auditDName,
    this.auditDTime,
    this.auditEId,
    this.auditEFlag,
    this.auditEName,
    this.auditETime,
    this.auditFId,
    this.auditFFlag,
    this.auditFName,
    this.auditFTime,
    this.auditGId,
    this.landContractChargePlanId,
    this.batchId,
    this.yearNo,
    this.organizationNo,
    this.organizationName,
    this.planTypeNo,
    this.farmerId,
    this.contractSignType,
    this.farmerName,
    this.farmerIdNumber,
    this.loanData,
    this.plowlandNo,
    this.area,
    this.totalFee,
    this.paddyArea,
    this.paddyTotalFee,
    this.rentInKindTotal,
    this.uplandArea,
    this.uplandTotalFee,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.statusCd,
    this.remark,
    this.params,
    this.partnerName,
    this.partnerId,
    this.auditPassImageUrl,
    this.auditRejectImageUrl,
    this.bond,
  });

  LandContractRecord.fromJson(Map<String, dynamic> json) {
    auditGFlag = json['auditGFlag'];
    auditGName = json['auditGName'];
    auditGTime = json['auditGTime'];
    auditHId = json['auditHId'];
    auditHFlag = json['auditHFlag'];
    auditHName = json['auditHName'];
    auditHTime = json['auditHTime'];
    currentAuditRoleId = json['currentAuditRoleId'];
    currentAuditRoleName = json['currentAuditRoleName'];
    auditLevel = json['auditLevel'];
    standardPrice = json['standardPrice'];
    approvalStatusNo = json['approvalStatusNo'];
    approvalRemark = json['approvalRemark'];
    auditOpinion = json['auditOpinion'];
    approverId = json['approverId'];
    approverName = json['approverName'];
    approverDate = json['approverDate'];
    auditAFlag = json['auditAFlag'];
    auditAId = json['auditAId'];
    auditAName = json['auditAName'];
    auditATime = json['auditATime'];
    auditBFlag = json['auditBFlag'];
    auditBId = json['auditBId'];
    auditBName = json['auditBName'];
    auditBTime = json['auditBTime'];
    auditCFlag = json['auditCFlag'];
    auditCId = json['auditCId'];
    auditCName = json['auditCName'];
    auditCTime = json['auditCTime'];
    auditDId = json['auditDId'];
    auditDFlag = json['auditDFlag'];
    auditDName = json['auditDName'];
    auditDTime = json['auditDTime'];
    auditEId = json['auditEId'];
    auditEFlag = json['auditEFlag'];
    auditEName = json['auditEName'];
    auditETime = json['auditETime'];
    auditFId = json['auditFId'];
    auditFFlag = json['auditFFlag'];
    auditFName = json['auditFName'];
    auditFTime = json['auditFTime'];
    auditGId = json['auditGId'];
    landContractChargePlanId = json['landContractChargePlanId'];
    batchId = json['batchId'];
    yearNo = json['yearNo'];
    organizationNo = json['organizationNo'];
    organizationName = json['organizationName'];
    planTypeNo = json['planTypeNo'];
    farmerId = json['farmerId'];
    contractSignType = json['contractSignType'];
    farmerName = json['farmerName'];
    farmerIdNumber = json['farmerIdNumber'];
    loanData = json['loanData'];
    plowlandNo = json['plowlandNo'];
    area = json['area'];
    totalFee = json['totalFee'];
    paddyArea = json['paddyArea'];
    paddyTotalFee = json['paddyTotalFee'];
    rentInKindTotal = json['rentInKindTotal'];
    uplandArea = json['uplandArea'];
    uplandTotalFee = json['uplandTotalFee'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    updateBy = json['updateBy'];
    updateTime = json['updateTime'];
    statusCd = json['statusCd'];
    remark = json['remark'];
    params = json['params'];
    partnerName = json['partnerName'];
    partnerId = json['partnerId'];
    auditPassImageUrl = json['auditPassImageUrl'];
    auditRejectImageUrl = json['auditRejectImageUrl'];
    bond = json['bond'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['auditGFlag'] = auditGFlag;
    data['auditGName'] = auditGName;
    data['auditGTime'] = auditGTime;
    data['auditHId'] = auditHId;
    data['auditHFlag'] = auditHFlag;
    data['auditHName'] = auditHName;
    data['auditHTime'] = auditHTime;
    data['currentAuditRoleId'] = currentAuditRoleId;
    data['currentAuditRoleName'] = currentAuditRoleName;
    data['auditLevel'] = auditLevel;
    data['standardPrice'] = standardPrice;
    data['approvalStatusNo'] = approvalStatusNo;
    data['approvalRemark'] = approvalRemark;
    data['auditOpinion'] = auditOpinion;
    data['approverId'] = approverId;
    data['approverName'] = approverName;
    data['approverDate'] = approverDate;
    data['auditAFlag'] = auditAFlag;
    data['auditAId'] = auditAId;
    data['auditAName'] = auditAName;
    data['auditATime'] = auditATime;
    data['auditBFlag'] = auditBFlag;
    data['auditBId'] = auditBId;
    data['auditBName'] = auditBName;
    data['auditBTime'] = auditBTime;
    data['auditCFlag'] = auditCFlag;
    data['auditCId'] = auditCId;
    data['auditCName'] = auditCName;
    data['auditCTime'] = auditCTime;
    data['auditDId'] = auditDId;
    data['auditDFlag'] = auditDFlag;
    data['auditDName'] = auditDName;
    data['auditDTime'] = auditDTime;
    data['auditEId'] = auditEId;
    data['auditEFlag'] = auditEFlag;
    data['auditEName'] = auditEName;
    data['auditETime'] = auditETime;
    data['auditFId'] = auditFId;
    data['auditFFlag'] = auditFFlag;
    data['auditFName'] = auditFName;
    data['auditFTime'] = auditFTime;
    data['auditGId'] = auditGId;
    data['landContractChargePlanId'] = landContractChargePlanId;
    data['batchId'] = batchId;
    data['yearNo'] = yearNo;
    data['organizationNo'] = organizationNo;
    data['organizationName'] = organizationName;
    data['planTypeNo'] = planTypeNo;
    data['farmerId'] = farmerId;
    data['contractSignType'] = contractSignType;
    data['farmerName'] = farmerName;
    data['farmerIdNumber'] = farmerIdNumber;
    data['loanData'] = loanData;
    data['plowlandNo'] = plowlandNo;
    data['area'] = area;
    data['totalFee'] = totalFee;
    data['paddyArea'] = paddyArea;
    data['paddyTotalFee'] = paddyTotalFee;
    data['rentInKindTotal'] = rentInKindTotal;
    data['uplandArea'] = uplandArea;
    data['uplandTotalFee'] = uplandTotalFee;
    data['createBy'] = createBy;
    data['createTime'] = createTime;
    data['updateBy'] = updateBy;
    data['updateTime'] = updateTime;
    data['statusCd'] = statusCd;
    data['remark'] = remark;
    data['params'] = params;
    data['partnerName'] = partnerName;
    data['partnerId'] = partnerId;
    data['auditPassImageUrl'] = auditPassImageUrl;
    data['auditRejectImageUrl'] = auditRejectImageUrl;
    data['bond'] = bond;
    return data;
  }
}