import 'dart:convert';

class UserInfo {
  bool? success;
  int? code;
  String? msg;
  Data? data;

  UserInfo({this.success, this.code, this.msg, this.data});

  UserInfo.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  get staffId => null;

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    data['msg'] = msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? superiorId;
  int? id;
  int? staffId;
  String? staffName;
  String? loginName;
  String? token;
  String? telephone;
  String? orgId;
  String? orgName;
  String? orgFullName;
  String? superiorOrgId;
  String? superiorOrgName;
  List<String>? rolesId;
  String? roleCode;
  String? roleName;
  String? loginTime;
  int? systemId;
  String? systemName;
  String? cityId;
  String? cityCode;
  String? cityName;
  String? idCard;
  String? openId;
  String? staffHasOrgName;
  String? staffHasOrgCode;
  String? ifRegistered;
  PluginInfo? pluginInfo;
  OrgInfo? selectOrgInfo;

  Data(
      {this.superiorId,
      this.id,
      this.staffId,
      this.staffName,
      this.loginName,
      this.token,
      this.telephone,
      this.orgId,
      this.orgName,
      this.orgFullName,
      this.superiorOrgId,
      this.superiorOrgName,
      this.rolesId,
      this.roleCode,
      this.roleName,
      this.loginTime,
      this.systemId,
      this.systemName,
      this.cityId,
      this.cityCode,
      this.cityName,
      this.idCard,
      this.openId,
      this.staffHasOrgName,
      this.staffHasOrgCode,
      this.pluginInfo,
      this.selectOrgInfo,
      this.ifRegistered});

  Data.fromJson(Map<String, dynamic> json) {
    staffHasOrgName = json['staffHasOrgName'];
    staffHasOrgCode = json['staffHasOrgCode'];
    superiorId = json['superiorId'];
    id = json['id'];
    staffId = json['staffId'];
    staffName = json['staffName'];
    loginName = json['loginName'];
    token = json['token'];
    telephone = json['telephone'];
    orgId = json['orgId'];
    orgName = json['orgName'];
    orgFullName = json['orgFullName'];
    superiorOrgId = json['superiorOrgId'];
    superiorOrgName = json['superiorOrgName'];
    if (json['rolesId'] == null) {
      rolesId = [];
    } else {
      rolesId = json['rolesId'].cast<String>();
    }

    roleCode = json['roleCode'];
    roleName = json['roleName'];
    loginTime = json['loginTime'];
    systemId = json['systemId'];
    systemName = json['systemName'];
    cityId = json['cityId'];
    cityCode = json['cityCode'];
    cityName = json['cityName'];
    idCard = json['idCard'];
    openId = json['openId'];
    pluginInfo = json['pluginInfo'] != null
        ? PluginInfo.fromJson(json['pluginInfo'])
        : null;
    selectOrgInfo = OrgInfo();
    ifRegistered = json["ifRegistered"];
    // selectOrgInfo = pluginInfo?.orgInfos![0];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['superiorId'] = superiorId;
    data['id'] = id;
    data['staffId'] = staffId;
    data['staffName'] = staffName;
    data['loginName'] = loginName;
    data['token'] = token;
    data['telephone'] = telephone;
    data['orgId'] = orgId;
    data['orgName'] = orgName;
    data['orgFullName'] = orgFullName;
    data['superiorOrgId'] = superiorOrgId;
    data['superiorOrgName'] = superiorOrgName;
    data['rolesId'] = rolesId;
    data['roleCode'] = roleCode;
    data['roleName'] = roleName;
    data['loginTime'] = loginTime;
    data['systemId'] = systemId;
    data['systemName'] = systemName;
    data['cityId'] = cityId;
    data['cityCode'] = cityCode;
    data['cityName'] = cityName;
    data['idCard'] = idCard;
    data['openId'] = openId;
    data['ifRegistered'] = ifRegistered;
    if (pluginInfo != null) {
      data['pluginInfo'] = pluginInfo!.toJson();
    }
    return data;
  }
}

class PluginInfo {
  List<OrgInfo>? orgInfos;

  PluginInfo({this.orgInfos});

  PluginInfo.fromJson(Map<String, dynamic> json) {
    var obj = json['orgInfos'];
    if (obj is String) {
      List<dynamic> list = jsonDecode(obj);
      orgInfos = <OrgInfo>[];
      for (var v in list) {
        orgInfos!.add(OrgInfo.fromJson(v));
      }
    } else {
      List<dynamic> map = obj;
      orgInfos = <OrgInfo>[];
      for (var v in map) {
        if (v is OrgInfo) {
          orgInfos!.add(v);
        } else {
          orgInfos!.add(OrgInfo.fromJson(v));
        }
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['orgInfos'] = orgInfos?.map((orgInfo) {
      return orgInfo.toJson();
    }).toList();
    return data;
  }
}

class OrgInfo {
  String? orgName;
  String? orgCode;
  String? orgLevel;
  String? orgId;
  String? parentId;

  OrgInfo({
    this.orgName,
    this.orgCode,
    this.orgLevel,
    this.orgId,
    this.parentId,
  });

  OrgInfo.fromJson(Map<String, dynamic> json) {
    orgName = json['orgName'];
    orgCode = json['orgCode'];
    orgLevel = json['orgLevel'];
    orgId = json['orgId'];
    parentId = json['parentId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['orgName'] = orgName;
    data['orgCode'] = orgCode;
    data['orgLevel'] = orgLevel;
    data['orgId'] = orgId;
    data['parentId'] = parentId;
    return data;
  }
}

class UserInfoSingleton {
  static UserInfoSingleton? _instance;

  UserInfoSingleton._internal() {
    _instance = this;
  }
  factory UserInfoSingleton() => _instance ?? UserInfoSingleton._internal();

  //属性
  UserInfo userInfo = UserInfo();
}
