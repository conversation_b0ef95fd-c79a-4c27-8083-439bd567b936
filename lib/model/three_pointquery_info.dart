/// success : true
/// code : 0
/// msg : "success"
/// data : {"taskId":1,"taskCode":"FWqGL5iVtG","pointCode":"mAJEtODcnK","samplingStatus":"1","orgName":"<PERSON>","orgCode":"860101","longitude":"124.197847999999993","latitude":"124.197847999999993","landUseType":"0103","salineAlkaliFlag":1,"samplingType":"1","pointType":"1","samplingTaskImage":[{"taskImageId":123,"imageUrl":"http://************:30111/bdh-dev-new/bdh-soil-census/268e45945110420196cc3583e7b7c64e1747879270220.png","imageType":"N","imageTime":1745911644000,"imageAngle":"30","taskId":1},{"taskImageId":112,"imageUrl":"http://************:30111/bdh-dev-new/bdh-soil-census/93617b609d794aff9fd01819f0eeecdd1747879270696.png","imageType":"E","imageTime":1745911644000,"imageAngle":"29","taskId":1}],"samplingTaskSample":[{"taskSampleId":22,"sampleCode":"SAMPLE-004","sampleType":"2","sampleWeight":221,"taskId":1},{"taskSampleId":1,"sampleCode":"SAMPLE-002","sampleType":"1","sampleWeight":28,"taskId":1}],"surveyLongitude":158.58841,"surveyLatitude":58.21324,"correct":null}

class ThreePointqueryInfo {
  ThreePointqueryInfo({
      bool? success, 
      num? code, 
      String? msg, 
      Data? data,}){
    _success = success;
    _code = code;
    _msg = msg;
    _data = data;
}

  ThreePointqueryInfo.fromJson(dynamic json) {
    _success = json['success'];
    _code = json['code'];
    _msg = json['msg'];
    _data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }
  bool? _success;
  num? _code;
  String? _msg;
  Data? _data;
ThreePointqueryInfo copyWith({  bool? success,
  num? code,
  String? msg,
  Data? data,
}) => ThreePointqueryInfo(  success: success ?? _success,
  code: code ?? _code,
  msg: msg ?? _msg,
  data: data ?? _data,
);
  bool? get success => _success;
  num? get code => _code;
  String? get msg => _msg;
  Data? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    map['code'] = _code;
    map['msg'] = _msg;
    if (_data != null) {
      map['data'] = _data?.toJson();
    }
    return map;
  }

}

/// taskId : 1
/// taskCode : "FWqGL5iVtG"
/// pointCode : "mAJEtODcnK"
/// samplingStatus : "1"
/// orgName : "Philip Garza"
/// orgCode : "860101"
/// longitude : "124.197847999999993"
/// latitude : "124.197847999999993"
/// landUseType : "0103"
/// salineAlkaliFlag : 1
/// samplingType : "1"
/// pointType : "1"
/// samplingTaskImage : [{"taskImageId":123,"imageUrl":"http://************:30111/bdh-dev-new/bdh-soil-census/268e45945110420196cc3583e7b7c64e1747879270220.png","imageType":"N","imageTime":1745911644000,"imageAngle":"30","taskId":1},{"taskImageId":112,"imageUrl":"http://************:30111/bdh-dev-new/bdh-soil-census/93617b609d794aff9fd01819f0eeecdd1747879270696.png","imageType":"E","imageTime":1745911644000,"imageAngle":"29","taskId":1}]
/// samplingTaskSample : [{"taskSampleId":22,"sampleCode":"SAMPLE-004","sampleType":"2","sampleWeight":221,"taskId":1},{"taskSampleId":1,"sampleCode":"SAMPLE-002","sampleType":"1","sampleWeight":28,"taskId":1}]
/// surveyLongitude : 158.58841
/// surveyLatitude : 58.21324
/// correct : null

class Data {
  Data({
      num? taskId, 
      String? taskCode, 
      String? pointCode, 
      String? samplingStatus, 
      String? orgName, 
      String? orgCode, 
      String? longitude, 
      String? latitude, 
      String? landUseType, 
      num? salineAlkaliFlag, 
      String? samplingType, 
      String? pointType, 
      List<SamplingTaskImage>? samplingTaskImage, 
      List<SamplingTaskSample>? samplingTaskSample, 
      num? surveyLongitude, 
      num? surveyLatitude, 
      dynamic correct,
      num? samplingPointRange,}){
    _taskId = taskId;
    _taskCode = taskCode;
    _pointCode = pointCode;
    _samplingStatus = samplingStatus;
    _orgName = orgName;
    _orgCode = orgCode;
    _longitude = longitude;
    _latitude = latitude;
    _landUseType = landUseType;
    _salineAlkaliFlag = salineAlkaliFlag;
    _samplingType = samplingType;
    _pointType = pointType;
    _samplingTaskImage = samplingTaskImage;
    _samplingTaskSample = samplingTaskSample;
    _surveyLongitude = surveyLongitude;
    _surveyLatitude = surveyLatitude;
    _correct = correct;
    _samplingPointRange = samplingPointRange;
}

  Data.fromJson(dynamic json) {
    _taskId = json['taskId'];
    _taskCode = json['taskCode'];
    _pointCode = json['pointCode'];
    _samplingStatus = json['samplingStatus'];
    _orgName = json['orgName'];
    _orgCode = json['orgCode'];
    _longitude = json['longitude'];
    _latitude = json['latitude'];
    _landUseType = json['landUseType'];
    _salineAlkaliFlag = json['salineAlkaliFlag'];
    _samplingType = json['samplingType'];
    _pointType = json['pointType'];
    if (json['samplingTaskImage'] != null) {
      _samplingTaskImage = [];
      json['samplingTaskImage'].forEach((v) {
        _samplingTaskImage?.add(SamplingTaskImage.fromJson(v));
      });
    }
    if (json['samplingTaskSample'] != null) {
      _samplingTaskSample = [];
      json['samplingTaskSample'].forEach((v) {
        _samplingTaskSample?.add(SamplingTaskSample.fromJson(v));
      });
    }
    _surveyLongitude = json['surveyLongitude'];
    _surveyLatitude = json['surveyLatitude'];
    _correct = json['correct'];
    _samplingPointRange = json['samplingPointRange'];
  }
  num? _taskId;
  String? _taskCode;
  String? _pointCode;
  String? _samplingStatus;
  String? _orgName;
  String? _orgCode;
  String? _longitude;
  String? _latitude;
  String? _landUseType;
  num? _salineAlkaliFlag;
  String? _samplingType;
  String? _pointType;
  List<SamplingTaskImage>? _samplingTaskImage;
  List<SamplingTaskSample>? _samplingTaskSample;
  num? _surveyLongitude;
  num? _surveyLatitude;
  dynamic _correct;
  num? _samplingPointRange;
Data copyWith({  num? taskId,
  String? taskCode,
  String? pointCode,
  String? samplingStatus,
  String? orgName,
  String? orgCode,
  String? longitude,
  String? latitude,
  String? landUseType,
  num? salineAlkaliFlag,
  String? samplingType,
  String? pointType,
  List<SamplingTaskImage>? samplingTaskImage,
  List<SamplingTaskSample>? samplingTaskSample,
  num? surveyLongitude,
  num? surveyLatitude,
  dynamic correct,
  num? samplingPointRange,
}) => Data(  taskId: taskId ?? _taskId,
  taskCode: taskCode ?? _taskCode,
  pointCode: pointCode ?? _pointCode,
  samplingStatus: samplingStatus ?? _samplingStatus,
  orgName: orgName ?? _orgName,
  orgCode: orgCode ?? _orgCode,
  longitude: longitude ?? _longitude,
  latitude: latitude ?? _latitude,
  landUseType: landUseType ?? _landUseType,
  salineAlkaliFlag: salineAlkaliFlag ?? _salineAlkaliFlag,
  samplingType: samplingType ?? _samplingType,
  pointType: pointType ?? _pointType,
  samplingTaskImage: samplingTaskImage ?? _samplingTaskImage,
  samplingTaskSample: samplingTaskSample ?? _samplingTaskSample,
  surveyLongitude: surveyLongitude ?? _surveyLongitude,
  surveyLatitude: surveyLatitude ?? _surveyLatitude,
  correct: correct ?? _correct,
  samplingPointRange: samplingPointRange ?? _samplingPointRange,
);
  num? get taskId => _taskId;
  String? get taskCode => _taskCode;
  String? get pointCode => _pointCode;
  String? get samplingStatus => _samplingStatus;
  String? get orgName => _orgName;
  String? get orgCode => _orgCode;
  String? get longitude => _longitude;
  String? get latitude => _latitude;
  String? get landUseType => _landUseType;
  num? get salineAlkaliFlag => _salineAlkaliFlag;
  String? get samplingType => _samplingType;
  String? get pointType => _pointType;
  List<SamplingTaskImage>? get samplingTaskImage => _samplingTaskImage;
  List<SamplingTaskSample>? get samplingTaskSample => _samplingTaskSample;
  num? get surveyLongitude => _surveyLongitude;
  num? get surveyLatitude => _surveyLatitude;
  dynamic get correct => _correct;
  num? get samplingPointRange => _samplingPointRange;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['taskId'] = _taskId;
    map['taskCode'] = _taskCode;
    map['pointCode'] = _pointCode;
    map['samplingStatus'] = _samplingStatus;
    map['orgName'] = _orgName;
    map['orgCode'] = _orgCode;
    map['longitude'] = _longitude;
    map['latitude'] = _latitude;
    map['landUseType'] = _landUseType;
    map['salineAlkaliFlag'] = _salineAlkaliFlag;
    map['samplingType'] = _samplingType;
    map['pointType'] = _pointType;
    if (_samplingTaskImage != null) {
      map['samplingTaskImage'] = _samplingTaskImage?.map((v) => v.toJson()).toList();
    }
    if (_samplingTaskSample != null) {
      map['samplingTaskSample'] = _samplingTaskSample?.map((v) => v.toJson()).toList();
    }
    map['surveyLongitude'] = _surveyLongitude;
    map['surveyLatitude'] = _surveyLatitude;
    map['correct'] = _correct;
    map['samplingPointRange'] = _samplingPointRange;
    return map;
  }

}

/// taskSampleId : 22
/// sampleCode : "SAMPLE-004"
/// sampleType : "2"
/// sampleWeight : 221
/// taskId : 1

class SamplingTaskSample {
  SamplingTaskSample({
      num? taskSampleId, 
      String? sampleCode, 
      String? sampleType, 
      num? sampleWeight, 
      num? taskId,}){
    _taskSampleId = taskSampleId;
    _sampleCode = sampleCode;
    _sampleType = sampleType;
    _sampleWeight = sampleWeight;
    _taskId = taskId;
}

  SamplingTaskSample.fromJson(dynamic json) {
    _taskSampleId = json['taskSampleId'];
    _sampleCode = json['sampleCode'];
    _sampleType = json['sampleType'];
    _sampleWeight = json['sampleWeight'];
    _taskId = json['taskId'];
  }
  num? _taskSampleId;
  String? _sampleCode;
  String? _sampleType;
  num? _sampleWeight;
  num? _taskId;
SamplingTaskSample copyWith({  num? taskSampleId,
  String? sampleCode,
  String? sampleType,
  num? sampleWeight,
  num? taskId,
}) => SamplingTaskSample(  taskSampleId: taskSampleId ?? _taskSampleId,
  sampleCode: sampleCode ?? _sampleCode,
  sampleType: sampleType ?? _sampleType,
  sampleWeight: sampleWeight ?? _sampleWeight,
  taskId: taskId ?? _taskId,
);
  num? get taskSampleId => _taskSampleId;
  String? get sampleCode => _sampleCode;
  String? get sampleType => _sampleType;
  num? get sampleWeight => _sampleWeight;
  num? get taskId => _taskId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['taskSampleId'] = _taskSampleId;
    map['sampleCode'] = _sampleCode;
    map['sampleType'] = _sampleType;
    map['sampleWeight'] = _sampleWeight;
    map['taskId'] = _taskId;
    return map;
  }

}

/// taskImageId : 123
/// imageUrl : "http://************:30111/bdh-dev-new/bdh-soil-census/268e45945110420196cc3583e7b7c64e1747879270220.png"
/// imageType : "N"
/// imageTime : 1745911644000
/// imageAngle : "30"
/// taskId : 1

class SamplingTaskImage {
  SamplingTaskImage({
      num? taskImageId, 
      String? imageUrl, 
      String? imageType, 
      num? imageTime, 
      String? imageAngle, 
      num? taskId,}){
    _taskImageId = taskImageId;
    _imageUrl = imageUrl;
    _imageType = imageType;
    _imageTime = imageTime;
    _imageAngle = imageAngle;
    _taskId = taskId;
}

  SamplingTaskImage.fromJson(dynamic json) {
    _taskImageId = json['taskImageId'];
    _imageUrl = json['imageUrl'];
    _imageType = json['imageType'];
    _imageTime = json['imageTime'];
    _imageAngle = json['imageAngle'];
    _taskId = json['taskId'];
  }
  num? _taskImageId;
  String? _imageUrl;
  String? _imageType;
  num? _imageTime;
  String? _imageAngle;
  num? _taskId;
SamplingTaskImage copyWith({  num? taskImageId,
  String? imageUrl,
  String? imageType,
  num? imageTime,
  String? imageAngle,
  num? taskId,
}) => SamplingTaskImage(  taskImageId: taskImageId ?? _taskImageId,
  imageUrl: imageUrl ?? _imageUrl,
  imageType: imageType ?? _imageType,
  imageTime: imageTime ?? _imageTime,
  imageAngle: imageAngle ?? _imageAngle,
  taskId: taskId ?? _taskId,
);
  num? get taskImageId => _taskImageId;
  String? get imageUrl => _imageUrl;
  String? get imageType => _imageType;
  num? get imageTime => _imageTime;
  String? get imageAngle => _imageAngle;
  num? get taskId => _taskId;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['taskImageId'] = _taskImageId;
    map['imageUrl'] = _imageUrl;
    map['imageType'] = _imageType;
    map['imageTime'] = _imageTime;
    map['imageAngle'] = _imageAngle;
    map['taskId'] = _taskId;
    return map;
  }

}