enum EnvType {
  //默认
  custom,
  bdh,
  daXing,
  xin<PERSON>iang,
  hua<PERSON><PERSON>
}

class UrlConfig {
  //软件环境
  final EnvType envType;

  ///权限系统
  final String ssoApi;

  final String commonApi;
  //我的田 于生俊 2025-06-11
  final String? fieldApi;

  ///隐私协议
  final String baseApi;

  ///微前端
  final String microfront;

  ///消息中台
  final String mesApi;

  ///鉴种模块api
  final String jzApi;

  ///h5
  final String h5;

  ///法大大
  final String authApi;

  ///大兴安岭土地承包
  final String dahingGanLingApi;

  ///水费管理
  final String waterMangeApi;

  ///android包id
  final String? androidApkId;

  ///chat
  final String chatApi;

  ///农贷助手
  final String financeApi;

  ///施肥建议
  final String? fertilizApi;

  ///农业贷款审批
  final String? loanApi;

  final String? licenseKeyX5;

  final String? samicConfirmApi;
  final String? stageApi;
  // 我的田
  final String? plotApi;

  // 高标准农田
  final String? highApi;

  // 三方 API
  final String? threePartyApi;

  // 土壤测评
  final String? soilTestingApi;
  //成本效益分析
  final String? costanalysisApi;

  final String? finaApi;
  //专家巡田
  final String? zjxtApi;
  //百科
  final String? encyApi;
  //农机管家
  final String? agricultureApi;
  //天气
  final String? weatherApi;
  final bool? isDebug;

  //三方普查
  final String? threeCensusesApi;

  //业务消息
  final String? busiApi;
  //专家巡田
  final String? expotrApi;
  //农情
  final String? farmfellApi;

  //星级排行
  final String? appApi;

  // 叶龄
  final String leafAgeApi;

  const UrlConfig(
      {this.envType = EnvType.custom,
      this.appApi,
      required this.commonApi,
      required this.ssoApi,
      required this.baseApi,
      required this.microfront,
      required this.mesApi,
      required this.jzApi,
      required this.h5,
      required this.authApi,
      required this.dahingGanLingApi,
      required this.waterMangeApi,
      required this.chatApi,
      required this.financeApi,
      this.fertilizApi,
      this.licenseKeyX5,
      this.samicConfirmApi,
      this.stageApi,
      this.plotApi,
      this.threePartyApi,
      this.soilTestingApi,
      this.highApi,
      this.androidApkId,
      this.finaApi,
      this.costanalysisApi,
      this.busiApi,
      this.expotrApi,
      this.farmfellApi,
      this.fieldApi,
      this.loanApi,
      this.zjxtApi,
      this.encyApi,
      this.agricultureApi,
      this.isDebug,
      this.threeCensusesApi,
      this.weatherApi,
      required this.leafAgeApi});
}
