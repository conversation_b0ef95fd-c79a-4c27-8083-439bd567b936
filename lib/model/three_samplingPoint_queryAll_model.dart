/// success : true
/// code : 0
/// msg : "success"
/// data : [{"taskId":99,"taskCode":"test1120250618171524460","pointCode":"test11","samplingStatus":"1","orgName":"七星农场","orgCode":"860303","longitude":132.615678,"latitude":47.212887,"surveyLongitude":132.618378,"surveyLatitude":47.215587,"correct":null,"farmAuditStatus":"1"},{"taskId":98,"taskCode":"test1020250618171524460","pointCode":"test10","samplingStatus":"1","orgName":"七星农场","orgCode":"860303","longitude":132.624678,"latitude":47.237895,"surveyLongitude":132.622978,"surveyLatitude":47.236195,"correct":null,"farmAuditStatus":"1"},{"taskId":97,"taskCode":"test1220250618171524459","pointCode":"test12","samplingStatus":"0","orgName":"七星农场","orgCode":"860303","longitude":132.678912,"latitude":47.224581,"surveyLongitude":132.674312,"surveyLatitude":47.219981,"correct":null,"farmAuditStatus":"2"}]

class ThreeSamplingPointQueryAllModel {
  ThreeSamplingPointQueryAllModel({
      bool? success, 
      num? code, 
      String? msg, 
      List<Data>? data,}){
    _success = success;
    _code = code;
    _msg = msg;
    _data = data;
}

  ThreeSamplingPointQueryAllModel.fromJson(dynamic json) {
    _success = json['success'];
    _code = json['code'];
    _msg = json['msg'];
    if (json['data'] != null) {
      _data = [];
      json['data'].forEach((v) {
        _data?.add(Data.fromJson(v));
      });
    }
  }
  bool? _success;
  num? _code;
  String? _msg;
  List<Data>? _data;
ThreeSamplingPointQueryAllModel copyWith({  bool? success,
  num? code,
  String? msg,
  List<Data>? data,
}) => ThreeSamplingPointQueryAllModel(  success: success ?? _success,
  code: code ?? _code,
  msg: msg ?? _msg,
  data: data ?? _data,
);
  bool? get success => _success;
  num? get code => _code;
  String? get msg => _msg;
  List<Data>? get data => _data;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['success'] = _success;
    map['code'] = _code;
    map['msg'] = _msg;
    if (_data != null) {
      map['data'] = _data?.map((v) => v.toJson()).toList();
    }
    return map;
  }

}

/// taskId : 99
/// taskCode : "test1120250618171524460"
/// pointCode : "test11"
/// samplingStatus : "1"
/// orgName : "七星农场"
/// orgCode : "860303"
/// longitude : 132.615678
/// latitude : 47.212887
/// surveyLongitude : 132.618378
/// surveyLatitude : 47.215587
/// correct : null
/// farmAuditStatus : "1"

class Data {
  Data({
      num? taskId, 
      String? taskCode, 
      String? pointCode, 
      String? samplingStatus, 
      String? orgName, 
      String? orgCode, 
      num? longitude, 
      num? latitude, 
      num? surveyLongitude, 
      num? surveyLatitude, 
      dynamic correct,
      num? samplingPointRange,
      String? farmAuditStatus,}){
    _taskId = taskId;
    _taskCode = taskCode;
    _pointCode = pointCode;
    _samplingStatus = samplingStatus;
    _orgName = orgName;
    _orgCode = orgCode;
    _longitude = longitude;
    _latitude = latitude;
    _surveyLongitude = surveyLongitude;
    _surveyLatitude = surveyLatitude;
    _correct = correct;
    _samplingPointRange = samplingPointRange;
    _farmAuditStatus = farmAuditStatus;
}

  Data.fromJson(dynamic json) {
    _taskId = json['taskId'];
    _taskCode = json['taskCode'];
    _pointCode = json['pointCode'];
    _samplingStatus = json['samplingStatus'];
    _orgName = json['orgName'];
    _orgCode = json['orgCode'];
    _longitude = json['longitude'];
    _latitude = json['latitude'];
    _surveyLongitude = json['surveyLongitude'];
    _surveyLatitude = json['surveyLatitude'];
    _correct = json['correct'];
    _samplingPointRange = json['samplingPointRange'];
    _farmAuditStatus = json['farmAuditStatus'];
  }
  num? _taskId;
  String? _taskCode;
  String? _pointCode;
  String? _samplingStatus;
  String? _orgName;
  String? _orgCode;
  num? _longitude;
  num? _latitude;
  num? _surveyLongitude;
  num? _surveyLatitude;
  dynamic _correct;
  num? _samplingPointRange;
  String? _farmAuditStatus;
Data copyWith({  num? taskId,
  String? taskCode,
  String? pointCode,
  String? samplingStatus,
  String? orgName,
  String? orgCode,
  num? longitude,
  num? latitude,
  num? surveyLongitude,
  num? surveyLatitude,
  dynamic correct,
  num? samplingPointRange,
  String? farmAuditStatus,
}) => Data(  taskId: taskId ?? _taskId,
  taskCode: taskCode ?? _taskCode,
  pointCode: pointCode ?? _pointCode,
  samplingStatus: samplingStatus ?? _samplingStatus,
  orgName: orgName ?? _orgName,
  orgCode: orgCode ?? _orgCode,
  longitude: longitude ?? _longitude,
  latitude: latitude ?? _latitude,
  surveyLongitude: surveyLongitude ?? _surveyLongitude,
  surveyLatitude: surveyLatitude ?? _surveyLatitude,
  correct: correct ?? _correct,
  samplingPointRange: samplingPointRange ?? _samplingPointRange,
  farmAuditStatus: farmAuditStatus ?? _farmAuditStatus,
);
  num? get taskId => _taskId;
  String? get taskCode => _taskCode;
  String? get pointCode => _pointCode;
  String? get samplingStatus => _samplingStatus;
  String? get orgName => _orgName;
  String? get orgCode => _orgCode;
  num? get longitude => _longitude;
  num? get latitude => _latitude;
  num? get surveyLongitude => _surveyLongitude;
  num? get surveyLatitude => _surveyLatitude;
  dynamic get correct => _correct;
  num? get samplingPointRange => _samplingPointRange;
  String? get farmAuditStatus => _farmAuditStatus;

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{};
    map['taskId'] = _taskId;
    map['taskCode'] = _taskCode;
    map['pointCode'] = _pointCode;
    map['samplingStatus'] = _samplingStatus;
    map['orgName'] = _orgName;
    map['orgCode'] = _orgCode;
    map['longitude'] = _longitude;
    map['latitude'] = _latitude;
    map['surveyLongitude'] = _surveyLongitude;
    map['surveyLatitude'] = _surveyLatitude;
    map['correct'] = _correct;
    map['samplingPointRange'] = _samplingPointRange;
    map['farmAuditStatus'] = _farmAuditStatus;
    return map;
  }

}