import 'package:bdh_smart_agric_app/pages/message_bdh_digital/provider/message_bdh_digital_provider.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/examinePro.dart';
import 'package:bdh_smart_agric_app/viewmodel/font_scale_model.dart';
import 'package:bdh_smart_agric_app/viewmodel/locale_model.dart';
import 'package:bdh_smart_agric_app/viewmodel/theme_model.dart';
import 'package:bdh_smart_agric_app/viewmodel/user_model.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import '../pages/product/rank/rank_bdh_digital_provider.dart';

List<SingleChildWidget> providers = [
  ...independentServices,
  ...dependentServices
];

List<SingleChildWidget> independentServices = [
  ChangeNotifierProvider<ExaminePro>(create: (_) => ExaminePro()),
  ChangeNotifierProvider<ThemeModel>(
    create: (context) => ThemeModel(),
  ),
  ChangeNotifierProvider<LocaleModel>(create: (context) => LocaleModel()),
  ChangeNotifierProvider<UserModel>(create: (context) => UserModel()),
  ChangeNotifierProvider<FontScaleModel>(create: (context) => FontScaleModel()),
  ChangeNotifierProvider<MessageBdhDigitalProvider>(
    create: (context) => MessageBdhDigitalProvider()
      ..initData()
      ..getAllMessageData()
      ..getUnReadMessageData(),
  ),
  ChangeNotifierProvider<RankBdhDigitalProvider>(
      create: (context) => RankBdhDigitalProvider()),
];

List<SingleChildWidget> dependentServices = [];
