import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/bdh_login_multi_account_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/model/ped_recommend_model.dart';
import 'package:bdh_smart_agric_app/model/recommend_model.dart';
import 'package:bdh_smart_agric_app/model/rice_user_home_result_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/collect/my_collect.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/index/draw_food_stuff_price.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/manage/price_manage.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/mypage/my_page.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/myrelease/my_reslease.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/person/person_price.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/qualification/my_qualification.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/release_manage/release_manage_page.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/requirementrelease/buy_release.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/requirementrelease/qualification.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/requirementrelease/sale_release.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/transaction/my_transaction.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/transaction/transaction.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/transaction/transaction_manage.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/transaction/transaction_report.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/user_certification/real_name_certification_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/live_detail.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/live_search.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/ditail_page/ad_ditail.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/ditail_page/photo_article_ditail.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/ditail_page/video_play_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/leave_message/leave_message_ditail.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/leave_message/leave_message_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/leave_message/leave_message_page.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/model/search_content_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/search_new_page.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/premium_classroom.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/topic/video_second_topic.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/video_search.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/forget_pwd_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/login_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/login_choose_account_bdh_digital.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/login_pincode_input_bdh_digital.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/login_verify_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/login/forget_password_page.dart';
import 'package:bdh_smart_agric_app/pages/login/login_choose_account.dart';
import 'package:bdh_smart_agric_app/pages/login/login_page.dart';
import 'package:bdh_smart_agric_app/pages/login/login_pincode_input.dart';
import 'package:bdh_smart_agric_app/pages/login/login_telephone_page.dart';
import 'package:bdh_smart_agric_app/pages/login/login_verify_page.dart';
import 'package:bdh_smart_agric_app/pages/login/modify_password_page.dart';
import 'package:bdh_smart_agric_app/pages/login/modify_telephone_page.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/register_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/login/register_page.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/news_message_home.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/agricultural_condition.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalrecords/agricultural_records.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/pages/message_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/agricultural_manager_view.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/chat.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/charge_info.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/charge_info_photo.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/dia_apply/dia_application.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/dia_apply/dia_apply_detail.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/dia_apply_success.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/dia_authent/dia_authentication.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/examine.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/menu.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/insure_main_page.dart';
import 'package:bdh_smart_agric_app/pages/product/loanapply/audit_release_page.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/my_field_page.dart';
import 'package:bdh_smart_agric_app/pages/product/landcontract/bdh_land_contract_page.dart';
import 'package:bdh_smart_agric_app/pages/product/loanapply/loan_apply_page.dart';
import 'package:bdh_smart_agric_app/pages/product/onlinepay/online_pay.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_page1.dart';
import 'package:bdh_smart_agric_app/pages/product/patrol/patrol_main_page.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_list_page.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_main_page.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_news_list_page.dart';
import 'package:bdh_smart_agric_app/pages/product/threecensuses/three_index_page.dart';
import 'package:bdh_smart_agric_app/pages/product/weather/more_weather.dart';
import 'package:bdh_smart_agric_app/pages/project/dxal/banklist/bank_list_info.dart';
import 'package:bdh_smart_agric_app/pages/project/dxal/landcontract/land_contract_page.dart';
import 'package:bdh_smart_agric_app/pages/project/dxal/login/dahing_forget_password_page.dart';
import 'package:bdh_smart_agric_app/pages/project/dxal/login/dahing_login_page.dart';
import 'package:bdh_smart_agric_app/pages/project/dxal/login/dahing_register_page.dart';
import 'package:bdh_smart_agric_app/pages/project/dxal/onlinepay/online_pay.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/land_contract_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/my_water_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_farmer_list.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/pos_farm_list_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_recharge_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/water_containing_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/water_dispatch_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/water_distribution_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/water_manage_page.dart';
import 'package:bdh_smart_agric_app/pages/splash.dart';
import 'package:bdh_smart_agric_app/pages/tabmain/tab_main.dart';
import 'package:bdh_smart_agric_app/pages/project/dxal/banklist/daxing_bank_list_info.dart';
import 'package:bdh_smart_agric_app/pages/testimonialsTorrent/ped_detail_page.dart';
import 'package:bdh_smart_agric_app/pages/testimonialsTorrent/testimonials_torrent_page.dart';
import 'package:bdh_smart_agric_app/pages/testimonialsTorrent/tt_details_page.dart';
import 'package:bdh_smart_agric_app/pages/user/setting/setting_bdh_digital.dart/modify_telephone_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/user/setting/setting_bdh_digital.dart/setting_page_bdh_digital.dart';
import 'package:bdh_smart_agric_app/pages/user/setting/setting_bdh_digital.dart/user_info_bdh_digital.dart';
import 'package:bdh_smart_agric_app/pages/user/setting/setting_page.dart';
import 'package:bdh_smart_agric_app/pages/user/user_info_edit.dart';
import 'package:bdh_smart_agric_app/pages/user/we_chat_auth/telephone_login.dart';
import 'package:bdh_smart_agric_app/pages/user/we_chat_auth/um_push.dart';
import 'package:bdh_smart_agric_app/pages/user/works_star/works_stars_page.dart';
import 'package:bdh_smart_agric_app/pages/user/works_star/works_stars_video.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/live_web_page.dart';
import 'package:bdh_smart_agric_app/pages/worktable/widgets/scan_login/sacn_page_bdh_digital.dart';
import 'package:bdh_smart_agric_app/pages/worktable/widgets/scan_login/scan_login_confirm_page.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import '../pages/product/cal/cal_home_page.dart';
import 'package:bdh_smart_agric_app/pages/product/constanalysis/addAccount.dart';
import 'package:bdh_smart_agric_app/pages/product/centerInfo/releaseAudit/releaseAudit.dart';
import '../pages/project/xjscjsbt/watermanage/water_settle_page.dart';
import 'package:bdh_smart_agric_app/pages/product/constanalysis/costTabbar.dart';
import '../pages/product/centerInfo/releaseAudit/proofSettlement/proofSettlement.dart';
import '../pages/product/centerInfo/releaseAudit/proofSettlement/supplementInfo.dart';
import 'package:bdh_smart_agric_app/pages/product/centerInfo/releaseAudit/unlockReview/unlockReview.dart';
import 'package:bdh_smart_agric_app/pages/product/index/cost_analysis_info.dart';
import 'package:bdh_smart_agric_app/pages/product/constanalysis/detailAccount.dart';

class RouteName {
  //业务路由
  static const String splash = "splash";
  static const String login = "login";
  static const String loginVerify = "loginVerify";
  static const String forgetPassword = "forgetPassword";
  static const String register = "register";
  static const String privicy = "privicy";
  static const String tabMain = "tabMain";
  static const String setting = "setting";
  static const String changePwd = "changePwd";
  static const String changePhone = "changePhone";
  static const String MyFieldPage = "MyFieldPage";
  static const String service = "service";
  static const String telephoneLogin = 'telephoneLogin';
  static const String loginChooseAccount = 'loginChooseAccount';

  //首页
  static const String leaveMessagePage = "leaveMessagePage";
  static const String newsMessageHome = "newsMessageHome";
  static const String searchNewPage = "searchNewPage";
  static const String leaveMessageDitail = "leaveMessageDitail";
  static const String photoArticleDitail = "photoArticleDitail";
  static const String videoPlayView = "videoPlayView";
  static const String premiumClassroom = "premiumClassroom";
  static const String adDitail = "adDitail";

  //视频
  static const String videoSearchPage = "videoSearchPage";
  static const String videoSecondTopicPage = "videoSecondTopicPage";

  //直播
  static const String liveSearchPage = "liveSearchPage";
  static const String liveDetail = "liveDetail";

  //粮价
  static const String transactionReport = "transactionReport";
  static const String transactionReportManage = "transactionReportManage";
  static const String pricePublishManage = "pricePublishManage";
  static const String myPriceCollect = "myPriceCollect";
  static const String myPriceHomePage = "myPriceHomePage";
  static const String buyPublish = "buyPublish";
  static const String salePublish = "salePublish";
  static const String buyQualification = "buyQualification";
  static const String priceScan = "priceScan";
  static const String myRelease = "myRelease";
  static const String myTransaction = "myTransaction";
  static const String realNameCertification = "realNameCertification";
  static const String riceHome = "riceHome";
  static const String personPrice = "personPrice";
  static const String transactionPage = "transactionPage";
  static const String releaseManagePage = "releaseManagePage";
  static const String myQualification = "myQualification";

  //个人中心 '我的'
  static const String settingPage = "settingPage"; //我的>设置
  static const String userinfoEdit = "userinfoEdit"; //我的>编辑用户图像
  static const String bankList = "bankList"; //我的>银行卡
  static const String modifyPasswordPage = "modifyPasswordPage";
  static const String modifyTelephonePage = "modifyTelephonePage";
  static const String worksStarsPage = "worksStarsPage";
  static const String worksStarsVideo = "worksStarsVideo";
  static const String loginPincodeInput = "loginPincodeInput";
  static const String loginTelephonePage = 'loginTelephonePage';

  //北大荒土地承包
  static const String bdhLandContract = "bdhLandContract";

  //大兴安岭土地承包
  static const String daHingLandContract = "daHingLandContract";
  static const String daHingBankList = "daHingBankList";
  static const String daHingOnlinePay = "daHingOnlinePay";
  static const String daHingLogin = "daHingLogin";

  //新疆土地承包
  static const String xjLandContract = "xjLandContract";

  //新疆水费管理
  static const String xjWaterManage = "xjWaterManage";
  static const String waterPrice = "waterPrice";
  static const String myWater = "mywater";
  static const String waterContaining = "waterContaining";
  static const String waterDistribution = "waterDistribution";
  static const String dispatchWater = "dispatchWater";
  static const String waterSettle = "waterSettle";

  //产品线上缴费
  static const String onlinePay = "onlinePay";

  //Chat
  static const String chatPage = "chatMainPage";

  //我的补贴
  static const String subsidyPage = "subsidyMainPage";
  static const String subsidyListPage = "subsidyListPage";
  static const String subsidyNewsListPage = "subsidyNewsListPage";
  //随手记
  static const String costAnalysis = "costanalysis";
  static const String addAccount = "addAccount";
  static const String detailAccount = "detailAccount";
  //财务核算
  static const String costAnalysisInfo = "costanalysisinfo";

  //pos
  static const String posCharge = 'posCharge';
  static const String posRechargePage = 'posRechargePage';

  //荐种清单
  static const String testimonialsTorrentPage = 'testimonialsTorrentPage';
  static const String ttDetailsPage = 'ttDetailsPage';
  static const String pedDetailsPage = 'pedDetailsPage';
  //UmPush
  static const String umPush = 'umPush';

  //农业计算器
  static const String cal = 'cal';
  static const String seeding = 'seeding'; //播量计算
  static const String weight = 'weight'; //单口流量计算
  static const String predicted = 'predicted'; //测产计算
  static const String corn = 'corn'; //玉米产量计算
  static const String grain = 'grain'; //粮损率计算
  static const String mechant = 'mechant'; //纯转商计算
  static const String quotient = 'quotient'; //商转纯计算

  //农贷助手
  static const String loanApply = 'loadApply';

  //数字北大荒 注册
  static const String registerBdhDigitalPage = 'registerBdhDigitalPage';
  static const String loginBdhDigitalPage = 'loginBdhDigitalPage';
  static const String loginVerifyBdhDigitalPage = 'loginVerifyBdhDigitalPage';
  static const String loginPincodeInputBdhDigital =
      "loginPincodeInputBdhDigital";
  static const String loginChooseAccountBdhDigital =
      'loginChooseAccountBdhDigital';
  static const String forgetPwdBdhDigitalPage = 'forgetPwdBdhDigitalPage';
  static const String userInfoBdhDigital = 'userInfoBdhDigital';
  static const String settingPageBdhDigital = 'settingPageBdhDigital';
  static const String modifyTelephoneBdhDigitalPage =
      'modifyTelephoneBdhDigitalPage';
  static const String scanLoginConfirmPage = 'scanLoginConfirmPage';
  static const String sacnPageBdhDigital = 'sacnPageBdhDigital';
  //释放审核
  static const String releaseAudit = "releaseAudit";
  //审核释放
  static const String auditRelease = 'auditRelease';

  // Unlock Review
  static const String unlockReview = "unlockReview";

  // New route
  static const String proofSettlement = "proofSettlement";

  // 补充贷款解锁审核
  static const String supplementInfo = "supplementInfo";
  // 农事记录
  static const String agriculturalRecords = "agriculturalRecords";
  //数字北大荒 消息
  static const String messageBdhDigitalPage = 'messageBdhDigitalPage';

//数字北大荒 星级排行
  static const String rankBdhDigitalPage = 'rankBdhDigitalPage';
  //数字北大荒土地承包 ————————————————————
  // 土地承包首页
  static const String diaLandContract = 'diaLandContract';
  //基本田申请
  static const String diaApplication = 'diaApplication';
  //基本田认证
  static const String diaAuthentication = 'diaAuthentication';
  //基本田申请完成
  static const String diaApplySuccess = 'diaApplySuccess';
  //签字
  static const String chargeInfo = 'chargeInfo';
  //拍照
  static const String chargeInfoPhoto = 'chargeInfoPhoto';
  //预收承包费计划审核
  static const String examine = 'examine';
  //巡田百科
  static const String patrol = "patrol";

  //农机管家
  static const String agriculturalManager = "agriculturalManager";
  //三次土壤普查
  static const String threeCensus = "threeCensus";

  //天气
  static const String weather = "weather";

  //农情服务
  static const String agriculturalCondition = "agriculturalCondition";
}

RouteSettings getRouteSettingsFromUri(Uri uri) {
  return RouteSettings(
      name: uri.pathSegments.first, arguments: uri.pathSegments);
}

class MyRouter {
  //tabMain
  static String currentRouteName = '';

  static Route<dynamic>? generateRoute(RouteSettings settings) {
    Logger().i("MyRouter route to ${settings.name}");
    currentRouteName = settings.name ?? '';
    GlobalServiceView.needShowServiceBtn(settings.name ?? '');
    switch (settings.name) {
      case RouteName.splash:
        return CupertinoPageRoute(builder: (_) => const SplashPage());
      //业务路由
      case RouteName.login:
        return CupertinoPageRoute(builder: (_) => const LoginPage());
      case RouteName.loginVerify:
        return CupertinoPageRoute(builder: (_) => const LoginVerifyPage());
      case RouteName.forgetPassword:
        return CupertinoPageRoute(builder: (_) => const ForgetPasswordPage());
      case RouteName.register:
        return CupertinoPageRoute(builder: (_) => const RegisterPage());
      case RouteName.tabMain:
        return CupertinoPageRoute(
            builder: (_) => const TabMainPage(),
            settings: const RouteSettings(name: RouteName.tabMain));
      // case RouteName.service:
      //   return CupertinoPageRoute(builder: (_) => const TabServicePage());
      case RouteName.transactionReport:
        return CupertinoPageRoute(
            builder: (_) => const TransactionReportPage());
      case RouteName.transactionReportManage:
        return CupertinoPageRoute(builder: (_) => const TransactionManage());
      case RouteName.pricePublishManage:
        return CupertinoPageRoute(builder: (_) => const PriceManage());
      case RouteName.myPriceCollect:
        return CupertinoPageRoute(builder: (_) => const MyCollect());
      case RouteName.myPriceHomePage:
        return CupertinoPageRoute(builder: (_) => const MyPage());
      case RouteName.buyPublish:
        return CupertinoPageRoute(builder: (_) => const BuyRelease());
      case RouteName.salePublish:
        return CupertinoPageRoute(builder: (_) => const SaleRelease());
      case RouteName.buyQualification:
        return CupertinoPageRoute(builder: (_) => const Qualification());
      case RouteName.priceScan:
        return CupertinoPageRoute(
            builder: (_) => DrawFoodStuffPrice(
                  item: settings.arguments as MenuConfigItem,
                ));
      case RouteName.myTransaction:
        return CupertinoPageRoute(builder: (_) => const MyTransaction());
      case RouteName.myRelease:
        return CupertinoPageRoute(builder: (_) => const MyRelease());
      case RouteName.releaseManagePage:
        return CupertinoPageRoute(
            builder: (_) => ReleaseManagePage(
                  configItem: settings.arguments as MenuConfigItem,
                ));
      case RouteName.myQualification:
        return CupertinoPageRoute(builder: (_) => const MyQualification());
      case RouteName.realNameCertification:
        return CupertinoPageRoute(
            builder: (_) => const RealNameCertificationView());
      case RouteName.riceHome:
        return CupertinoPageRoute(
            builder: (_) => DrawFoodStuffPrice(
                  item: settings.arguments as MenuConfigItem,
                ));
      case RouteName.daHingLandContract:
        return CupertinoPageRoute(
            builder: (_) => const DaHingLandContractPage(),
            settings: const RouteSettings(name: RouteName.daHingLandContract));
      case RouteName.xjLandContract:
        return CupertinoPageRoute(
            builder: (_) => const XjLandContractPage(),
            settings: const RouteSettings(name: RouteName.xjLandContract));
      case RouteName.settingPage:
        return CupertinoPageRoute(builder: (_) => const SettingPage());
      case RouteName.newsMessageHome:
        return CupertinoPageRoute(builder: (_) => const NewsMessageHome());
      case RouteName.userinfoEdit:
        return CupertinoPageRoute(
            builder: (_) => UserinfoEdit(
                  info: settings.arguments as RiceUserHomeInfo,
                ));
      case RouteName.leaveMessagePage:
        return CupertinoPageRoute(builder: (_) => const LeaveMessagePage());
      case RouteName.searchNewPage:
        return CupertinoPageRoute(builder: (_) => const SearchNewPage());
      case RouteName.leaveMessageDitail:
        return CupertinoPageRoute(
            builder: (_) => LeaveMessageDitail(
                  model: settings.arguments as LeaveMessageModel,
                ));
      case RouteName.photoArticleDitail:
        return CupertinoPageRoute(
            builder: (_) => PhotoArticleDitail(
                  model: settings.arguments as SearchContentModel,
                ));
      case RouteName.adDitail:
        return CupertinoPageRoute(
            builder: (_) => AdDitail(
                  model: settings.arguments as SearchContentModel,
                ));
      case RouteName.videoPlayView:
        return CupertinoPageRoute(
            builder: (_) => VideoPlayView(
                  model: settings.arguments as SearchContentModel,
                ));
      case RouteName.premiumClassroom:
        return CupertinoPageRoute(
          builder: (_) => PremiumClassroom(
            type: ((settings.arguments as Map)['type']) as EntryType,
            videoId: ((settings.arguments as Map)['videoId']) as int,
            myLikeVideo: (settings.arguments as Map).containsKey('myLikeVideo')
                ? (((settings.arguments as Map)['myLikeVideo']) as bool)
                : (false),
            notShowGuidePage:
                ((settings.arguments as Map)['notShowGuidePage']) as bool,
          ),
        );
      case RouteName.videoSearchPage:
        return CupertinoPageRoute(builder: (_) => const VideoSearchPage());
      case RouteName.videoSecondTopicPage:
        return CupertinoPageRoute(
            builder: (_) => VideoSecondTopicPage(
                  item: settings.arguments as DictNode,
                ));
      case RouteName.onlinePay:
        return CupertinoPageRoute(
            builder: (_) => const OnlinePay(),
            settings: const RouteSettings(name: RouteName.onlinePay));
      case RouteName.modifyPasswordPage:
        return CupertinoPageRoute(builder: (_) => const ModifyPasswordPage());
      case RouteName.modifyTelephonePage:
        return CupertinoPageRoute(builder: (_) => const ModifyTelephonePage());
      case RouteName.personPrice:
        return CupertinoPageRoute(
            builder: (_) => PersonPrice(org: settings.arguments as Map));
      case RouteName.transactionPage:
        return CupertinoPageRoute(
            builder: (_) => TransactionPage(org: settings.arguments as Map));
      case RouteName.bankList:
        return CupertinoPageRoute(
            builder: (_) => const BankListInfoPage(
                  isDaxing: false,
                ));
      case RouteName.daHingBankList:
        return CupertinoPageRoute(
            builder: (_) => const DaHingBankListInfoPage());
      case RouteName.daHingLogin:
        return CupertinoPageRoute(builder: (_) => const DaHingLoginPage());
      case RouteName.daHingOnlinePay:
        return CupertinoPageRoute(
            builder: (_) => const DaHingOnlinePay(),
            settings: const RouteSettings(name: RouteName.daHingOnlinePay));
      case RouteName.worksStarsPage:
        return CupertinoPageRoute(builder: (_) => const WorksStarsPage());
      case RouteName.bdhLandContract:
        return CupertinoPageRoute(builder: (_) => const BdhLandContractPage());
      case RouteName.worksStarsVideo:
        return CupertinoPageRoute(
            builder: (_) => WorksStarsVideo(
                type: ((settings.arguments as Map)['type']) as EntryType,
                videoId: ((settings.arguments as Map)['videoId']) as int,
                myLikeVideo:
                    (settings.arguments as Map).containsKey('myLikeVideo')
                        ? (((settings.arguments as Map)['myLikeVideo']) as bool)
                        : (false),
                notShowGuidePage:
                    ((settings.arguments as Map)['notShowGuidePage']) as bool));
      case RouteName.xjWaterManage:
        return CupertinoPageRoute(builder: (_) => const WaterManagePage());

      case RouteName.waterPrice:
        return CupertinoPageRoute(builder: (_) => const WaterManagePage());
      case RouteName.costAnalysisInfo:
        return CupertinoPageRoute(builder: (_) => const CostAnalysisInfoPage());
      case RouteName.myWater:
        return CupertinoPageRoute(builder: (_) => const MyWaterPage());
      case RouteName.waterContaining:
        return CupertinoPageRoute(builder: (_) => const WaterContainingPage());
      case RouteName.waterDistribution:
        return CupertinoPageRoute(
            builder: (_) => const WaterDistributionPage());
      case RouteName.dispatchWater:
        return CupertinoPageRoute(builder: (_) => const WaterDispatchPage());
      case RouteName.waterSettle:
        return CupertinoPageRoute(builder: (_) => const WaterSettlePage());
      case RouteName.loginPincodeInput:
        return CupertinoPageRoute(
            builder: (_) => LoginPincodeInput(
                  phoneNumber: settings.arguments as String,
                ));
      case RouteName.loginTelephonePage:
        return CupertinoPageRoute(builder: (_) => const LoginTelephonePage());
      case RouteName.chatPage:
        return CupertinoPageRoute(builder: (_) => const ChatMainPage());
      case RouteName.subsidyPage:
        return CupertinoPageRoute(builder: (_) => const SubsidyMainPage());
      case RouteName.subsidyListPage:
        return CupertinoPageRoute(builder: (_) => const SubsidyListPage());
      case RouteName.costAnalysis:
        return CupertinoPageRoute(builder: (_) => const CostTabbar());
      case RouteName.posCharge:
        return CupertinoPageRoute(builder: (_) => const PosFarmerList());
      case RouteName.posRechargePage:
        return CupertinoPageRoute(
            builder: (_) =>
                PosRechargePage(model: settings.arguments as RecordsItemModel));
      case RouteName.liveSearchPage:
        return CupertinoPageRoute(builder: (_) => const LiveSearchPage());
      case RouteName.testimonialsTorrentPage:
        return CupertinoPageRoute(
            builder: (_) => const TestimonialsTorrentPage());
      case RouteName.ttDetailsPage:
        return CupertinoPageRoute(
            builder: (_) => TtDetailsPage(
                pedItem: settings.arguments as PedRecommendModel));
      case RouteName.pedDetailsPage:
        return CupertinoPageRoute(
            builder: (_) =>
                PedDetailPage(record: settings.arguments as Records));
      case RouteName.liveDetail:
        return CupertinoPageRoute(builder: (_) {
          var paths = settings.arguments as List<String>? ?? <String>[];
          if (paths.length >= 2) {
            return LiveDetail(uid: paths[1]);
          } else {
            return const LiveDetail();
          }
        });
      case RouteName.cal:
        return CupertinoPageRoute(builder: (_) {
          return const CalHomePage();
        });
      case RouteName.umPush:
        return CupertinoPageRoute(builder: (_) => const UmPush());
      case RouteName.loanApply:
        return CupertinoPageRoute(builder: (_) => const LoanApplyPage());
      case RouteName.auditRelease:
        return CupertinoPageRoute(builder: (_) => const AuditReleasePage());
      case RouteName.telephoneLogin:
        return CupertinoPageRoute(builder: (_) => const TelephoneLogin());
      case RouteName.loginChooseAccount:
        return CupertinoPageRoute(
            builder: (_) => LoginChooseAccount(
                  model: settings.arguments as BdhLoginMultiAccountModel,
                ));
      case RouteName.registerBdhDigitalPage:
        return CupertinoPageRoute(
            builder: (_) => const RegisterBdhDigitalPage());
      case RouteName.loginBdhDigitalPage:
        return CupertinoPageRoute(builder: (_) => const LoginBdhDigitalPage());
      case RouteName.loginVerifyBdhDigitalPage:
        return CupertinoPageRoute(
            builder: (_) => const LoginVerifyBdhDigitalPage());
      case RouteName.loginPincodeInputBdhDigital:
        return CupertinoPageRoute(
            builder: (_) => LoginPincodeInputBdhDigital(
                  phoneNumber: settings.arguments as String,
                ));
      case RouteName.loginChooseAccountBdhDigital:
        return CupertinoPageRoute(
            builder: (_) => LoginChooseAccountBdhDigital(
                  model: settings.arguments as BdhLoginMultiAccountModel,
                ));
      case RouteName.forgetPwdBdhDigitalPage:
        return CupertinoPageRoute(
            builder: (_) => const ForgetPwdBdhDigitalPage());
      case RouteName.userInfoBdhDigital:
        return CupertinoPageRoute(builder: (_) => const UserInfoBdhDigital());
      case RouteName.settingPageBdhDigital:
        return CupertinoPageRoute(builder: (_) {
          return const SettingPageBdhDigital();
        });

      case RouteName.modifyTelephoneBdhDigitalPage:
        return CupertinoPageRoute(
            builder: (_) => const ModifyTelephoneBdhDigitalPage());

      case RouteName.scanLoginConfirmPage:
        return CupertinoPageRoute(builder: (cxt) {
          return ScanLoginConfirmPage(ticket: settings.arguments as String);
        });

      case RouteName.sacnPageBdhDigital:
        return CupertinoPageRoute(
            builder: (_) => SacnPageBdhDigital(
                  controller: settings.arguments as MobileScannerController,
                ));

      case RouteName.MyFieldPage:
        return CupertinoPageRoute(builder: (_) => const MyFieldPage());

      //释放审核
      // case RouteName.releaseAudit:
      //   return CupertinoPageRoute(builder: (_) => LoanSettlementRelease());
      // Unlock Review
      case RouteName.unlockReview:
        return CupertinoPageRoute(builder: (_) => const UnlockReviewPage());

      // New route
      case RouteName.proofSettlement:
        return CupertinoPageRoute(builder: (_) => const ProofSettlementPage());

      // 补充贷款解锁审核
      case RouteName.supplementInfo:
        return CupertinoPageRoute(
          builder: (_) => const SupplementInfoPage(),
          settings: settings, // 保留路由参数
        );
      case RouteName.diaLandContract:
        return CupertinoPageRoute(builder: (_) => const DiaLandContract());
      case RouteName.diaApplication:
        return CupertinoPageRoute(builder: (_) => const DiaApplication());
      case RouteName.chargeInfo:
        return CupertinoPageRoute(
            builder: (_) => const ChargeInfo(), settings: settings);
      case RouteName.chargeInfoPhoto:
        return CupertinoPageRoute(
            builder: (_) => const ChargeInfoPhoto(), settings: settings);
      case RouteName.examine:
        return CupertinoPageRoute(
            builder: (_) => const Examine(), settings: settings);
      case RouteName.diaAuthentication:
        return CupertinoPageRoute(builder: (_) => const DiaAuthentication());
      case RouteName.addAccount:
        return CupertinoPageRoute(builder: (_) => const AddAccountPage());
      case RouteName.diaApplySuccess:
        return CupertinoPageRoute(
            builder: (_) => const DiaApplySuccess(
                  params: {},
                ));
      case RouteName.detailAccount:
        return CupertinoPageRoute(
          builder: (_) => DetailAccount(
            id: (settings.arguments as Map)['id'],
          ),
        );
      case RouteName.agriculturalRecords:
        return CupertinoPageRoute(
          builder: (_) => const AgriculturalRecordsPage(),
        );

      case RouteName.messageBdhDigitalPage:
        return CupertinoPageRoute(
            builder: (_) => const MessageBdhDigitalPage());
      case RouteName.rankBdhDigitalPage:
        return CupertinoPageRoute(builder: (_) {
          return const RankBdhDigitalPage();
        });

      case RouteName.patrol:
        return CupertinoPageRoute(builder: (_) {
          return const PatrolMainPage();
        });
      case RouteName.agriculturalManager:
        return CupertinoPageRoute(builder: (_) {
          return const AgriculturalManagerPage();
        });
      case RouteName.threeCensus:
        return CupertinoPageRoute(builder: (_) {
          return const ThreeIndexPage();
        });
      case RouteName.weather:
        return CupertinoPageRoute(builder: (_) {
          return MoreWeatherPage(
            params: settings.arguments as Map<String, dynamic>,
          );
        });
      case RouteName.agriculturalCondition:
        return CupertinoPageRoute(builder: (_) {
          return AgriculturalConditionPage(arguments: settings.arguments);
        });
      default:
        return CupertinoPageRoute(
            builder: (_) => Scaffold(
                  body: Center(
                    child: Text('No route defined for ${settings.name}'),
                  ),
                ));
    }
  }
}
