import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/search_new_page.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/news_message_home.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluwx/fluwx.dart';

import '../manager/router_manager.dart';

class TabServicePage extends StatefulWidget {
  final MenuConfigItem item;

  const TabServicePage({super.key, required this.item});

  @override
  State<StatefulWidget> createState() => _TabServiceNewPageState();
}

class _TabServiceNewPageState extends State<TabServicePage> {
  List<MenuConfigItem> topItems = [];
  List<MenuConfigItem> bottomGroups = [];

  @override
  void initState() {
    super.initState();
    for (var v in widget.item.children!) {
      if (v.authCode == "topMenu") {
        for (var topItem in v.children!) {
          topItems.add(topItem);
        }
      }
      if (v.authCode == "bottomMenu") {
        for (var bottomGroup in v.children!) {
          var bottomChildren = bottomGroup.children ?? [];
          for (var item in bottomChildren) {
            if (item.authName == "农业计算器") {
              item.icon = "calculator";
            }
          }
          bottomGroups.add(bottomGroup);

          /// TODO 手动添加模块，服务端添加模块后删除
          if (bottomGroup.authName == "助农") {
            var item = bottomGroup.children![0];
            var newItem = MenuConfigItem(
                authId: item.authId,
                parentId: item.parentId,
                authCode: item.authCode,
                authName: '荐种助手',
                authType: item.authType,
                url: "testimonialsTorrentPage",
                icon: item.icon,
                orderNum: item.orderNum,
                children: item.children);
            bottomGroup.children!.add(newItem);
          }

          /// TODO 手动添加模块，服务端添加模块后删除
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(239, 239, 239, 1),
      appBar: AppBar(
        backgroundColor: const Color.fromRGBO(22, 183, 96, 1),
        titleSpacing: 0,
        title: PreferredSize(
          preferredSize: Size.fromWidth(375.px),
          child: SizedBox(
            height: 40.px,
            width: 345.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.of(context)
                        .push(CupertinoPageRoute(builder: (context) {
                      return const SearchNewPage();
                    }));
                  },
                  child: Container(
                    width: 250.px,
                    height: 28.px,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(Radius.circular(14.px))),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: 7.px,
                        ),
                        Image.asset(
                            width: 24.px,
                            height: 24.px,
                            ImageHelper.wrapAssets("search_grey.png")),
                        Text(
                          '请输入...',
                          style: TextStyle(
                              color: const Color.fromRGBO(168, 184, 177, 1),
                              fontSize: 12.px,
                              fontWeight: FontWeight.w300),
                        ),
                      ],
                    ),
                  ),
                ),
                Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context)
                            .push(CupertinoPageRoute(builder: (ctx) {
                          return const NewsMessageHome();
                        }));
                      },
                      child: Image.asset(
                          width: 24.px,
                          height: 24.px,
                          ImageHelper.wrapAssets("chat_square_dots.png")),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
      body: LayoutBuilder(
        builder: (BuildContext context, BoxConstraints constraints) {
          return Stack(
            children: [
              Image.asset(
                  width: 375.px,
                  fit: BoxFit.fitWidth,
                  ImageHelper.wrapAssets("service_background.png")),
              Column(
                children: [
                  SizedBox(
                    height: 20.px,
                    width: 375.px,
                  ),
                  SizedBox(
                    width: 293.px,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        ...topItems.map((e) {
                          return MenuItemView(item: e);
                        })
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 20.px,
                  ),
                  SizedBox(
                    width: 351.px,
                    height: constraints.maxHeight - 97.px,
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          ...bottomGroups.map((group) {
                            //group.authName
                            debugPrint('-->def:${group.toJson()}');
                            return Container(
                              width: 351.px,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(2.px))),
                              margin: EdgeInsets.only(bottom: 15.px),
                              padding: EdgeInsets.only(
                                  top: 15.px,
                                  left: 18.px,
                                  right: 15.px,
                                  bottom: 18.px),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    group.authName ?? "",
                                    style: TextStyle(
                                        fontSize: 12.px,
                                        fontWeight: FontWeight.w400,
                                        color:
                                            const Color.fromRGBO(0, 0, 0, 0.4)),
                                  ),
                                  SizedBox(
                                    height: 15.px,
                                  ),
                                  SizedBox(
                                    width: 315.px,
                                    child: Wrap(
                                      runSpacing: 20.px,
                                      children: [
                                        ...(group.children ?? []).map((e) {
                                          debugPrint('->lll:${e.toJson()}');
                                          return MenuItemView(
                                            item: e,
                                            isGroup: true,
                                          );
                                        })
                                      ],
                                    ),
                                  )
                                ],
                              ),
                            );
                          })
                        ],
                      ),
                    ),
                  ),
                ],
              )
            ],
          );
        },
      ),
    );
  }
}

class MenuItemView extends StatelessWidget {
  final bool? isGroup;
  final MenuConfigItem item;

  const MenuItemView({super.key, required this.item, this.isGroup});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        //统计用户点击
        BDHResponsitory.saveMenuUse({
          "menuCode": item.authCode,
          "menuName": item.authName,
          "menuType": "2", //所有服务类型
        }).then((res) {
          Log.i(res.msg);
        });

        if (item.url!.startsWith("gh_")) {
          //跳转微信小程序
          Fluwx fluwx = Fluwx();
          fluwx.open(target: MiniProgram(username: item.url!));
        } else if (item.url!.startsWith("pages")) {
          //跳转uni小程序
          NativeUtil.openUni({"path": item.url});
        } else if (item.url!.startsWith("http")) {
          //跳转h5网页
          Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
            return HtmlPage(
                title: item.authName ?? "", content: item.url ?? "");
          }));
        } else {
          //跳转flutter 原生模块
          Navigator.of(context)
              .pushNamed(item.url ?? "无效路径", arguments: item)
              .then((res) {
            GlobalServiceView.needShowServiceBtn('service');
          });
        }
      },
      child: isGroup == true
          ? Container(
              width: 78.75.px,
              height: 60.px,
              color: Colors.transparent,
              child: Column(
                children: [
                  Image.asset(
                      width: 32.px,
                      height: 32.px,
                      ImageHelper.wrapOldAssets("${item.icon!}.png")),
                  SizedBox(
                    height: 6.px,
                  ),
                  Text(
                    item.authName ?? "",
                    style: TextStyle(
                        color: Colors.black,
                        fontSize: 12.px,
                        fontWeight: FontWeight.w500),
                  )
                ],
              ),
            )
          : Container(
              width: 73.25.px,
              height: 57.px,
              color: Colors.transparent,
              child: Column(
                children: [
                  Image.asset(
                      width: 32.px,
                      height: 32.px,
                      ImageHelper.wrapOldAssets("${item.icon!}.png")),
                  SizedBox(
                    height: 5.px,
                  ),
                  Text(
                    item.authName ?? "",
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 12.px,
                        fontWeight: FontWeight.w500),
                  )
                ],
              ),
            ),
    );
  }
}

class MenuItem {
  String icon;
  String title;
  String path;

  MenuItem({required this.icon, required this.title, required this.path});
}

class MenuGroup {
  String title;
  List<MenuItem> children;

  MenuGroup({required this.title, required this.children});
}
