import 'dart:async';
import 'package:bdh_smart_agric_app/components/bdh_network_image.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/pages/login/privicy_dialog.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../utils/Global.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    Global().fetchWeatherImages();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: [SystemUiOverlay.top]);

    //检查用户是否同意了隐私协议
    if (StorageManager.sharedPreferences?.getBool("isPrivacyAgree") == true) {
      Timer.periodic(const Duration(milliseconds: 3000), (timer) {
        nextPage(context);
        timer.cancel();
      });
    } else {
      Future.delayed(const Duration(milliseconds: 100), () {
        if (!mounted) {
          return;
        }
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) {
              MediaQueryData data = MediaQuery.of(context)
                  .copyWith(textScaler: const TextScaler.linear(1));
              return MediaQuery(
                data: data,
                child: const PrivicyDialog(),
              );
            });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      child:
          Image.asset(fit: BoxFit.cover, ImageHelper.wrapAssets("splash.jpg")),
      // child: BdhNetworkImage(
      //     fit: BoxFit.cover,
      //     url:
      //         "https://app.bdhic.com/microfront/oss-huawei-obs/mobile_app/bdh_agri_farm_clothes/1854065691708096512.jpg"),
    );
  }

  nextPage(context) {
    if (StorageUtil.userInfo()?.data?.token != null) {
      // Navigator.push(
      //   context,
      //   MaterialPageRoute(
      //     builder: (context) => RealTimeDataPage(
      //     ),
      //   ),
      // );
      Navigator.of(context).pushReplacementNamed(RouteName.tabMain);
    } else {
      // Navigator.of(context).pushReplacementNamed(RouteName.login);
      Navigator.of(context).pushReplacementNamed(RouteName.loginBdhDigitalPage);
    }
  }
}
