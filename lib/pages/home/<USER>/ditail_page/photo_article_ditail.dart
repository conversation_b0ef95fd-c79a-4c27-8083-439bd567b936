import 'dart:async';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_segment_line.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/ditail_page/photo_atical_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_tool.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:video_player/video_player.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:fluwx/fluwx.dart';

import '../../../../components/bdh_share_view.dart';
import '../../../../const/url_config_const.dart';
import '../../../../utils/color_util.dart';
import '../../../../utils/debounce_throttle_util.dart';
import '../../../../utils/image_util.dart';
import '../../../../utils/request/home_service.dart';
import '../../search/model/search_content_model.dart';
import '../../search/search_new_page.dart';

class PhotoArticleDitail extends StatefulWidget {
  final SearchContentModel model;
  const PhotoArticleDitail({super.key, required this.model});

  @override
  State<PhotoArticleDitail> createState() => _PhotoArticleDitailState();
}

class _PhotoArticleDitailState extends State<PhotoArticleDitail> {
  final GlobalKey webViewKey = GlobalKey();
  InAppWebViewController? webViewController;
  double progress = 0;
  InAppWebViewSettings settings = InAppWebViewSettings(
    minimumFontSize: 20,
    supportZoom: true,
    displayZoomControls: true,
    builtInZoomControls: true,
    supportMultipleWindows: true,
    verticalScrollBarEnabled: false,
    horizontalScrollBarEnabled: false,
    // isInspectable: kDebugMode,
  );

  late FindInteractionController findInteractionController;
  final searchController = TextEditingController();
  var textFound = "";
  bool showUpDownIcon = false;
  bool showDeletIcon = false;
  final FocusScopeNode focusScopeNode = FocusScopeNode();

  late PhotoAticalModel ditailModel;
  bool showBigFont = false;
  final double addFont = 5.0;
  String htmlStr = "";
  double webViewHeight = 0;
  double bottomViewHeight = 40.px;
  WebViewController controller = WebViewController();
  Animation<double>? animation;
  AnimationController? animationController;

  List<String> videoList = [];
  // List<VideoPlayModel> videoModelList = [];
  Fluwx fluwx = Fluwx();
  // bool isPlaying = false;
  // bool showFullScreen = false;

  VideoPlayerController? currentVideoController;
  double playViewMaxH = 250.px;
  String searchQuery = '';
  final _debounce = Debouncer(milliseconds: 300);

  @override
  void initState() {
    super.initState();
    ditailModel = PhotoAticalModel();
    initFindInteractionController();
    // _initFluwx();
    // controller
    //   ..setJavaScriptMode(JavaScriptMode.unrestricted)
    //   ..addJavaScriptChannel("Resize", onMessageReceived: (message) {
    //     double height = double.parse(message.message);
    //     setState(() {
    //       webViewHeight = height / 10;
    //     });
    //   });
    getDital();
    searchController.addListener(() {
      setState(() {
        showUpDownIcon = searchController.text.isNotEmpty ? true : false;
        showDeletIcon = searchController.text.isNotEmpty ? true : false;
      });
    });
  }

  initFindInteractionController() {
    findInteractionController = FindInteractionController(
      onFindResultReceived: (controller, activeMatchOrdinal, numberOfMatches,
          isDoneCounting) async {
        if (isDoneCounting) {
          setState(() {
            // textFound = numberOfMatches > 0
            //     ? '${activeMatchOrdinal + 1} of $numberOfMatches'
            //     : '';
            textFound = numberOfMatches > 0
                ? '${activeMatchOrdinal + 1}/$numberOfMatches'
                : '';
          });
          if (numberOfMatches == 0) {
            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
              content: Text(
                  '没有搜到: "${await findInteractionController.getSearchText()}"'),
            ));
          }
        }
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    // for (int i = 0; i < videoModelList.length; i++) {
    //   VideoPlayModel modelItem = videoModelList[i];
    //   modelItem.controller.dispose();
    // }
  }

  Future<bool> isWXInstalled() async {
    var result = await fluwx.isWeChatInstalled;
    if (!result) {
      showToast("无法打开微信 请检查是否安装了微信");
      return false;
    }
    return true;
  }

  void _launchURL(url) async => await canLaunchUrl(url)
      ? await launchUrl(url)
      : showToast("对不起，打不开链接地址：$url");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leadingWidth: 50.px,
        actions: !showUpDownIcon
            ? []
            : [
                SizedBox(
                  width: 35.px,
                  child: IconButton(
                    highlightColor: Colors.transparent,
                    hoverColor: Colors.transparent,
                    icon: const Icon(
                      Icons.arrow_upward,
                      size: 18.0,
                    ),
                    onPressed: () {
                      findInteractionController.findNext(forward: false);
                    },
                  ),
                ),
                SizedBox(
                  width: 35.px,
                  child: Center(
                    child: IconButton(
                      highlightColor: Colors.transparent,
                      hoverColor: Colors.transparent,
                      icon: const Icon(
                        Icons.arrow_downward,
                        size: 18.0,
                      ),
                      onPressed: () {
                        findInteractionController.findNext();
                      },
                    ),
                  ),
                ),
                SizedBox(width: 10.px),
                //搜索按钮
                Container(
                  padding: EdgeInsets.only(right: 15.px),
                  child: GestureDetector(
                    onTap: () {
                      focusScopeNode.unfocus();
                      if (searchController.text == '') {
                        findInteractionController.clearMatches();
                        setState(() {
                          textFound = '';
                        });
                      } else {
                        findInteractionController.findAll(
                            find: searchController.text);
                      }
                    },
                    child: const Text('搜索'),
                  ),
                ),
              ],
        leading: IconButton(
          onPressed: () async {
            _debounce.run(() async {
              if (ditailModel.content == null) {
                Navigator.of(context).pop();
              } else {
                var canBack = await webViewController?.canGoBack();
                if (canBack!) {
                  webViewController?.goBack();
                } else {
                  Future.delayed(const Duration(milliseconds: 300), () {
                    Navigator.of(context).pop();
                  });
                }
              }
            });
          },
          icon: Image.asset(
              width: 22.px,
              height: 22.px,
              ImageHelper.wrapAssets("backBlackIcon.png")),
        ),
        title: searchView(),
      ),
      // body: widget.model.sourceType == '2'
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, cons) {
            return Stack(
              children: [
                Positioned(
                  top: 0,
                  child: SizedBox(
                      height: 3.px,
                      width: 375.px,
                      // decoration: BoxDecoration(border: Border.all(width: 1)),
                      child: progress < 1.0
                          ? LinearProgressIndicator(value: progress)
                          : Container()),
                ),
                Positioned(
                    top: 3.px,
                    // bottom: bottomViewHeight - ScreenTool().bottomBarHeight,
                    bottom: bottomViewHeight,
                    child: SizedBox(
                        height: ScreenTool().screenHeight -
                            bottomViewHeight -
                            ScreenTool().bottomBarHeight,
                        width: 375.px,
                        // child: WebViewWidget(controller: controller), // old webview
                        child: ditailModel.content != null
                            ? InAppWebView(
                                key: webViewKey,
                                initialUrlRequest: URLRequest(
                                    url: WebUri(ditailModel.content ?? '')),
                                // initialUrlRequest: URLRequest(
                                //     url: WebUri('https://flutter.dev/')),
                                findInteractionController:
                                    findInteractionController,
                                initialSettings: settings,
                                // androidOnPermissionRequest:
                                //     (controller, origin, resources) async {
                                //   return PermissionRequestResponse(
                                //       resources: resources,
                                //       action: PermissionRequestResponseAction
                                //           .GRANT);
                                // },
                                onReceivedServerTrustAuthRequest:
                                    (controller, challenge) async {
                                  return ServerTrustAuthResponse(
                                      action: ServerTrustAuthResponseAction
                                          .PROCEED);
                                },
                                // initialOptions: InAppWebViewGroupOptions(
                                //   android: AndroidInAppWebViewOptions(
                                //     mixedContentMode: AndroidMixedContentMode
                                //         .MIXED_CONTENT_ALWAYS_ALLOW,
                                //     // 允许加载不安全的内容
                                //     allowContentAccess: true,
                                //     allowFileAccess: true,
                                //     useHybridComposition: true,
                                //     // useShouldInterceptRequest: true,
                                //   ),
                                //   ios: IOSInAppWebViewOptions(
                                //     allowsInlineMediaPlayback: true,
                                //   ),
                                // ),
                                onWebViewCreated:
                                    (InAppWebViewController controllerWeb) {
                                  controllerWeb.setSettings(
                                      settings: InAppWebViewSettings(
                                          //     contentBlockers: [
                                          //   ContentBlocker(
                                          //       trigger: ContentBlockerTrigger(
                                          //         urlFilter:
                                          //             "weixin://dl/business/?*",
                                          //       ),
                                          //       action: ContentBlockerAction(
                                          //           type: ContentBlockerActionType
                                          //               .BLOCK))
                                          // ],
                                          builtInZoomControls: true,
                                          supportZoom: true));

                                  webViewController = controllerWeb;
                                },
                                onConsoleMessage: (controller, consoleMessage) {
                                  Logger().i('打印=----$consoleMessage');
                                },
                                onProgressChanged:
                                    (InAppWebViewController controller,
                                        int progress) {
                                  setState(() {
                                    this.progress = progress / 100;
                                  });
                                },

                                gestureRecognizers: <Factory<
                                    OneSequenceGestureRecognizer>>{
                                  Factory<OneSequenceGestureRecognizer>(
                                    () => ScaleGestureRecognizer(),
                                  ),
                                },
                                // onLoadStart: (controller, url) {
                                //   controller.injectCSSCode(
                                //       source:
                                //           ':root {touch-action: pan-x pan-y;height: 100%}');
                                // },
                                onLoadStop: (controller, url) {
                                  controller.evaluateJavascript(
                                      source:
                                          'document.getElementById("content_bottom_interaction").remove();');
                                },

                                onLoadStart: (controller, url) async {
                                  controller.evaluateJavascript(
                                      source:
                                          'document.getElementById("content_bottom_interaction").remove();');

                                  if (url!.rawValue.contains('weixin://')) {
                                    bool canLaunch = await canLaunchUrl(url);
                                    if (canLaunch) {
                                      _launchURL(url);
                                    } else {
                                      showToast('请安装微信');
                                    }
                                    controller.stopLoading();
                                    // showToast('当前跳转链接无法打开,自动返回');
                                    Future.delayed(
                                        const Duration(milliseconds: 200), () {
                                      webViewController?.goBack();
                                    });
                                  } else {
                                    controller.isLoading();
                                  }
                                },
                              )
                            : Container()
                        // : const BdhEmptyView(
                        //     emptyImgPath: 'assets/images/emptyNoInfoImg.png',
                        //     myTextColor: Color.fromRGBO(24, 66, 56, 0.4),
                        //   ),
                        // child: Expanded(child: inappweView),
                        )),
                Positioned(
                    bottom: 0,
                    // bottom: ScreenTool().bottomBarHeight,
                    child: Column(
                      children: [
                        BdhSegmentLine(width: 375.px),
                        Container(
                          color: Colors.white,
                          height: bottomViewHeight,
                          width: 375.px,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  //分享 按钮
                                  GestureDetector(
                                    onTap: () {
                                      _debounce.run(() async {
                                        gotoShareWechat();
                                      });
                                    },
                                    child: imgTextBtn(
                                        '${ditailModel.forwardCount ?? 0}',
                                        'reply.png',
                                        24.px,
                                        24.px),
                                  ),
                                  //点赞 按钮
                                  GestureDetector(
                                    onTap: () {
                                      // if (ditailModel.isLikeAndForward == 1) {
                                      // }
                                      photoArticalClickeLike();
                                    },
                                    child: imgTextBtn(
                                        '${ditailModel.likeCount ?? 0}',
                                        (ditailModel.isLike ?? 0) == 1
                                            ? 'like.png'
                                            : 'heart.png',
                                        24.px,
                                        24.px),
                                  )
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    )),
              ],
            );
          },
        ),
      ),
    );
  } // build

/*
//视频视图 方式一
  Widget videoPlayViewMethordOne() {
    return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        scrollDirection: Axis.vertical,
        itemCount: videoModelList.length,
        itemBuilder: (BuildContext context, int index) {
          VideoPlayModel model = videoModelList[index];
          return Container(
            margin: EdgeInsets.only(bottom: 20.px),
            // height: 200.px,
            // width: 375.px,
            // decoration: BoxDecoration(border: Border.all(width: 1)),
            child: LayoutBuilder(builder: (ctx, cons) {
              return Stack(
                children: [
                  GestureDetector(
                    onTap: () {
                      // model.controller.value.isPlaying
                      //     ? model.controller.pause()
                      //     : model.controller.play();
                      // model.isPlaying = model.controller.value.isPlaying;

                      for (int i = 0; i < videoModelList.length; i++) {
                        VideoPlayModel modelItem = videoModelList[i];
                        if (model.controller == modelItem.controller) {
                          model.controller.value.isPlaying
                              ? model.controller.pause()
                              : model.controller.play();
                          model.isPlaying = model.controller.value.isPlaying;
                        } else {
                          modelItem.controller.pause();
                          modelItem.isPlaying = false;
                        }
                      }
                      if (mounted) {
                        setState(() {});
                      }
                    },
                    child: Center(
                      child: (model.controller.value.isInitialized)
                          ? SizedBox(
                              width: 375.px,
                              height:
                                  375.px / model.controller.value.aspectRatio,
                              child: Stack(
                                children: [
                                  VideoPlayer(model.controller),
                                  Align(
                                    alignment: Alignment.center,
                                    child: Visibility(
                                        visible: !model.isPlaying,
                                        child: Image.asset(
                                            width: 60.px,
                                            ImageHelper.wrapAssets(
                                                "play.png"))),
                                  )
                                ],
                              ),
                            )
                          : Container(),
                    ),
                  ),
                  Positioned(
                    // top: (cons.maxHeight / 2) +
                    //     (375.px / model.controller.value.aspectRatio / 2) +
                    //     20.px,
                    top: 375.px / model.controller.value.aspectRatio - 40,
                    left: 147.5.px,
                    child: Offstage(
                      offstage: !model.isFunlScreen,
                      child: GestureDetector(
                        onTap: () {
                          model.controller.pause();
                          PhotoAticalModel photoAticalModel =
                              PhotoAticalModel();
                          photoAticalModel.videoUrl = model.videoUrl;
                          Navigator.of(context)
                              .push(MaterialPageRoute(builder: (ctx) {
                            return ContentVideoPlayerFullScreen(
                                model: photoAticalModel,
                                position: model.controller.value.position);
                          })).then((e) {
                            SystemChrome.setPreferredOrientations(
                                [DeviceOrientation.portraitUp]);
                            setState(() {});
                          });
                        },
                        child: Image.asset(
                            width: 80.px,
                            ImageHelper.wrapAssets("full_screen.png")),
                      ),
                    ),
                  ),
                ],
              );
            }),
          );
        });
  }

//视频视图 方式二
  Widget videoPlayViewMethordTwo() {
    return ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        scrollDirection: Axis.vertical,
        itemCount: videoList.length,
        itemBuilder: (BuildContext context, int index) {
          return Container(
            margin: EdgeInsets.only(bottom: 20.px),
            height: playViewMaxH,
            width: 375.px,
            child: LayoutBuilder(builder: (ctx, cons) {
              String playUrl = videoList[index];
              return VideoPlayerView(
                playUrl: playUrl,
                onVideoControllerInit: (videoController) {
                  currentVideoController = videoController;
                  setState(() {
                    playViewMaxH =
                        375.px / currentVideoController!.value.aspectRatio;
                  });
                },
              );
            }),
          );
        });
  }

*/

// 搜索视图
  Widget searchView() {
    return Container(
      padding: EdgeInsets.only(left: 5.px, right: 8.px),
      height: 32.px,
      width: 200.px,
      decoration: BoxDecoration(
        // border: Border.all(width: 1),
        borderRadius: BorderRadius.circular(21),
        color: BDHColor.black03,
      ),
      child: Row(
        children: [
          Image.asset(
            width: 24.px,
            height: 24.px,
            ImageHelper.wrapAssets('search_grey.png'),
          ),
          SizedBox(
            width: 5.px,
          ),
          Expanded(
            child: CupertinoTextField.borderless(
              style: TextStyle(
                fontSize: 13.px,
                color: HexColor('#000000'),
                fontWeight: FontWeight.w400,
              ),
              textInputAction: TextInputAction.search,
              padding: EdgeInsets.zero,
              controller: searchController,
              placeholder: "请输入搜索内容",
              placeholderStyle: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w500,
                  color: HexColor('#A8B8B1')),
              onTap: () {
                Logger().i(' textfiled  点击--onTap----');
              },
              onSubmitted: (value) {
                focusScopeNode.unfocus();
                if (value == '') {
                  findInteractionController.clearMatches();
                  setState(() {
                    textFound = '';
                  });
                } else {
                  findInteractionController.findAll(find: value);
                }
              },
            ),
          ),
          showUpDownIcon
              ? Text(
                  textFound,
                  style: TextStyle(
                    fontSize: 13.px,
                    color: HexColor('#000000'),
                    fontWeight: FontWeight.w400,
                  ),
                )
              : Container(),
          SizedBox(
            width: 5.px,
          ),
          showDeletIcon
              ? GestureDetector(
                  onTap: () {
                    searchController.text = '';
                    findInteractionController.clearMatches();
                    textFound = '';
                  },
                  child: Image.asset(
                      width: 16.px,
                      height: 16.px,
                      ImageHelper.wrapAssets('delteIcons.png')),
                )
              : Container(),
        ],
      ),
    );
  }

  // 搜索视图old
  Widget searchViewOld() {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            Navigator.of(context).push(CupertinoPageRoute(
              builder: (context) {
                return const SearchNewPage();
              },
            ));
          },
          child: Container(
            padding: EdgeInsets.only(left: 12.px, right: 12.px),
            height: 28.px,
            width: 230.px,
            decoration: BoxDecoration(
              // border: Border.all(width: 1),
              borderRadius: BorderRadius.circular(21),
              color: BDHColor.black03,
            ),
            child: Row(
              children: [
                Image.asset(
                  width: 24.px,
                  height: 24.px,
                  ImageHelper.wrapAssets('search_grey.png'),
                ),
                SizedBox(
                  width: 5.px,
                ),
                Text(
                  '请输入...',
                  style: TextStyle(
                      fontSize: 12.px,
                      color: HexColor('#A8B8B1'),
                      fontWeight: FontWeight.w400),
                )
              ],
            ),
          ),
        ),
        SizedBox(
          width: 10.px,
        ),
        Image.asset(
            width: 24.px,
            height: 24.px,
            ImageHelper.wrapAssets("bdh_chat.png")),
      ],
    );
  }

  Widget imgTextBtn(String title, String imgName, double w, double h) {
    return SizedBox(
      // decoration: BoxDecoration(border: Border.all(width: 1)),
      // height: 50.px,
      width: 100.px,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(width: w, height: h, ImageHelper.wrapAssets(imgName)),
          SizedBox(
            width: 2.px,
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.px,
              fontWeight: FontWeight.w400,
              color: BDHColor.black00,
            ),
          ),
        ],
      ),
    );
  }

  // 标题, 时间, 作者 view
  Widget getTitleTimeAuthorView() {
    return Column(
      children: [
        //标题
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              child: Text(
                textAlign: TextAlign.left,
                ditailModel.title ?? '',
                // maxLines: 1,
                overflow: TextOverflow.clip,
                style: TextStyle(fontSize: 20.px, fontWeight: FontWeight.w600),
              ),
            )
          ],
        ),
        SizedBox(height: 10.px),
        //作者
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(
              textAlign: TextAlign.left,
              // '${ditailModel.authorName ?? ''}',
              ditailModel.sourceName ?? '',
              maxLines: 1,
              overflow: TextOverflow.clip,
              style: TextStyle(
                  color: BDHColor.black04,
                  fontSize: 10.px,
                  fontWeight: FontWeight.w400),
            ),
            SizedBox(width: 5.px),
            //时间
            Text(
              textAlign: TextAlign.left,
              ditailModel.publishDate ?? '',
              maxLines: 1,
              overflow: TextOverflow.clip,
              style: TextStyle(
                  color: BDHColor.black04,
                  fontSize: 10.px,
                  fontWeight: FontWeight.w400),
            )
          ],
        ),
      ],
    );
  }

//微信分享
  gotoShareWechat() {
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        useSafeArea: true,
        context: context,
        builder: (ctx) {
          return BdhShareView(
            shareWechatCallBack: () async {
              if (await isWXInstalled()) {
                if (kDebugMode) {
                  print('点击微信');
                }
                //分享后打开的图文连接
                String linkUrl = "";
                if ((widget.model.sourceType ?? '') == '2') {
                  // linkUrl = '${ditailModel.content ?? ''}&inApp=0'; //0显示打开
                  linkUrl = ditailModel.content ??
                      ''; //备注: 2025/05/13, 何老师让把转载分享到微信的 inapp参数删除
                } else {
                  String urlStr =
                      "${urlConfig.h5}/argic/demo?contentUid=${ditailModel.uid}&inApp=0";
                  linkUrl = urlStr;
                }

                // String imageUrl = '';
                // if (widget.model.imageList != null &&
                //     widget.model.imageList!.isNotEmpty) {
                //   imageUrl =
                //       "${urlConfig.microfront}${widget.model.imageList!.first}";
                // }
                String imageUrl = '';
                if (widget.model.image != null && widget.model.image != "") {
                  imageUrl =
                      "${urlConfig.microfront}${widget.model.image!.split(",").first}";
                }

                if (imageUrl.isEmpty || imageUrl == "") {
                  CompressUtil.assetToUint8List(
                          ImageHelper.wrapAssets('defaulSharetImg.png'))
                      .then((endData) {
                    /// 分享到好友
                    var model = WeChatShareWebPageModel(
                      //链接
                      linkUrl,
                      //标题
                      title: ditailModel.title ?? '',
                      //摘要
                      description: "点击查看详情",
                      // //小图
                      // thumbnail: WeChatImage.network(
                      //     "${urlConfig.microfront}${widget.model.image}"),
                      // thumbnail: WeChatImage.network(imageUrl),
                      // thumbData: await HomeService.getImageData(imageUrl),
                      thumbData: endData,
                      //微信消息
                      scene: WeChatScene.session,
                    );

                    fluwx.share(model).then((res) {
                      if (res) {
                        BDHResponsitory.shareToWeChat({
                          "contentUid": ditailModel.uid,
                          "contentType": ditailModel.contentType,
                          "forwardClick": 1
                        }).then((endData) {
                          Future.delayed(const Duration(seconds: 5), () {
                            getDital();
                          });
                          if (kDebugMode) {
                            print('分享完成');
                          }
                        });
                      }
                    });
                  });
                } else {
                  HomeService.getImageData(imageUrl).then((res) {
                    CompressUtil.u8ToU8(res ?? Uint8List(0)).then((endData) {
                      /// 分享到好友
                      var model = WeChatShareWebPageModel(
                        //链接
                        linkUrl,
                        //标题
                        title: ditailModel.title ?? '',
                        //摘要
                        description: "点击查看详情",
                        // //小图
                        // thumbnail: WeChatImage.network(
                        //     "${urlConfig.microfront}${widget.model.image}"),
                        // thumbnail: WeChatImage.network(imageUrl),
                        // thumbData: await HomeService.getImageData(imageUrl),
                        thumbData: endData,
                        //微信消息
                        scene: WeChatScene.session,
                      );

                      fluwx.share(model).then((res) {
                        if (res) {
                          BDHResponsitory.shareToWeChat({
                            "contentUid": ditailModel.uid,
                            "contentType": ditailModel.contentType,
                            "forwardClick": 1
                          }).then((endData) {
                            Future.delayed(const Duration(seconds: 5), () {
                              getDital();
                            });
                            if (kDebugMode) {
                              print('分享完成');
                            }
                          });
                        }
                      });
                    });
                  });
                }
              }
            },
            shareWechatMomentsCallBack: () async {
              if (kDebugMode) {
                print('点击微信朋友圈');
              }

              if (await isWXInstalled()) {
                //分享后打开的图文连接
                String linkUrl = "";
                if ((widget.model.sourceType ?? '') == '2') {
                  // linkUrl = '${ditailModel.content ?? ''}&inApp=0';
                  linkUrl = ditailModel.content ??
                      ''; //备注: 2025/05/13, 何老师让把转载分享到微信的 inapp参数删除
                } else {
                  String urlStr =
                      "${urlConfig.h5}/argic/demo?contentUid=${ditailModel.uid}&inApp=0";
                  linkUrl = urlStr;
                }
                //分享的小图片
                String imageUrl = '';
                if (widget.model.image != null && widget.model.image != "") {
                  imageUrl =
                      "${urlConfig.microfront}${widget.model.image!.split(",").first}";
                }

                if (imageUrl.isEmpty || imageUrl == "") {
                  CompressUtil.assetToUint8List(
                          ImageHelper.wrapAssets('defaulSharetImg.png'))
                      .then((endData) {
                    /// 分享到好友
                    var model = WeChatShareWebPageModel(
                      //链接
                      linkUrl,
                      //标题
                      title: ditailModel.title ?? '',
                      //摘要
                      description: "点击查看详情",
                      //小图
                      // thumbnail: WeChatImage.network(imageUrl),
                      // thumbData: await HomeService.getImageData(imageUrl),
                      thumbData: endData,
                      //微信消息
                      scene: WeChatScene.timeline,
                    );

                    fluwx.share(model);
                    BDHResponsitory.shareToWeChat({
                      "contentUid": ditailModel.uid,
                      "contentType": ditailModel.contentType,
                      "forwardClick": 1
                    }).then((res) {
                      Future.delayed(const Duration(seconds: 5), () {
                        getDital();
                      });
                      if (kDebugMode) {
                        print('分享完成');
                      }
                    });
                  });
                } else {
                  HomeService.getImageData(imageUrl).then((res) {
                    CompressUtil.u8ToU8(res ?? Uint8List(0)).then((endData) {
                      /// 分享到好友
                      var model = WeChatShareWebPageModel(
                        //链接
                        linkUrl,
                        //标题
                        title: ditailModel.title ?? '',
                        //摘要
                        description: "点击查看详情",
                        //小图
                        // thumbnail: WeChatImage.network(imageUrl),
                        // thumbData: await HomeService.getImageData(imageUrl),
                        thumbData: endData,
                        //微信消息
                        scene: WeChatScene.timeline,
                      );

                      fluwx.share(model);
                      BDHResponsitory.shareToWeChat({
                        "contentUid": ditailModel.uid,
                        "contentType": ditailModel.contentType,
                        "forwardClick": 1
                      }).then((res) {
                        Future.delayed(const Duration(seconds: 5), () {
                          getDital();
                        });
                        if (kDebugMode) {
                          print('分享完成');
                        }
                      });
                    });
                  });
                }
              }
            },
          );
        });
  }

  //net: 详情 点赞
  photoArticalClickeLike() {
    HomeService.photoArticalClickeLike({
      "contentType": ditailModel.contentType ?? '1',
      "contentUid": ditailModel.uid ?? 0,
      "state": (ditailModel.isLike ?? 0) == 1 ? 0 : 1,
    }).then((res) {
      Logger().i(' 详情 点赞= ');
      Logger().i(res);
      if (res['code'] == 0 && res['msg'] == 'success') {
        getDital();
      }
    });
  }

  //net: 详情 contentUids
  getDital() {
    HomeService.getDetail(widget.model.contentUId ?? '').then((result) {
      // Logger()
      //     .i(' 详情res= widget.model.contentUId=${widget.model.contentUId}');
      Logger().i(result);
      if (result['code'] == 0 && result['msg'] == 'success') {
        PhotoAticalModel model = PhotoAticalModel.fromJson(result['data']);
        // Logger().i(' 详情res= ditailModel.uid=${model.uid}-----');
        setState(() {
          ditailModel = model;
        });
        if ((widget.model.sourceType ?? '') == '2') {
          // controller.loadRequest(Uri.parse(ditailModel.content ?? ''));
          // webViewController?.loadUrl(
          //     urlRequest: URLRequest(url: WebUri(ditailModel.content ?? "")));
          // String urlStr = '${ditailModel.content}&inApp=1';
          // ditailModel.content = urlStr; // 10.17通高湛/袁振确认 转载不需要加上inApp=1
          webViewController?.reload();
        } else {
          // String urlStr =
          //     'http://************:30918/argic/demo?contentUid=186202408291723315318';
//  http://localhost:8080/argic/demo?contentUid=86202409181622352580&inApp=0
          String urlStr =
              "${urlConfig.h5}/argic/demo?contentUid=${ditailModel.uid}&inApp=1"; // inApp这个字段传1就不展示了
          // controller.loadRequest(Uri.parse(urlStr));
          ditailModel.content = urlStr;
        }
        Logger().i(' -----------详情begin--------------');
        Logger().i(' 详情sourceType=  ${widget.model.sourceType}');
        Logger().i(' 详情content=  ${ditailModel.content ?? ''}');
        Logger().i(' -----------详情end--------------');
        setState(() {});
      }
    });
  }
/*
  Future<VideoPlayerController> getVideoPlayVC(String videoUrl) async {
    Logger().i("${urlConfig.microfront}$videoUrl");
    VideoPlayerController controller = VideoPlayerController.networkUrl(
        Uri.parse("${urlConfig.microfront}$videoUrl"));
    await controller.initialize().onError((a, b) {}).then((_) {
      controller.setLooping(true);
      controller.pause();
    });
    return controller;
  }

  //get video
  getVideoSourceMethordOne(String htmlString) async {
    RegExp regExp = RegExp(r'src="([^"]*\.mp4)"');
    // 使用RegExp的allMatches方法获取所有匹配项
    Iterable<Match> matches = regExp.allMatches(htmlString);
    List<String> tempVideoList = [];
    List<VideoPlayModel> tempVideoModelList = [];
    List<Future<VideoPlayerController>> playerVcList = [];
    // 遍历匹配项并打印结果
    for (var match in matches) {
      String videoUrl = match.group(1)!;
      tempVideoList.add(videoUrl);
    }

    for (int i = 0; i < tempVideoList.length; i++) {
      String urlStr = tempVideoList[i];
      playerVcList.add(getVideoPlayVC(urlStr));
    }

    // 等待所有异步任务完成，并收集结果
    List<VideoPlayerController> results = await Future.wait(playerVcList);
    for (int i = 0; i < results.length; i++) {
      String videoStr = '';
      if (i < tempVideoList.length) {
        videoStr = tempVideoList[i];
      }
      VideoPlayerController vc = results[i];
      VideoPlayModel model = VideoPlayModel(
        controller: vc,
        showFullScreen: vc.value.aspectRatio > (4 / 3) ? true : false,
        isFunlScreen: true,
        isPlaying: false,
        videoUrl: videoStr,
      );
      tempVideoModelList.add(model);
    }

    setState(() {
      videoList = tempVideoList;
      videoModelList = tempVideoModelList;
    });
  }

//方式2
  getVideoSourceMethordTwo(String htmlString) {
    RegExp regExp = RegExp(r'src="([^"]*\.mp4)"');
    Iterable<Match> matches = regExp.allMatches(htmlString);
    List<String> tempVideoList = [];
    // List<VideoPlayModel> tempVideoModelList = [];
    // List<Future<VideoPlayerController>> playerVcList = [];
    // 遍历匹配项并打印结果
    for (var match in matches) {
      String videoUrl = match.group(1)!;
      tempVideoList.add(videoUrl);
    }
    setState(() {
      videoList = tempVideoList;
    });
  }

  */
} // state

/*
class VideoPlayModel {
  VideoPlayerController controller;
  bool showFullScreen = false;
  bool isFunlScreen = false;
  bool isPlaying = false;
  String videoUrl = '';
  VideoPlayModel({
    required this.controller,
    required this.showFullScreen,
    required this.isFunlScreen,
    required this.isPlaying,
    required this.videoUrl,
  });
}
*/
//----------------------视频播放器子组件-------------------------
/*
class VideoPlayerView extends StatefulWidget {
  final String playUrl;
  final Function(VideoPlayerController) onVideoControllerInit;
  const VideoPlayerView(
      {super.key, required this.playUrl, required this.onVideoControllerInit});

  @override
  State<VideoPlayerView> createState() => _VideoPlayerViewState();
}

class _VideoPlayerViewState extends State<VideoPlayerView> {
  VideoPlayerController? _controller;
  bool showFullScreen = false;
  bool isFollow = false;
  bool isFunlScreen = false;
  bool isPlaying = false;
  String? watchStartTime;

  @override
  void initState() {
    super.initState();
    if (widget.playUrl.isNotEmpty) {
      //初始化播放器
      _controller = VideoPlayerController.networkUrl(
          Uri.parse("${urlConfig.microfront}${widget.playUrl}"))
        ..initialize().onError((a, b) {
          debugPrint(b.toString());
        }).then((_) {
          widget.onVideoControllerInit(_controller!);
          if (_controller!.value.aspectRatio > (4 / 3)) {
            showFullScreen = true; //视频宽高比大于4/3显示全屏按钮
          }
          _controller!.setLooping(true);
          _controller!.play();
          isPlaying = true;
          watchStartTime =
              DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
          if (mounted) {
            setState(() {});
          }
        });
      //加载视频详情
      // loadDetail();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: LayoutBuilder(builder: (ctx, cons) {
        return Stack(
          children: [
            GestureDetector(
              onTap: () {
                _controller!.value.isPlaying
                    ? _controller?.pause()
                    : _controller?.play();
                isPlaying = _controller!.value.isPlaying;
                if (mounted) {
                  setState(() {});
                }
              },
              child: Center(
                child: (_controller != null && _controller!.value.isInitialized)
                    ? SizedBox(
                        width: 375.px,
                        height: 375.px / _controller!.value.aspectRatio,
                        child: Stack(
                          children: [
                            VideoPlayer(_controller!),
                            Align(
                              alignment: Alignment.center,
                              child: Visibility(
                                  visible: !isPlaying,
                                  child: Image.asset(
                                      width: 60.px,
                                      ImageHelper.wrapAssets("play.png"))),
                            )
                          ],
                        ),
                      )
                    : Container(),
              ),
            ),
            Positioned(
              // top: (cons.maxHeight / 2) +
              //     (375.px / _controller!.value.aspectRatio / 2) +
              //     20.px,
              top: 375.px / _controller!.value.aspectRatio - 40,
              left: 147.5.px,
              child: Offstage(
                offstage: !showFullScreen,
                // offstage: false,
                child: GestureDetector(
                  onTap: () {
                    _controller!.pause();
                    PhotoAticalModel photoAticalModel = PhotoAticalModel();
                    photoAticalModel.videoUrl = widget.playUrl;
                    Navigator.of(context)
                        .push(MaterialPageRoute(builder: (ctx) {
                      return ContentVideoPlayerFullScreen(
                          model: photoAticalModel,
                          position: _controller!.value.position);
                    })).then((e) {
                      SystemChrome.setPreferredOrientations(
                          [DeviceOrientation.portraitUp]);
                      setState(() {});
                    });
                  },
                  child: Image.asset(
                      width: 80.px, ImageHelper.wrapAssets("full_screen.png")),
                ),
              ),
            ),
          ],
        );
      }),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _controller?.dispose();
  }
}

*/

