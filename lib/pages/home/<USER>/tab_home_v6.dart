import 'dart:io';
import 'package:bdh_smart_agric_app/components/bdh_tag.dart';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/model/guide_page_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/user_org_code_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/bordercast_view/home_broadcast_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/news_subview/bdh_text_scaler.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/model/channel_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/model/sub_company_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/news_common_list/news_common_list_v6.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/underline_indicator.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/guide_view/move_animation.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/guide_view/move_animation_bottom.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/guide_view/scale_animation.dart';
import 'package:bdh_smart_agric_app/pages/product/landcontract/sign/bdh_signed_soil_test_formula_page.dart';
import 'package:bdh_smart_agric_app/pages/product/weather/weather_page.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/cover_tool.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/gps/gps_receiver.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/request/home_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_tool.dart';
import 'package:bdh_smart_agric_app/viewmodel/font_scale_model.dart';
import 'package:bdh_smart_agric_app/viewmodel/user_model.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gif/gif.dart';
import 'package:logger/web.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../components/jh_cascade_tree_picker.dart';
import '../../../utils/request/bdh_land_contract_service.dart';
import '../../../utils/storage_util.dart';
import '../../version/bdh_newversion_view.dart';
import '../news/bordercast_view/home_fertilize_notice_view.dart';
import '../news/model/weather_model.dart';
import '../video/components/underline_indicator_scalefont.dart';

class TabHomePageV6 extends StatefulWidget {
  const TabHomePageV6({super.key});

  @override
  State<StatefulWidget> createState() => _TabHomePageV6State();
}

class _TabHomePageV6State extends State<TabHomePageV6>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  OrgTreeResult? treeResult;
  Map<dynamic, dynamic>? currentOrg;
  List<OrgModelV2> orgModelV2List = [];
  bool isTheSameOrgCode = true;

  @override
  bool get wantKeepAlive => true;
  TabController? _tabController;
  List<ChannelModel> channelModelList = []; // 频道
  bool bigfont = false;
  double fontScale = 1.0;
  bool showFloatingBtn = false;
  bool showChooseFarmView = false;
  OrgModelV2? currentOrgModel;
  String farmName = '';
  WeatherModel? weatherModel;
  double showToolBarWeatherViewOpacity = 0.0;
  final ScrollController _scrollController = ScrollController();
  bool finishedShowGuide = true;
  final _pageController = PageController();
  List guideImgList = [
    GuidePageModel(
        index: 0,
        imgUrl: 'home002.png',
        tipInfo: '通过左右滑动可查看更多北大荒集团下属单位的新闻资讯',
        tipInfoTopPostion: 300.px),
    GuidePageModel(
        index: 1,
        imgUrl: 'home002.png',
        tipInfo: '点击可切换此单位内容频道，\n更多资讯供您选择',
        tipInfoTopPostion: 320.px),
    GuidePageModel(
        index: 2,
        imgUrl: 'home001.png',
        tipInfo: '点击可选择农场，更多内容频道等您发现，开启数字北大荒新体验吧',
        tipInfoTopPostion: 200.px),
  ];
  late GifController controllerGif;
  num defalutLatitude = 45.76091999999998;
  num defalutLongitude = 126.63125300000002;
  // String address = '哈尔滨市';
  String address = kDefaultCity;
  bool showlocate = false;
  PackageInfo packageInfo = PackageInfo(
    appName: 'Unknown',
    packageName: 'Unknown',
    version: 'Unknown',
    buildNumber: 'Unknown',
    buildSignature: 'Unknown',
    installerStore: 'Unknown',
  );

  @override
  void initState() {
    super.initState();

    controllerGif = GifController(vsync: this);

    getWeather();
    getOrgListTreeNew();
    // getFertilizeNotice();

    bus.on("upDateOrg", busUpDateOrg);
    bus.on("location", busLocation);
    bus.on("locationError", busLocationError);

    _scrollController.addListener(() {
      double postion = _scrollController.offset;
      setState(() {
        showToolBarWeatherViewOpacity =
            // (postion / 60) > 1.0 ? 1.0 : postion / 60;
            // (postion / 35) > 1.0 ? 1.0 : 0;
            (postion / 45) < 1.0 ? 0 : 1;
      });
      bus.emit("ListScrolled", postion);
    });
    // setState(() {
    //   finishedShowGuide = false;
    // });
    // saveGuideViewFinshedState();

    readGuideViewFinshedState().then((res) {
      if (!res) {
        Future.delayed(const Duration(seconds: 1), () {
          showDialog(
              barrierDismissible: false,
              context: context,
              builder: (ctx) {
                return guideView();
              });
        });
      } else {
        // versionUpdate();
        showServiceView();
      }
    });
    NativeUtil.getParamsFromNative(context);
    // Future.delayed(const Duration(seconds: 6), () {
    //   PermissionUtil.requestNotificationPermission().then((res) {
    //     if (res) {
    //       NativeUtil.initUmengPush({});
    //     }
    //   });
    // });
  }

  void busUpDateOrg(e) {
    if (!mounted) {
      return;
    }
    getTreeChannel();
  }

  void busLocation(e) {
    if (!mounted) {
      return;
    }
    setState(() {
      showlocate = false;
    });
    LocationResult locationResult = e as LocationResult;
    getWeatherData(locationResult.latitude ?? 0, locationResult.longitude ?? 0,
        locationResult.addressCity ?? '哈尔滨市');
  }

  void busLocationError(e, {bool force = false}) {
    if (!mounted) {
      return;
    }

    Map<String, Object> result = e;
    if (Platform.isAndroid && result['errorCode'] == 12) {
      showNeedLocationPermissionDialog(force: force);
    }
  }

  showServiceView() {
    String userOrgCode = StorageUtil.orgCode() ?? '';
    GlobalServiceView.orgCode = userOrgCode;
    GlobalServiceView.needShowServiceBtn('home');
  }

  versionUpdate() {
    bool? haveShowHomeBroadcaseView =
        StorageManager.sharedPreferences?.getBool('haveShowHomeBroadcaseView');
    bool isBefore20250131 = FormatTool().isBefore20250131();
    Logger().i(isBefore20250131);
    if (Platform.isAndroid) {
      Future.delayed(const Duration(seconds: 2), () {
        GetCurrentInstallVersion.check(context: context, needShowDialog: true)
            .then((res) {
          Logger().i('res = $res');
          bool haveNewVersion = res['haveNewVersion'];
          if (isBefore20250131 &&
              !haveNewVersion &&
              !(haveShowHomeBroadcaseView ?? false)) {
            Future.delayed(const Duration(seconds: 1), () {
              showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (ctx) {
                    return PopScope(
                        canPop: false,
                        child: Center(
                          child: HomeBroadcastView(clickedClose: () {
                            StorageManager.sharedPreferences!
                                .setBool('haveShowHomeBroadcaseView', true);
                          }),
                        ));
                  });
            });
          }
        });
      });
    } else {
      if (isBefore20250131 && !(haveShowHomeBroadcaseView ?? false)) {
        Future.delayed(const Duration(seconds: 1), () {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (ctx) {
                return PopScope(
                    canPop: false,
                    child: Center(
                      child: HomeBroadcastView(clickedClose: () {
                        StorageManager.sharedPreferences!
                            .setBool('haveShowHomeBroadcaseView', true);
                      }),
                    ));
              });
        });
      }
    }
  }

  check() async {
    await BDHResponsitory.getVersionInfo().then((result) {
      // if (int.parse(result.data!.build!) > 105) {
      if (result.data?.updateType == '01' || result.data?.updateType == '02') {
        int systemBuildNumber = int.parse(packageInfo.buildNumber);
        // systemBuildNumber = 2;
        if (int.parse(result.data!.build!) >= systemBuildNumber) {
          Future.delayed(const Duration(seconds: 1), () {
            showDialog(
                context: context,
                barrierDismissible: false,
                builder: (ctx) {
                  return PopScope(
                      // canPop: false,
                      child: Center(
                    child: BdhNewVersionView(
                      versionResult: result,
                    ),
                  ));
                });
          });
        }
      }
    });
  }

  checkUserOrg() {
    bool showLeaveMsgBtn = false;
    String userOrgCode = StorageUtil.orgCode() ?? '';
    String selcetedOrgCode = context.read<UserModel>().currentOrg!["orgCode"];
    showLeaveMsgBtn =
        UserOrgCodeModel.checkUserOrg(userOrgCode, selcetedOrgCode);
    setState(() {
      isTheSameOrgCode = showLeaveMsgBtn;
    });
  }

  changeTabAction() {
    if (_tabController != null) {
      _tabController!.addListener(() {
        showFloatingBtn = false;
        int index = _tabController!.index;
        // Logger().i('切换tabbar----------------------$index');
        dealWithChangeTabBarAction(index);
      });
    }
  }

  dealWithChangeTabBarAction(int index) {
    showFloatingBtn = false;
    if (index < orgModelV2List.length) {
      OrgModelV2 model = orgModelV2List[index];
      BDHResponsitory.saveMenuUse({
        "menuCode": model.orgCode,
        "menuName": model.orgName,
        "menuType": "1", //组织机构类型
      }).then((res) {
        Log.i(res.msg);
      });
      if (model.children!.isNotEmpty) {
        // farmName = '选择农场';
        farmName = model.children!.first.orgName ?? '';
      }
      setState(() {
        currentOrgModel = model;
        showChooseFarmView = model.children!.isEmpty ? false : true;
      });
      saveOrgCode(model.orgCode ?? '');
      reSetFarmModelSelction(model);
      checkUserOrg();
    }
  }

  saveOrgCode(String selectedOrgCode) {
    Map currentOrg = {"orgCode": selectedOrgCode};
    context.read<UserModel>().setCurrentOrg(currentOrg);
    bus.emit("orgChange", currentOrg);
  }

  selectedFarmModelCallBack(OrgModelV2 model) {
    saveOrgCode(model.orgCode ?? '');
    if (model.isSelected ?? false) {
      farmName = model.orgName ?? '';
    } else {
      farmName = '选择农场';
    }
    checkUserOrg();
    bus.emit('SelectdFarmAction', model);
  }

  reSetFarmModelSelction(OrgModelV2 model) {
    for (int i = 0; i < model.children!.length; i++) {
      OrgModelV2 itemModel = model.children![i];
      itemModel.isSelected = false;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    double fontScaleA = context.watch<FontScaleModel>().fontScale;
    setState(() {
      fontScale = fontScaleA;
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(
      children: [
        Positioned(
          child: SizedBox(
            height: ScreenTool().screenHeight,
            width: 375.px,
            child: Image.asset(
              height: 448.px,
              width: 375.px,
              ImageHelper.wrapAssets('homePageBGImg_blue.png'),
              fit: BoxFit.cover,
            ),
          ),
        ),
        Positioned.fill(
          child: Scaffold(
            backgroundColor: BDHColor.kClear,
            body: NotificationListener<ShowFloatBtnNotifiationV6>(
              onNotification: (res) {
                // Logger().i('====================================');
                // Logger().i(res.showFloatBtn);
                Future.delayed(const Duration(milliseconds: 100), () {
                  setState(() {
                    showFloatingBtn = res.showFloatBtn;
                  });
                });
                return true;
              },
              child: NestedScrollView(
                controller: _scrollController,
                headerSliverBuilder:
                    (BuildContext context, bool innerBoxIsScrolled) {
                  return <Widget>[
                    _buildHeader(context, innerBoxIsScrolled),
                  ];
                },
                //SliverFillRemaining
                body: orgModelV2List.isEmpty
                    ? Container()
                    : TabBarView(
                        controller: _tabController,
                        children: orgModelV2List.map((model) {
                          return NewsCommonListV6(
                            orgModelV2: model,
                            tabController: _tabController!,
                            fontScale: fontScale,
                            checkUserOrgCallBack: (res) {
                              setState(() {
                                isTheSameOrgCode = res;
                              });
                            },
                          );
                        }).toList(),
                      ),
              ),
            ),
            floatingActionButton: showFloatingBtn && isTheSameOrgCode
                ? GestureDetector(
                    onTap: () {
                      Navigator.of(context)
                          .pushNamed('leaveMessagePage')
                          .then((res) {
                        GlobalServiceView.needShowServiceBtn('home');
                        bus.emit('RefeshLeaveMsg', '');
                      });
                      // Navigator.of(context)
                      //     .push(CupertinoPageRoute(builder: (context) {
                      //   return const LeaveMessagePage();
                      // })).then((res) {
                      //   bus.emit('RefeshLeaveMsg', '');
                      // });
                    },
                    child: ClipOval(
                      child: Container(
                        height: 60.px,
                        width: 60.px,
                        decoration: BoxDecoration(
                          // border: Border.all(width: 1),
                          color: HexColor('#16B760'),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image.asset(
                              width: 24.px,
                              height: 24.px,
                              ImageHelper.wrapAssets('pass-fill.png'),
                            ),
                            Text(
                              '留言',
                              textScaler:
                                  BdhTextScaler(textScaleFactor: fontScale),
                              style: TextStyle(
                                  fontSize: 14.px,
                                  fontWeight: FontWeight.w400,
                                  color: HexColor('#ffffff')),
                            )
                          ],
                        ),
                      ),
                    ),
                  )
                : Container(),
          ),
        ),
      ],
    );
  }

  // 写:本地存储
  void saveGuideViewFinshedState() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.setBool('finishedShowHomeGuide', finishedShowGuide);
  }

  //读:本地存储
  Future<bool> readGuideViewFinshedState() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool? finishedShowHomeGuide = preferences.getBool('finishedShowHomeGuide');
    bool res = finishedShowHomeGuide ?? false;
    // return res;
    return true;
  }

//跳过引导 或 开始体验
  finishGuidePage() {
    setState(() {
      finishedShowGuide = true;
    });
    saveGuideViewFinshedState();
    Navigator.of(context).pop();
    versionUpdate();
    showServiceView();
  }

//下一页
  goToNextPage(int index) {
    int movePage = index + 1 > guideImgList.length - 1
        ? guideImgList.length - 1
        : index + 1;
    _pageController.animateToPage(movePage,
        duration: const Duration(milliseconds: 500),
        curve: Curves.fastOutSlowIn);
  }

//引导页
  Widget guideView() {
    return Container(
      color: BDHColor.black06,
      height: ScreenTool().screenHeight,
      width: 375.px,
      child: PageView.builder(
          physics: const NeverScrollableScrollPhysics(),
          controller: _pageController,
          itemCount: guideImgList.length,
          itemBuilder: (ctx, index) {
            GuidePageModel model = guideImgList[index];
            return Stack(
              children: [
                Image.asset(
                  fit: BoxFit.cover,
                  width: 375.px,
                  height: ScreenTool().screenHeight,
                  ImageHelper.wrapGuideAssets(model.imgUrl),
                ),
                //------button: 跳过引导------
                Positioned(
                  bottom: 90.px,
                  left: 40.px,
                  child: index == guideImgList.length - 1
                      ? Container()
                      : GestureDetector(
                          onTap: () {
                            finishGuidePage(); //跳过引导
                          },
                          child: Container(
                            width: 135.px,
                            height: 40.px,
                            padding: EdgeInsets.only(
                                left: 10.px,
                                right: 10.px,
                                top: 5.px,
                                bottom: 5.px),
                            decoration: BoxDecoration(
                                color: const Color.fromRGBO(255, 255, 255, 0.1),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(2.px)),
                                border: Border.all(
                                    color: const Color.fromRGBO(
                                        255, 255, 255, 0.4))),
                            child: Center(
                              child: Text(
                                "跳过引导",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400),
                              ),
                            ),
                          ),
                        ),
                ),
                //------button: 下一步 or 开始体验------
                Positioned(
                  bottom: index == guideImgList.length - 1
                      ? Platform.isIOS
                          ? (ScreenTool().screenHeight -
                              ScreenTool().statusBarHeight -
                              95.px)
                          : 460.px
                      : 90.px,
                  right: index == guideImgList.length - 1
                      ? ((375 - 135) * 0.5).px
                      : 40.px,

                  // bottom: index == guideImgList.length - 1
                  //     ? Platform.isIOS
                  //         ? 400.px
                  //         : 460.px
                  //     : 90.px,
                  // right: index == guideImgList.length - 1
                  //     ? ((375 - 135) * 0.5).px
                  //     : 40.px,
                  child: GestureDetector(
                    onTap: () {
                      if (index == guideImgList.length - 1) {
                        finishGuidePage(); //跳过引导 完成引导
                      } else {
                        goToNextPage(index); //点击进入下一页
                      }
                    },
                    child: Container(
                      width: 135.px,
                      height: 40.px,
                      padding: EdgeInsets.only(
                          left: 10.px, right: 10.px, top: 5.px, bottom: 5.px),
                      decoration: BoxDecoration(
                          color: const Color.fromRGBO(255, 255, 255, 0.1),
                          borderRadius: BorderRadius.all(Radius.circular(2.px)),
                          border: Border.all(
                              color: const Color.fromRGBO(255, 255, 255, 0.4))),
                      child: Center(
                        child: Text(
                          index == guideImgList.length - 1 ? "开启体验" : "下一步",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
                    ),
                  ),
                ),

                //提示文字
                Positioned(
                  top: model.tipInfoTopPostion,
                  left: 50.px,
                  child: Container(
                      decoration: const BoxDecoration(
                          // border: Border.all(width: 1, color: Colors.white),
                          ),
                      height: 60.px,
                      width: 285.px,
                      child: Text(model.tipInfo,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: Colors.white,
                              height: 1.5,
                              fontSize: 18.px,
                              fontWeight: FontWeight.w400))),
                ),

                //分公司: 跑马灯动画
                index == 0
                    ? Positioned(
                        top: 50.px,
                        left: 0,
                        child: SizedBox(
                          height: 100.px,
                          width: 375.px,
                          child: Gif(
                            // fps: 30,
                            // duration: Duration(seconds: 4),
                            controller: controllerGif,
                            autostart: Autostart.loop,
                            image: const AssetImage(
                                'assets/images/led_company.gif'),
                          ),
                        ),
                      )
                    : index == 1
                        ? Positioned(
                            top: 130.px,
                            left: 0,
                            child: Container(
                              decoration:
                                  const BoxDecoration(color: Colors.white),
                              height: 40.px,
                              width: 375.px,
                              child: Gif(
                                // fps: 30,
                                // duration: Duration(seconds: 4),
                                controller: controllerGif,
                                autostart: Autostart.loop,
                                image: const AssetImage(
                                    'assets/images/led_company_channel.gif'),
                              ),
                            ),
                          )
                        : Container(),

                //

                //手指动画
                (index == 0 || index == 1)
                    ? Positioned(
                        top: index == 0
                            ? 140.px
                            : index == 1
                                ? 180.px
                                : 80.px,
                        left: (375.px - 160.px) * 0.5,
                        child: SizedBox(
                          height: 80.px,
                          width: 80.px,
                          child: MoveAnimation(
                              viewW: 80.px,
                              imgName: 'finger_point_left_up.png',
                              duration: const Duration(
                                milliseconds: 900,
                              ),
                              offsetX: Offset.zero,
                              offsetY: Offset((80.px * 2) / 100, 0)),
                        ),
                      )
                    : Positioned(
                        top: Platform.isIOS ? 90.px : 120.px,
                        left: (375.px - 160.px) * 0.5,
                        child: SizedBox(
                            height: 80.px,
                            width: 80.px,
                            child: const ScaleAnimation(
                              imgName: 'finger_point_left_up_scale.png',
                              duration: Duration(milliseconds: 900),
                              begin: 0.8,
                              end: 1.2,
                            )),
                      ),

                index == 2
                    ? Positioned(
                        bottom: Platform.isIOS ? -500.px : -430.px,
                        left: 0,
                        child: const SizedBox(
                          // height: 100.px,
                          // width: 375.px,
                          child: MoveAnimationBottom(
                            imgName: 'guideChooseFarm.png',
                            duration: Duration(milliseconds: 800),
                            offsetX: Offset(0, 0),
                            offsetY: Offset(0, -1),
                          ),
                        ),
                      )
                    : Container(),

                Platform.isIOS && index == guideImgList.length - 1
                    ? Positioned(
                        top: 0,
                        left: 0,
                        child: GestureDetector(
                          onTap: () {
                            finishGuidePage(); //跳过引导 完成引导
                          },
                          child: Container(
                            height: 100.px,
                            width: 375.px,
                            color: Colors.transparent,
                          ),
                        ),
                      )
                    : Container(),
              ],
            );
          }),
    );
  }

  requestUserLoctionPermission() {
    PermissionUtil.requestLocationPermission(
            // context, "您所处位置的天气信息")
            context,
            "以便为您提供所处位置的天气信息")
        .then((res) {
      if (Platform.isIOS) {
        if (res == true) {
          GpsReceiver receiver = GpsReceiver.getInstance();
          receiver.start(true);
        } else {
          return BrnDialogManager.showConfirmDialog(context,
              title: "提示",
              cancel: '取消',
              confirm: '确定',
              message: "需要您开启定位权限, 是否去开启定位权限？", onConfirm: () {
            Navigator.of(context).pop();
            openAppSettings();
          }, onCancel: () {
            Navigator.of(context).pop();
          });
        }
      }
    });
  }

  Widget topToolView() {
    return Container(
        padding: EdgeInsets.only(left: 10.px, right: 10.px),
        height: 40.px,
        width: 375.px,
        decoration: const BoxDecoration(
          // border: Border.all(width: 1),
          color: BDHColor.kClear,
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              GestureDetector(
                onTap: () {
                  // GetCurrentInstallVersion.check(context: context);//测试 version
                  requestUserLoctionPermission();
                  getWeather();
                },
                child: SizedBox(
                  // decoration: BoxDecoration(border: Border.all(width: 1)),
                  child: Row(
                    children: [
                      Image.asset(
                        width: 24.px,
                        height: 24.px,
                        // ImageHelper.wrapAssets('homeWeatherLoaction_white.png'),
                        ImageHelper.wrapAssets('locate_empty_black.png'),
                      ),
                      SizedBox(width: 2.px),
                      Text(
                        address,
                        style: TextStyle(
                            fontSize: 15.px,
                            fontWeight: FontWeight.w500,
                            color: Colors.black),
                      ),
                      AnimatedOpacity(
                        duration: const Duration(milliseconds: 500),
                        opacity: showToolBarWeatherViewOpacity,
                        child: getWeatherView(true),
                      ),
                    ],
                  ),
                ),
              ),

              //搜索,消息,视图
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      BDHResponsitory.saveMenuUse({
                        "menuCode": "home-serach",
                        "menuName": "天气",
                        "menuType": "3", //搜索类型 天气
                      }).then((res) {
                        Log.i(res.msg);
                      });
                      Navigator.of(context).pushNamed('searchNewPage');
                    },
                    child: Image.asset(
                      width: 24.px,
                      height: 24.px,
                      ImageHelper.wrapAssets("findBalckIcon.png"),
                    ),
                  ),
                  SizedBox(width: 10.px),
                  GestureDetector(
                    onTap: () {
                      Navigator.of(context).pushNamed('newsMessageHome');
                      // Navigator.of(context)
                      //     .push(CupertinoPageRoute(builder: (ctx) {
                      //   return const NewsMessageHome();
                      // }));
                    },
                    child: Image.asset(
                      width: 24.px,
                      height: 24.px,
                      ImageHelper.wrapAssets("chat-square-dots-black.png"),
                    ),
                  ),
                ],
              )
            ],
          ),
        ));
  }

  Widget getWeatherView(bool showSmall) {
    return GestureDetector(
      onTap: () {
        // pagesA/pages/weather/moreWeather
        BDHResponsitory.saveMenuUse({
          "menuCode": "home-weather",
          "menuName": "天气",
          "menuType": "3", //搜索类型 天气
        }).then((res) {
          Log.i(res.msg);
        });
        LocationResult? locationResult =
            GpsReceiver.getInstance().locationResult;
        if (locationResult?.longitude == null) {
          showToast("请开启定位权限");
          return;
        } else {
          Navigator.of(context)
              .pushNamed(RouteName.weather, arguments: <String, dynamic>{
            "address": address,
            "latitude": defalutLatitude,
            "longitude": defalutLongitude,
            "temp": weatherModel?.temp
          });
        }
      },
      child: Container(
        padding: EdgeInsets.only(left: 5.px),
        height: 110.px,
        // width: 375.px,
        decoration: const BoxDecoration(
          color: BDHColor.kClear,
          // color: Colors.black,
          // border: Border.all(width: 1),
        ),
        child: Column(
          mainAxisAlignment:
              showSmall ? MainAxisAlignment.center : MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            weatherModel == null
                ? Container()
                : AnimatedOpacity(
                    opacity: (showToolBarWeatherViewOpacity == 1.0 &&
                            showSmall == false)
                        ? 0.0
                        : 1.0,
                    duration: const Duration(milliseconds: 300),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(width: showSmall ? 0 : 10.px),
                        //icon
                        Image.asset(
                          width: showSmall ? 34.px : 44.px,
                          height: showSmall ? 34.px : 44.px,
                          // ImageHelper.wrapWeatherAssets(
                          //     weatherModel?.weatherIcon ?? ''),
                          ImageHelper.wrapWeatherAssets(
                              '${weatherModel?.weathercode ?? '00'}.png'),
                        ),
                        SizedBox(width: 10.px),
                        Text(
                          '${weatherModel?.temp ?? ''}°C',
                          style: TextStyle(
                              fontSize: showSmall ? 20.px : 32.px,
                              fontWeight: Platform.isIOS
                                  ? FontWeight.w600
                                  : FontWeight.w600,
                              color: Colors.black),
                        ), //温度
                        SizedBox(width: showSmall ? 0 : 10.px),
                        showSmall
                            ? Container()
                            : Text(
                                '${weatherModel?.weather ?? ''} ${weatherModel?.winddirclass ?? ''}',
                                style: const TextStyle(
                                    fontSize: 16,
                                    // fontWeight: FontWeight.w500,
                                    fontWeight: FontWeight.w700,
                                    color: BDHColor.black04),
                              ),
                        Visibility(
                            visible: showlocate && !showSmall,
                            child: GestureDetector(
                              onTap: () {
                                PermissionUtil.requestLocationPermission(
                                        // context, "您所处位置的天气信息")
                                        context,
                                        "以便为您提供所处位置的天气信息")
                                    .then((res) {
                                  if (res == true) {
                                    GpsReceiver receiver =
                                        GpsReceiver.getInstance();
                                    receiver.start(true);
                                    Log.d(
                                        "requestLocationPermission $res ${receiver.errorResult}");
                                    if (receiver.errorResult != null) {
                                      busLocationError(receiver.errorResult,
                                          force: true);
                                    }
                                  } else {
                                    return BrnDialogManager.showConfirmDialog(
                                        context,
                                        title: "提示",
                                        cancel: '取消',
                                        confirm: '确定',
                                        message: "需要您开启定位权限, 是否去开启定位权限？",
                                        onConfirm: () {
                                      Navigator.of(context).pop();
                                      openAppSettings();
                                    }, onCancel: () {
                                      Navigator.of(context).pop();
                                    });
                                  }
                                });
                              },
                              child: const BdhTag(
                                  name: "点击开启定位", color: Colors.grey),
                            ))
                      ],
                    )),
            // SizedBox(height: 3.px)
          ],
        ),
      ),
    );
  }

  // 头部
  Widget _buildHeader(BuildContext context, bool innerBoxIsScrolled) {
    return SliverOverlapAbsorber(
      handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
      sliver: SliverAppBar(
        // centerTitle: true,
        titleSpacing: 0,
        title: topToolView(),
        // floating: true,
        pinned: true,
        // elevation: 6, //影深
        // expandedHeight: 182.px,
        expandedHeight: 168.px,
        forceElevated: innerBoxIsScrolled, //为true时展开有阴影
        // backgroundColor: BDHColor.kClear,
        backgroundColor: Colors.blue[50],
        toolbarHeight: 30.px,
        // collapsedHeight: 60.px,
        flexibleSpace: FlexibleSpaceBar(
          // collapseMode: CollapseMode.pin,
          background: Stack(
            children: [
              Positioned(
                child: SizedBox(
                  height: 448.px,
                  width: 375.px,
                  child: Image.asset(
                    height: 448.px,
                    width: 375.px,
                    ImageHelper.wrapAssets(
                        'homePageBGImg_blue_bg.png'), //homePageExpendBGImg  homePageBGImg
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Positioned(
                child: Container(
                  decoration: const BoxDecoration(
                    color: BDHColor.kClear,
                    // border: Border.all(width: 1),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      SizedBox(height: 35.px),
                      getWeatherView(false),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // 底部固定栏
        bottom: orgModelV2List.isNotEmpty
            ? PreferredSize(
                preferredSize:
                    const Size.fromHeight(kBottomNavigationBarHeight),
                child: LayoutBuilder(builder:
                    (BuildContext context, BoxConstraints constraints) {
                  // final double collapsedHeight = constraints.maxHeight;
                  // print('SliverAppBar collapsed height: $constraints');
                  return TabBar(
                    onTap: (index) {
                      bool indexIsChanging = _tabController!.indexIsChanging;
                      // Logger().i(
                      //     '切换tabbar----------------------$index-----$indexIsChanging');
                      if (!indexIsChanging) {
                        OrgModelV2 clickedOrgModel = orgModelV2List[index];
                        dealWithChangeTabBarAction(index);
                        bus.emit('SelectedCompany', clickedOrgModel);
                      }
                    },
                    tabAlignment: TabAlignment.start,
                    dividerColor: Colors.transparent,
                    controller: _tabController,
                    labelColor: const Color.fromRGBO(0, 0, 0, 1),
                    labelPadding: EdgeInsets.only(left: 10.px, right: 10.px),
                    unselectedLabelColor:
                        const Color.fromRGBO(153, 153, 153, 1),
                    labelStyle: TextStyle(
                      // fontSize: 16.px,
                      fontSize: 20.px,
                      fontWeight:
                          Platform.isIOS ? FontWeight.w700 : FontWeight.w600,
                      fontFamily: "PingFang SC",
                    ),
                    unselectedLabelStyle: TextStyle(
                      fontSize: 16.px,
                      fontWeight:
                          Platform.isIOS ? FontWeight.w700 : FontWeight.w600,
                      fontFamily: "PingFang SC",
                    ),
                    isScrollable: true,
                    indicator: const TCUnderlineTabIndicator(
                        indicatorWidth: 25,
                        isRound: false,
                        indicatorBottom: 0,
                        borderSide: BorderSide(
                          width: 2,
                          // color: Color.fromRGBO(30, 216, 116, 1),
                          color: Color.fromRGBO(0, 0, 0, 0),
                        )),
                    tabs: orgModelV2List
                        .map((model) => TCTabScale(
                              text: model.orgNickName ?? '',
                              fontScale: fontScale,
                            ))
                        .toList(),
                  );
                }),
              )
            : null,
      ),
    );
  }

  showBottomMultiSelectPicker(BuildContext context) {
    var tempData = [];
    for (var e in treeResult!.data!) {
      tempData.add(e.toJson());
    }
    JhCascadeTreePicker.show(context,
        data: tempData,
        valueKey: "orgCode",
        labelKey: "orgName",
        childrenKey: "list",
        clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
      currentOrg = (ress as List).last;

      context.read<UserModel>().setCurrentOrg(currentOrg!);
      bus.emit("orgChange", currentOrg);
      setState(() {
        channelModelList = [];
      });
      getTreeChannel();
    });
  }

//net: 首页频道树
  getTreeChannel() {
    String orgCode = '';
    if (currentOrg != null) {
      orgCode = currentOrg!["orgCode"];
    }
    // Logger().i('orgCode= $orgCode');

    HomeService.getTreeChannel(
      {"orgCode": orgCode},
    ).then((res) {
      // Logger().i('首页频道树res= ');
      // Logger().i(res);
      if (res['code'] == 0 && res['success'] == true) {
        final dataList = res['data'];
        List<ChannelModel> tempModelList = [];
        for (int i = 0; i < dataList.length; i++) {
          ChannelModel model = ChannelModel.fromJson(dataList[i]);
          List<ChannelModel> tempsubChannerList = [];
          if (dataList[i]['childChannel'] != null) {
            for (int j = 0; j < dataList[i]['childChannel'].length; j++) {
              ChannelModel childChannelModel =
                  ChannelModel.fromJson(dataList[i]['childChannel'][j]);
              j == 0
                  ? childChannelModel.isSeleced = true
                  : childChannelModel.isSeleced = false;
              tempsubChannerList.add(childChannelModel);
            }
          }
          model.childChannel = tempsubChannerList;
          tempModelList.add(model);
          // Logger().i(model.channelName);
        }

        setState(() {
          channelModelList = tempModelList;
          _tabController =
              TabController(length: tempModelList.length, vsync: this);
        });
      }
    });
  }

//net: 组织架构 v1
  loadOrg() {
    BDHResponsitory.getOrgTree({}).then((res) {
      treeResult = res;
      currentOrg = res.data!.first.toJson();
      context.read<UserModel>().setCurrentOrg(currentOrg!);
      bus.emit("orgChange", currentOrg);
      setState(() {});
      getTreeChannel(); // 获取频道
    });
  }

  getWeather() {
    Permission.locationWhenInUse.status.then((result) {
      if (result != PermissionStatus.granted) {
        showlocate = true;
        setState(() {});
      }
    });

    Future.delayed(const Duration(seconds: 3), () {
      GpsReceiver receiver = GpsReceiver.getInstance();

      Log.d("start getWeather locationWhenInUse ${receiver.locationResult}");
      if (receiver.locationResult != null) {
        // Logger().i('定位天气');

        getWeatherData(
            receiver.locationResult!.latitude ?? 0,
            receiver.locationResult!.longitude ?? 0,
            receiver.locationResult!.addressCity ?? '哈尔滨市');
      } else {
        // Logger().i('默认哈尔滨天气');
        getWeatherData(defalutLatitude, defalutLongitude, address);
      }
    });
  }

  bool needLocationPermissionDialogShowed = false;

  void showNeedLocationPermissionDialog({bool force = false}) {
    if (!mounted) {
      return;
    }
    if (!force && needLocationPermissionDialogShowed) {
      return;
    }
    needLocationPermissionDialogShowed = true;
    showConfirmDialog(context,
            title: "缺少定位权限", message: "需要进入设置打开系统定位开关", cancel: "暂不开启")
        .then((result) {
      if (!mounted) {
        return;
      }
      if (result == true) {
        NativeUtil.openAndroidLocationSecureSettings();
      }
    });
  }

  // getWeatherData(LocationResult locationResult) {
  getWeatherData(num latitude, num longitude, String locationAddress) {
    // Logger().i(
    //     '获取天气locationResult= ${locationResult.latitude}---${locationResult.longitude}');
    // HomeService.getWeatherData(
    //         locationResult.latitude ?? 0, locationResult.longitude ?? 0)
    HomeService.getWeatherData(latitude, longitude).then((res) {
      // Logger().i('获取天气res= ');
      // Logger().i(res);
      if (res['status'] == 200) {
        final weatherData = res['data'];
        WeatherModel model = WeatherModel.fromJson(weatherData);
        setState(() {
          weatherModel = model;
          address = locationAddress;
          defalutLatitude = latitude;
          defalutLongitude = longitude;
        });
      }
    });
  }

  //net:获取9个分公司树v2
  getOrgListTreeNew() {
    String userOrgCode = StorageUtil.orgCode() ?? '';
    if (userOrgCode.length > 6) {
      String userOrgCodeForFarm = userOrgCode.substring(0, 6);
      userOrgCode = userOrgCodeForFarm;
    }
    HomeService.getOrgListTreeNew({}).then((res) {
      // Logger().i('获取9个分公司res= ');
      // Logger().i(res);
      if (res['code'] == 0 && res['success'] == true) {
        final dataList = res['data'];
        List<OrgModelV2> tempModelList = [];
        for (int i = 0; i < dataList.length; i++) {
          OrgModelV2 model = OrgModelV2.fromJson(dataList[i]);
          if (model.orgCode == '86') {
            //   // 处理集团名称过长问题
            //   String groupName = model.orgName ?? '';
            //   groupName = groupName.length > 6
            //       ? '${groupName.substring(0, 6)}..'
            //       : groupName;
            //   model.orgName = groupName;
            model.orgNickName = '集团';
            model.orgName = '北大荒集团';
          }

          model.isCompany = true;
          tempModelList.add(model);
          if (userOrgCode.contains(model.orgCode ?? '')) {}
        }

        for (int i = 0; i < tempModelList.length; i++) {
          OrgModelV2 model = tempModelList[i];
          String orgCodeStr = model.orgCode ?? '';
          if (orgCodeStr == '86' || orgCodeStr == '861') {
            //集团+环球
            saveOrgCode(model.orgCode ?? '');
          } else {
            model.children?.insert(0, model);
          }
        }

        int index = tempModelList.lastIndexWhere((item) {
          return userOrgCode.contains(item.orgCode ?? '');
        });

        setState(() {
          orgModelV2List = tempModelList;
          _tabController =
              TabController(length: tempModelList.length, vsync: this);
          if (index != -1) {
            currentOrgModel = orgModelV2List[index];
          } else {
            currentOrgModel = tempModelList.firstOrNull!;
          }
        });
        changeTabAction();
        // print(orgModelV2List[index].orgCode);
        // print(userOrgCode);
        // if (index != -1) {
        //   Future.delayed(const Duration(milliseconds: 100), () {
        //     _tabController?.animateTo(orgModelV2List.length - 1,
        //         duration: const Duration(microseconds: 200));
        //     return Future.delayed(const Duration(seconds: 1), () {
        //       // return Future.delayed(const Duration(milliseconds: 200), () {
        //       _tabController?.animateTo(index,
        //           duration: const Duration(seconds: 2));
        //       return Future.delayed(const Duration(milliseconds: 1000), () {
        //         List<OrgModelV2> childList = currentOrgModel!.children!;
        //         for (int i = 0; i < childList.length; i++) {
        //           OrgModelV2 model = childList[i];
        //           // if (userOrgCode.contains(model.orgCode ?? '') ||
        //           //     (model.orgCode ?? '').contains(userOrgCode)) {
        //           if ((model.orgCode ?? '').contains(userOrgCode)) {
        //             model.isSelected = true;
        //             selectedFarmModelCallBack(model);
        //             break;
        //           }
        //         }
        //       });
        //     });
        //   });
        // }
        if (index != -1) {
          Future.delayed(const Duration(milliseconds: 500), () {
            _tabController?.animateTo(index,
                duration: const Duration(milliseconds: 300));
            return Future.delayed(const Duration(milliseconds: 2600), () {
              List<OrgModelV2> childList = currentOrgModel!.children!;
              for (int i = 0; i < childList.length; i++) {
                OrgModelV2 model = childList[i];
                // if (userOrgCode.contains(model.orgCode ?? '') ||
                //     (model.orgCode ?? '').contains(userOrgCode)) {
                if ((model.orgCode ?? '').contains(userOrgCode)) {
                  model.isSelected = true;
                  // return Future.delayed(duration)
                  selectedFarmModelCallBack(model);
                  break;
                }
              }
            });
          });
        }
      } else {
        showToast('获取组织tree接口异常');
      }
    });
  }

  getFertilizeNotice() {
    BdhLandResponsitory.getFertilizeNotice({"yearNo": "2025"}).then((result) {
      if (result.code != 0) {
        return;
      }
      readData();
    });
  }

  Future readData() async {
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (ctx) {
          return PopScope(
              canPop: false,
              child: Center(
                child: HomeFertilizeNoticeView(gotoFertilizePage: () {
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return const BdhSignedSoilTestFormulaPage(contractId: "");
                  }));
                }),
              ));
        });
  }

  @override
  void dispose() {
    bus.off("upDateOrg", busUpDateOrg);
    bus.off("location", busLocation);
    bus.off("locationError", busLocationError);
    super.dispose();
  }
}
