class WeatherModel {
  late double? bodytemp;
  late double? temp;
  late String? winddirclass; //西南风
  late String? weather; //小雨
  late String? weatherIcon;
  late String? weathercode;

  WeatherModel({
    this.bodytemp,
    this.temp,
    this.winddirclass,
    this.weather,
    this.weatherIcon = 'w.png',
    this.weathercode,
  });

  WeatherModel.fromJson(Map<String, dynamic> json) {
    bodytemp = json['bodytemp'];
    temp = json['temp'];
    winddirclass = json['winddirclass'];
    weather = json['weather'];
    weathercode = json['weathercode'];
  }
}
