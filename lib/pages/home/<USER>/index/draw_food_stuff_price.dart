import 'package:bdh_smart_agric_app/components/jh_cascade_tree_picker.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/index/price_list.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/index/price_statistics.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/transaction/transaction_card.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/user_certification/real_name_certification_view_home.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

import '../../../../components/bdh_network_image.dart';
import '../../../../const/url_config_const.dart';
import '../../../../model/rice_user_home_result_model.dart';
import '../../../../model/user_certification_model.dart';
import '../../../../utils/request/rice_price_service.dart';

class DrawFoodStuffPrice extends StatefulWidget {
  final MenuConfigItem item;

  const DrawFoodStuffPrice({super.key, required this.item});

  @override
  State<StatefulWidget> createState() =>
      _DrawFoodStuffPriceState(menuConfigItems: item);
}

class _DrawFoodStuffPriceState extends State<DrawFoodStuffPrice> {
  MenuConfigItem menuConfigItems;

  _DrawFoodStuffPriceState({required this.menuConfigItems});

  GlobalKey anchorKey = GlobalKey();

  final ScrollController _scrollController = ScrollController();
  double _opacity = 0.0;

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  OrgTreeResult? treeResult;
  Map<dynamic, dynamic>? currentOrg;

  RiceUserHomeInfo? userHomeInfo;
  UserCertificationModel? certifictionModel;

  @override
  void initState() {
    super.initState();
    loadOrg();

    _scrollController.addListener(() {
      setState(() {
        _opacity = _scrollController.offset / 50;
        _opacity = _opacity.clamp(0.0, 1.0);
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white.withOpacity(0.98),
      endDrawer: Drawer(
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        width: 280.px,
        elevation: 3,
        child: _buildRightDrawer(),
      ),
      body: Stack(
        children: <Widget>[
          Positioned(
            top: 0,
            child: Image.asset(
              'assets/images/price_bg.png',
              width: 375.px,
              fit: BoxFit.cover,
              height: 320.px,
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 86.px),
            child: SingleChildScrollView(
              controller: _scrollController,
              child: SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Column(
                  children: [
                    SizedBox(
                        width: 375.px,
                        height: 224.px,
                        child: const PriceStatistics()),
                    Container(
                      margin: EdgeInsets.only(top: 16.px),
                      width: 375.px,
                      height: 164.px,
                      child: const TransactionCard(),
                    ),
                    const PriceList()
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            top: 0,
            child: Container(
              width: MediaQuery.of(context).size.width,
              height: 86.px,
              color: Colors.white.withOpacity(_opacity),
              child: Container(
                padding: const EdgeInsets.only(top: 26),
                child: GestureDetector(
                  onTap: () {
                    showBottomMultiSelectPicker(context);
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Container(
                          margin: EdgeInsets.only(left: 13.px),
                          alignment: Alignment.center,
                          width: 32.px,
                          height: 32.px,
                          child: Icon(
                            Icons.arrow_back_ios,
                            color: Colors.black,
                            size: 20.px,
                          ),
                        ),
                      ),
                      Stack(
                        children: [
                          Positioned(
                              top: 3.px,
                              child: Image.asset(
                                ImageHelper.wrapAssets("title_bg.png"),
                                width: 80.px,
                                height: 24.px,
                              )),
                          Text(
                            currentOrg != null ? currentOrg!["orgName"] : "",
                            style: const TextStyle(color: Colors.black),
                          ),
                        ],
                      ),
                      Icon(
                        Icons.arrow_drop_down_sharp,
                        color: Colors.black,
                        size: 20.px,
                      ),
                      const Spacer(),
                      GestureDetector(
                          onTap: () {
                            _scaffoldKey.currentState?.openEndDrawer();
                            loadConfig();
                            loadUserData();
                            getUserAuthenticationInfo();
                            // loadConfig();
                            // RenderBox? renderBox = anchorKey.currentContext!
                            //     .findRenderObject() as RenderBox;
                            //
                            // var offset =
                            //     renderBox.localToGlobal(Offset(-95.px, 35.px));
                            // showDialog(
                            //     context: context,
                            //     barrierDismissible: true,
                            //     barrierColor: Colors.transparent,
                            //     useSafeArea: false,
                            //     builder: (BuildContext context) {
                            //       return GestureDetector(
                            //         onTap: () {
                            //           Navigator.of(context).pop();
                            //         },
                            //         child: Material(
                            //           color: Colors.transparent,
                            //           child: Stack(
                            //             children: [
                            //               Positioned(
                            //                 top: offset.dy,
                            //                 left: offset.dx,
                            //                 child: ConstrainedBox(
                            //                     constraints: BoxConstraints(
                            //                         maxHeight: 300.px),
                            //                     child: Container(
                            //                       width: 132.px,
                            //                       decoration: BoxDecoration(
                            //                           borderRadius:
                            //                               BorderRadius.all(
                            //                                   Radius.circular(
                            //                                       8.px)),
                            //                           color:
                            //                               const Color.fromRGBO(
                            //                                   3, 25, 18, 0.8)),
                            //                       child: SingleChildScrollView(
                            //                         child: Column(
                            //                           mainAxisAlignment:
                            //                               MainAxisAlignment
                            //                                   .center,
                            //                           children: [
                            //                             ...menuConfigItems
                            //                                 .children!
                            //                                 .map((item) =>
                            //                                     MenuItemView(
                            //                                       item: item,
                            //                                       callBack: () {
                            //                                         loadConfig();
                            //                                         bus.emit(
                            //                                             "riceOrgChange",
                            //                                             currentOrg);
                            //                                       },
                            //                                     ))
                            //                             // ...widget.item.children!.map(
                            //                             //     (item) => MenuItemView(
                            //                             //         item: item))
                            //                           ],
                            //                         ),
                            //                       ),
                            //                     )),
                            //               )
                            //             ],
                            //           ),
                            //         ),
                            //       );
                            //     });
                          },
                          child: Container(
                            margin: EdgeInsets.only(right: 15.px),
                            alignment: Alignment.center,
                            width: 40.px,
                            height: 40.px,
                            color: Colors.transparent,
                            child: ImageIcon(
                                key: anchorKey,
                                AssetImage(ImageHelper.wrapAssets(
                                    "ic_price_more.png")),
                                size: 24.px,
                                color: Colors.black),
                          ))
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // build 右侧抽屉
  Widget _buildRightDrawer() {
    return Container(
      color: Colors.white,
      width: 280.px,
      child: Column(
        children: [
          Container(
            color: const Color.fromRGBO(246, 247, 251, 1),
            width: 280.px,
            height: 160.px,
            alignment: Alignment.topLeft,
            child: Container(
              margin: EdgeInsets.only(left: 20.px, top: 64.px, right: 20.px),
              width: 240.px,
              height: 64.px,
              child: Row(children: [
                ClipOval(
                  child: BdhNetworkImage(
                      fit: BoxFit.cover,
                      url: "${urlConfig.microfront}${userHomeInfo?.pics}",
                      width: 48.px,
                      height: 48.px),
                ),
                Container(
                    height: 64.px,
                    margin: EdgeInsets.only(left: 12.px),
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 120.px,
                            child: Text(
                              userHomeInfo?.nickName ?? "",
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  fontSize: 20.px,
                                  color: Colors.black,
                                  fontWeight: FontWeight.w600),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              Navigator.of(context)
                                  .push(CupertinoPageRoute(builder: (_) {
                                return const RealNameCertificationViewHome();
                              })).then((res) {
                                getUserAuthenticationInfo();
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.only(top: 4.px),
                              alignment: Alignment.center,
                              padding: EdgeInsets.only(
                                  bottom: 1.px, left: 8.px, right: 8.px),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(45.px),
                                  gradient: const LinearGradient(
                                      colors: [
                                        Color.fromRGBO(30, 216, 116, 1),
                                        Color.fromRGBO(23, 199, 158, 1)
                                      ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight)),
                              height: 24.px,
                              child: Text(
                                '实名认证(${(certifictionModel?.approveStatus == '') || (certifictionModel?.approveStatus == null) ? '未认证' : certifictionModel?.approveStatus == '0' ? '待审核' : certifictionModel?.approveStatus == '1' ? '已认证' : certifictionModel?.approveStatus == '2' ? '驳回' : ''})',
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: 12.px, color: Colors.white),
                              ),
                            ),
                          ),
                        ])),
                const Spacer(),
                // ImageIcon(
                //   AssetImage(ImageHelper.wrapAssets("ic_edit.png")),
                //   size: 16.px,
                //   color: Colors.black,
                // )
              ]),
            ),
          ),
          Container(
            height: 85.px,
            alignment: Alignment.center,
            child: SizedBox(
              width: 260.px,
              height: 45.px,
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    // Column(
                    //     mainAxisAlignment: MainAxisAlignment.center,
                    //     children: [
                    //       Text(
                    //         userHomeInfo?.createTime ?? "",
                    //         style: TextStyle(
                    //             fontSize: 16.px,
                    //             fontFamily: "bayon",
                    //             color: const Color.fromRGBO(57, 61, 68, 1),
                    //             fontWeight: FontWeight.w500),
                    //       ),
                    //       Text(
                    //         "注册时间",
                    //         style: TextStyle(
                    //             fontSize: 13.px,
                    //             color: const Color.fromRGBO(39, 31, 0, 1)),
                    //       )
                    //     ]),
                    VerticalDivider(
                        width: 1.px,
                        indent: 2.5.px,
                        endIndent: 2.5.px,
                        color: const Color.fromRGBO(246, 247, 251, 1)),
                    Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            // "${userHomeInfo?.cooperationNumber ?? 0}",
                            "${userHomeInfo?.sheetDetailsViewCount ?? 0}",
                            style: TextStyle(
                                fontFamily: "bayon",
                                fontSize: 16.px,
                                color: const Color.fromRGBO(57, 61, 68, 1),
                                fontWeight: FontWeight.w500),
                          ),
                          Text(
                            "周访问量",
                            style: TextStyle(
                                fontSize: 13.px,
                                color: const Color.fromRGBO(39, 31, 0, 1)),
                          )
                        ]),
                    VerticalDivider(
                        width: 1.px,
                        indent: 2.5.px,
                        endIndent: 2.5.px,
                        color: const Color.fromRGBO(246, 247, 251, 1)),
                    GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          // Navigator.of(context).push(CupertinoPageRoute(
                          //     builder: (BuildContext context) {
                          //   return const MyQualification();
                          // }));
                        },
                        child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "${userHomeInfo?.certificationApproveCount ?? 0}",
                                style: TextStyle(
                                    fontFamily: "bayon",
                                    fontSize: 16.px,
                                    color: const Color.fromRGBO(57, 61, 68, 1),
                                    fontWeight: FontWeight.w500),
                              ),
                              Text(
                                "收粮资质",
                                style: TextStyle(
                                    fontSize: 13.px,
                                    color: const Color.fromRGBO(39, 31, 0, 1)),
                              )
                            ]))
                  ]),
            ),
          ),
          Divider(
            height: 1.px,
            color: const Color.fromRGBO(246, 247, 251, 1),
          ),
          Expanded(
            flex: 100,
            child: Container(
              margin: EdgeInsets.only(bottom: 10.px),
              child: SingleChildScrollView(
                child: Container(
                  padding: EdgeInsets.only(top: 10.px),
                  child: Column(
                      children: menuConfigItems.children!
                          .map((item) => MenuItemView(
                                item: item,
                                certificationApproveCount:
                                    userHomeInfo?.certificationApproveCount ??
                                        0,
                                certificationApproveSumCount: userHomeInfo
                                        ?.certificationApproveSumCount ??
                                    0,
                                callBack: () {
                                  loadConfig();
                                  loadUserData();
                                  bus.emit("riceOrgChange", currentOrg);
                                },
                              ))
                          .toList()),
                ),
              ),
            ),
          ),
          Divider(
            height: 1.px,
            color: const Color.fromRGBO(246, 247, 251, 1),
          ),
          Container(
            height: 72.px,
            alignment: Alignment.center,
            child: TextButton(
                onPressed: () {
                  _scaffoldKey.currentState?.closeEndDrawer();
                },
                child: Text("关闭",
                    style: TextStyle(
                        fontSize: 14.px,
                        color: Colors.black,
                        fontWeight: FontWeight.w400))),
          )
        ],
      ),
    );
  }

  // @override
  // Widget build(BuildContext context) {
  //   return Scaffold(
  //     // appBar: AppBar(
  //     //   backgroundColor: Colors.transparent,
  //     // ),
  //     // body: const SingleChildScrollView(
  //     //   child: Column(
  //     //     children: [PriceStatistics(), TransactionCard(), PriceList()],
  //     //   ),
  //     // ),
  //     body: Container(
  //       color: const Color.fromRGBO(226, 232, 240, 0.32),
  //       child: NestedScrollView(
  //         headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
  //           return <Widget>[
  //             _buildHeader(context, innerBoxIsScrolled),
  //           ];
  //         },
  //         body: SingleChildScrollView(
  //           child: Column(
  //             children: [
  //               SizedBox(
  //                 height: 88.px,
  //               ),
  //               SizedBox(
  //                 width: 375.px,
  //                 height: 164.px,
  //                 child: const TransactionCard(),
  //               ),
  //               const PriceList()
  //             ],
  //           ),
  //         ),
  //       ),
  //     ),
  //   );
  // }

  // 头部
  Widget _buildHeader(BuildContext context, bool innerBoxIsScrolled) {
    return SliverOverlapAbsorber(
      handle: NestedScrollView.sliverOverlapAbsorberHandleFor(context),
      sliver: SliverAppBar(
          primary: true,
          automaticallyImplyLeading: false,
          title: GestureDetector(
            onTap: () {
              showBottomMultiSelectPicker(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    alignment: Alignment.center,
                    width: 32.px,
                    height: 32.px,
                    child: Icon(
                      Icons.arrow_back_ios,
                      color: Colors.black,
                      size: 20.px,
                    ),
                  ),
                ),
                Stack(
                  children: [
                    Positioned(
                        top: 3.px,
                        child: Image.asset(
                          ImageHelper.wrapAssets("title_bg.png"),
                          width: 80.px,
                          height: 24.px,
                        )),
                    Text(
                      currentOrg != null ? currentOrg!["orgName"] : "",
                      style: const TextStyle(color: Colors.black),
                    ),
                  ],
                ),
                Icon(
                  Icons.arrow_drop_down_sharp,
                  color: Colors.black,
                  size: 20.px,
                )
              ],
            ),
          ),
          actionsIconTheme: const IconThemeData(
            color: Colors.black,
          ),
          actions: [
            GestureDetector(
                onTap: () {
                  loadConfig();
                  RenderBox? renderBox =
                      anchorKey.currentContext!.findRenderObject() as RenderBox;

                  var offset = renderBox.localToGlobal(Offset(-95.px, 35.px));
                  showDialog(
                      context: context,
                      barrierDismissible: true,
                      barrierColor: Colors.transparent,
                      useSafeArea: false,
                      builder: (BuildContext context) {
                        return GestureDetector(
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Material(
                            color: Colors.transparent,
                            child: Stack(
                              children: [
                                Positioned(
                                  top: offset.dy,
                                  left: offset.dx,
                                  child: ConstrainedBox(
                                      constraints:
                                          BoxConstraints(maxHeight: 300.px),
                                      child: Container(
                                        width: 132.px,
                                        decoration: BoxDecoration(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(8.px)),
                                            color: const Color.fromRGBO(
                                                3, 25, 18, 0.8)),
                                        child: SingleChildScrollView(
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              ...menuConfigItems.children!
                                                  .map((item) => MenuItemView(
                                                        item: item,
                                                        callBack: () {
                                                          loadConfig();
                                                          bus.emit(
                                                              "riceOrgChange",
                                                              currentOrg);
                                                        },
                                                      ))
                                              // ...widget.item.children!.map(
                                              //     (item) => MenuItemView(
                                              //         item: item))
                                            ],
                                          ),
                                        ),
                                      )),
                                )
                              ],
                            ),
                          ),
                        );
                      });
                },
                child: Container(
                  margin: EdgeInsets.only(right: 10.px),
                  alignment: Alignment.center,
                  width: 40.px,
                  height: 40.px,
                  color: Colors.transparent,
                  child: ImageIcon(
                      key: anchorKey,
                      AssetImage(ImageHelper.wrapAssets("ic_price_more.png")),
                      size: 24.px,
                      color: Colors.black),
                ))
          ],
          pinned: true,
          floating: true,
          elevation: 6,
          //影深
          expandedHeight: 276.px,
          forceElevated: innerBoxIsScrolled,
          //为true时展开有阴影
          // backgroundColor: HexColor('#44BF7D'),
          backgroundColor: Colors.white,
          flexibleSpace: FlexibleSpaceBar(
            collapseMode: CollapseMode.pin,
            background: Container(
              decoration: const BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/images/price_bg.png'), // 本地图片
                  fit: BoxFit.cover,
                ),
              ),
              child: Column(
                children: [
                  SizedBox(
                    height: 84.px,
                  ),
                  SizedBox(
                      width: 375.px, height: 216.px, child: PriceStatistics()),
                ],
              ),
            ),
          ),
          stretchTriggerOffset: 48.px,
          stretch: false),
    );
  }

  showBottomMultiSelectPicker(BuildContext context) {
    var tempData = [];
    for (var e in treeResult!.data!) {
      tempData.add(e.toJson());
    }
    JhCascadeTreePicker.show(context,
        data: tempData,
        valueKey: "orgCode",
        labelKey: "orgName",
        childrenKey: "list",
        clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
      setState(() {
        currentOrg = (ress as List).last;
      });
      bus.emit("riceOrgChange", currentOrg);
    });
  }

  loadUserData() {
    // PriceResponsitory.riceUserHome({"orgCode": orgCode}).then((res) {
    //   setState(() {
    //     userHomeInfo = res.data;
    //   });
    // });
    PriceResponsitory.riceUserHomev1({}).then((res) {
      setState(() {
        userHomeInfo = res.data;
      });
    });
  }

  // 获取用户认证信息
  getUserAuthenticationInfo() {
    PriceResponsitory.getUserAuthenticationInfo({}).then((res) {
      Logger().i('获取用户认证信息');
      Logger().i(res);
      if (res['code'] == 0 && res['msg'] == 'success') {
        final resData = res['data'];
        UserCertificationModel model = UserCertificationModel.fromJson(resData);
        setState(() {
          certifictionModel = model;
        });
      }
    });
  }

  // loadConfig1() {
  //   BDHResponsitory.getFoodMenuConfig().then((value) {
  //     debugPrint('loadConfig=${value.data}');
  //     List<MenuConfigItem> tempList = value.data as List<MenuConfigItem>;
  //     for (var element in tempList) {
  //       if (element.authCode == "ricetransaction1") {
  //         setState(() {
  //           menuConfigItems = element;
  //         });
  //       }
  //     }
  //   });
  // }

  loadConfig() {
    BDHResponsitory.getConfig().then((res) {
      // Logger().i('loadConfig=$res');
      res.data!.forEach((item) {
        if (item.authCode == "ricetransaction1") {
          setState(() {
            menuConfigItems = item;
          });
        }

        //old temp save
        // if (item.authCode == "mainMenu") {
        //   for (var v in item.children!) {
        //     if (v.authCode == "service") {
        //       for (var menus in v.children!) {
        //         if (menus.authCode == "topMenu") {
        //           for (var menuItem in menus.children!) {
        //             if (menuItem.authCode == "ricetransaction") {
        //               //test
        //               // for (var t in menuItem.children!) {
        //               //   t.authName = '000';
        //               // }
        //               setState(() {
        //                 menuConfigItems = menuItem;
        //               });
        //             }
        //           }
        //         }
        //       }
        //     }
        //   }
        // }
      });
    });
  }

  loadOrg() {
    BDHResponsitory.getOrgTree({
      "orgLevelList": [1, 2, 3, 4]
    }).then((res) {
      loadConfig();
      treeResult = res;
      currentOrg = res.data!.first.toJson();
      //获取当前用户所属组织机构
      var tempOrg =
          StorageUtil.userInfo()?.data?.pluginInfo?.orgInfos?.first.toJson();
      if (tempOrg != null) {
        currentOrg = tempOrg;
      }
      bus.emit("riceOrgChange", currentOrg);
      setState(() {});
    });
  }

  @override
  void dispose() {
    bus.off("riceOrgChange");
    super.dispose();
  }
}

class MenuItemView extends StatelessWidget {
  final MenuConfigItem item;
  final num? certificationApproveCount;
  final num? certificationApproveSumCount;
  final Function? callBack;

  const MenuItemView({
    super.key,
    required this.item,
    this.callBack,
    this.certificationApproveCount,
    this.certificationApproveSumCount,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (item.authCode == "touristSale") {
          showToast("请联系管理区管理员进行农户角色开通");
        } else {
          if (item.authName == "发布买粮") {
            if (certificationApproveCount == 0) {
              showToast("用户无收粮资质,请通过资质审核后进行收粮!");
              return;
            }
          }
          if (item.authName == "收粮认证") {
            if (certificationApproveSumCount == 0) {
              Navigator.of(context)
                  .pushNamed(RouteName.buyQualification)
                  .then((res) {
                GlobalServiceView.needShowServiceBtn('riceHome');
                if (callBack != null) {
                  callBack!();
                }
              });
              return;
            }
          }
          // Navigator.of(context).pop();
          Navigator.of(context)
              .pushNamed(item.url ?? "未知路由", arguments: item)
              .then((res) {
            GlobalServiceView.needShowServiceBtn('riceHome');
            if (callBack != null) {
              callBack!();
            }
          });
        }
      },
      child: Container(
        width: 280.px,
        height: 44.px,
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.only(left: 20.px, right: 20.px),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Image.asset(
                color: Colors.black,
                width: 24.px,
                ImageHelper.wrapAssets("${item.icon}.png")),
            SizedBox(width: 15.px),
            Text(
              item.authName ?? "",
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 14.px,
                  fontWeight: FontWeight.w400),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios_rounded,
              color: const Color.fromRGBO(192, 204, 199, 0.6),
              size: 16.px,
            )
          ],
        ),
      ),
    );
  }
}
