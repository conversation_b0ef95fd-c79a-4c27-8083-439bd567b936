import 'dart:async';
import 'dart:io';
import 'package:audio_video_progress_bar/audio_video_progress_bar.dart';
import 'package:bdh_smart_agric_app/components/bdh_network_image.dart';
import 'package:bdh_smart_agric_app/components/bdh_no_data.dart';
import 'package:bdh_smart_agric_app/components/bdh_share_view.dart';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/video_detail_model.dart';
import 'package:bdh_smart_agric_app/model/video_page_result_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/guide_view/move_animation.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/guide_view/scale_animation.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/video_player_full_screen.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/debounce_throttle_util.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/request/home_service.dart';
import 'package:bdh_smart_agric_app/utils/request/video_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/viewmodel/user_model.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
// import 'package:flutter_intro/flutter_intro.dart';
import 'package:fluwx/fluwx.dart';
import 'package:gif/gif.dart';
import 'package:intl/intl.dart';
import 'package:logger/web.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:video_player/video_player.dart';

import '../../../model/guide_page_model.dart';
import '../../../utils/screen/screen_tool.dart';

import 'premium_classroom.dart';

class PremiumClassroomHome extends StatefulWidget {
  final EntryType type;
  final int? videoId;
  final String? firstCategory;
  final String? secondCategory;
  final String? keyWord;
  final bool? myLikeVideo;
  final PageController? pageController;
  final TabController? tabController;
  final bool? notShowGuidePage;

  const PremiumClassroomHome({
    super.key,
    required this.type,
    this.videoId,
    this.firstCategory,
    this.secondCategory,
    this.keyWord,
    this.pageController,
    this.tabController,
    this.notShowGuidePage,
    this.myLikeVideo,
  });

  @override
  State<StatefulWidget> createState() => PremiumClassroomHomeState();
}

class PremiumClassroomHomeState extends State<PremiumClassroomHome>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  var pageNo = 1;
  bool isLoading = false;
  List<VideoItem> items = [];
  VideoPlayerController? currentVideoController;
  final PageController controller = PageController(keepPage: false);
  bool finishedShowVideoGuide = true;
  late GifController controllerGif;
  final _pageController = PageController();
  List guideImgList = [
    GuidePageModel(
      index: 0,
      imgUrl: 'guide_video01.png',
      tipInfo: '通过上下滑动屏幕浏\n览更多农技农艺视频',
      tipInfoTopPostion: 400.px,
    ),
    GuidePageModel(
      index: 1,
      imgUrl: 'guide_video01.png',
      tipInfo: '点击顶部的“搜索”图标，输入关键词搜索，发现更多感兴趣的内容‌',
      tipInfoTopPostion: 300.px,
    ),
    GuidePageModel(
      index: 2,
      imgUrl: 'guide_video01.png',
      tipInfo: '如果您对这个视频感兴趣，可点击屏幕右侧“心形”图标进行点赞',
      tipInfoTopPostion: 300.px,
    ),
    GuidePageModel(
      index: 3,
      imgUrl: 'guide_video01.png',
      tipInfo: '此外还可以通过屏幕右侧\n“分享”图标进行微信分享',
      tipInfoTopPostion: 300.px,
    ),
    GuidePageModel(
      index: 4,
      imgUrl: 'guide_video01.png',
      tipInfo: '想系统性学习不同生育期视频，\n点击“专题”图标进入专题',
      tipInfoTopPostion: 300.px,
    ),
    GuidePageModel(
      index: 5,
      imgUrl: 'guide_zhuanti_bg.png',
      tipInfo: '点击不同生育期系统\n学习农作物种植知识',
      tipInfoTopPostion: 350.px,
    ),
    GuidePageModel(
      index: 6,
      imgUrl: 'guide_zhuanti_bg.png',
      tipInfo: '想了解其他农作物专题,\n可点击“更多专题"',
      tipInfoTopPostion: 350.px,
    ),
  ];

  @override
  void initState() {
    super.initState();
    GlobalServiceView.hidenView();
    // setState(() {
    //   finishedShowVideoGuide = false;
    // });
    // saveGuideViewFinshedState();

    readGuideViewFinshedState().then((res) {
      res = true;
      setState(() {
        finishedShowVideoGuide = res;
      });
      if (!res) {
      } else {
        GlobalServiceView.needShowServiceBtn('video');
      }
    });

    controllerGif = GifController(vsync: this);
    controller.addListener(() {
      if (controller.position.pixels == controller.position.maxScrollExtent) {
        _onLoading();
      }
    });

    loadVideos(true);
    // bus.on('UserChangeBottomTabbarAction', (configItems) {
    //   Future.delayed(const Duration(milliseconds: 200), () {
    //     if (configItems.authCode == "video") {
    //       currentVideoController?.play();
    //     } else {
    //       currentVideoController?.pause();
    //     }
    //   });
    // });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      color: Colors.black,
      child: Stack(
        children: [
          RefreshIndicator(
              onRefresh: _onRefresh,
              child: PageView.builder(
                  controller: controller,
                  itemCount: items.length,
                  scrollDirection: Axis.vertical,
                  itemBuilder: (ctx, idx) {
                    return PlayerView(
                      item: items[idx],
                      onVideoControllerInit: (videoController) {
                        currentVideoController = videoController;
                        if (!finishedShowVideoGuide ||
                            widget.tabController?.index == 1) {
                          currentVideoController?.pause();
                        }
                      },
                      pageController: widget.pageController,
                      onError: (item) {
                        items.remove(item);
                        setState(() {});
                      },
                    );
                  })),
          (items.isEmpty && isLoading == false)
              ? Material(
                  child: Container(
                    color: Colors.black,
                    alignment: Alignment.center,
                    child: const BdhNoData(
                      showImage: false,
                      desc: "暂无相关内容，敬请期待",
                      textStyle: TextStyle(color: Colors.white),
                    ),
                  ),
                )
              : Container(),
        ],
      ),
    );
  }

  // 写:本地存储
  void saveGuideViewFinshedState() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.setBool('finishedShowVideoGuide', finishedShowVideoGuide);
  }

  //读:本地存储
  Future<bool> readGuideViewFinshedState() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    bool? finishedShowVideoGuide =
        preferences.getBool('finishedShowVideoGuide');
    Logger().i('$finishedShowVideoGuide--$finishedShowVideoGuide');
    // setState(() {
    //   finishedShowVideoGuide = finishedShowVideoGuide ?? false;
    // });
    bool res = finishedShowVideoGuide ?? false;
    // return res;
    return true;
  }

  //跳过引导 或 开始体验
  finishGuidePage() {
    setState(() {
      finishedShowVideoGuide = true;
    });
    saveGuideViewFinshedState();
    GlobalServiceView.needShowServiceBtn('video');
    Navigator.of(context).pop();
    currentVideoController?.play();
  }

//下一页
  goToNextPage(int index) {
    int movePage = index + 1 > guideImgList.length - 1
        ? guideImgList.length - 1
        : index + 1;
    _pageController.animateToPage(movePage,
        duration: const Duration(milliseconds: 500),
        curve: Curves.fastOutSlowIn);
  }

  //引导页
  Widget guideView() {
    return Container(
      color: BDHColor.black06,
      height: ScreenTool().screenHeight,
      width: 375.px,
      child: PageView.builder(
          physics: const NeverScrollableScrollPhysics(),
          controller: _pageController,
          itemCount: guideImgList.length,
          itemBuilder: (ctx, index) {
            GuidePageModel model = guideImgList[index];
            return Stack(
              children: [
                Image.asset(
                  fit: BoxFit.cover,
                  width: 375.px,
                  height: ScreenTool().screenHeight,
                  ImageHelper.wrapGuideAssets(model.imgUrl),
                ),
                //------button: 跳过引导------
                Positioned(
                  bottom: 90.px,
                  left: 40.px,
                  child: index == guideImgList.length - 1
                      ? Container()
                      : GestureDetector(
                          onTap: () {
                            finishGuidePage(); //跳过引导
                          },
                          child: Container(
                            width: 135.px,
                            height: 40.px,
                            padding: EdgeInsets.only(
                                left: 10.px,
                                right: 10.px,
                                top: 5.px,
                                bottom: 5.px),
                            decoration: BoxDecoration(
                                color: const Color.fromRGBO(255, 255, 255, 0.1),
                                borderRadius:
                                    BorderRadius.all(Radius.circular(2.px)),
                                border: Border.all(
                                    color: const Color.fromRGBO(
                                        255, 255, 255, 0.4))),
                            child: Center(
                              child: Text(
                                "跳过引导",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400),
                              ),
                            ),
                          ),
                        ),
                ),
                //------button: 下一步 or 开始体验------
                Positioned(
                  bottom: 90.px,
                  right: index == guideImgList.length - 1
                      ? ((375 - 135) * 0.5).px
                      : 40.px,
                  child: GestureDetector(
                    onTap: () {
                      if (index == guideImgList.length - 1) {
                        finishGuidePage(); //跳过引导 完成引导
                      } else {
                        goToNextPage(index); //点击进入下一页
                      }
                    },
                    child: Container(
                      width: 135.px,
                      height: 40.px,
                      padding: EdgeInsets.only(
                          left: 10.px, right: 10.px, top: 5.px, bottom: 5.px),
                      decoration: BoxDecoration(
                          color: const Color.fromRGBO(255, 255, 255, 0.1),
                          borderRadius: BorderRadius.all(Radius.circular(2.px)),
                          border: Border.all(
                              color: const Color.fromRGBO(255, 255, 255, 0.4))),
                      child: Center(
                        child: Text(
                          index == guideImgList.length - 1 ? "开启体验" : "下一步",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400),
                        ),
                      ),
                    ),
                  ),
                ),

                //提示文字
                Positioned(
                  top: model.tipInfoTopPostion,
                  left: 50.px,
                  child: SizedBox(
                      height: 60.px,
                      width: 285.px,
                      child: Text(model.tipInfo,
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              color: Colors.white,
                              height: 1.5,
                              fontSize: 18.px,
                              fontWeight: FontWeight.w400))),
                ),

                //专题: 跑马灯动画
                index == 5
                    ? Positioned(
                        top: 160.px,
                        left: 0,
                        child: SizedBox(
                          height: 100.px,
                          width: 375.px,
                          child: Gif(
                            // fps: 30,
                            // duration: Duration(seconds: 4),
                            controller: controllerGif,
                            autostart: Autostart.loop,
                            image: const AssetImage(
                                'assets/images/guide_zhuanti.gif'),
                          ),
                        ),
                      )
                    : Container(),

                (index == 5)
                    ? Positioned(
                        top: 230.px,
                        left: (375.px - 160.px) * 0.5,
                        child: SizedBox(
                          height: 80.px,
                          width: 80.px,
                          child: MoveAnimation(
                              viewW: 80.px,
                              imgName: 'finger_point_left_up.png',
                              duration: const Duration(
                                milliseconds: 900,
                              ),
                              offsetX: Offset.zero,
                              offsetY: Offset((80.px * 2) / 100, 0)),
                        ),
                      )
                    : Container(),

                //手指动画 up down
                index == 0
                    ? Positioned(
                        top: index == 0
                            ? 180.px
                            : index == 1
                                ? 210.px
                                : 80.px,
                        left: (375.px - 80.px) * 0.5,
                        child: SizedBox(
                          height: 80.px,
                          width: 80.px,
                          child: MoveAnimation(
                            viewW: 80.px,
                            imgName: 'finger_point_up_down.png',
                            duration: const Duration(milliseconds: 900),
                            offsetX: Offset.zero,
                            offsetY: Offset(
                              0,
                              (80.px * 2) / 100,
                            ),
                          ),
                        ),
                      )
                    : Container(),

                //搜索视图 / 更多专题视图
                index == 1 || index == 6
                    ? Positioned(
                        top: 40.px,
                        right: 0,
                        child: searchView(model),
                      )
                    : Container(),

                //分享 点赞, 专题 view
                Positioned(
                  top: 400.px,
                  right: 0,
                  child: shareAndStarView(model),
                )
              ],
            );
          }),
    );
  }

//搜索 view
  Widget searchView(GuidePageModel model) {
    return SizedBox(
      height: 80.px,
      width: 375.px,
      child: Row(
        children: [
          Expanded(
              flex: 5,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Positioned(
                    top: 20.px,
                    right: model.index == 6 ? -10.px : -40.px,
                    child: SizedBox(
                      height: 80.px,
                      width: 80.px,
                      child: const ScaleAnimation(
                        imgName: 'finger_point_right_up.png',
                        duration: Duration(milliseconds: 900),
                        begin: 0.6,
                        end: 1.2,
                      ),
                    ),
                  )
                ],
              )),
          Expanded(
            flex: 1,
            child: SizedBox(
              child: model.index == 1
                  ? SizedBox(
                      height: 60.px,
                      width: 50.px,
                      child: Center(
                        child: Image.asset(
                          width: 44.px,
                          height: 44.px,
                          ImageHelper.wrapAssets("guide_video_search.png"),
                        ),
                      ),
                    )
                  : SizedBox(
                      height: 65.px,
                      width: 65.px,
                      child: Center(
                        child: Image.asset(
                          width: 65.px,
                          height: 65.px,
                          ImageHelper.wrapAssets("guide_more_zhaunti.png"),
                        ),
                      ),
                    ),
            ),
          )
        ],
      ),
    );
  }

//分享 点赞, 专题 view
  Widget shareAndStarView(GuidePageModel model) {
    return SizedBox(
      height: 200.px,
      width: 375.px,
      child: Row(
        children: [
          Expanded(
            flex: 5,
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                // SizedBox(),
                model.index == 2 || model.index == 3 || model.index == 4
                    ? Positioned(
                        top: model.index == 2
                            ? 15.px
                            : model.index == 3
                                ? 75.px
                                : model.index == 4
                                    ? 135.px
                                    : 0,
                        right: -10.px,
                        child: SizedBox(
                            height: 80.px,
                            width: 80.px,
                            child: const ScaleAnimation(
                              imgName: 'finger_point_right_up.png',
                              duration: Duration(milliseconds: 900),
                              begin: 0.8,
                              end: 1.1,
                            )),
                      )
                    : Container(),
              ],
            ),
          ),
          Expanded(
            flex: 1,
            child: SizedBox(
              child: Column(
                children: [
                  model.index == 2
                      ? SizedBox(
                          height: 60.px,
                          width: 50.px,
                          child: Center(
                            child: Image.asset(
                              width: 44.px,
                              height: 44.px,
                              ImageHelper.wrapAssets("guide_head.png"),
                            ),
                          ),
                        )
                      : SizedBox(height: 60.px, width: 50.px),
                  model.index == 3
                      ? SizedBox(
                          height: 60.px,
                          width: 50.px,
                          child: Center(
                            child: Image.asset(
                              width: 44.px,
                              height: 44.px,
                              ImageHelper.wrapAssets("guide_share.png"),
                            ),
                          ),
                        )
                      : SizedBox(height: 60.px, width: 50.px),
                  model.index == 4
                      ? SizedBox(
                          height: 60.px,
                          width: 50.px,
                          child: Center(
                            child: Image.asset(
                              width: 52.px,
                              height: 52.px,
                              ImageHelper.wrapAssets("guide_zhanti.png"),
                            ),
                          ),
                        )
                      : SizedBox(height: 60.px, width: 50.px),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  loadVideos(bool isRefresh) {
    setState(() {
      isLoading = true;
    });
    if (isRefresh) {
      pageNo = 1;
      items = [];
      setState(() {});
    }

    VideoResponsitory.getVideoByPage({
      "firstCategory": widget.firstCategory,
      "orgCode": context.read<UserModel>().currentOrg?["orgCode"],
      "pageNo": pageNo,
      "pageSize": 10,
      "secondCategory": widget.secondCategory,
      "videoId": widget.videoId,
      "keyword": widget.keyWord ?? '',
      "myLikeVideo": widget.myLikeVideo ?? false,
    }).then((e) {
      isLoading = false;
      if (isRefresh) {
        items = e.data?.dataList ?? [];
        items.first.isfirst = true;
      } else {
        if (e.data!.dataList!.isNotEmpty) {
          items.addAll(e.data?.dataList ?? []);
        } else {
          showToast("没有更多的视频了");
        }
      }
      setState(() {});
    });
  }

  Future<void> _onRefresh() async {
    pageNo = 1;
    setState(() {
      items = [];
    });
    loadVideos(true);
  }

  void _onLoading() async {
    pageNo = pageNo + 1;
    loadVideos(false);
    if (mounted) setState(() {});
  }

  @override
  bool get wantKeepAlive => true;
}

class PlayerView extends StatefulWidget {
  final VideoItem item;
  final Function(VideoPlayerController) onVideoControllerInit;
  final Function(VideoItem) onError;
  final PageController? pageController;
  const PlayerView(
      {super.key,
      required this.item,
      required this.onVideoControllerInit,
      required this.onError,
      this.pageController});

  @override
  State<StatefulWidget> createState() => PlayerViewState();
}

class PlayerViewState extends State<PlayerView> {
  VideoPlayerController? _controller;
  bool showFullScreen = false;
  bool isFollow = false;
  bool isFunlScreen = false;
  bool isPlaying = false;
  VideoDetailResult? videoDetailResult;
  Timer? timer;
  String? watchStartTime;
  int duration = 0;
  bool error = false;
  final _debounce = Debouncer(milliseconds: 500);
  Fluwx fluwx = Fluwx();
  Duration position = Duration.zero;

  lookHistoryUpDate() {
    VideoResponsitory.videoScanHistory({
      "entranceType": "01",
      "videoUid": widget.item.uid,
      "watchedDuration": duration,
      "watchedStartTime": watchStartTime
    }).then((e) {
      Logger().i("-------------观看日志上传-----------");
    });
  }

  @override
  void initState() {
    super.initState();
    if (kDebugMode) {
      print("--------------------------初始化状态-----------------------------");
    }
    if (widget.item.playUrl != null) {
      //初始化播放器

      var playUrl =
          "${urlConfig.microfront}${widget.item.hlsUrl ?? widget.item.playUrl}";
      if (Platform.isIOS) {
        if (Platform.operatingSystemVersion.contains("18")) {
          playUrl = "${urlConfig.microfront}${widget.item.playUrl}";
        }
      }
      _controller = VideoPlayerController.networkUrl(Uri.parse(playUrl))
        ..initialize().onError((a, b) {
          debugPrint(b.toString());
        }).then((_) {
          // widget.onVideoControllerInit(_controller!);
          if (_controller!.value.aspectRatio > (4 / 3)) {
            showFullScreen = true; //视频宽高比大于4/3显示全屏按钮
          }
          _controller!.addListener(() {
            setState(() {
              position = _controller!.value.position;
              isPlaying = _controller!.value.isPlaying;
            });
          });
          _controller!.setLooping(true);
          _controller!.play();

          isPlaying = true;
          watchStartTime =
              DateFormat('yyyy-MM-dd HH:mm:ss').format(DateTime.now());
          timer = Timer.periodic(const Duration(seconds: 1), (timer) {
            duration = duration + 1;
          });
          if (mounted) {
            setState(() {});
          }
          widget.onVideoControllerInit(_controller!);
        });
      //加载视频详情
      loadDetail();
      lookHistoryUpDate();
    }
  }

  @override
  Widget build(BuildContext context) {
    return !error
        ? Scaffold(
            backgroundColor: Colors.black,
            body: LayoutBuilder(builder: (ctx, cons) {
              return Stack(
                children: [
                  GestureDetector(
                    onTap: () {
                      _controller!.value.isPlaying
                          ? _controller?.pause()
                          : _controller?.play();
                      isPlaying = _controller!.value.isPlaying;
                      if (mounted) {
                        setState(() {});
                      }
                    },
                    child: Center(
                      child: (_controller != null &&
                              _controller!.value.isInitialized)
                          ? SizedBox(
                              width: 375.px,
                              height: 375.px / _controller!.value.aspectRatio,
                              child: Stack(
                                children: [
                                  VideoPlayer(_controller!),
                                  Align(
                                    alignment: Alignment.center,
                                    child: Visibility(
                                        visible: !isPlaying,
                                        child: Image.asset(
                                            width: 60.px,
                                            ImageHelper.wrapAssets(
                                                "play.png"))),
                                  )
                                ],
                              ),
                            )
                          : Container(),
                    ),
                  ),
                  Positioned(
                      left: 0,
                      bottom: 0,
                      child: Image.asset(
                        ImageHelper.wrapAssets("shadow_bottom.png"),
                        width: 375.px,
                      )),
                  Positioned(
                    top: (cons.maxHeight / 2) +
                        (375.px / _controller!.value.aspectRatio / 2) +
                        20.px,
                    left: 147.5.px,
                    child: Offstage(
                      offstage: !showFullScreen,
                      child: GestureDetector(
                        onTap: () {
                          // _controller!.pause();

                          Navigator.of(context)
                              .push(MaterialPageRoute(builder: (ctx) {
                            return VideoPlayerFullScreenPage(
                              item: widget.item,
                              position: _controller!.value.position,
                              url: widget.item.playUrl ?? "",
                              controller: _controller!,
                            );
                          })).then((e) {
                            SystemChrome.setPreferredOrientations(
                                [DeviceOrientation.portraitUp]);
                            // AutoOrientation.portraitAutoMode();
                            loadDetail();
                            setState(() {});
                          });
                        },
                        child: Image.asset(
                            width: 80.px,
                            ImageHelper.wrapAssets("full_screen.png")),
                      ),
                    ),
                  ),
                  Positioned(
                      bottom: 150.px,
                      right: 5.px,
                      child: SizedBox(
                        width: 55.px,
                        height: 260.px,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 46.px,
                              height: 46.px,
                              margin: EdgeInsets.only(bottom: 10.px),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(26.px))),
                              child: ClipOval(
                                child: BdhNetworkImage(
                                  fit: BoxFit.cover,
                                  url:
                                      "${urlConfig.microfront}${widget.item.pics}",
                                  width: 42.px,
                                  height: 42.px,
                                  errorImage: "header.png",
                                ),
                              ),
                            ),
                            Column(
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    _debounce.run(() {
                                      VideoResponsitory.videoLike(
                                              {"videoId": widget.item.videoId})
                                          .then((res) {
                                        loadDetail();
                                      });
                                    });
                                  },
                                  child:

                                      // widget.item.isfirst == true
                                      //     ? IntroStepBuilder(
                                      //         padding: EdgeInsets.zero,
                                      //         order: 2,
                                      //         text: "点击星星点赞",
                                      //         overlayBuilder: (params) {
                                      //           return GuideStar(
                                      //             params: params,
                                      //             tipInfo:
                                      //                 '如果你对这个视频感兴趣，可点击屏幕右侧“心形”图标进行点赞',
                                      //           );
                                      //         },
                                      //         builder: (ctx, key) {
                                      //           return Image.asset(
                                      //               key: key,
                                      //               width: 36.px,
                                      //               height: 36.px,
                                      //               ImageHelper.wrapAssets(
                                      //                   (videoDetailResult?.data
                                      //                                   ?.isLike ==
                                      //                               1
                                      //                           ? true
                                      //                           : false)
                                      //                       ? "like.png"
                                      //                       : "unlike_shadow.png"));
                                      //         },
                                      //       )
                                      //     :

                                      Image.asset(
                                          width: 36.px,
                                          height: 36.px,
                                          ImageHelper.wrapAssets(
                                              (videoDetailResult
                                                              ?.data?.isLike ==
                                                          1
                                                      ? true
                                                      : false)
                                                  ? "like.png"
                                                  : "unlike_shadow.png")),
                                ),
                                Text(
                                  "${videoDetailResult?.data?.likeCount ?? "0"}",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 12.px,
                                    shadows: const [
                                      Shadow(
                                        color: Colors.black,
                                        offset: Offset(0, 0),
                                        blurRadius: 10.0,
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            ),
                            GestureDetector(
                              onTap: () {
                                gotoShareWechat();
                              },
                              child: Column(
                                children: [
                                  Image.asset(
                                      width: 36.px,
                                      height: 36.px,
                                      ImageHelper.wrapAssets(
                                          "share_shadow.png")),
                                  Text(
                                    // "${widget.item.forwardCount}",
                                    "${videoDetailResult?.data?.forwardCount ?? "0"}",
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w500,
                                        fontSize: 12.px,
                                        shadows: const [
                                          Shadow(
                                            color: Colors.black,
                                            offset: Offset(0, 0),
                                            blurRadius: 10.0,
                                          ),
                                        ]),
                                  )
                                ],
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                pauseVideo();
                                Navigator.of(context)
                                    .pushNamed('videoSecondTopicPage',
                                        arguments: DictNode(
                                            name: widget.item.firstCategoryName,
                                            code: widget.item.firstCategory))
                                    .then((res) {
                                  GlobalServiceView.needShowServiceBtn('video');
                                  loadDetail();
                                });
                                // Navigator.of(context)
                                //     .push(CupertinoPageRoute(builder: (ctx) {
                                //   return VideoSecondTopicPage(
                                //     item: DictNode(
                                //         name: widget.item.firstCategoryName,
                                //         code: widget.item.firstCategory),
                                //   );
                                // })).then((res) {
                                //   loadDetail();
                                // });
                              },
                              child: Container(
                                  alignment: Alignment.center,
                                  color: Colors.transparent,
                                  height: 70.px,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Container(
                                        width: 46.px,
                                        height: 46.px,
                                        alignment: Alignment.center,
                                        decoration: BoxDecoration(
                                          color: const Color.fromRGBO(
                                              255, 255, 255, 0.6),
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(10.px)),
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(8.px)),
                                          child: videoDetailResult != null
                                              ? BdhNetworkImage(
                                                  width: 42.px,
                                                  height: 42.px,
                                                  url:
                                                      "${urlConfig.microfront}${(videoDetailResult?.data?.topicImg ?? "").split(",").first}",
                                                )
                                              : Container(),
                                        ),
                                      ),
                                      Text(
                                        widget.item.firstCategoryName ?? "",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w600,
                                            fontSize: 12.px),
                                      )
                                    ],
                                  )),
                            )
                          ],
                        ),
                      )),
                  Positioned(
                      left: 0.px,
                      bottom: 0.px,
                      child: SizedBox(
                        width: 375.px,
                        height: 132.px,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding:
                                  EdgeInsets.only(left: 15.px, right: 15.px),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        padding: EdgeInsets.only(
                                            left: 5.px,
                                            top: 5.px,
                                            bottom: 5.px,
                                            right: 10.px),
                                        decoration: const BoxDecoration(
                                            color: Color.fromRGBO(
                                                255, 255, 255, 0.1)),
                                        child: Row(
                                          children: [
                                            Container(
                                              width: 18.px,
                                              height: 18.px,
                                              margin:
                                                  EdgeInsets.only(right: 5.px),
                                              alignment: Alignment.center,
                                              decoration: BoxDecoration(
                                                  color: HexColor("#0CC165"),
                                                  borderRadius:
                                                      BorderRadius.all(
                                                          Radius.circular(
                                                              2.px))),
                                              child: Image.asset(
                                                  fit: BoxFit.cover,
                                                  width: 12.px,
                                                  height: 12.px,
                                                  ImageHelper.wrapAssets(
                                                      "locate.png")),
                                            ),
                                            Text(
                                              widget.item.orgName ?? "",
                                              style: const TextStyle(
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.w600),
                                            )
                                          ],
                                        ),
                                      ),
                                      GestureDetector(
                                        onTap: () {
                                          // BDHResponsitory.saveMenuUse({
                                          //   "menuCode": "video-subject",
                                          //   "menuName": "专题",
                                          //   "menuType": "2",
                                          // }).then((res) {
                                          //   Log.i(res.msg);
                                          // });
                                          pauseVideo();
                                          Navigator.of(context)
                                              .pushNamed('videoSecondTopicPage',
                                                  arguments: DictNode(
                                                      name: widget.item
                                                          .firstCategoryName,
                                                      code: widget
                                                          .item.firstCategory))
                                              .then((res) {
                                            GlobalServiceView
                                                .needShowServiceBtn('video');
                                            loadDetail();
                                          });
                                          // Navigator.of(context).push(
                                          //     CupertinoPageRoute(
                                          //         builder: (ctx) {
                                          //   return VideoSecondTopicPage(
                                          //     item: DictNode(
                                          //         name: widget
                                          //             .item.firstCategoryName,
                                          //         code: widget
                                          //             .item.firstCategory),
                                          //   );
                                          // })).then((res) {
                                          //   loadDetail();
                                          // });
                                        },
                                        child: Container(
                                          margin: EdgeInsets.only(left: 4.px),
                                          padding: EdgeInsets.only(
                                              left: 5.px,
                                              top: 5.px,
                                              bottom: 5.px,
                                              right: 10.px),
                                          decoration: const BoxDecoration(
                                              color: Color.fromRGBO(
                                                  255, 255, 255, 0.1)),
                                          child: Row(
                                            children: [
                                              Container(
                                                width: 18.px,
                                                height: 18.px,
                                                margin: EdgeInsets.only(
                                                    right: 5.px),
                                                alignment: Alignment.center,
                                                decoration: BoxDecoration(
                                                    color: HexColor("#F7700F"),
                                                    borderRadius:
                                                        BorderRadius.all(
                                                            Radius.circular(
                                                                2.px))),
                                                child: Image.asset(
                                                    fit: BoxFit.cover,
                                                    width: 12.px,
                                                    height: 12.px,
                                                    ImageHelper.wrapAssets(
                                                        "topic.png")),
                                              ),
                                              Text.rich(TextSpan(children: [
                                                const TextSpan(
                                                    text: "专题",
                                                    style: TextStyle(
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.w600)),
                                                const TextSpan(
                                                    text: "  |  ",
                                                    style: TextStyle(
                                                        color: Color.fromRGBO(
                                                            255, 255, 255, 0.4),
                                                        fontWeight:
                                                            FontWeight.w600)),
                                                TextSpan(
                                                    text: widget.item
                                                            .firstCategoryName ??
                                                        "",
                                                    style: const TextStyle(
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.w600)),
                                              ]))
                                            ],
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  Container(
                                    height: 5.px,
                                  ),
                                  Text(
                                    "@${widget.item.authorName}",
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 14.px),
                                  ),
                                  SizedBox(
                                    height: 6.px,
                                  ),
                                  Text(
                                    widget.item.videoDesc ?? "",
                                    maxLines: 2,
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14.px,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  Visibility(
                                      visible: true,
                                      child: _controller != null
                                          ? Container(
                                              margin:
                                                  EdgeInsets.only(top: 8.px),
                                              child: ProgressBar(
                                                progress: position,
                                                total:
                                                    _controller!.value.duration,
                                                onSeek: (value) {
                                                  _controller!.pause();
                                                  _controller!.seekTo(value);
                                                  _controller!.play();
                                                },
                                                timeLabelType:
                                                    TimeLabelType.totalTime,
                                                timeLabelLocation:
                                                    TimeLabelLocation.sides,
                                                timeLabelTextStyle:
                                                    const TextStyle(
                                                        color: Colors.white,
                                                        fontWeight:
                                                            FontWeight.bold),
                                                thumbRadius: 6.h,
                                                thumbColor: Colors.white,
                                                baseBarColor:
                                                    const Color.fromRGBO(
                                                        255, 255, 255, 0.4),
                                                progressBarColor:
                                                    const Color.fromRGBO(
                                                        255, 255, 255, 0.6),
                                              ),
                                            )
                                          : Container())
                                ],
                              ),
                            ),
                          ],
                        ),
                      )),
                ],
              );
            }),
          )
        : Material(
            child: Container(
              color: Colors.black,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                      width: 60.px,
                      height: 60.px,
                      ImageHelper.wrapAssets("image_error.png")),
                  SizedBox(
                    height: 10.px,
                  ),
                  const Text("视频已删除",
                      style:
                          TextStyle(color: Color.fromRGBO(131, 149, 142, 1))),
                ],
              ),
            ),
          );
  }

  loadDetail() {
    VideoResponsitory.videoDetail(widget.item.videoId ?? 0).then((res) {
      if (res.code != 0) {
        setState(() {
          error = true;
          widget.onError(widget.item);
        });
      } else {
        if (mounted) {
          setState(() {
            videoDetailResult = res;
            error = false;
          });
        }
      }
    });
  }

  pauseVideo() {
    _controller?.pause();
    setState(() {
      isPlaying = false;
    });
  }

  gotoShareWechat() {
    Logger().i(' 微信分享');
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        useSafeArea: true,
        context: context,
        builder: (ctx) {
          return BdhShareView(
            shareWechatCallBack: () async {
              if (kDebugMode) {
                print('点击微信');
              }
              if (await isWXInstalled()) {
                //分享后打开的图文连接
                String linkUrl =
                    "${urlConfig.h5}/argic/VideoPage?videoId=${widget.item.videoId}";

                //分享的小图片
                // String imageUrl = "${urlConfig.microfront}${widget.item.pics}";
                String imageUrl =
                    "${urlConfig.microfront}${widget.item.coverImg}";
                if (imageUrl.isEmpty || imageUrl == "") {
                  CompressUtil.assetToUint8List(
                          ImageHelper.wrapAssets('defaulSharetImg.png'))
                      .then((endData) {
                    /// 分享到好友
                    var model = WeChatShareWebPageModel(
                      //链接
                      linkUrl,
                      //标题
                      title: widget.item.title ?? '',
                      //摘要
                      description: widget.item.videoDesc!.length > 50
                          ? "观看视频"
                          : widget.item.videoDesc,
                      //小图
                      // thumbnail: WeChatImage.network(imageUrl),
                      // thumbData: endData,

                      //微信消息
                      scene: WeChatScene.session,
                    );
                    fluwx.share(model).then((res) {
                      if (res) {
                        VideoResponsitory.videoForward({
                          "videoId": widget.item.videoId,
                          "forwardClick": 1
                        }).then((res) {
                          loadDetail();
                        });
                      }
                    });
                  });
                } else {
                  HomeService.getImageData(imageUrl).then((res) {
                    CompressUtil.u8ToU8(res ?? Uint8List(0)).then((endData) {
                      /// 分享到好友
                      var model = WeChatShareWebPageModel(
                        //链接
                        linkUrl,
                        //标题
                        title: widget.item.title ?? '',
                        //摘要
                        description: widget.item.videoDesc!.length > 50
                            ? "观看视频"
                            : widget.item.videoDesc,
                        //小图
                        // thumbnail: WeChatImage.network(imageUrl),
                        thumbData: endData,

                        //微信消息
                        scene: WeChatScene.session,
                      );
                      fluwx.share(model).then((res) {
                        if (res) {
                          VideoResponsitory.videoForward({
                            "videoId": widget.item.videoId,
                            "forwardClick": 1
                          }).then((res) {
                            loadDetail();
                          });
                        }
                      });
                    });
                  });
                }
              }
            },
            shareWechatMomentsCallBack: () async {
              if (kDebugMode) {
                print('点击微信朋友圈');
              }
              if (await isWXInstalled()) {
                //分享后打开的图文连接
                String linkUrl =
                    "${urlConfig.h5}/argic/VideoPage?videoId=${widget.item.videoId}";

                //分享的小图片
                // String imageUrl = "${urlConfig.microfront}${widget.item.pics}";
                String imageUrl =
                    "${urlConfig.microfront}${widget.item.coverImg}";
                if (imageUrl.isEmpty || imageUrl == "") {
                  CompressUtil.assetToUint8List(
                          ImageHelper.wrapAssets('defaulSharetImg.png'))
                      .then((endData) {
                    /// 分享到好友
                    var model = WeChatShareWebPageModel(
                      //链接
                      linkUrl,
                      //标题
                      title: widget.item.title ?? '',
                      //摘要
                      description: widget.item.videoDesc!.length > 50
                          ? "观看视频"
                          : widget.item.videoDesc,
                      //小图
                      // thumbnail: WeChatImage.network(imageUrl),
                      // thumbData: endData,

                      //微信消息
                      scene: WeChatScene.timeline,
                    );

                    fluwx.share(model).then((res) {
                      if (res) {
                        VideoResponsitory.videoForward({
                          "videoId": widget.item.videoId,
                          "forwardClick": 1
                        }).then((res) {
                          loadDetail();
                        });
                      }
                    });
                  });
                } else {
                  HomeService.getImageData(imageUrl).then((res) {
                    CompressUtil.u8ToU8(res ?? Uint8List(0)).then((endData) {
                      /// 分享到好友
                      var model = WeChatShareWebPageModel(
                        //链接
                        linkUrl,
                        //标题
                        title: widget.item.title ?? '',
                        //摘要
                        description: widget.item.videoDesc!.length > 50
                            ? "观看视频"
                            : widget.item.videoDesc,
                        //小图
                        // thumbnail: WeChatImage.network(imageUrl),
                        thumbData: endData,

                        //微信消息
                        scene: WeChatScene.timeline,
                      );

                      fluwx.share(model).then((res) {
                        if (res) {
                          VideoResponsitory.videoForward({
                            "videoId": widget.item.videoId,
                            "forwardClick": 1
                          }).then((res) {
                            loadDetail();
                          });
                        }
                      });
                    });
                  });
                }
              }
            },
          );
        });
  }

  Future<bool> isWXInstalled() async {
    var result = await fluwx.isWeChatInstalled;
    if (!result) {
      showToast("无法打开微信 请检查是否安装了微信");
      return false;
    }
    return true;
  }

  @override
  void dispose() {
    super.dispose();
    _controller?.dispose();
    timer?.cancel();
    Logger().i("-------------播放器销毁-----------");
  }
}
