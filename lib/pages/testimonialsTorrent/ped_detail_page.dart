import 'package:bdh_smart_agric_app/components/bdh_no_data.dart';
import 'package:bdh_smart_agric_app/model/ped_detail_model.dart';
import 'package:bdh_smart_agric_app/model/recommend_model.dart';
import 'package:bdh_smart_agric_app/utils/request/testimonials_torrent_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class PedDetailPage extends StatefulWidget {
  final Records record;

  const PedDetailPage({super.key, required this.record});

  @override
  State<PedDetailPage> createState() => _PedDetailPageState();
}

class _PedDetailPageState extends State<PedDetailPage> {
  PedDetailModel? pedDetail;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    initData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: Text(widget.record.procName ?? '荐种清单',
              style: TextStyle(
                  fontSize: 16.px,
                  color: Colors.black,
                  fontWeight: FontWeight.bold)),
          centerTitle: true),
      body: pedDetail == null
          ? const Center(
        child: BdhNoData(desc: "暂无品种信息"),
      )
          : Container(
        color: const Color(0xffF2F5F9),
        child: Container(
            margin: EdgeInsets.all(12.px),
            padding: EdgeInsets.all(12.px),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(12.px)),
            ),
            child: ListView(
              children: [
                getItemView('品种类型', pedDetail?.pedType ?? '-'),
                getItemView(
                    '年份', pedDetail?.approvalYear?.toString() ?? '-'),
                getItemView('是否审定', pedDetail?.approvalStatus ?? '-'),
                getItemView('审定编号', pedDetail?.registerCertNo ?? '-'),
                getItemView('品种原代号', pedDetail?.sourceCode ?? '-'),
                getItemView(
                    '主茎叶数', pedDetail?.raiseLeafMax?.toString() ?? '-'),
                getItemView(
                    '生育日数', pedDetail?.growthDays?.toString() ?? '-'),
                getItemView('是否转基因', pedDetail?.transgeneFlag ?? '-'),
                getItemView(
                    '转基因目标性状', pedDetail?.transgenicTargetTrait ?? '-'),
                getItemView(
                    '转化体名称', pedDetail?.transgenicBodyName ?? '-'),
                getItemView(
                    '转化体所有者', pedDetail?.transgenicBodyOwner ?? '-'),
                getItemView('农业转基因生物安全证书编号',
                    pedDetail?.gmBioSafetyCertNumber ?? '-'),
                getItemView('适种区域', pedDetail?.adaptRegion ?? '-'),
                getItemView('申请者', pedDetail?.applicant ?? '-'),
                getItemView('育种者', pedDetail?.breeder ?? '-'),
                getItemView('母本', pedDetail?.femaleName ?? '-'),
                getItemView('父本', pedDetail?.maleName ?? '-'),
                getItemView(
                    '初试产量(kg)', pedDetail?.firstTrialYield ?? '-'),
                getItemView(
                    '选育方法', pedDetail?.breedingType ?? '-'),
                getItemView(
                    '初试比对照增产(%)', pedDetail?.firstTrialYieldIncreaseVsControl?.toString() ?? '-'),
                getItemView(
                    '复试产量(kg)', pedDetail?.secondTrialYield ?? '-'),
                getItemView(
                    '复试比对照增产(%)', pedDetail?.secondTrialYieldIncreaseVsControl?.toString() ?? '-'),
                getItemView('区试平均产量(kg)',
                    pedDetail?.regionalTrialAvgYield ?? '-'),
                getItemView(
                    '区试平均比对照增产(%)', pedDetail?.regionalTrialAvgYieldIncreaseVsControl?.toString() ?? '-'),
                getItemView(
                    '生试产量(kg)', pedDetail?.productionTrialYield ?? '-'),
                getItemView(
                    '生试比对照增产(%)', pedDetail?.productionTrialYieldIncreaseVsControl?.toString() ?? '-'),
                getItemView(
                    '对照', pedDetail?.control ?? '-'),
                getItemView(
                    '≥10℃活动积温(℃)', pedDetail?.activeAccuTemp ?? '-'),
                getItemView('特征特性', pedDetail?.characteristics ?? '-'),
                getItemView('产量表现', pedDetail?.yieldPerformance ?? '-'),
                getItemView(
                    '栽培技术要点', pedDetail?.cultivationFactors ?? '-'),
                getItemView(
                    '注意事项', pedDetail?.notes ?? '-'),
                getItemView(
                    '市场份额', pedDetail?.marketShare ?? '-')
              ],
            )),
      ),
    );
  }

  /** * 构建一个包含标题和值的项视图组件，支持点击弹出对话框。
   * * * @param title 项视图的标题文本，通常用于描述值的含义
   * * @param value 项视图显示的具体数值或内容
   * * @param showDialogTextNum 控制点击后弹出对话框中显示的文本数量
   * * @return 返回一个包含标题和值布局的Widget */
  Widget getItemView(String title, String value) {
    List<Widget> widgets = [
      Text(
        title,
        style: TextStyle(fontSize: 16.px, color: const Color(0xff333333)),
      ),
      Expanded(
          child: Text(
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            value,
            textAlign: TextAlign.end,
            style: TextStyle(fontSize: 16.px, color: const Color(0xff666666)),
          ))
    ];

    VoidCallback? clickListener;
    if (title.length + value.length > 16) {
      widgets.add(Icon(Icons.navigate_next,
          color: const Color(0xff333333), size: 20.px));
      clickListener = () {
        showDialog(
            context: context,
            builder: (context) {
              return AlertDialog(
                backgroundColor: Colors.white,
                title: Text(title),
                content: Text(value),
              );
            });
      };
    }

    return GestureDetector(
      onTap: () {
        if(clickListener != null) clickListener();
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 12.px),
        height: 40.px,
        alignment: Alignment.centerLeft,
        child: Row(
          children: widgets,
        ),
      ),
    );
  }

  initData() {
    TestimonialsTorrentService.getPedDetail({"breedCd": widget.record.breedCd})
        .then((value) {
      setState(() {
        pedDetail = value;
      });
    });
  }
}
