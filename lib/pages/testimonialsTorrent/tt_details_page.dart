import 'dart:convert';

import 'package:bdh_smart_agric_app/components/bdh_no_data.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/ped_recommend_model.dart';
import 'package:bdh_smart_agric_app/model/recommend_model.dart';
import 'package:bdh_smart_agric_app/utils/request/testimonials_torrent_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class TtDetailsPage extends StatefulWidget {
  final PedRecommendModel pedItem;

  const TtDetailsPage({super.key, required this.pedItem});

  @override
  State<TtDetailsPage> createState() => _TtDetailsPageState();
}

class _TtDetailsPageState extends State<TtDetailsPage> {
  ScrollController synfController = ScrollController();
  ScrollController pzmcController = ScrollController();
  Map<String, ScrollController> keyIndicatorsControllers = {};
  Map<String, ScrollController> referenceIndicatorsControllers = {};
  RecommendModel? recommendModel;
  Map<TraitFilters, List<TraitDataList>> keyIndicatorsMap = {};
  Map<TraitFilters, List<TraitDataList>> referenceIndicatorsMap = {};
  Map<int, TraitFilters> traitFiltersMap = {};
  List<Records> records = [];
  List<String> years = [];
  bool _isSyncing = false; // 防止循环滚动的标志位
  Map<String, int> tabMap = {"关键指标": 1, "参考指标": 2};
  String curTraitType = '关键指标';
  final String _PZMC_key = 'PZMC';
  final String _SYNF_key = 'SYNF';

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    initData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
          title: Text(widget.pedItem.pedRecommendName ?? '荐种清单',
              style: TextStyle(
                  fontSize: 16.px,
                  color: Colors.black,
                  fontWeight: FontWeight.bold)),
          centerTitle: true),
      body: recommendModel == null
          ? const Center(
              child: BdhNoData(desc: "暂无品种信息"),
            )
          : Container(
              color: const Color(0xffF2F5F9),
              child: Column(
                children: [
                  getTopTitleView(),
                  Expanded(
                    child: Container(
                      margin: EdgeInsets.all(12.px),
                      padding: EdgeInsets.all(12.px),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(Radius.circular(12.px)),
                      ),
                      child: getContentView(),
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget getTopTitleView() {
    return Container(
      padding: EdgeInsets.all(12.px),
      margin: EdgeInsets.only(left: 12.px, right: 12.px, top: 12.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(12.px)),
      ),
      alignment: Alignment.center,
      child: Text(
        '橙色标记的性状为品种筛选的关键指标,黑色为参考指标',
        style: TextStyle(
          fontSize: 12.px,
          fontWeight: FontWeight.bold,
          color: const Color(0xffff7c24),
        ),
      ),
    );
  }

  Widget getContentView() {
    List<String> traitNameList = records.map((e) => e.procName ?? '-').toList();
    List<TraitFilters> keyIndicatorsKeys = keyIndicatorsMap.keys.toList();
    List<TraitFilters> referenceIndicatorsKeys =
        referenceIndicatorsMap.keys.toList();
    int dataCount = keyIndicatorsKeys.length;
    dataCount += referenceIndicatorsKeys.length;
    setControllerListener(pzmcController);
    setControllerListener(synfController);
    return Column(
      children: [
        getTableItem('品种名称', traitNameList, pzmcController,
            canScroll: true,
            canDivider: true,
            valueStyle: TextStyle(
                fontSize: 14.px,
                color: const Color(0xff028B5D),
                fontWeight: FontWeight.bold),
            onTap: (value) => toDetail(value)),
        getTableItem('试验年份', years, synfController, canDivider: true),
        Expanded(
            child: ListView.builder(
                itemCount: dataCount,
                itemBuilder: (context, index) {
                  List<TraitFilters> traitFilters = [];
                  int newIndex = index;
                  if (index < keyIndicatorsKeys.length) {
                    traitFilters = keyIndicatorsKeys.toList();
                  } else {
                    newIndex = index - keyIndicatorsKeys.length;
                    traitFilters = referenceIndicatorsKeys.toList();
                  }
                  TraitFilters filter = traitFilters[newIndex];
                  List<TraitDataList> traitList = [];
                  if (index < keyIndicatorsKeys.length) {
                    traitList = keyIndicatorsMap[filter] ?? [];
                  } else {
                    traitList = referenceIndicatorsMap[filter] ?? [];
                  }
                  List<String> traitStrList =
                      traitList.map((e) => e.value ?? '-').toList();
                  ScrollController controller = getController(filter);
                  String title = filter.surveyName ?? '-';
                  String? filterData = filter.filterData;
                  if (filterData != null && filterData.length > 3) {
                    var dataJson = jsonDecode(filterData);
                    title += "(${dataJson['start']}-${dataJson['end']})";
                  }
                  print('当前数据是: $dataCount.     $title');
                  if (index < keyIndicatorsKeys.length) {
                    TextStyle style = TextStyle(
                        fontSize: 14.px,
                        color: const Color(0xffff7c24),
                        fontWeight: FontWeight.bold);
                    return getTableItem(title, traitStrList, controller,
                        titleStyle: style);
                  } else {
                    return getTableItem(title, traitStrList, controller);
                  }
                }))
      ],
    );
  }

  Widget getTopView() {
    List<String> traitNameList = records.map((e) => e.procName ?? '-').toList();
    setControllerListener(pzmcController);
    setControllerListener(synfController);
    return Container(
      height: 120.px,
      margin: EdgeInsets.only(bottom: 30.px),
      child: Column(
        children: [
          getTableItem('品种名称', traitNameList, pzmcController,
              canScroll: true,
              canDivider: true,
              valueStyle: TextStyle(
                  fontSize: 14.px,
                  color: const Color(0xff028B5D),
                  fontWeight: FontWeight.bold),
              onTap: (value) => toDetail(value)),
          getTableItem('试验年份', years, synfController, canDivider: true),
        ],
      ),
    );
  }

  Widget getTableView() {
    return Container(
      height: 60.px,
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          getTopTabItem('关键指标'),
          getTopTabItem('参考指标'),
        ],
      ),
    );
  }

  Widget getTraitInfoView() {
    int type = tabMap[curTraitType] ?? 1;
    List<TraitFilters> traitFilters = [];
    if (type == 1) {
      traitFilters = keyIndicatorsMap.keys.toList();
    } else {
      traitFilters = referenceIndicatorsMap.keys.toList();
    }
    return Expanded(
        child: ListView.builder(
            itemCount: traitFilters.length,
            itemBuilder: (context, index) {
              TraitFilters filter = traitFilters[index];
              List<TraitDataList> traitList = [];
              if (type == 1) {
                traitList = keyIndicatorsMap[filter] ?? [];
              } else {
                traitList = referenceIndicatorsMap[filter] ?? [];
              }
              List<String> traitStrList =
                  traitList.map((e) => e.value ?? '-').toList();
              ScrollController controller = getController(filter);
              String title = filter.surveyName ?? '-';
              String? filterData = filter.filterData;
              if (filterData != null && filterData.length > 3) {
                var dataJson = jsonDecode(filterData);
                title += "(${dataJson['start']}-${dataJson['end']})";
              }
              return getTableItem(title, traitStrList, controller);
            }));
  }

  Widget getTopTabItem(title) {
    return GestureDetector(
        onTap: () {
          curTraitType = title;
          setState(() {});
        },
        child: Container(
          height: 60.px,
          width: 90.px,
          alignment: Alignment.center,
          decoration: title == curTraitType
              ? BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          color: const Color(0xff028B5D), width: 2.px)))
              : null,
          child: Text(title,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: 14.px,
                  color: title == curTraitType
                      ? const Color(0xff028B5D)
                      : const Color(0xff333333))),
        ));
  }

  Widget getTableItem(
      String title, List<String> valueList, ScrollController controller,
      {TextStyle? titleStyle,
      TextStyle? valueStyle,
      bool canScroll = true,
      bool canDivider = false,
      ValueChanged<String>? onTap}) {
    return Container(
      height: 60.px,
      alignment: Alignment.centerLeft,
      child: Row(
        children: [
          getSingleTableItem(
              title,
              titleStyle ??
                  TextStyle(
                      fontSize: 14.px,
                      color: Colors.black,
                      fontWeight: FontWeight.bold)),
          Expanded(
            child: ListView.separated(
                separatorBuilder: (context, index) {
                  return Container(
                    width: 1.px,
                    height: 60.px,
                    color: canDivider ? const Color(0xff999999) : Colors.white,
                  );
                },
                controller: controller,
                scrollDirection: Axis.horizontal,
                physics: canScroll
                    ? const ClampingScrollPhysics() // 允许滚动
                    : const NeverScrollableScrollPhysics(),
                // 禁止滚动
                itemCount: valueList.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      if (onTap != null) onTap(valueList[index]);
                    },
                    child: getSingleTableItem(
                        valueList[index],
                        valueStyle ??
                            TextStyle(fontSize: 14.px, color: Colors.black)),
                  );
                }),
          )
        ],
      ),
    );
  }

  Widget getSingleTableItem(String value, TextStyle style) {
    return Container(
      width: 90.px,
      height: 60.px,
      alignment: Alignment.center,
      child: Text(value,
          style: style, maxLines: 2, overflow: TextOverflow.ellipsis),
    );
  }

  initData() {
    TestimonialsTorrentService.recommend(widget.pedItem.pedRecommendId)
        .then((value) {
      recommendModel = value;
      List<TraitFilters> traitFilters =
          recommendModel?.pedRecommendData?.traitFilters ?? [];
      if (traitFilters.isNotEmpty) {
        for (var value in traitFilters) {
          if (value.indType == 1) {
            keyIndicatorsMap[value] = [];
          } else {
            referenceIndicatorsMap[value] = [];
          }
          traitFiltersMap[value.surveyCfgId ?? -1] = value;
        }
      }

      records = recommendModel?.pedRecommendData?.records ?? [];
      for (var record in records) {
        years.add(record.statYear ?? '-');
        List<TraitDataList> traitDataList = record.traitDataList ?? [];
        if (traitDataList.isEmpty) continue;
        for (var data in traitDataList) {
          TraitFilters? filters = traitFiltersMap[data.surveyCfgId ?? -1];
          if (filters == null) continue;
          if (filters.indType == 1) {
            keyIndicatorsMap[filters]?.add(data);
          } else {
            referenceIndicatorsMap[filters]?.add(data);
          }
        }
      }
      setState(() {});
    });
  }

  getController(TraitFilters filter) {
    Map<String, ScrollController> controllerMap;
    if (filter.indType == 1) {
      controllerMap = keyIndicatorsControllers;
    } else {
      controllerMap = referenceIndicatorsControllers;
    }
    String key = filter.surveyCfgId?.toString() ?? '';
    if (controllerMap.containsKey(key)) {
      return controllerMap[key]!;
    }
    ScrollController controller = ScrollController();
    setControllerListener(controller);
    controllerMap[key] = controller;

    return controller;
  }

  setControllerListener(ScrollController controller) {
    controller.addListener(() {
      if (!_isSyncing) {
        _isSyncing = true;
        int type = tabMap[curTraitType] ?? 1;
        // Map<String, ScrollController> controllerMap;
        // if (type == 1) {
        //   controllerMap = keyIndicatorsControllers;
        // } else {
        //   controllerMap = referenceIndicatorsControllers;
        // }
        if (keyIndicatorsControllers.isEmpty &&
            referenceIndicatorsControllers.isEmpty) return;
        List<ScrollController> controllerList =
            keyIndicatorsControllers.values.toList();
        List<ScrollController> controllerList2 =
            referenceIndicatorsControllers.values.toList();
        for (ScrollController item in controllerList) {
          if (item == controller) continue;
          item.jumpTo(
            controller.offset,
          );
        }
        for (ScrollController item in controllerList2) {
          if (item == controller) continue;
          item.jumpTo(
            controller.offset,
          );
        }
        if (controller != synfController) {
          synfController.jumpTo(controller.offset);
        }

        if (controller != pzmcController) {
          pzmcController.jumpTo(controller.offset);
        }
        _isSyncing = false;
      }
    });
  }

  toDetail(String pedName) {
    Records? record;
    for (var item in this.records) {
      if (item.procName == pedName) {
        record = item;
        break;
      }
    }
    if (record == null) return;
    Navigator.pushNamed(context, RouteName.pedDetailsPage, arguments: record);
  }
}
