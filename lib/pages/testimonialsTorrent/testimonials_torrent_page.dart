import 'package:bdh_smart_agric_app/components/bdh_no_data.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/ped_recommend_model.dart';
import 'package:bdh_smart_agric_app/model/testimonials_torrent_base_model.dart';
import 'package:bdh_smart_agric_app/utils/request/testimonials_torrent_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class TestimonialsTorrentPage extends StatefulWidget {
  const TestimonialsTorrentPage({super.key});

  @override
  State<TestimonialsTorrentPage> createState() =>
      _TestimonialsTorrentPageState();
}

class _TestimonialsTorrentPageState extends State<TestimonialsTorrentPage>
    with SingleTickerProviderStateMixin {
  List<TestimonialsTorrentBaseModel> cropNameModelList = [];
  List<TestimonialsTorrentBaseModel> zoologyModelList = [];
  List<PedRecommendModel> pedRecommendModelList = [];
  Map<String, List<PedRecommendModel>> recommendMap = {};
  TestimonialsTorrentBaseModel? curZoology;
  TestimonialsTorrentBaseModel? curCrop;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    initData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text('荐种清单',
              style: TextStyle(
                  fontSize: 16.px,
                  color: Colors.black,
                  fontWeight: FontWeight.bold)),
          centerTitle: true,
          actions: [getZoologyDropdown()],
        ),
        body: Column(
          children: [
            Container(
              width: MediaQuery.of(context).size.width,
              height: 40.px,
              color: Colors.white,
              alignment: Alignment.center,
              child: getTopTab(),
            ),
            Expanded(
                child: pedRecommendModelList.isEmpty
                    ? const Center(
                        child: BdhNoData(desc: "暂无荐种信息"),
                      )
                    : Container(
                        color: const Color(0xffF2F5F9),
                        padding: EdgeInsets.only(
                            left: 12.px,
                            right: 12.px,
                            top: 12.px,
                            bottom: 12.px),
                        child: ListView.builder(
                          itemBuilder: (context, index) {
                            return getPedRecommendItem(index);
                          },
                          itemCount: pedRecommendModelList.length,
                        ),
                      ))
          ],
        ));
  }

  Widget getTopTab() {
    if (cropNameModelList.isEmpty) return Container();
    return ListView.builder(
      scrollDirection: Axis.horizontal,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        return getTopTabItem(index);
      },
      itemCount: cropNameModelList.length,
    );
  }

  Widget getTopTabItem(index) {
    TestimonialsTorrentBaseModel itemData = cropNameModelList[index];
    return GestureDetector(
        onTap: () => setCurCrop(itemData),
        child: Container(
          height: 40.px,
          width: 60.px,
          alignment: Alignment.center,
          decoration: itemData == curCrop
              ? BoxDecoration(
                  border: Border(
                      bottom: BorderSide(
                          color: const Color(0xff028B5D), width: 2.px)))
              : null,
          child: Text(itemData.name ?? '-',
              style: TextStyle(
                  fontSize: 14.px,
                  color: itemData == curCrop
                      ? const Color(0xff028B5D)
                      : const Color(0xff333333))),
        ));
  }

  Widget getPedRecommendItem(index) {
    PedRecommendModel itemData = pedRecommendModelList[index];
    return GestureDetector(
        onTap: () => Navigator.of(context).pushNamed(RouteName.ttDetailsPage, arguments: itemData),
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6.px), color: Colors.white),
          margin: EdgeInsets.only(bottom: 4.px),
          padding: EdgeInsets.only(top: 15.px, bottom: 15.px),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    height: 24.px,
                    width: 6.px,
                    color: const Color(0xff028B5D),
                  ),
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(left: 9.px),
                      child: Text(itemData.pedRecommendName ?? '-',
                          style: TextStyle(
                              fontSize: 16.px,
                              color: const Color(0xff333333),
                              fontWeight: FontWeight.bold)),
                    ),
                  ),
                  Container(
                    padding: EdgeInsets.only(
                        left: 6.px, right: 6.px, top: 2.px, bottom: 2.px),
                    margin: EdgeInsets.only(right: 15.px),
                    color: const Color(0xff0BA64D),
                    child: Text('查看',
                        style: TextStyle(
                            fontSize: 14.px,
                            color: Colors.white,
                            fontWeight: FontWeight.bold)),
                  )
                ],
              ),
              getPedRecommendItemItem(
                  '荐种标准', itemData.pedRecommendTemplateName ?? '-'),
              getPedRecommendItemItem('试验年份', itemData.years ?? '-'),
              getPedRecommendItemItem(
                  '推荐品种数量', itemData.pedNum?.toString() ?? '-'),
              getPedRecommendItemItem('推荐人', itemData.recommenderName ?? '-'),
            ],
          ),
        ));
  }

  Widget getPedRecommendItemItem(title, value) {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 4.px, left: 15.px, right: 15.px),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title,
              style:
                  TextStyle(fontSize: 12.px, color: const Color(0xff666666))),
          Text(value,
              style: TextStyle(fontSize: 14.px, color: const Color(0xff333333)))
        ],
      ),
    );
  }

  DropdownButtonHideUnderline getZoologyDropdown() {
    return DropdownButtonHideUnderline(
        child: SizedBox(
      width: 100.px,
      child: DropdownButton<TestimonialsTorrentBaseModel>(
        items: getDropdownMenuItemList(zoologyModelList),
        onChanged: (value) => setCurZoology(value!),
        hint: Padding(
          padding: EdgeInsets.only(left: 10.px),
          child: const Text('请选择'),
        ),
        isExpanded: true,
        value: curZoology,
        //设置阴影的高度
        elevation: 8,
        style: TextStyle(fontSize: 14.px, color: Colors.black),
        //减少按钮的高度。默认情况下，此按钮的高度与其菜单项的高度相同。如果isDense为true，则按钮的高度减少约一半。 这个当按钮嵌入添加的容器中时，非常有用
        isDense: true,
        iconSize: 20.px,
      ),
    ));
  }

  List<DropdownMenuItem<TestimonialsTorrentBaseModel>> getDropdownMenuItemList(
      List<TestimonialsTorrentBaseModel> languages) {
    return languages
        .map((l) => DropdownMenuItem<TestimonialsTorrentBaseModel>(
              value: l,
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(left: 10.px),
                      child: Text(
                        l.name!,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  )
                ],
              ),
            ))
        .toList();
  }

  setZoology(TestimonialsTorrentBaseModel curZoology) {
    this.curZoology = curZoology;
    setState(() {});
  }

  initData() {
    TestimonialsTorrentService.getZoology().then((value) {
      zoologyModelList = value;
      if (zoologyModelList.isNotEmpty) {
        curZoology = zoologyModelList[0];
      }
      setState(() {});
      getCropList();
    });
  }

  getCropList() {
    TestimonialsTorrentService.getCropList().then((value) {
      cropNameModelList = value;
      if (cropNameModelList.isNotEmpty) {
        curCrop = cropNameModelList[0];
      }
      setState(() {});
      getRecommendList();
    });
  }

  setCurZoology(TestimonialsTorrentBaseModel curZoology) {
    this.curZoology = curZoology;
    pedRecommendModelList = [];
    setState(() {});
    getRecommendList();
  }

  setCurCrop(TestimonialsTorrentBaseModel curCrop) {
    this.curCrop = curCrop;
    pedRecommendModelList = [];
    setState(() {});
    getRecommendList();
  }

  getRecommendList() {
    if (curZoology == null) return;
    if (curCrop == null) return;
    String key = "${curZoology!.code}&${curCrop!.code}";
    Map<String, dynamic> param = {
      'ecoregionCode': curZoology!.code,
      'raiseCropsCd': curCrop!.code
    };
    if (recommendMap.containsKey(key)) {
      print('当前进来没？？？$key');
      var list = recommendMap[key];
      if (list != null) {
        pedRecommendModelList = list;
        setState(() {});
        return;
      }
    }
    TestimonialsTorrentService.getRecommendList(param).then((value) {
      pedRecommendModelList = value;
      if (pedRecommendModelList.isNotEmpty) {
        recommendMap[key] = pedRecommendModelList;
      }
      setState(() {});
    });
  }
}
