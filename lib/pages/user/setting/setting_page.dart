import 'dart:io';

import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/model/version_result.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_segment_line.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/user/setting/cancel_account.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page_inappweb.dart';
import 'package:bdh_smart_agric_app/pages/user/setting/account_manage.dart';
import 'package:bdh_smart_agric_app/pages/user/tab_user.dart';
import 'package:bdh_smart_agric_app/pages/version/bdh_newversion_view.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:install_plugin/install_plugin.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'package:app_settings/app_settings.dart';

class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  State<StatefulWidget> createState() => _SettingPageState();
}

class _SettingPageState extends State {
  bool showCancelCount = false;
  @override
  void initState() {
    super.initState();
    var useInfo = StorageUtil.userInfo();
    if (useInfo?.data?.idCard != null) {
      setState(() {
        showCancelCount = false;
      });
    } else {
      setState(() {
        showCancelCount = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("设置"),
      ),
      body: SizedBox(
        width: 375.px,
        child: Column(
          children: [
            BdhSegmentLine(width: 375.px),
            UserMenuItemView(
                item: UserMenuItem(
                    title: "用户协议",
                    onClick: () {
                      Navigator.of(context)
                          .push(CupertinoPageRoute(builder: (context) {
                        return const HtmlPage1(
                            title: "用户协议",
                            content: "https://www.bdhic.com/Anticipation.html");
                      }));
                    })),
            UserMenuItemView(
                item: UserMenuItem(
                    title: "隐私政策",
                    onClick: () {
                      Navigator.of(context)
                          .push(CupertinoPageRoute(builder: (context) {
                        return const HtmlPage1(
                            title: "隐私政策",
                            content:
                                "https://smartagric.bdhic.com/privacy.html");
                      }));
                    })),
            UserMenuItemView(
                item: UserMenuItem(
                    title: "账号管理",
                    onClick: () {
                      Navigator.of(context)
                          .push(CupertinoPageRoute(builder: (context) {
                        return const AccountManagePage();
                      }));
                    })),
            UserMenuItemView(
                item: UserMenuItem(
                    title: "权限管理",
                    onClick: () {
                      AppSettings.openAppSettings();
                    })),
            const VersionItemView(),
            SizedBox(
              height: 20.px,
            ),
            const Spacer(),
            GestureDetector(
              onTap: () {
                TDToast.showLoadingWithoutText(
                    context: context, preventTap: true);
                BDHResponsitory.logout({}).then((res) {
                  TDToast.dismissLoading();
                  StorageManager.storage?.clear();
                  if (mounted) {
                    Navigator.of(context).pop();
                    // Navigator.of(context)
                    //     .popAndPushNamed(RouteName.login)
                    //     .then((res) {
                    //   GlobalServiceView.hidenView();
                    // });

                    Navigator.of(context)
                        .popAndPushNamed(RouteName.loginBdhDigitalPage)
                        .then((res) {
                      GlobalServiceView.hidenView();
                    });
                  }
                });
              },
              child: Container(
                width: 355.px,
                height: 52.px,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(26.px)),
                    // color: const Color.fromRGBO(254, 44, 85, 1),
                    border: Border.all(
                        width: 1.px,
                        color: const Color.fromRGBO(226, 235, 231, 1))),
                child: Text(
                  "退出登录",
                  style: TextStyle(
                      color: const Color.fromRGBO(254, 44, 85, 1),
                      fontSize: 16.px,
                      fontWeight: FontWeight.w400),
                ),
              ),
            ),
            const SizedBox(height: 60),
          ],
        ),
      ),
    );
  }
}

class VersionItemView extends StatefulWidget {
  const VersionItemView({super.key});

  @override
  State<StatefulWidget> createState() => _VersionItemViewState();
}

class _VersionItemViewState extends State<VersionItemView> {
  bool hasNewVersion = false;
  VersionResult? versionResult;
  PackageInfo? initPackageInfo;
  @override
  void initState() {
    super.initState();
    checkLocalVerison();
    if (Platform.isAndroid) {
      check();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (hasNewVersion) {
          showDialog(
              barrierDismissible: false,
              context: context,
              builder: (ctx) {
                return PopScope(
                    canPop: false,
                    child: Center(
                      child: DownLoadView(
                        downloadUrl: versionResult?.data?.downloadUrl ?? "",
                        size: int.parse(versionResult!.data!.size!),
                        installCallBack: (installRes) {
                          Navigator.of(context).pop();
                          showToast(installRes ? '安装成功' : '更新失败');
                        },
                      ),
                      // child: BdhNewversionDonwload(
                      //   downloadUrl: versionResult?.data?.downloadUrl ?? "",
                      //   size: int.parse(versionResult!.data!.size!),
                      //   completeInsatllCallBack: (completeInstall) {
                      //     // Logger().i('_progressValue====$progressValue');
                      //     if (completeInstall) {}
                      //   },
                      // ),
                    ));
              });
        } else {
          showToast("已经是最新版本");
        }
      },
      child: Container(
        color: Colors.white,
        width: 345.px,
        child: Column(
          children: [
            SizedBox(
              height: 51.px,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      SizedBox(
                        width: 10.px,
                      ),
                      // const Text("版本号(1.0.7)")
                      Text("版本号(${initPackageInfo?.version ?? ''})")
                    ],
                  ),
                  Row(
                    children: [
                      // 隐藏小红点
                      // hasNewVersion
                      //     ? ClipOval(
                      //         child: Container(
                      //           height: 8.px,
                      //           width: 8.px,
                      //           color: Colors.red,
                      //         ),
                      //       )
                      //     : Container(),
                      // SizedBox(
                      //   width: 5.px,
                      // ),
                      // hasNewVersion
                      //     ? const Text(
                      //         "有新的版本",
                      //         style: TextStyle(color: Colors.red),
                      //       )
                      //     : const Text("已是最新版本"),
                      Container(
                        margin: EdgeInsets.only(left: 5.px),
                        child: Image.asset(
                            width: 24.px,
                            height: 24.px,
                            ImageHelper.wrapAssets("arrow_right.png")),
                      )
                    ],
                  )
                ],
              ),
            ),
            BdhSegmentLine(
              width: 345.px,
              opacity: 0.4,
            ),
            GestureDetector(
              onTap: () {
                Navigator.of(context)
                    .push(CupertinoPageRoute(builder: (context) {
                  // return const HtmlPage(
                  //     title: "备案信息", content: "https://beian.miit.gov.cn/");
                  return const HtmlPageInappweb(
                      content: 'https://beian.miit.gov.cn/', title: '备案信息');
                }));
              },
              child: Container(
                margin: EdgeInsets.only(top: 50.px),
                width: 200.px,
                child: Text(
                  textAlign: TextAlign.center,
                  'ICP备案/许可证号：黑ICP备2021002948号-5A',
                  maxLines: 2,
                  style: TextStyle(color: BDHColor.black04, fontSize: 14.px),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  checkLocalVerison() {
    GetCurrentInstallVersion.initPackageInfo().then((res) {
      setState(() {
        initPackageInfo = res;
      });
    });
  }

  check() async {
    // var result = await BDHResponsitory.getVersionInfo();
    // versionResult = result;
    // if (int.parse(result.data!.build!) > 105) {
    //   // if (int.parse(result.data!.build!) > 2) {
    //   setState(() {
    //     hasNewVersion = true;
    //   });
    // }

    GetCurrentInstallVersion.check(needShowDialog: false).then((res) {
      setState(() {
        hasNewVersion = res['haveNewVersion'];
        versionResult = res['VersionResult'];
      });
    });
  }
}

class DownLoadView extends StatefulWidget {
  final String downloadUrl;
  final int size;
  final Function(bool installRes) installCallBack;
  const DownLoadView(
      {super.key,
      required this.downloadUrl,
      required this.size,
      required this.installCallBack});

  @override
  State<StatefulWidget> createState() => _DownLoadViewState();
}

class _DownLoadViewState extends State<DownLoadView> {
  double _progressValue = 0;

  @override
  void initState() {
    super.initState();
    _networkInstallApk(widget.downloadUrl, widget.size);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300.px,
      height: 100.px,
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      padding: EdgeInsets.all(15.px),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 200.px,
                height: 5.px,
                child: LinearProgressIndicator(
                  value: _progressValue,
                ),
              ),
              Text("${(_progressValue * 100).toStringAsFixed(0)}%")
            ],
          ),
          const Text("更新中...")
        ],
      ),
    );
  }

  _networkInstallApk(downloadUrl, filelength) async {
    if (_progressValue != 0 && _progressValue < 1) {
      // print("Wait a moment, downloading");
      return;
    }

    _progressValue = 0.0;
    var appDocDir = await getTemporaryDirectory();
    String savePath = "${appDocDir.path}/bdh_smart_agric_app.apk";
    String fileUrl = "${urlConfig.microfront}/$downloadUrl";
    await Dio().download(fileUrl, savePath, onReceiveProgress: (count, total) {
      final value = count / filelength;
      if (_progressValue != value) {
        setState(() {
          if (_progressValue < 1.0) {
            _progressValue = count / filelength;
          } else {
            _progressValue = 0.0;
          }
        });
        // print("${(_progressValue * 100).toStringAsFixed(0)}%");
        setState(() {});
      }
    });

    final res = await InstallPlugin.install(savePath);
    // print(
    //     "install apk ${res['isSuccess'] == true ? 'success' : 'fail:${res['errorMessage'] ?? ''}'}");
    widget.installCallBack(res['isSuccess'] == true ? true : false);
  }
}
