import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class CancelAccount extends StatefulWidget {
  const CancelAccount({super.key});

  @override
  State<StatefulWidget> createState() => _CancelAccountState();
}

class _CancelAccountState extends State {
  String text = '''
  1.账户注销后，用户将无法使用原账号、密码登陆数字北大荒APP。
      2.账户注销后，用户将无法使用该账户有权使用的数字北大荒App项下的服务功能。
      3.账户注销后，用户将不再享有与数字北大荒APP签署的相关协议、权利义务文件中约定的适用于用户的权利，但法律、法规、规章、规范性文件等另有要求或相关协议、权利义务文件中另有约定的除外。
      4.账户注销的行为具有不可逆转性，一旦注销完成，将无法恢复。如果用户在账户注销后以相同的信息再次申请账户，经数字北大荒APP审核通过后，此时的账户将被默认为新的账户。
      5.账户注销后，用户使用数字北大荒APP签署的下列合同/协议及基于该等合同/协议获取的相关服务不因账户注销而终止，但用户应自行与合同/协议的相对方沟通合同/协议履行事宜，数字北大荒APP不负有沟通、协调、提供查询等义务：
      （1）土地承包合同/协议；如用户与农场签署的土地承包合同/协议期限未届满，则账户注销后，合同/协议继续履行；
      （2）获取的金融服务；如用户与金融服务提供商签署的相关金融服务合同/协议未届满，则账户注销后，合同/协议继续履行。
      6.基于土地承包管理的必要，数字北大荒APP在用户账户注销后，对于在业务办理过程中已经获知的用户相关信息将继续保有，并将依据法律、法规的规定保证信息的安全性。
      7.账户注销后，将不会免除或减轻用户根据相关法律、法规、规章、规范性文件或相关协议、权利义务文件中用户将（可能）承担的相关责任。
  ''';
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("注销账号"),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            SizedBox(
              height: cons.maxHeight -
                  100.px -
                  MediaQuery.of(context).padding.bottom,
              child: SingleChildScrollView(
                child: Text.rich(TextSpan(text: text)),
              ),
            ),
            GestureDetector(
              onTap: () {
                showGeneralDialog(
                  context: context,
                  pageBuilder: (BuildContext buildContext,
                      Animation<double> animation,
                      Animation<double> secondaryAnimation) {
                    return TDAlertDialog(
                      title: "确认注销？",
                      rightBtnAction: () {
                        BDHResponsitory.preLogOff().then((res) {
                          if (res.code != 0) {
                            showToast("存在尚未办结的业务，请联系管理员");
                          } else {
                            BDHResponsitory.logOff().then((res1) {
                              showToast("账号注销成功");
                              // Navigator.of(context)
                              //     .pushReplacementNamed(RouteName.login);
                              Navigator.of(context).pushReplacementNamed(
                                  RouteName.loginBdhDigitalPage);
                            });
                          }
                        });
                      },
                    );
                  },
                );
              },
              child: BDHButtonGreen(
                width: 345.px,
                height: 44.px,
                title: "我已知晓",
              ),
            )
          ],
        );
      }),
    );
  }
}
