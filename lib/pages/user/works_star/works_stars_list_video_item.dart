import 'package:bdh_smart_agric_app/components/bdh_network_image.dart';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/model/search_content_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/premium_classroom.dart';
import 'package:bdh_smart_agric_app/pages/user/works_star/works_stars_view_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/video_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class WorksStarsListVideoItem extends StatefulWidget {
  final SearchContentModel item;
  final WorksStarsViewModel viewModel;
  // final VideoItem item;
  const WorksStarsListVideoItem(
      {super.key, required this.item, required this.viewModel});
  // final VideoGridModel? videoGridModel;
  // const VideoItemCard({super.key, required this.item, this.videoGridModel});

  @override
  State<StatefulWidget> createState() => _WorksStarsListVideoItemState();
}

class _WorksStarsListVideoItemState extends State<WorksStarsListVideoItem> {
  int likeCount = 0;
  bool isLike = false;

  @override
  void initState() {
    super.initState();
    likeCount = widget.item.likeCount ?? 0;
    isLike = widget.item.isLike == 1 ? true : false;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Navigator.of(context)
        //     .push(CupertinoPageRoute(
        //         builder: (ctx) {
        //           return PremiumClassroom(
        //             type: EntryType.topic,
        //             videoId: widget.item.contentId,
        //             notShowGuidePage: true,
        //             // firstCategory: widget.item.firstCategory,
        //             // keyWord: widget.videoGridModel?.keyword,
        //             // secondCategory: widget.item.secondCategory,
        //           );
        //         },
        //         settings: const RouteSettings(name: "pagePlay")))
        //     .then((res) {
        //   loadDetail();
        // });
        Navigator.of(context).pushNamed('worksStarsVideo', arguments: {
          'type': EntryType.topic,
          'videoId': widget.item.contentId,
          'notShowGuidePage': true,
          'myLikeVideo': true,
        }).then((res) {
          loadDetail();
          GlobalServiceView.needShowServiceBtn('worksStarsPage');
        });

        // Navigator.of(context).pushNamed('worksStarsVideo', arguments: {
        //   'type': EntryType.topic,
        //   'videoId': widget.item.contentId,
        //   'notShowGuidePage': true,
        // }).then((res) {
        //   loadDetail();
        //   GlobalServiceView.needShowServiceBtn('worksStarsPage');
        // });
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(2.0.px)),
            border: Border.all(
                width: 0.3.px, color: const Color.fromRGBO(226, 235, 231, 1))),
        child: Column(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(2.px),
                  topRight: Radius.circular(2.px)),
              child: BdhNetworkImage(
                url: '${urlConfig.microfront}${widget.item.image}',
                width: 170.px,
                height: 227.px,
                fit: BoxFit.cover,
                errorImage: "header.png",
              ),
            ),
            Container(
              color: const Color.fromRGBO(241, 245, 243, 0.4),
              padding: EdgeInsets.all(10.px),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.item.title ?? "",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style:
                        TextStyle(fontSize: 13.px, fontWeight: FontWeight.w500),
                  ),
                  SizedBox(height: 10.px),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          ClipOval(
                            child: BdhNetworkImage(
                              url:
                                  "${urlConfig.microfront}${widget.item.staffPics}",
                              width: 16.px,
                              height: 16.px,
                              errorImage: "header.png",
                            ),
                          ),
                          SizedBox(width: 5.px),
                          Text(
                            widget.item.publishSource ?? "",
                            style: TextStyle(
                                fontSize: 10.px, fontWeight: FontWeight.w400),
                          )
                        ],
                      ),
                      GestureDetector(
                        onTap: () {
                          VideoResponsitory.videoLike(
                              {"videoId": widget.item.contentId}).then((res) {
                            loadDetail();
                          });
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Image.asset(
                                width: 16.px,
                                height: 16.px,
                                ImageHelper.wrapAssets(
                                    isLike ? "like.png" : "like_empty.png")),
                            SizedBox(width: 5.px),
                            Text(
                              "$likeCount",
                              style: TextStyle(
                                  color: isLike
                                      ? Colors.red
                                      : const Color.fromRGBO(168, 184, 177, 1),
                                  fontWeight: FontWeight.w500,
                                  fontSize: 11.px),
                            )
                          ],
                        ),
                      )
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  loadDetail() {
    VideoResponsitory.videoDetail(widget.item.contentId ?? 0).then((res) {
      setState(() {
        isLike = res.data?.isLike == 1 ? true : false;
        likeCount = res.data?.likeCount ?? 0;
      });
      widget.viewModel.initData();
    });
  }
}
