import 'dart:convert';
import 'dart:io';

import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/pages/user/we_chat_auth/push_message_model.dart';
import 'package:bdh_smart_agric_app/utils/cover_tool.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/cupertino.dart';

class NativeParamterBack {
  static String logInfo = '';

  //,ios 路由跳转处理  umeng push 回调
  static dealWithUMPushRounte(dynamic call, BuildContext context) {
    Log.i('收到参数: readOffLineNotificationfrom : ${call.arguments}}');
    String currentString = FormatTool.getCurrentTimeString();
    logInfo += '收到参数(时间: $currentString)' '\n';
    logInfo += '收到参数: readOffLineNotificationfrom : ${call.arguments}}';
    logInfo += '\n\n';
    Map arg = call.arguments;
    dealWithRouteData(arg, context);
  }

//android, 路由跳转处理
  static dealWithRouteData(Map arg, BuildContext context) {
    String routeName = MyRouter.currentRouteName;
    if (routeName != 'tabMain') {
      Navigator.popUntil(context, ModalRoute.withName(RouteName.tabMain));
    }
    if (arg['platform'] == "ios") {
      Map msgBody = arg['msgBody'];
      Map extra = msgBody['extra'];
      if (extra.isEmpty) {
        return;
      }
      String str = jsonEncode(extra);
      var extraObj = jsonDecode(str);
      Extra model = Extra.fromJson(extraObj);
      Navigator.of(context).pushNamed(model.routeName ?? '');
    } else {
      var msgBody = arg['msgBody'];
      Map<String, dynamic> userMap = jsonDecode(msgBody);
      if (userMap['extra'] != null) {
        Map extra = userMap['extra'];
        if (extra.isEmpty) {
          return;
        }
        String str = jsonEncode(extra);
        var extraObj = jsonDecode(str);
        Extra model = Extra.fromJson(extraObj);
        Navigator.of(context).pushNamed(model.routeName ?? '');
      }
    }
  }

//集成native umpush 发送deviceToken 回调 处理
  static dealWithSendDeviceToken(dynamic call) {
    String phoneTypeStr = Platform.isIOS ? 'iOS' : 'android';
    Log.i('收到参数: DeviceToken from $phoneTypeStr: ${call.arguments}}');
    //showToast('收到参数: DeviceToken from $phoneTypeStr: ${call.arguments}}');
    String currentString = FormatTool.getCurrentTimeString();
    logInfo += '收到参数(时间: $currentString): DeviceToken from android: ' '\n';
    logInfo += call.arguments.toString();
    logInfo += '\n\n';
    Map deviceTokenArg = call.arguments;
    String deviceToken = deviceTokenArg['deviceToken'];
    StorageUtil.saveDeviceToken(deviceToken);
    upLoadDeviceToken(deviceToken); //直接发送到服务端
  }

//集成flutter umpush 发送deviceToken 回调 处理
  static dealWithSendDeviceTokenFromFlutter(String deviceToken) {
    String currentString = FormatTool.getCurrentTimeString();
    logInfo += '收到参数(时间: $currentString): DeviceToken from 集成flutter: ' '\n';
    logInfo += deviceToken;
    logInfo += '\n\n';
    StorageUtil.saveDeviceToken(deviceToken);
    upLoadDeviceToken(deviceToken); //直接发送到服务端
  }

//收到友盟deviceToken上传到服务端
  static uploadTokenToServer(String deviceToken) {
    String deviceTokenLocal = StorageUtil.readDeviceToken();
    if (deviceTokenLocal.isEmpty) {
      StorageUtil.saveDeviceToken(deviceToken);
      upLoadDeviceToken(deviceToken);
    } else {
      if (deviceTokenLocal != deviceToken) {
        StorageUtil.saveDeviceToken(deviceToken);
        upLoadDeviceToken(deviceToken);
      }
    }
  }

//用户登录时再传一次deviceToken
  static uploadTokenToServerWhenLogin() {
    String deviceToken = StorageUtil.readDeviceToken();
    if (deviceToken.isEmpty) {
      return;
    }
    upLoadDeviceToken(deviceToken);
  }

  static upLoadDeviceToken(String deviceToken) {
    // Map<String, dynamic> form = {};
    // form['deviceToken'] = deviceToken;
    // form['deviceType'] = Platform.isIOS ? 'iOS' : 'android';
    // BDHResponsitory.updateUserExtendInfo(form).then((res) {
    //   Log.i('上传deviceToken到服务端接口回调:$res');
    // });
  }

  //发送静默通知(android, 拉起权限弹框 暂时保留)
  // static Future<void> sendTestNotification() async {
  //   FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
  //       FlutterLocalNotificationsPlugin();

  //   const AndroidInitializationSettings initializationSettingsAndroid =
  //       AndroidInitializationSettings('@mipmap/ic_launcher');

  //   const InitializationSettings initializationSettings =
  //       InitializationSettings(
  //     android: initializationSettingsAndroid,
  //   );

  //   await flutterLocalNotificationsPlugin.initialize(initializationSettings);

  //   const AndroidNotificationDetails androidPlatformChannelSpecifics =
  //       AndroidNotificationDetails('test_channel', 'Test Notifications',
  //           importance: Importance.min,
  //           priority: Priority.min,
  //           playSound: false);

  //   const NotificationDetails platformChannelSpecifics =
  //       NotificationDetails(android: androidPlatformChannelSpecifics);

  //   await flutterLocalNotificationsPlugin.show(
  //       0, '静默发送通知', '这是一条开启权限的静默通知消息', platformChannelSpecifics);
  // }
}
