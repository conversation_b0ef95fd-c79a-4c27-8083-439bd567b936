import 'dart:convert';
import 'dart:io';

import 'package:bdh_smart_agric_app/pages/user/we_chat_auth/native_paramter_back.dart';
import 'package:bdh_smart_agric_app/pages/user/we_chat_auth/push_message_model.dart';
import 'package:bdh_smart_agric_app/utils/cover_tool.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:oktoast/oktoast.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';
// import 'package:umeng_push_sdk/umeng_push_sdk.dart';

class UmPush extends StatefulWidget {
  const UmPush({super.key});

  @override
  State<UmPush> createState() => _UmPushState();
}

class _UmPushState extends State<UmPush> with AutomaticKeepAliveClientMixin {
  TextEditingController controller = TextEditingController();
  TextEditingController deviceTokenController = TextEditingController();
  late Map<String, VoidCallback?> methods;
  int badgeNumber = 0;

  void registerPush() {
    // UmengPushSdk.setLogEnable(true);
    // UmengCommonSdk.initCommon(
    //   "671b4c92667bfe33f3ca75a6", //androidAppkey
    //   "6722d46c7e5e6a4eeb897f57", //iosAppkey
    //   "BDH_PUSH", //channel
    //   "34250254602c4aaabc7905d52e03fd95", //pushSecret
    // );
    // UmengPushSdk.register("671b4c92667bfe33f3ca75a6", "BDH_PUSH");
  }

  @override
  void initState() {
    super.initState();
    // registerPush();
    // getMd5();
    controller.text += NativeParamterBack.logInfo;
    methods = {
      // 'register': () async => registerPush(),

//获取DeviceToken
      // 'DeviceToken': () async {
      //   String? deviceToken = await UmengPushSdk.getRegisteredId();
      //   if (deviceToken != null) {
      //     print(" deviceToken:" + deviceToken);
      //     controller.text += deviceToken + "\n";
      //   }
      // },

//添加别名
      // 'addAlias': () async => controller.text +=
      //     "${await UmengPushSdk.addAlias("myAlias", "BDH")}\n",

//移除别名
      // 'removeAlias': () async => controller.text +=
      //     "${await UmengPushSdk.removeAlias("myAlias", "SINA_WEIBO")}\n",
//绑定别名
      // 'setAlias': () async => controller.text =
      //     (await UmengPushSdk.setAlias("myAlias", "SINA_WEIBO")).toString(),

//       'addTags': () async => controller.text +=
// // (await UmengPushSdk.addTags(["myTag1", "myTag2", "myTag3"]))
//           "${await UmengPushSdk.addTags(['testTag'])}\n",

//移除标签
      // 'removeTags': () async =>
      //     controller.text += "${await UmengPushSdk.removeTags([
      //           "groupios",
      //           "856ios",
      //           "group_android",
      //           "856_android",
      //         ])}\n",

//获取已设置的标签
      // 'getAllTags': () async =>
      //     controller.text += "${await UmengPushSdk.getTags()}\n",

//打开推送功能
      // 'openPush': () async => UmengPushSdk.setPushEnable(true),

//关闭推送功能
      // 'closePush': () async => UmengPushSdk.setPushEnable(false),

//清空日志
      'clear': () {
        controller.text = "";
        deviceTokenController.text = "";
      },

      '推送1': () {
        // controller.text = "";
        getMd5();
      },
      'API推送': () {
        // controller.text = "";
        sendNotificationByApi();
      },
      '复制用户Token': () {
        String? token = StorageUtil.userInfo()?.data!.token;
        if (token == null) {
          showToast("token复制失败", duration: const Duration(milliseconds: 1500));
        } else {
          showToast("token复制成功", duration: const Duration(milliseconds: 1500));
          Clipboard.setData(ClipboardData(text: token));
        }
      },
      '复制用户staffId': () {
        int staffId = StorageUtil.userInfo()?.data?.staffId ?? 0;

        if (staffId.toString().isEmpty) {
          showToast("复制用户staffId失败",
              duration: const Duration(milliseconds: 1500));
        } else {
          String currentString = FormatTool.getCurrentTimeString();
          NativeParamterBack.logInfo += '收到参数(时间: $currentString)' '\n';
          NativeParamterBack.logInfo += '收到参数: staffId= : $staffId}';
          NativeParamterBack.logInfo += '\n\n';
          showToast("复制用户staffId成功",
              duration: const Duration(milliseconds: 1500));
          Clipboard.setData(ClipboardData(text: staffId.toString()));
          setState(() {});
        }
      },

      // '虚拟定位测试': () {
      //   NativeUtil.virtualGPSLocaionJudgement({
      //     "apiKey": "cb174d5f4d268a8a72cf54b7b6c724f2",
      //     "apiKeyIOS": "47ac324f0fda3f062e8cf85d90ae7a8d"
      //   }).then((result) {
      //     Log.d("virtualGPSLocaionJudgement result is $result");
      //   });
      // },
    };

//设置deviceToken回调
    // UmengPushSdk.setTokenCallback((deviceToken) {
    //   if (kDebugMode) {
    //     print("deviceToken:$deviceToken");
    //   }
    //   controller.text += "$deviceToken\n";
    // });

//设置自定义消息回调
    // UmengPushSdk.setMessageCallback((msg) {
    //   if (kDebugMode) {
    //     print(" receive custom:$msg");
    //   }
    //   controller.text += "$msg\n";
    // });

//设置通知消息回调
    // UmengPushSdk.setNotificationCallback((receive) {
    //   if (kDebugMode) {
    //     print(" receive:$receive");
    //   }
    //   controller.text += "receive:$receive\n";
    // }, (open) {
    //   if (kDebugMode) {
    //     print(" open:$open");
    //     Map<String, dynamic> userMap = jsonDecode(open);
    //     PushMessageModel model = PushMessageModel.fromJson(userMap);
    //     print(model);
    //     if (model.extra != null && model.extra?.routeName != null) {
    //       // Navigator.of(context).pushNamed('searchNewPage');
    //       Navigator.of(context).pushNamed(model.extra!.routeName!);
    //     }
    //   }
    //   controller.text += "open:$open\n";
    // });

    bus.on("GetVirtualGPSLocaionJudgementResult", (data) {
      if (!mounted) {
        return;
      }
      Log.i('GetVirtualGPSLocaionJudgementResult : $data');
      var result = data["result"];
      var msg = data["msg"];
      controller.text += "$data\n\n";
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    bus.off("GetVirtualGPSLocaionJudgementResult");
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
        appBar: AppBar(
          title: Text('PushDebug'),
        ),
        body: Center(
          child: Column(
            children: <Widget>[
              Expanded(
                flex: 1,
                child: Container(
                  decoration: BoxDecoration(border: Border.all(width: 2)),
                  child: SizedBox(
                    width: 350.px,
                    child: CupertinoTextField.borderless(
                      padding: EdgeInsets.zero,
                      controller: deviceTokenController,
                      placeholder: "请输入deviceToken,以英文逗号分开",
                      placeholderStyle: TextStyle(
                          fontSize: 16.px,
                          fontWeight: FontWeight.w400,
                          color: const Color.fromRGBO(0, 0, 0, 0.2)),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 9,
                child: Container(
                  // color: Colors.red,
                  child: TextField(
                    maxLines: 200,
                    controller: controller,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Container(
                  // color: Colors.blue,
                  child: SingleChildScrollView(
                    child: Wrap(
                      runSpacing: 5,
                      spacing: 5,
                      children: methods.keys
                          .map((e) => ElevatedButton(
                                child: Text(e),
                                onPressed: methods[e],
                              ))
                          .toList(),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  @override
  bool get wantKeepAlive => true;

  String generateSignature(
      String httpMethod, String url, String postBody, String appMasterSecret) {
    // 提取Host字段的域名(或ip:端口)和URI的path部分
    Uri parsedUrl = Uri.parse(url);
    String host = parsedUrl.host;
    String path = parsedUrl.path;

    // 拼接字符串
    // String stringToSign = '$httpMethod$host$path$postBody$appMasterSecret';
    String stringToSign = '$httpMethod$url$postBody$appMasterSecret';

    // 计算MD5值
    var digest = '${md5.convert(utf8.encode(stringToSign))}';

    // 将MD5值转换为32位小写十六进制字符串
    String sign = digest.toString().toLowerCase();

    return sign;
  }

  getMd5() async {
    String httpMethod = 'POST';
    String url = 'https://msgapi.umeng.com/api/send';
    // 获取 13 位时间戳（毫秒）
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    int timeStampThreeDayLate =
        DateTime.now().add(const Duration(days: 3)).millisecondsSinceEpoch;
    String currentTime = FormatTool.timeFormat(timestamp: timestamp);
    String expireTime = FormatTool.timeFormat(timestamp: timeStampThreeDayLate);
    String deviceTokenLocal = StorageUtil.readDeviceToken();
    // 示例请求体
    Map<String, dynamic> requestBody = Platform.isAndroid
        ? {
            // "appkey": "671b4c92667bfe33f3ca75a6",
            "appkey": "6826f9babc47b67d83683f42",
            "timestamp": timestamp,
            "description": "推送消息-$currentTime",
            "payload": {
              "display_type": "notification",
              "body": {
                "ticker": "推送消息-$currentTime",
                "text": "推送消息-$currentTime",
                "title": "推送消息-$currentTime",
                "after_open": "go_app", //go_app  go_custom  go_activity
                // "custom": {
                //   "routeName": "searchNewPage",
                //   "routeparamter1": "value2",
                // },
                "play_sound": true,
                "play_lights": true,
                "play_vibrate": true
              },
              "extra": {
                "routeName": "searchNewPage",
                "routeparamter1": "value2",
              }
            },

            // "type": "unicast",
            "type": "listcast",
            // "device_tokens":
            //     "Ar_cSVUChR8xuGl0stcE9pfdCIxKH7VayPqAEiE1R9zk,AnTLIVMap6yyjidNzql92seT1xGxFEhcMcmld96-FVTT",
            // "device_tokens": "AnTLIVMap6yyjidNzql92seT1xGxFEhcMcmld96-FVTT",
            //华为: Ar_cSVUChR8xuGl0stcE9pfdCIxKH7VayPqAEiE1R9zk
            //小米: AnTLIVMap6yyjidNzql92seT1xGxFEhcMcmld96-FVTT
            //vivo: AvZqzVquMCV4MyKMiWUwsbg0XMyjEliSox4UnNrYSgNK
            //荣耀:AmTrsg4uSPK8Li8u3sZLkSHEgCAhbsaCHmXNqTFLcDFJ

            // "device_tokens": 'Arb-0R1O7GPtGyqKWdJrRHkxl7qIyHwvLDFCdH4h_SoU',
            "device_tokens": deviceTokenLocal,
            // "device_tokens": deviceTokenController.text.isEmpty
            //     ? ""
            //     : deviceTokenController.text,
            // "umeng_category": 1,
            // "mipush": true,
            // "mi_activity": "com.umeng.message.MfrMessageActivity",
            "channel_fcm": 0,
            "channel_properties": {
              "channel_activity":
                  "com.example.bdh_smart_agric_app.MfrMessageActivity",
              // "channel_activity": "xxxx",
              // "main_activity": "xx",
              "huawei_channel_importance": "NORMAL",
              // "huawei_channel_category": "DEVICE_REMINDER",
              "huawei_channel_category": "WORK",
              "xiaomi_channel_id": "133563",
              "vivo_category": "DEVICE_REMINDER",
            },

            "policy": {
              // "expire_time": "2025-03-12 13:51:08",
              "expire_time": expireTime,
              // "expire_time": timestamp,
              "notification_closed_filter": false
            }
          }
        : {
            // "appkey": "6722d46c7e5e6a4eeb897f57",
            "appkey": "6826fd4f79267e0210660646",
            "timestamp": timestamp,
            "description": "推送消息-$currentTime",
            "payload": {
              "aps": {
                "alert": {
                  "body": "推送消息ios-$currentTime",
                  "title": "推送消息ios-$currentTime",
                  "subtitle": "推送消息ios-$currentTime"
                },
                "sound": "default"
              }
            },
            // "type": "unicast",
            "type": "listcast",
            //ios:28d9fb83cf4af9205e5025da401a3e07d1351e52054a29fae7b8f338e9ddd778
            // "device_tokens":
            //     '28d9fb83cf4af9205e5025da401a3e07d1351e52054a29fae7b8f338e9ddd778',
            "device_tokens": deviceTokenLocal,
            // "device_tokens": deviceTokenController.text.isEmpty
            //     ? "2b7da0eb4d1c133e3b958c43e6d59fdcf617bd7acd09290ef7d7f157637f94d7"
            //     : deviceTokenController.text,
            "production_mode": false,
            "policy": {
              // "expire_time": "2025-03-12 13:51:08",
              "expire_time": expireTime,
              // "expire_time": timestamp,
              "notification_closed_filter": false
            }
          };

    // String appMasterSecret =
    //     'ssoecjv5akub8wdxgnnmtjvbi65qbrto'; // 替换为实际的app_master_secret
    // String appMasterSecretIOS =
    //     'lczvtvaf2fnycnlqnmm6sjwb4fkdue6u'; // 替换为实际的app_master_secret
    String appMasterSecret =
        '23cqgo5rtncjjfwydrrstlhpgqnjvsnn'; // 替换为实际的app_master_secret
    String appMasterSecretIOS =
        'nktdbh6ahilall7zoggv2tmxevqphmtj'; // 替换为实际的app_master_secret
    String postBody = jsonEncode(requestBody);
    // 生成签名
    String sign = generateSignature(httpMethod, url, postBody,
        Platform.isAndroid ? appMasterSecret : appMasterSecretIOS);
// 构造完整的请求URL
    String fullUrl = '$url?sign=$sign';
    // 将请求体转换为JSON字符串

    // 使用Dio发送POST请求
    Dio dio = Dio();
    try {
      Response response = await dio.post(
        fullUrl,
        data: postBody,
        options: Options(contentType: 'application/json'),
      );
      print('请求成功: ${response.data}');
      controller.text = response.data;
    } catch (e) {
      if (e is DioException) {
        if (e.response != null) {
          print('请求失败: ${e.response}');
          controller.text = '${e.response}';
        } else {
          print('请求失败: ${e.message}');
        }
      } else {
        print('其他异常: $e');
      }
    }
    print('生成的签名: $sign');
  }
}

//使用api 通知
sendNotificationByApi() {
  int timestamp = DateTime.now().millisecondsSinceEpoch;
  String currentTime = FormatTool.timeFormat(timestamp: timestamp);
  int staffId = StorageUtil.userInfo()?.data?.staffId ?? 0;
  List staffIds = [staffId];
  Map<String, dynamic> form = {
    "description": "desc",
    "text": "API推送消息-$currentTime-您的设备有新的消息,请及时查看,设备报警了",
    "title": "API推送消息-$currentTime",
    "staffIds": staffIds,
    "newsSource": "bdh-app-gathering",
    "extra": {
      "routeName": "searchNewPage",
      "params": {"key1": "aaa"}
    },
    "production_mode": false
  };
  BDHResponsitory.sendNotification(form).then((res) {
    Log.i('调用服务端消息接口回调:$res');
    if (res.code == 0) {
      showToast('API发送消息:${res.msg}');
    } else {
      showToast('API发送消息失败:${res.msg}');
    }
  });
}
// 7890
// 127.0.0.1
/*
class mytest {

var p =
  {
    "appkey":"xx",    // 必填，应用唯一标识
    "timestamp":"xx",    // 必填，时间戳，10位或者13位均可，时间戳有效期为10分钟
    "type":"xx",    // 必填，消息发送类型,其值可以为:
                        // unicast-单播
                        // listcast-列播，要求不超过500个device_token
                        // filecast-文件播，多个device_token可通过文件形式批量发送
                        // broadcast-广播
                        // groupcast-组播，按照filter筛选用户群,请参照filter参数
                        // customizedcast，通过alias进行推送，包括以下两种case:
                        // -alias:对单个或者多个alias进行推送
                        // -file_id:将alias存放到文件后，根据file_id来推送
    "device_tokens":"xx",    // 当type=unicast时,必填,表示指定的单个设备
                                     // 当type=listcast时,必填,要求不超过500个,以英文逗号分隔
    "alias_type":"xx",    // 当type=customizedcast时,必填
                                // alias的类型, alias_type可由开发者自定义,开发者在SDK中调用setAlias(alias, alias_type)时所设置的alias_type
    "alias":"xx",    // 当type=customizedcast时,选填(此参数和file_id二选一)
                        // 开发者填写自己的alias,要求不超过500个alias,多个alias以英文逗号间隔
                        // 在SDK中调用setAlias(alias, alias_type)时所设置的alias
    "file_id":"xx",    // 当type=filecast时，必填，file内容为多条device_token，以回车符分割 // 当type=customizedcast时，选填(此参数和alias二选一) // file内容为多条alias，以回车符分隔。注意同一个文件内的alias所对应的alias_type必须和接口参数alias_type一致 // 使用文件播需要先调用文件上传接口获取file_id，参照"文件上传"
    "filter":{},    // 当type=groupcast时，filter或group_id至少填一个，filter代表用户筛选条件，如用户标签、渠道等，参考附录G
                     // filter的内容长度最大为3000B
    "group_id":"xx",    // 当type=groupcast时，filter或group_id至少填一个，group_id代表用户分群ID（详见Portal：运营-人群管理页面）
                     // 当filter和group_id同时存在时，只取group_id
    "payload":{    // 必填，JSON格式，具体消息内容(Android最大为2048B)
        "display_type":"xx",    // 必填，消息类型: notification(通知)、message(消息)
        "body":{    // 必填，消息体
                       // 当display_type=message时，body的内容只需填写custom字段
                       // 当display_type=notification时，body包含如下参数:
            "title":"xx",    // 必填，通知标题
            "text":"xx",    // 必填，通知文字描述
            "ticker":"xx",    //可选, 通知栏提示文字
            "big_body":"xxx",    //可选, 最多120个字符大文本

            // 消息重弹（推送专业版（Pro）高级能力，SDK v6.5.7及以上版本支持）:
            "re_pop":0,    // 可选，0：不重弹；1：重弹。默认值是0

            // 自定义通知图标:
            "icon":"xx",    // 可选，状态栏图标ID，R.drawable.[smallIcon]，
                                // 如果没有，默认使用应用图标
                                // 图片要求为24*24dp的图标，或24*24px放在drawable-mdpi下
                                // 注意四周各留1个dp的空白像素
            "img":"xx",    // 可选，通知栏大图标的URL链接。
                               // 该字段要求以http或者https开头，图片建议不大于100KB。
            "expand_image":"xx",    // 消息下方展示大图，支持自有通道消息展示
                                              // 厂商通道展示大图目前仅支持小米,要求图片为固定876*324px,仅处理在友盟推送后台上传的图片。如果上传的图片不符合小米的要求，则通过小米通道下发的消息不展示该图片，其他要求请参考小米推送文档[小米富媒体推送](https://dev.mi.com/console/doc/detail?pId=1278#_3_3 "小米富媒体推送")

            // 自定义通知声音:
            "sound":"xx",    // 可选，通知声音，R.raw.[sound]
                                  // 如果该字段为空，采用SDK默认的声音，即res/raw/下的
                                  // umeng_push_notification_default_sound声音文件。如果SDK默认声音文件不存在，则使用系统默认Notification提示音

            // 自定义通知样式:
            "builder_id": xx,    // 可选，默认为0，用于标识该通知采用的样式。使用该参数时
                                      // 开发者必须在App里面实现自定义通知栏样式

            //角标，当前设置支持友盟、华为、荣耀、vivo四个通道，小米、vivo和魅族系统默认支持自动+1，OPPO支持红点（需申请）
            //若同时存在set_badge和add_badge字段，只取set_badge的值。下述数值范围1~99仅针对华为/荣耀厂商通道；友盟通道数值不限制（如：可传-1，角标减1）
            "set_badge":5,     //可选，没有默认值。角标设置数字，范围为1~99。如果设置的值不在此区间该参数值将被忽略，需配合main_activity使用，具体说明参考main_activity
            "add_badge":1,     //可选，没有默认值。角标设置数字，范围为1~99。如果设置的值不在此区间该参数值将被忽略，需配合main_activity使用，具体说明参考main_activity

            // 通知到达设备后的提醒方式(注意，"true/false"为字符串):
            "play_vibrate":"true/false",    // 可选，收到通知是否震动，默认为"true"
            "play_lights":"true/false",    // 可选，收到通知是否闪灯，默认为"true"
            "play_sound":"true/false",    // 可选，收到通知是否发出声音，默认为"true"

            //点击"通知"的后续行为(默认为打开app):
            "after_open":"xx",    // 可选，默认为"go_app"，值可以为:
                                         // "go_app":打开应用
                                         // "go_url":跳转到URL
                                         // "go_activity":打开特定的activity
                                         // "go_custom":用户自定义内容
            "url":"xx",    // 当after_open=go_url时，必填
                             // 通知栏点击后跳转的URL，要求以http或者https开头
            "activity":"xx",    //当after_open=go_activity时，必填。
                                    // 通知栏点击后打开的Activity
            "custom":"xx"/{},    // 当display_type=message时,必填
                                       // 当display_type=notification且after_open=go_custom时，必填
                                       // 用户自定义内容，可以为字符串或者JSON格式。
        },

        "extra":{    // 可选，JSON格式，用户自定义key-value。
                     // 可以配合消息到达后，打开App/URL/Activity使用
            "key1":"value1",
            "key2":"value2",
            ...
        }
    },
    "policy":{    // 可选，发送策略
        "start_time":"xx",    // 可选，定时发送时，若不填写表示立即发送
                                // 定时发送时间不能小于当前时间
                                // 格式:"yyyy-MM-dd HH:mm:ss"
                                // 注意，start_time只对任务类消息生效
        "expire_time":"xx",    // 可选，消息过期时间，其值不可小于发送时间或者start_time(如果填写了的话)
                                  // 如果不填写此参数，默认为3天后过期。格式同start_time
        "max_send_num": xx,    // 可选，发送限速，每秒发送的最大条数。最小值1000
                                     //开发者发送的消息如果有请求自己服务器的资源，可以考虑此参数
        "out_biz_no":"xx" ,   // 可选，消息发送接口对任务类消息的幂等性保证
                                // 强烈建议开发者在发送任务类消息时填写这个字段，友盟服务端会根据这个字段对消息做去重避免重复发送
                                // 同一个appkey下面的多个消息会根据out_biz_no去重，不同发送任务的out_biz_no需要保证不同，否则会出现后发消息被去重过滤的情况
                                // 注意，out_biz_no只对任务类消息有效
         "notification_closed_filter":true,  //可选，只对display_type=notification的消息生效，设置为true会过滤关闭通知栏消息的设备，以免占用厂商额度。
         "in_app":{
            "in_app":true       //可选，如果配置为true,在通知栏关闭时会通过应用内弹窗展示。该功能为推送专业版（Pro）高级能力，SDK v6.6.3及以上版本支持
         }

    },
    "production_mode":"true/false",    // 可选，true正式模式，false测试模式。默认为true  // 广播、组播下的测试模式只会将消息发给测试设备。测试设备需要到web上添加  // 单播、文件播不受测试设备限制
    "description":"xx",    // 可选，发送消息描述，建议填写
    "category":0,    // 可选，友盟消息自分类，0：资讯营销类消息，1：服务与通讯类消息。有利于小米和vivo通道选择，建议填写。
    "callback_params": { // 自定义回执参数
        "name": "string",
        "age": 26
    },
    "channel_properties":{    // 可选，厂商通道相关的特殊配置
        "channel_activity":"xxx",  //系统弹窗，走厂商通道时必填。只有display_type=notification时有效，表示华为、小米、oppo、vivo、魅族的设备离线时走系统通道下发时打开指定页面acitivity的完整包路径。
        "xiaomi_channel_id":"",    // 小米channel_id，具体使用及限制请参考小米推送文档 https://dev.mi.com/console/doc/detail?pId=2086
        "vivo_category":"xx",      // vivo消息二级分类参数：友盟侧只进行参数透传，不做合法性校验，具体使用及限制参考[vivo消息推送分类功能说明]https://dev.vivo.com.cn/documentCenter/doc/359
        "vivo_addbadge":"true" , //vivo角标功能，需要客户端先完成系统API接入(https://dev.vivo.com.cn/documentCenter/doc/787),否则发消息时会报错，错误码为10089
        "oppo_channel_id":"xx" ,   // 可选， android8以上推送消息需要新建通道，否则消息无法触达用户。push sdk 6.0.5及以上创建了默认的通道:upush_default，消息提交厂商通道时默认添加该通道。如果要自定义通道名称或使用私信，请自行创建通道，推送消息时携带该参数具体可参考[oppo推送私信通道申请] https://open.oppomobile.com/new/developmentDoc/info?id=11227
        "oppo_category":"xx", //可选，OPPO消息类别，参考OPPO官方文档 https://open.oppomobile.com/new/developmentDoc/info?id=13189
        "oppo_notify_level":"xx", //OPPO通知栏消息提醒等级，1-通知栏，2-通知栏+锁屏 16-通知栏+锁屏+横幅+震动+铃声，使用该参数时，oppo_category必传
        "main_activity":"xx",         // 可选，应用入口Activity类全路径,主要用于华为通道角标展示。具体使用可参考[华为角标使用说明]https://developer.umeng.com/docs/67966/detail/272597
        "huawei_channel_importance":"NORMAL",// 可选，华为&荣耀消息分类 LOW：资讯营销类消息，NORMAL：服务与通讯类消息
        "huawei_channel_category":"MARKETING", // 可选，华为自分类消息类型 [华为消息分类]https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/message-priority-0000001181716924
        "channel_fcm":"0"  // 可选，fcm通道开关，0不使用，1使用
    },
    "local_properties":{    //可选，本地通知相关的特殊配置
        //请严格按照华为官方文档https://developer.huawei.com/consumer/cn/doc/development/HMSCore-Guides/message-classification-0000001149358835#section1085395991513设置category和importance取值。
        //若category参数和importance参数均不为空，友盟只校验参数值是否合法，不校验参数的对应关系是否正确。
        //若category参数不为空，importance参数未设置，友盟会校验category参数值是否合法，同时根据category参数来智能适配对应的importance值。
        //若category参数未设置，importance参数不为空，友盟会同时忽略category参数和importance参数。
        "category":"CATEGORY_PROMO",    //可选，华为本地通知category
        "importance":"IMPORTANCE_MIN"    //可选，华为本地通知importance
    }
};

{"ret":"SUCCESS","data":{"msg_id":"ulpzqb1174235375457601"}}

Ar_cSVUChR8xuGl0stcE9pfdCIxKH7VayPqAEiE1R9zk

}

*/


