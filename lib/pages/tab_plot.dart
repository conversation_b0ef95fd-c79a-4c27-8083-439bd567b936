import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class TabPlotPage extends StatefulWidget {
  const TabPlotPage({super.key});

  @override
  State<StatefulWidget> createState() => _TabPlotPageState();
}

class _TabPlotPageState extends State<TabPlotPage> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child:
          Image.asset(width: 375.px, ImageHelper.wrapAssets("mock_plot.png")),
    );
  }
}
