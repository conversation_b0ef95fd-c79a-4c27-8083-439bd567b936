import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:install_plugin/install_plugin.dart';
import 'package:path_provider/path_provider.dart';

class BdhNewversionDonwload extends StatefulWidget {
  final String downloadUrl;
  final int size;
  final Function(bool completeInstall) completeInsatllCallBack;
  const BdhNewversionDonwload(
      {super.key,
      required this.downloadUrl,
      required this.size,
      required this.completeInsatllCallBack});

  @override
  State<BdhNewversionDonwload> createState() => _BdhNewversionDonwloadState();
}

class _BdhNewversionDonwloadState extends State<BdhNewversionDonwload> {
  double _progressValue = 0;

  @override
  void initState() {
    super.initState();
    _networkInstallApk(widget.downloadUrl, widget.size);
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 240.px,
      height: 50.px,
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(13.px)),
            ),
            width: 240.px,
            height: 10.px,
            child: LinearProgressIndicator(
              backgroundColor: const Color.fromRGBO(211, 216, 215, 1),
              borderRadius: BorderRadius.all(Radius.circular(13.px)),
              value: _progressValue,
            ),
          ),
          SizedBox(height: 10.px),
          // Text("${(_progressValue * 100).toStringAsFixed(0)}%")
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text("更新中...",
                  style: TextStyle(
                    color: BDHColor.black06,
                    fontWeight: FontWeight.w500,
                    fontSize: 14.px,
                  )),
              Text("${(_progressValue * 100).toStringAsFixed(0)}%",
                  style: TextStyle(
                    color: BDHColor.black06,
                    fontWeight: FontWeight.w500,
                    fontSize: 14.px,
                  ))
            ],
          ),
        ],
      ),
    );
  }

  _networkInstallApk(downloadUrl, filelength) async {
    if (_progressValue != 0 && _progressValue < 1) {
      // print("Wait a moment, downloading");
      return;
    }

    _progressValue = 0.0;
    var appDocDir = await getTemporaryDirectory();
    String savePath = "${appDocDir.path}/bdh_smart_agric_app.apk";
    String fileUrl = "${urlConfig.microfront}/$downloadUrl";
    await Dio().download(
      fileUrl,
      savePath,
      onReceiveProgress: (count, total) {
        final value = count / filelength;
        if (_progressValue != value) {
          setState(() {
            if (_progressValue < 1.0) {
              _progressValue = count / filelength;
            } else {
              _progressValue = 0.0;
            }
          });
          // print("${(_progressValue * 100).toStringAsFixed(0)}%");
          setState(() {});
        }
      },
    );

    final res = await InstallPlugin.install(savePath);
    widget.completeInsatllCallBack(res['isSuccess'] == true ? true : false);
    // print(
    //     "install apk ${res['isSuccess'] == true ? 'success' : 'fail:${res['errorMessage'] ?? ''}'}");
  }
}
