import 'package:bdh_smart_agric_app/model/version_result.dart';
import 'package:bdh_smart_agric_app/pages/version/bdh_newversion_donwload.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../utils/color_util.dart';

class GetCurrentInstallVersion {
  //获取 当前安装版本
  static Future<PackageInfo> initPackageInfo() async {
    final info = await PackageInfo.fromPlatform();
    return info;
  }

// homePage check version
  static Future<Map> check(
      {BuildContext? context, bool? needShowDialog}) async {
    bool haveNewVersion = false;
    VersionResult versionResult = VersionResult();
    Map resMap = {
      'haveNewVersion': haveNewVersion,
      'VersionResult': versionResult,
    };
    resMap =
        await GetCurrentInstallVersion.initPackageInfo().then((packageInfo) {
      return BDHResponsitory.getVersionInfo().then((result) {
        // if (int.parse(result.data!.build!) > 105) {
        int systemBuildNumber = int.parse(packageInfo.buildNumber);
        // systemBuildNumber = 2;
        if (int.parse(result.data?.build ?? "0") > systemBuildNumber) {
          if (result.data?.updateType == '01' ||
              result.data?.updateType == '02') {
            Future.delayed(const Duration(seconds: 1), () {
              if (context != null && needShowDialog != null) {
                if (needShowDialog) {
                  showDialog(
                      context: context,
                      barrierDismissible: false,
                      builder: (ctx) {
                        return PopScope(
                            canPop: false,
                            child: Center(
                              child: BdhNewVersionView(
                                versionResult: result,
                              ),
                            ));
                      });
                }
              }
            });
          }
          resMap = {
            'haveNewVersion': true,
            'VersionResult': result,
          };
          return resMap;
        } else {
          resMap = {
            'haveNewVersion': false,
            'VersionResult': result,
          };
          return resMap;
        }
      });
    });
    return resMap;
  }
}

enum VersonUploadState {
  versionUpdataBegin, //更新:未开始
  versionUpdataOnGoing, //更新:进行中
  versionUpdataSuccess, //更新:完成
  versionUpdataErr, //更新:失败
}

class BdhNewVersionView extends StatefulWidget {
  final VersionResult? versionResult;
  const BdhNewVersionView({super.key, this.versionResult});

  @override
  State<BdhNewVersionView> createState() => _BdhNewVersionViewState();
}

class _BdhNewVersionViewState extends State<BdhNewVersionView> {
  VersonUploadState versonUploadState = VersonUploadState.versionUpdataBegin;
  String savePath = '';
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20.px)),
          color: Colors.white),
      height: 376.px,
      width: 300.px,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            top: -40.px,
            left: 60.px,
            child: Image.asset(
              width: 180.px,
              height: 180.px,
              ImageHelper.wrapAssets('versionUpdateImg.png'),
            ),
          ),
          Positioned(
            top: 15.px,
            right: 15.px,
            child: (widget.versionResult!.data?.updateType ?? '') == '02'
                ? GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Image.asset(
                      width: 24.px,
                      height: 24.px,
                      ImageHelper.wrapAssets('arrowCloseUpdate.png'),
                    ),
                  )
                : Container(),
          ),
          Positioned(
            bottom: 0.px,
            child: Container(
              height: 236.px,
              width: 300.px,
              decoration: const BoxDecoration(
                  // border: Border.all(width: 1),
                  ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  SizedBox(
                    // decoration: BoxDecoration(border: Border.all(width: 1)),
                    width: 300.px,
                    // height: 30.px,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          versonUploadState ==
                                  VersonUploadState.versionUpdataSuccess
                              ? '更新成功'
                              : '${widget.versionResult?.data?.version ?? ''}版本更新',
                          style: TextStyle(
                              fontWeight: FontWeight.w600, fontSize: 20.px),
                        )
                      ],
                    ),
                  ),
                  Visibility(
                    visible: versonUploadState ==
                            VersonUploadState.versionUpdataSuccess
                        ? false
                        : true,
                    child: SizedBox(
                      // padding: EdgeInsets.only(top: 20.px),
                      // decoration: BoxDecoration(border: Border.all(width: 1)),
                      width: 244.px,
                      height: 90.px,
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              // '更新内容：新增了环球农业平道，更好用的新版本快来更新吧！',
                              '更新内容：\n${widget.versionResult?.data?.versionDesc ?? ''}',
                              textAlign: TextAlign.left,
                              style: TextStyle(
                                color: BDHColor.black06,
                                fontWeight: FontWeight.w500,
                                fontSize: 14.px,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    // child: Container(
                    //   // padding: EdgeInsets.only(top: 20.px),
                    //   decoration: BoxDecoration(border: Border.all(width: 1)),
                    //   width: 244.px,
                    //   height: 80.px,
                    //   child: Column(
                    //     mainAxisAlignment: MainAxisAlignment.start,
                    //     crossAxisAlignment: CrossAxisAlignment.start,
                    //     children: [
                    //       Text(
                    //         // '更新内容：新增了环球农业平道，更好用的新版本快来更新吧！',
                    //         '更新内容：\n${widget.versionResult?.data?.versionDesc ?? ''}',
                    //         textAlign: TextAlign.left,
                    //         style: TextStyle(
                    //           color: BDHColor.black06,
                    //           fontWeight: FontWeight.w500,
                    //           fontSize: 14.px,
                    //         ),
                    //       ),
                    //     ],
                    //   ),
                    // ),
                  ),
                  Column(
                    children: [
                      // 按钮视图
                      Visibility(
                        visible: versonUploadState ==
                                VersonUploadState.versionUpdataOnGoing
                            ? false
                            : true,
                        child: GestureDetector(
                          onTap: () {
                            //立即升级
                            if (versonUploadState ==
                                VersonUploadState.versionUpdataBegin) {
                              setState(() {
                                versonUploadState =
                                    VersonUploadState.versionUpdataOnGoing;
                              });
                            }

                            //再次更新
                            if (versonUploadState ==
                                VersonUploadState.versionUpdataErr) {
                              setState(() {
                                versonUploadState =
                                    VersonUploadState.versionUpdataOnGoing;
                              });
                            }

                            //立即安装
                            if (versonUploadState ==
                                VersonUploadState.versionUpdataBegin) {
                              setState(() {
                                versonUploadState =
                                    VersonUploadState.versionUpdataOnGoing;
                              });
                            }

                            //立即体验
                            if (versonUploadState ==
                                VersonUploadState.versionUpdataSuccess) {
                              setState(() {
                                Navigator.of(context).pop();
                              });
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  HexColor("#1ED874"),
                                  HexColor("#17C79E"),
                                ],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(4.px)),
                            ),
                            width: 172.px,
                            height: 40.px,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  versonUploadState ==
                                          VersonUploadState.versionUpdataBegin
                                      ? '立即升级'
                                      : versonUploadState ==
                                              VersonUploadState.versionUpdataErr
                                          ? '再次更新'
                                          : versonUploadState ==
                                                  VersonUploadState
                                                      .versionUpdataSuccess
                                              ? '立即体验'
                                              : '立即升级',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14.px,
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ),
                      Visibility(
                          visible: versonUploadState ==
                                  VersonUploadState.versionUpdataErr
                              ? true
                              : false,
                          child: Container(
                            padding: EdgeInsets.only(top: 5.px),
                            width: 300.px,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  '更新失败',
                                  style: TextStyle(
                                    color: HexColor('#FF0000'),
                                    fontWeight: FontWeight.w500,
                                    fontSize: 14.px,
                                  ),
                                )
                              ],
                            ),
                          )),
                    ],
                  ),
                  //进度视图
                  Visibility(
                    visible: versonUploadState ==
                            VersonUploadState.versionUpdataOnGoing
                        ? true
                        : false,
                    // child: DownLoadView(
                    //   downloadUrl:
                    //       widget.versionResult?.data?.downloadUrl ?? "",
                    //   size: int.parse(widget.versionResult!.data!.size!),
                    // ),
                    child: BdhNewversionDonwload(
                      downloadUrl:
                          widget.versionResult?.data?.downloadUrl ?? "",
                      size: int.parse(widget.versionResult!.data!.size!),
                      completeInsatllCallBack: (completeInstall) {
                        // Logger().i('_progressValue====$progressValue');
                        if (completeInstall) {
                          setState(() {
                            versonUploadState =
                                VersonUploadState.versionUpdataSuccess;
                          });
                        } else {
                          setState(() {
                            versonUploadState =
                                VersonUploadState.versionUpdataErr;
                          });
                        }
                      },
                    ),

                    // GestureDetector(
                    //   onTap: () {},
                    //   child: Container(
                    //     decoration: BoxDecoration(
                    //         // border: Border.all(width: 1),
                    //         ),
                    //     width: 240.px,
                    //     height: 50.px,
                    //     child: Column(
                    //       children: [
                    //         SizedBox(
                    //           width: 240.px,
                    //           height: 10.px,
                    //           child: LinearProgressIndicator(
                    //             backgroundColor:
                    //                 const Color.fromRGBO(211, 216, 215, 1),
                    //             borderRadius:
                    //                 BorderRadius.all(Radius.circular(13.px)),
                    //             value: 0.5,
                    //           ),
                    //         ),
                    //         SizedBox(height: 10.px),
                    //         // Text("${(_progressValue * 100).toStringAsFixed(0)}%")
                    //         Row(
                    //           mainAxisAlignment:
                    //               MainAxisAlignment.spaceBetween,
                    //           children: [
                    //             Text("更新中...",
                    //                 style: TextStyle(
                    //                   color: BDHColor.black06,
                    //                   fontWeight: FontWeight.w500,
                    //                   fontSize: 14.px,
                    //                 )),
                    //             Text("80%",
                    //                 style: TextStyle(
                    //                   color: BDHColor.black06,
                    //                   fontWeight: FontWeight.w500,
                    //                   fontSize: 14.px,
                    //                 ))
                    //           ],
                    //         ),
                    //       ],
                    //     ),
                    //   ),
                    // ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  } // end build
}
