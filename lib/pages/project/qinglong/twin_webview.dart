import 'package:bdh_smart_agric_app/components/hybrid_webview.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:webview_flutter/webview_flutter.dart';

class TwinWebview extends StatefulWidget {
  final String url;
  const TwinWebview(
      {super.key,
      this.url =
          "https://daapp.bdhic.com/bdh-water-business-application-mobile/"});

  @override
  State<StatefulWidget> createState() => TwinWebviewState();
}

class TwinWebviewState extends State<TwinWebview> {
  late final HybridWebViewController _controller;
  @override
  void initState() {
    super.initState();

    _controller = HybridWebViewController.fromPlatformCreationParams(
        const PlatformWebViewControllerCreationParams());

    _initController(_controller);
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  void _initController(HybridWebViewController controller) {
    _controller
      ..setBackgroundColor(Colors.white)
      //测试用,如果需要测试则注释掉loadRequest
      //..loadFlutterAsset('assets/js/test.html')
      ..loadRequest(Uri.parse(widget.url))
      ..addJavaScriptObject(JsApi())
      ..addJavaScriptObject(JsEchoApi(), namespace: 'echo')
      ..addJavaScriptObject(BdhApi(context: context))
      ..setNavigationDelegate(NavigationDelegate(
        onProgress: (progress) {
          debugPrint('process=$progress');
        },
        onPageStarted: (url) {
          debugPrint('url onPageStarted $url');
        },
        onPageFinished: (url) {
          debugPrint('url onPageFinished $url');
          _loadBdhScript();
        },
      ));
  }

  void _loadBdhScript() async {
    if (mounted) {
      String js = await rootBundle.loadString('assets/js/bdh_js_bridge.js');
      String jsBridge = '''
        javascript:$js
      ''';
      _controller.runJavaScript(jsBridge);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: HybridWebViewWidget(controller: _controller),
    );
  }
}

class BdhApi extends JavaScriptNamespaceInterface {
  final BuildContext context;

  BdhApi({required this.context});
  @override
  void register() {
    registerFunction(logout);
    registerFunction(userInfo);
  }

  //获取用户信息
  //这么用有安全隐患,无法避免中间人攻击,后续应该考虑更加稳妥的获取Token的方法
  @pragma('vm:entry-point')
  String userInfo(dynamic msg) {
    if (context.mounted) {
      var data = StorageUtil.userInfo()?.data;
      return data?.toJson().toString() ?? "{}";
    }
    return "{}";
  }

  //退出登录
  @pragma('vm:entry-point')
  void logout(dynamic msg) {
    if (context.mounted) {
      BrnLoadingDialog.show(context,
          content: "正在退出..   ", barrierDismissible: false);
      StorageManager.storage?.clear();
      BDHResponsitory.logout({}).then((res) {
        StorageManager.storage?.clear();
        if (context.mounted) {
          BrnLoadingDialog.dismiss(context);
          Navigator.of(context).popAndPushNamed(RouteName.splash);
        }
      }).onError((e, s) {
        if (context.mounted) {
          BrnLoadingDialog.dismiss(context);
        }
      });
    }
  }
}
