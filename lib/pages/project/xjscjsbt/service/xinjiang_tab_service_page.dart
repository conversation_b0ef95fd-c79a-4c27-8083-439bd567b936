import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/search_new_page.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/news_message_home.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluwx/fluwx.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

class XinJiangTabServicePage extends StatefulWidget {
  final MenuConfigItem item;
  const XinJiangTabServicePage({super.key, required this.item});

  @override
  State<StatefulWidget> createState() => _XinJiangTabServicePageState();
}

class _XinJiangTabServicePageState extends State<XinJiangTabServicePage> {
  List<MenuConfigItem> topItems = [];
  List<MenuConfigItem> bottomGroups = [];

  @override
  void initState() {
    super.initState();

    //topItems.add(MenuConfigItem(authName: "粮食交易", icon: "amKeep", url: "2"));
    //topItems.add(MenuConfigItem(authName: "线上缴费", icon: "amKeep", url: "1"));
    //topItems.add(MenuConfigItem(authName: "土地承包", icon: "amKeep", url: "1"));
    for (var v in widget.item.children!) {
      if (v.authCode == "topMenu") {
        for (var topItem in v.children!) {
          topItems.add(topItem);
        }
      }

      // bottomGroups.add(MenuConfigItem(authName: "authName1", children: [
      //   MenuConfigItem(authName: "name", icon: "a"),
      //   MenuConfigItem(authName: "name", icon: "a"),
      //   MenuConfigItem(authName: "name", icon: "a"),
      //   MenuConfigItem(authName: "name", icon: "a")
      // ]));
      // bottomGroups
      //     .add(MenuConfigItem(authName: "authName2", children: topItems));
      // bottomGroups
      //     .add(MenuConfigItem(authName: "authName3", children: topItems));

      if (v.authCode == "bottomMenu") {
        for (var bottomGroup in v.children!) {
          bottomGroups.add(bottomGroup);
        }
      }
    }
  }

  void _onClickItem(MenuConfigItem item) {
    if (item.url!.startsWith("gh_")) {
      Fluwx fluwx = Fluwx();
      fluwx.open(target: MiniProgram(username: item.url!));
    } else if (item.url!.startsWith("pages")) {
      //跳转uni小程序
      //土地承包特殊处理
      if (item.authName == "土地承包") {
        Navigator.of(context).pushNamed(RouteName.bdhLandContract);
      } else {
        NativeUtil.openUni({"path": item.url});
      }
    } else {
      Navigator.of(context)
          .pushNamed(item.url ?? "无效路径", arguments: item)
          .then((res) {
        GlobalServiceView.needShowServiceBtn('service');
      });
    }
  }

  Widget _widgetMainArea() {
    int length = topItems.length;

    Widget? child;
    if (length == 1) {
      child = Row(
        children: [
          Flexible(flex: 1, child: _widgetMainAreaItemFullWidth(topItems.first))
        ],
      );
    } else if (length == 2) {
      child = Row(
        children: [
          Expanded(
              flex: 1, child: _widgetMainAreaItemFullWidthPart(topItems.first)),
          SizedBox(
            width: 10.px,
          ),
          Expanded(
              flex: 1, child: _widgetMainAreaItemFullWidthPart(topItems.last))
        ],
      );
    } else if (length == 3) {
      child = Row(
        children: [
          Expanded(child: _widgetMainAreaItemFullHeight(topItems.first)),
          SizedBox(
            width: 10.px,
          ),
          Expanded(
              child: Column(
            children: [
              TopItemView(
                item: topItems[1],
                onClick: _onClickItem,
              ),
              SizedBox(
                height: 10.px,
              ),
              TopItemView(
                item: topItems[2],
                onClick: _onClickItem,
              )
            ],
          ))
        ],
      );
    } else {
      child = Row(
        children: [
          Expanded(
              child: Column(
            children: [
              TopItemView(
                item: topItems[0],
                onClick: _onClickItem,
              ),
              SizedBox(
                height: 10.px,
              ),
              TopItemView(
                item: topItems[1],
                onClick: _onClickItem,
              ),
            ],
          )),
          SizedBox(
            width: 10.px,
          ),
          Expanded(
              child: Column(
            children: [
              TopItemView(
                item: topItems[2],
                onClick: _onClickItem,
              ),
              SizedBox(
                height: 10.px,
              ),
              TopItemView(
                item: topItems[3],
                onClick: _onClickItem,
              ),
            ],
          ))
        ],
      );
    }

    return Container(
      margin: EdgeInsets.only(bottom: 20.px, left: 20.px, right: 20.px),
      child: child,
    );
  }

  Widget _widgetMainAreaItemFullWidth(MenuConfigItem item) {
    return TopItemView(
      item: item,
      height: 132.px,
      iconBottom: 0,
      iconHeight: 132.px,
      iconWidth: 132.px,
      summaryWidth: 200.px,
      summaryMaxLines: 1,
      showDetailButton: true,
      onClick: _onClickItem,
    );
  }

  Widget _widgetMainAreaItemFullHeight(MenuConfigItem item) {
    return TopItemView(
      item: item,
      height: 212.px,
      width: 165.px,
      iconHeight: 100.px,
      iconWidth: 100.px,
      summaryWidth: 100.px,
      iconBottom: 0,
      showDetailButton: true,
      onClick: _onClickItem,
    );
  }

  Widget _widgetMainAreaItemFullWidthPart(MenuConfigItem item) {
    return TopItemView(
      item: item,
      height: 150.px,
      width: 165.px,
      iconHeight: 100.px,
      iconWidth: 100.px,
      summaryWidth: 100.px,
      showDetailButton: false,
      onClick: _onClickItem,
    );
  }

  Widget _bodyWidget() {
    return Column(
      children: [
        AppBar(
          title: Padding(
              padding: EdgeInsets.only(left: 20.px),
              child: Text("农业一站式服务",
                  style: TextStyle(
                      fontSize: 20.px,
                      fontWeight: FontWeight.w700,
                      color: Colors.white))),
          centerTitle: false,
          toolbarHeight: 44.px,
          backgroundColor: Colors.transparent,
          titleSpacing: 0,
          actions: [
            GestureDetector(
              onTap: () {
                Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                  return const NewsMessageHome();
                }));
              },
              child: SvgPicture.asset(
                  alignment: Alignment.center,
                  fit: BoxFit.cover,
                  width: 24.px,
                  ImageHelper.wrapAssets("ic_chat.svg")),
            ),
            SizedBox(
              width: 5.px,
            ),
            GestureDetector(
              onTap: () {
                Navigator.of(context)
                    .push(CupertinoPageRoute(builder: (context) {
                  return const SearchNewPage();
                }));
              },
              child: SvgPicture.asset(
                  alignment: Alignment.center,
                  fit: BoxFit.cover,
                  width: 24.px,
                  colorFilter:
                      const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                  ImageHelper.wrapAssets("ic_search.svg")),
            ),
            SizedBox(
              width: 20.px,
            ),
          ],
        ),
        Expanded(
          child: CustomScrollView(
            slivers: [
              SliverPadding(padding: EdgeInsets.only(top: 10.px)),
              SliverToBoxAdapter(
                  child: Column(
                children: [
                  if (topItems.isNotEmpty) _widgetMainArea(),
                  if (bottomGroups.isNotEmpty)
                    ...bottomGroups.map((group) {
                      return Container(
                        width: double.infinity,
                        decoration: BoxDecoration(
                            color: const Color.fromRGBO(255, 255, 255, 1),
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.px))),
                        margin: EdgeInsets.only(
                            bottom: 20.px, left: 20.px, right: 20.px),
                        padding: EdgeInsets.only(
                            top: 12.px,
                            bottom: 15.px,
                            left: 7.5.px,
                            right: 7.5.px),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  width: 7.5.px,
                                ),
                                Text(
                                  group.authName ?? "",
                                  style: TextStyle(
                                      fontSize: 12.px,
                                      fontWeight: FontWeight.w600,
                                      color: const Color.fromRGBO(
                                          102, 102, 102, 1)),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 20.px,
                            ),
                            SizedBox(
                              child: Wrap(
                                runSpacing: 20.px,
                                children: [
                                  ...(group.children ?? []).map((e) {
                                    return MenuItemView(
                                      item: e,
                                      isGroup: true,
                                      onClick: _onClickItem,
                                    );
                                  })
                                ],
                              ),
                            )
                          ],
                        ),
                      );
                    })
                ],
              ))
            ],
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: Container(
      decoration: const BoxDecoration(
          image: DecorationImage(
        image: AssetImage('assets/images/bg_xinjiang_tab_service.png'), // 本地图片
        fit: BoxFit.cover,
      )),
      child: _bodyWidget(),
    ));
  }
}

//定制服务器下发的 icon
final _overlayTopItem = {
  "粮食交易": {
    "summary": "一站式粮食撮合平台一站式粮食撮合平台一站式粮食撮合平台",
    "iconBuilder": _grainTradingTopItemViewIconBuilder,
  },
  "土地承包": {
    "summary": "轻松线上完成",
    "iconBuilder": _landContractTopItemViewIconBuilder,
  },
  "水费管理": {
    "summary": "轻松线上完成水费缴纳",
    "iconBuilder": _waterManageTopItemViewIconBuilder,
  },
};

typedef IconBuilder = Widget Function(
    BuildContext context, double width, double height);

IconBuilder _defaultTopItemViewIconBuilder = (context, width, height) {
  return SvgPicture.asset(
    ImageHelper.wrapAssets("ic_service_main_function_default.svg"),
    width: width,
    height: height,
  );
};

IconBuilder _grainTradingTopItemViewIconBuilder = (context, width, height) {
  return ClipRRect(
      borderRadius: BorderRadius.all(Radius.circular(8.px)),
      child: Image.asset(
          width: width,
          height: height,
          ImageHelper.wrapAssets("pic_grain_trading.png")));
};

IconBuilder _landContractTopItemViewIconBuilder = (context, width, height) {
  return SvgPicture.asset(
    ImageHelper.wrapAssets("ic_service_main_function_land.svg"),
    width: width,
    height: height,
  );
};

IconBuilder _waterManageTopItemViewIconBuilder = (context, width, height) {
  return SvgPicture.asset(
    ImageHelper.wrapAssets("ic_service_main_function_water.svg"),
    width: width,
    height: height,
  );
};

class TopItemView extends StatelessWidget {
  final MenuConfigItem item;
  final double? width;
  final double? height;
  final double? iconWidth;
  final double? iconHeight;
  final bool showDetailButton;
  final double? summaryWidth;
  final int summaryMaxLines;
  final double? iconBottom;
  final Function(MenuConfigItem) onClick;
  const TopItemView(
      {super.key,
      required this.item,
      this.width,
      this.height,
      this.iconWidth,
      this.iconHeight,
      this.showDetailButton = false,
      this.summaryWidth,
      this.iconBottom,
      this.summaryMaxLines = 2,
      required this.onClick});

  @override
  Widget build(BuildContext context) {
    IconBuilder iconBuilder =
        _overlayTopItem[item.authName]?["iconBuilder"] as IconBuilder? ??
            _defaultTopItemViewIconBuilder;

    String summary =
        _overlayTopItem[item.authName]?["summary"] as String? ?? "便民的线上服务";
    Widget child = Container(
      height: height ?? 102.px,
      width: width,
      decoration: BoxDecoration(
          gradient: const LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Color.fromRGBO(218, 252, 237, 1),
                Color.fromRGBO(255, 255, 255, 1)
              ]),
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Stack(
        children: [
          Column(
            children: [
              SizedBox(
                height: 20.px,
              ),
              Row(
                children: [
                  SizedBox(
                    width: 20.px,
                  ),
                  Text(item.authName ?? "",
                      style: TextStyle(
                          fontSize: 20.px,
                          fontWeight: FontWeight.w700,
                          color: Colors.black)),
                  SvgPicture.asset(ImageHelper.wrapAssets("arrow_forward.svg"),
                      width: 16.px,
                      height: 16.px,
                      colorFilter:
                          const ColorFilter.mode(Colors.black, BlendMode.srcIn))
                ],
              ),
              SizedBox(
                height: 2.px,
              ),
              Row(
                children: [
                  SizedBox(
                    width: 20.px,
                  ),
                  SizedBox(
                    width: summaryWidth ?? 80.px,
                    child: Text(summary,
                        maxLines: summaryMaxLines,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 12.px,
                            fontWeight: FontWeight.w400,
                            color: const Color.fromRGBO(0, 0, 0, 0.3))),
                  ),
                ],
              ),
              SizedBox(
                height: 16.px,
              ),
              if (showDetailButton)
                Row(
                  children: [
                    SizedBox(
                      width: 20.px,
                    ),
                    Container(
                      width: 64.px,
                      height: 24.px,
                      decoration: BoxDecoration(
                          color: const Color.fromRGBO(30, 192, 106, 0.2),
                          borderRadius:
                              BorderRadius.all(Radius.circular(14.px))),
                      child: Center(
                          child: Text("查看详情",
                              style: TextStyle(
                                  fontSize: 12.px,
                                  fontWeight: FontWeight.w500,
                                  color:
                                      const Color.fromRGBO(30, 192, 106, 1)))),
                    )
                  ],
                )
            ],
          ),
          Positioned(
              right: 0,
              bottom: iconBottom ?? -10.px,
              child:
                  iconBuilder(context, iconWidth ?? 72.px, iconHeight ?? 72.px))
        ],
      ),
    );
    child = GestureDetector(
      onTap: () {
        onClick.call(item);
      },
      child: child,
    );
    return child;
  }
}

class MenuItemView extends StatelessWidget {
  final bool isGroup;
  final MenuConfigItem item;
  final Function(MenuConfigItem) onClick;
  const MenuItemView(
      {super.key,
      required this.item,
      this.isGroup = false,
      required this.onClick});

  Widget _widgetIcon() {
    return Image.asset(
        width: 40.px,
        height: 40.px,
        ImageHelper.wrapOldAssets("${item.icon!}.png"),
        errorBuilder: (context, error, stackTrace) {
      return SvgPicture.asset(
        ImageHelper.wrapAssets("ic_default.svg"),
        width: 40.px,
        height: 40.px,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (item.url!.startsWith("gh_")) {
          Fluwx fluwx = Fluwx();
          fluwx.open(target: MiniProgram(username: item.url!));
        } else if (item.url!.startsWith("pages")) {
          //跳转uni小程序
          //土地承包特殊处理
          if (item.authName == "土地承包") {
            Navigator.of(context).pushNamed(RouteName.bdhLandContract);
          } else {
            NativeUtil.openUni({"path": item.url});
          }
        } else {
          Navigator.of(context)
              .pushNamed(item.url ?? "无效路径", arguments: item)
              .then((res) {
            GlobalServiceView.needShowServiceBtn('service');
          });
        }
      },
      child: Container(
        width: 80.px,
        color: Colors.transparent,
        child: Column(
          children: [
            _widgetIcon(),
            SizedBox(
              height: 5.px,
            ),
            Text(
              item.authName ?? "",
              textAlign: TextAlign.center,
              strutStyle: StrutStyle(fontSize: 12.px, forceStrutHeight: true),
              style: TextStyle(
                  color: const Color.fromRGBO(51, 51, 51, 1),
                  fontSize: 12.px,
                  fontWeight: FontWeight.w500),
            )
          ],
        ),
      ),
    );
  }
}
