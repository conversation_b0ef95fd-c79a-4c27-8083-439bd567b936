import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class FddContractWebView extends StatefulWidget {
  final String url;
  const FddContractWebView({super.key, required this.url});

  @override
  State<StatefulWidget> createState() => _FddContractWebViewState();
}

class _FddContractWebViewState extends State<FddContractWebView> {
  final WebViewController _webViewController = WebViewController();
  @override
  void initState() {
    super.initState();
    _webViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    _webViewController.loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("合同签订"),
      ),
      body: WebViewWidget(controller: _webViewController),
    );
  }
}
