import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_check.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:logger/web.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../../../model/query_contract_list_model.dart';

class LandContractSignPage extends StatefulWidget {
  const LandContractSignPage({super.key});

  @override
  State<StatefulWidget> createState() => _LandContractSignPageState();
}

class _LandContractSignPageState extends State with WidgetsBindingObserver {
  DictNode? year;
  DictNode? contractType;

  List<DictNode> yearDics = [];
  List<DictNode> contractTypeDics = [];
  List<ContractItem> items = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    loadDics();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (ctx, cons) {
      return Scaffold(
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: SizedBox(
          width: 375.px,
          height: cons.maxHeight,
          child: Stack(
            children: [
              Image.asset(
                  width: 375.px,
                  ImageHelper.wrapAssets("land_contract_sign_background.png")),
              Positioned(
                  left: 20.px,
                  top: 184.px,
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          TDPicker.showMultiPicker(context, onConfirm: (data) {
                            // print(data);
                            setState(() {
                              year = yearDics[data.first];
                              isLoading = true;
                            });
                            loadContracts();
                            Navigator.of(context).pop();
                          }, data: [
                            yearDics.map((e) {
                              return e.name!;
                            }).toList()
                          ]);
                        },
                        child: Container(
                          padding: EdgeInsets.only(left: 10.px, right: 10.px),
                          height: 24.px,
                          decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(12.px)),
                              color: const Color.fromRGBO(118, 228, 215, 0.3)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                year?.name ?? "请选择年份",
                                style: TextStyle(
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                width: 10.px,
                              ),
                              Image.asset(
                                  width: 11.px,
                                  height: 11.px,
                                  ImageHelper.wrapAssets("arrow_down_line.png"))
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 10.px,
                      ),
                      GestureDetector(
                        onTap: () {
                          TDPicker.showMultiPicker(context, onConfirm: (data) {
                            // print(data);
                            setState(() {
                              contractType = contractTypeDics[data.first];
                              isLoading = true;
                            });
                            loadContracts();
                            Navigator.of(context).pop();
                          }, data: [
                            contractTypeDics.map((e) {
                              return e.name!;
                            }).toList()
                          ]);
                        },
                        child: Container(
                          height: 24.px,
                          padding: EdgeInsets.only(left: 10.px, right: 10.px),
                          decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(12.px)),
                              color: const Color.fromRGBO(118, 228, 215, 0.3)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                contractType?.name ?? "请选择合同类型",
                                style: TextStyle(
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                width: 10.px,
                              ),
                              Image.asset(
                                  width: 11.px,
                                  height: 11.px,
                                  ImageHelper.wrapAssets("arrow_down_line.png"))
                            ],
                          ),
                        ),
                      )
                    ],
                  )),
              Positioned(
                left: 16.px,
                top: 224.px,
                child: SizedBox(
                  width: 343.px,
                  height: cons.maxHeight -
                      MediaQuery.of(context).padding.bottom -
                      224.px,
                  child: isLoading
                      ? const ViewStateBusyWidget()
                      : (items.isNotEmpty
                          ? ListView.builder(
                              padding: EdgeInsets.zero,
                              itemCount: items.length,
                              itemBuilder: (ctx, index) {
                                return ContractItemView(item: items[index]);
                              })
                          : const BdhEmptyView()),
                ),
              )
            ],
          ),
        ),
      );
    });
  }

  loadDics() {
    setState(() {
      isLoading = true;
    });
    Future.wait([
      LandResponsitory.getDicByKey("year_cd"),
      LandResponsitory.getDicByKey("os_contract_type")
    ]).then((list) {
      yearDics = list[0].data ?? [];
      year = yearDics.first;
      contractTypeDics = list[1].data ?? [];
      // contractType = contractTypeDics.first;
      loadContracts();
    });
  }

  loadContracts() {
    // print(jsonEncode({
    //   "yearNo": year?.code,
    //   "osContractType": contractType?.code,
    // }));
    LandResponsitory.queryContractList({
      "yearNo": year?.code,
      "osContractType": contractType?.code,
    }).then((res) {
      setState(() {
        isLoading = false;
        items = res.data ?? [];
      });
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      setState(() {
        isLoading = true;
      });
      loadContracts();
    }
    super.didChangeAppLifecycleState(state);
  }
}

class ContractItemView extends StatefulWidget {
  final ContractItem item;
  const ContractItemView({super.key, required this.item});

  @override
  State<StatefulWidget> createState() => _ContraceItemViewState();
}

class _ContraceItemViewState extends State<ContractItemView> {
  bool isCheck = false;
  bool isOpen = false;
  @override
  Widget build(BuildContext context) {
    TextStyle textStyle1 = TextStyle(
        fontSize: 12.px,
        fontWeight: FontWeight.w400,
        color: const Color.fromRGBO(41, 41, 52, 1));
    TextStyle textStyle2 = TextStyle(
        fontSize: 12.px,
        fontWeight: FontWeight.w500,
        overflow: TextOverflow.ellipsis,
        color: const Color.fromRGBO(41, 41, 52, 0.8));
    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      margin: EdgeInsets.only(bottom: 10.px),
      width: 343.px,
      child: Column(
        children: [
          Container(
            height: 52.px,
            padding: EdgeInsets.only(right: 15.px),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 3.px,
                      height: 17.px,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.only(
                              topRight: Radius.circular(2.px),
                              bottomRight: Radius.circular(2.px)),
                          color: const Color.fromRGBO(10, 174, 108, 1)),
                    ),
                    SizedBox(
                      width: 16.px,
                    ),
                    BdhCheck(
                      width: 15.px,
                      isCheck: isCheck,
                      onClick: (check) {
                        setState(() {
                          isCheck = check;
                        });
                        if (isCheck == true) {
                          showDialog(
                              context: context,
                              builder: (ctx) {
                                return Align(
                                  alignment: Alignment.bottomCenter,
                                  child: Container(
                                    margin: EdgeInsets.only(bottom: 20.px),
                                    child: GestureDetector(
                                      onTap: () {
                                        Navigator.of(context).pop();
                                        (widget.item.contractSignNo != null &&
                                                widget.item.contractSignNo != 0)
                                            ? LandResponsitory.scanContract({
                                                "fddContractNo":
                                                    widget.item.fddContractNo,
                                                "mobileDevice": true
                                              }).then((res) {
                                                if (res.code == 0) {
                                                  NativeUtil.openFdd(res.data);
                                                }
                                              })
                                            : LandResponsitory.manualSignPage({
                                                "fddContractNo":
                                                    widget.item.fddContractNo
                                              }).then((res) {
                                                if (res.code == 0) {
                                                  Logger().i(res.data);
                                                  NativeUtil.openFdd(res.data);
                                                }
                                              });
                                      },
                                      child: BDHButtonGreen(
                                          width: 341.px,
                                          height: 50.px,
                                          title: (widget.item.contractSignNo !=
                                                      null &&
                                                  widget.item.contractSignNo !=
                                                      0)
                                              ? "查看合同"
                                              : '签订'),
                                    ),
                                  ),
                                );
                              }).then((res) {
                            setState(() {
                              isCheck = false;
                            });
                          });
                        }
                      },
                      isCircle: true,
                    ),
                    SizedBox(
                      width: 8.px,
                    ),
                    Container(
                      constraints: BoxConstraints(
                        maxWidth: 200.px, // 设置最大宽度为200
                      ),
                      child: Text(
                        '${widget.item.serialNumber}',
                        style: TextStyle(
                            fontSize: 15.px, fontWeight: FontWeight.w500),
                      ),
                    ),
                    SizedBox(
                      width: 8.px,
                    ),
                    widget.item.contractSignNo == 1
                        ? const TagColor(
                            title: "已签订",
                            color: Color.fromRGBO(10, 174, 108, 1))
                        : const TagColor(
                            title: "未签订",
                            color: Color.fromRGBO(255, 110, 70, 1)),
                  ],
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      isOpen = !isOpen;
                    });
                  },
                  child: isOpen
                      ? const Text(
                          "收起",
                          style: TextStyle(color: Colors.grey),
                        )
                      : const Text("展开", style: TextStyle(color: Colors.green)),
                )
              ],
            ),
          ),
          Visibility(
              visible: isOpen,
              child: Container(
                padding: EdgeInsets.all(15.px),
                margin:
                    EdgeInsets.only(left: 15.px, bottom: 15.px, right: 15.px),
                decoration: BoxDecoration(
                    color: const Color.fromRGBO(243, 245, 249, 0.8),
                    borderRadius: BorderRadius.all(Radius.circular(4.px))),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "年份",
                          style: textStyle1,
                        ),
                        SizedBox(
                          width: 200.px,
                          child: Text(
                            "${widget.item.yearNo ?? 0}",
                            style: textStyle2,
                            textAlign: TextAlign.right,
                          ),
                        )
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "所属农场",
                          style: textStyle1,
                        ),
                        SizedBox(
                          width: 200.px,
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Text(
                              widget.item.organizationName ?? "",
                              style: textStyle2,
                              maxLines: 3,
                            ),
                          ),
                        )
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "合同编号",
                          style: textStyle1,
                        ),
                        SizedBox(
                            width: 200.px,
                            child: Text(
                              widget.item.serialNumber ?? "",
                              style: textStyle2,
                              textAlign: TextAlign.right,
                            ))
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "姓名",
                          style: textStyle1,
                        ),
                        SizedBox(
                            width: 200.px,
                            child: Text(
                              widget.item.farmerName ?? "",
                              style: textStyle2,
                              textAlign: TextAlign.right,
                            ))
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "身份证号",
                          style: textStyle1,
                        ),
                        SizedBox(
                            width: 200.px,
                            child: Text(
                              widget.item.farmerIdNumber ?? "",
                              style: textStyle2,
                              textAlign: TextAlign.right,
                            ))
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "收费面积(亩)",
                          style: textStyle1,
                        ),
                        SizedBox(
                            width: 200.px,
                            child: Text(
                              "${widget.item.chargeArea ?? 0}",
                              style: textStyle2,
                              textAlign: TextAlign.right,
                            ))
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "合同金额",
                          style: textStyle1,
                        ),
                        Text(
                          "${widget.item.totalFee ?? 0}",
                          style: textStyle2,
                        )
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "签订时间",
                          style: textStyle1,
                        ),
                        Text(
                          widget.item.contractSignDate ?? "",
                          style: textStyle2,
                        )
                      ],
                    ),
                  ],
                ),
              ))
        ],
      ),
    );
  }
}

class TagColor extends StatelessWidget {
  final String title;
  final Color color;
  const TagColor({super.key, required this.title, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 3.px, right: 3.px),
      decoration: BoxDecoration(
          color: color, borderRadius: BorderRadius.all(Radius.circular(3.px))),
      child: Text(
        title,
        style: TextStyle(
            color: Colors.white, fontSize: 11.px, fontWeight: FontWeight.w500),
      ),
    );
  }
}
