import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class FddWebview extends StatefulWidget {
  final String url;
  const FddWebview({
    super.key,
    required this.url,
  });

  @override
  State<StatefulWidget> createState() => _FddWebviewState();
}

class _FddWebviewState extends State<FddWebview> {
  WebViewController controller = WebViewController();

  @override
  void initState() {
    super.initState();
    controller.setJavaScriptMode(JavaScriptMode.unrestricted);
    controller.loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: WebViewWidget(controller: controller),
    );
  }
}
