import 'dart:convert';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/enterprise_info_result_model.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/info/info_collect_enterprise_save.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/info/info_collect_save.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/transfer/application_for_management_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/info/info_collect.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/info/info_collect_enterprise.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/contract/land_contract_sign_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/realname/real_name_auth_page.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:bdh_smart_agric_app/viewmodel/user_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:provider/provider.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class XjLandContractPage extends StatefulWidget {
  const XjLandContractPage({super.key});

  @override
  State<StatefulWidget> createState() => _XjLandContractPageState();
}

class _XjLandContractPageState extends State {
  List<MenuItem> items = [
    MenuItem("信息采集", "menu_collect.png", "path"),
    // MenuItem("企业信息", "menu_enterprise.png", "path"),
    MenuItem("合同签订", "menu_contract.png", "path"),
    MenuItem("身份地流转", "menu_bussiness_apply.png", "path")
  ];
  LandBaseInfo? landBaseInfo;
  Partner? partner;

  @override
  void initState() {
    super.initState();
    //1.判断是否实名

    bus.on("refreshLandInfo", (v) {
      loadInfo();
    });
    Future.delayed(const Duration(milliseconds: 100), () {
      getRealNameStatus();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    super.dispose();
    bus.off("refreshLandInfo");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F5F9),
      body: LayoutBuilder(builder: (ctx, cons) {
        return SizedBox(
            width: cons.maxWidth,
            height: cons.maxHeight,
            child: Stack(
              children: [
                Stack(
                  children: [
                    Image.asset(
                        width: 375.px,
                        ImageHelper.wrapAssets("land_contract_background.png")),
                    Positioned(
                      left: 20.px,
                      top: MediaQuery.of(context).padding.top + 15.px,
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        child: Icon(Icons.chevron_left,
                            color: Colors.white, size: 30.px),
                      ),
                    ),
                    Positioned(
                      bottom: 100.px,
                      right: 0,
                      child: Image.asset(
                        ImageHelper.wrapAssets("land_contract_headIcon.png"),
                        width: 131.px,
                      ),
                    ),
                  ],
                ),
                landBaseInfo != null
                    ? Positioned(
                        left: 12.px,
                        top: 217.px,
                        child: Container(
                          width: 351.px,
                          height: 103.px,
                          decoration: BoxDecoration(
                              image: DecorationImage(
                                  fit: BoxFit.cover,
                                  image: AssetImage(ImageHelper.wrapAssets(
                                      "land_contract_menu_background.png")))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              ...items.map((e) {
                                return MenuItemView(
                                  item: e,
                                  onClick: (item) {
                                    if (item.name == "信息采集") {
                                      Navigator.of(context).push(
                                          CupertinoPageRoute(builder: (ctx) {
                                        return InfoCollectSavePage(
                                          baseInfo: landBaseInfo,
                                          title: "信息采集",
                                        );
                                      }));
                                    }
                                    if (item.name == "企业信息") {
                                      if (partner != null) {
                                        Navigator.of(context).push(
                                            CupertinoPageRoute(builder: (ctx) {
                                          return InfoCollectEnterpriseSavePage(
                                            partner: partner,
                                            title: "企业信息",
                                            steps: const ["企业信息", "法人信息"],
                                            stepIndex: 0,
                                          );
                                        }));
                                      } else {
                                        Navigator.of(context).push(
                                            CupertinoPageRoute(builder: (ctx) {
                                          return InfoCollectEnterprisePage(
                                            title: "企业信息",
                                            steps: const [
                                              "企业信息",
                                              "法人信息",
                                              "代办人信息"
                                            ],
                                            landBaseInfo: landBaseInfo,
                                            stepIndex: 0,
                                          );
                                        }));
                                      }
                                    }
                                    if (item.name == "合同签订") {
                                      Navigator.of(context).push(
                                          CupertinoPageRoute(builder: (ctx) {
                                        return const LandContractSignPage();
                                      }));
                                    }
                                    if (item.name == "身份地流转") {
                                      Navigator.of(context).push(
                                          CupertinoPageRoute(builder: (ctx) {
                                        return const ApplicationForManagementPage();
                                      }));
                                    }
                                  },
                                );
                              })
                            ],
                          ),
                        ))
                    : Container(),
                // const BottomAll()
              ],
            ));
      }),
    );
  }

  getRealNameStatus() async {
    TDToast.showLoadingWithoutText(context: context, preventTap: true);
    var accountId = StorageUtil.userInfo()?.data?.id;
    var result = await LandResponsitory.getRealNameStatus(accountId);
    if (result.data == null) {
      TDToast.dismissLoading();

      //还没有实名认证
      showRoleDialog();
    } else {
      loadInfo();
    }
  }

  showRoleDialog() {
    if (mounted) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (ctx) {
            return RoleSelectContainer(
              onSelect: (roleItem) {
                StorageManager.sharedPreferences
                    ?.setString("landRoleName", roleItem.roleName);
                if (roleItem.roleName == "职工" || roleItem.roleName == "非职工") {
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return RealNameAuthPage(
                      title: "基本信息-${roleItem.roleName}",
                      stepIndex: 0,
                      steps: const ["实名认证", "信息补充"],
                    );
                  }));
                } else if (roleItem.roleName == "企业") {
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return const InfoCollectEnterprisePage(
                      title: "企业信息",
                      stepIndex: 0,
                      steps: ["企业信息", "法人信息", "代办人实名认证", "代办人信息"],
                    );
                  }));
                }
              },
            );
          });
    }
  }

  showRoleDialogAuthed() {
    if (mounted) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (ctx) {
            return RoleSelectContainer(
              onSelect: (roleItem) {
                StorageManager.sharedPreferences
                    ?.setString("landRoleName", roleItem.roleName);
                if (roleItem.roleName == "职工") {
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return InfoCollectPage(
                      baseInfo: landBaseInfo,
                      title: "基本信息-职工",
                    );
                  }));
                } else if (roleItem.roleName == "非职工") {
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return InfoCollectPage(
                      baseInfo: landBaseInfo,
                      title: "基本信息-非职工",
                    );
                  }));
                } else if (roleItem.roleName == "企业") {
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return const InfoCollectEnterprisePage(
                      title: "企业信息",
                      steps: ["企业信息", "法人信息", "代办人信息"],
                      stepIndex: 0,
                    );
                  }));
                }
              },
            );
          });
    }
  }

  //查询基本信息和企业信息’
  loadInfo() {
    Future.wait([
      LandResponsitory.getResidentInfo(),
      LandResponsitory.queryEnterpriseInfo({}),
      LandResponsitory.getDicByKey("opposite_party_type")
    ]).then((resList) {
      TDToast.dismissLoading();
      landBaseInfo = (resList[0] as LandBaseInfoResult).data;
      StorageManager.storage
          ?.setItem(kLandBaseInfo, jsonEncode(landBaseInfo!.toJson()));
      partner = (resList[1] as EnterpriseInfoResult).data?.partner;
      context.read<UserModel>().setPartyType(resList[2] as DictList);
      setState(() {});
      if (landBaseInfo?.organizationNo != null) {
        return;
      } else {
        showRoleDialogAuthed();
      }
    });
  }
}

class MenuItem {
  String name;
  String icon;
  String path;
  MenuItem(this.name, this.icon, this.path);
}

class MenuItemView extends StatelessWidget {
  final MenuItem item;
  final Function(MenuItem) onClick;
  const MenuItemView({super.key, required this.item, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClick(item);
      },
      child: SizedBox(
        height: 63.px,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Image.asset(
                width: 40.px, height: 40.px, ImageHelper.wrapAssets(item.icon)),
            Text(
              item.name,
              style: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w500,
                  color: const Color.fromRGBO(51, 51, 51, 1)),
            )
          ],
        ),
      ),
    );
  }
}

class RoleItem {
  String roleName;
  String desc;
  bool isSelect;
  RoleItem(this.roleName, this.desc, this.isSelect);
}

class RoleSelectContainer extends StatefulWidget {
  final Function(RoleItem) onSelect;
  const RoleSelectContainer({super.key, required this.onSelect});

  @override
  State<StatefulWidget> createState() => _RoleSelectContainerState();
}

class _RoleSelectContainerState extends State<RoleSelectContainer> {
  List<RoleItem> roleItems = [
    RoleItem("职工", "职工身份请选择此入口", true),
    RoleItem("非职工", "非职工身份请选择此入口", false),
    // RoleItem("企业", "企业代办人员选择此入口", false)
  ];
  RoleItem curItem = RoleItem("职工", "职工身份请选择此入口", true);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 319.px,
        height: 323.5.px,
        padding: EdgeInsets.all(27.px),
        decoration: BoxDecoration(
            image: DecorationImage(
                image:
                    AssetImage(ImageHelper.wrapAssets("role_background.png")))),
        child: Material(
          color: Colors.transparent,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                margin: EdgeInsets.only(bottom: 10.px),
                width: 265.px,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "身份选择入口",
                      style: TextStyle(
                        fontSize: 23.px,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Text(
                      "请根据您的身份选择入口",
                      style: TextStyle(
                        fontSize: 12.px,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 266.px,
                height: 118.5.px,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    ...roleItems.map((e) {
                      return GestureDetector(
                        onTap: () {
                          for (var v in roleItems) {
                            if (v.roleName == e.roleName) {
                              v.isSelect = true;
                            } else {
                              v.isSelect = false;
                            }
                          }
                          curItem = e;
                          setState(() {});
                        },
                        child: e.isSelect == true
                            ? Container(
                                width: 82.px,
                                height: 118.5.px,
                                margin: EdgeInsets.only(right: 10.px),
                                padding: EdgeInsets.only(
                                    left: 11.px,
                                    right: 11.px,
                                    top: 22.px,
                                    bottom: 22.px),
                                decoration: BoxDecoration(
                                    image: DecorationImage(
                                        fit: BoxFit.cover,
                                        image: AssetImage(
                                            ImageHelper.wrapAssets(
                                                "role_select.png")))),
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: [
                                    Text(
                                      e.roleName,
                                      style: TextStyle(
                                          fontSize: 14.px,
                                          fontWeight: FontWeight.w600),
                                    ),
                                    Text(e.desc,
                                        style: TextStyle(
                                            fontSize: 12.px,
                                            fontWeight: FontWeight.w400))
                                  ],
                                ),
                              )
                            : Container(
                                decoration: BoxDecoration(
                                    color: const Color.fromRGBO(0, 0, 0, 0.03),
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(8.px))),
                                width: 82.px,
                                height: 118.5.px,
                                padding: EdgeInsets.only(
                                    left: 11.px,
                                    right: 11.px,
                                    top: 22.px,
                                    bottom: 22.px),
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: [
                                    Text(
                                      e.roleName,
                                      style: TextStyle(
                                          fontSize: 14.px,
                                          fontWeight: FontWeight.w600),
                                    ),
                                    Text(e.desc,
                                        style: TextStyle(
                                            fontSize: 12.px,
                                            fontWeight: FontWeight.w400))
                                  ],
                                ),
                              ),
                      );
                    })
                  ],
                ),
              ),
              SizedBox(
                height: 25.px,
              ),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                  widget.onSelect(curItem);
                },
                child: BDHButtonGreen(
                  width: 225.px,
                  height: 45.px,
                  title: "进入",
                  borderRadius: 22.5.px,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
