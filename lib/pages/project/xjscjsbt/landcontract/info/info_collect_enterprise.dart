import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_steps.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_range_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_ocr_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/enterprise_info_result_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/info/info_collect_legal.dart';
import 'package:bdh_smart_agric_app/utils/dic_util.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class InfoCollectEnterprisePage extends StatefulWidget {
  final String title;
  final List<String>? steps;
  final int? stepIndex;
  final Partner? partner;
  final LandBaseInfo? landBaseInfo;

  const InfoCollectEnterprisePage({
    super.key,
    required this.title,
    this.steps,
    this.stepIndex,
    this.partner,
    this.landBaseInfo,
  });

  @override
  State<StatefulWidget> createState() => _InfoCollectEnterprisePageState();
}

class _InfoCollectEnterprisePageState extends State<InfoCollectEnterprisePage> {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  Map<String, dynamic> form = {};
  List<DictNode> partyTypeDics = [];

  @override
  void initState() {
    super.initState();
    loadDics();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(239, 241, 245, 1),
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Visibility(
                visible: widget.stepIndex != null,
                child: Container(
                  width: 375.px,
                  height: 66.px,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(15.px),
                          bottomRight: Radius.circular(15.px))),
                  child: BdhStepsHorizontal(
                    steps: widget.steps != null
                        ? widget.steps!
                            .map((e) => BdhStepsItemData(title: e))
                            .toList()
                        : [BdhStepsItemData(title: "默认")],
                    activeIndex: widget.stepIndex ?? 0,
                  ),
                )),
            SizedBox(
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  45.px -
                  (widget.stepIndex != null ? 86.px : 20.px),
              child: SingleChildScrollView(
                child: Form(
                    key: key,
                    autovalidateMode: AutovalidateMode.onUserInteraction,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 20.px,
                        ),
                        BdhOcrImagePicker(
                          item: FormItem(),
                          initialValue: widget.partner?.busiLicensePicUrl !=
                                  null
                              ? BDHFile(url: widget.partner?.busiLicensePicUrl)
                              : null,
                          icon: "upload_license.png",
                          onUpload: (v) {},
                          onSaved: (v) {
                            form["busiLicensePicUrl"] = v?.url;
                          },
                          validator: (v) {
                            if (v == null) {
                              return "营业执照不能为空!";
                            }
                            return null;
                          },
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 20.px),
                          padding: EdgeInsets.only(left: 10.px, right: 10.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            children: [
                              BdhTextInput(
                                initialValue: widget.partner?.partnerName,
                                item: FormItem(title: "组织名称", isRequired: true),
                                onSaved: (v) {
                                  form["partnerName"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "组织名称不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                initialValue: widget.partner?.partnerCode,
                                item: FormItem(title: "机构代码", isRequired: true),
                                onSaved: (v) {
                                  form["partnerCode"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "机构代码不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhDatePicker(
                                initialValue: widget.partner?.buildDate != null
                                    ? DateFormat('yyyy-MM-dd')
                                        .parse(widget.partner!.buildDate!)
                                    : null,
                                item: FormItem(title: "成立日期", isRequired: true),
                                onSaved: (v) {
                                  form["buildDate"] = (v != null
                                      ? DateFormat('yyyy-MM-dd').format(v)
                                      : null);
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "成立日期不能为空";
                                  }
                                  DateTime currentDate = DateTime.now();
                                  if (v.isAfter(currentDate)) {
                                    return "成立日期不能大于当前日期";
                                  }
                                  return null;
                                },
                              ),
                              BdhDateRangePicker(
                                initialValue: (widget.partner?.businessTerm !=
                                            null &&
                                        widget.partner?.businessTermEndDate !=
                                            null)
                                    ? DateTimeRange(
                                        start: DateFormat('yyyy-MM-dd').parse(
                                            widget.partner!.businessTerm!),
                                        end: DateFormat('yyyy-MM-dd').parse(
                                            widget
                                                .partner!.businessTermEndDate!))
                                    : null,
                                item: FormItem(title: "营业期限", isRequired: true),
                                onSaved: (v) {
                                  form["businessTerm"] =
                                      DateFormat('yyyy-MM-dd').format(v!.start);
                                  form["businessTermEndDate"] =
                                      DateFormat('yyyy-MM-dd').format(v.end);
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "营业期限不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item: FormItem(title: "住所", isRequired: true),
                                initialValue: widget.partner?.businessAddress,
                                onSaved: (v) {
                                  form["businessAddress"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "住所不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item:
                                    FormItem(title: "法定代表人", isRequired: true),
                                initialValue: widget.partner?.legalName,
                                onSaved: (v) {
                                  form["legalName"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "法定代表人不能为空";
                                  }
                                  return null;
                                },
                              ),
                              partyTypeDics.isNotEmpty
                                  ? BdhSingleDataPicker(
                                      initialValue: widget
                                                  .partner?.oppositePartyType !=
                                              null
                                          ? DictNode(
                                              name: DicUtil.dictCodeToName(
                                                  "${widget.partner?.oppositePartyType}",
                                                  partyTypeDics),
                                              code:
                                                  "${widget.partner?.oppositePartyType}")
                                          : null,
                                      item: FormItem(
                                          title: "类型",
                                          data: partyTypeDics,
                                          isRequired: true),
                                      onSaved: (v) {
                                        form["oppositePartyType"] =
                                            int.parse(v?.code ?? "0");
                                      },
                                    )
                                  : Container(),
                            ],
                          ),
                        ),
                      ],
                    )),
              ),
            ),
            GestureDetector(
              onTap: () {
                if (key.currentState!.validate()) {
                  key.currentState!.save();
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return InfoCollectLegalPersonPage(
                      partner: widget.partner,
                      landBaseInfo: widget.landBaseInfo,
                      title: "企业信息",
                      steps: widget.steps,
                      stepIndex: (widget.stepIndex ?? 0) + 1,
                      preData: form,
                    );
                  }));
                }
              },
              child: BDHButtonGreen(width: 347.px, height: 45.px, title: "下一步"),
            )
          ],
        );
      }),
    );
  }

  loadDics() {
    Future.wait([
      LandResponsitory.getDicByKey("opposite_party_type"),
    ]).then((list) {
      setState(() {
        partyTypeDics = list[0].data ?? [];
      });
    });
  }
}
