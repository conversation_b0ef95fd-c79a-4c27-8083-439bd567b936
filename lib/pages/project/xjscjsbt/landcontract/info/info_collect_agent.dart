import 'dart:convert';
import 'dart:developer' as developer;
import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_steps.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_org_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_vertify_image_picker.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/utils/dic_util.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class InfoCollectAgentPage extends StatefulWidget {
  final String title;
  final List<String>? steps;
  final int? stepIndex;
  final dynamic preData;

  const InfoCollectAgentPage(
      {super.key,
      required this.title,
      this.steps,
      this.stepIndex,
      this.preData});

  @override
  State<StatefulWidget> createState() => _InfoCollectAgentPageState();
}

class _InfoCollectAgentPageState extends State<InfoCollectAgentPage> {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  Map<String, dynamic> form = {};
  TextEditingController textEditingController1 = TextEditingController();
  TextEditingController textEditingController2 = TextEditingController();
  TextEditingController textEditingController3 = TextEditingController();
  TextEditingController textEditingController4 = TextEditingController();
  TextEditingController textEditingController5 = TextEditingController();

  LandBaseInfo? landBaseInfo;
  OrgTreeResult? treeResult;
  List<DictNode> nations = [];
  List<DictNode> sexs = [];
  List<DictNode> employees = [];
  List<DictNode> ifDics = [];

  bool isEmployee = true;

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(microseconds: 100), () {
      loadInfo();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(239, 241, 245, 1),
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Visibility(
                visible: widget.stepIndex != null,
                child: Container(
                  width: 375.px,
                  height: 66.px,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(15.px),
                          bottomRight: Radius.circular(15.px))),
                  child: BdhStepsHorizontal(
                    steps: widget.steps != null
                        ? widget.steps!
                            .map((e) => BdhStepsItemData(title: e))
                            .toList()
                        : [BdhStepsItemData(title: "默认")],
                    activeIndex: widget.stepIndex ?? 0,
                  ),
                )),
            Container(
              alignment: Alignment.center,
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  45.px -
                  (widget.stepIndex != null ? 86.px : 20.px),
              child: landBaseInfo != null
                  ? SingleChildScrollView(
                      child: Form(
                          key: key,
                          child: Column(
                            children: [
                              Container(
                                margin: EdgeInsets.only(top: 20.px),
                                padding:
                                    EdgeInsets.only(left: 10.px, right: 10.px),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(8.px))),
                                width: 347.px,
                                child: Column(
                                  children: [
                                    BdhTextInput(
                                      controller: textEditingController1,
                                      initialValue: landBaseInfo?.name,
                                      item: FormItem(
                                          title: "姓名",
                                          isRequired: true,
                                          isCanEdit: false),
                                      onSaved: (v) {
                                        form["name"] = v;
                                      },
                                      validator: (v) {
                                        if (v == null) {
                                          return "姓名不能为空";
                                        }
                                        return null;
                                      },
                                    ),
                                    BdhTextInput(
                                      controller: textEditingController2,
                                      initialValue: landBaseInfo?.idNumber,
                                      item: FormItem(
                                          title: "身份证",
                                          isRequired: true,
                                          isCanEdit: false),
                                      onSaved: (v) {
                                        form["idNumber"] = v;
                                      },
                                      validator: (v) {
                                        if (v == null) {
                                          return "身份证不能为空";
                                        }
                                        return null;
                                      },
                                    ),
                                    treeResult != null
                                        ? BdhOrgDataPicker(
                                            item: FormItem(
                                                title: "资格所在单位",
                                                data: treeResult?.data,
                                                isRequired: true),
                                            initialValue:
                                                landBaseInfo?.organizationNo !=
                                                        null
                                                    ? [
                                                        {
                                                          "orgCode": landBaseInfo
                                                              ?.organizationNo,
                                                          "orgName": landBaseInfo
                                                              ?.organizationName
                                                        }
                                                      ]
                                                    : null,
                                            validator: (v) {
                                              if (v == null) {
                                                return "资格所在单位";
                                              }
                                              return null;
                                            },
                                            onSaved: (v) {
                                              form["organizationNo"] =
                                                  (v as List).last["orgCode"];
                                              form["organizationName"] =
                                                  (v).last["orgName"];
                                            },
                                          )
                                        : Container(),
                                    BdhSingleDataPicker(
                                      onChange: (v) {
                                        if (v.code == "1") {
                                          setState(() {
                                            isEmployee = true;
                                          });
                                        } else {
                                          setState(() {
                                            isEmployee = false;
                                          });
                                        }
                                      },
                                      initialValue: landBaseInfo?.isEmployee !=
                                              null
                                          ? DictNode(
                                              name: DicUtil.dictCodeToName(
                                                  "${landBaseInfo!.isEmployee}",
                                                  ifDics),
                                              code:
                                                  "${landBaseInfo!.isEmployee}")
                                          : null,
                                      item: FormItem(title: "职工", data: ifDics),
                                      onSaved: (v) {
                                        form["isEmployee"] =
                                            int.parse(v?.code ?? "0");
                                      },
                                    ),
                                    isEmployee == true
                                        ? BdhTextInput(
                                            item: FormItem(
                                                title: "社会保险号码",
                                                isRequired: true),
                                            initialValue:
                                                landBaseInfo?.socialSn ??
                                                    landBaseInfo?.idNumber ??
                                                    "",
                                            onSaved: (v) {
                                              form["socialSn"] = v;
                                            },
                                            validator: (v) {
                                              if (v == null) {
                                                return "社会保险号码不能为空";
                                              }
                                              return null;
                                            },
                                          )
                                        : Container(),
                                    isEmployee == true
                                        ? BdhSingleDataPicker(
                                            initialValue: landBaseInfo
                                                        ?.isMilitiaman !=
                                                    null
                                                ? DictNode(
                                                    name: DicUtil.dictCodeToName(
                                                        "${landBaseInfo!.isMilitiaman}",
                                                        ifDics),
                                                    code:
                                                        "${landBaseInfo!.isMilitiaman}")
                                                : null,
                                            item: FormItem(
                                                title: "民兵", data: ifDics),
                                            onSaved: (v) {
                                              form["isMilitiaman"] =
                                                  int.parse(v?.code ?? "0");
                                            },
                                          )
                                        : Container(),
                                    BdhTextInput(
                                      controller: textEditingController3,
                                      initialValue: landBaseInfo?.domicilePlace,
                                      item: FormItem(
                                          title: "户籍所在地", isRequired: true),
                                      onSaved: (v) {
                                        form["domicilePlace"] = v;
                                      },
                                      validator: (v) {
                                        if (v == null) {
                                          return "户籍所在地不能为空";
                                        }
                                        return null;
                                      },
                                    ),
                                    BdhTextInput(
                                      controller: textEditingController4,
                                      initialValue:
                                          landBaseInfo?.issuingAuthority,
                                      item: FormItem(
                                          title: "签发机关", isRequired: true),
                                      onSaved: (v) {
                                        form["issuingAuthority"] = v;
                                      },
                                      validator: (v) {
                                        if (v == null) {
                                          return "签发机关不能为空";
                                        }
                                        return null;
                                      },
                                    ),
                                    BdhTextInput(
                                      controller: textEditingController5,
                                      item: FormItem(
                                          title: "有效期限", isRequired: true),
                                      initialValue: landBaseInfo?.validDate,
                                      onSaved: (v) {
                                        form["validDate"] = v;
                                      },
                                      validator: (v) {
                                        if (v == null || v == "") {
                                          return "有效期限不能为空";
                                        }
                                        return null;
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                margin: EdgeInsets.only(top: 20.px),
                                padding: EdgeInsets.all(15.px),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(8.px))),
                                width: 347.px,
                                child: Column(
                                  children: [
                                    const Row(
                                      children: [
                                        Text(
                                          "*",
                                          style: TextStyle(color: Colors.red),
                                        ),
                                        Text("上传本人照片及身份证正反面")
                                      ],
                                    ),
                                    SizedBox(
                                      height: 15.px,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        BdhVertifyImagePicker(
                                          item: FormItem(title: "本人照片"),
                                          initialValue: landBaseInfo
                                                      ?.photoPath !=
                                                  null
                                              ? BDHFile(
                                                  url: landBaseInfo?.photoPath)
                                              : null,
                                          onSaved: (v) {
                                            form["photoPath"] = v?.url;
                                          },
                                          validator: (v) {
                                            if (v == null) {
                                              return "本人照片不能为空";
                                            }
                                            return null;
                                          },
                                        ),
                                        BdhVertifyImagePicker(
                                          onUpload: (v) {
                                            //法大大校验
                                            UserInfo? userInfo =
                                                StorageUtil.userInfo();
                                            LandResponsitory.ocrIdCard({
                                              "picUrl": v.url,
                                              "accoundId": userInfo?.data?.id,
                                              "systemId": "systemlandcontract"
                                            }).then((res) {
                                              if (res.code == 0) {
                                                if (res.data["姓名"] != null &&
                                                    res.data["公民身份号码"] !=
                                                        null) {
                                                  textEditingController1.text =
                                                      res.data["姓名"]["words"];
                                                  textEditingController2.text =
                                                      res.data["公民身份号码"]
                                                          ["words"];
                                                  textEditingController3.text =
                                                      res.data["住址"]["words"];

                                                  form["address"] =
                                                      res.data["住址"]["words"];
                                                  form["sex"] = dictNameToCode(
                                                      res.data["性别"]["words"],
                                                      sexs);
                                                  form["nation"] =
                                                      dictNameToCode(
                                                          res.data["民族"]
                                                              ["words"],
                                                          nations);
                                                } else {
                                                  showToast("身份证正面识别失败");
                                                }
                                              } else {
                                                // showToast("身份证识别失败");
                                              }
                                            });
                                          },
                                          item: FormItem(title: "身份证正面"),
                                          initialValue: landBaseInfo?.idFront !=
                                                  null
                                              ? BDHFile(
                                                  url: landBaseInfo?.idFront)
                                              : null,
                                          onSaved: (v) {
                                            form["idFront"] = v?.url;
                                          },
                                          validator: (v) {
                                            if (v == null) {
                                              return "身份证正面不能为空";
                                            }
                                            return null;
                                          },
                                        ),
                                        BdhVertifyImagePicker(
                                          onUpload: (v) {
                                            //法大大校验
                                            UserInfo? userInfo =
                                                StorageUtil.userInfo();
                                            LandResponsitory.ocrIdCard({
                                              "picUrl": v.url,
                                              "accoundId": userInfo?.data?.id,
                                              "systemId": "systemlandcontract"
                                            }).then((res) {
                                              if (res.code == 0) {
                                                if (res.data["签发机关"] != null &&
                                                    res.data["签发日期"] != null) {
                                                  textEditingController4.text =
                                                      res.data["签发机关"]["words"];
                                                  textEditingController5.text =
                                                      "${res.data["签发日期"]["words"]}-${res.data["失效日期"]["words"]}";
                                                } else {
                                                  showToast("身份证反面识别失败");
                                                }
                                              } else {
                                                // showToast("身份证反面识别失败");
                                              }
                                            });
                                          },
                                          item: FormItem(title: "身份证反面"),
                                          initialValue:
                                              landBaseInfo?.idBack != null
                                                  ? BDHFile(
                                                      url: landBaseInfo?.idBack)
                                                  : null,
                                          onSaved: (v) {
                                            form["idBack"] = v?.url;
                                          },
                                          validator: (v) {
                                            if (v == null) {
                                              return "身份证反面不能为空";
                                            }
                                            return null;
                                          },
                                        )
                                      ],
                                    )
                                  ],
                                ),
                              ),
                              Container(
                                margin:
                                    EdgeInsets.only(top: 20.px, bottom: 20.px),
                                padding: EdgeInsets.all(15.px),
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(8.px))),
                                width: 347.px,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text("上传身份证复印件"),
                                    SizedBox(
                                      height: 15.px,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        BdhVertifyImagePicker(
                                          item: FormItem(title: "身份证复印件"),
                                          initialValue:
                                              landBaseInfo?.idPictureUrl != null
                                                  ? BDHFile(
                                                      url: landBaseInfo
                                                          ?.idPictureUrl)
                                                  : null,
                                          onSaved: (v) {
                                            form["idPictureUrl"] = v?.url;
                                          },
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              )
                            ],
                          )),
                    )
                  : Container(),
            ),
            GestureDetector(
              onTap: () {
                if (key.currentState!.validate()) {
                  key.currentState!.save();

                  Map<String, dynamic> enterpriseForm = {};
                  enterpriseForm["partner"] = widget.preData;
                  enterpriseForm["farmers"] = form;
                  developer.log(jsonEncode(enterpriseForm));
                  TDToast.showLoadingWithoutText(
                      context: context, preventTap: true);
                  LandResponsitory.insertEnterpriseInfo(enterpriseForm)
                      .then((res) {
                    TDToast.dismissLoading();
                    bus.emit("refreshLandInfo");
                    if (res.code == 0) {
                      Navigator.of(context).popUntil((pre) {
                        return pre.settings.name == RouteName.xjLandContract;
                      });
                    }
                  });
                }
              },
              child: BDHButtonGreen(width: 347.px, height: 45.px, title: "保存"),
            )
          ],
        );
      }),
    );
  }

  loadInfo() {
    Future.wait([
      LandResponsitory.getResidentInfo(),
      LandResponsitory.getOrgData(),
      LandResponsitory.getDicByKey("nation"),
      LandResponsitory.getDicByKey("sex"),
      LandResponsitory.getDicByKey("is_employee"),
      LandResponsitory.getDicByKey("pub_if")
    ]).then((list) {
      setState(() {
        landBaseInfo = (list[0] as LandBaseInfoResult).data;
        treeResult = (list[1] as OrgTreeResult);
        nations = (list[2] as DictList).data ?? [];
        sexs = (list[3] as DictList).data ?? [];
        employees = (list[4] as DictList).data ?? [];
        ifDics = (list[5] as DictList).data ?? [];
        textEditingController1.text = landBaseInfo?.name ?? "";
        textEditingController2.text = landBaseInfo?.idNumber ?? "";
        textEditingController3.text = landBaseInfo?.domicilePlace ?? "";
        textEditingController4.text = landBaseInfo?.issuingAuthority ?? "";
        textEditingController5.text = landBaseInfo?.validDate ?? "";
      });
    });
  }

  String dictNameToCode(String name, List<DictNode> nodes) {
    var code = "xxx";
    for (DictNode node in nodes) {
      if (node.name == name) {
        code = node.code ?? "";
      }
    }
    return code;
  }
}
