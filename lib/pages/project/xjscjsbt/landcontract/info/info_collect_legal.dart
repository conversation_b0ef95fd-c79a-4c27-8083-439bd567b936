import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_steps.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_ocr_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_vertify_image_picker.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/enterprise_info_result_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/info/info_collect_agent.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/realname/real_name_auth_page.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class InfoCollectLegalPersonPage extends StatefulWidget {
  final String title;
  final List<String>? steps;
  final int? stepIndex;
  final dynamic preData;
  final Partner? partner;
  final LandBaseInfo? landBaseInfo;

  const InfoCollectLegalPersonPage(
      {super.key,
      required this.title,
      this.steps,
      this.stepIndex,
      this.preData,
      this.partner,
      this.landBaseInfo});

  @override
  State<StatefulWidget> createState() => _InfoCollectLegalPersonPageState();
}

class _InfoCollectLegalPersonPageState
    extends State<InfoCollectLegalPersonPage> {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  Map<String, dynamic> form = {};
  TextEditingController textEditingController1 = TextEditingController();
  TextEditingController textEditingController2 = TextEditingController();
  TextEditingController textEditingController3 = TextEditingController();
  TextEditingController textEditingController4 = TextEditingController();
  TextEditingController textEditingController5 = TextEditingController();

  @override
  void initState() {
    super.initState();

    textEditingController1.text = widget.partner?.legalName ?? "";
    textEditingController2.text = widget.partner?.legalIdNumber ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(239, 241, 245, 1),
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Visibility(
                visible: widget.stepIndex != null,
                child: Container(
                  width: 375.px,
                  height: 66.px,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(15.px),
                          bottomRight: Radius.circular(15.px))),
                  child: BdhStepsHorizontal(
                    steps: widget.steps != null
                        ? widget.steps!
                            .map((e) => BdhStepsItemData(title: e))
                            .toList()
                        : [BdhStepsItemData(title: "默认")],
                    activeIndex: widget.stepIndex ?? 0,
                  ),
                )),
            SizedBox(
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  45.px -
                  (widget.stepIndex != null ? 86.px : 20.px),
              child: SingleChildScrollView(
                child: Form(
                    key: key,
                    child: Column(
                      children: [
                        SizedBox(
                          height: 20.px,
                        ),
                        BdhOcrImagePicker(
                          item: FormItem(),
                          initialValue: widget.partner?.idFront != null
                              ? BDHFile(url: widget.partner?.idFront)
                              : null,
                          icon: "upload_id.png",
                          onUpload: (v) {
                            //法大大校验
                            UserInfo? userInfo = StorageUtil.userInfo();
                            LandResponsitory.ocrIdCard({
                              "picUrl": v.url,
                              "accoundId": userInfo?.data?.id,
                              "systemId": "systemlandcontract"
                            }).then((res) {
                              if (res.code == 0) {
                                if (res.data["姓名"] != null &&
                                    res.data["公民身份号码"] != null) {
                                  textEditingController1.text =
                                      res.data["姓名"]["words"];
                                  textEditingController2.text =
                                      res.data["公民身份号码"]["words"];
                                } else {
                                  showToast("身份证正面识别失败");
                                }
                              } else {
                                // showToast("身份证识别失败");
                              }
                            });
                          },
                          onSaved: (v) {
                            form["idFront"] = v?.url;
                          },
                          validator: (v) {
                            if (v == null) {
                              return "身份证不能为空!";
                            }
                            return null;
                          },
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 20.px),
                          padding: EdgeInsets.only(left: 10.px, right: 10.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            children: [
                              BdhTextInput(
                                controller: textEditingController1,
                                initialValue: widget.partner?.legalName,
                                item: FormItem(title: "姓名", isRequired: true),
                                onSaved: (v) {
                                  form["legalName"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "姓名不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                controller: textEditingController2,
                                item: FormItem(title: "身份证号", isRequired: true),
                                initialValue: widget.partner?.legalIdNumber,
                                onSaved: (v) {
                                  form["legalIdNumber"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "身份证不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item: FormItem(title: "手机号", isRequired: true),
                                // initialValue: widget.partner?.legalTelephone,
                                onSaved: (v) {
                                  form["legalTelephone"] = v;
                                },
                                validator: (v) {
                                  if (v == null || v.isEmpty) {
                                    return "手机号不能为空";
                                  }
                                  if (v.length != 11) {
                                    return "手机号必须是11位";
                                  }
                                  if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(v)) {
                                    return "请输入有效的手机号";
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 20.px, bottom: 20.px),
                          padding: EdgeInsets.all(15.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text("上传身份证复印件"),
                              SizedBox(
                                height: 15.px,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  BdhVertifyImagePicker(
                                    item: FormItem(title: "身份证复印件"),
                                    initialValue: widget
                                                .partner?.idPictureUrl !=
                                            null
                                        ? BDHFile(
                                            url: widget.partner?.idPictureUrl)
                                        : null,
                                    onSaved: (v) {
                                      form["idPictureUrl"] = v?.url;
                                    },
                                  ),
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    )),
              ),
            ),
            GestureDetector(
              onTap: () {
                if (key.currentState!.validate()) {
                  key.currentState!.save();
                  if (widget.steps?.length == 2) {
                    if (widget.landBaseInfo != null) {
                      TDToast.showLoadingWithoutText(
                          context: context, preventTap: true);

                      Map<String, dynamic> enterpriseForm = {};
                      enterpriseForm["partner"] = {...widget.preData, ...form};
                      enterpriseForm["farmers"] = widget.landBaseInfo!.toJson();
                      LandResponsitory.insertEnterpriseInfo(enterpriseForm)
                          .then((rs) {
                        TDToast.dismissLoading();
                        Navigator.of(context).popUntil((pre) {
                          return pre.settings.name == RouteName.xjLandContract;
                        });
                      });
                    }
                  } else if (widget.steps?.length == 3) {
                    Navigator.of(context)
                        .push(CupertinoPageRoute(builder: (ctx) {
                      return InfoCollectAgentPage(
                          preData: {...widget.preData, ...form},
                          title: widget.title,
                          steps: widget.steps,
                          stepIndex: (widget.stepIndex ?? 0) + 1);
                    }));
                  } else if (widget.steps?.length == 4) {
                    Navigator.of(context)
                        .push(CupertinoPageRoute(builder: (ctx) {
                      return RealNameAuthPage(
                          title: widget.title,
                          steps: widget.steps,
                          bridgeData: {...widget.preData, ...form},
                          stepIndex: (widget.stepIndex ?? 0) + 1);
                    }));
                  }
                }
              },
              child: BDHButtonGreen(
                  width: 347.px,
                  height: 45.px,
                  title: widget.steps?.length == 2 ? "保存" : "下一步"),
            )
          ],
        );
      }),
    );
  }
}
