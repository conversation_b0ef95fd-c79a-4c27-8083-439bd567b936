import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_range_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_vertify_image_picker.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/enterprise_info_result_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/dic_util.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class InfoCollectEnterpriseSavePage extends StatefulWidget {
  final String title;
  final List<String>? steps;
  final int? stepIndex;
  final Partner? partner;

  const InfoCollectEnterpriseSavePage({
    super.key,
    required this.title,
    this.steps,
    this.stepIndex,
    this.partner,
  });

  @override
  State<StatefulWidget> createState() => _InfoCollectEnterpriseSavePageState();
}

class _InfoCollectEnterpriseSavePageState
    extends State<InfoCollectEnterpriseSavePage> {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  Map<String, dynamic> form = {};
  List<DictNode> partyTypeDics = [];

  @override
  void initState() {
    super.initState();
    loadDics();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(239, 241, 245, 1),
      appBar: AppBar(
        title: const Text("基本信息-企业"),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            SizedBox(
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  65.px,
              child: SingleChildScrollView(
                child: Form(
                    key: key,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SizedBox(
                          width: 375.px,
                          height: 20.px,
                        ),
                        Container(
                          padding: EdgeInsets.only(left: 10.px, right: 10.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            children: [
                              BdhTextInput(
                                initialValue: widget.partner?.partnerName,
                                item: FormItem(title: "组织名称", isRequired: true),
                                onSaved: (v) {
                                  form["partnerName"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "组织名称不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                initialValue: widget.partner?.partnerCode,
                                item: FormItem(title: "机构代码", isRequired: true),
                                onSaved: (v) {
                                  form["partnerCode"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "机构代码不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhDatePicker(
                                initialValue: widget.partner?.buildDate != null
                                    ? DateFormat('yyyy-MM-dd')
                                        .parse(widget.partner!.buildDate!)
                                    : null,
                                item: FormItem(title: "成立日期", isRequired: true),
                                onSaved: (v) {
                                  form["buildDate"] = (v != null
                                      ? DateFormat('yyyy-MM-dd').format(v)
                                      : null);
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "成立日期不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhDateRangePicker(
                                initialValue: (widget.partner?.businessTerm !=
                                            null &&
                                        widget.partner?.businessTermEndDate !=
                                            null)
                                    ? DateTimeRange(
                                        start: DateFormat('yyyy-MM-dd').parse(
                                            widget.partner!.businessTerm!),
                                        end: DateFormat('yyyy-MM-dd').parse(
                                            widget
                                                .partner!.businessTermEndDate!))
                                    : null,
                                item: FormItem(title: "营业期限", isRequired: true),
                                onSaved: (v) {
                                  form["businessTerm"] =
                                      DateFormat('yyyy-MM-dd').format(v!.start);
                                  form["businessTermEndDate"] =
                                      DateFormat('yyyy-MM-dd').format(v.end);
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "营业期限不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item: FormItem(title: "住所", isRequired: true),
                                initialValue: widget.partner?.businessAddress,
                                onSaved: (v) {
                                  form["businessAddress"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "住所不能为空";
                                  }
                                  return null;
                                },
                              ),
                              partyTypeDics.isNotEmpty
                                  ? BdhSingleDataPicker(
                                      initialValue: widget
                                                  .partner?.oppositePartyType !=
                                              null
                                          ? DictNode(
                                              name: DicUtil.dictCodeToName(
                                                  "${widget.partner?.oppositePartyType}",
                                                  partyTypeDics),
                                              code:
                                                  "${widget.partner?.oppositePartyType}")
                                          : null,
                                      item: FormItem(
                                          title: "类型",
                                          data: partyTypeDics,
                                          isRequired: true),
                                      onSaved: (v) {
                                        form["oppositePartyType"] =
                                            int.parse(v?.code ?? "0");
                                      },
                                    )
                                  : Container(),
                            ],
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          margin: EdgeInsets.only(
                              top: 10.px, bottom: 10.px, left: 17.px),
                          child: Text(
                            "法人信息",
                            style: TextStyle(
                                fontSize: 12.px, fontWeight: FontWeight.w400),
                          ),
                        ),
                        Container(
                            padding: EdgeInsets.only(left: 10.px, right: 10.px),
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(8.px))),
                            width: 347.px,
                            child: Column(children: [
                              BdhTextInput(
                                initialValue: widget.partner?.legalName,
                                item: FormItem(title: "姓名", isRequired: true),
                                onSaved: (v) {
                                  form["legalName"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "姓名不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item: FormItem(
                                    title: "身份证号",
                                    isRequired: true,
                                    isCanEdit: false),
                                initialValue: widget.partner?.legalIdNumber,
                                onSaved: (v) {
                                  form["legalIdNumber"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "身份证不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item: FormItem(title: "手机号", isRequired: true),
                                initialValue: widget.partner?.legalTelephone,
                                onSaved: (v) {
                                  form["legalTelephone"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "手机号不能为空";
                                  }
                                  return null;
                                },
                              ),
                            ])),
                        Container(
                          alignment: Alignment.centerLeft,
                          margin: EdgeInsets.only(
                              top: 10.px, bottom: 10.px, left: 17.px),
                          child: Text(
                            "附件",
                            style: TextStyle(
                                fontSize: 12.px, fontWeight: FontWeight.w400),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(bottom: 20.px),
                          padding: EdgeInsets.all(15.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text("企业营业执照"),
                              SizedBox(
                                height: 15.px,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  BdhVertifyImagePicker(
                                    item: FormItem(title: "企业营业执照"),
                                    initialValue:
                                        widget.partner?.busiLicensePicUrl !=
                                                null
                                            ? BDHFile(
                                                url: widget
                                                    .partner?.busiLicensePicUrl)
                                            : null,
                                    onSaved: (v) {
                                      form["busiLicensePicUrl"] = v?.url;
                                    },
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(bottom: 20.px),
                          padding: EdgeInsets.all(15.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text("法人身份证正面"),
                              SizedBox(
                                height: 15.px,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  BdhVertifyImagePicker(
                                    item: FormItem(title: "法人身份证正面"),
                                    initialValue: widget.partner?.idFront !=
                                            null
                                        ? BDHFile(url: widget.partner?.idFront)
                                        : null,
                                    onSaved: (v) {
                                      form["idFront"] = v?.url;
                                    },
                                  ),
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    )),
              ),
            ),
            GestureDetector(
              onTap: () {
                if (key.currentState!.validate()) {
                  key.currentState!.save();
                  TDToast.showLoadingWithoutText(
                      context: context, preventTap: true);
                  form["partnerId"] = widget.partner?.partnerId;
                  LandResponsitory.updateEnterpriseInfo(form).then((rs) {
                    bus.emit("refreshLandInfo");
                    TDToast.dismissLoading();
                    Navigator.of(context).popUntil((pre) {
                      return pre.settings.name == RouteName.xjLandContract;
                    });
                  });
                }
              },
              child: BDHButtonGreen(width: 347.px, height: 45.px, title: "保存"),
            )
          ],
        );
      }),
    );
  }

  loadDics() {
    Future.wait([
      LandResponsitory.getDicByKey("opposite_party_type"),
    ]).then((list) {
      setState(() {
        partyTypeDics = list[0].data ?? [];
      });
    });
  }
}
