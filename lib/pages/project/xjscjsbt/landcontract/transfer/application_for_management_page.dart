import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/transfer_application_result_model.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/transfer/transfer_management_contract_page.dart';
import 'package:bdh_smart_agric_app/utils/dic_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class ApplicationForManagementPage extends StatefulWidget {
  const ApplicationForManagementPage({super.key});

  @override
  State<StatefulWidget> createState() => _ApplicationForManagementPageState();
}

class _ApplicationForManagementPageState
    extends State<ApplicationForManagementPage> with TickerProviderStateMixin {
  List<TransferApplicationItem> items = [];

  int curentAprovalStatusNo = 0;

  DictNode? year;
  DictNode? contractType;

  List<DictNode> yearDics = [];
  List<DictNode> contractTypeDics = [];
  List<DictNode> transTypeDics = [];
  List<DictNode> aprovalTypeDics = [];
  bool isLoading = false;
  late TabController tabController1;

  @override
  void initState() {
    super.initState();
    tabController1 = TabController(length: 3, vsync: this);
    loadDics();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 2),
      appBar: AppBar(
        title: const Text("身份地流转"),
        actions: [
          GestureDetector(
            onTap: () {
              TDPicker.showMultiPicker(context, onConfirm: (data) {
                setState(() {
                  year = yearDics[data.first];
                });
                setState(() {
                  isLoading = true;
                });
                loadData();
                Navigator.of(context).pop();
              }, data: [
                yearDics.map((e) {
                  return e.name!;
                }).toList()
              ]);
            },
            child: Text(year?.name ?? "请选择年份"),
          ),
          SizedBox(
            width: 15.px,
          )
        ],
        bottom: PreferredSize(
            preferredSize: Size(375.px, 44.px),
            child: TDTabBar(
                indicatorColor: const Color.fromRGBO(10, 174, 108, 1),
                controller: tabController1,
                labelColor: const Color.fromRGBO(10, 174, 108, 1),
                dividerColor: Colors.transparent,
                showIndicator: true,
                onTap: (idx) {
                  setState(() {
                    curentAprovalStatusNo = idx;
                    isLoading = true;
                  });

                  loadData();
                },
                tabs: const [
                  TDTab(
                    text: "待审核",
                  ),
                  TDTab(
                    text: "审核通过",
                  ),
                  TDTab(
                    text: "审核退回",
                  )
                ])),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            SizedBox(
              width: 375.px,
            ),
            Container(
              margin: EdgeInsets.only(top: 10.px),
              width: 351.px,
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  65.px,
              child: isLoading
                  ? const ViewStateBusyWidget()
                  : (items.isEmpty
                      ? const BdhEmptyView()
                      : ListView.builder(
                          itemCount: items.length,
                          itemBuilder: (ctx, idx) {
                            return ApplyItemView(
                              item: items[idx],
                              transTypeDics: transTypeDics,
                              aprovalTypeDics: aprovalTypeDics,
                            );
                          })),
            ),
            GestureDetector(
              onTap: () {
                Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                  return const TransferManagementContractPage(
                    title: "经营权流转申请",
                  );
                }));
              },
              child: BDHButtonGreen(width: 347.px, height: 45.px, title: "新增"),
            )
          ],
        );
      }),
    );
  }

  loadDics() {
    setState(() {
      isLoading = true;
    });
    Future.wait([
      LandResponsitory.getDicByKey("year_cd"),
      LandResponsitory.getDicByKey("aproval_status_no"),
      LandResponsitory.getDicByKey("trans_type"),
    ]).then((list) {
      yearDics = list[0].data ?? [];
      year = yearDics.first;
      aprovalTypeDics = list[1].data ?? [];
      transTypeDics = list[2].data ?? [];

      loadData();
    });
  }

  loadData() {
    LandResponsitory.queryMyTransfer({"year": year?.code}).then((res) {
      setState(() {
        isLoading = false;
        items = (res.data ?? []).where((e) {
          return e.approvalStatusNo == curentAprovalStatusNo;
        }).toList();
      });
    });
  }
}

class ApplyItemView extends StatelessWidget {
  final TransferApplicationItem item;
  final List<DictNode> transTypeDics;
  final List<DictNode> aprovalTypeDics;
  const ApplyItemView(
      {super.key,
      required this.item,
      required this.transTypeDics,
      required this.aprovalTypeDics});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 351.px,
      height: 128.px,
      margin: EdgeInsets.only(bottom: 10.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.all(15.px),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.partnerName ?? item.farmerName ?? "",
                  style:
                      TextStyle(fontSize: 16.px, fontWeight: FontWeight.w600),
                ),
                Row(
                  children: [
                    Text(item.farmerPhone ?? ""),
                    SizedBox(
                      width: 10.px,
                    ),
                    Tag(
                        name: item.partnerCode == null
                            ? (item.isEmployee == 1 ? "职工" : "非职工")
                            : "企业",
                        color: item.partnerCode == null
                            ? const Color.fromRGBO(51, 121, 250, 1)
                            : const Color.fromRGBO(255, 147, 6, 1)),
                    SizedBox(
                      width: 10.px,
                    ),
                    Tag(
                        name: DicUtil.dictCodeToName(
                            "${item.transType}", transTypeDics),
                        color: const Color.fromRGBO(10, 174, 108, 1))
                  ],
                ),
                SizedBox(
                  height: 10.px,
                ),
                Row(
                  children: [
                    Text(
                      "转包面积",
                      style: TextStyle(
                          color: const Color.fromRGBO(41, 41, 52, 0.6),
                          fontSize: 12.px,
                          fontWeight: FontWeight.w500),
                    ),
                    SizedBox(
                      width: 10.px,
                    ),
                    Text("${item.chargeArea ?? 0}",
                        style: TextStyle(
                            color: const Color.fromRGBO(41, 41, 52, 1),
                            fontSize: 12.px,
                            fontWeight: FontWeight.w500))
                  ],
                ),
                SizedBox(
                  height: 5.px,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Text(
                          "转包金额",
                          style: TextStyle(
                              color: const Color.fromRGBO(41, 41, 52, 0.6),
                              fontSize: 12.px,
                              fontWeight: FontWeight.w500),
                        ),
                        SizedBox(
                          width: 10.px,
                        ),
                        Text("${item.totalFee ?? ""}",
                            style: TextStyle(
                                color: const Color.fromRGBO(41, 41, 52, 1),
                                fontSize: 12.px,
                                fontWeight: FontWeight.w500))
                      ],
                    ),
                    Text(
                        "${item.transStartDate!.split(" ").first}-${item.transEndDate!.split(" ").first}",
                        style: TextStyle(
                            color: const Color.fromRGBO(44, 44, 52, 0.6),
                            fontSize: 12.px,
                            fontWeight: FontWeight.w400))
                  ],
                )
              ],
            ),
          ),
          Positioned(
              top: 0,
              right: 0,
              child: StatusTag(
                  name: DicUtil.dictCodeToName(
                      "${item.approvalStatusNo}", aprovalTypeDics),
                  color: const Color.fromRGBO(243, 84, 66, 1)))
        ],
      ),
    );
  }
}

class StatusTag extends StatelessWidget {
  final String name;
  final Color color;
  const StatusTag({super.key, required this.name, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.5.px),
      alignment: Alignment.center,
      decoration: BoxDecoration(
          color: Color.fromRGBO(color.red, color.green, color.blue, 0.15),
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(8.px),
              topRight: Radius.circular(8.px))),
      child: Text(
        name,
        style: TextStyle(
            color: color, fontSize: 12.px, fontWeight: FontWeight.w600),
      ),
    );
  }
}

class Tag extends StatelessWidget {
  final String name;
  final Color color;
  const Tag({super.key, required this.name, required this.color});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.only(left: 3.px, right: 3.px),
      decoration: BoxDecoration(
          color: Color.fromRGBO(color.red, color.green, color.blue, 0.15),
          borderRadius: BorderRadius.all(Radius.circular(2.px))),
      child: Text(
        name,
        style: TextStyle(
            color: color, fontSize: 12.px, fontWeight: FontWeight.w400),
      ),
    );
  }
}
