import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_steps.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input.dart';
import 'package:bdh_smart_agric_app/main.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/query_my_contract_result_model.dart';
import 'package:bdh_smart_agric_app/model/query_transfer_config_result_model.dart';
import 'package:bdh_smart_agric_app/model/transfer_application_result_model.dart';
import 'package:bdh_smart_agric_app/utils/dic_util.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:decimal/decimal.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

//经营权流转申请新增
class TransferManageInfoPage extends StatefulWidget {
  final String title;
  final List<String>? steps;
  final int? stepIndex;
  final TransferApplicationItem? applyInfo;
  final num contractId;
  final List<String> landNos;
  final num? toFarmerId;
  final num? toPartnerId;
  final List<ContractDetail> landList;
  final QueryTransferConfigResult? config;

  const TransferManageInfoPage({
    super.key,
    required this.title,
    this.steps,
    this.stepIndex,
    this.applyInfo,
    required this.contractId,
    required this.landNos,
    this.toFarmerId,
    this.toPartnerId,
    required this.landList,
    this.config,
  });

  @override
  State<StatefulWidget> createState() => _TransferManageInfoPageState();
}

class _TransferManageInfoPageState extends State<TransferManageInfoPage>
    with RouteAware {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  Map<String, dynamic> form = {};
  List<DictNode> transTypeDics = [];
  List<DictNode> payTypeDics = [];
  ScrollController scrollController = ScrollController();
  num totalChargeArea = 0;
  Decimal totalPrice = Decimal.fromInt(0);
  num breachAmount = 0;
  num breachProprotion = 0;
  TextEditingController breachAmountController = TextEditingController();
  TextEditingController breachProportionController = TextEditingController();
  TextEditingController priceUnitController = TextEditingController();

  @override
  void initState() {
    super.initState();
    totalChargeArea =
        widget.landList.fold(0.0, (sum, land) => sum + (land.chargeArea ?? 0));
    breachAmountController.text = "${widget.config?.data?.breachAmount ?? 0}";
    breachProportionController.text = "${widget.config?.data?.lateFeePer ?? 0}";
    priceUnitController.text = "${widget.config?.data?.transUnitPrice ?? 0}";
    totalPrice =
        Decimal.parse("${(widget.config?.data?.transUnitPrice ?? 0)}") *
            Decimal.parse("$totalChargeArea");
    Future.delayed(const Duration(microseconds: 100), () {
      loadOrg();
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context)!);
  }

  @override
  void dispose() {
    routeObserver.unsubscribe(this);
    super.dispose();
  }

  // 当页面进入屏幕时触发
  @override
  void didPush() {}
  // 当页面离开屏幕时触发
  @override
  void didPopNext() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(239, 241, 245, 1),
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Visibility(
                visible: widget.stepIndex != null,
                child: Container(
                  width: 375.px,
                  height: 66.px,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(15.px),
                          bottomRight: Radius.circular(15.px))),
                  child: BdhStepsHorizontal(
                    steps: widget.steps != null
                        ? widget.steps!
                            .map((e) => BdhStepsItemData(title: e))
                            .toList()
                        : [BdhStepsItemData(title: "默认")],
                    activeIndex: widget.stepIndex ?? 0,
                  ),
                )),
            Container(
              alignment: Alignment.center,
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  45.px -
                  (widget.stepIndex != null ? 86.px : 20.px),
              child: SingleChildScrollView(
                  child: Column(
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 20.px),
                    padding: EdgeInsets.all(20.px),
                    width: 347.px,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(Radius.circular(8.px))),
                    child: Column(children: [
                      ...widget.landList.map((land) {
                        return Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('地块编号',
                                    style: TextStyle(
                                        fontFamily: "PingFang SC",
                                        fontWeight: FontWeight.w500,
                                        fontSize: 16.px)),
                                Container(height: 10.px),
                                Text(land.plotNo ?? '暂无编号',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14.px)),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text('地块面积',
                                    style: TextStyle(
                                        fontFamily: "PingFang SC",
                                        fontWeight: FontWeight.w500,
                                        fontSize: 16.px)),
                                Container(height: 10.px),
                                Text('${land.chargeArea}',
                                    style: TextStyle(
                                        fontWeight: FontWeight.w400,
                                        fontSize: 14.px)),
                              ],
                            ),
                            const Divider(),
                          ],
                        );
                      }),
                      Container(height: 20.px),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('总面积',
                              style: TextStyle(
                                  fontFamily: "PingFang SC",
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16.px)),
                          Container(height: 10.px),
                          Text('$totalChargeArea',
                              style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 16.px)),
                        ],
                      ),
                      Container(height: 20.px),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text('总价格',
                              style: TextStyle(
                                  fontFamily: "PingFang SC",
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16.px)),
                          Container(height: 10.px),
                          Text(totalPrice.toStringAsFixed(2),
                              style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  fontSize: 16.px)),
                        ],
                      )
                    ]),
                  ),
                  Form(
                      key: key,
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      child: Column(
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 20.px),
                            padding: EdgeInsets.only(left: 10.px, right: 10.px),
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(8.px))),
                            width: 347.px,
                            child: Column(
                              children: [
                                transTypeDics.isEmpty
                                    ? Container()
                                    : BdhSingleDataPicker(
                                        initialValue: widget
                                                    .config?.data?.transType !=
                                                null
                                            ? DictNode(
                                                name: DicUtil.dictCodeToName(
                                                    "${widget.config!.data!.transType!}",
                                                    transTypeDics),
                                                code:
                                                    "${widget.config!.data!.transType!}")
                                            : null,
                                        item: FormItem(
                                            title: "流转方式",
                                            data: transTypeDics,
                                            isRequired: true),
                                        onSaved: (v) {
                                          form["transType"] =
                                              int.parse(v?.code ?? "0");
                                        },
                                        validator: (v) {
                                          if (v == null) {
                                            return "流转方式不能为空";
                                          }
                                          return null;
                                        },
                                      ),
                                BdhDatePicker(
                                  item:
                                      FormItem(title: "流转开始", isRequired: true),
                                  initialValue:
                                      widget.config?.data?.transStartDateFull !=
                                              null
                                          ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                              .parse(widget.config!.data!
                                                  .transStartDateFull!)
                                          : null,
                                  onSaved: (v) {
                                    form["transStartDate"] = v != null
                                        ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                            .format(v)
                                        : null;
                                  },
                                  validator: (v) {
                                    if (v == null) {
                                      return "流转开始时间不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhDatePicker(
                                  item:
                                      FormItem(title: "流转结束", isRequired: true),
                                  initialValue:
                                      widget.config?.data?.transEndDateFull !=
                                              null
                                          ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                              .parse(widget.config!.data!
                                                  .transEndDateFull!)
                                          : null,
                                  onSaved: (v) {
                                    form["transEndDate"] = v != null
                                        ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                            .format(v)
                                        : null;
                                  },
                                  validator: (v) {
                                    if (v == null) {
                                      return "流转结束时间不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhDatePicker(
                                  item:
                                      FormItem(title: "交付时间", isRequired: true),
                                  initialValue:
                                      widget.config?.data?.deliveryDateFull !=
                                              null
                                          ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                              .parse(widget.config!.data!
                                                  .deliveryDateFull!)
                                          : null,
                                  onSaved: (v) {
                                    form["deliveryDate"] = v != null
                                        ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                            .format(v)
                                        : null;
                                  },
                                  validator: (v) {
                                    if (v == null) {
                                      return "交付时间不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  initialValue:
                                      widget.config?.data?.deliveryMethod,
                                  item:
                                      FormItem(title: "交付方式", isRequired: true),
                                  onSaved: (v) {
                                    form["deliveryMethod"] = v;
                                  },
                                  validator: (v) {
                                    if (v == null || v == "") {
                                      return "交付方式不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  initialValue:
                                      "${widget.config?.data?.transUnitPrice ?? ""}",
                                  item: FormItem(
                                    title: "流转价格",
                                    isRequired: true,
                                  ),
                                  textInputType: TextInputType.number,
                                  controller: priceUnitController,
                                  unit: "元/亩",
                                  onSaved: (v) {
                                    form["transUnitPrice"] = v;
                                  },
                                  onChange: (v) {
                                    if (RegUtil.isNumericAmount(v)) {
                                      totalPrice = Decimal.parse(v) *
                                          Decimal.parse("$totalChargeArea");
                                    }
                                  },
                                  validator: (v) {
                                    if (v == null || v == "") {
                                      return "流转价格不能为空";
                                    }
                                    final parsedValue = double.tryParse(v);
                                    if (parsedValue == null) {
                                      return "流转价格必须是数字";
                                    }
                                    if (parsedValue < 0) {
                                      return "流转价格不能为负数";
                                    }
                                    return null;
                                  },
                                ),
                                BdhDatePicker(
                                  item:
                                      FormItem(title: "支付时间", isRequired: true),
                                  initialValue:
                                      widget.applyInfo?.payDate != null
                                          ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                              .parse(widget.applyInfo!.payDate!)
                                          : null,
                                  onSaved: (v) {
                                    form["payDate"] = v != null
                                        ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                            .format(v)
                                        : null;
                                  },
                                  validator: (value) {
                                    if (value == null) {
                                      return "支付时间不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  initialValue: widget.config?.data?.payMethod,
                                  item:
                                      FormItem(title: "支付方式", isRequired: true),
                                  onSaved: (v) {
                                    form["payMethod"] = v;
                                  },
                                  validator: (v) {
                                    if (v == null || v == "") {
                                      return "支付方式不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  item:
                                      FormItem(title: "经营项目", isRequired: true),
                                  initialValue:
                                      widget.config?.data?.projectName,
                                  onSaved: (v) {
                                    form["projectName"] = v;
                                  },
                                  validator: (v) {
                                    if (v == null || v.isEmpty) {
                                      return "经营项目不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  item: FormItem(
                                      title: "农业政策性补贴", isRequired: true),
                                  initialValue:
                                      widget.config?.data?.agpSubsidyAgree,
                                  onSaved: (v) {
                                    form["agpSubsidyAgree"] = v;
                                  },
                                  validator: (v) {
                                    if (v == null || v.isEmpty) {
                                      return "农业政策性补贴不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  item: FormItem(title: "补偿", isRequired: true),
                                  initialValue:
                                      widget.config?.data?.compenAgree,
                                  onSaved: (v) {
                                    form["compenAgree"] = v;
                                  },
                                  validator: (v) {
                                    if (v == null || v.isEmpty) {
                                      return "补偿不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  item:
                                      FormItem(title: "违约金额", isRequired: true),
                                  initialValue:
                                      "${widget.config?.data?.breachAmount ?? ""}",
                                  textInputType: TextInputType.number,
                                  unit: "元",
                                  controller: breachAmountController,
                                  onSaved: (v) {
                                    form["breachAmount"] = v;
                                  },
                                  validator: (v) {
                                    if (v == null || v == "") {
                                      return "违约金额不能为空";
                                    }
                                    final parsedValue = double.tryParse(v);
                                    if (parsedValue == null) {
                                      return "违约金额必须是数字";
                                    }
                                    if (parsedValue < 0) {
                                      return "违约金额不能为负数";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  item: FormItem(
                                      title: "滞纳金比例", isRequired: true),
                                  initialValue:
                                      "${widget.config?.data?.lateFeePer ?? ""}",
                                  onSaved: (v) {
                                    form["lateFeePer"] = v;
                                  },
                                  controller: breachProportionController,
                                  // onChange: (v) {
                                  //   if (RegUtil.isNumericAmount(v)) {
                                  //     breachProprotion = num.parse(v);
                                  //     breachAmountController.text =
                                  //         "${totalPrice * num.parse(v) / 1000}";
                                  //   }
                                  // },
                                  unit: "‰",
                                  textInputType: TextInputType.number,
                                  validator: (v) {
                                    if (v == null || v == "") {
                                      return "滞纳金比例不能为空";
                                    }
                                    final parsedValue = double.tryParse(v);
                                    if (parsedValue == null) {
                                      return "滞纳金比例必须是数字";
                                    }
                                    if (parsedValue < 0) {
                                      return "滞纳金比例不能为负数";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  item: FormItem(
                                      title: "双方约定事项", isRequired: true),
                                  initialValue:
                                      widget.config?.data?.otherMatters ?? "",
                                  onSaved: (v) {
                                    form["otherMatters"] = v;
                                  },
                                  validator: (v) {
                                    if (v == null || v == "") {
                                      return "双方约定事项不能为空";
                                    }
                                    return null;
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      )),
                ],
              )),
            ),
            GestureDetector(
              onTap: () {
                if (key.currentState!.validate()) {
                  key.currentState!.save();

                  TDToast.showLoadingWithoutText(
                      context: context, preventTap: true);
                  form["fromContractId"] = widget.contractId;
                  form["landNumbers"] = widget.landNos;
                  form["toFarmerId"] = widget.toFarmerId;
                  form["toPartnerId"] = widget.toPartnerId;
                  form['contractDetailIs'] = widget.landList
                      .map((land) => land.contractDetailId)
                      .toList();
                  form['transTotalPrice'] = totalPrice.toString();

                  LandResponsitory.saveTransfer(form).then((res) {
                    TDToast.dismissLoading();
                    if (res.code == 0) {
                      showToast("保存成功");
                      Navigator.of(context).popUntil((pre) {
                        return pre.settings.name == RouteName.xjLandContract;
                      });
                    }
                  });
                }
              },
              child: BDHButtonGreen(width: 347.px, height: 45.px, title: "保存"),
            )
          ],
        );
      }),
    );
  }

  loadOrg() {
    Future.wait([
      LandResponsitory.getDicByKey("trans_type"),
      LandResponsitory.getDicByKey("charge_source"),
    ]).then((list) {
      transTypeDics = list[0].data ?? [];
      payTypeDics = list[1].data ?? [];
      setState(() {});
    });
  }
}
