import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_network_image.dart';
import 'package:bdh_smart_agric_app/components/bdh_steps.dart';
import 'package:bdh_smart_agric_app/components/bdh_tag.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/model/query_farmer_result_model.dart';
import 'package:bdh_smart_agric_app/model/query_my_contract_result_model.dart';
import 'package:bdh_smart_agric_app/model/query_partner_result_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_check.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/transfer/transfer_management_contract_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/landcontract/transfer/transfer_management_info_page.dart';
import 'package:bdh_smart_agric_app/utils/dic_util.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:bdh_smart_agric_app/viewmodel/user_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

//选择对象
class TransferManagementObjectPage extends StatefulWidget {
  final String title;
  final List<String>? steps;
  final int? stepIndex;
  final num contractId;
  final List<String> landNo;
  final List<ContractDetail> landList;
  final MyContractItem contractItem;
  const TransferManagementObjectPage(
      {super.key,
      required this.title,
      this.steps,
      this.stepIndex,
      required this.contractId,
      required this.landNo,
      required this.landList,
      required this.contractItem});

  @override
  State<StatefulWidget> createState() => _TransferManagementObjectState();
}

class _TransferManagementObjectState
    extends State<TransferManagementObjectPage> {
  List<ContractFarmer> items = [];
  List<ContractPartner> items1 = [];
  String objectType = "查人员";
  bool isSelect = false;
  String searchKey = "";
  ContractFarmer? curFarmer;
  ContractPartner? curPartner;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 2),
      appBar: AppBar(
        title: const Text("经营权流转申请"),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Container(
              width: 375.px,
              height: 66.px,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(15.px),
                      bottomRight: Radius.circular(15.px))),
              child: BdhStepsHorizontal(
                steps: [
                  BdhStepsItemData(title: "选择合同"),
                  BdhStepsItemData(title: "选择地块"),
                  BdhStepsItemData(title: "选择对象"),
                  BdhStepsItemData(title: "填写内容")
                ],
                activeIndex: 2,
              ),
            ),
            SizedBox(
              height: 10.px,
            ),
            Visibility(
                visible: isSelect == false,
                child: LandSearchBar(
                  filterData: const ["查人员", "查企业"],
                  placeholder: "请输入名称",
                  onSearch: (item) {
                    searchKey = item.keyword ?? "";
                    if (objectType == "查人员") {
                      loadFarmers();
                    } else if (objectType == "查企业") {
                      loadPartners();
                    }
                  },
                  onTypeChange: (type) {
                    setState(() {
                      objectType = type;
                    });
                  },
                )),
            Visibility(
                visible: isSelect,
                child: SizedBox(
                  width: 351.px,
                  height: 40.px,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "对象信息",
                        style: TextStyle(
                            fontSize: 14.px, fontWeight: FontWeight.w500),
                      ),
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            isSelect = false;
                          });
                        },
                        child: Text(
                          "重新选择",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w500,
                              color: const Color.fromRGBO(10, 174, 108, 1)),
                        ),
                      )
                    ],
                  ),
                )),
            Visibility(
                visible: isSelect == false && objectType == "查人员",
                child: Container(
                  width: 351.px,
                  padding: EdgeInsets.only(top: 15.px),
                  height: cons.maxHeight -
                      MediaQuery.of(context).padding.bottom -
                      45.px -
                      66.px -
                      25.px -
                      37.px,
                  child: ListView.builder(
                      itemCount: items.length,
                      itemBuilder: (ctx, idx) {
                        return FarmerItemView(
                          item: items[idx],
                          onSelect: (farmer) {
                            setState(() {
                              isSelect = true;
                            });
                            curFarmer = farmer;
                          },
                        );
                      }),
                )),
            Visibility(
                visible: isSelect == false && objectType == "查企业",
                child: Container(
                  width: 351.px,
                  padding: EdgeInsets.only(top: 15.px),
                  height: cons.maxHeight -
                      MediaQuery.of(context).padding.bottom -
                      45.px -
                      66.px -
                      25.px -
                      37.px,
                  child: ListView.builder(
                      itemCount: items1.length,
                      itemBuilder: (ctx, idx) {
                        return PartnerItemView(
                          item: items1[idx],
                          onSelect: (partner) {
                            setState(() {
                              isSelect = true;
                            });
                            curPartner = partner;
                          },
                        );
                      }),
                )),
            isSelect == true && objectType == "查企业"
                ? Container(
                    width: 351.px,
                    alignment: Alignment.topCenter,
                    padding: EdgeInsets.only(top: 15.px),
                    height: cons.maxHeight -
                        MediaQuery.of(context).padding.bottom -
                        45.px -
                        66.px -
                        25.px -
                        37.px,
                    child: PartnerItemSelectView(item: curPartner!),
                  )
                : Container(),
            isSelect == true && objectType == "查人员"
                ? Container(
                    width: 351.px,
                    alignment: Alignment.topCenter,
                    height: cons.maxHeight -
                        MediaQuery.of(context).padding.bottom -
                        45.px -
                        66.px -
                        25.px -
                        37.px,
                    child: FarmerSelectView(item: curFarmer!),
                  )
                : Container(),
            Visibility(
                visible: isSelect == true,
                child: GestureDetector(
                  onTap: () {
                    //取下默认配置
                    LandBaseInfo? info = StorageUtil.landbaseInfo();
                    TDToast.showLoadingWithoutText(context: context);
                    LandResponsitory.queryDefaultTransConfig({
                      "yearNo": DateTime.now().year,
                      "organizationNo": widget.contractItem.organizationNo
                    }).then((res) {
                      TDToast.dismissLoading();
                      Navigator.of(context)
                          .push(CupertinoPageRoute(builder: (ctx) {
                        return TransferManageInfoPage(
                          title: "经营权流转申请",
                          contractId: widget.contractId,
                          landNos: widget.landNo,
                          toFarmerId: curFarmer?.farmerId,
                          toPartnerId: curPartner?.partnerId,
                          landList: widget.landList,
                          config: res,
                        );
                      }));
                    });
                  },
                  child: BDHButtonGreen(
                      width: 347.px, height: 45.px, title: "下一步"),
                ))
          ],
        );
      }),
    );
  }

  loadPartners() {
    LandResponsitory.queryPartner({"searchKey": searchKey}).then((res) {
      setState(() {
        items1 = res.data ?? [];
      });
    });
  }

  loadFarmers() {
    LandResponsitory.queryFarmer({"searchKey": searchKey}).then((res) {
      setState(() {
        items = res.data ?? [];
      });
    });
  }
}

class FarmerItem {
  String? name;
  String? isEmployee;
  String? idCardNumer;
  String? telPhoneNumer;
  String? organizationName;
  String? sexNo;
  String? iconUrl;

  FarmerItem(
      {this.name,
      this.isEmployee,
      this.idCardNumer,
      this.telPhoneNumer,
      this.organizationName,
      this.sexNo,
      this.iconUrl});
}

class FarmerItemView extends StatefulWidget {
  final Function(ContractFarmer) onSelect;
  final ContractFarmer item;
  const FarmerItemView({super.key, required this.item, required this.onSelect});

  @override
  State<StatefulWidget> createState() => _FarmerItemViewState();
}

class _FarmerItemViewState extends State<FarmerItemView> {
  bool isCheck = false;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 351.px,
      height: 97.px,
      padding: EdgeInsets.all(13.px),
      margin: EdgeInsets.only(bottom: 10.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(6.px)),
            child: BdhNetworkImage(
                fit: BoxFit.cover,
                width: 65.px,
                height: 65.px,
                url: widget.item.photoPath ?? ""),
          ),
          SizedBox(
            width: 10.px,
          ),
          SizedBox(
            width: 250.px,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.item.name ?? "",
                      style: TextStyle(
                          fontSize: 16.px, fontWeight: FontWeight.w600),
                    ),
                    BdhCheck(
                        isCheck: isCheck,
                        width: 15.px,
                        onClick: (check) {
                          setState(() {
                            isCheck = check;
                          });
                          if (isCheck == true) {
                            showDialog(
                                context: context,
                                builder: (ctx) {
                                  return Align(
                                    alignment: Alignment.bottomCenter,
                                    child: Container(
                                      margin: EdgeInsets.only(bottom: 20.px),
                                      child: GestureDetector(
                                        onTap: () {
                                          widget.onSelect(widget.item);
                                          Navigator.of(context).pop();
                                        },
                                        child: BDHButtonGreen(
                                            width: 341.px,
                                            height: 50.px,
                                            title: "选择"),
                                      ),
                                    ),
                                  );
                                }).then((res) {
                              setState(() {
                                isCheck = false;
                              });
                            });
                          }
                        })
                  ],
                ),
                Row(
                  children: [
                    BdhTag(
                        name: widget.item.sexNo == 1 ? "男" : "女",
                        color: widget.item.sexNo == 1
                            ? const Color.fromRGBO(10, 174, 108, 1)
                            : const Color.fromRGBO(243, 84, 66, 1)),
                    SizedBox(
                      width: 5.px,
                    ),
                    BdhTag(
                        name: widget.item.isEmployee == 1 ? "职工" : "非职工",
                        color: widget.item.isEmployee == 1
                            ? const Color.fromRGBO(51, 121, 250, 1)
                            : const Color.fromRGBO(255, 147, 6, 1)),
                    SizedBox(
                      width: 5.px,
                    ),
                    Text(
                      widget.item.idNumber ?? "",
                      style: TextStyle(
                        fontSize: 12.px,
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.item.phone1 ?? "",
                      style: TextStyle(
                        fontSize: 12.px,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      widget.item.organizationName ?? "",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: const Color.fromRGBO(44, 44, 52, 0.6)),
                    )
                  ],
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

class FarmerSelectView extends StatelessWidget {
  final ContractFarmer item;
  const FarmerSelectView({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    TextStyle style1 = TextStyle(
        fontSize: 12.px,
        fontWeight: FontWeight.w400,
        color: const Color.fromRGBO(44, 44, 52, 0.5));
    TextStyle style2 = TextStyle(
        fontSize: 12.px,
        fontWeight: FontWeight.w500,
        color: const Color.fromRGBO(44, 44, 52, 0.8));
    return Container(
      width: 351.px,
      height: 149.px,
      padding: EdgeInsets.all(13.px),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.px)),
          color: Colors.white),
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(6.px)),
            child: BdhNetworkImage(
                fit: BoxFit.cover,
                width: 94.px,
                height: 119.px,
                url: item.photoPath ?? ""),
          ),
          SizedBox(
            width: 10.px,
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.name ?? "",
                style: TextStyle(fontSize: 18.px, fontWeight: FontWeight.w600),
              ),
              Row(
                children: [
                  Text(
                    "性别：",
                    style: style1,
                  ),
                  Text(
                    item.sexNo == 1 ? "男" : "女",
                    style: style2,
                  )
                ],
              ),
              Row(
                children: [
                  Text(
                    "身份证号：",
                    style: style1,
                  ),
                  Text(
                    item.idNumber ?? "",
                    style: style2,
                  )
                ],
              ),
              Row(
                children: [
                  Text(
                    "手机号：",
                    style: style1,
                  ),
                  Text(
                    item.phone1 ?? "",
                    style: style2,
                  )
                ],
              ),
              Row(
                children: [
                  Text(
                    "农户身份：",
                    style: style1,
                  ),
                  Text(
                    item.isEmployee == 1 ? "职工" : "非职工",
                    style: style2,
                  )
                ],
              )
            ],
          )
        ],
      ),
    );
  }
}

class PartnerItem {
  String? name;
  String? legalName;
  String? legalTelPhoneNumber;
  String? orgType;
  String? farmerName;
  String? telPhoneNumber;
  String? busiLicensePicUrl;
  PartnerItem(
      {this.name,
      this.legalName,
      this.legalTelPhoneNumber,
      this.orgType,
      this.farmerName,
      this.telPhoneNumber,
      this.busiLicensePicUrl});
}

class PartnerItemView extends StatefulWidget {
  final Function(ContractPartner) onSelect;
  final ContractPartner item;
  const PartnerItemView(
      {super.key, required this.item, required this.onSelect});

  @override
  State<StatefulWidget> createState() => _PartnerItemViewState();
}

class _PartnerItemViewState extends State<PartnerItemView> {
  bool isCheck = false;
  @override
  Widget build(BuildContext context) {
    TextStyle style1 = TextStyle(fontSize: 12.px, fontWeight: FontWeight.w400);
    TextStyle style2 = TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500);
    return Container(
      width: 351.px,
      height: 155.px,
      padding: EdgeInsets.all(13.px),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8.px)),
          color: Colors.white),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.item.partnerName ?? "",
                style: TextStyle(fontSize: 16.px, fontWeight: FontWeight.w600),
              ),
              BdhCheck(
                  isCheck: isCheck,
                  width: 15.px,
                  onClick: (check) {
                    setState(() {
                      isCheck = check;
                    });
                    if (isCheck == true) {
                      showDialog(
                          context: context,
                          builder: (ctx) {
                            return Align(
                              alignment: Alignment.bottomCenter,
                              child: Container(
                                margin: EdgeInsets.only(bottom: 20.px),
                                child: GestureDetector(
                                  onTap: () {
                                    widget.onSelect(widget.item);
                                    Navigator.of(context).pop();
                                  },
                                  child: BDHButtonGreen(
                                      width: 341.px,
                                      height: 50.px,
                                      title: "选择"),
                                ),
                              ),
                            );
                          }).then((res) {
                        setState(() {
                          isCheck = false;
                        });
                      });
                    }
                  })
            ],
          ),
          Container(
            width: 319.px,
            height: 64.px,
            decoration: BoxDecoration(
                color: const Color.fromRGBO(243, 245, 249, 0.5),
                borderRadius: BorderRadius.all(Radius.circular(8.px))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "法定代表人",
                      style: style1,
                    ),
                    Text(
                      widget.item.legalName ?? "",
                      style: style2,
                    )
                  ],
                ),
                Container(
                  width: 1.px,
                  height: 30.px,
                  color: const Color.fromRGBO(234, 237, 243, 1),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "手机号：",
                      style: style1,
                    ),
                    Text(widget.item.legalTelephone ?? "", style: style2)
                  ],
                ),
                Container(
                  width: 1.px,
                  height: 30.px,
                  color: const Color.fromRGBO(234, 237, 243, 1),
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "组织类型",
                      style: style1,
                    ),
                    Text(
                        DicUtil.dictCodeToName(
                            "${widget.item.oppositePartyType}",
                            context.read<UserModel>().partyType?.data ?? []),
                        style: style2)
                  ],
                )
              ],
            ),
          ),
          Row(
            children: [
              const BdhTag(name: "经办人", color: Color.fromRGBO(10, 174, 108, 1)),
              SizedBox(
                width: 10.px,
              ),
              Text(widget.item.farmerName ?? ""),
              Container(
                margin: EdgeInsets.only(left: 6.px, right: 6.px),
                width: 3.px,
                height: 3.px,
                decoration: BoxDecoration(
                    color: Colors.black,
                    borderRadius: BorderRadius.all(Radius.circular(1.5.px))),
              ),
              Text(widget.item.farmerPhone ?? "")
            ],
          )
        ],
      ),
    );
  }
}

class PartnerItemSelectView extends StatelessWidget {
  final ContractPartner item;
  const PartnerItemSelectView({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    TextStyle style1 = TextStyle(
        fontSize: 12.px,
        fontWeight: FontWeight.w400,
        color: const Color.fromRGBO(44, 44, 52, 0.5));
    TextStyle style2 = TextStyle(
        fontSize: 12.px,
        fontWeight: FontWeight.w500,
        color: const Color.fromRGBO(44, 44, 52, 0.8));
    return Container(
      width: 351.px,
      height: 301.px,
      padding: EdgeInsets.all(13.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            item.partnerName ?? "",
            style: TextStyle(fontSize: 18.px, fontWeight: FontWeight.w600),
          ),
          ClipRRect(
            borderRadius: BorderRadius.all(Radius.circular(8.px)),
            child: BdhNetworkImage(
              fit: BoxFit.cover,
              url: item.busiLicensePicUrl ?? "",
              width: 315.px,
              height: 160.px,
            ),
          ),
          Row(
            children: [
              Text(
                "代办人姓名：",
                style: style1,
              ),
              Text(item.farmerName ?? "", style: style2)
            ],
          ),
          Row(
            children: [
              Text("身份证号：", style: style1),
              Text(item.farmerIdNumber ?? "", style: style2)
            ],
          ),
          Row(
            children: [
              Text("手机号：", style: style1),
              Text(item.farmerPhone ?? "", style: style2)
            ],
          )
        ],
      ),
    );
  }
}
