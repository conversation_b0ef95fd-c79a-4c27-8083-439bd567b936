import 'dart:async';

import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';

import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:oktoast/oktoast.dart';

import 'flow_water_apply_info_page.dart';
import 'model/water_manage_index_model.dart';
import 'model/water_planting_info_model.dart';
import 'my_water_payment_info_page.dart';
import 'my_water_payment_recharge_page.dart';
import 'my_water_planting_info_page.dart';
import 'my_water_use_apply_page.dart';
import 'my_water_use_detail_page.dart';
import 'my_water_use_info_page.dart';
import 'request/xinjiang_water_manage_service.dart';
import 'widget/water_apply_item_widget.dart';
import 'widget/water_manage_account_item_widget.dart';

//我的水费
class MyWaterPage extends StatefulWidget {
  const MyWaterPage({super.key});

  @override
  State<MyWaterPage> createState() => _MyWaterPageState();
}

class _MyWaterPageState extends State<MyWaterPage> with AutoDisposeStateMixin {
  late final _Controller controller;
  @override
  void initState() {
    super.initState();
    controller = useChangeNotifier(_Controller(context))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading) return _widgetLoading();
    if (controller.needWritePlantInfo) {
      return MyWaterPlantingInfoPage(
        isCheck: true,
        itemList: controller.itemList,
      );
    } else {
      return const MyWaterIndexPage();
    }
  }

  //加载中
  Widget _widgetLoading() {
    return Scaffold(
      appBar: AppBar(
        title: const Text("我的水费"),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: const Center(
        child: ViewStateBusyWidget(),
      ),
    );
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  _Controller(this.context);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  Null _handlerError(Object error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted || disposed) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  List<WaterPlantingInfoItem> itemList = [];

  //判断是否需要先让用户填写种植信息
  bool get needWritePlantInfo {
    if (itemList.isEmpty) {
      return true;
    }
    bool need = false;
    for (var item in itemList) {
      if (item.piId == null || item.auditResult != "审核通过") {
        need = true;
      }
    }
    return need;
  }

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    _loadingStatus = LoadingStatus.loading;
    notifyListeners();
    UserInfo? userInfo = StorageUtil.userInfo();
    var data = {"idNumber": userInfo?.data?.idCard};

    XinJiangWaterManageService()
        .getFarmerPlot(data, cancelToken: createCancelToken())
        .then((result) {
      Log.d("result is $result");

      itemList = result.data == null
          ? []
          : (result.data as List)
              .map<WaterPlantingInfoItem>((data) =>
                  WaterPlantingInfoItem.fromJson(data as Map<String, Object?>))
              .toList();
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }
}

//我的水费
class MyWaterIndexPage extends StatefulWidget {
  const MyWaterIndexPage({super.key});

  @override
  State<MyWaterIndexPage> createState() => _MyWaterIndexPageState();
}

class _MyWaterIndexPageState extends State<MyWaterIndexPage>
    with AutoDisposeStateMixin {
  late final ScrollController _scrollController;

  late final _IndexController controller;

  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());
    controller = useChangeNotifier(_IndexController(context))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("我的水费"),
        actions: [
          if (!controller.isLoading)
            GestureDetector(
              onTap: controller.onClickPaymentRecords,
              child: Text("缴费记录",
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w500,
                    color: const Color.fromRGBO(30, 192, 106, 1),
                  )),
            ),
          SizedBox(
            width: 15.px,
          )
        ],
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: controller.isLoading
              ? _widgetLoading(context)
              : _widgetBody(context)),
    );
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  //申请机动地地块-列表为空
  Widget _widgetEmpty(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            ImageHelper.wrapAssets("icon_nodata.svg"),
            width: 201.5.px,
            height: 100.px,
          ),
          SizedBox.square(
            dimension: 45.px,
          ),
          Text(
            "您还没有配置水费账户",
            style: TextStyle(
                color: const Color.fromRGBO(44, 44, 52, 1), fontSize: 14.px),
          ),
          SizedBox.square(
            dimension: 12.px,
          ),
          TextButton(
            style: ButtonStyle(
                shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(9.px))),
                backgroundColor: WidgetStateProperty.resolveWith((states) {
                  if (states.contains(WidgetState.disabled)) {
                    return Colors.grey;
                  } else if (states.contains(WidgetState.pressed)) {
                    return const Color.fromRGBO(23, 156, 102, 0.8);
                  } else {
                    return const Color.fromRGBO(2, 139, 93, 1);
                  }
                }),
                overlayColor: WidgetStateProperty.all(Colors.transparent)),
            onPressed: controller.onClickPlantingInfo,
            child: Text(
              "去配置",
              style: TextStyle(
                  fontSize: 14.px,
                  decoration: TextDecoration.none,
                  color: Colors.white),
            ),
          )
        ],
      ),
    );
  }

  Widget _widgetBody(BuildContext context) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        if (controller.waterManageIndexModel?.myAccList?.isEmpty ?? true) ...[
          SliverFillRemaining(child: _widgetEmpty(context)),
        ],
        if (controller.waterManageIndexModel?.myAccList?.isNotEmpty ??
            false) ...[
          SliverToBoxAdapter(child: _widgetWaterAccountTitle(context)),
          SliverToBoxAdapter(child: _widgetWaterAccountList(context)),
        ],
        if ((controller.waterManageIndexModel?.myAccList?.isNotEmpty ??
                false) &&
            (controller.waterManageIndexModel?.myApplyList?.isNotEmpty ??
                false)) ...[
          SliverToBoxAdapter(child: _widgetWaterApplyTitle(context)),
          _widgetWaterApplyList()
        ]
      ],
    );
  }

  //水费账户标题
  Widget _widgetWaterAccountTitle(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(
            top: 16.px, bottom: 6.px, left: 12.px, right: 12.px),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text("我的水费账户",
                style: TextStyle(
                    fontSize: 16.px,
                    color: const Color.fromRGBO(51, 51, 51, 1),
                    fontWeight: FontWeight.w600)),
            const Spacer(),
            GestureDetector(
              onTap: controller.onClickPlantingInfo,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                      alignment: Alignment.center,
                      fit: BoxFit.cover,
                      width: 24.px,
                      ImageHelper.wrapAssets("ic_pencil.svg")),
                  Text("修改信息",
                      style: TextStyle(
                        fontSize: 14.px,
                        color: const Color.fromRGBO(30, 192, 106, 1),
                      ))
                ],
              ),
            )
          ],
        ));
  }

  //用水申请标题
  Widget _widgetWaterApplyTitle(BuildContext context) {
    return Container(
        margin: EdgeInsets.only(
            top: 10.px, bottom: 6.px, left: 12.px, right: 12.px),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text("用水记录",
                style: TextStyle(
                    fontSize: 16.px,
                    color: const Color.fromRGBO(51, 51, 51, 1),
                    fontWeight: FontWeight.w600)),
            const Spacer(),
            GestureDetector(
              onTap: controller.onClickWaterUseInfo,
              child: Text("全部记录",
                  style: TextStyle(
                    fontSize: 14.px,
                    color: const Color.fromRGBO(30, 192, 106, 1),
                  )),
            )
          ],
        ));
  }

  //水费账户列表
  Widget _widgetWaterAccountList(BuildContext context) {
    var myAccList = controller.waterManageIndexModel?.myAccList ?? [];

    return Column(
      children: myAccList
          .map((item) => WaterManageAccountItemWidget(
                item: item,
                onClickRecharge: controller.onClickRecharge,
                onClickUseWater: controller.onClickUseWater,
              ))
          .toList(),
    );
  }

  //用水记录列表
  Widget _widgetWaterApplyList() {
    var myApplyList = controller.waterManageIndexModel?.myApplyList ?? [];
    return SliverList.builder(
      itemBuilder: (context, index) => WaterApplyItemWidget(
        item: myApplyList[index],
        onClickProcess: controller.onClickProcessWaterUse,
        onClick: controller.onClickItemWaterUse,
      ),
      itemCount: myApplyList.length,
    );
  }
}

class _IndexController extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  _IndexController(this.context);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  Null _handlerError(Object error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  WaterManageIndexModel? waterManageIndexModel;

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    _loadingStatus = LoadingStatus.loading;
    notifyListeners();
    UserInfo? userInfo = StorageUtil.userInfo();
    var data = {"idNumber": userInfo?.data?.idCard};

    XinJiangWaterManageService()
        .index(data, cancelToken: createCancelToken())
        .then((result) {
      Log.d("result is $result");

      waterManageIndexModel = WaterManageIndexModel.fromJson(result.data ?? {});
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  void onClickRecharge(MyAcc item) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => MyWaterPaymentRechargePage(item: item)))
        .then((result) {
      if (result == true) {
        loadData();
      }
    });
  }

  void onClickUseWater(MyAcc item) {
    Navigator.of(context)
        .push(
            CupertinoPageRoute(builder: (_) => MyWaterUseApplyPage(item: item)))
        .then((result) {
      if (result == true) {
        loadData();
      }
    });
  }

  //缴费记录
  void onClickPaymentRecords() {
    Navigator.of(context)
        .push(
            CupertinoPageRoute(builder: (_) => const MyWaterPaymentInfoPage()))
        .then((result) {
      if (result == true) {}
    });
  }

  //种植信息
  void onClickPlantingInfo() {
    Navigator.of(context)
        .push(
            CupertinoPageRoute(builder: (_) => const MyWaterPlantingInfoPage()))
        .then((result) {
      if (!context.mounted) {
        return;
      }
      if (result == true) {
        Navigator.maybePop(context);
      }
    });
  }

  //用水记录
  void onClickWaterUseInfo() {
    Navigator.of(context)
        .push(CupertinoPageRoute(builder: (_) => const MyWaterUseInfoPage()))
        .then((result) {
      if (result == true) {}
    });
  }

  void onClickItemWaterUse(MyApply item) {
    if (item.waId == null) {
      return;
    }
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => MyWaterUseDetailPage(waId: item.waId!)))
        .then((result) {
      if (result == true) {}
    });
  }

  void onClickProcessWaterUse(MyApply item) {
    if (item.waId == null) {
      return;
    }
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => FlowWaterApplyInfoPage(
                  waId: item.waId!,
                )))
        .then((result) {
      if (result == true) {}
    });
  }
}
