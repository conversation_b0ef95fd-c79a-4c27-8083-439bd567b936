import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/model/page_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/model/water_manage_index_model.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:dio/dio.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';

import '../model/water_use_record_model.dart';

//新疆水费管理
class XinJiangWaterManageService {
  const XinJiangWaterManageService._();

  static const XinJiangWaterManageService _instance =
      XinJiangWaterManageService._();

  factory XinJiangWaterManageService() => _instance;

  //factory XinJiangWaterManageService.test() => XinJiangWaterManageServiceTest();

  Future<RequestNoData> _requestNoData(String url, dynamic data,
      {CancelToken? cancelToken}) {
    return waterManageHttp
        .post(url, data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  //我的水费首页
  Future<RequestNoData> index(dynamic data, {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/index", data, cancelToken: cancelToken);

  //新增缴费记录
  Future<RequestNoData> saveChargeRecord(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/saveChargeRecord", data,
          cancelToken: cancelToken);
  //业务审核轨迹-待实现查询
  Future<RequestNoData> getAuditTrace(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/getAuditTrace", data,
          cancelToken: cancelToken);

  //缴费记录明细
  Future<RequestNoData> getPaymentRecordInfo(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/getPaymentRecordInfo/$data", null,
          cancelToken: cancelToken);

  //缴费记录列表
  Future<RequestNoData> getPaymentRecordList(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/getPaymentRecordList", data,
          cancelToken: cancelToken);

  //修改｜保存种植信息
  Future<RequestNoData> insertPlantInfo(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/insertPlantInfo", data,
          cancelToken: cancelToken);

  //修改｜保存种植信息
  Future<RequestNoData> updatePLantInfo(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/updatePLantInfo", data,
          cancelToken: cancelToken);

  //获取种植信息列表
  Future<RequestNoData> getPlantInfoList(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/getPlantInfoList", data,
          cancelToken: cancelToken);

  //获取农户地块 关联种植信息
  Future<RequestNoData> getFarmerPlot(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/getFarmerPlot", data,
          cancelToken: cancelToken);

  //作物列表
  Future<RequestNoData> cropByPage(dynamic data, {CancelToken? cancelToken}) =>
      _requestNoData("/api/common/cropByPage", data, cancelToken: cancelToken);

  //作物全量列表无需授权
  Future<RequestNoData> cropList(dynamic data, {CancelToken? cancelToken}) =>
      _requestNoData("/api/common/cropList", data, cancelToken: cancelToken);

  //地块列表
  Future<RequestNoData> plotByList(dynamic data, {CancelToken? cancelToken}) =>
      _requestNoData("/api/common/plotByList", data, cancelToken: cancelToken);

  //地块分页查询
  Future<RequestNoData> plotByPage(dynamic data, {CancelToken? cancelToken}) =>
      _requestNoData("/api/common/plotByPage", data, cancelToken: cancelToken);

  //账户下拉选
  Future<RequestNoData> getWaterAccList(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/getWaterAccList", data,
          cancelToken: cancelToken);

  //用水记录列表
  Future<RequestNoData> waterApplyList(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/waterApplyList", data,
          cancelToken: cancelToken);

  //用水记录详情
  Future<RequestNoData> waterHandleInfo(dynamic waId,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/info/$waId", null,
          cancelToken: cancelToken);

  @Deprecated("update to getPlantInfoPlot")
  Future<RequestNoData> getContractPlot(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/getContractPlot", data,
          cancelToken: cancelToken);

  //获取种植信息地块列表
  Future<RequestNoData> getPlantInfoPlot(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/getPlantInfoPlot", data,
          cancelToken: cancelToken);

  //计算预估费用
  Future<RequestNoData> estimatePrice(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/app/waterFee/estimatePrice", data,
          cancelToken: cancelToken);

  //app端用水申请
  Future<RequestNoData> waterApplyInsert(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/insert", data,
          cancelToken: cancelToken);

  //接水员-接水员待处理申请 - 数量
  Future<RequestNoData> waterApplyHandleReceiverPendingNumber(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData(
          "/api/app/water/apply/handle/receiver/pending/number", data,
          cancelToken: cancelToken);
  //接水员 待处理申请 - 分页查询
  Future<RequestNoData> waterApplyHandleReceiverPendingPage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/receiver/pending/page", data,
          cancelToken: cancelToken);

  //接水员 待处理申请 - 接水员提交
  Future<RequestNoData> waterApplyHandleReceiverSubmit(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/receiver/submit", data,
          cancelToken: cancelToken);

  //接水员 接水申请tab - 分页查询
  Future<RequestNoData> waterApplyHandleReceiverRunningPage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/receiver/running/page", data,
          cancelToken: cancelToken);

  //接水员 放水申请tab - 提交
  ///api/app/water/apply/handle/audit
  Future<RequestNoData> waterApplyHandleAudit(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/audit", data,
          cancelToken: cancelToken);

  //接水员 完成 - 分页查询
  ///api/app/water/apply/handle/audit
  Future<RequestNoData> waterApplyHandleReceiverDonePage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/receiver/done/page", data,
          cancelToken: cancelToken);

  //接水员 申请详情
  // /api/app/water/apply/handle/info/10
  Future<RequestNoData> waterApplyHandleInfo(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/info/$data", data,
          cancelToken: cancelToken);

  //接水员 查看流程
  // /api/app/water/apply/handle/info/10
  Future<RequestNoData> waterApplyViewProcess(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/view/process/$data", null,
          cancelToken: cancelToken);

  //接水员 退回流程
  ///api/app/water/apply/handle/audit
  Future<RequestNoData> waterApplyHandleReceiverReject(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/receiver/reject", data,
          cancelToken: cancelToken);

  // //接水员-审核列表查询
  // Future<RequestNoData> waterApplyQueryByPage(dynamic data,
  //         {CancelToken? cancelToken}) =>
  //     _requestNoData("/api/app/water/apply/queryByPage", data,
  //         cancelToken: cancelToken);

  // //接水员-查看流程
  // Future<RequestNoData> applyViewProcess(dynamic waId,
  //         {CancelToken? cancelToken}) =>
  //     _requestNoData("/api/app/water/apply/view/process/$waId", null,
  //         cancelToken: cancelToken);

  // //接水员-退回查看流程
  // ///api/app/water/apply/handle/reject
  // Future<RequestNoData> applyHandleReject(dynamic data,
  //         {CancelToken? cancelToken}) =>
  //     _requestNoData("api/app/water/apply/handle/reject", data,
  //         cancelToken: cancelToken);

  //用水申请 - 接水员提交
  ///api/app/water/apply/handle/receiver
  Future<RequestNoData> applyHandleReceiver(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/receiver", data,
          cancelToken: cancelToken);

  //用水申请 - 详情
  ///api/app/water/apply/handle/info/10
  Future<RequestNoData> applyHandleInfo(dynamic waId,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/info/$waId", null,
          cancelToken: cancelToken);

  //配水员-配水员待处理申请 - 数量
  Future<RequestNoData> waterApplyHandleDistributorPendingNumber(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData(
          "/api/app/water/apply/handle/distributor/pending/number", data,
          cancelToken: cancelToken);

  //配水员待处理申请 - 分页查询
  Future<RequestNoData> waterApplyHandleDistributorPendingPage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData(
          "/api/app/water/apply/handle/distributor/pending/page", data,
          cancelToken: cancelToken);

  //配水员待处理申请 - 配水员提交
  Future<RequestNoData> waterApplyHandleDistributorSubmit(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/distributor/submit", data,
          cancelToken: cancelToken);

  //配水员-用水申请tab - 分页查询
  Future<RequestNoData> waterApplyHandleDistributorMonitorPage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData(
          "/api/app/water/apply/handle/distributor/monitor/page", data,
          cancelToken: cancelToken);

  //配水员-放水申请tab - 分页查询
  Future<RequestNoData> waterApplyHandleDistributorRunningPage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData(
          "/api/app/water/apply/handle/distributor/running/page", data,
          cancelToken: cancelToken);

  //配水员-放水申请tab - 提交
  Future<RequestNoData> waterApplyHandleDistributorReleaseSubmit(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData(
          "/api/app/water/apply/handle/distributor/release/submit", data,
          cancelToken: cancelToken);

  //配水员-完成tab - 分页查询
  Future<RequestNoData> waterApplyHandleDistributorDonePage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/distributor/done/page", data,
          cancelToken: cancelToken);

  //配水员-用水申请 - 详情
  Future<RequestNoData> waterApplyHandleDistributorReleaseInfo(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData(
          "/api/app/water/apply/handle/distributor/release/info/$data", null,
          cancelToken: cancelToken);

  //退回流程 - 配水员
  Future<RequestNoData> waterApplyHandleDistributorReleaseReject(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData(
          "/api/app/water/apply/handle/distributor/release/reject", data,
          cancelToken: cancelToken);

  //调度员-用水申请tab - 分页查询
  Future<RequestNoData> waterApplyHandleRegulatorPendingPage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/regulator/pending/page", data,
          cancelToken: cancelToken);

  //调度员-用水申请tab - 提交
  Future<RequestNoData> waterApplyHandleRegulatorSubmit(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/regulator/submit", data,
          cancelToken: cancelToken);

  //调度员-用水申请tab - 提交
  Future<RequestNoData> waterApplyHandleRegulatorRunningPage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/regulator/running/page", data,
          cancelToken: cancelToken);

  //调度员-完成 - 分页查询
  Future<RequestNoData> waterApplyHandleRegulatorDonePage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/regulator/done/page", data,
          cancelToken: cancelToken);

  //调度员-退回流程
  Future<RequestNoData> waterApplyHandleRegulatorReject(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/regulator/reject", data,
          cancelToken: cancelToken);

  //干 支 斗-级联列表
  Future<RequestNoData> canalList(dynamic data, {CancelToken? cancelToken}) =>
      _requestNoData("/api/common/canal/list", data, cancelToken: cancelToken);

  // 配水员-获取-干 支 斗-级联列表
  Future<RequestNoData> waterApplyHandleDistributorCanal(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/apply/handle/distributor/canal", data,
          cancelToken: cancelToken);

  // 配水员-配水员配置组织机构列表
  Future<OrgTreeResult> waterApplyHandleDistributorOrg(dynamic data,
      {CancelToken? cancelToken}) {
    return waterManageHttp
        .post("/api/app/water/apply/handle/distributor/org",
            data: data, cancelToken: cancelToken)
        .then<OrgTreeResult>((result) => OrgTreeResult.fromJson(result.data));
  }

  //------  结算水费 -------
  // 结算水费 -用结算用水分页查询
  Future<RequestNoData> querySettleWaterApplyByPage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/settle/querySettleWaterApplyByPage", data,
          cancelToken: cancelToken);

  // 结算水费 -app端用水申请
  Future<RequestNoData> waterSettleInsert(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/settle/insert", data,
          cancelToken: cancelToken);

  // 结算水费 - 用水量统计
  Future<RequestNoData> waterSettleTotalWaterApply(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/settle/totalWaterApply", data,
          cancelToken: cancelToken);

  // 结算水费 - 用水量统计
  Future<RequestNoData> querySettleAccountByPage(dynamic data,
          {CancelToken? cancelToken}) =>
      _requestNoData("/api/app/water/settle/querySettleAccountByPage", data,
          cancelToken: cancelToken);

  //查询登陆人中台配置的单位集合
  Future<OrgTreeResult> queryOrgFarmTreeByStaff(dynamic data,
      {CancelToken? cancelToken}) {
    return waterManageHttp
        .post("/org/amporg/queryOrgFarmTreeByStaff",
            data: data, cancelToken: cancelToken)
        .then<OrgTreeResult>((result) => OrgTreeResult.fromJson(result.data));
  }

  //票据
  Future waterChargeTickets(dynamic data, String savePath,
      {CancelToken? cancelToken, void Function(int, int)? onReceiveProgress}) {
    return waterManageDownloadHttp.post("/water/charge/tickets",
        onReceiveProgress: onReceiveProgress,
        data: data,
        options: Options(responseType: ResponseType.bytes),
        cancelToken: cancelToken);
  }

  //字典接口
  Future<DictList> getDicByKey(String key, {CancelToken? cancelToken}) {
    return ssoHttp
        .post("/sso/dict/list/$key", cancelToken: cancelToken)
        .then<DictList>((response) {
      return DictList.fromJson(response.data);
    });
  }
}

class XinJiangWaterManageServiceTest extends XinJiangWaterManageService {
  static const XinJiangWaterManageServiceTest _testInstance =
      XinJiangWaterManageServiceTest._();

  factory XinJiangWaterManageServiceTest() => _testInstance;
  const XinJiangWaterManageServiceTest._() : super._();

  @override
  Future<RequestNoData> getFarmerPlot(data, {CancelToken? cancelToken}) {
    var data = <Map>[];

    return mockDelay(RequestNoData(code: 0, data: []));
  }

  @override
  Future<RequestNoData> index(dynamic data, {CancelToken? cancelToken}) {
    var data = const WaterManageIndexModel(myAccList: [
      MyAcc(
        accId: 0,
        orgCode: "0",
        orgName: "玉米合作社",
        accCode: " 0",
        growerType: "0",
        growerName: "187团",
        idNumber: "0",
        accBalance: 29.2,
      ),
      MyAcc(
        accId: 1,
        orgCode: "1",
        orgName: "香蕉合作社",
        accCode: " 1",
        growerType: "1",
        growerName: "188团",
        idNumber: "1",
        accBalance: 39.2,
      ),
    ], myApplyList: [
      MyApply(
          waId: 0,
          yearNo: 2025,
          orgCode: "0",
          operateName: "operateName",
          operateIdNumber: "operateIdNumber",
          plotNo: "183010101001A",
          plotName: "183010101001A",
          plotArea: 50.0,
          cropCode: "0",
          cropName: "玉米",
          usageDate: "2025-01-11",
          waterCons: 1.1,
          drainageStatus: "0",
          plotRel: PlotRel(
            regimentCanal: "181",
            companyCanal: "01",
            mainCanal: "01",
            branchCanal: "02",
            lateralCanal: "08",
            billingMode: "按量计费",
            plotType: "身份地",
          )),
      MyApply(
          waId: 1,
          yearNo: 2025,
          orgCode: "1",
          operateName: "operateName1",
          operateIdNumber: "operateIdNumber1",
          plotNo: "183010101001A",
          plotName: "183010101001A",
          plotArea: 50.0,
          cropCode: "0",
          cropName: "香蕉",
          usageDate: "2025-01-12",
          waterCons: 1.1,
          drainageStatus: "1",
          amount: 111111,
          plotRel: PlotRel(
            regimentCanal: "181",
            companyCanal: "01",
            mainCanal: "01",
            branchCanal: "02",
            lateralCanal: "08",
            billingMode: "按量计费",
            plotType: "身份地",
          )),
    ]).toJson();

    return mockDelay(RequestNoData(code: 0, data: data));
  }

  @override
  Future<RequestNoData> getWaterAccList(dynamic data,
      {CancelToken? cancelToken}) {
    var data = [
      {
        "accId": 0, //账户id
        "orgCode": "0", //所在单位编码(团场)
        "orgName": "玉米合作社", //单位名称
        "accCode": " 0", //账户编码
        "growerType": 1, //类型同种植信息：1农户；2企业
        "growerName": "张三" //账户名
      },
      {
        "accId": 1, //账户id
        "orgCode": "1", //所在单位编码(团场)
        "orgName": "香蕉合作社", //单位名称
        "accCode": " 0", //账户编码
        "growerType": 1, //类型同种植信息：1农户；2企业
        "growerName": "张三" //账户名
      }
    ];
    return mockDelay(RequestNoData(code: 0, data: data));
  }

  //作物全量列表无需授权
  @override
  Future<RequestNoData> cropList(dynamic data, {CancelToken? cancelToken}) {
    var data = [
      {
        "raiseCropsCd": "2",
        "raiseCropsNm": "玉米",
        "raiseCropsType": "01",
        "raiseCropsTypeNm": "粮食",
        "raiseCropsSubtype": "",
        "raiseCropsSubtypeNm": null,
        "orders": 2
      },
      {
        "raiseCropsCd": "23",
        "raiseCropsNm": "小麦",
        "raiseCropsType": "01",
        "raiseCropsTypeNm": "粮食",
        "raiseCropsSubtype": "",
        "raiseCropsSubtypeNm": null,
        "orders": 4
      },
      {
        "raiseCropsCd": "125",
        "raiseCropsNm": "鲜食玉米",
        "raiseCropsType": "01",
        "raiseCropsTypeNm": "粮食",
        "raiseCropsSubtype": "",
        "raiseCropsSubtypeNm": null,
        "orders": 125
      },
      {
        "raiseCropsCd": "14",
        "raiseCropsNm": "向日葵",
        "raiseCropsType": "02",
        "raiseCropsTypeNm": "经济",
        "raiseCropsSubtype": "",
        "raiseCropsSubtypeNm": null,
        "orders": 14
      },
      {
        "raiseCropsCd": "93",
        "raiseCropsNm": "辣椒",
        "raiseCropsType": "02",
        "raiseCropsTypeNm": "经济",
        "raiseCropsSubtype": "0205",
        "raiseCropsSubtypeNm": "蔬菜",
        "orders": 93
      },
      {
        "raiseCropsCd": "1",
        "raiseCropsNm": "水稻",
        "raiseCropsType": "01",
        "raiseCropsTypeNm": "粮食",
        "raiseCropsSubtype": "",
        "raiseCropsSubtypeNm": null,
        "orders": 1
      },
      {
        "raiseCropsCd": "3",
        "raiseCropsNm": "大豆",
        "raiseCropsType": "01",
        "raiseCropsTypeNm": "粮食",
        "raiseCropsSubtype": "",
        "raiseCropsSubtypeNm": null,
        "orders": 3
      },
      {
        "raiseCropsCd": "35",
        "raiseCropsNm": "甜玉米",
        "raiseCropsType": "01",
        "raiseCropsTypeNm": "粮食",
        "raiseCropsSubtype": "",
        "raiseCropsSubtypeNm": null,
        "orders": 11
      },
      {
        "raiseCropsCd": "12",
        "raiseCropsNm": "杂豆",
        "raiseCropsType": "01",
        "raiseCropsTypeNm": "粮食",
        "raiseCropsSubtype": "0103",
        "raiseCropsSubtypeNm": "杂豆",
        "orders": 12
      },
      {
        "raiseCropsCd": "16",
        "raiseCropsNm": "高梁",
        "raiseCropsType": "01",
        "raiseCropsTypeNm": "粮食",
        "raiseCropsSubtype": "0101",
        "raiseCropsSubtypeNm": "杂粮",
        "orders": 16
      }
    ];
    return mockDelay(RequestNoData(code: 0, data: data));
  }

  @override
  Future<RequestNoData> insertPlantInfo(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDelay(RequestNoData(code: 0, success: true, data: data));
  }

  @override
  Future<RequestNoData> waterApplyHandleAudit(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDelay(RequestNoData(code: 0, success: true, data: data));
  }

  @override
  Future<RequestNoData> waterApplyHandleReceiverSubmit(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDelay(RequestNoData(code: 0, success: true, data: data));
  }

  @override
  Future<RequestNoData> getPaymentRecordInfo(dynamic data,
      {CancelToken? cancelToken}) {
    var data = {
      "recordId": 11111, //缴费编号
      "year": 2025, //年
      "accId": 1, //账号id
      "accName": "张三", //账户名-缴费户名
      "orgName": "农村信用社", //账户所属单位-缴费单位
      "recordType": 2, //记录类型：1 收费 2 退费
      "chargeChannel": 1, //收费渠道：1 线上 2 线下
      "amount": 111.11, //金额
      "chargeMethod": 2, //1 转账 2 银行
      "bankAccount": "", //银行账号
      "isTicketPrinted": 1, //票据是否打印：1 是 0 否
      "isDeposited": 1, //1 缴费成功 0 审核中-缴费状态
      "paymentChannel": "APP", //缴费渠道：1 POS 2 APP
      "createTime": DateTime.now().millisecondsSinceEpoch //缴费时间 时间戳
    };

    return mockDelay(RequestNoData(code: 0, success: true, data: data));
  }

  @override
  Future<RequestNoData> waterApplyList(dynamic data,
      {CancelToken? cancelToken}) {
    var data = <Map>[
      WaterUseResultModel(dateStr: "202501", waterApplyList: [
        MyApply(
            waId: 0,
            yearNo: 2025,
            orgCode: "0",
            operateName: "operateName",
            operateIdNumber: "operateIdNumber",
            plotNo: "183010101001A",
            plotName: "183010101001A",
            plotArea: 50.0,
            cropCode: "0",
            cropName: "玉米",
            usageDate: "2025-01-11",
            waterCons: 1.1,
            drainageStatus: "",
            plotRel: PlotRel(
              regimentCanal: "181",
              companyCanal: "01",
              mainCanal: "01",
              branchCanal: "02",
              lateralCanal: "08",
              billingMode: "按量计费",
              plotType: "身份地",
            )),
        MyApply(
            waId: 1,
            yearNo: 2025,
            orgCode: "1",
            operateName: "operateName1",
            operateIdNumber: "operateIdNumber1",
            plotNo: "183010101001A",
            plotName: "183010101001A",
            plotArea: 50.0,
            cropCode: "0",
            cropName: "香蕉",
            usageDate: "2025-01-12",
            waterCons: 1.1,
            drainageStatus: "",
            amount: 111111,
            plotRel: PlotRel(
              regimentCanal: "181",
              companyCanal: "01",
              mainCanal: "01",
              branchCanal: "02",
              lateralCanal: "08",
              billingMode: "按量计费",
              plotType: "身份地",
            )),
      ]).toJson(),
      WaterUseResultModel(dateStr: "202502", waterApplyList: [
        MyApply(
            waId: 0,
            yearNo: 2025,
            orgCode: "0",
            operateName: "operateName",
            operateIdNumber: "operateIdNumber",
            plotNo: "183010101001A",
            plotName: "183010101001A",
            plotArea: 50.0,
            cropCode: "0",
            cropName: "玉米",
            usageDate: "2025-01-11",
            waterCons: 1.1,
            drainageStatus: "0",
            plotRel: PlotRel(
              regimentCanal: "181",
              companyCanal: "01",
              mainCanal: "01",
              branchCanal: "02",
              lateralCanal: "08",
              billingMode: "按量计费",
              plotType: "身份地",
            )),
        MyApply(
            waId: 1,
            yearNo: 2025,
            orgCode: "1",
            operateName: "operateName1",
            operateIdNumber: "operateIdNumber1",
            plotNo: "183010101001A",
            plotName: "183010101001A",
            plotArea: 50.0,
            cropCode: "0",
            cropName: "香蕉",
            usageDate: "2025-01-12",
            waterCons: 1.1,
            drainageStatus: "1",
            amount: 111111,
            plotRel: PlotRel(
              regimentCanal: "181",
              companyCanal: "01",
              mainCanal: "01",
              branchCanal: "02",
              lateralCanal: "08",
              billingMode: "按量计费",
              plotType: "身份地",
            )),
      ]).toJson(),
      WaterUseResultModel(dateStr: "202502", waterApplyList: [
        MyApply(
            waId: 0,
            yearNo: 2025,
            orgCode: "0",
            operateName: "operateName",
            operateIdNumber: "operateIdNumber",
            plotNo: "183010101001A",
            plotName: "183010101001A",
            plotArea: 50.0,
            cropCode: "0",
            cropName: "玉米",
            usageDate: "2025-01-11",
            waterCons: 1.1,
            drainageStatus: "一方水",
            plotRel: PlotRel(
              regimentCanal: "181",
              companyCanal: "01",
              mainCanal: "01",
              branchCanal: "02",
              lateralCanal: "08",
              billingMode: "按量计费",
              plotType: "身份地",
            )),
        MyApply(
            waId: 1,
            yearNo: 2025,
            orgCode: "1",
            operateName: "operateName1",
            operateIdNumber: "operateIdNumber1",
            plotNo: "183010101001A",
            plotName: "183010101001A",
            plotArea: 50.0,
            cropCode: "0",
            cropName: "香蕉",
            usageDate: "2025-01-12",
            waterCons: 1.1,
            drainageStatus: "1",
            amount: 111111,
            plotRel: PlotRel(
              regimentCanal: "181",
              companyCanal: "01",
              mainCanal: "01",
              branchCanal: "02",
              lateralCanal: "08",
              billingMode: "按量计费",
              plotType: "身份地",
            )),
      ]).toJson(),
    ];

    return mockDelay(RequestNoData(code: 0, success: true, data: data));
  }

  @override
  Future<RequestNoData> waterHandleInfo(dynamic waId,
      {CancelToken? cancelToken}) {
    var data = {
      "waId": 10,
      "yearNo": 2025,
      "orgCode": "8601010101",
      "growerType": "农户",
      "growerName": "老张",
      "idNumber": "230101190898909090",
      "farmerId": 0,
      "companyId": 1234,
      "operateName": "",
      "operatePhone": "",
      "plotNo": "1810101022801715A",
      "plotName": "1810101022801715A",
      "plotArea": 50.0,
      "plotType": "身份地",
      "cropCode": "001",
      "cropName": "玉米",
      "remark": null,
      "usageDate": "2025-10-10",
      "waterCons": 100,
      "actualWaterUsage": 0,
      "waterFreq": 0,
      "waterReceiverId": 1,
      "auditStatus": "1",
      "auditResult": "1",
      "statusCd": null,
      "createBy": 38650,
      "createTime": "2025-02-17 16:19:08",
      "updateBy": 38650,
      "updateTime": "2025-02-19 10:18:06",
      "dataSource": "APP",
      "auditLevel": 1,
      "accId": 123,
      "advanceCollect": 100,
      "amount": 100,
      "accumWaterConsume": 0,
      "firstLevelReviewer": "老张",
      "firstLevelReviewerId": 38650,
      "firstLevelReviewerResult": "审核通过",
      "auditRole": null,
      "params": null,
      "waterReceiverName": "1",
      "canal": "01干02支28斗",
      "drainageStatus": "1",
      "operatorInfoList": [
        {"userName": null, "taskName": "申请用水"}
      ],
      "priceRecords": [
        {
          "priceLevel": "1", //0按面积 1-4分段计费
          "amount": 0, //预估金额
          "waterCons": 0, //用水量
          "priceMin": "", //分段计费左极值
          "priceMax": "", //分段计费右极值
          "priceMinStr": "", //分段计费左极值描述
          "priceMaxStr": "" //分段计费右极值描述
        },
        {
          "priceLevel": "1", //0按面积 1-4分段计费
          "amount": 2, //预估金额
          "waterCons": 3, //用水量
          "priceMin": "", //分段计费左极值
          "priceMax": "", //分段计费右极值
          "priceMinStr": "", //分段计费左极值描述
          "priceMaxStr": "" //分段计费右极值描述
        },
        {
          "priceLevel": "1", //0按面积 1-4分段计费
          "amount": 3, //预估金额
          "waterCons": 3, //用水量
          "priceMin": "", //分段计费左极值
          "priceMax": "", //分段计费右极值
          "priceMinStr": "", //分段计费左极值描述
          "priceMaxStr": "" //分段计费右极值描述
        },
        {
          "priceLevel": "1", //0按面积 1-4分段计费
          "amount": 4, //预估金额
          "waterCons": 4, //用水量
          "priceMin": "", //分段计费左极值
          "priceMax": "", //分段计费右极值
          "priceMinStr": "", //分段计费左极值描述
          "priceMaxStr": "" //分段计费右极值描述
        }
      ],
      "orgName": "福江管理区",
      "orgFullName": "黑龙江省二九〇农场福江管理区"
    };
    return mockDelay(RequestNoData(code: 0, success: true, data: data));
  }

  @override
  Future<RequestNoData> getContractPlot(dynamic data,
      {CancelToken? cancelToken}) {
    var data = [
      {
        "plotType": "1", //类型
        "chargeArea": 11.1, //面积
        "landNumberNo": "1810101022801715A", //地块编码
        "landNumber": "1810101022801715A", //新疆地块名
        "organizationNo": "", //单位编码
        "partnerName": "", //企业名
        "farmerPhone": "", //办理人电话/农户手机号
        "farmerName": "", //代办人姓名/农户姓名
        "farmerIdNumber": "" //证件号
      },
      {
        "plotType": "1", //类型
        "chargeArea": 12.1, //面积
        "landNumberNo": "1810101022801715B", //地块编码
        "landNumber": "1810101022801715B", //新疆地块名
        "organizationNo": "", //单位编码
        "partnerName": "", //企业名
        "farmerPhone": "", //办理人电话/农户手机号
        "farmerName": "", //代办人姓名/农户姓名
        "farmerIdNumber": "" //证件号
      }
    ];
    return mockDelay(RequestNoData(code: 0, success: true, data: data));
  }

  @override
  Future<RequestNoData> estimatePrice(dynamic data,
      {CancelToken? cancelToken}) {
    var data = [
      {
        "priceLevel": 1, //分段计费枚举 1-4逐级增加
        "amount": 6 //金额
      },
      {
        "priceLevel": 2, //分段计费枚举 1-4逐级增加
        "amount": 1 //金额
      },
      {
        "priceLevel": 3, //分段计费枚举 1-4逐级增加
        "amount": 2 //金额
      },
      {
        "priceLevel": 4, //分段计费枚举 1-4逐级增加
        "amount": 3 //金额
      }
    ];
    return mockDelay(RequestNoData(code: 0, success: true, data: data),
        seconds: 10);
  }

  @override
  Future<RequestNoData> waterApplyInsert(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDelay(RequestNoData(code: 0, success: true, data: data));
  }

  @override
  Future<RequestNoData> waterApplyHandleReceiverReject(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDelay(RequestNoData(code: 0, success: true, data: data));
  }

  // @override
  Future<RequestNoData> waterApplyQueryByPage(dynamic data,
      {CancelToken? cancelToken}) {
    int page = data["page"] as int;
    int rows = data["rows"] as int;

    var records = List.generate(rows, (i) => i).map((i) {
      return {
        "waId": page * rows + i,
        "yearNo": 2025,
        "plotNo": "1810101022801715A${page * rows + i}",
        "plotName": "1810101022801715A${page * rows + i}",
        "usageDate": "2024-12-12",
        "createTime": "2025-02-17 16:16:52",
        "waterCons": 100,
        "canal": "01干02支28斗",
        "orgName": "福江管理区",
        "orgFullName": "黑龙江省二九〇农场福江管理区"
      };
    }).toList();
    var result =
        PageModel(size: 10, total: 30, pages: 3, records: records).toJson();

    return mockDelay(RequestNoData(code: 0, success: true, data: result));
  }

  Future<RequestNoData> mockDelay(RequestNoData data, {int seconds = 1}) {
    return Future.delayed(Duration(seconds: seconds), () => data);
  }

  Future<RequestNoData> mockContainingList(dynamic data,
      {CancelToken? cancelToken}) {
    int page = data["page"] as int;
    int rows = data["rows"] as int;

    var records = List.generate(rows, (i) => i).map((i) {
      return {
        "waId": page * rows + i,
        "yearNo": 2025,
        "orgCode": "8601010101",
        "growerType": "农户",
        "growerName": "老张",
        "idNumber": "230101190898909090",
        "farmerId": 0,
        "companyId": 1234,
        "operateName": "",
        "operatePhone": "10086",
        "plotNo": "1810101022801715A${page * rows + i}",
        "plotName": "1810101022801715A",
        "plotArea": 50.0,
        "plotType": "1",
        "cropCode": "001",
        "cropName": "玉米",
        "remark": null,
        "usageDate": "2025-10-09",
        "waterCons": 0,
        "actualWaterUsage": 10,
        "waterFreq": 0,
        "waterReceiverId": 1,
        "auditStatus": "1",
        "auditResult": "1",
        "statusCd": null,
        "createBy": 38650,
        "createTime": "2025-02-17 16:19:08",
        "updateBy": 38650,
        "updateTime": "2025-02-19 10:18:06",
        "dataSource": "APP",
        "auditLevel": 1,
        "accId": 123,
        "advanceCollect": 100,
        "amount": 100,
        "accumWaterConsume": 0,
        "firstLevelReviewer": "老张",
        "firstLevelReviewerId": 38650,
        "firstLevelReviewerResult": "审核通过",
        "auditRole": null,
        "params": null,
        "waterReceiverName": "老张2",
        "canal": "01干02支28斗",
        "drainageStatus": "1",
        "operatorInfoList": [
          {"userName": "老张1", "taskName": "申请用水"},
          {"userName": "老张2", "taskName": "申请用水"},
          {"userName": "老张3", "taskName": "申请用水"},
          {"userName": "老张4", "taskName": "申请用水"}
        ],
        "priceRecords": [
          {
            "priceLevel": "1", //0按面积 1-4分段计费
            "amount": 0, //预估金额
            "waterCons": 0, //用水量
            "priceMin": "", //分段计费左极值
            "priceMax": "", //分段计费右极值
            "priceMinStr": "", //分段计费左极值描述
            "priceMaxStr": "" //分段计费右极值描述
          }
        ],
        "orgName": "福江管理区",
        "orgFullName": "黑龙江省二九〇农场福江管理区"
      };
    }).toList();
    var result =
        PageModel(size: 10, total: 30, pages: 3, records: records).toJson();
    return mockDelay(RequestNoData(code: 0, success: true, data: result));
  }

  @override
  Future<RequestNoData> waterApplyHandleReceiverPendingNumber(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDelay(RequestNoData(code: 0, success: true, data: 10));
  }

  @override
  Future<RequestNoData> waterApplyHandleDistributorSubmit(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDelay(RequestNoData(code: 0, success: true, data: 10));
  }

  @override
  Future<RequestNoData> waterApplyHandleReceiverRunningPage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockContainingList(data);
  }

  @override
  Future<RequestNoData> waterApplyHandleReceiverPendingPage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockContainingList(data);
  }

  @override
  Future<RequestNoData> waterApplyHandleReceiverDonePage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockContainingList(data);
  }

  @override
  Future<RequestNoData> canalList(dynamic data, {CancelToken? cancelToken}) {
    String name = "branchCanal";

    if (data["canalType"] == 0) {
      name = "mainCanal";
    } else if (data["canalType"] == 1) {
      name = "branchCanal";
    } else if (data["canalType"] == 2) {
      name = "lateralCanal";
    }

    var result = [
      {name: "01"},
      {name: "02"},
      {name: "03"}
    ];

    return mockDelay(RequestNoData(code: 0, success: true, data: result));
  }

  Future<RequestNoData> mockDistributionList(dynamic data,
      {CancelToken? cancelToken}) {
    int page = data["page"] as int;
    int rows = data["rows"] as int;

    var records = List.generate(rows, (i) => i).map((i) {
      return {
        "applicantNumber": 0,
        "auditStatus": "处理中",
        "createTime": "2025-02-17 16:19:08",
        "orgCode": "01",
        "orgFullName": "农村信用社",
        "orgName": "农村信用社",
        "plotNumber": 0,
        "updateTime": "2025-02-17",
        "usageBeginDate": "2025-02-17",
        "usageEndDate": "2025-02-17",
        "waterUsage": 100,
        "wrId": page * rows + i,
        "mainCanal": "01",
        "branchCanal": "01",
        "lateralCanal": "01",
        "releaseStatus": "处理中"
      };
    }).toList();
    var result =
        PageModel(size: 10, total: 30, pages: 3, records: records).toJson();
    return mockDelay(RequestNoData(code: 0, success: true, data: result));
  }

  @override
  Future<RequestNoData> waterApplyHandleRegulatorSubmit(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDelay(RequestNoData(code: 0, success: true, data: 10));
  }

  @override
  Future<RequestNoData> waterApplyHandleDistributorPendingNumber(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDelay(RequestNoData(code: 0, success: true, data: 10));
  }

  @override
  Future<RequestNoData> waterApplyHandleRegulatorPendingPage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDistributionList(data, cancelToken: cancelToken);
  }

  @override
  Future<RequestNoData> waterApplyHandleRegulatorRunningPage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDistributionList(data, cancelToken: cancelToken);
  }

  @override
  Future<RequestNoData> waterApplyHandleRegulatorDonePage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDistributionList(data, cancelToken: cancelToken);
  }

  @override
  Future<RequestNoData> waterApplyHandleDistributorPendingPage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDistributionList(data, cancelToken: cancelToken);
  }

  @override
  Future<RequestNoData> waterApplyHandleDistributorMonitorPage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDistributionList(data, cancelToken: cancelToken);
  }

  @override
  Future<RequestNoData> waterApplyHandleDistributorRunningPage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDistributionList(data, cancelToken: cancelToken);
  }

  @override
  Future<RequestNoData> waterApplyHandleDistributorReleaseInfo(dynamic data,
      {CancelToken? cancelToken}) {
    var result = {
      "applicantNumber": 0,
      "branchCanal": "",
      "createTime": "",
      "lateralCanal": "",
      "mainCanal": "",
      "orgCode": "",
      "orgFullName": "",
      "orgName": "",
      "plotNumber": 0,
      "usageBeginDate": "2025-02-17",
      "usageEndDate": "2025-02-17",
      "releaseStatus": "处理中",
      "waterApplies": [
        {
          "accId": 0,
          "accumWaterConsume": 0,
          "actualWaterUsage": 100,
          "advanceCollect": 0,
          "amount": 0,
          "auditResult": "",
          "auditStatus": "",
          "canal": "01干01支01斗",
          "createTime": "2025-02-17",
          "cropCode": "",
          "cropName": "",
          "drainageStatus": "",
          "farmerId": 0,
          "firstLevelReviewer": "第一审核人姓名",
          "firstLevelReviewerResult": "第一审核人结果通过",
          "growerName": "",
          "growerType": "",
          "idNumber": "",
          "operateName": "",
          "operatePhone": "",
          "operatorInfoList": [
            {"taskName": "", "userName": ""}
          ],
          "orgCode": "",
          "orgFullName": "",
          "orgName": "农村信用社",
          "plotArea": 10,
          "plotName": "",
          "plotNo": "",
          "plotType": "",
          "usageDate": "2025-02-17",
          "waId": 0,
          "waterCons": 0,
          "waterFreq": 0,
          "waterReceiverId": 0,
          "waterReceiverName": "zhan",
          "yearNo": 0
        },
        {
          "accId": 0,
          "accumWaterConsume": 0,
          "actualWaterUsage": 0,
          "advanceCollect": 0,
          "amount": 0,
          "auditResult": "",
          "auditStatus": "",
          "canal": "干支斗",
          "createTime": "",
          "cropCode": "",
          "cropName": "",
          "drainageStatus": "",
          "farmerId": 0,
          "firstLevelReviewer": "第一审核人姓名",
          "firstLevelReviewerResult": "第一审核人结果通过",
          "growerName": "",
          "growerType": "",
          "idNumber": "",
          "operateName": "",
          "operatePhone": "",
          "operatorInfoList": [
            {"taskName": "", "userName": ""}
          ],
          "orgCode": "",
          "orgFullName": "",
          "orgName": "",
          "plotArea": 0,
          "plotName": "",
          "plotNo": "",
          "plotType": "",
          "usageDate": "",
          "waId": 0,
          "waterCons": 0,
          "waterFreq": 0,
          "waterReceiverId": 0,
          "waterReceiverName": "",
          "yearNo": 0
        },
        {
          "accId": 0,
          "accumWaterConsume": 0,
          "actualWaterUsage": 0,
          "advanceCollect": 0,
          "amount": 0,
          "auditResult": "",
          "auditStatus": "",
          "canal": "干支斗",
          "createTime": "",
          "cropCode": "",
          "cropName": "",
          "drainageStatus": "",
          "farmerId": 0,
          "firstLevelReviewer": "第一审核人姓名",
          "firstLevelReviewerResult": "第一审核人结果通过",
          "growerName": "",
          "growerType": "",
          "idNumber": "",
          "operateName": "",
          "operatePhone": "",
          "operatorInfoList": [
            {"taskName": "", "userName": ""}
          ],
          "orgCode": "",
          "orgFullName": "",
          "orgName": "",
          "plotArea": 0,
          "plotName": "",
          "plotNo": "",
          "plotType": "",
          "usageDate": "",
          "waId": 0,
          "waterCons": 0,
          "waterFreq": 0,
          "waterReceiverId": 0,
          "waterReceiverName": "",
          "yearNo": 0
        }
      ],
      "waterDistributor": {
        "staffName": "调度员姓名",
      },
      "waterUsage": 100,
      "wrId": 0
    };
    return mockDelay(RequestNoData(code: 0, success: true, data: result));
  }

  @override
  Future<RequestNoData> waterApplyHandleDistributorDonePage(dynamic data,
      {CancelToken? cancelToken}) {
    return mockDistributionList(data, cancelToken: cancelToken);
  }
}
