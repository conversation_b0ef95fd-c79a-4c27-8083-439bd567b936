import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../utils/dialog_extensions.dart';
import 'const_dict.dart';
import 'model/page_model.dart';
import 'model/water_apply_item_model.dart';
import 'request/xinjiang_water_manage_service.dart';
import 'water_apply_detail_page.dart';
import 'widget/bdh_steps2.dart';
import 'widget/distribution_apply_item_review_widget.dart';

class WaterDistributionApplyReviewNextPage extends StatefulWidget {
  final Map<String, dynamic> item;
  const WaterDistributionApplyReviewNextPage({super.key, required this.item});

  @override
  State<WaterDistributionApplyReviewNextPage> createState() =>
      _WaterDistributionApplyReviewNextPageState();
}

class _WaterDistributionApplyReviewNextPageState
    extends State<WaterDistributionApplyReviewNextPage>
    with AutoDisposeStateMixin, MixinDefaultWidget {
  late final ScrollController _scrollController;

  late final _Controller controller;

  @override
  void initState() {
    super.initState();

    _scrollController = useScrollController(ScrollController());
    _scrollController.addListener(_scrollListener);
    controller = useChangeNotifier(_Controller(context, widget.item))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  PreferredSizeWidget _widgetAppBar() {
    return AppBar(
      toolbarHeight: kTextTabBarHeight,
      title: const Text("放水记录"),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _widgetAppBar(),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: controller.isLoading ? widgetLoading() : _widgetBody()),
    );
  }

  Widget _widgetBottom() {
    return Container(
        color: Colors.white,
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.only(
            left: 24.px, right: 12.px, bottom: 12.px, top: 12.px),
        child: LayoutBuilder(builder: (context, constraints) {
          return SizedBox(
              width: constraints.maxWidth,
              child: Row(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          BdhRadio(
                            iconSize: 16.px,
                            checked: controller.checkedAll,
                            onCheckBoxChanged: (selected) {
                              if (selected == true) {
                                controller.checkAll();
                              } else {
                                controller.uncheckAll();
                              }
                            },
                          ),
                          SizedBox(
                            width: 3.px,
                          ),
                          Text(
                            "全选",
                            style: TextStyle(
                                fontSize: 16.px,
                                color: const Color.fromRGBO(51, 51, 51, 1),
                                fontWeight: FontWeight.w600),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 6.px,
                      ),
                      Text.rich(
                        TextSpan(children: [
                          const TextSpan(
                              text: "地块数: ",
                              style: TextStyle(
                                  color: Color.fromRGBO(51, 51, 51, 0.4))),
                          TextSpan(
                              text: "${controller.plotCountTotal}",
                              style: const TextStyle(
                                color: Color.fromRGBO(51, 51, 51, 1),
                              ))
                        ]),
                        style: TextStyle(
                            fontSize: 13.px, fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                  SizedBox(
                    width: 15.px,
                  ),
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text.rich(
                        TextSpan(children: [
                          const TextSpan(
                              text: "申请用水量: ",
                              style: TextStyle(
                                color: Color.fromRGBO(51, 51, 51, 0.4),
                              )),
                          TextSpan(
                              text: "${controller.waterUseTotal}m³",
                              style: const TextStyle(
                                color: Color.fromRGBO(51, 51, 51, 1),
                              ))
                        ]),
                        style: TextStyle(
                            fontSize: 13.px, fontWeight: FontWeight.w500),
                      ),
                      SizedBox(
                        height: 6.px,
                      ),
                      Text.rich(
                        TextSpan(children: [
                          const TextSpan(
                              text: "申请人员数量: ",
                              style: TextStyle(
                                  color: Color.fromRGBO(51, 51, 51, 0.4))),
                          TextSpan(
                              text: "${controller.waterApplyTotal}",
                              style: const TextStyle(
                                color: Color.fromRGBO(51, 51, 51, 1),
                              ))
                        ]),
                        style: TextStyle(
                            fontSize: 13.px, fontWeight: FontWeight.w500),
                      ),
                    ],
                  )),
                  BdhTextButton(
                    width: 120.px,
                    height: 40.px,
                    text: '提交到调度员',
                    textFontWeight: FontWeight.w500,
                    textSize: 13.px,
                    borderRadius: BorderRadius.all(Radius.circular(22.px)),
                    backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
                    disableBackgroundColor: Colors.grey.shade400,
                    pressedBackgroundColor:
                        const Color.fromRGBO(16, 164, 85, 1),
                    foregroundColor: Colors.white,
                    disableForegroundColor: Colors.white,
                    pressedForegroundColor: Colors.white,
                    onPressed: controller.checkedItems.isEmpty
                        ? null
                        : controller.onClickReview,
                  )
                ],
              ));
        }));
  }

  void _scrollListener() async {
    if (!mounted) {
      return;
    }
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      Log.d("_scrollController scroll to bottom");

      controller.loadMore();
    }
  }

  Widget _widgetStep() {
    return Container(
        padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
        margin: EdgeInsets.only(bottom: 6.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: BdhStepsHorizontal(
          steps: waterApplyReviewStepDict,
          activeIndex: 1,
          outerIconSize: 30.px,
          innerIconSize: 24.px,
        ));
  }

  Widget _widgetBody() {
    return Column(
      children: [
        _widgetStep(),
        Expanded(
            child: RefreshIndicator(
                color: const Color.fromRGBO(2, 139, 93, 1),
                onRefresh: controller.refresh,
                child: Scrollbar(
                    controller: _scrollController,
                    child: CustomScrollView(
                      controller: _scrollController,
                      physics: const ClampingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      slivers: [
                        if (controller.items.isNotEmpty) ...[
                          _widgetList(),
                          SliverToBoxAdapter(
                            child: _loadMore(context),
                          ),
                        ],
                        if (controller.items.isEmpty)
                          SliverFillRemaining(
                              hasScrollBody: false, child: widgetEmpty()),
                      ],
                    )))),
        if (controller.items.isNotEmpty) _widgetBottom()
      ],
    );
  }

  //用水记录列表
  Widget _widgetList() {
    return SliverList.builder(
      itemBuilder: (context, index) {
        return DistributionApplyItemReviewWidget(
          checked: controller.isChecked(index),
          checkable: true,
          item: controller.items[index],
          onPressed: controller.onClickItem,
          onPressedPhone: controller.onPressedPhone,
          onCheck: controller.onItemCheck,
          index: index,
        );
      },
      itemCount: controller.items.length,
    );
  }

  Widget _loadMore(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 20.px, right: 20.px, bottom: 5.px),
      child: Text(
        controller.needLoadMore ? "加载更多" : "没有更多数据了",
        style: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(24, 66, 56, 0.4)),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _Controller extends AutoDisposeChangeNotifier
    with
        LoadMoreChangeNotifier<WaterApplyItem>,
        MultiCheckChangeNotifier<WaterApplyItem> {
  @override
  final BuildContext context;

  final Map<String, dynamic> map;

  _Controller(this.context, this.map);

  //加载数据
  void loadData() {
    reload(showLoading: true);
  }

  //当前 item
  @override
  List<WaterApplyItem> items = [];

  @override
  List<WaterApplyItem> get multiCheckableItems => items;

  @override
  Future reloadFuture(
      {required bool showLoading,
      required bool loadingMore,
      required bool refresh}) {
    var data = {
      "auditLevel": 1,
      "usageBeginDate": map["usageBeginDate"],
      "usageEndDate": map["usageEndDate"],
      "page": page,
      "rows": row,
    };

    if (map["orgCode"] != null) {
      data["orgCode"] = map["orgCode"];
    }

    if (map["mainCanals"] != null) {
      data["mainCanals"] = map["mainCanals"];
    }

    if (map["branchCanals"] != null) {
      data["branchCanals"] = map["branchCanals"];
    }

    if (map["lateralCanals"] != null) {
      data["lateralCanals"] = map["lateralCanals"];
    }

    return XinJiangWaterManageService()
        .waterApplyHandleDistributorPendingPage(data,
            cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true) {
        var page = PageModel.fromJson(result.data);
        var loadItems = page.records?.map<WaterApplyItem>((item) {
              return WaterApplyItem.fromJson(item);
            }).toList() ??
            [];

        total = page.total ?? 0;
        if (refresh || showLoading) {
          items.clear();
          checkedItems.clear();
          plotCountTotal = 0;
          waterApplyTotal = 0;
          waterUseTotal = 0;
        }
        Log.d("load success ");
        items.addAll(loadItems);
      }
      loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        loadingStatus = LoadingStatus.success;
      });
    });
  }

  void onClickItem(WaterApplyItem item) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => WaterApplyDetailPage(
                  waId: item.waId!,
                  showApproval: false,
                )))
        .then((result) {
      if (result == true) {
        reload(showLoading: true);
      }
    });
  }

  int plotCountTotal = 0;
  num waterApplyTotal = 0;
  num waterUseTotal = 0;

  @override
  void checkChangeCallback() {
    plotCountTotal = 0;
    waterApplyTotal = 0;
    waterUseTotal = 0;
    List plotList = [];

    List waterApplyList = [];

    for (var item in checkedItems) {
      waterUseTotal += item.actualWaterUsage ?? 0;

      if (item.plotNo != null && !plotList.contains(item.plotNo)) {
        plotList.add(item.plotNo);
      }

      if (item.receiverName != null &&
          !waterApplyList.contains(item.receiverName)) {
        waterApplyList.add(item.receiverName);
      }
    }
    //waterUseTotal = waterUseTotal * 100;
    waterApplyTotal = waterApplyList.length;
    plotCountTotal = plotList.length;
  }

  void onPressedPhone(WaterApplyItem item) {
    if (item.operatePhone?.isEmpty ?? true) {
      return;
    }

    launchUrl(Uri.parse("tel:${item.operatePhone}"));
  }

  void onClickReview() {
    if (checkedItems.isEmpty) {
      return;
    }

    showConfirmDialog(context, message: "确认提交吗?", cancel: "取消", confirm: "确认")
        .then((result) {
      if (result == true) {
        if (!context.mounted) {
          return;
        }
        var checkedIds = checkedItems.map((item) => item.waId).toList();

        var data = <String, dynamic>{
          "waIds": checkedIds,
          "waterUsage": waterUseTotal,
          "plotNumber": plotCountTotal,
          "applicantNumber": waterApplyTotal,
        };

        data.addAll(map);
        Log.d("data is $data");

        showLoading(context, content: "正在提交 ", barrierDismissible: false);
        XinJiangWaterManageService()
            .waterApplyHandleDistributorSubmit(data,
                cancelToken: createCancelToken())
            .then((result) {
          if (!context.mounted) {
            return;
          }
          BrnLoadingDialog.dismiss(context);
          if (result.success == true && result.code == 0) {
            showToast("提交成功");
            Navigator.maybePop(context, true);
          }
        }).onError((error, stackTrace) {
          handleError(error, stackTrace, errorDo: () {
            BrnLoadingDialog.dismiss(context);
          });
        });
      }
    });
  }
}
