import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/widget/bdh_steps2.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class FlowPlantingApplyInfoPage extends StatefulWidget {
  const FlowPlantingApplyInfoPage({super.key});

  @override
  State<FlowPlantingApplyInfoPage> createState() =>
      _FlowPlantingApplyInfoPageState();
}

class _FlowPlantingApplyInfoPageState extends State<FlowPlantingApplyInfoPage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("查看流程"),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: CustomScrollView(
            slivers: [
              SliverPadding(
                padding: EdgeInsets.all(20.px),
                sliver: SliverToBoxAdapter(
                    child: BdhStepsVertical(
                        outerIconSize: 30.px,
                        innerIconSize: 24.px,
                        steps: [
                          BdhStepsItemData(
                              title: "提交审核", content: "2024-10-08 06:54"),
                          BdhStepsItemData(
                              title: "连队管理人员审核", content: "2024-10-08 06:54"),
                          BdhStepsItemData(
                              title: "团场审批", content: "2024-10-08 06:54")
                        ],
                        activeIndex: 1)),
              )
            ],
          )),
    );
  }
}
