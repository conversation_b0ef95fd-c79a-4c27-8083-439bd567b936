class CropItem {
  final String? raiseCropsCd;
  final String? raiseCropsNm;
  final String? raiseCropsType;
  final String? raiseCropsTypeNm;
  final String? raiseCropsSubtype;
  final dynamic raiseCropsSubtypeNm;
  final int? orders;
  const CropItem(
      {this.raiseCropsCd,
      this.raiseCropsNm,
      this.raiseCropsType,
      this.raiseCropsTypeNm,
      this.raiseCropsSubtype,
      this.raiseCropsSubtypeNm,
      this.orders});
  CropItem copyWith(
      {String? raiseCropsCd,
      String? raiseCropsNm,
      String? raiseCropsType,
      String? raiseCropsTypeNm,
      String? raiseCropsSubtype,
      dynamic? raiseCropsSubtypeNm,
      int? orders}) {
    return CropItem(
        raiseCropsCd: raiseCropsCd ?? this.raiseCropsCd,
        raiseCropsNm: raiseCropsNm ?? this.raiseCropsNm,
        raiseCropsType: raiseCropsType ?? this.raiseCropsType,
        raiseCropsTypeNm: raiseCropsTypeNm ?? this.raiseCropsTypeNm,
        raiseCropsSubtype: raiseCropsSubtype ?? this.raiseCropsSubtype,
        raiseCropsSubtypeNm: raiseCropsSubtypeNm ?? this.raiseCropsSubtypeNm,
        orders: orders ?? this.orders);
  }

  Map<String, Object?> to<PERSON><PERSON>() {
    return {
      'raiseCropsCd': raiseCropsCd,
      'raiseCropsNm': raiseCropsNm,
      'raiseCropsType': raiseCropsType,
      'raiseCropsTypeNm': raiseCropsTypeNm,
      'raiseCropsSubtype': raiseCropsSubtype,
      'raiseCropsSubtypeNm': raiseCropsSubtypeNm,
      'orders': orders
    };
  }

  static CropItem fromJson(Map<String, Object?> json) {
    return CropItem(
        raiseCropsCd: json['raiseCropsCd'] == null
            ? null
            : json['raiseCropsCd'] as String,
        raiseCropsNm: json['raiseCropsNm'] == null
            ? null
            : json['raiseCropsNm'] as String,
        raiseCropsType: json['raiseCropsType'] == null
            ? null
            : json['raiseCropsType'] as String,
        raiseCropsTypeNm: json['raiseCropsTypeNm'] == null
            ? null
            : json['raiseCropsTypeNm'] as String,
        raiseCropsSubtype: json['raiseCropsSubtype'] == null
            ? null
            : json['raiseCropsSubtype'] as String,
        raiseCropsSubtypeNm: json['raiseCropsSubtypeNm'] as dynamic,
        orders: json['orders'] == null ? null : json['orders'] as int);
  }

  @override
  String toString() {
    return '''CropItem(
                raiseCropsCd:$raiseCropsCd,
raiseCropsNm:$raiseCropsNm,
raiseCropsType:$raiseCropsType,
raiseCropsTypeNm:$raiseCropsTypeNm,
raiseCropsSubtype:$raiseCropsSubtype,
raiseCropsSubtypeNm:$raiseCropsSubtypeNm,
orders:$orders
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is CropItem &&
        other.runtimeType == runtimeType &&
        other.raiseCropsCd == raiseCropsCd &&
        other.raiseCropsNm == raiseCropsNm &&
        other.raiseCropsType == raiseCropsType &&
        other.raiseCropsTypeNm == raiseCropsTypeNm &&
        other.raiseCropsSubtype == raiseCropsSubtype &&
        other.raiseCropsSubtypeNm == raiseCropsSubtypeNm &&
        other.orders == orders;
  }

  @override
  int get hashCode {
    return Object.hash(runtimeType, raiseCropsCd, raiseCropsNm, raiseCropsType,
        raiseCropsTypeNm, raiseCropsSubtype, raiseCropsSubtypeNm, orders);
  }
}
