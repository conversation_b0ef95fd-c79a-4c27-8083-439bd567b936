class WaterApplyDetailModel {
  final num? waId;
  final num? yearNo;
  final String? orgCode;
  final String? growerType;
  final String? growerName;
  final String? idNumber;
  final num? farmerId;
  final num? companyId;
  final String? operateName;
  final String? operatePhone;
  final String? plotNo;
  final String? plotName;
  final double? plotArea;
  final String? plotType;
  final String? cropCode;
  final String? cropName;
  final dynamic remark;
  final String? usageDate;
  final num? waterCons;
  final num? actualWaterUsage;
  final num? waterFreq;
  final num? waterReceiverId;
  final String? auditStatus;
  final String? auditResult;
  final dynamic statusCd;
  final num? createBy;
  final String? createTime;
  final num? updateBy;
  final String? updateTime;
  final String? dataSource;
  final num? auditLevel;
  final num? accId;
  final num? advanceCollect;
  final num? amount;
  final num? accumWaterConsume;
  final String? firstLevelReviewer;
  final num? firstLevelReviewerId;
  final String? firstLevelReviewerResult;
  final dynamic auditRole;
  final dynamic params;
  final String? receiverName;
  final String? distributorName;
  final String? regulatorName;
  final String? canal;
  final String? drainageStatus;
  final List<OperatorInfoList>? operatorInfoList;
  final List<PriceRecords>? priceRecords;
  final String? orgName;
  final String? orgFullName;
  final String? usageEndDate;
  final String? usageBeginDate;
  final String? actualUsageDate;
  const WaterApplyDetailModel(
      {this.waId,
      this.yearNo,
      this.orgCode,
      this.growerType,
      this.growerName,
      this.idNumber,
      this.farmerId,
      this.companyId,
      this.operateName,
      this.operatePhone,
      this.plotNo,
      this.plotName,
      this.plotArea,
      this.plotType,
      this.cropCode,
      this.cropName,
      this.remark,
      this.usageDate,
      this.waterCons,
      this.actualWaterUsage,
      this.waterFreq,
      this.waterReceiverId,
      this.auditStatus,
      this.auditResult,
      this.statusCd,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.dataSource,
      this.auditLevel,
      this.accId,
      this.advanceCollect,
      this.amount,
      this.accumWaterConsume,
      this.firstLevelReviewer,
      this.firstLevelReviewerId,
      this.firstLevelReviewerResult,
      this.auditRole,
      this.params,
      this.receiverName,
      this.distributorName,
      this.regulatorName,
      this.canal,
      this.drainageStatus,
      this.operatorInfoList,
      this.priceRecords,
      this.orgName,
      this.orgFullName,
      this.actualUsageDate,
      this.usageBeginDate,
      this.usageEndDate});
  WaterApplyDetailModel copyWith({
    num? waId,
    num? yearNo,
    String? orgCode,
    String? growerType,
    String? growerName,
    String? idNumber,
    num? farmerId,
    num? companyId,
    String? operateName,
    String? operatePhone,
    String? plotNo,
    String? plotName,
    double? plotArea,
    String? plotType,
    String? cropCode,
    String? cropName,
    dynamic? remark,
    String? usageDate,
    num? waterCons,
    num? actualWaterUsage,
    num? waterFreq,
    num? waterReceiverId,
    String? auditStatus,
    String? auditResult,
    dynamic? statusCd,
    num? createBy,
    String? createTime,
    num? updateBy,
    String? updateTime,
    String? dataSource,
    num? auditLevel,
    num? accId,
    num? advanceCollect,
    num? amount,
    num? accumWaterConsume,
    String? firstLevelReviewer,
    num? firstLevelReviewerId,
    String? firstLevelReviewerResult,
    dynamic? auditRole,
    dynamic? params,
    String? receiverName,
    String? distributorName,
    String? regulatorName,
    String? canal,
    String? drainageStatus,
    List<OperatorInfoList>? operatorInfoList,
    List<PriceRecords>? priceRecords,
    String? orgName,
    String? orgFullName,
    String? usageEndDate,
    String? usageBeginDate,
    String? actualUsageDate,
  }) {
    return WaterApplyDetailModel(
      waId: waId ?? this.waId,
      yearNo: yearNo ?? this.yearNo,
      orgCode: orgCode ?? this.orgCode,
      growerType: growerType ?? this.growerType,
      growerName: growerName ?? this.growerName,
      idNumber: idNumber ?? this.idNumber,
      farmerId: farmerId ?? this.farmerId,
      companyId: companyId ?? this.companyId,
      operateName: operateName ?? this.operateName,
      operatePhone: operatePhone ?? this.operatePhone,
      plotNo: plotNo ?? this.plotNo,
      plotName: plotName ?? this.plotName,
      plotArea: plotArea ?? this.plotArea,
      plotType: plotType ?? this.plotType,
      cropCode: cropCode ?? this.cropCode,
      cropName: cropName ?? this.cropName,
      remark: remark ?? this.remark,
      usageDate: usageDate ?? this.usageDate,
      waterCons: waterCons ?? this.waterCons,
      actualWaterUsage: actualWaterUsage ?? this.actualWaterUsage,
      waterFreq: waterFreq ?? this.waterFreq,
      waterReceiverId: waterReceiverId ?? this.waterReceiverId,
      auditStatus: auditStatus ?? this.auditStatus,
      auditResult: auditResult ?? this.auditResult,
      statusCd: statusCd ?? this.statusCd,
      createBy: createBy ?? this.createBy,
      createTime: createTime ?? this.createTime,
      updateBy: updateBy ?? this.updateBy,
      updateTime: updateTime ?? this.updateTime,
      dataSource: dataSource ?? this.dataSource,
      auditLevel: auditLevel ?? this.auditLevel,
      accId: accId ?? this.accId,
      advanceCollect: advanceCollect ?? this.advanceCollect,
      amount: amount ?? this.amount,
      accumWaterConsume: accumWaterConsume ?? this.accumWaterConsume,
      firstLevelReviewer: firstLevelReviewer ?? this.firstLevelReviewer,
      firstLevelReviewerId: firstLevelReviewerId ?? this.firstLevelReviewerId,
      firstLevelReviewerResult:
          firstLevelReviewerResult ?? this.firstLevelReviewerResult,
      auditRole: auditRole ?? this.auditRole,
      params: params ?? this.params,
      receiverName: receiverName ?? this.receiverName,
      distributorName: distributorName ?? this.distributorName,
      regulatorName: regulatorName ?? this.regulatorName,
      canal: canal ?? this.canal,
      drainageStatus: drainageStatus ?? this.drainageStatus,
      operatorInfoList: operatorInfoList ?? this.operatorInfoList,
      priceRecords: priceRecords ?? this.priceRecords,
      orgName: orgName ?? this.orgName,
      orgFullName: orgFullName ?? this.orgFullName,
      usageBeginDate: usageBeginDate ?? this.usageBeginDate,
      usageEndDate: usageEndDate ?? this.usageEndDate,
      actualUsageDate: actualUsageDate ?? this.actualUsageDate,
    );
  }

  Map<String, Object?> toJson() {
    return {
      'waId': waId,
      'yearNo': yearNo,
      'orgCode': orgCode,
      'growerType': growerType,
      'growerName': growerName,
      'idNumber': idNumber,
      'farmerId': farmerId,
      'companyId': companyId,
      'operateName': operateName,
      'operatePhone': operatePhone,
      'plotNo': plotNo,
      'plotName': plotName,
      'plotArea': plotArea,
      'plotType': plotType,
      'cropCode': cropCode,
      'cropName': cropName,
      'remark': remark,
      'usageDate': usageDate,
      'waterCons': waterCons,
      'actualWaterUsage': actualWaterUsage,
      'waterFreq': waterFreq,
      'waterReceiverId': waterReceiverId,
      'auditStatus': auditStatus,
      'auditResult': auditResult,
      'statusCd': statusCd,
      'createBy': createBy,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'dataSource': dataSource,
      'auditLevel': auditLevel,
      'accId': accId,
      'advanceCollect': advanceCollect,
      'amount': amount,
      'accumWaterConsume': accumWaterConsume,
      'firstLevelReviewer': firstLevelReviewer,
      'firstLevelReviewerId': firstLevelReviewerId,
      'firstLevelReviewerResult': firstLevelReviewerResult,
      'auditRole': auditRole,
      'params': params,
      'receiverName': receiverName,
      'distributorName': distributorName,
      'regulatorName': regulatorName,
      'canal': canal,
      'drainageStatus': drainageStatus,
      'operatorInfoList': operatorInfoList
          ?.map<Map<String, dynamic>>((data) => data.toJson())
          .toList(),
      'priceRecords': priceRecords
          ?.map<Map<String, dynamic>>((data) => data.toJson())
          .toList(),
      'orgName': orgName,
      'orgFullName': orgFullName,
      'usageBeginDate': usageBeginDate,
      'usageEndDate': usageEndDate,
      'actualUsageDate': actualUsageDate,
    };
  }

  static WaterApplyDetailModel fromJson(Map<String, Object?> json) {
    return WaterApplyDetailModel(
      waId: json['waId'] == null ? null : json['waId'] as num,
      yearNo: json['yearNo'] == null ? null : json['yearNo'] as num,
      orgCode: json['orgCode'] == null ? null : json['orgCode'] as String,
      growerType:
          json['growerType'] == null ? null : json['growerType'] as String,
      growerName:
          json['growerName'] == null ? null : json['growerName'] as String,
      idNumber: json['idNumber'] == null ? null : json['idNumber'] as String,
      farmerId: json['farmerId'] == null ? null : json['farmerId'] as num,
      companyId: json['companyId'] == null ? null : json['companyId'] as num,
      operateName:
          json['operateName'] == null ? null : json['operateName'] as String,
      operatePhone:
          json['operatePhone'] == null ? null : json['operatePhone'] as String,
      plotNo: json['plotNo'] == null ? null : json['plotNo'] as String,
      plotName: json['plotName'] == null ? null : json['plotName'] as String,
      plotArea: json['plotArea'] == null ? null : json['plotArea'] as double,
      plotType: json['plotType'] == null ? null : json['plotType'] as String,
      cropCode: json['cropCode'] == null ? null : json['cropCode'] as String,
      cropName: json['cropName'] == null ? null : json['cropName'] as String,
      remark: json['remark'] as dynamic,
      usageDate: json['usageDate'] == null ? null : json['usageDate'] as String,
      waterCons: json['waterCons'] == null ? null : json['waterCons'] as num,
      actualWaterUsage: json['actualWaterUsage'] == null
          ? null
          : json['actualWaterUsage'] as num,
      waterFreq: json['waterFreq'] == null ? null : json['waterFreq'] as num,
      waterReceiverId: json['waterReceiverId'] == null
          ? null
          : json['waterReceiverId'] as num,
      auditStatus:
          json['auditStatus'] == null ? null : json['auditStatus'] as String,
      auditResult:
          json['auditResult'] == null ? null : json['auditResult'] as String,
      statusCd: json['statusCd'] as dynamic,
      createBy: json['createBy'] == null ? null : json['createBy'] as num,
      createTime:
          json['createTime'] == null ? null : json['createTime'] as String,
      updateBy: json['updateBy'] == null ? null : json['updateBy'] as num,
      updateTime:
          json['updateTime'] == null ? null : json['updateTime'] as String,
      dataSource:
          json['dataSource'] == null ? null : json['dataSource'] as String,
      auditLevel: json['auditLevel'] == null ? null : json['auditLevel'] as num,
      accId: json['accId'] == null ? null : json['accId'] as num,
      advanceCollect:
          json['advanceCollect'] == null ? null : json['advanceCollect'] as num,
      amount: json['amount'] == null ? null : json['amount'] as num,
      accumWaterConsume: json['accumWaterConsume'] == null
          ? null
          : json['accumWaterConsume'] as num,
      firstLevelReviewer: json['firstLevelReviewer'] == null
          ? null
          : json['firstLevelReviewer'] as String,
      firstLevelReviewerId: json['firstLevelReviewerId'] == null
          ? null
          : json['firstLevelReviewerId'] as num,
      firstLevelReviewerResult: json['firstLevelReviewerResult'] == null
          ? null
          : json['firstLevelReviewerResult'] as String,
      auditRole: json['auditRole'] as dynamic,
      params: json['params'] as dynamic,
      receiverName:
          json['receiverName'] == null ? null : json['receiverName'] as String,
      distributorName: json['distributorName'] == null
          ? null
          : json['distributorName'] as String,
      regulatorName: json['regulatorName'] == null
          ? null
          : json['regulatorName'] as String,
      canal: json['canal'] == null ? null : json['canal'] as String,
      drainageStatus: json['drainageStatus'] == null
          ? null
          : json['drainageStatus'] as String,
      operatorInfoList: json['operatorInfoList'] == null
          ? null
          : (json['operatorInfoList'] as List)
              .map<OperatorInfoList>((data) =>
                  OperatorInfoList.fromJson(data as Map<String, Object?>))
              .toList(),
      priceRecords: json['priceRecords'] == null
          ? null
          : (json['priceRecords'] as List)
              .map<PriceRecords>(
                  (data) => PriceRecords.fromJson(data as Map<String, Object?>))
              .toList(),
      orgName: json['orgName'] == null ? null : json['orgName'] as String,
      orgFullName:
          json['orgFullName'] == null ? null : json['orgFullName'] as String,
      usageBeginDate: json['usageBeginDate'] == null
          ? null
          : json['usageBeginDate'] as String,
      usageEndDate:
          json['usageEndDate'] == null ? null : json['usageEndDate'] as String,
      actualUsageDate: json['actualUsageDate'] == null
          ? null
          : json['actualUsageDate'] as String,
    );
  }

  @override
  String toString() {
    return '''WaterApplyDetailModel(
                waId:$waId,
yearNo:$yearNo,
orgCode:$orgCode,
growerType:$growerType,
growerName:$growerName,
idNumber:$idNumber,
farmerId:$farmerId,
companyId:$companyId,
operateName:$operateName,
operatePhone:$operatePhone,
plotNo:$plotNo,
plotName:$plotName,
plotArea:$plotArea,
plotType:$plotType,
cropCode:$cropCode,
cropName:$cropName,
remark:$remark,
usageDate:$usageDate,
waterCons:$waterCons,
actualWaterUsage:$actualWaterUsage,
waterFreq:$waterFreq,
waterReceiverId:$waterReceiverId,
auditStatus:$auditStatus,
auditResult:$auditResult,
statusCd:$statusCd,
createBy:$createBy,
createTime:$createTime,
updateBy:$updateBy,
updateTime:$updateTime,
dataSource:$dataSource,
auditLevel:$auditLevel,
accId:$accId,
advanceCollect:$advanceCollect,
amount:$amount,
accumWaterConsume:$accumWaterConsume,
firstLevelReviewer:$firstLevelReviewer,
firstLevelReviewerId:$firstLevelReviewerId,
firstLevelReviewerResult:$firstLevelReviewerResult,
auditRole:$auditRole,
params:$params,
receiverName:$receiverName,
distributorName:$distributorName,
regulatorName:$regulatorName,
canal:$canal,
drainageStatus:$drainageStatus,
operatorInfoList:${operatorInfoList.toString()},
priceRecords:${priceRecords.toString()},
orgName:$orgName,
orgFullName:$orgFullName,
usageBeginDate:$usageBeginDate,
usageEndDate:$usageEndDate,
actualUsageDate:$actualUsageDate,
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is WaterApplyDetailModel &&
        other.runtimeType == runtimeType &&
        other.waId == waId &&
        other.yearNo == yearNo &&
        other.orgCode == orgCode &&
        other.growerType == growerType &&
        other.growerName == growerName &&
        other.idNumber == idNumber &&
        other.farmerId == farmerId &&
        other.companyId == companyId &&
        other.operateName == operateName &&
        other.operatePhone == operatePhone &&
        other.plotNo == plotNo &&
        other.plotName == plotName &&
        other.plotArea == plotArea &&
        other.plotType == plotType &&
        other.cropCode == cropCode &&
        other.cropName == cropName &&
        other.remark == remark &&
        other.usageDate == usageDate &&
        other.waterCons == waterCons &&
        other.actualWaterUsage == actualWaterUsage &&
        other.waterFreq == waterFreq &&
        other.waterReceiverId == waterReceiverId &&
        other.auditStatus == auditStatus &&
        other.auditResult == auditResult &&
        other.statusCd == statusCd &&
        other.createBy == createBy &&
        other.createTime == createTime &&
        other.updateBy == updateBy &&
        other.updateTime == updateTime &&
        other.dataSource == dataSource &&
        other.auditLevel == auditLevel &&
        other.accId == accId &&
        other.advanceCollect == advanceCollect &&
        other.amount == amount &&
        other.accumWaterConsume == accumWaterConsume &&
        other.firstLevelReviewer == firstLevelReviewer &&
        other.firstLevelReviewerId == firstLevelReviewerId &&
        other.firstLevelReviewerResult == firstLevelReviewerResult &&
        other.auditRole == auditRole &&
        other.params == params &&
        other.receiverName == receiverName &&
        other.distributorName == distributorName &&
        other.regulatorName == regulatorName &&
        other.canal == canal &&
        other.drainageStatus == drainageStatus &&
        other.operatorInfoList == operatorInfoList &&
        other.priceRecords == priceRecords &&
        other.orgName == orgName &&
        other.orgFullName == orgFullName &&
        other.usageBeginDate == usageBeginDate &&
        other.usageEndDate == usageEndDate &&
        other.actualUsageDate == actualUsageDate;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        waId,
        yearNo,
        orgCode,
        growerType,
        growerName,
        idNumber,
        farmerId,
        companyId,
        operateName,
        operatePhone,
        plotNo,
        plotName,
        plotArea,
        plotType,
        cropCode,
        cropName,
        remark,
        usageDate,
        waterCons);
  }
}

class PriceRecords {
  final String? priceLevel;
  final num? amount;
  final num? waterCons;
  final String? priceMin;
  final String? priceMax;
  final String? priceMinStr;
  final String? priceMaxStr;
  final String? priceLevelDesc;
  const PriceRecords(
      {this.priceLevel,
      this.amount,
      this.waterCons,
      this.priceMin,
      this.priceMax,
      this.priceMinStr,
      this.priceMaxStr,
      this.priceLevelDesc});
  PriceRecords copyWith(
      {String? priceLevel,
      num? amount,
      num? waterCons,
      String? priceMin,
      String? priceMax,
      String? priceMinStr,
      String? priceMaxStr,
      String? priceLevelDesc}) {
    return PriceRecords(
        priceLevel: priceLevel ?? this.priceLevel,
        amount: amount ?? this.amount,
        waterCons: waterCons ?? this.waterCons,
        priceMin: priceMin ?? this.priceMin,
        priceMax: priceMax ?? this.priceMax,
        priceMinStr: priceMinStr ?? this.priceMinStr,
        priceMaxStr: priceMaxStr ?? this.priceMaxStr,
        priceLevelDesc: priceLevelDesc ?? this.priceLevelDesc);
  }

  Map<String, Object?> toJson() {
    return {
      'priceLevel': priceLevel,
      'amount': amount,
      'waterCons': waterCons,
      'priceMin': priceMin,
      'priceMax': priceMax,
      'priceMinStr': priceMinStr,
      'priceMaxStr': priceMaxStr,
      'priceLevelDesc': priceLevelDesc,
    };
  }

  static PriceRecords fromJson(Map<String, Object?> json) {
    return PriceRecords(
        priceLevel:
            json['priceLevel'] == null ? null : json['priceLevel'] as String,
        amount: json['amount'] == null ? null : json['amount'] as num,
        waterCons: json['waterCons'] == null ? null : json['waterCons'] as num,
        priceMin: json['priceMin'] == null ? null : json['priceMin'] as String,
        priceMax: json['priceMax'] == null ? null : json['priceMax'] as String,
        priceMinStr:
            json['priceMinStr'] == null ? null : json['priceMinStr'] as String,
        priceMaxStr:
            json['priceMaxStr'] == null ? null : json['priceMaxStr'] as String,
        priceLevelDesc: json['priceLevelDesc'] == null
            ? null
            : json['priceLevelDesc'] as String);
  }

  @override
  String toString() {
    return '''PriceRecords(
                priceLevel:$priceLevel,
amount:$amount,
waterCons:$waterCons,
priceMin:$priceMin,
priceMax:$priceMax,
priceMinStr:$priceMinStr,
priceMaxStr:$priceMaxStr,
priceLevelDesc:$priceLevelDesc,
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is PriceRecords &&
        other.runtimeType == runtimeType &&
        other.priceLevel == priceLevel &&
        other.amount == amount &&
        other.waterCons == waterCons &&
        other.priceMin == priceMin &&
        other.priceMax == priceMax &&
        other.priceMinStr == priceMinStr &&
        other.priceMaxStr == priceMaxStr &&
        other.priceLevelDesc == priceLevelDesc;
  }

  @override
  int get hashCode {
    return Object.hash(runtimeType, priceLevel, amount, waterCons, priceMin,
        priceMax, priceMinStr, priceMaxStr, priceLevelDesc);
  }
}

class OperatorInfoList {
  final String? userName;
  final String? taskName;
  const OperatorInfoList({this.userName, this.taskName});
  OperatorInfoList copyWith({dynamic? userName, String? taskName}) {
    return OperatorInfoList(
        userName: userName ?? this.userName,
        taskName: taskName ?? this.taskName);
  }

  Map<String, Object?> toJson() {
    return {'userName': userName, 'taskName': taskName};
  }

  static OperatorInfoList fromJson(Map<String, Object?> json) {
    return OperatorInfoList(
        userName: json['userName'] == null ? null : json['userName'] as String,
        taskName: json['taskName'] == null ? null : json['taskName'] as String);
  }

  @override
  String toString() {
    return '''OperatorInfoList(
                userName:$userName,
taskName:$taskName
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is OperatorInfoList &&
        other.runtimeType == runtimeType &&
        other.userName == userName &&
        other.taskName == taskName;
  }

  @override
  int get hashCode {
    return Object.hash(runtimeType, userName, taskName);
  }
}
