class PageModel {
  final List? records;
  final int? total;
  final int? size;
  final int? current;
  final List<dynamic>? orders;
  final bool? optimizeCountSql;
  final bool? hitCount;
  final dynamic countId;
  final dynamic maxLimit;
  final bool? searchCount;
  final int? pages;
  const PageModel(
      {this.records,
      this.total,
      this.size,
      this.current,
      this.orders,
      this.optimizeCountSql,
      this.hitCount,
      this.countId,
      this.maxLimit,
      this.searchCount,
      this.pages});
  PageModel copyWith(
      {List<dynamic>? records,
      int? total,
      int? size,
      int? current,
      List<dynamic>? orders,
      bool? optimizeCountSql,
      bool? hitCount,
      dynamic? countId,
      dynamic? maxLimit,
      bool? searchCount,
      int? pages}) {
    return PageModel(
        records: records ?? this.records,
        total: total ?? this.total,
        size: size ?? this.size,
        current: current ?? this.current,
        orders: orders ?? this.orders,
        optimizeCountSql: optimizeCountSql ?? this.optimizeCountSql,
        hitCount: hitCount ?? this.hitCount,
        countId: countId ?? this.countId,
        maxLimit: maxLimit ?? this.maxLimit,
        searchCount: searchCount ?? this.searchCount,
        pages: pages ?? this.pages);
  }

  Map<String, Object?> toJson() {
    return {
      'records': records,
      'total': total,
      'size': size,
      'current': current,
      'orders': orders,
      'optimizeCountSql': optimizeCountSql,
      'hitCount': hitCount,
      'countId': countId,
      'maxLimit': maxLimit,
      'searchCount': searchCount,
      'pages': pages
    };
  }

  static PageModel fromJson(Map<String, Object?> json) {
    return PageModel(
        records:
            json['records'] == null ? null : json['records'] as List<dynamic>,
        total: json['total'] == null ? null : json['total'] as int,
        size: json['size'] == null ? null : json['size'] as int,
        current: json['current'] == null ? null : json['current'] as int,
        orders: json['orders'] == null ? null : json['orders'] as List<dynamic>,
        optimizeCountSql: json['optimizeCountSql'] == null
            ? null
            : json['optimizeCountSql'] as bool,
        hitCount: json['hitCount'] == null ? null : json['hitCount'] as bool,
        countId: json['countId'] as dynamic,
        maxLimit: json['maxLimit'] as dynamic,
        searchCount:
            json['searchCount'] == null ? null : json['searchCount'] as bool,
        pages: json['pages'] == null ? null : json['pages'] as int);
  }

  @override
  String toString() {
    return '''PageModel(
                records:$records,
total:$total,
size:$size,
current:$current,
orders:$orders,
optimizeCountSql:$optimizeCountSql,
hitCount:$hitCount,
countId:$countId,
maxLimit:$maxLimit,
searchCount:$searchCount,
pages:$pages
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is PageModel &&
        other.runtimeType == runtimeType &&
        other.records == records &&
        other.total == total &&
        other.size == size &&
        other.current == current &&
        other.orders == orders &&
        other.optimizeCountSql == optimizeCountSql &&
        other.hitCount == hitCount &&
        other.countId == countId &&
        other.maxLimit == maxLimit &&
        other.searchCount == searchCount &&
        other.pages == pages;
  }

  @override
  int get hashCode {
    return Object.hash(runtimeType, records, total, size, current, orders,
        optimizeCountSql, hitCount, countId, maxLimit, searchCount, pages);
  }
}
