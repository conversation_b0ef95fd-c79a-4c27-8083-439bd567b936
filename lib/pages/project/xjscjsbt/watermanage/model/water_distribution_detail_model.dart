class WaterDistributionDetail {
  final num? wrId;
  final String? usageBeginDate;
  final String? usageEndDate;
  final num? createBy;
  final String? createTime;
  final num? updateBy;
  final String? updateTime;
  final dynamic statusCd;
  final String? orgCode;
  final num? waterUsage;
  final num? plotNumber;
  final num? applicantNumber;
  final dynamic mainCanal;
  final dynamic branchCanal;
  final dynamic lateralCanal;
  final dynamic remark;
  final dynamic params;
  final dynamic waIds;
  final List<WaterApplies>? waterApplies;
  final dynamic waterReleaseApplies;
  final String? orgName;
  final String? orgFullName;
  final num? auditLevel;
  final String? auditStatus;
  final String? auditResult;
  final dynamic mainCanals;
  final dynamic branchCanals;
  final dynamic lateralCanals;
  final WaterDistributor? waterDistributor;
  final String? releaseStatus;
  final num? distributorStaffId;
  final dynamic distributorOpTime;
  final num? regulatorStaffId;
  final String? regulatorOpTime;
  final String? distributorName;
  final String? regulatorName;
  const WaterDistributionDetail(
      {this.wrId,
      this.usageBeginDate,
      this.usageEndDate,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.statusCd,
      this.orgCode,
      this.waterUsage,
      this.plotNumber,
      this.applicantNumber,
      this.mainCanal,
      this.branchCanal,
      this.lateralCanal,
      this.remark,
      this.params,
      this.waIds,
      this.waterApplies,
      this.waterReleaseApplies,
      this.orgName,
      this.orgFullName,
      this.auditLevel,
      this.auditStatus,
      this.auditResult,
      this.mainCanals,
      this.branchCanals,
      this.lateralCanals,
      this.waterDistributor,
      this.releaseStatus,
      this.distributorStaffId,
      this.distributorOpTime,
      this.regulatorStaffId,
      this.regulatorOpTime,
      this.distributorName,
      this.regulatorName});
  WaterDistributionDetail copyWith(
      {num? wrId,
      String? usageBeginDate,
      String? usageEndDate,
      num? createBy,
      String? createTime,
      num? updateBy,
      String? updateTime,
      dynamic? statusCd,
      String? orgCode,
      num? waterUsage,
      num? plotNumber,
      num? applicantNumber,
      dynamic? mainCanal,
      dynamic? branchCanal,
      dynamic? lateralCanal,
      dynamic? remark,
      dynamic? params,
      dynamic? waIds,
      List<WaterApplies>? waterApplies,
      dynamic? waterReleaseApplies,
      String? orgName,
      String? orgFullName,
      num? auditLevel,
      String? auditStatus,
      String? auditResult,
      dynamic? mainCanals,
      dynamic? branchCanals,
      dynamic? lateralCanals,
      WaterDistributor? waterDistributor,
      String? releaseStatus,
      num? distributorStaffId,
      dynamic? distributorOpTime,
      num? regulatorStaffId,
      String? regulatorOpTime,
      String? distributorName,
      String? regulatorName}) {
    return WaterDistributionDetail(
        wrId: wrId ?? this.wrId,
        usageBeginDate: usageBeginDate ?? this.usageBeginDate,
        usageEndDate: usageEndDate ?? this.usageEndDate,
        createBy: createBy ?? this.createBy,
        createTime: createTime ?? this.createTime,
        updateBy: updateBy ?? this.updateBy,
        updateTime: updateTime ?? this.updateTime,
        statusCd: statusCd ?? this.statusCd,
        orgCode: orgCode ?? this.orgCode,
        waterUsage: waterUsage ?? this.waterUsage,
        plotNumber: plotNumber ?? this.plotNumber,
        applicantNumber: applicantNumber ?? this.applicantNumber,
        mainCanal: mainCanal ?? this.mainCanal,
        branchCanal: branchCanal ?? this.branchCanal,
        lateralCanal: lateralCanal ?? this.lateralCanal,
        remark: remark ?? this.remark,
        params: params ?? this.params,
        waIds: waIds ?? this.waIds,
        waterApplies: waterApplies ?? this.waterApplies,
        waterReleaseApplies: waterReleaseApplies ?? this.waterReleaseApplies,
        orgName: orgName ?? this.orgName,
        orgFullName: orgFullName ?? this.orgFullName,
        auditLevel: auditLevel ?? this.auditLevel,
        auditStatus: auditStatus ?? this.auditStatus,
        auditResult: auditResult ?? this.auditResult,
        mainCanals: mainCanals ?? this.mainCanals,
        branchCanals: branchCanals ?? this.branchCanals,
        lateralCanals: lateralCanals ?? this.lateralCanals,
        waterDistributor: waterDistributor ?? this.waterDistributor,
        releaseStatus: releaseStatus ?? this.releaseStatus,
        distributorStaffId: distributorStaffId ?? this.distributorStaffId,
        distributorOpTime: distributorOpTime ?? this.distributorOpTime,
        regulatorStaffId: regulatorStaffId ?? this.regulatorStaffId,
        regulatorOpTime: regulatorOpTime ?? this.regulatorOpTime,
        distributorName: distributorName ?? this.distributorName,
        regulatorName: regulatorName ?? this.regulatorName);
  }

  Map<String, Object?> toJson() {
    return {
      'wrId': wrId,
      'usageBeginDate': usageBeginDate,
      'usageEndDate': usageEndDate,
      'createBy': createBy,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'statusCd': statusCd,
      'orgCode': orgCode,
      'waterUsage': waterUsage,
      'plotNumber': plotNumber,
      'applicantNumber': applicantNumber,
      'mainCanal': mainCanal,
      'branchCanal': branchCanal,
      'lateralCanal': lateralCanal,
      'remark': remark,
      'params': params,
      'waIds': waIds,
      'waterApplies': waterApplies
          ?.map<Map<String, dynamic>>((data) => data.toJson())
          .toList(),
      'waterReleaseApplies': waterReleaseApplies,
      'orgName': orgName,
      'orgFullName': orgFullName,
      'auditLevel': auditLevel,
      'auditStatus': auditStatus,
      'auditResult': auditResult,
      'mainCanals': mainCanals,
      'branchCanals': branchCanals,
      'lateralCanals': lateralCanals,
      'waterDistributor': waterDistributor?.toJson(),
      'releaseStatus': releaseStatus,
      'distributorStaffId': distributorStaffId,
      'distributorOpTime': distributorOpTime,
      'regulatorStaffId': regulatorStaffId,
      'regulatorOpTime': regulatorOpTime,
      'distributorName': distributorName,
      'regulatorName': regulatorName
    };
  }

  static WaterDistributionDetail fromJson(Map<String, Object?> json) {
    return WaterDistributionDetail(
        wrId: json['wrId'] == null ? null : json['wrId'] as num,
        usageBeginDate: json['usageBeginDate'] == null
            ? null
            : json['usageBeginDate'] as String,
        usageEndDate: json['usageEndDate'] == null
            ? null
            : json['usageEndDate'] as String,
        createBy: json['createBy'] == null ? null : json['createBy'] as num,
        createTime:
            json['createTime'] == null ? null : json['createTime'] as String,
        updateBy: json['updateBy'] == null ? null : json['updateBy'] as num,
        updateTime:
            json['updateTime'] == null ? null : json['updateTime'] as String,
        statusCd: json['statusCd'] as dynamic,
        orgCode: json['orgCode'] == null ? null : json['orgCode'] as String,
        waterUsage:
            json['waterUsage'] == null ? null : json['waterUsage'] as num,
        plotNumber:
            json['plotNumber'] == null ? null : json['plotNumber'] as num,
        applicantNumber: json['applicantNumber'] == null
            ? null
            : json['applicantNumber'] as num,
        mainCanal: json['mainCanal'] as dynamic,
        branchCanal: json['branchCanal'] as dynamic,
        lateralCanal: json['lateralCanal'] as dynamic,
        remark: json['remark'] as dynamic,
        params: json['params'] as dynamic,
        waIds: json['waIds'] as dynamic,
        waterApplies: json['waterApplies'] == null
            ? null
            : (json['waterApplies'] as List)
                .map<WaterApplies>((data) =>
                    WaterApplies.fromJson(data as Map<String, Object?>))
                .toList(),
        waterReleaseApplies: json['waterReleaseApplies'] as dynamic,
        orgName: json['orgName'] == null ? null : json['orgName'] as String,
        orgFullName:
            json['orgFullName'] == null ? null : json['orgFullName'] as String,
        auditLevel:
            json['auditLevel'] == null ? null : json['auditLevel'] as num,
        auditStatus:
            json['auditStatus'] == null ? null : json['auditStatus'] as String,
        auditResult:
            json['auditResult'] == null ? null : json['auditResult'] as String,
        mainCanals: json['mainCanals'] as dynamic,
        branchCanals: json['branchCanals'] as dynamic,
        lateralCanals: json['lateralCanals'] as dynamic,
        waterDistributor: json['waterDistributor'] == null
            ? null
            : WaterDistributor.fromJson(
                json['waterDistributor'] as Map<String, Object?>),
        releaseStatus: json['releaseStatus'] == null
            ? null
            : json['releaseStatus'] as String,
        distributorStaffId: json['distributorStaffId'] == null
            ? null
            : json['distributorStaffId'] as num,
        distributorOpTime: json['distributorOpTime'] as dynamic,
        regulatorStaffId: json['regulatorStaffId'] == null
            ? null
            : json['regulatorStaffId'] as num,
        regulatorOpTime: json['regulatorOpTime'] == null
            ? null
            : json['regulatorOpTime'] as String,
        distributorName: json['distributorName'] == null
            ? null
            : json['distributorName'] as String,
        regulatorName: json['regulatorName'] == null
            ? null
            : json['regulatorName'] as String);
  }

  @override
  String toString() {
    return '''WaterDistributionDetail(
                wrId:$wrId,
usageBeginDate:$usageBeginDate,
usageEndDate:$usageEndDate,
createBy:$createBy,
createTime:$createTime,
updateBy:$updateBy,
updateTime:$updateTime,
statusCd:$statusCd,
orgCode:$orgCode,
waterUsage:$waterUsage,
plotNumber:$plotNumber,
applicantNumber:$applicantNumber,
mainCanal:$mainCanal,
branchCanal:$branchCanal,
lateralCanal:$lateralCanal,
remark:$remark,
params:$params,
waIds:$waIds,
waterApplies:${waterApplies.toString()},
waterReleaseApplies:$waterReleaseApplies,
orgName:$orgName,
orgFullName:$orgFullName,
auditLevel:$auditLevel,
auditStatus:$auditStatus,
auditResult:$auditResult,
mainCanals:$mainCanals,
branchCanals:$branchCanals,
lateralCanals:$lateralCanals,
waterDistributor:${waterDistributor.toString()},
releaseStatus:$releaseStatus,
distributorStaffId:$distributorStaffId,
distributorOpTime:$distributorOpTime,
regulatorStaffId:$regulatorStaffId,
regulatorOpTime:$regulatorOpTime,
distributorName:$distributorName,
regulatorName:$regulatorName
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is WaterDistributionDetail &&
        other.runtimeType == runtimeType &&
        other.wrId == wrId &&
        other.usageBeginDate == usageBeginDate &&
        other.usageEndDate == usageEndDate &&
        other.createBy == createBy &&
        other.createTime == createTime &&
        other.updateBy == updateBy &&
        other.updateTime == updateTime &&
        other.statusCd == statusCd &&
        other.orgCode == orgCode &&
        other.waterUsage == waterUsage &&
        other.plotNumber == plotNumber &&
        other.applicantNumber == applicantNumber &&
        other.mainCanal == mainCanal &&
        other.branchCanal == branchCanal &&
        other.lateralCanal == lateralCanal &&
        other.remark == remark &&
        other.params == params &&
        other.waIds == waIds &&
        other.waterApplies == waterApplies &&
        other.waterReleaseApplies == waterReleaseApplies &&
        other.orgName == orgName &&
        other.orgFullName == orgFullName &&
        other.auditLevel == auditLevel &&
        other.auditStatus == auditStatus &&
        other.auditResult == auditResult &&
        other.mainCanals == mainCanals &&
        other.branchCanals == branchCanals &&
        other.lateralCanals == lateralCanals &&
        other.waterDistributor == waterDistributor &&
        other.releaseStatus == releaseStatus &&
        other.distributorStaffId == distributorStaffId &&
        other.distributorOpTime == distributorOpTime &&
        other.regulatorStaffId == regulatorStaffId &&
        other.regulatorOpTime == regulatorOpTime &&
        other.distributorName == distributorName &&
        other.regulatorName == regulatorName;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        wrId,
        usageBeginDate,
        usageEndDate,
        createBy,
        createTime,
        updateBy,
        updateTime,
        statusCd,
        orgCode,
        waterUsage,
        plotNumber,
        applicantNumber,
        mainCanal,
        branchCanal,
        lateralCanal,
        remark,
        params,
        waIds,
        waterApplies);
  }
}

class WaterDistributor {
  final num? wdisId;
  final String? orgCode;
  final num? staffId;
  final String? phoneNo;
  final String? validStatus;
  final String? statusCd;
  final num? createBy;
  final String? createTime;
  final num? updateBy;
  final String? updateTime;
  final dynamic remark;
  final String? auditStatus;
  final String? auditResult;
  final String? scope;
  final List<dynamic>? scopes;
  final dynamic params;
  final String? orgName;
  final String? orgFullName;
  final String? loginName;
  final String? staffName;
  final List<CanalList>? canalList;
  final String? canalLabel;
  final dynamic canals;
  const WaterDistributor(
      {this.wdisId,
      this.orgCode,
      this.staffId,
      this.phoneNo,
      this.validStatus,
      this.statusCd,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.remark,
      this.auditStatus,
      this.auditResult,
      this.scope,
      this.scopes,
      this.params,
      this.orgName,
      this.orgFullName,
      this.loginName,
      this.staffName,
      this.canalList,
      this.canalLabel,
      this.canals});
  WaterDistributor copyWith(
      {num? wdisId,
      String? orgCode,
      num? staffId,
      String? phoneNo,
      String? validStatus,
      String? statusCd,
      num? createBy,
      String? createTime,
      num? updateBy,
      String? updateTime,
      dynamic? remark,
      String? auditStatus,
      String? auditResult,
      String? scope,
      List<dynamic>? scopes,
      dynamic? params,
      String? orgName,
      String? orgFullName,
      String? loginName,
      String? staffName,
      List<CanalList>? canalList,
      String? canalLabel,
      dynamic? canals}) {
    return WaterDistributor(
        wdisId: wdisId ?? this.wdisId,
        orgCode: orgCode ?? this.orgCode,
        staffId: staffId ?? this.staffId,
        phoneNo: phoneNo ?? this.phoneNo,
        validStatus: validStatus ?? this.validStatus,
        statusCd: statusCd ?? this.statusCd,
        createBy: createBy ?? this.createBy,
        createTime: createTime ?? this.createTime,
        updateBy: updateBy ?? this.updateBy,
        updateTime: updateTime ?? this.updateTime,
        remark: remark ?? this.remark,
        auditStatus: auditStatus ?? this.auditStatus,
        auditResult: auditResult ?? this.auditResult,
        scope: scope ?? this.scope,
        scopes: scopes ?? this.scopes,
        params: params ?? this.params,
        orgName: orgName ?? this.orgName,
        orgFullName: orgFullName ?? this.orgFullName,
        loginName: loginName ?? this.loginName,
        staffName: staffName ?? this.staffName,
        canalList: canalList ?? this.canalList,
        canalLabel: canalLabel ?? this.canalLabel,
        canals: canals ?? this.canals);
  }

  Map<String, Object?> toJson() {
    return {
      'wdisId': wdisId,
      'orgCode': orgCode,
      'staffId': staffId,
      'phoneNo': phoneNo,
      'validStatus': validStatus,
      'statusCd': statusCd,
      'createBy': createBy,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'remark': remark,
      'auditStatus': auditStatus,
      'auditResult': auditResult,
      'scope': scope,
      'scopes': scopes,
      'params': params,
      'orgName': orgName,
      'orgFullName': orgFullName,
      'loginName': loginName,
      'staffName': staffName,
      'canalList': canalList
          ?.map<Map<String, dynamic>>((data) => data.toJson())
          .toList(),
      'canalLabel': canalLabel,
      'canals': canals
    };
  }

  static WaterDistributor fromJson(Map<String, Object?> json) {
    return WaterDistributor(
        wdisId: json['wdisId'] == null ? null : json['wdisId'] as num,
        orgCode: json['orgCode'] == null ? null : json['orgCode'] as String,
        staffId: json['staffId'] == null ? null : json['staffId'] as num,
        phoneNo: json['phoneNo'] == null ? null : json['phoneNo'] as String,
        validStatus:
            json['validStatus'] == null ? null : json['validStatus'] as String,
        statusCd: json['statusCd'] == null ? null : json['statusCd'] as String,
        createBy: json['createBy'] == null ? null : json['createBy'] as num,
        createTime:
            json['createTime'] == null ? null : json['createTime'] as String,
        updateBy: json['updateBy'] == null ? null : json['updateBy'] as num,
        updateTime:
            json['updateTime'] == null ? null : json['updateTime'] as String,
        remark: json['remark'] as dynamic,
        auditStatus:
            json['auditStatus'] == null ? null : json['auditStatus'] as String,
        auditResult:
            json['auditResult'] == null ? null : json['auditResult'] as String,
        scope: json['scope'] == null ? null : json['scope'] as String,
        scopes: json['scopes'] == null ? null : json['scopes'] as List<dynamic>,
        params: json['params'] as dynamic,
        orgName: json['orgName'] == null ? null : json['orgName'] as String,
        orgFullName:
            json['orgFullName'] == null ? null : json['orgFullName'] as String,
        loginName:
            json['loginName'] == null ? null : json['loginName'] as String,
        staffName:
            json['staffName'] == null ? null : json['staffName'] as String,
        canalList: json['canalList'] == null
            ? null
            : (json['canalList'] as List)
                .map<CanalList>(
                    (data) => CanalList.fromJson(data as Map<String, Object?>))
                .toList(),
        canalLabel:
            json['canalLabel'] == null ? null : json['canalLabel'] as String,
        canals: json['canals'] as dynamic);
  }

  @override
  String toString() {
    return '''WaterDistributor(
                wdisId:$wdisId,
orgCode:$orgCode,
staffId:$staffId,
phoneNo:$phoneNo,
validStatus:$validStatus,
statusCd:$statusCd,
createBy:$createBy,
createTime:$createTime,
updateBy:$updateBy,
updateTime:$updateTime,
remark:$remark,
auditStatus:$auditStatus,
auditResult:$auditResult,
scope:$scope,
scopes:$scopes,
params:$params,
orgName:$orgName,
orgFullName:$orgFullName,
loginName:$loginName,
staffName:$staffName,
canalList:${canalList.toString()},
canalLabel:$canalLabel,
canals:$canals
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is WaterDistributor &&
        other.runtimeType == runtimeType &&
        other.wdisId == wdisId &&
        other.orgCode == orgCode &&
        other.staffId == staffId &&
        other.phoneNo == phoneNo &&
        other.validStatus == validStatus &&
        other.statusCd == statusCd &&
        other.createBy == createBy &&
        other.createTime == createTime &&
        other.updateBy == updateBy &&
        other.updateTime == updateTime &&
        other.remark == remark &&
        other.auditStatus == auditStatus &&
        other.auditResult == auditResult &&
        other.scope == scope &&
        other.scopes == scopes &&
        other.params == params &&
        other.orgName == orgName &&
        other.orgFullName == orgFullName &&
        other.loginName == loginName &&
        other.staffName == staffName &&
        other.canalList == canalList &&
        other.canalLabel == canalLabel &&
        other.canals == canals;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        wdisId,
        orgCode,
        staffId,
        phoneNo,
        validStatus,
        statusCd,
        createBy,
        createTime,
        updateBy,
        updateTime,
        remark,
        auditStatus,
        auditResult,
        scope,
        scopes,
        params,
        orgName,
        orgFullName,
        loginName);
  }
}

class CanalList {
  final num? wdisCId;
  final num? wdisId;
  final String? mainCanal;
  final dynamic branchCanal;
  final dynamic lateralCanal;
  final dynamic statusCd;
  final num? createBy;
  final num? createTime;
  final dynamic remark;
  final dynamic updateBy;
  final dynamic updateTime;
  final dynamic params;
  const CanalList(
      {this.wdisCId,
      this.wdisId,
      this.mainCanal,
      this.branchCanal,
      this.lateralCanal,
      this.statusCd,
      this.createBy,
      this.createTime,
      this.remark,
      this.updateBy,
      this.updateTime,
      this.params});
  CanalList copyWith(
      {num? wdisCId,
      num? wdisId,
      String? mainCanal,
      dynamic? branchCanal,
      dynamic? lateralCanal,
      dynamic? statusCd,
      num? createBy,
      num? createTime,
      dynamic? remark,
      dynamic? updateBy,
      dynamic? updateTime,
      dynamic? params}) {
    return CanalList(
        wdisCId: wdisCId ?? this.wdisCId,
        wdisId: wdisId ?? this.wdisId,
        mainCanal: mainCanal ?? this.mainCanal,
        branchCanal: branchCanal ?? this.branchCanal,
        lateralCanal: lateralCanal ?? this.lateralCanal,
        statusCd: statusCd ?? this.statusCd,
        createBy: createBy ?? this.createBy,
        createTime: createTime ?? this.createTime,
        remark: remark ?? this.remark,
        updateBy: updateBy ?? this.updateBy,
        updateTime: updateTime ?? this.updateTime,
        params: params ?? this.params);
  }

  Map<String, Object?> toJson() {
    return {
      'wdisCId': wdisCId,
      'wdisId': wdisId,
      'mainCanal': mainCanal,
      'branchCanal': branchCanal,
      'lateralCanal': lateralCanal,
      'statusCd': statusCd,
      'createBy': createBy,
      'createTime': createTime,
      'remark': remark,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'params': params
    };
  }

  static CanalList fromJson(Map<String, Object?> json) {
    return CanalList(
        wdisCId: json['wdisCId'] == null ? null : json['wdisCId'] as num,
        wdisId: json['wdisId'] == null ? null : json['wdisId'] as num,
        mainCanal:
            json['mainCanal'] == null ? null : json['mainCanal'] as String,
        branchCanal: json['branchCanal'] as dynamic,
        lateralCanal: json['lateralCanal'] as dynamic,
        statusCd: json['statusCd'] as dynamic,
        createBy: json['createBy'] == null ? null : json['createBy'] as num,
        createTime:
            json['createTime'] == null ? null : json['createTime'] as num,
        remark: json['remark'] as dynamic,
        updateBy: json['updateBy'] as dynamic,
        updateTime: json['updateTime'] as dynamic,
        params: json['params'] as dynamic);
  }

  @override
  String toString() {
    return '''CanalList(
                wdisCId:$wdisCId,
wdisId:$wdisId,
mainCanal:$mainCanal,
branchCanal:$branchCanal,
lateralCanal:$lateralCanal,
statusCd:$statusCd,
createBy:$createBy,
createTime:$createTime,
remark:$remark,
updateBy:$updateBy,
updateTime:$updateTime,
params:$params
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is CanalList &&
        other.runtimeType == runtimeType &&
        other.wdisCId == wdisCId &&
        other.wdisId == wdisId &&
        other.mainCanal == mainCanal &&
        other.branchCanal == branchCanal &&
        other.lateralCanal == lateralCanal &&
        other.statusCd == statusCd &&
        other.createBy == createBy &&
        other.createTime == createTime &&
        other.remark == remark &&
        other.updateBy == updateBy &&
        other.updateTime == updateTime &&
        other.params == params;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        wdisCId,
        wdisId,
        mainCanal,
        branchCanal,
        lateralCanal,
        statusCd,
        createBy,
        createTime,
        remark,
        updateBy,
        updateTime,
        params);
  }
}

class WaterApplies {
  final num? waId;
  final num? yearNo;
  final String? orgCode;
  final String? growerType;
  final String? growerName;
  final String? idNumber;
  final dynamic farmerId;
  final dynamic companyId;
  final dynamic operateName;
  final String? operatePhone;
  final dynamic operateIdNumber;
  final String? plotNo;
  final String? plotName;
  final num? plotArea;
  final String? plotType;
  final String? cropCode;
  final String? cropName;
  final dynamic remark;
  final String? usageDate;
  final num? waterCons;
  final num? actualWaterUsage;
  final num? waterFreq;
  final dynamic waterReceiverId;
  final String? auditStatus;
  final String? auditResult;
  final dynamic statusCd;
  final num? createBy;
  final String? createTime;
  final num? updateBy;
  final String? updateTime;
  final String? dataSource;
  final num? auditLevel;
  final num? accId;
  final num? advanceCollect;
  final num? amount;
  final num? accumWaterConsume;
  final dynamic auditRole;
  final dynamic params;
  final dynamic waterReceiverName;
  final String? canal;
  final String? drainageStatus;
  final List<OperatorInfoList>? operatorInfoList;
  final dynamic priceRecords;
  final String? orgName;
  final String? orgFullName;
  final String? receiverName;
  final String? distributorName;
  final String? regulatorName;

  final String? usageEndDate;
  final String? usageBeginDate;
  final String? actualUsageDate;
  const WaterApplies(
      {this.waId,
      this.yearNo,
      this.orgCode,
      this.growerType,
      this.growerName,
      this.idNumber,
      this.farmerId,
      this.companyId,
      this.operateName,
      this.operatePhone,
      this.operateIdNumber,
      this.plotNo,
      this.plotName,
      this.plotArea,
      this.plotType,
      this.cropCode,
      this.cropName,
      this.remark,
      this.usageDate,
      this.waterCons,
      this.actualWaterUsage,
      this.waterFreq,
      this.waterReceiverId,
      this.auditStatus,
      this.auditResult,
      this.statusCd,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.dataSource,
      this.auditLevel,
      this.accId,
      this.advanceCollect,
      this.amount,
      this.accumWaterConsume,
      this.auditRole,
      this.params,
      this.waterReceiverName,
      this.canal,
      this.drainageStatus,
      this.operatorInfoList,
      this.priceRecords,
      this.orgName,
      this.orgFullName,
      this.receiverName,
      this.distributorName,
      this.regulatorName,
      this.actualUsageDate,
      this.usageBeginDate,
      this.usageEndDate});
  WaterApplies copyWith({
    num? waId,
    num? yearNo,
    String? orgCode,
    String? growerType,
    String? growerName,
    String? idNumber,
    dynamic? farmerId,
    dynamic? companyId,
    dynamic? operateName,
    String? operatePhone,
    dynamic? operateIdNumber,
    String? plotNo,
    String? plotName,
    num? plotArea,
    String? plotType,
    String? cropCode,
    String? cropName,
    dynamic? remark,
    String? usageDate,
    num? waterCons,
    num? actualWaterUsage,
    num? waterFreq,
    dynamic? waterReceiverId,
    String? auditStatus,
    String? auditResult,
    dynamic? statusCd,
    num? createBy,
    String? createTime,
    num? updateBy,
    String? updateTime,
    String? dataSource,
    num? auditLevel,
    num? accId,
    num? advanceCollect,
    num? amount,
    num? accumWaterConsume,
    dynamic? auditRole,
    dynamic? params,
    dynamic? waterReceiverName,
    String? canal,
    String? drainageStatus,
    List<OperatorInfoList>? operatorInfoList,
    dynamic? priceRecords,
    String? orgName,
    String? orgFullName,
    String? receiverName,
    String? distributorName,
    String? regulatorName,
    String? usageEndDate,
    String? usageBeginDate,
    String? actualUsageDate,
  }) {
    return WaterApplies(
      waId: waId ?? this.waId,
      yearNo: yearNo ?? this.yearNo,
      orgCode: orgCode ?? this.orgCode,
      growerType: growerType ?? this.growerType,
      growerName: growerName ?? this.growerName,
      idNumber: idNumber ?? this.idNumber,
      farmerId: farmerId ?? this.farmerId,
      companyId: companyId ?? this.companyId,
      operateName: operateName ?? this.operateName,
      operatePhone: operatePhone ?? this.operatePhone,
      operateIdNumber: operateIdNumber ?? this.operateIdNumber,
      plotNo: plotNo ?? this.plotNo,
      plotName: plotName ?? this.plotName,
      plotArea: plotArea ?? this.plotArea,
      plotType: plotType ?? this.plotType,
      cropCode: cropCode ?? this.cropCode,
      cropName: cropName ?? this.cropName,
      remark: remark ?? this.remark,
      usageDate: usageDate ?? this.usageDate,
      waterCons: waterCons ?? this.waterCons,
      actualWaterUsage: actualWaterUsage ?? this.actualWaterUsage,
      waterFreq: waterFreq ?? this.waterFreq,
      waterReceiverId: waterReceiverId ?? this.waterReceiverId,
      auditStatus: auditStatus ?? this.auditStatus,
      auditResult: auditResult ?? this.auditResult,
      statusCd: statusCd ?? this.statusCd,
      createBy: createBy ?? this.createBy,
      createTime: createTime ?? this.createTime,
      updateBy: updateBy ?? this.updateBy,
      updateTime: updateTime ?? this.updateTime,
      dataSource: dataSource ?? this.dataSource,
      auditLevel: auditLevel ?? this.auditLevel,
      accId: accId ?? this.accId,
      advanceCollect: advanceCollect ?? this.advanceCollect,
      amount: amount ?? this.amount,
      accumWaterConsume: accumWaterConsume ?? this.accumWaterConsume,
      auditRole: auditRole ?? this.auditRole,
      params: params ?? this.params,
      waterReceiverName: waterReceiverName ?? this.waterReceiverName,
      canal: canal ?? this.canal,
      drainageStatus: drainageStatus ?? this.drainageStatus,
      operatorInfoList: operatorInfoList ?? this.operatorInfoList,
      priceRecords: priceRecords ?? this.priceRecords,
      orgName: orgName ?? this.orgName,
      orgFullName: orgFullName ?? this.orgFullName,
      receiverName: receiverName ?? this.receiverName,
      distributorName: distributorName ?? this.distributorName,
      regulatorName: regulatorName ?? this.regulatorName,
      usageBeginDate: usageBeginDate ?? this.usageBeginDate,
      usageEndDate: usageEndDate ?? this.usageEndDate,
      actualUsageDate: actualUsageDate ?? this.actualUsageDate,
    );
  }

  Map<String, Object?> toJson() {
    return {
      'waId': waId,
      'yearNo': yearNo,
      'orgCode': orgCode,
      'growerType': growerType,
      'growerName': growerName,
      'idNumber': idNumber,
      'farmerId': farmerId,
      'companyId': companyId,
      'operateName': operateName,
      'operatePhone': operatePhone,
      'operateIdNumber': operateIdNumber,
      'plotNo': plotNo,
      'plotName': plotName,
      'plotArea': plotArea,
      'plotType': plotType,
      'cropCode': cropCode,
      'cropName': cropName,
      'remark': remark,
      'usageDate': usageDate,
      'waterCons': waterCons,
      'actualWaterUsage': actualWaterUsage,
      'waterFreq': waterFreq,
      'waterReceiverId': waterReceiverId,
      'auditStatus': auditStatus,
      'auditResult': auditResult,
      'statusCd': statusCd,
      'createBy': createBy,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'dataSource': dataSource,
      'auditLevel': auditLevel,
      'accId': accId,
      'advanceCollect': advanceCollect,
      'amount': amount,
      'accumWaterConsume': accumWaterConsume,
      'auditRole': auditRole,
      'params': params,
      'waterReceiverName': waterReceiverName,
      'canal': canal,
      'drainageStatus': drainageStatus,
      'operatorInfoList': operatorInfoList
          ?.map<Map<String, dynamic>>((data) => data.toJson())
          .toList(),
      'priceRecords': priceRecords,
      'orgName': orgName,
      'orgFullName': orgFullName,
      'receiverName': receiverName,
      'distributorName': distributorName,
      'regulatorName': regulatorName,
      'usageBeginDate': usageBeginDate,
      'usageEndDate': usageEndDate,
      'actualUsageDate': actualUsageDate,
    };
  }

  static WaterApplies fromJson(Map<String, Object?> json) {
    return WaterApplies(
      waId: json['waId'] == null ? null : json['waId'] as num,
      yearNo: json['yearNo'] == null ? null : json['yearNo'] as num,
      orgCode: json['orgCode'] == null ? null : json['orgCode'] as String,
      growerType:
          json['growerType'] == null ? null : json['growerType'] as String,
      growerName:
          json['growerName'] == null ? null : json['growerName'] as String,
      idNumber: json['idNumber'] == null ? null : json['idNumber'] as String,
      farmerId: json['farmerId'] as dynamic,
      companyId: json['companyId'] as dynamic,
      operateName: json['operateName'] as dynamic,
      operatePhone:
          json['operatePhone'] == null ? null : json['operatePhone'] as String,
      operateIdNumber: json['operateIdNumber'] as dynamic,
      plotNo: json['plotNo'] == null ? null : json['plotNo'] as String,
      plotName: json['plotName'] == null ? null : json['plotName'] as String,
      plotArea: json['plotArea'] == null ? null : json['plotArea'] as num,
      plotType: json['plotType'] == null ? null : json['plotType'] as String,
      cropCode: json['cropCode'] == null ? null : json['cropCode'] as String,
      cropName: json['cropName'] == null ? null : json['cropName'] as String,
      remark: json['remark'] as dynamic,
      usageDate: json['usageDate'] == null ? null : json['usageDate'] as String,
      waterCons: json['waterCons'] == null ? null : json['waterCons'] as num,
      actualWaterUsage: json['actualWaterUsage'] == null
          ? null
          : json['actualWaterUsage'] as num,
      waterFreq: json['waterFreq'] == null ? null : json['waterFreq'] as num,
      waterReceiverId: json['waterReceiverId'] as dynamic,
      auditStatus:
          json['auditStatus'] == null ? null : json['auditStatus'] as String,
      auditResult:
          json['auditResult'] == null ? null : json['auditResult'] as String,
      statusCd: json['statusCd'] as dynamic,
      createBy: json['createBy'] == null ? null : json['createBy'] as num,
      createTime:
          json['createTime'] == null ? null : json['createTime'] as String,
      updateBy: json['updateBy'] == null ? null : json['updateBy'] as num,
      updateTime:
          json['updateTime'] == null ? null : json['updateTime'] as String,
      dataSource:
          json['dataSource'] == null ? null : json['dataSource'] as String,
      auditLevel: json['auditLevel'] == null ? null : json['auditLevel'] as num,
      accId: json['accId'] == null ? null : json['accId'] as num,
      advanceCollect:
          json['advanceCollect'] == null ? null : json['advanceCollect'] as num,
      amount: json['amount'] == null ? null : json['amount'] as num,
      accumWaterConsume: json['accumWaterConsume'] == null
          ? null
          : json['accumWaterConsume'] as num,
      auditRole: json['auditRole'] as dynamic,
      params: json['params'] as dynamic,
      waterReceiverName: json['waterReceiverName'] as dynamic,
      canal: json['canal'] == null ? null : json['canal'] as String,
      drainageStatus: json['drainageStatus'] == null
          ? null
          : json['drainageStatus'] as String,
      operatorInfoList: json['operatorInfoList'] == null
          ? null
          : (json['operatorInfoList'] as List)
              .map<OperatorInfoList>((data) =>
                  OperatorInfoList.fromJson(data as Map<String, Object?>))
              .toList(),
      priceRecords: json['priceRecords'] as dynamic,
      orgName: json['orgName'] == null ? null : json['orgName'] as String,
      orgFullName:
          json['orgFullName'] == null ? null : json['orgFullName'] as String,
      receiverName:
          json['receiverName'] == null ? null : json['receiverName'] as String,
      distributorName: json['distributorName'] == null
          ? null
          : json['distributorName'] as String,
      regulatorName: json['regulatorName'] == null
          ? null
          : json['regulatorName'] as String,
      usageBeginDate: json['usageBeginDate'] == null
          ? null
          : json['usageBeginDate'] as String,
      usageEndDate:
          json['usageEndDate'] == null ? null : json['usageEndDate'] as String,
      actualUsageDate: json['actualUsageDate'] == null
          ? null
          : json['actualUsageDate'] as String,
    );
  }

  @override
  String toString() {
    return '''WaterApplies(
                waId:$waId,
yearNo:$yearNo,
orgCode:$orgCode,
growerType:$growerType,
growerName:$growerName,
idNumber:$idNumber,
farmerId:$farmerId,
companyId:$companyId,
operateName:$operateName,
operatePhone:$operatePhone,
operateIdNumber:$operateIdNumber,
plotNo:$plotNo,
plotName:$plotName,
plotArea:$plotArea,
plotType:$plotType,
cropCode:$cropCode,
cropName:$cropName,
remark:$remark,
usageDate:$usageDate,
waterCons:$waterCons,
actualWaterUsage:$actualWaterUsage,
waterFreq:$waterFreq,
waterReceiverId:$waterReceiverId,
auditStatus:$auditStatus,
auditResult:$auditResult,
statusCd:$statusCd,
createBy:$createBy,
createTime:$createTime,
updateBy:$updateBy,
updateTime:$updateTime,
dataSource:$dataSource,
auditLevel:$auditLevel,
accId:$accId,
advanceCollect:$advanceCollect,
amount:$amount,
accumWaterConsume:$accumWaterConsume,
auditRole:$auditRole,
params:$params,
waterReceiverName:$waterReceiverName,
canal:$canal,
drainageStatus:$drainageStatus,
operatorInfoList:${operatorInfoList.toString()},
priceRecords:$priceRecords,
orgName:$orgName,
orgFullName:$orgFullName,
receiverName:$receiverName,
distributorName:$distributorName,
regulatorName:$regulatorName,
usageBeginDate:$usageBeginDate,
usageEndDate:$usageEndDate,
actualUsageDate:$actualUsageDate,
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is WaterApplies &&
        other.runtimeType == runtimeType &&
        other.waId == waId &&
        other.yearNo == yearNo &&
        other.orgCode == orgCode &&
        other.growerType == growerType &&
        other.growerName == growerName &&
        other.idNumber == idNumber &&
        other.farmerId == farmerId &&
        other.companyId == companyId &&
        other.operateName == operateName &&
        other.operatePhone == operatePhone &&
        other.operateIdNumber == operateIdNumber &&
        other.plotNo == plotNo &&
        other.plotName == plotName &&
        other.plotArea == plotArea &&
        other.plotType == plotType &&
        other.cropCode == cropCode &&
        other.cropName == cropName &&
        other.remark == remark &&
        other.usageDate == usageDate &&
        other.waterCons == waterCons &&
        other.actualWaterUsage == actualWaterUsage &&
        other.waterFreq == waterFreq &&
        other.waterReceiverId == waterReceiverId &&
        other.auditStatus == auditStatus &&
        other.auditResult == auditResult &&
        other.statusCd == statusCd &&
        other.createBy == createBy &&
        other.createTime == createTime &&
        other.updateBy == updateBy &&
        other.updateTime == updateTime &&
        other.dataSource == dataSource &&
        other.auditLevel == auditLevel &&
        other.accId == accId &&
        other.advanceCollect == advanceCollect &&
        other.amount == amount &&
        other.accumWaterConsume == accumWaterConsume &&
        other.auditRole == auditRole &&
        other.params == params &&
        other.waterReceiverName == waterReceiverName &&
        other.canal == canal &&
        other.drainageStatus == drainageStatus &&
        other.operatorInfoList == operatorInfoList &&
        other.priceRecords == priceRecords &&
        other.orgName == orgName &&
        other.orgFullName == orgFullName &&
        other.receiverName == receiverName &&
        other.distributorName == distributorName &&
        other.regulatorName == regulatorName &&
        other.usageBeginDate == usageBeginDate &&
        other.usageEndDate == usageEndDate &&
        other.actualUsageDate == actualUsageDate;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        waId,
        yearNo,
        orgCode,
        growerType,
        growerName,
        idNumber,
        farmerId,
        companyId,
        operateName,
        operatePhone,
        operateIdNumber,
        plotNo,
        plotName,
        plotArea,
        plotType,
        cropCode,
        cropName,
        remark,
        usageDate);
  }
}

class OperatorInfoList {
  final String? userName;
  final String? taskName;
  const OperatorInfoList({this.userName, this.taskName});
  OperatorInfoList copyWith({String? userName, String? taskName}) {
    return OperatorInfoList(
        userName: userName ?? this.userName,
        taskName: taskName ?? this.taskName);
  }

  Map<String, Object?> toJson() {
    return {'userName': userName, 'taskName': taskName};
  }

  static OperatorInfoList fromJson(Map<String, Object?> json) {
    return OperatorInfoList(
        userName: json['userName'] == null ? null : json['userName'] as String,
        taskName: json['taskName'] == null ? null : json['taskName'] as String);
  }

  @override
  String toString() {
    return '''OperatorInfoList(
                userName:$userName,
taskName:$taskName
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is OperatorInfoList &&
        other.runtimeType == runtimeType &&
        other.userName == userName &&
        other.taskName == taskName;
  }

  @override
  int get hashCode {
    return Object.hash(runtimeType, userName, taskName);
  }
}
