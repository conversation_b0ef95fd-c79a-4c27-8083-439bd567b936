import 'water_manage_index_model.dart';

class WaterUseResultModel {
  final String? dateStr;
  final List<MyApply>? waterApplyList;
  const WaterUseResultModel({this.dateStr, this.waterApplyList});
  WaterUseResultModel copyWith(
      {String? dateStr, List<MyApply>? waterApplyList}) {
    return WaterUseResultModel(
        dateStr: dateStr ?? this.dateStr,
        waterApplyList: waterApplyList ?? this.waterApplyList);
  }

  Map<String, Object?> toJson() {
    return {
      'dateStr': dateStr,
      'waterApplyList': waterApplyList
          ?.map<Map<String, dynamic>>((data) => data.toJson())
          .toList()
    };
  }

  static WaterUseResultModel fromJson(Map<String, Object?> json) {
    return WaterUseResultModel(
        dateStr: json['dateStr'] == null ? null : json['dateStr'] as String,
        waterApplyList: json['waterApplyList'] == null
            ? null
            : (json['waterApplyList'] as List)
                .map<MyApply>(
                    (data) => MyApply.fromJson(data as Map<String, Object?>))
                .toList());
  }

  @override
  String toString() {
    return '''WaterUseResultModel(
                dateStr:$dateStr,
waterApplyList:${waterApplyList.toString()}
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is WaterUseResultModel &&
        other.runtimeType == runtimeType &&
        other.dateStr == dateStr &&
        other.waterApplyList == waterApplyList;
  }

  @override
  int get hashCode {
    return Object.hash(runtimeType, dateStr, waterApplyList);
  }
}
