class FlowItem {
  final int? traceId;
  final int? orderId;
  final String? auditResult;
  final String? auditNote;
  final String? auditValue;
  final int? createBy;
  final int? auditLevel;
  final String? createTime;
  final dynamic updateBy;
  final dynamic updateTime;
  final dynamic remark;
  final dynamic params;
  final dynamic loginName;
  final dynamic staffName;
  final dynamic busId;
  final dynamic busIds;
  final dynamic submitStatus;
  final String? createName;
  final String? auditName;
  const FlowItem(
      {this.traceId,
      this.orderId,
      this.auditResult,
      this.auditNote,
      this.auditValue,
      this.createBy,
      this.auditLevel,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.remark,
      this.params,
      this.loginName,
      this.staffName,
      this.busId,
      this.busIds,
      this.submitStatus,
      this.createName,
      this.auditName});
  FlowItem copyWith(
      {int? traceId,
      int? orderId,
      String? auditResult,
      String? auditNote,
      String? auditValue,
      int? createBy,
      int? auditLevel,
      String? createTime,
      dynamic? updateBy,
      dynamic? updateTime,
      dynamic? remark,
      dynamic? params,
      dynamic? loginName,
      dynamic? staffName,
      dynamic? busId,
      dynamic? busIds,
      dynamic? submitStatus,
      String? createName,
      String? auditName}) {
    return FlowItem(
        traceId: traceId ?? this.traceId,
        orderId: orderId ?? this.orderId,
        auditResult: auditResult ?? this.auditResult,
        auditNote: auditNote ?? this.auditNote,
        auditValue: auditValue ?? this.auditValue,
        createBy: createBy ?? this.createBy,
        auditLevel: auditLevel ?? this.auditLevel,
        createTime: createTime ?? this.createTime,
        updateBy: updateBy ?? this.updateBy,
        updateTime: updateTime ?? this.updateTime,
        remark: remark ?? this.remark,
        params: params ?? this.params,
        loginName: loginName ?? this.loginName,
        staffName: staffName ?? this.staffName,
        busId: busId ?? this.busId,
        busIds: busIds ?? this.busIds,
        submitStatus: submitStatus ?? this.submitStatus,
        createName: createName ?? this.createName,
        auditName: auditName ?? this.auditName);
  }

  Map<String, Object?> toJson() {
    return {
      'traceId': traceId,
      'orderId': orderId,
      'auditResult': auditResult,
      'auditNote': auditNote,
      'auditValue': auditValue,
      'createBy': createBy,
      'auditLevel': auditLevel,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'remark': remark,
      'params': params,
      'loginName': loginName,
      'staffName': staffName,
      'busId': busId,
      'busIds': busIds,
      'submitStatus': submitStatus,
      'createName': createName,
      'auditName': auditName
    };
  }

  static FlowItem fromJson(Map<String, Object?> json) {
    return FlowItem(
        traceId: json['traceId'] == null ? null : json['traceId'] as int,
        orderId: json['orderId'] == null ? null : json['orderId'] as int,
        auditResult:
            json['auditResult'] == null ? null : json['auditResult'] as String,
        auditNote:
            json['auditNote'] == null ? null : json['auditNote'] as String,
        auditValue:
            json['auditValue'] == null ? null : json['auditValue'] as String,
        createBy: json['createBy'] == null ? null : json['createBy'] as int,
        auditLevel:
            json['auditLevel'] == null ? null : json['auditLevel'] as int,
        createTime:
            json['createTime'] == null ? null : json['createTime'] as String,
        updateBy: json['updateBy'] as dynamic,
        updateTime: json['updateTime'] as dynamic,
        remark: json['remark'] as dynamic,
        params: json['params'] as dynamic,
        loginName: json['loginName'] as dynamic,
        staffName: json['staffName'] as dynamic,
        busId: json['busId'] as dynamic,
        busIds: json['busIds'] as dynamic,
        submitStatus: json['submitStatus'] as dynamic,
        createName:
            json['createName'] == null ? null : json['createName'] as String,
        auditName:
            json['auditName'] == null ? null : json['auditName'] as String);
  }

  @override
  String toString() {
    return '''FlowItem(
                traceId:$traceId,
orderId:$orderId,
auditResult:$auditResult,
auditNote:$auditNote,
auditValue:$auditValue,
createBy:$createBy,
auditLevel:$auditLevel,
createTime:$createTime,
updateBy:$updateBy,
updateTime:$updateTime,
remark:$remark,
params:$params,
loginName:$loginName,
staffName:$staffName,
busId:$busId,
busIds:$busIds,
submitStatus:$submitStatus,
createName:$createName,
auditName:$auditName
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is FlowItem &&
        other.runtimeType == runtimeType &&
        other.traceId == traceId &&
        other.orderId == orderId &&
        other.auditResult == auditResult &&
        other.auditNote == auditNote &&
        other.auditValue == auditValue &&
        other.createBy == createBy &&
        other.auditLevel == auditLevel &&
        other.createTime == createTime &&
        other.updateBy == updateBy &&
        other.updateTime == updateTime &&
        other.remark == remark &&
        other.params == params &&
        other.loginName == loginName &&
        other.staffName == staffName &&
        other.busId == busId &&
        other.busIds == busIds &&
        other.submitStatus == submitStatus &&
        other.createName == createName &&
        other.auditName == auditName;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        traceId,
        orderId,
        auditResult,
        auditNote,
        auditValue,
        createBy,
        auditLevel,
        createTime,
        updateBy,
        updateTime,
        remark,
        params,
        loginName,
        staffName,
        busId,
        busIds,
        submitStatus,
        createName,
        auditName);
  }
}
