import 'package:bdh_smart_agric_app/utils/log.dart';

class WaterPlantingInfoItem {
  final int? piId;
  final int? statYear;
  final String? orgCode;
  final String? orgName;
  final String? growerType;
  final String? idNumber;
  final String? plotNo;
  final String? plotName;
  final double? plotArea;
  final String? plotType;
  final String? regimentCanal;
  final String? companyCanal;
  final String? mainCanal;
  final String? branchCanal;
  final String? lateralCanal;
  final String? billingMode;
  final String? growerName;
  final String? operatorName;
  final String? operatorPhone;
  final String? auditResult;
  final String? auditStatus;
  final String? operatorIdNum;
  final num? farmerId;
  final num? companyId;
  final List<PlantingDetail>? attrs;
  const WaterPlantingInfoItem(
      {this.piId,
      this.statYear,
      this.orgCode,
      this.orgName,
      this.growerType,
      this.idNumber,
      this.plotNo,
      this.plotName,
      this.plotArea,
      this.plotType,
      this.regimentCanal,
      this.companyCanal,
      this.mainCanal,
      this.branchCanal,
      this.lateralCanal,
      this.billingMode,
      this.growerName,
      this.operatorName,
      this.operatorPhone,
      this.auditResult,
      this.auditStatus,
      this.farmerId,
      this.companyId,
      this.operatorIdNum,
      this.attrs});
  WaterPlantingInfoItem copyWith(
      {int? piId,
      int? statYear,
      String? orgCode,
      String? orgName,
      String? growerType,
      String? idNumber,
      String? plotNo,
      String? plotName,
      double? plotArea,
      String? plotType,
      String? regimentCanal,
      String? companyCanal,
      String? mainCanal,
      String? branchCanal,
      String? lateralCanal,
      String? billingMode,
      String? growerName,
      String? operatorName,
      String? operatorPhone,
      String? auditResult,
      String? auditStatus,
      String? operatorIdNum,
      num? farmerId,
      num? companyId,
      List<PlantingDetail>? attrs}) {
    return WaterPlantingInfoItem(
        piId: piId ?? this.piId,
        statYear: statYear ?? this.statYear,
        orgCode: orgCode ?? this.orgCode,
        orgName: orgName ?? this.orgName,
        growerType: growerType ?? this.growerType,
        idNumber: idNumber ?? this.idNumber,
        plotNo: plotNo ?? this.plotNo,
        plotName: plotName ?? this.plotName,
        plotArea: plotArea ?? this.plotArea,
        plotType: plotType ?? this.plotType,
        regimentCanal: regimentCanal ?? this.regimentCanal,
        companyCanal: companyCanal ?? this.companyCanal,
        mainCanal: mainCanal ?? this.mainCanal,
        branchCanal: branchCanal ?? this.branchCanal,
        lateralCanal: lateralCanal ?? this.lateralCanal,
        billingMode: billingMode ?? this.billingMode,
        growerName: growerName ?? this.growerName,
        operatorName: operatorName ?? this.operatorName,
        operatorPhone: operatorPhone ?? this.operatorPhone,
        auditResult: auditResult ?? this.auditResult,
        auditStatus: auditStatus ?? this.auditStatus,
        operatorIdNum: operatorIdNum ?? this.operatorIdNum,
        companyId: companyId ?? this.companyId,
        farmerId: farmerId ?? this.farmerId,
        attrs: attrs ?? this.attrs);
  }

  Map<String, Object?> toJson() {
    return {
      'piId': piId,
      'statYear': statYear,
      'orgCode': orgCode,
      'orgName': orgName,
      'growerType': growerType,
      'idNumber': idNumber,
      'plotNo': plotNo,
      'plotName': plotName,
      'plotArea': plotArea,
      'plotType': plotType,
      'regimentCanal': regimentCanal,
      'companyCanal': companyCanal,
      'mainCanal': mainCanal,
      'branchCanal': branchCanal,
      'lateralCanal': lateralCanal,
      'billingMode': billingMode,
      'growerName': growerName,
      'operatorName': operatorName,
      'operatorPhone': operatorPhone,
      'auditResult': auditResult,
      'auditStatus': auditStatus,
      "operatorIdNum": operatorIdNum,
      "farmerId": farmerId,
      "companyId": companyId,
      'attrs':
          attrs?.map<Map<String, dynamic>>((data) => data.toJson()).toList()
    };
  }

  static WaterPlantingInfoItem fromJson(Map<String, Object?> json) {
    Log.d(json);
    return WaterPlantingInfoItem(
        piId: json['piId'] == null ? null : json['piId'] as int,
        statYear: json['statYear'] == null ? null : json['statYear'] as int,
        orgCode: json['orgCode'] == null ? null : json['orgCode'] as String,
        orgName: json['orgName'] == null ? null : json['orgName'] as String,
        growerType:
            json['growerType'] == null ? null : json['growerType'] as String,
        idNumber: json['idNumber'] == null ? null : json['idNumber'] as String,
        plotNo: json['plotNo'] == null ? null : json['plotNo'] as String,
        plotName: json['plotName'] == null ? null : json['plotName'] as String,
        plotArea: json['plotArea'] == null ? null : json['plotArea'] as double,
        plotType: json['plotType'] == null ? null : json['plotType'] as String,
        regimentCanal: json['regimentCanal'] == null
            ? null
            : json['regimentCanal'] as String,
        companyCanal: json['companyCanal'] == null
            ? null
            : json['companyCanal'] as String,
        mainCanal:
            json['mainCanal'] == null ? null : json['mainCanal'] as String,
        branchCanal:
            json['branchCanal'] == null ? null : json['branchCanal'] as String,
        lateralCanal: json['lateralCanal'] == null
            ? null
            : json['lateralCanal'] as String,
        billingMode:
            json['billingMode'] == null ? null : json['billingMode'] as String,
        growerName:
            json['growerName'] == null ? null : json['growerName'] as String,
        operatorName: json['operatorName'] == null
            ? null
            : json['operatorName'] as String,
        operatorPhone: json['operatorPhone'] == null
            ? null
            : json['operatorPhone'] as String,
        auditResult:
            json['auditResult'] == null ? null : json['auditResult'] as String,
        auditStatus:
            json['auditStatus'] == null ? null : json['auditStatus'] as String,
        operatorIdNum: json['operatorIdNum'] == null
            ? null
            : json['operatorIdNum'] as String,
        companyId: json['companyId'] == null ? null : json['companyId'] as num,
        farmerId: json['farmerId'] == null ? null : json['farmerId'] as num,
        attrs: json['attrs'] == null
            ? null
            : (json['attrs'] as List)
                .map<PlantingDetail>((data) =>
                    PlantingDetail.fromJson(data as Map<String, Object?>))
                .toList());
  }

  @override
  String toString() {
    return '''WaterPlantingInfoItem(
                piId:$piId,
statYear:$statYear,
orgCode:$orgCode,
orgName:$orgName,
growerType:$growerType,
idNumber:$idNumber,
plotNo:$plotNo,
plotName:$plotName,
plotArea:$plotArea,
plotType:$plotType,
regimentCanal:$regimentCanal,
companyCanal:$companyCanal,
mainCanal:$mainCanal,
branchCanal:$branchCanal,
lateralCanal:$lateralCanal,
billingMode:$billingMode,
growerName:$growerName,
operatorName:$operatorName,
operatorPhone:$operatorPhone,
auditResult:$auditResult,
auditStatus:$auditStatus,
operatorIdNum: $operatorIdNum,
farmerId: $farmerId,
companyId:$companyId,
attrs:$attrs
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is WaterPlantingInfoItem &&
        other.runtimeType == runtimeType &&
        other.piId == piId &&
        other.statYear == statYear &&
        other.orgCode == orgCode &&
        other.orgName == orgName &&
        other.growerType == growerType &&
        other.idNumber == idNumber &&
        other.plotNo == plotNo &&
        other.plotName == plotName &&
        other.plotArea == plotArea &&
        other.plotType == plotType &&
        other.regimentCanal == regimentCanal &&
        other.companyCanal == companyCanal &&
        other.mainCanal == mainCanal &&
        other.branchCanal == branchCanal &&
        other.lateralCanal == lateralCanal &&
        other.billingMode == billingMode &&
        other.growerName == growerName &&
        other.operatorName == operatorName &&
        other.operatorPhone == operatorPhone &&
        other.auditResult == auditResult &&
        other.auditStatus == auditStatus &&
        other.operatorIdNum == operatorIdNum &&
        other.farmerId == farmerId &&
        other.companyId == companyId &&
        other.attrs == attrs;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        piId,
        statYear,
        orgCode,
        orgName,
        growerType,
        idNumber,
        plotNo,
        plotName,
        plotArea,
        plotType,
        regimentCanal,
        companyCanal,
        mainCanal,
        branchCanal,
        lateralCanal,
        billingMode,
        growerName,
        operatorName,
        operatorPhone);
  }
}

class PlantingDetail {
  final int? piAttrId;
  final int? piId;
  final String? cropCode;
  final String? cropName;
  final num? plotArea;
  final dynamic statusCd;
  final int? createBy;
  final num? createTime;
  final dynamic updateBy;
  final dynamic updateTime;
  final dynamic remark;
  final dynamic params;
  const PlantingDetail(
      {this.piAttrId,
      this.piId,
      this.cropCode,
      this.cropName,
      this.plotArea,
      this.statusCd,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.remark,
      this.params});
  PlantingDetail copyWith(
      {int? piAttrId,
      int? piId,
      String? cropCode,
      String? cropName,
      double? plotArea,
      dynamic? statusCd,
      int? createBy,
      double? createTime,
      dynamic? updateBy,
      dynamic? updateTime,
      dynamic? remark,
      dynamic? params}) {
    return PlantingDetail(
        piAttrId: piAttrId ?? this.piAttrId,
        piId: piId ?? this.piId,
        cropCode: cropCode ?? this.cropCode,
        cropName: cropName ?? this.cropName,
        plotArea: plotArea ?? this.plotArea,
        statusCd: statusCd ?? this.statusCd,
        createBy: createBy ?? this.createBy,
        createTime: createTime ?? this.createTime,
        updateBy: updateBy ?? this.updateBy,
        updateTime: updateTime ?? this.updateTime,
        remark: remark ?? this.remark,
        params: params ?? this.params);
  }

  Map<String, Object?> toJson() {
    return {
      'piAttrId': piAttrId,
      'piId': piId,
      'cropCode': cropCode,
      'cropName': cropName,
      'plotArea': plotArea,
      'statusCd': statusCd,
      'createBy': createBy,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'remark': remark,
      'params': params
    };
  }

  static PlantingDetail fromJson(Map<String, Object?> json) {
    return PlantingDetail(
        piAttrId: json['piAttrId'] == null ? null : json['piAttrId'] as int,
        piId: json['piId'] == null ? null : json['piId'] as int,
        cropCode: json['cropCode'] == null ? null : json['cropCode'] as String,
        cropName: json['cropName'] == null ? null : json['cropName'] as String,
        plotArea: json['plotArea'] == null ? null : json['plotArea'] as num,
        statusCd: json['statusCd'] as dynamic,
        createBy: json['createBy'] == null ? null : json['createBy'] as int,
        createTime:
            json['createTime'] == null ? null : json['createTime'] as num,
        updateBy: json['updateBy'] as dynamic,
        updateTime: json['updateTime'] as dynamic,
        remark: json['remark'] as dynamic,
        params: json['params'] as dynamic);
  }

  @override
  String toString() {
    return '''PlantingDetail(
                piAttrId:$piAttrId,
piId:$piId,
cropCode:$cropCode,
cropName:$cropName,
plotArea:$plotArea,
statusCd:$statusCd,
createBy:$createBy,
createTime:$createTime,
updateBy:$updateBy,
updateTime:$updateTime,
remark:$remark,
params:$params
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is PlantingDetail &&
        other.runtimeType == runtimeType &&
        other.piAttrId == piAttrId &&
        other.piId == piId &&
        other.cropCode == cropCode &&
        other.cropName == cropName &&
        other.plotArea == plotArea &&
        other.statusCd == statusCd &&
        other.createBy == createBy &&
        other.createTime == createTime &&
        other.updateBy == updateBy &&
        other.updateTime == updateTime &&
        other.remark == remark &&
        other.params == params;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        piAttrId,
        piId,
        cropCode,
        cropName,
        plotArea,
        statusCd,
        createBy,
        createTime,
        updateBy,
        updateTime,
        remark,
        params);
  }
}
