class PlotItem {
  final int? statYear;
  final String? organizationNo;
  final String? organizationName;
  final int? farmerId;
  final String? farmerName;
  final String? farmerIdNumber;
  final dynamic userId;
  final dynamic partnerId;
  final dynamic partnerName;
  final dynamic partnerCode;
  final dynamic operatorName;
  final dynamic farmerPhone;
  final dynamic plotNo;
  final String? landNumberNo;
  final String? landNumber;
  final num? chargeArea;
  final String? plotType;
  final String? billingMode;
  final String? mainCanal;
  final String? branchCanal;
  final String? lateralCanal;
  final String? regimentCanal;
  final String? companyCanal;
  final dynamic sexNo;
  final dynamic address;
  final dynamic photoPath;
  final dynamic businessAddress;
  final dynamic busLicensePicUrl;
  final String? growerType;
  final String? cropCode;
  final String? cropName;
  final int? piAttrId;
  final int? piId;
  const PlotItem(
      {this.statYear,
      this.organizationNo,
      this.organizationName,
      this.farmerId,
      this.farmerName,
      this.farmerIdNumber,
      this.userId,
      this.partnerId,
      this.partnerName,
      this.partnerCode,
      this.operatorName,
      this.farmerPhone,
      this.plotNo,
      this.landNumberNo,
      this.landNumber,
      this.chargeArea,
      this.plotType,
      this.billingMode,
      this.mainCanal,
      this.branchCanal,
      this.lateralCanal,
      this.regimentCanal,
      this.companyCanal,
      this.sexNo,
      this.address,
      this.photoPath,
      this.businessAddress,
      this.busLicensePicUrl,
      this.growerType,
      this.cropCode,
      this.cropName,
      this.piAttrId,
      this.piId});
  PlotItem copyWith(
      {int? statYear,
      String? organizationNo,
      String? organizationName,
      int? farmerId,
      String? farmerName,
      String? farmerIdNumber,
      dynamic? userId,
      dynamic? partnerId,
      dynamic? partnerName,
      dynamic? partnerCode,
      dynamic? operatorName,
      dynamic? farmerPhone,
      dynamic? plotNo,
      String? landNumberNo,
      String? landNumber,
      num? chargeArea,
      String? plotType,
      String? billingMode,
      String? mainCanal,
      String? branchCanal,
      String? lateralCanal,
      String? regimentCanal,
      String? companyCanal,
      dynamic? sexNo,
      dynamic? address,
      dynamic? photoPath,
      dynamic? businessAddress,
      dynamic? busLicensePicUrl,
      String? growerType,
      String? cropCode,
      String? cropName,
      int? piAttrId,
      int? piId}) {
    return PlotItem(
        statYear: statYear ?? this.statYear,
        organizationNo: organizationNo ?? this.organizationNo,
        organizationName: organizationName ?? this.organizationName,
        farmerId: farmerId ?? this.farmerId,
        farmerName: farmerName ?? this.farmerName,
        farmerIdNumber: farmerIdNumber ?? this.farmerIdNumber,
        userId: userId ?? this.userId,
        partnerId: partnerId ?? this.partnerId,
        partnerName: partnerName ?? this.partnerName,
        partnerCode: partnerCode ?? this.partnerCode,
        operatorName: operatorName ?? this.operatorName,
        farmerPhone: farmerPhone ?? this.farmerPhone,
        plotNo: plotNo ?? this.plotNo,
        landNumberNo: landNumberNo ?? this.landNumberNo,
        landNumber: landNumber ?? this.landNumber,
        chargeArea: chargeArea ?? this.chargeArea,
        plotType: plotType ?? this.plotType,
        billingMode: billingMode ?? this.billingMode,
        mainCanal: mainCanal ?? this.mainCanal,
        branchCanal: branchCanal ?? this.branchCanal,
        lateralCanal: lateralCanal ?? this.lateralCanal,
        regimentCanal: regimentCanal ?? this.regimentCanal,
        companyCanal: companyCanal ?? this.companyCanal,
        sexNo: sexNo ?? this.sexNo,
        address: address ?? this.address,
        photoPath: photoPath ?? this.photoPath,
        businessAddress: businessAddress ?? this.businessAddress,
        busLicensePicUrl: busLicensePicUrl ?? this.busLicensePicUrl,
        growerType: growerType ?? this.growerType,
        cropCode: cropCode ?? this.cropCode,
        cropName: cropName ?? this.cropName,
        piAttrId: piAttrId ?? this.piAttrId,
        piId: piId ?? this.piId);
  }

  Map<String, Object?> toJson() {
    return {
      'statYear': statYear,
      'organizationNo': organizationNo,
      'organizationName': organizationName,
      'farmerId': farmerId,
      'farmerName': farmerName,
      'farmerIdNumber': farmerIdNumber,
      'userId': userId,
      'partnerId': partnerId,
      'partnerName': partnerName,
      'partnerCode': partnerCode,
      'operatorName': operatorName,
      'farmerPhone': farmerPhone,
      'plotNo': plotNo,
      'landNumberNo': landNumberNo,
      'landNumber': landNumber,
      'chargeArea': chargeArea,
      'plotType': plotType,
      'billingMode': billingMode,
      'mainCanal': mainCanal,
      'branchCanal': branchCanal,
      'lateralCanal': lateralCanal,
      'regimentCanal': regimentCanal,
      'companyCanal': companyCanal,
      'sexNo': sexNo,
      'address': address,
      'photoPath': photoPath,
      'businessAddress': businessAddress,
      'busLicensePicUrl': busLicensePicUrl,
      'growerType': growerType,
      'cropCode': cropCode,
      'cropName': cropName,
      'piAttrId': piAttrId,
      'piId': piId
    };
  }

  static PlotItem fromJson(Map<String, Object?> json) {
    return PlotItem(
        statYear: json['statYear'] == null ? null : json['statYear'] as int,
        organizationNo: json['organizationNo'] == null
            ? null
            : json['organizationNo'] as String,
        organizationName: json['organizationName'] == null
            ? null
            : json['organizationName'] as String,
        farmerId: json['farmerId'] == null ? null : json['farmerId'] as int,
        farmerName:
            json['farmerName'] == null ? null : json['farmerName'] as String,
        farmerIdNumber: json['farmerIdNumber'] == null
            ? null
            : json['farmerIdNumber'] as String,
        userId: json['userId'] as dynamic,
        partnerId: json['partnerId'] as dynamic,
        partnerName: json['partnerName'] as dynamic,
        partnerCode: json['partnerCode'] as dynamic,
        operatorName: json['operatorName'] as dynamic,
        farmerPhone: json['farmerPhone'] as dynamic,
        plotNo: json['plotNo'] as dynamic,
        landNumberNo: json['landNumberNo'] == null
            ? null
            : json['landNumberNo'] as String,
        landNumber:
            json['landNumber'] == null ? null : json['landNumber'] as String,
        chargeArea:
            json['chargeArea'] == null ? null : json['chargeArea'] as num,
        plotType: json['plotType'] == null ? null : json['plotType'] as String,
        billingMode:
            json['billingMode'] == null ? null : json['billingMode'] as String,
        mainCanal:
            json['mainCanal'] == null ? null : json['mainCanal'] as String,
        branchCanal:
            json['branchCanal'] == null ? null : json['branchCanal'] as String,
        lateralCanal: json['lateralCanal'] == null
            ? null
            : json['lateralCanal'] as String,
        regimentCanal: json['regimentCanal'] == null
            ? null
            : json['regimentCanal'] as String,
        companyCanal: json['companyCanal'] == null
            ? null
            : json['companyCanal'] as String,
        sexNo: json['sexNo'] as dynamic,
        address: json['address'] as dynamic,
        photoPath: json['photoPath'] as dynamic,
        businessAddress: json['businessAddress'] as dynamic,
        busLicensePicUrl: json['busLicensePicUrl'] as dynamic,
        growerType:
            json['growerType'] == null ? null : json['growerType'] as String,
        cropCode: json['cropCode'] == null ? null : json['cropCode'] as String,
        cropName: json['cropName'] == null ? null : json['cropName'] as String,
        piAttrId: json['piAttrId'] == null ? null : json['piAttrId'] as int,
        piId: json['piId'] == null ? null : json['piId'] as int);
  }

  @override
  String toString() {
    return '''PlotItem(
                statYear:$statYear,
organizationNo:$organizationNo,
organizationName:$organizationName,
farmerId:$farmerId,
farmerName:$farmerName,
farmerIdNumber:$farmerIdNumber,
userId:$userId,
partnerId:$partnerId,
partnerName:$partnerName,
partnerCode:$partnerCode,
operatorName:$operatorName,
farmerPhone:$farmerPhone,
plotNo:$plotNo,
landNumberNo:$landNumberNo,
landNumber:$landNumber,
chargeArea:$chargeArea,
plotType:$plotType,
billingMode:$billingMode,
mainCanal:$mainCanal,
branchCanal:$branchCanal,
lateralCanal:$lateralCanal,
regimentCanal:$regimentCanal,
companyCanal:$companyCanal,
sexNo:$sexNo,
address:$address,
photoPath:$photoPath,
businessAddress:$businessAddress,
busLicensePicUrl:$busLicensePicUrl,
growerType:$growerType,
cropCode:$cropCode,
cropName:$cropName,
piAttrId:$piAttrId,
piId:$piId
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is PlotItem &&
        other.runtimeType == runtimeType &&
        other.statYear == statYear &&
        other.organizationNo == organizationNo &&
        other.organizationName == organizationName &&
        other.farmerId == farmerId &&
        other.farmerName == farmerName &&
        other.farmerIdNumber == farmerIdNumber &&
        other.userId == userId &&
        other.partnerId == partnerId &&
        other.partnerName == partnerName &&
        other.partnerCode == partnerCode &&
        other.operatorName == operatorName &&
        other.farmerPhone == farmerPhone &&
        other.plotNo == plotNo &&
        other.landNumberNo == landNumberNo &&
        other.landNumber == landNumber &&
        other.chargeArea == chargeArea &&
        other.plotType == plotType &&
        other.billingMode == billingMode &&
        other.mainCanal == mainCanal &&
        other.branchCanal == branchCanal &&
        other.lateralCanal == lateralCanal &&
        other.regimentCanal == regimentCanal &&
        other.companyCanal == companyCanal &&
        other.sexNo == sexNo &&
        other.address == address &&
        other.photoPath == photoPath &&
        other.businessAddress == businessAddress &&
        other.busLicensePicUrl == busLicensePicUrl &&
        other.growerType == growerType &&
        other.cropCode == cropCode &&
        other.cropName == cropName &&
        other.piAttrId == piAttrId &&
        other.piId == piId;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        statYear,
        organizationNo,
        organizationName,
        farmerId,
        farmerName,
        farmerIdNumber,
        userId,
        partnerId,
        partnerName,
        partnerCode,
        operatorName,
        farmerPhone,
        plotNo,
        landNumberNo,
        landNumber,
        chargeArea,
        plotType,
        billingMode,
        mainCanal);
  }
}
