class PaymentRecordModel {
  final String? dateStr;
  final List<PaymentRecordItem>? accList;
  const PaymentRecordModel({this.dateStr, this.accList});
  PaymentRecordModel copyWith(
      {String? dateStr, List<PaymentRecordItem>? accList}) {
    return PaymentRecordModel(
        dateStr: dateStr ?? this.dateStr, accList: accList ?? this.accList);
  }

  Map<String, Object?> toJson() {
    return {
      'dateStr': dateStr,
      'accList':
          accList?.map<Map<String, dynamic>>((data) => data.toJson()).toList()
    };
  }

  static PaymentRecordModel fromJson(Map<String, Object?> json) {
    return PaymentRecordModel(
        dateStr: json['dateStr'] == null ? null : json['dateStr'] as String,
        accList: json['accList'] == null
            ? null
            : (json['accList'] as List)
                .map<PaymentRecordItem>((data) =>
                    PaymentRecordItem.fromJson(data as Map<String, Object?>))
                .toList());
  }

  @override
  String toString() {
    return '''PaymentRecordModel(
                dateStr:$dateStr,
accList:${accList.toString()}
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is PaymentRecordModel &&
        other.runtimeType == runtimeType &&
        other.dateStr == dateStr &&
        other.accList == accList;
  }

  @override
  int get hashCode {
    return Object.hash(runtimeType, dateStr, accList);
  }
}

class PaymentRecordItem {
  //缴费编码
  final int? recordId;
  //年
  final dynamic year;
  //账号id
  final int? accId;
  //记录类型：1 收费 2 退费
  final String? recordType;
  //收费渠道：1 线上 2 线下
  final String? chargeChannel;
  //金额
  final double? amount;
  //收费账户 1 转账 2 银行
  final String? chargeMethod;
  //银行账号
  final String? bankAccount;
  //票据是否打印：1 是 0 否
  final String? isTicketPrinted;
  // 1：缴费成功 0：审核中-缴费状态
  final String? isDeposited;
  //时间戳
  final int? createTime;
  ////账户名-缴费户名
  final String? growerName;
  //账户所属单位-缴费单位
  final String? orgName;
  final String? paymentChannel;

  const PaymentRecordItem({
    this.recordId,
    this.year,
    this.accId,
    this.recordType,
    this.chargeChannel,
    this.amount,
    this.chargeMethod,
    this.bankAccount,
    this.isTicketPrinted,
    this.isDeposited,
    this.createTime,
    this.growerName,
    this.orgName,
    this.paymentChannel,
  });
  PaymentRecordItem copyWith(
      {int? recordId,
      int? year,
      int? accId,
      String? recordType,
      String? chargeChannel,
      double? amount,
      String? chargeMethod,
      String? bankAccount,
      String? isTicketPrinted,
      String? isDeposited,
      int? createTime, ////账户名-缴费户名
      final String? growerName,
      //账户所属单位-缴费单位
      final String? orgName,
      final String? paymentChannel}) {
    return PaymentRecordItem(
        recordId: recordId ?? this.recordId,
        year: year ?? this.year,
        accId: accId ?? this.accId,
        recordType: recordType ?? this.recordType,
        chargeChannel: chargeChannel ?? this.chargeChannel,
        amount: amount ?? this.amount,
        chargeMethod: chargeMethod ?? this.chargeMethod,
        bankAccount: bankAccount ?? this.bankAccount,
        isTicketPrinted: isTicketPrinted ?? this.isTicketPrinted,
        isDeposited: isDeposited ?? this.isDeposited,
        createTime: createTime ?? this.createTime,
        growerName: growerName ?? this.growerName,
        orgName: orgName ?? this.orgName,
        paymentChannel: paymentChannel ?? this.paymentChannel);
  }

  Map<String, Object?> toJson() {
    return {
      'recordId': recordId,
      'year': year,
      'accId': accId,
      'recordType': recordType,
      'chargeChannel': chargeChannel,
      'amount': amount,
      'chargeMethod': chargeMethod,
      'bankAccount': bankAccount,
      'isTicketPrinted': isTicketPrinted,
      'isDeposited': isDeposited,
      'createTime': createTime,
      'growerName': growerName,
      'orgName': orgName,
      'paymentChannel': paymentChannel,
    };
  }

  static PaymentRecordItem fromJson(Map<String, Object?> json) {
    return PaymentRecordItem(
      recordId: json['recordId'] == null ? null : json['recordId'] as int,
      year: json['year'] == null ? null : json['year'] as dynamic,
      accId: json['accId'] == null ? null : json['accId'] as int,
      recordType:
          json['recordType'] == null ? null : json['recordType'] as String,
      chargeChannel: json['chargeChannel'] == null
          ? null
          : json['chargeChannel'] as String,
      amount: json['amount'] == null ? null : json['amount'] as double,
      chargeMethod:
          json['chargeMethod'] == null ? null : json['chargeMethod'] as String,
      bankAccount:
          json['bankAccount'] == null ? null : json['bankAccount'] as String,
      isTicketPrinted: json['isTicketPrinted'] == null
          ? null
          : json['isTicketPrinted'] as String,
      isDeposited:
          json['isDeposited'] == null ? null : json['isDeposited'] as String,
      createTime: json['createTime'] == null ? null : json['createTime'] as int,
      growerName:
          json['growerName'] == null ? null : json['growerName'] as String,
      orgName: json['orgName'] == null ? null : json['orgName'] as String,
      paymentChannel: json['paymentChannel'] == null
          ? null
          : json['paymentChannel'] as String,
    );
  }

  @override
  String toString() {
    return '''AccList(
                recordId:$recordId,
year:$year,
accId:$accId,
recordType:$recordType,
chargeChannel:$chargeChannel,
amount:$amount,
chargeMethod:$chargeMethod,
bankAccount:$bankAccount,
isTicketPrinted:$isTicketPrinted,
isDeposited:$isDeposited,
createTime:$createTime,
growerName:$growerName,
orgName:$orgName,
paymentChannel:$paymentChannel,
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is PaymentRecordItem &&
        other.runtimeType == runtimeType &&
        other.recordId == recordId &&
        other.year == year &&
        other.accId == accId &&
        other.recordType == recordType &&
        other.chargeChannel == chargeChannel &&
        other.amount == amount &&
        other.chargeMethod == chargeMethod &&
        other.bankAccount == bankAccount &&
        other.isTicketPrinted == isTicketPrinted &&
        other.isDeposited == isDeposited &&
        other.growerName == growerName &&
        other.orgName == orgName &&
        other.paymentChannel == paymentChannel &&
        other.createTime == createTime;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        recordId,
        year,
        accId,
        recordType,
        chargeChannel,
        amount,
        chargeMethod,
        bankAccount,
        isTicketPrinted,
        isDeposited,
        createTime,
        growerName,
        orgName,
        paymentChannel);
  }
}
