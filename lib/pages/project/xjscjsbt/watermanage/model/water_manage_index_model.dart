class WaterManageIndexModel {
  final List<MyApply>? myApplyList;
  final List<MyAcc>? myAccList;
  const WaterManageIndexModel({this.myApplyList, this.myAccList});
  WaterManageIndexModel copyWith(
      {List<MyApply>? myApplyList, List<MyAcc>? myAccList}) {
    return WaterManageIndexModel(
        myApplyList: myApplyList ?? this.myApplyList,
        myAccList: myAccList ?? this.myAccList);
  }

  Map<String, Object?> toJson() {
    return {
      'myApplyList': myApplyList
          ?.map<Map<String, dynamic>>((data) => data.toJson())
          .toList(),
      'myAccList':
          myAccList?.map<Map<String, dynamic>>((data) => data.toJson()).toList()
    };
  }

  static WaterManageIndexModel fromJson(Map<String, Object?> json) {
    return WaterManageIndexModel(
        myApplyList: json['myApplyList'] == null
            ? null
            : (json['myApplyList'] as List)
                .map<MyApply>(
                    (data) => MyApply.fromJson(data as Map<String, Object?>))
                .toList(),
        myAccList: json['myAccList'] == null
            ? null
            : (json['myAccList'] as List)
                .map<MyAcc>(
                    (data) => MyAcc.fromJson(data as Map<String, Object?>))
                .toList());
  }

  @override
  String toString() {
    return '''WaterManageIndexModel(
                myApplyList:${myApplyList.toString()},
myAccList:${myAccList.toString()}
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is WaterManageIndexModel &&
        other.runtimeType == runtimeType &&
        other.myApplyList == myApplyList &&
        other.myAccList == myAccList;
  }

  @override
  int get hashCode {
    return Object.hash(runtimeType, myApplyList, myAccList);
  }
}

class MyAcc {
  final int? accId;
  final String? orgCode;
  final String? orgName;
  final String? accCode;
  final String? growerType;
  final String? growerName;
  final String? idNumber;
  final double? accBalance;
  final dynamic piId;
  final dynamic statusCd;
  final dynamic createBy;
  final dynamic createTime;
  final dynamic updateBy;
  final dynamic updateTime;
  final dynamic remark;
  final dynamic params;
  final dynamic con;
  final dynamic year;
  final dynamic operatorName;
  final dynamic operatorPhone;
  final dynamic operatorIdNum;
  final String? busMode;
  const MyAcc(
      {this.accId,
      this.orgCode,
      this.orgName,
      this.accCode,
      this.growerType,
      this.growerName,
      this.idNumber,
      this.accBalance,
      this.piId,
      this.statusCd,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.remark,
      this.params,
      this.con,
      this.year,
      this.operatorName,
      this.operatorPhone,
      this.operatorIdNum,
      this.busMode});
  MyAcc copyWith(
      {int? accId,
      String? orgCode,
      String? orgName,
      String? accCode,
      String? growerType,
      String? growerName,
      String? idNumber,
      double? accBalance,
      dynamic? piId,
      dynamic? statusCd,
      dynamic? createBy,
      dynamic? createTime,
      dynamic? updateBy,
      dynamic? updateTime,
      dynamic? remark,
      dynamic? params,
      dynamic? con,
      dynamic? year,
      dynamic? operatorName,
      dynamic? operatorPhone,
      dynamic? operatorIdNum,
      String? busMode}) {
    return MyAcc(
        accId: accId ?? this.accId,
        orgCode: orgCode ?? this.orgCode,
        orgName: orgName ?? this.orgName,
        accCode: accCode ?? this.accCode,
        growerType: growerType ?? this.growerType,
        growerName: growerName ?? this.growerName,
        idNumber: idNumber ?? this.idNumber,
        accBalance: accBalance ?? this.accBalance,
        piId: piId ?? this.piId,
        statusCd: statusCd ?? this.statusCd,
        createBy: createBy ?? this.createBy,
        createTime: createTime ?? this.createTime,
        updateBy: updateBy ?? this.updateBy,
        updateTime: updateTime ?? this.updateTime,
        remark: remark ?? this.remark,
        params: params ?? this.params,
        con: con ?? this.con,
        year: year ?? this.year,
        operatorName: operatorName ?? this.operatorName,
        operatorPhone: operatorPhone ?? this.operatorPhone,
        operatorIdNum: operatorIdNum ?? this.operatorIdNum,
        busMode: busMode ?? this.busMode);
  }

  Map<String, Object?> toJson() {
    return {
      'accId': accId,
      'orgCode': orgCode,
      'orgName': orgName,
      'accCode': accCode,
      'growerType': growerType,
      'growerName': growerName,
      'idNumber': idNumber,
      'accBalance': accBalance,
      'piId': piId,
      'statusCd': statusCd,
      'createBy': createBy,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'remark': remark,
      'params': params,
      'con': con,
      'year': year,
      'operatorName': operatorName,
      'operatorPhone': operatorPhone,
      'operatorIdNum': operatorIdNum,
      'busMode': busMode,
    };
  }

  static MyAcc fromJson(Map<String, Object?> json) {
    return MyAcc(
      accId: json['accId'] == null ? null : json['accId'] as int,
      orgCode: json['orgCode'] == null ? null : json['orgCode'] as String,
      orgName: json['orgName'] == null ? null : json['orgName'] as String,
      accCode: json['accCode'] == null ? null : json['accCode'] as String,
      growerType:
          json['growerType'] == null ? null : json['growerType'] as String,
      growerName:
          json['growerName'] == null ? null : json['growerName'] as String,
      idNumber: json['idNumber'] == null ? null : json['idNumber'] as String,
      accBalance:
          json['accBalance'] == null ? null : json['accBalance'] as double,
      piId: json['piId'] as dynamic,
      statusCd: json['statusCd'] as dynamic,
      createBy: json['createBy'] as dynamic,
      createTime: json['createTime'] as dynamic,
      updateBy: json['updateBy'] as dynamic,
      updateTime: json['updateTime'] as dynamic,
      remark: json['remark'] as dynamic,
      params: json['params'] as dynamic,
      con: json['con'] as dynamic,
      year: json['year'] as dynamic,
      operatorName: json['operatorName'] as dynamic,
      operatorPhone: json['operatorPhone'] as dynamic,
      operatorIdNum: json['operatorIdNum'] as dynamic,
      busMode: json['busMode'] == null ? null : json['busMode'] as String,
    );
  }

  @override
  String toString() {
    return '''MyAcc(
                accId:$accId,
orgCode:$orgCode,
orgName:$orgName,
accCode:$accCode,
growerType:$growerType,
growerName:$growerName,
idNumber:$idNumber,
accBalance:$accBalance,
piId:$piId,
statusCd:$statusCd,
createBy:$createBy,
createTime:$createTime,
updateBy:$updateBy,
updateTime:$updateTime,
remark:$remark,
params:$params,
con:$con,
year:$year,
operatorName:$operatorName,
operatorPhone:$operatorPhone,
operatorIdNum:$operatorIdNum,
busMode:$busMode,
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is MyAcc &&
        other.runtimeType == runtimeType &&
        other.accId == accId &&
        other.orgCode == orgCode &&
        other.orgName == orgName &&
        other.accCode == accCode &&
        other.growerType == growerType &&
        other.growerName == growerName &&
        other.idNumber == idNumber &&
        other.accBalance == accBalance &&
        other.piId == piId &&
        other.statusCd == statusCd &&
        other.createBy == createBy &&
        other.createTime == createTime &&
        other.updateBy == updateBy &&
        other.updateTime == updateTime &&
        other.remark == remark &&
        other.params == params &&
        other.con == con &&
        other.year == year &&
        other.operatorName == operatorName &&
        other.operatorPhone == operatorPhone &&
        other.operatorIdNum == operatorIdNum &&
        other.busMode == busMode;
  }

  @override
  int get hashCode {
    return Object.hash(
      runtimeType,
      accId,
      orgCode,
      orgName,
      accCode,
      growerType,
      growerName,
      idNumber,
      accBalance,
      piId,
      statusCd,
      createBy,
      createTime,
      updateBy,
      updateTime,
      remark,
      params,
      con,
      year,
      operatorName,
    );
  }
}

class MyApply {
  final int? waId;
  final int? accId;
  final int? yearNo;
  final String? orgCode;
  final String? growerType;
  final String? growerName;
  final String? idNumber;
  final String? operateName;
  final String? operateIdNumber;
  final String? operatePhone;
  final String? plotNo;
  final String? plotName;
  final String? cropCode;
  final String? cropName;
  final String? usageDate;
  final double? waterCons;
  final double? actualWaterUsage;
  final int? waterFreq;
  final double? advanceCollect;
  final double? amount;
  final double? accumWaterConsume;
  final String? drainageStatus;
  final double? plotArea;
  final PlotRel? plotRel;
  const MyApply(
      {this.waId,
      this.accId,
      this.yearNo,
      this.orgCode,
      this.growerType,
      this.growerName,
      this.idNumber,
      this.operateName,
      this.operateIdNumber,
      this.operatePhone,
      this.plotNo,
      this.plotName,
      this.cropCode,
      this.cropName,
      this.usageDate,
      this.waterCons,
      this.actualWaterUsage,
      this.waterFreq,
      this.advanceCollect,
      this.amount,
      this.accumWaterConsume,
      this.drainageStatus,
      this.plotArea,
      this.plotRel});
  MyApply copyWith(
      {int? waId,
      int? accId,
      int? yearNo,
      String? orgCode,
      String? growerType,
      String? growerName,
      String? idNumber,
      String? operateName,
      String? operateIdNumber,
      String? operatePhone,
      String? plotNo,
      String? plotName,
      String? cropCode,
      String? cropName,
      String? usageDate,
      double? waterCons,
      double? actualWaterUsage,
      int? waterFreq,
      double? advanceCollect,
      double? amount,
      double? accumWaterConsume,
      String? drainageStatus,
      double? plotArea,
      PlotRel? plotRel}) {
    return MyApply(
        waId: waId ?? this.waId,
        accId: accId ?? this.accId,
        yearNo: yearNo ?? this.yearNo,
        orgCode: orgCode ?? this.orgCode,
        growerType: growerType ?? this.growerType,
        growerName: growerName ?? this.growerName,
        idNumber: idNumber ?? this.idNumber,
        operateName: operateName ?? this.operateName,
        operateIdNumber: operateIdNumber ?? this.operateIdNumber,
        operatePhone: operatePhone ?? this.operatePhone,
        plotNo: plotNo ?? this.plotNo,
        plotName: plotName ?? this.plotName,
        cropCode: cropCode ?? this.cropCode,
        cropName: cropName ?? this.cropName,
        usageDate: usageDate ?? this.usageDate,
        waterCons: waterCons ?? this.waterCons,
        actualWaterUsage: actualWaterUsage ?? this.actualWaterUsage,
        waterFreq: waterFreq ?? this.waterFreq,
        advanceCollect: advanceCollect ?? this.advanceCollect,
        amount: amount ?? this.amount,
        accumWaterConsume: accumWaterConsume ?? this.accumWaterConsume,
        drainageStatus: drainageStatus ?? this.drainageStatus,
        plotArea: plotArea ?? this.plotArea,
        plotRel: plotRel ?? this.plotRel);
  }

  Map<String, Object?> toJson() {
    return {
      'waId': waId,
      'accId': accId,
      'yearNo': yearNo,
      'orgCode': orgCode,
      'growerType': growerType,
      'growerName': growerName,
      'idNumber': idNumber,
      'operateName': operateName,
      'operateIdNumber': operateIdNumber,
      'operatePhone': operatePhone,
      'plotNo': plotNo,
      'plotName': plotName,
      'cropCode': cropCode,
      'cropName': cropName,
      'usageDate': usageDate,
      'waterCons': waterCons,
      'actualWaterUsage': actualWaterUsage,
      'waterFreq': waterFreq,
      'advanceCollect': advanceCollect,
      'amount': amount,
      'accumWaterConsume': accumWaterConsume,
      'drainageStatus': drainageStatus,
      'plotArea': plotArea,
      'plotRel': plotRel?.toJson()
    };
  }

  static MyApply fromJson(Map<String, Object?> json) {
    return MyApply(
        waId: json['waId'] == null ? null : json['waId'] as int,
        accId: json['accId'] == null ? null : json['accId'] as int,
        yearNo: json['yearNo'] == null ? null : json['yearNo'] as int,
        orgCode: json['orgCode'] == null ? null : json['orgCode'] as String,
        growerType:
            json['growerType'] == null ? null : json['growerType'] as String,
        growerName:
            json['growerName'] == null ? null : json['growerName'] as String,
        idNumber: json['idNumber'] == null ? null : json['idNumber'] as String,
        operateName:
            json['operateName'] == null ? null : json['operateName'] as String,
        operateIdNumber: json['operateIdNumber'] as dynamic,
        operatePhone: json['operatePhone'] == null
            ? null
            : json['operatePhone'] as String,
        plotNo: json['plotNo'] == null ? null : json['plotNo'] as String,
        plotName: json['plotName'] == null ? null : json['plotName'] as String,
        cropCode: json['cropCode'] == null ? null : json['cropCode'] as String,
        cropName: json['cropName'] == null ? null : json['cropName'] as String,
        usageDate:
            json['usageDate'] == null ? null : json['usageDate'] as String,
        waterCons:
            json['waterCons'] == null ? null : json['waterCons'] as double,
        actualWaterUsage: json['actualWaterUsage'] == null
            ? null
            : json['actualWaterUsage'] as double,
        waterFreq: json['waterFreq'] == null ? null : json['waterFreq'] as int,
        advanceCollect: json['advanceCollect'] == null
            ? null
            : json['advanceCollect'] as double,
        amount: json['amount'] == null ? null : json['amount'] as double,
        accumWaterConsume: json['accumWaterConsume'] == null
            ? null
            : json['accumWaterConsume'] as double,
        drainageStatus: json['drainageStatus'] == null
            ? null
            : json['drainageStatus'] as String,
        plotArea: json['plotArea'] == null ? null : json['plotArea'] as double,
        plotRel: json['plotRel'] == null
            ? null
            : PlotRel.fromJson(json['plotRel'] as Map<String, Object?>));
  }

  @override
  String toString() {
    return '''MyApplyList(
                waId:$waId,
accId:$accId,
yearNo:$yearNo,
orgCode:$orgCode,
growerType:$growerType,
growerName:$growerName,
idNumber:$idNumber,
operateName:$operateName,
operateIdNumber:$operateIdNumber,
operatePhone:$operatePhone,
plotNo:$plotNo,
plotName:$plotName,
cropCode:$cropCode,
cropName:$cropName,
usageDate:$usageDate,
waterCons:$waterCons,
actualWaterUsage:$actualWaterUsage,
waterFreq:$waterFreq,
advanceCollect:$advanceCollect,
amount:$amount,
accumWaterConsume:$accumWaterConsume,
drainageStatus:$drainageStatus,
plotArea:$plotArea,
plotRel:${plotRel.toString()}
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is MyApply &&
        other.runtimeType == runtimeType &&
        other.waId == waId &&
        other.accId == accId &&
        other.yearNo == yearNo &&
        other.orgCode == orgCode &&
        other.growerType == growerType &&
        other.growerName == growerName &&
        other.idNumber == idNumber &&
        other.operateName == operateName &&
        other.operateIdNumber == operateIdNumber &&
        other.operatePhone == operatePhone &&
        other.plotNo == plotNo &&
        other.plotName == plotName &&
        other.cropCode == cropCode &&
        other.cropName == cropName &&
        other.usageDate == usageDate &&
        other.waterCons == waterCons &&
        other.actualWaterUsage == actualWaterUsage &&
        other.waterFreq == waterFreq &&
        other.advanceCollect == advanceCollect &&
        other.amount == amount &&
        other.accumWaterConsume == accumWaterConsume &&
        other.drainageStatus == drainageStatus &&
        other.plotArea == plotArea &&
        other.plotRel == plotRel;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        waId,
        accId,
        yearNo,
        orgCode,
        growerType,
        growerName,
        idNumber,
        operateName,
        operateIdNumber,
        operatePhone,
        plotNo,
        plotName,
        cropCode,
        cropName,
        usageDate,
        waterCons,
        actualWaterUsage,
        waterFreq,
        advanceCollect);
  }
}

class PlotRel {
  final String? regimentCanal;
  final String? companyCanal;
  final String? mainCanal;
  final String? branchCanal;
  final String? lateralCanal;
  final String? billingMode;
  final String? plotType;
  const PlotRel(
      {this.regimentCanal,
      this.companyCanal,
      this.mainCanal,
      this.branchCanal,
      this.lateralCanal,
      this.billingMode,
      this.plotType});
  PlotRel copyWith(
      {String? regimentCanal,
      String? companyCanal,
      String? mainCanal,
      String? branchCanal,
      String? lateralCanal,
      String? billingMode,
      String? plotType}) {
    return PlotRel(
        regimentCanal: regimentCanal ?? this.regimentCanal,
        companyCanal: companyCanal ?? this.companyCanal,
        mainCanal: mainCanal ?? this.mainCanal,
        branchCanal: branchCanal ?? this.branchCanal,
        lateralCanal: lateralCanal ?? this.lateralCanal,
        billingMode: billingMode ?? this.billingMode,
        plotType: plotType ?? this.plotType);
  }

  Map<String, Object?> toJson() {
    return {
      'regimentCanal': regimentCanal,
      'companyCanal': companyCanal,
      'mainCanal': mainCanal,
      'branchCanal': branchCanal,
      'lateralCanal': lateralCanal,
      'billingMode': billingMode,
      'plotType': plotType
    };
  }

  static PlotRel fromJson(Map<String, Object?> json) {
    return PlotRel(
        regimentCanal: json['regimentCanal'] == null
            ? null
            : json['regimentCanal'] as String,
        companyCanal: json['companyCanal'] == null
            ? null
            : json['companyCanal'] as String,
        mainCanal:
            json['mainCanal'] == null ? null : json['mainCanal'] as String,
        branchCanal:
            json['branchCanal'] == null ? null : json['branchCanal'] as String,
        lateralCanal: json['lateralCanal'] == null
            ? null
            : json['lateralCanal'] as String,
        billingMode:
            json['billingMode'] == null ? null : json['billingMode'] as String,
        plotType: json['plotType'] == null ? null : json['plotType'] as String);
  }

  @override
  String toString() {
    return '''PlotRel(
                regimentCanal:$regimentCanal,
companyCanal:$companyCanal,
mainCanal:$mainCanal,
branchCanal:$branchCanal,
lateralCanal:$lateralCanal,
billingMode:$billingMode,
plotType:$plotType
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is PlotRel &&
        other.runtimeType == runtimeType &&
        other.regimentCanal == regimentCanal &&
        other.companyCanal == companyCanal &&
        other.mainCanal == mainCanal &&
        other.branchCanal == branchCanal &&
        other.lateralCanal == lateralCanal &&
        other.billingMode == billingMode &&
        other.plotType == plotType;
  }

  @override
  int get hashCode {
    return Object.hash(runtimeType, regimentCanal, companyCanal, mainCanal,
        branchCanal, lateralCanal, billingMode, plotType);
  }
}
