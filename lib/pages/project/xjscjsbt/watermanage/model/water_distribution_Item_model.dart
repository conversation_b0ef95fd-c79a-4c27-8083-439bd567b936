class WaterDistributionItem {
  final int? wrId;
  final String? usageBeginDate;
  final String? usageEndDate;
  final int? createBy;
  final String? createTime;
  final int? updateBy;
  final String? updateTime;
  final String? statusCd;
  final String? orgCode;
  final num? waterUsage;
  final int? plotNumber;
  final int? applicantNumber;
  final String? mainCanal;
  final String? branchCanal;
  final String? lateralCanal;
  final dynamic remark;
  final dynamic params;
  final dynamic waIds;
  final dynamic waterApplies;
  final dynamic waterReleaseApplies;
  final String? orgName;
  final String? orgFullName;
  final int? auditLevel;
  final String? auditStatus;
  final String? auditResult;
  final dynamic mainCanals;
  final dynamic branchCanals;
  final dynamic lateralCanals;
  final dynamic waterDistributor;
  final String? releaseStatus;
  final int? distributorStaffId;
  final dynamic distributorOpTime;
  final int? regulatorStaffId;
  final dynamic regulatorOpTime;
  final String? distributorName;
  final String? regulatorName;
  const WaterDistributionItem(
      {this.wrId,
      this.usageBeginDate,
      this.usageEndDate,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.statusCd,
      this.orgCode,
      this.waterUsage,
      this.plotNumber,
      this.applicantNumber,
      this.mainCanal,
      this.branchCanal,
      this.lateralCanal,
      this.remark,
      this.params,
      this.waIds,
      this.waterApplies,
      this.waterReleaseApplies,
      this.orgName,
      this.orgFullName,
      this.auditLevel,
      this.auditStatus,
      this.auditResult,
      this.mainCanals,
      this.branchCanals,
      this.lateralCanals,
      this.waterDistributor,
      this.releaseStatus,
      this.distributorStaffId,
      this.distributorOpTime,
      this.regulatorStaffId,
      this.regulatorOpTime,
      this.distributorName,
      this.regulatorName});
  WaterDistributionItem copyWith(
      {int? wrId,
      String? usageBeginDate,
      String? usageEndDate,
      int? createBy,
      String? createTime,
      int? updateBy,
      String? updateTime,
      String? statusCd,
      String? orgCode,
      num? waterUsage,
      int? plotNumber,
      int? applicantNumber,
      String? mainCanal,
      String? branchCanal,
      String? lateralCanal,
      dynamic? remark,
      dynamic? params,
      dynamic? waIds,
      dynamic? waterApplies,
      dynamic? waterReleaseApplies,
      String? orgName,
      String? orgFullName,
      int? auditLevel,
      String? auditStatus,
      String? auditResult,
      dynamic? mainCanals,
      dynamic? branchCanals,
      dynamic? lateralCanals,
      dynamic? waterDistributor,
      String? releaseStatus,
      int? distributorStaffId,
      dynamic? distributorOpTime,
      int? regulatorStaffId,
      dynamic? regulatorOpTime,
      String? distributorName,
      String? regulatorName}) {
    return WaterDistributionItem(
        wrId: wrId ?? this.wrId,
        usageBeginDate: usageBeginDate ?? this.usageBeginDate,
        usageEndDate: usageEndDate ?? this.usageEndDate,
        createBy: createBy ?? this.createBy,
        createTime: createTime ?? this.createTime,
        updateBy: updateBy ?? this.updateBy,
        updateTime: updateTime ?? this.updateTime,
        statusCd: statusCd ?? this.statusCd,
        orgCode: orgCode ?? this.orgCode,
        waterUsage: waterUsage ?? this.waterUsage,
        plotNumber: plotNumber ?? this.plotNumber,
        applicantNumber: applicantNumber ?? this.applicantNumber,
        mainCanal: mainCanal ?? this.mainCanal,
        branchCanal: branchCanal ?? this.branchCanal,
        lateralCanal: lateralCanal ?? this.lateralCanal,
        remark: remark ?? this.remark,
        params: params ?? this.params,
        waIds: waIds ?? this.waIds,
        waterApplies: waterApplies ?? this.waterApplies,
        waterReleaseApplies: waterReleaseApplies ?? this.waterReleaseApplies,
        orgName: orgName ?? this.orgName,
        orgFullName: orgFullName ?? this.orgFullName,
        auditLevel: auditLevel ?? this.auditLevel,
        auditStatus: auditStatus ?? this.auditStatus,
        auditResult: auditResult ?? this.auditResult,
        mainCanals: mainCanals ?? this.mainCanals,
        branchCanals: branchCanals ?? this.branchCanals,
        lateralCanals: lateralCanals ?? this.lateralCanals,
        waterDistributor: waterDistributor ?? this.waterDistributor,
        releaseStatus: releaseStatus ?? this.releaseStatus,
        distributorStaffId: distributorStaffId ?? this.distributorStaffId,
        distributorOpTime: distributorOpTime ?? this.distributorOpTime,
        regulatorStaffId: regulatorStaffId ?? this.regulatorStaffId,
        regulatorOpTime: regulatorOpTime ?? this.regulatorOpTime,
        distributorName: distributorName ?? this.distributorName,
        regulatorName: regulatorName ?? this.regulatorName);
  }

  Map<String, Object?> toJson() {
    return {
      'wrId': wrId,
      'usageBeginDate': usageBeginDate,
      'usageEndDate': usageEndDate,
      'createBy': createBy,
      'createTime': createTime,
      'updateBy': updateBy,
      'updateTime': updateTime,
      'statusCd': statusCd,
      'orgCode': orgCode,
      'waterUsage': waterUsage,
      'plotNumber': plotNumber,
      'applicantNumber': applicantNumber,
      'mainCanal': mainCanal,
      'branchCanal': branchCanal,
      'lateralCanal': lateralCanal,
      'remark': remark,
      'params': params,
      'waIds': waIds,
      'waterApplies': waterApplies,
      'waterReleaseApplies': waterReleaseApplies,
      'orgName': orgName,
      'orgFullName': orgFullName,
      'auditLevel': auditLevel,
      'auditStatus': auditStatus,
      'auditResult': auditResult,
      'mainCanals': mainCanals,
      'branchCanals': branchCanals,
      'lateralCanals': lateralCanals,
      'waterDistributor': waterDistributor,
      'releaseStatus': releaseStatus,
      'distributorStaffId': distributorStaffId,
      'distributorOpTime': distributorOpTime,
      'regulatorStaffId': regulatorStaffId,
      'regulatorOpTime': regulatorOpTime,
      'distributorName': distributorName,
      'regulatorName': regulatorName
    };
  }

  static WaterDistributionItem fromJson(Map<String, Object?> json) {
    return WaterDistributionItem(
        wrId: json['wrId'] == null ? null : json['wrId'] as int,
        usageBeginDate: json['usageBeginDate'] == null
            ? null
            : json['usageBeginDate'] as String,
        usageEndDate: json['usageEndDate'] == null
            ? null
            : json['usageEndDate'] as String,
        createBy: json['createBy'] == null ? null : json['createBy'] as int,
        createTime:
            json['createTime'] == null ? null : json['createTime'] as String,
        updateBy: json['updateBy'] == null ? null : json['updateBy'] as int,
        updateTime:
            json['updateTime'] == null ? null : json['updateTime'] as String,
        statusCd: json['statusCd'] == null ? null : json['statusCd'] as String,
        orgCode: json['orgCode'] == null ? null : json['orgCode'] as String,
        waterUsage:
            json['waterUsage'] == null ? null : json['waterUsage'] as num,
        plotNumber:
            json['plotNumber'] == null ? null : json['plotNumber'] as int,
        applicantNumber: json['applicantNumber'] == null
            ? null
            : json['applicantNumber'] as int,
        mainCanal:
            json['mainCanal'] == null ? null : json['mainCanal'] as String,
        branchCanal:
            json['branchCanal'] == null ? null : json['branchCanal'] as String,
        lateralCanal: json['lateralCanal'] == null
            ? null
            : json['lateralCanal'] as String,
        remark: json['remark'] as dynamic,
        params: json['params'] as dynamic,
        waIds: json['waIds'] as dynamic,
        waterApplies: json['waterApplies'] as dynamic,
        waterReleaseApplies: json['waterReleaseApplies'] as dynamic,
        orgName: json['orgName'] == null ? null : json['orgName'] as String,
        orgFullName:
            json['orgFullName'] == null ? null : json['orgFullName'] as String,
        auditLevel:
            json['auditLevel'] == null ? null : json['auditLevel'] as int,
        auditStatus:
            json['auditStatus'] == null ? null : json['auditStatus'] as String,
        auditResult:
            json['auditResult'] == null ? null : json['auditResult'] as String,
        mainCanals: json['mainCanals'] as dynamic,
        branchCanals: json['branchCanals'] as dynamic,
        lateralCanals: json['lateralCanals'] as dynamic,
        waterDistributor: json['waterDistributor'] as dynamic,
        releaseStatus: json['releaseStatus'] == null
            ? null
            : json['releaseStatus'] as String,
        distributorStaffId: json['distributorStaffId'] == null
            ? null
            : json['distributorStaffId'] as int,
        distributorOpTime: json['distributorOpTime'] as dynamic,
        regulatorStaffId: json['regulatorStaffId'] == null
            ? null
            : json['regulatorStaffId'] as int,
        regulatorOpTime: json['regulatorOpTime'] as dynamic,
        distributorName: json['distributorName'] == null
            ? null
            : json['distributorName'] as String,
        regulatorName: json['regulatorName'] == null
            ? null
            : json['regulatorName'] as String);
  }

  @override
  String toString() {
    return '''WaterDistributionItem(
                wrId:$wrId,
usageBeginDate:$usageBeginDate,
usageEndDate:$usageEndDate,
createBy:$createBy,
createTime:$createTime,
updateBy:$updateBy,
updateTime:$updateTime,
statusCd:$statusCd,
orgCode:$orgCode,
waterUsage:$waterUsage,
plotNumber:$plotNumber,
applicantNumber:$applicantNumber,
mainCanal:$mainCanal,
branchCanal:$branchCanal,
lateralCanal:$lateralCanal,
remark:$remark,
params:$params,
waIds:$waIds,
waterApplies:$waterApplies,
waterReleaseApplies:$waterReleaseApplies,
orgName:$orgName,
orgFullName:$orgFullName,
auditLevel:$auditLevel,
auditStatus:$auditStatus,
auditResult:$auditResult,
mainCanals:$mainCanals,
branchCanals:$branchCanals,
lateralCanals:$lateralCanals,
waterDistributor:$waterDistributor,
releaseStatus:$releaseStatus,
distributorStaffId:$distributorStaffId,
distributorOpTime:$distributorOpTime,
regulatorStaffId:$regulatorStaffId,
regulatorOpTime:$regulatorOpTime,
distributorName:$distributorName,
regulatorName:$regulatorName
    ) ''';
  }

  @override
  bool operator ==(Object other) {
    return other is WaterDistributionItem &&
        other.runtimeType == runtimeType &&
        other.wrId == wrId &&
        other.usageBeginDate == usageBeginDate &&
        other.usageEndDate == usageEndDate &&
        other.createBy == createBy &&
        other.createTime == createTime &&
        other.updateBy == updateBy &&
        other.updateTime == updateTime &&
        other.statusCd == statusCd &&
        other.orgCode == orgCode &&
        other.waterUsage == waterUsage &&
        other.plotNumber == plotNumber &&
        other.applicantNumber == applicantNumber &&
        other.mainCanal == mainCanal &&
        other.branchCanal == branchCanal &&
        other.lateralCanal == lateralCanal &&
        other.remark == remark &&
        other.params == params &&
        other.waIds == waIds &&
        other.waterApplies == waterApplies &&
        other.waterReleaseApplies == waterReleaseApplies &&
        other.orgName == orgName &&
        other.orgFullName == orgFullName &&
        other.auditLevel == auditLevel &&
        other.auditStatus == auditStatus &&
        other.auditResult == auditResult &&
        other.mainCanals == mainCanals &&
        other.branchCanals == branchCanals &&
        other.lateralCanals == lateralCanals &&
        other.waterDistributor == waterDistributor &&
        other.releaseStatus == releaseStatus &&
        other.distributorStaffId == distributorStaffId &&
        other.distributorOpTime == distributorOpTime &&
        other.regulatorStaffId == regulatorStaffId &&
        other.regulatorOpTime == regulatorOpTime &&
        other.distributorName == distributorName &&
        other.regulatorName == regulatorName;
  }

  @override
  int get hashCode {
    return Object.hash(
        runtimeType,
        wrId,
        usageBeginDate,
        usageEndDate,
        createBy,
        createTime,
        updateBy,
        updateTime,
        statusCd,
        orgCode,
        waterUsage,
        plotNumber,
        applicantNumber,
        mainCanal,
        branchCanal,
        lateralCanal,
        remark,
        params,
        waIds,
        waterApplies);
  }
}
