import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_org_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/request/xinjiang_water_manage_service.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/widget/bdh_steps2.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import '../../../../components/form/bdh_single_data_picker.dart';
import '../../../../model/org_tree_list_model.dart';
import '../../../../utils/auto_dispose_state_extension.dart';
import 'const_dict.dart';
import 'water_distribution_apply_review_next_page.dart';

//配水管理-用水申请-审核
class WaterDistributionApplyReviewPage extends StatefulWidget {
  const WaterDistributionApplyReviewPage({super.key});

  @override
  State<WaterDistributionApplyReviewPage> createState() =>
      _WaterDistributionApplyReviewPageState();
}

class _WaterDistributionApplyReviewPageState
    extends State<WaterDistributionApplyReviewPage> with AutoDisposeStateMixin {
  late final _Controller controller;
  late final ScrollController _scrollController;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());

    controller = useChangeNotifier(_Controller(context))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  PreferredSizeWidget _widgetAppBar() {
    return AppBar(
      toolbarHeight: kTextTabBarHeight,
      title: const Text("放水记录"),
      actions: [
        GestureDetector(
          onTap: controller.onClickReset,
          child: Text("重置",
              style: TextStyle(
                fontSize: 14.px,
                fontWeight: FontWeight.w500,
                color: const Color.fromRGBO(30, 192, 106, 1),
              )),
        ),
        SizedBox(
          width: 15.px,
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _widgetAppBar(),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: SafeArea(
            top: false,
            child: controller.isLoading
                ? _widgetLoading(context)
                : Stack(
                    children: [
                      _widgetBody(),
                      Positioned(bottom: 0, child: _widgetSubmitButton())
                    ],
                  )));
  }

  Widget _widgetSubmitButton() {
    return Padding(
        padding: EdgeInsets.only(left: 24.px, right: 24.px, bottom: 12.px),
        child: BdhTextButton(
          width: 327.px,
          height: 40.px,
          text: '下一步',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(22.px)),
          backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          disableBackgroundColor: Colors.grey.shade300,
          pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: _onSubmit,
        ));
  }

  void _onSubmit() {
    if (_formKey.currentState!.validate()) {
      if (controller.startTime == null || controller.endTime == null) {
        showToast("请选择时间段");
        return;
      }
      if (controller.startTime!.isAfter(controller.endTime!)) {
        showToast("开始时间不能晚于结束时间");
        return;
      }
      _formKey.currentState!.save();

      controller.next();
    }
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    return Column(
      children: [_widgetStep(), Expanded(child: _widgetList())],
    );
  }

  Widget _widgetList() {
    return Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverPadding(padding: EdgeInsets.only(top: 10.px)),
            SliverToBoxAdapter(
                child: ColoredBox(
                    color: Colors.white,
                    child: Column(
                      children: [
                        Divider(
                          color: const Color.fromRGBO(51, 51, 51, 0.05),
                          height: 1.px,
                          indent: 20.px,
                          endIndent: 20.px,
                        ),

                        _widgetStartDate(),
                        _widgetEndDate(),

                        _widgetCanal()
                        // _widgetOrg(),
                        //   _widgetMainCanal(),
                        //   _widgetBranchCanal(),
                        //   _widgetLateralCanal(),
                      ],
                    )))
          ],
        ));
  }

  Widget _widgetStartDate() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhDatePicker(
          showBottomLine: true,
          checkState: true,
          showArrow: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "用水开始时间",
              data: controller.treeResult?.data,
              isRequired: true),
          initialValue: controller.startTime,
          validator: (v) {
            if (v == null) {
              return "用水开始时间不能为空";
            }
          },
          onSaved: controller.onChangeStartTime,
          onChanged: controller.onChangeStartTime,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetEndDate() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhDatePicker(
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "用水结束时间",
              data: controller.treeResult?.data,
              isRequired: true),
          initialValue: controller.endTime,
          validator: (v) {
            if (v == null) {
              return "用水结束时间不能为空";
            }
            return null;
          },
          onSaved: controller.onChangeEndTime,
          onChanged: controller.onChangeEndTime,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetCanal() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          key: ValueKey(controller.orgCode),
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "干支斗", isRequired: true, data: controller.canalDict ?? []),
          initialValue: controller.cancel,
          validator: (v) {
            if (v == null) {
              return "干支斗不能为空";
            }
            return null;
          },
          onSaved: controller.onSaveCanal,
          onChange: controller.onChangeCanal,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetOrg() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhOrgDataPicker(
          showBottomLine: true,
          autoValidate: true,
          titleWidth: 116.px,
          minHeight: 44.px,
          showFullName: true,
          checkState: true,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "所属连队",
              data: controller.treeResult?.data ?? <OrgTreeItem>[],
              isRequired: true),
          initialValue: controller.orgCode != null
              ? [
                  {
                    "orgCode": controller.orgCode,
                    "orgName": controller.orgName,
                    "orgFullName": controller.orgFullName,
                  }
                ]
              : null,
          validator: (v) {
            if (v == null) {
              return "资格所在单位不能为空";
            }
            if (((v as List).last["orgId"] == null) ||
                (((v as List).last["list"])?.isEmpty ?? true)) {
              return null;
            } else {
              return "请选到最小级别";
            }
          },
          onChange: controller.onChangeOrg,
          onSaved: controller.onSaveOrg,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetMainCanal() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          key: ValueKey(controller.orgCode),
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "干",
              isRequired: false,
              data: controller.mainCanalDict ?? []),
          onSaved: controller.onSaveMainCanal,
          onChange: controller.onChangeMainCanal,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetBranchCanal() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          key: ValueKey(controller.mainCanal),
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "支",
              isRequired: false,
              data: controller.branchCanalDict ?? []),
          onSaved: controller.onSaveBranchCanal,
          onChange: controller.onChangeBranchCanal,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetLateralCanal() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          key: ValueKey("${controller.mainCanal}-${controller.branchCanal}"),
          showBottomLine: false,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "斗",
              isRequired: false,
              data: controller.lateralCanalDict ?? []),
          onSaved: controller.onSaveLateralCanal,
          onChange: controller.onChangeLateralCanal,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetStep() {
    return Container(
        padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: BdhStepsHorizontal(
          steps: waterApplyReviewStepDict,
          activeIndex: 0,
          outerIconSize: 30.px,
          innerIconSize: 24.px,
        ));
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  _Controller(this.context);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  List<DictNode>? mainCanalDict;
  List<DictNode>? branchCanalDict;
  List<DictNode>? lateralCanalDict;

  List<DictNode>? canalDict;

  OrgTreeResult? treeResult;

  void onClickReset() {
    startTime = null;
    endTime = null;
    cancel = null;
    notifyListeners();
  }

  DateTime? startTime;
  void onChangeStartTime(DateTime? time) {
    Log.d("onChangeStartTime $time");
    startTime = time;
  }

  DateTime? endTime;
  void onChangeEndTime(DateTime? time) {
    Log.d("onChangeEndTime $time");
    endTime = time;
  }

  String? orgCode;
  String? orgName;
  String? orgFullName;
  void onSaveOrg(list) {
    Log.d("onSaveOrg $list");
    orgCode = list.last["orgCode"];
  }

  void onChangeOrg(data) {
    var list = data as List;
    if (list.isEmpty) {
      return;
    }
    Log.d("onChangeOrg ${list.lastOrNull}");
    if ((list.last["orgId"] == null) ||
        ((list.last["list"])?.isEmpty ?? true)) {
      orgCode = list.last["orgCode"];
      loadMainCanalDict();
    } else {
      mainCanalDict = null;
      branchCanalDict = null;
      lateralCanalDict = null;
      notifyListeners();
    }
  }

  String? mainCanal;
  void onChangeMainCanal(DictNode? node) {
    Log.d("onChangeMainCanalOrg $node");
    mainCanal = node?.code;

    if (mainCanal != null) {
      loadBranchCanalDict();
    } else {
      mainCanalDict = null;
      branchCanalDict = null;
      lateralCanalDict = null;
      notifyListeners();
    }
  }

  void onSaveMainCanal(DictNode? node) {
    Log.d("onSaveMainCanalOrg $node");
    mainCanal = node?.code;
  }

  String? branchCanal;
  void onChangeBranchCanal(DictNode? node) {
    Log.d("onChangeBranchCanal $node");
    branchCanal = node?.code;
    if (branchCanal != null) {
      loadLateralCanalDict();
    } else {
      branchCanalDict = null;
      lateralCanalDict = null;
      notifyListeners();
    }
  }

  void onSaveBranchCanal(DictNode? node) {
    Log.d("onSaveBranchCanal $node");
    branchCanal = node?.code;
  }

  String? lateralCanal;
  void onChangeLateralCanal(DictNode? node) {
    Log.d("onChangeLateralCanal $node");
    lateralCanal = node?.code;
  }

  void onSaveLateralCanal(DictNode? node) {
    Log.d("onSaveLateralCanal $node");
    lateralCanal = node?.code;
  }

  void loadMainCanalDict() {
    mainCanalDict = null;
    branchCanalDict = null;
    lateralCanalDict = null;
    notifyListeners();
    var data = {
      // "year": 2024,
      "orgCode": orgCode,
      //   "canalType": 0,
    };
    Log.d("loadMainCanalDict $data");
    XinJiangWaterManageService()
        .waterApplyHandleDistributorCanal(data,
            cancelToken: createCancelToken())
        .then((result) {
      Log.d("loadMainCanalDict result $result");
      if (result.code == 0 && result.success == true && result.data != null) {
        mainCanalDict = (result.data as List?)
            ?.map<DictNode>((item) =>
                DictNode(code: item["mainCanal"], name: item["mainCanal"]))
            .toList();
        notifyListeners();
      }
    }).onError(handleError);
  }

  void loadBranchCanalDict() {
    branchCanalDict = null;
    lateralCanalDict = null;
    notifyListeners();
    var data = {
      // "year": 2024,
      "orgCode": orgCode,
      "canalType": 1,
      "mainCanal": mainCanal
    };

    XinJiangWaterManageService()
        .canalList(data, cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true && result.data != null) {
        branchCanalDict = (result.data as List?)
            ?.map<DictNode>((item) =>
                DictNode(code: item["branchCanal"], name: item["branchCanal"]))
            .toList();
        notifyListeners();
      }
    }).onError(handleError);
  }

  void loadLateralCanalDict() {
    lateralCanalDict = null;
    notifyListeners();
    var data = {
      // "year": 2024,
      "orgCode": orgCode,
      "canalType": 2,
      "mainCanal": mainCanal,
      "branchCanal": branchCanal
    };
    XinJiangWaterManageService()
        .canalList(data, cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true && result.data != null) {
        lateralCanalDict = (result.data as List?)
            ?.map<DictNode>((item) => DictNode(
                code: item["lateralCanal"], name: item["lateralCanal"]))
            .toList();
        notifyListeners();
      }
    }).onError(handleError);
  }

  DictNode? cancel;
  void onChangeCanal(DictNode? node) {
    Log.d("onChangeCanal $node");
    cancel = node;
  }

  void onSaveCanal(DictNode? node) {
    Log.d("onSaveMainCanalOrg $node");
    cancel = node;
  }

  //OrgTreeItem? chosenItem;

  void loadData() {
    XinJiangWaterManageService().waterApplyHandleDistributorCanal({},
        cancelToken: createCancelToken()).then((result) {
      canalDict = (result.data as List?)?.map<DictNode>((item) {
            String? mainCanal = (item["mainCanal"] != null &&
                    (item["mainCanal"].isNotEmpty ?? false))
                ? item["mainCanal"][0]
                : "-";

            String? branchCanal = (item["branchCanal"] != null &&
                    (item["branchCanal"].isNotEmpty ?? false))
                ? item["branchCanal"][0]
                : "-";

            String? lateralCanal = (item["lateralCanal"] != null &&
                    (item["lateralCanal"].isNotEmpty ?? false))
                ? item["lateralCanal"][0]
                : "-";

            return DictNode(
                code: item["orgCode"].toString(),
                data: item,
                name: "$mainCanal干$branchCanal支$lateralCanal斗");
          }).toList() ??
          [];
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(handleError);

    // Future.wait([
    //   XinJiangWaterManageService()
    //       .waterApplyHandleDistributorOrg({}, cancelToken: createCancelToken()),

    //   // LandResponsitory.getResidentInfo(),
    // ]).then((list) {
    //   treeResult = list[0] as OrgTreeResult;

    //   // landBaseInfo = (list[1] as LandBaseInfoResult).data;
    //   // Log.d("landBaseInfo is ${landBaseInfo?.toJson()}   ");
    //   _loadingStatus = LoadingStatus.success;
    //   notifyListeners();
    //   Map map = {};
    //   getFirstOrg(treeResult?.data, map);
    //   if (map["orgCode"] != null) {
    //     orgCode = map["orgCode"];
    //     orgName = map["orgName"];
    //     orgFullName = map["orgFullName"];
    //     loadMainCanalDict();
    //   }
    // }).onError(handleError);
  }

  void getFirstOrg(List<OrgTreeItem>? list, Map data) {
    if (list == null || list.isEmpty) return;

    if (data["orgName"] != null) {
      data["orgName"] = "${data["orgName"] ?? ""}/${list.first.orgName}";
    } else {
      data["orgName"] = list.first.orgName;
    }
    if (list.first.children == null || list.first.children!.isEmpty) {
      data["orgCode"] = list.first.orgCode;
      data["orgFullName"] = list.first.orgFullName;
    } else {
      return getFirstOrg(list.first.children, data);
    }
  }

  void next() {
    var orgCode = cancel?.data["orgCode"];

    var mainCanals = cancel?.data["mainCanal"];
    var branchCanals = cancel?.data["branchCanal"];
    var lateralCanals = cancel?.data["lateralCanal"];

    if (orgCode == null) {
      return;
    }
    var map = <String, dynamic>{
      "orgCode": orgCode,
      "usageBeginDate": dateFormat.format(startTime!),
      "usageEndDate": dateFormat.format(endTime!),
    };
    if (mainCanals != null) {
      map["mainCanals"] = mainCanals;
    }
    if (branchCanals != null) {
      map["branchCanals"] = branchCanals;
    }
    if (lateralCanals != null) {
      map["lateralCanals"] = lateralCanals;
    }

    Log.d("next: $map");

    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => WaterDistributionApplyReviewNextPage(
                  item: map,
                )))
        .then((result) {
      if (!context.mounted) {
        return;
      }
      if (result == true) {
        Navigator.maybePop(context, true);
      }
    });
  }
}
