import 'dart:convert';
import 'dart:io';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_count_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/debounce_throttle_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import '../../../../utils/dialog_extensions.dart';
import 'const_dict.dart';
import 'model/crop_item_model.dart';
import 'model/plot_item_model.dart';
import 'model/water_manage_index_model.dart';
import 'request/xinjiang_water_manage_service.dart';
import 'widget/notice_alert_dialog.dart';
import 'widget/price_predict.dart';

//用户-申请用水
class MyWaterUseApplyPage extends StatefulWidget {
  final MyAcc item;
  const MyWaterUseApplyPage({super.key, required this.item});

  @override
  State<MyWaterUseApplyPage> createState() => _MyWaterUseApplyPageState();
}

class _MyWaterUseApplyPageState extends State<MyWaterUseApplyPage>
    with AutoDisposeStateMixin, WidgetsBindingObserver {
  late final ScrollController _scrollController;

  late final _Controller controller;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final GlobalKey _inputKey = GlobalKey();

  List<Map>? priceData;

  late final DebounceValueListener<double> _debounceValueListener;
  bool keyboardVisible = false;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _scrollController = useScrollController(ScrollController());

    controller = useChangeNotifier(_Controller(context, widget.item))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();

    _debounceValueListener =
        DebounceValueListener<double>(const Duration(milliseconds: 200));
    keyboardVisible = false;
    _debounceValueListener.valueChanged = _viewInsetBottomChange;
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _debounceValueListener.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    if (!Platform.isAndroid) {
      return;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentInset = MediaQuery.of(context).viewInsets.bottom;
      _debounceValueListener.add(currentInset);
    });
  }

  void _viewInsetBottomChange(double inset) {
    var visible = false;
    if (inset > 0) {
      visible = true;
    } else {
      visible = false;
    }
    if (visible != keyboardVisible) {
      keyboardVisible = visible;
      if (keyboardVisible && controller._focusNode.hasFocus) {
        final context = _inputKey.currentContext!;
        final renderBox = context.findRenderObject() as RenderBox;
        _scrollController.position.ensureVisible(
          renderBox,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
      if (!keyboardVisible && controller._focusNode.hasFocus) {
        controller._focusNode.unfocus();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("申请用水"),
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: SafeArea(
            top: false,
            child: controller.isLoading
                ? _widgetLoading(context)
                : Stack(
                    children: [
                      _widgetList(),
                      Positioned(bottom: 0, child: _widgetSubmitButton())
                    ],
                  )));
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  String get plotText {
    return controller.plotType ?? "-";
  }

  String get plotArea {
    if (controller.plotArea == null) {
      return "-";
    }
    return "${controller.plotArea}亩";
  }

  Widget _widgetList() {
    return Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            SliverToBoxAdapter(
                child: ColoredBox(
                    color: Colors.white,
                    child: Column(
                      children: [
                        Divider(
                          color: const Color.fromRGBO(51, 51, 51, 0.05),
                          height: 1.px,
                          indent: 20.px,
                          endIndent: 20.px,
                        ),
                        SizedBox(
                          height: 10.px,
                        ),
                        _widgetSummary3(
                          "账户",
                          "${widget.item.growerName}",
                          "${widget.item.accBalance ?? "0"}元",
                        ),
                        SizedBox(
                          height: 10.px,
                        ),
                        Divider(
                          color: const Color.fromRGBO(51, 51, 51, 0.05),
                          height: 1.px,
                          indent: 20.px,
                          endIndent: 20.px,
                        ),
                        SizedBox(
                          height: 10.px,
                        ),
                        _widgetSummary("所在单位", widget.item.orgName ?? ""),
                        SizedBox(
                          height: 10.px,
                        ),
                        Divider(
                          color: const Color.fromRGBO(51, 51, 51, 0.05),
                          height: 1.px,
                          indent: 20.px,
                          endIndent: 20.px,
                        ),
                        _widgetPlotNo(),
                        SizedBox(
                          height: 10.px,
                        ),
                        _widgetSummary(
                            "种植作物",
                            (controller.chosenPlot?.data as PlotItem?)
                                    ?.cropName ??
                                "-"),
                        SizedBox(
                          height: 10.px,
                        ),
                        Divider(
                          color: const Color.fromRGBO(51, 51, 51, 0.05),
                          height: 1.px,
                          indent: 20.px,
                          endIndent: 20.px,
                        ),
                        //_widgetCrop(),
                        SizedBox(
                          height: 10.px,
                        ),
                        //地块类型：1身份地；2经营地
                        _widgetSummary("类型", plotText),
                        SizedBox(
                          height: 10.px,
                        ),
                        Divider(
                          color: const Color.fromRGBO(51, 51, 51, 0.05),
                          height: 1.px,
                          indent: 20.px,
                          endIndent: 20.px,
                        ),
                        SizedBox(
                          height: 10.px,
                        ),
                        _widgetSummary("面积", plotArea),
                        SizedBox(
                          height: 10.px,
                        ),
                        Divider(
                          color: const Color.fromRGBO(51, 51, 51, 0.05),
                          height: 1.px,
                          indent: 20.px,
                          endIndent: 20.px,
                        ),
                        _widgetWaterUseDate(),
                        _widgetWaterUseCount(),
                        SizedBox(
                          height: 10.px,
                        ),
                        _widgetWaterUse(),
                        SizedBox(
                          height: 20.px,
                        ),
                      ],
                    ))),
            _widgetMoneyDict(),
            SliverPadding(
              padding: EdgeInsets.only(
                  left: 26.px, right: 26.px, top: 8.px, bottom: 84.px),
              sliver: SliverToBoxAdapter(
                child: _widgetWarn(),
              ),
            )
          ],
        ));
  }

  Widget _widgetMoneyDict() {
    return SliverPadding(
        padding: EdgeInsets.only(left: 8.px, right: 8.px, top: 8.px),
        sliver: SliverGrid.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3, // 列数
                crossAxisSpacing: 8.px, // 交叉轴间距
                mainAxisSpacing: 8.px, // 主轴间距
                mainAxisExtent: 54.px),
            itemCount: waterDict.length,
            itemBuilder: (context, index) {
              var item = waterDict[index];
              bool checked = controller.waterIndexChecked == index;
              return GestureDetector(
                  onTap: () {
                    controller.onClickWaterCheck(index);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: checked
                          ? const Color.fromRGBO(30, 192, 106, 1)
                          : Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(8.px)),
                    ),
                    height: 54.px,
                    child: Center(
                        child: Text(
                      "${item}m³",
                      style: TextStyle(
                          color: checked ? Colors.white : Colors.black,
                          fontWeight: FontWeight.w500,
                          fontSize: 16.px),
                    )),
                  ));
            }));
  }

  Widget _widgetSummary(String title, String text, {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
          width: 116.px,
          child: Text.rich(TextSpan(children: [
            TextSpan(
              text: "*",
              style: TextStyle(
                  color: Colors.red,
                  fontSize: 16.px,
                  fontWeight: FontWeight.w500),
            ),
            TextSpan(
              text: title,
              style: TextStyle(
                  color: const Color.fromRGBO(51, 51, 51, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: 16.px),
            )
          ])),
        ),
        const Spacer(),
        Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 16.px),
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetCrop() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          minHeight: 44.px,
          titleWidth: 116.px,
          valueSpace: 0,

          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          placeholder: "请作物",
          item: FormItem(title: "种植作物", isRequired: true, data: []),
          // onSaved: controller.onCropChange,
          // onChange: controller.onCropChange,
          validator: (v) {
            if (v == null) {
              return "种植作物不能为空";
            }
            return null;
          },
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetWaterUseDate() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhDatePicker(
          item: FormItem(title: "申请用水时间", isRequired: true),
          initialValue: controller.usageDate,
          checkState: true,
          showBottomLine: true,
          showArrow: true,
          textAlign: TextAlign.right,
          minHeight: 44.px,
          titleWidth: 116.px,
          titleStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500),
          placeholderStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          onChanged: (v) {
            controller.usageDate = v;
          },
          onSaved: (v) {
            controller.usageDate = v;
          },
          validator: (v) {
            if (v == null) {
              return "用水时间不能为空";
            }
            var nowDate = DateTime.now();
            nowDate = nowDate.subtract(const Duration(days: 1));
            if (v.isBefore(nowDate)) {
              return "用水时间必须晚于当前时间";
            }
            return null;
          },
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetWaterUseCount() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhCountPicker(
          initialValue: null,
          showBottomLine: true,
          textAlign: TextAlign.right,
          item: FormItem(title: "用水次数", isRequired: true),
          valueStart: "第",
          valueEnd: "次用水",
          minHeight: 44.px,
          titleWidth: 116.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(22, 183, 96, 1),
              fontWeight: FontWeight.w600),
          valueStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          onSaved: (v) {
            controller.waterFreq = v;
          },
          validator: (v) {
            if (v == null) {
              return "用水次数不能为空";
            }
            return null;
          },
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetPlotNo() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          placeholder: "请选择地块",
          item: FormItem(
              title: "地块编号", isRequired: true, data: controller.plotDict),
          useCode: true,
          onSaved: controller.onPlotChange,
          onChange: controller.onPlotChange,
          initialValue: controller.chosenPlot,
          validator: (v) {
            if (v == null) {
              return "地块不能为空";
            }
            return null;
          },
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetSummary3(String title, String text, String text2,
      {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
            width: 116.px,
            child: Text.rich(TextSpan(children: [
              TextSpan(
                text: "*",
                style: TextStyle(
                    color: Colors.red,
                    fontSize: 16.px,
                    fontWeight: FontWeight.w500),
              ),
              TextSpan(
                text: title,
                style: TextStyle(
                    color: const Color.fromRGBO(51, 51, 51, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 16.px),
              )
            ]))),
        Expanded(
            child: Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 16.px),
        )),
        Text(
          text2,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 16.px),
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetWaterUse() {
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: 20.px,
            ),
            Text.rich(TextSpan(children: [
              TextSpan(
                text: "*",
                style: TextStyle(
                    color: Colors.red,
                    fontSize: 16.px,
                    fontWeight: FontWeight.w500),
              ),
              TextSpan(
                text: "申请用水量",
                style: TextStyle(
                    color: const Color.fromRGBO(51, 51, 51, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 16.px),
              )
            ])),
            const Spacer(),
            PricePredict(
              key: ValueKey(
                  "${controller._editingController.text}-${(controller.chosenPlot?.data as PlotItem?)?.cropCode}}-${(controller.chosenPlot?.data as PlotItem?)?.landNumberNo}-${(controller.chosenPlot?.data as PlotItem?)?.chargeArea}"),
              waterCons: controller._editingController.text,
              yearNo: controller.item.year,
              orgCode: controller.curPlotItem?.organizationNo,
              cropCode: (controller.chosenPlot?.data as PlotItem?)?.cropCode,
              plotNo: (controller.chosenPlot?.data as PlotItem?)?.landNumberNo,
              plotArea: (controller.chosenPlot?.data as PlotItem?)?.chargeArea,
              accId: controller.item.accId,
              onPriceChange: controller.onPricePredictChange,
            ),
            SizedBox(
              width: 20.px,
            ),
          ],
        ),
        SizedBox(
          height: 18.px,
        ),
        Row(
          children: [
            SizedBox(
              width: 20.px,
            ),
            Expanded(
                child: CupertinoTextField.borderless(
              key: _inputKey,
              focusNode: controller._focusNode,
              controller: controller._editingController,
              placeholder: "请输入用水量",
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              prefix: _buildClearButton(),
              prefixMode: OverlayVisibilityMode.editing,
              padding: EdgeInsets.zero,
              style: TextStyle(
                fontSize: 36.px,
                fontWeight: FontWeight.w600,
                fontFamily: "BEBAS",
                color: const Color.fromRGBO(44, 44, 52, 1),
              ),
              placeholderStyle: TextStyle(
                  fontSize: 36.px,
                  fontWeight: FontWeight.w400,
                  fontFamily: "BEBAS",
                  color: const Color.fromRGBO(51, 51, 51, 0.4)),
            )),
            Text(
              "m³",
              style: TextStyle(
                  color: const Color.fromRGBO(51, 51, 51, 1),
                  fontWeight: FontWeight.w600,
                  fontFamily: "BEBAS",
                  fontSize: 20.px),
            ),
            SizedBox(
              width: 20.px,
            ),
          ],
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetWarn() {
    return Text(
      "申请用水量仅供参考,实际结算价格与水量以接水员填写结果为准",
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 14.px,
        fontWeight: FontWeight.w500,
        fontFamily: "BEBAS",
        color: Colors.red,
      ),
    );
  }

  Widget _widgetSubmitButton() {
    return Padding(
        padding: EdgeInsets.only(left: 24.px, right: 24.px, bottom: 12.px),
        child: BdhTextButton(
          width: 327.px,
          height: 40.px,
          text: '申请用水',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(22.px)),
          backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          disableBackgroundColor: Colors.grey.shade300,
          pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: controller.priceRecords == null ? null : _onSubmit,
        ));
  }

  void _onSubmit() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      String text = controller._editingController.text;
      if (text.isEmpty) {
        showToast("请输入用水量");
        return;
      }

      if (controller.priceRecords == null) {
        return;
      }

      if (!RegUtil.isNumericAmount(text)) {
        showToast('用水量输入有误, 请重新输入');
        return;
      }

      double priceRecordTotal = 0;
      priceRecordTotal = controller.priceRecords!.fold<double>(0, (k, v) {
        return k + (v["amount"] ?? 0);
      });

      if (priceRecordTotal > (controller.item.accBalance ?? 0)) {
        showToast("账户余额不能低于低于预计水价");
        return;
      }

      try {
        double actualWaterUsage = double.parse(text);

        controller.submit(actualWaterUsage);
      } catch (error, stackTrace) {
        Log.e("actualWaterUsage pause error",
            error: error, stackTrace: stackTrace);
        showToast("请输入正确用水量");
      }
    }
  }

  Widget _buildClearButton() {
    final String clearLabel =
        CupertinoLocalizations.of(context).clearButtonLabel;

    return Semantics(
      button: true,
      label: clearLabel,
      child: GestureDetector(
        onTap: () {
          controller._editingController.clear();
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6.0),
          child: Icon(
            CupertinoIcons.clear_thick_circled,
            size: 18.0,
            color: CupertinoDynamicColor.resolve(
                const CupertinoDynamicColor.withBrightness(
                  color: Color(0x33000000),
                  darkColor: Color(0x33FFFFFF),
                ),
                context),
          ),
        ),
      ),
    );
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;
  final MyAcc item;

  late final TextEditingController _editingController;

  late FocusNode _focusNode;

  _Controller(this.context, this.item) {
    _focusNode = createFocusNode();
    _editingController = createTextController()..addListener(onTextChange);
  }

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  String? inputWater;

  int? waterIndexChecked;

  List<DictNode> plotDict = [];
  DictNode? _chosenPlot;
  DictNode? get chosenPlot => _chosenPlot;
  set chosenPlot(DictNode? node) {
    _chosenPlot = node;
    notifyListeners();
  }

  // List<DictNode> cropList = [];

  // DictNode? _chosenCrop;
  // DictNode? get chosenCrop => _chosenCrop;
  // set chosenCrop(DictNode? node) {
  //   _chosenCrop = node;
  //   notifyListeners();
  // }

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    var idNumber = item.growerType == "企业" ? item.operatorIdNum : item.idNumber;
    Future.wait([
      XinJiangWaterManageService().getPlantInfoPlot(
          {"idNumber": idNumber, "accId": item.accId},
          cancelToken: createCancelToken()),
      // XinJiangWaterManageService()
      //     .cropList({}, cancelToken: createCancelToken())
    ]).then((list) {
      plotDict = ((list[0]).data as List?)?.map<DictNode>((item) {
            var plotItem = PlotItem.fromJson(item);
            return DictNode(
                code: plotItem.landNumberNo,
                name:
                    "${plotItem.cropName}-${plotItem.chargeArea}亩-${plotItem.landNumberNo}",
                data: plotItem);
          }).toList() ??
          [];

      // cropList = ((list[1]).data as List?)?.map<DictNode>((item) {
      //       var cropItem = CropItem.fromJson(item);
      //       return DictNode(
      //           code: cropItem.raiseCropsCd,
      //           name: cropItem.raiseCropsNm,
      //           data: cropItem);
      //     }).toList() ??
      //     [];

      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  Null _handlerError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  void onClickWaterCheck(int index) {
    waterIndexChecked = index;
    Log.d("onClickWaterCheck $index");
    _editingController.text = waterDict[index].toString();

    notifyListeners();
  }

  String? plotType;

  num? plotArea;

  DateTime? _usageDate;
  DateTime? get usageDate => _usageDate;
  set usageDate(DateTime? date) {
    _usageDate = date;
    notifyListeners();
  }

  int? waterFreq;
  PlotItem? curPlotItem;

  void onPlotChange(DictNode? node) {
    curPlotItem = node?.data as PlotItem;
    plotType = curPlotItem?.plotType;
    plotArea = curPlotItem?.chargeArea;
    chosenPlot = node;
  }

  // void onCropChange(DictNode? node) {
  //   chosenCrop = node;
  // }

  void onTextChange() {
    String text = _editingController.text;

    if (inputWater == text) {
      return;
    }

    inputWater = text;
    Log.d("onTextChange $inputWater");
    if (waterIndexChecked != null &&
        text != waterDict[waterIndexChecked!].toString()) {
      waterIndexChecked = null;
    }
    notifyListeners();
  }

  List? priceRecords;

  void onPricePredictChange(List? value) {
    Log.d("onPricePredictChange $value");
    if (priceRecords != value) {
      priceRecords = value;
      notifyListeners();
    }
  }

  void submit(double actualWaterUsage) {
    var data = {
      "orgCode": (chosenPlot?.data as PlotItem?)?.organizationNo,
      "yearNo": DateTime.now().year,
      "growerType": item.growerType,
      "growerName": item.growerName,
      "idNumber": item.idNumber,
      "plotName": (chosenPlot?.data as PlotItem?)?.landNumber,
      "plotNo": (chosenPlot?.data as PlotItem?)?.landNumberNo,
      "plotArea": (chosenPlot?.data as PlotItem?)?.chargeArea,
      "plotType": (chosenPlot?.data as PlotItem?)?.plotType,
      "cropCode": (chosenPlot?.data as PlotItem?)?.cropCode,
      "cropName": (chosenPlot?.data as PlotItem?)?.cropName,
      "usageDate": dateFormat.format(usageDate!),
      "waterCons": actualWaterUsage,
      "waterFreq": waterFreq,
      "accId": item.accId,
      "priceRecords": priceRecords
    };
    Log.d(jsonEncode(data));

    Log.d("data is $data");

    showConfirmDialog(context, cancel: "取消", confirm: "确认", message: "确认提交吗?")
        .then((result) {
      if (result == true) {
        if (!context.mounted) {
          return;
        }
        showLoading(context, content: "正在提交 ", barrierDismissible: false);

        XinJiangWaterManageService()
            .waterApplyInsert(data, cancelToken: createCancelToken())
            .then((result) {
          if (!context.mounted) {
            return;
          }
          BrnLoadingDialog.dismiss(context);
          if (result.success == true && result.code == 0) {
            showToast("提交成功");
            Navigator.maybePop(context, true);
          }
        }).onError((error, stackTrace) {
          _handlerError(error, stackTrace, errorDo: () {
            BrnLoadingDialog.dismiss(context);
          });
        });
      }
    });
  }
}
