import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:dio/src/cancel_token.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import '../../../../utils/dialog_extensions.dart';
import 'model/page_model.dart';
import 'model/water_distribution_Item_model.dart';
import 'request/xinjiang_water_manage_service.dart';
import 'water_distribution_detail_page.dart';
import 'widget/water_distribution_item_widget.dart';

class WaterDistributionTabPage extends StatefulWidget {
  final int auditLevel;
  final bool showBottomApprovalAll;
  final bool showReject;
  final bool showFallback;
  final bool showApproval;
  final bool showFlow;
  final bool itemClickable;
  final bool showDistributionName;
  const WaterDistributionTabPage(
      {super.key,
      required this.auditLevel,
      required this.showBottomApprovalAll,
      required this.showReject,
      required this.showApproval,
      required this.itemClickable,
      required this.showFlow,
      required this.showDistributionName,
      required this.showFallback});

  @override
  State<WaterDistributionTabPage> createState() =>
      _WaterContainingTabPageState();
}

class _WaterContainingTabPageState extends State<WaterDistributionTabPage>
    with AutoDisposeStateMixin, MixinDefaultWidget {
  late final ScrollController _scrollController;

  late final _Controller controller;

  @override
  void initState() {
    super.initState();

    _scrollController = useScrollController(ScrollController());
    _scrollController.addListener(_scrollListener);
    controller = useChangeNotifier(_Controller(context, widget.auditLevel))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading) return widgetLoading();
    return _widgetBody();
  }

  // Widget _widgetBottom() {
  //   return Container(
  //       color: Colors.white,
  //       width: MediaQuery.of(context).size.width,
  //       padding: EdgeInsets.only(
  //           left: 24.px, right: 12.px, bottom: 12.px, top: 12.px),
  //       child: LayoutBuilder(builder: (context, constraints) {
  //         return SizedBox(
  //             width: constraints.maxWidth,
  //             child: Row(
  //               children: [
  //                 BdhRadio(
  //                   iconSize: 16.px,
  //                   checked: controller.checkedAll,
  //                   onCheckBoxChanged: (selected) {
  //                     if (selected == true) {
  //                       controller.checkAll();
  //                     } else {
  //                       controller.uncheckAll();
  //                     }
  //                   },
  //                 ),
  //                 SizedBox(
  //                   width: 3.px,
  //                 ),
  //                 const Text("全选"),
  //                 SizedBox(
  //                   width: 6.px,
  //                 ),
  //                 const Spacer(),
  //                 BdhTextButton(
  //                   width: 272.px,
  //                   height: 40.px,
  //                   text: '审核',
  //                   textFontWeight: FontWeight.w500,
  //                   textSize: 13.px,
  //                   borderRadius: BorderRadius.all(Radius.circular(22.px)),
  //                   backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
  //                   disableBackgroundColor: Colors.grey.shade400,
  //                   pressedBackgroundColor:
  //                       const Color.fromRGBO(16, 164, 85, 1),
  //                   foregroundColor: Colors.white,
  //                   disableForegroundColor: Colors.white,
  //                   pressedForegroundColor: Colors.white,
  //                   onPressed: controller.checkedItems.isEmpty
  //                       ? null
  //                       : controller.onClickReview,
  //                 )
  //               ],
  //             ));
  //       }));
  // }

  void _scrollListener() async {
    if (!mounted) {
      return;
    }
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      Log.d("_scrollController scroll to bottom");

      controller.loadMore();
    }
  }

  Widget _widgetBody() {
    return Column(
      children: [
        Expanded(
            child: RefreshIndicator(
                color: const Color.fromRGBO(2, 139, 93, 1),
                onRefresh: controller.refresh,
                child: Scrollbar(
                    controller: _scrollController,
                    child: CustomScrollView(
                      controller: _scrollController,
                      physics: const ClampingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      slivers: [
                        SliverPadding(padding: EdgeInsets.only(bottom: 6.px)),
                        if (controller.items.isNotEmpty) ...[
                          _widgetList(),
                          SliverToBoxAdapter(
                            child: _loadMore(context),
                          ),
                        ],
                        if (controller.items.isEmpty)
                          SliverFillRemaining(
                              hasScrollBody: false, child: widgetEmpty()),
                      ],
                    )))),
        if (controller.items.isNotEmpty && widget.showBottomApprovalAll)
          _widgetBottom()
      ],
    );
  }

  Widget _widgetBottom() {
    return Container(
        color: Colors.white,
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.only(
            left: 24.px, right: 12.px, bottom: 12.px, top: 12.px),
        child: LayoutBuilder(builder: (context, constraints) {
          return SizedBox(
              width: constraints.maxWidth,
              child: Row(
                children: [
                  BdhRadio(
                    iconSize: 16.px,
                    checked: controller.checkedAll,
                    onCheckBoxChanged: (selected) {
                      if (selected == true) {
                        controller.checkAll();
                      } else {
                        controller.uncheckAll();
                      }
                    },
                  ),
                  SizedBox(
                    width: 3.px,
                  ),
                  const Text("全选"),
                  SizedBox(
                    width: 6.px,
                  ),
                  const Spacer(),
                  BdhTextButton(
                    width: 272.px,
                    height: 40.px,
                    text: '审核',
                    textFontWeight: FontWeight.w500,
                    textSize: 13.px,
                    borderRadius: BorderRadius.all(Radius.circular(22.px)),
                    backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
                    disableBackgroundColor: Colors.grey.shade400,
                    pressedBackgroundColor:
                        const Color.fromRGBO(16, 164, 85, 1),
                    foregroundColor: Colors.white,
                    disableForegroundColor: Colors.white,
                    pressedForegroundColor: Colors.white,
                    onPressed: controller.checkedItems.isEmpty
                        ? null
                        : controller.onClickReview,
                  )
                ],
              ));
        }));
  }

  Widget _widgetList() {
    return SliverList.builder(
      itemBuilder: (context, index) {
        return WaterDistributionItemWidget(
          checked: controller.isChecked(index),
          checkable: widget.showBottomApprovalAll,
          item: controller.items[index],
          onClick: widget.itemClickable ? controller.onClickItem : null,
          onCheck: controller.onItemCheck,
          onClickApproval:
              widget.showApproval ? controller.onClickApproval : null,
          onClickFlow: widget.showFlow ? controller.onClickFlow : null,
          onClickFallback:
              widget.showFallback ? controller.onClickFallback : null,
          onClickReject: widget.showReject ? controller.onClickReject : null,
          index: index,
        );
      },
      itemCount: controller.items.length,
    );
  }

  Widget _loadMore(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 20.px, right: 20.px, bottom: 5.px),
      child: Text(
        controller.needLoadMore ? "加载更多" : "没有更多数据了",
        style: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(24, 66, 56, 0.4)),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _Controller extends AutoDisposeChangeNotifier
    with
        LoadMoreChangeNotifier<WaterDistributionItem>,
        MultiCheckChangeNotifier<WaterDistributionItem> {
  @override
  final BuildContext context;

  final int auditLevel;

  _Controller(this.context, this.auditLevel);

  //加载数据
  void loadData() {
    reload(showLoading: true);
  }

  //当前 item
  @override
  List<WaterDistributionItem> items = [];

  @override
  List<WaterDistributionItem> get multiCheckableItems => items;

  @override
  Future reloadFuture(
      {required bool showLoading,
      required bool loadingMore,
      required bool refresh}) {
    var data = {
      "auditLevel": auditLevel,
      "page": page,
      "rows": row,
    };
    Future<RequestNoData> Function(dynamic data, {CancelToken? cancelToken})?
        requestFunction;
    if (auditLevel == 2) {
      requestFunction =
          XinJiangWaterManageService().waterApplyHandleDistributorMonitorPage;
    } else if (auditLevel == 5) {
      requestFunction =
          XinJiangWaterManageService().waterApplyHandleDistributorRunningPage;
    } else if (auditLevel == 6) {
      requestFunction =
          XinJiangWaterManageService().waterApplyHandleDistributorDonePage;
    }
    return requestFunction
            ?.call(data, cancelToken: createCancelToken())
            .then((result) {
          if (result.code == 0 && result.success == true) {
            var page = PageModel.fromJson(result.data);
            var loadItems = page.records?.map<WaterDistributionItem>((item) {
                  return WaterDistributionItem.fromJson(item);
                }).toList() ??
                [];

            total = page.total ?? 0;
            if (refresh || showLoading) {
              items.clear();
              checkedItems.clear();
            }
            Log.d("load success ");
            items.addAll(loadItems);
          }
          loadingStatus = LoadingStatus.success;
          notifyListeners();
        }).onError((error, stackTrace) {
          handleError(error, stackTrace, errorDo: () {
            loadingStatus = LoadingStatus.success;
          });
        }) ??
        Future.value(false);
    ;
  }

  void onClickItem(WaterDistributionItem item) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => WaterDistributionDetailPage(
                  item: item,
                )))
        .then((result) {
      if (result == true) {
        reload(showLoading: true);
      }
    });
  }

  void onClickReject(WaterDistributionItem item) {}

  void onClickApproval(WaterDistributionItem item) {
    showConfirmDialog(context, cancel: "取消", confirm: "确认", message: "确认提交吗?")
        .then((result) {
      if (result == true) {
        if (!context.mounted) {
          return;
        }
        var data = {
          "auditNote": "",
          "busIds": [item.wrId]
        };
        Log.d("data is $data");
        showLoading(context, content: "正在提交 ", barrierDismissible: false);
        XinJiangWaterManageService()
            .waterApplyHandleDistributorReleaseSubmit(data,
                cancelToken: createCancelToken())
            .then((result) {
          if (!context.mounted) {
            return;
          }
          BrnLoadingDialog.dismiss(context);
          if (result.success == true && result.code == 0) {
            showToast("提交成功");
            reload(showLoading: true);
          }
        }).onError((error, stackTrace) {
          handleError(error, stackTrace, errorDo: () {
            BrnLoadingDialog.dismiss(context);
          });
        });
      }
    });
  }

  void onClickFallback(WaterDistributionItem item) {
    showConfirmDialog(context, cancel: "取消", confirm: "确认", message: "确认退回吗?")
        .then((result) {
      if (result == true) {
        if (!context.mounted) {
          return;
        }
        var data = {
          "auditNote": "",
          "busIds": [item.wrId]
        };
        Log.d("data is $data");

        showLoading(context, content: "正在提交 ", barrierDismissible: false);
        XinJiangWaterManageService()
            .waterApplyHandleDistributorReleaseReject(data,
                cancelToken: createCancelToken())
            .then((result) {
          if (!context.mounted) {
            return;
          }
          BrnLoadingDialog.dismiss(context);
          if (result.success == true && result.code == 0) {
            showToast("提交成功");
            reload(showLoading: true);
          }
        }).onError((error, stackTrace) {
          handleError(error, stackTrace, errorDo: () {
            BrnLoadingDialog.dismiss(context);
          });
        });
      }
    });
  }

  void onClickFlow(WaterDistributionItem item) {
    // Navigator.of(context)
    //     .push(CupertinoPageRoute(
    //         builder: (_) => FlowWaterApplyInfoPage(
    //               waId: item.waId!,
    //             )))
    //     .then((result) {
    //   if (result == true) {
    //     reload(showLoading: true);
    //   }
    // });
  }

  void onClickReview() {
    if (checkedItems.isEmpty) {
      return;
    }

    showConfirmDialog(context, cancel: "取消", confirm: "确认", message: "确认提交吗?")
        .then((result) {
      if (result == true) {
        if (!context.mounted) {
          return;
        }
        var checkedIds = checkedItems.map((item) => item.wrId).toList();

        var data = {"auditNote": "", "busIds": checkedIds};
        Log.d("data is $data");

        showLoading(context, content: "正在提交 ", barrierDismissible: false);
        XinJiangWaterManageService()
            .waterApplyHandleDistributorReleaseSubmit(data,
                cancelToken: createCancelToken())
            .then((result) {
          if (!context.mounted) {
            return;
          }
          BrnLoadingDialog.dismiss(context);
          if (result.success == true && result.code == 0) {
            showToast("提交成功");
            reload(showLoading: true);
          }
        }).onError((error, stackTrace) {
          handleError(error, stackTrace, errorDo: () {
            BrnLoadingDialog.dismiss(context);
          });
        });
      }
    });
  }
}
