import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_org_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/flow_water_apply_info_page.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/model/water_apply_item_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/request/xinjiang_water_manage_service.dart';

import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';

import '../../../../components/form/bdh_single_data_picker.dart';
import '../../../../model/org_tree_list_model.dart';
import '../../../../utils/auto_dispose_state_extension.dart';
import 'const_dict.dart';
import 'model/page_model.dart';
import 'my_water_use_detail_page.dart';
import 'water_settle_apply_page.dart';
import 'widget/water_settle_item_widget.dart';

//结算水费-放水记录
class WaterSettlePage extends StatefulWidget {
  const WaterSettlePage({super.key});

  @override
  State<WaterSettlePage> createState() => _WaterSettlePageState();
}

class _WaterSettlePageState extends State<WaterSettlePage>
    with AutoDisposeStateMixin {
  late final _Controller controller;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();

    controller = useChangeNotifier(_Controller(context))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  PreferredSizeWidget _widgetAppBar() {
    return AppBar(
      toolbarHeight: kTextTabBarHeight,
      title: const Text("结算列表"),
      actions: [
        GestureDetector(
          onTap: controller.onClickReset,
          child: Text("重置",
              style: TextStyle(
                fontSize: 14.px,
                fontWeight: FontWeight.w500,
                color: const Color.fromRGBO(30, 192, 106, 1),
              )),
        ),
        SizedBox(
          width: 15.px,
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _widgetAppBar(),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: SafeArea(
            top: false,
            child: controller.isLoading
                ? _widgetLoading(context)
                : Stack(
                    children: [
                      _widgetBody(),
                      Positioned(bottom: 0, child: _widgetSubmitButton())
                    ],
                  )));
  }

  Widget _widgetSubmitButton() {
    return Padding(
        padding: EdgeInsets.only(left: 24.px, right: 24.px, bottom: 12.px),
        child: BdhTextButton(
          width: 327.px,
          height: 40.px,
          text: '结算水费',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(22.px)),
          backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          disableBackgroundColor: Colors.grey.shade300,
          pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: _onSubmit,
        ));
  }

  void _onSubmit() {
    controller.next();
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    return Column(
      children: [
        _widgetSearch(),
        SizedBox(
          height: 10.px,
        ),
        Expanded(
            child: _ContextWidget(
          key: ValueKey(
              "${controller.nextCount}-${controller.startTime}-${controller.endTime}-${controller.orgCode}-${controller.mainCanal?.code}-${controller.branchCanal?.code}-${controller.lateralCanal?.code}"),
          usageBeginDate: controller.startTime,
          usageEndDate: controller.endTime,
          orgCode: controller.orgCode,
          mainCanal: controller.mainCanal?.code,
          branchCanal: controller.branchCanal?.code,
          lateralCanal: controller.lateralCanal?.code,
        ))
      ],
    );
  }

  Widget _widgetStartDate() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhDatePicker(
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          initialValue: controller.startTime,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "选择开始时间",
              data: controller.treeResult?.data,
              isRequired: false),
          onSaved: controller.onChangeStartTime,
          onChanged: controller.onChangeStartTime,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetEndDate() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhDatePicker(
          showBottomLine: controller.fullSearch ? true : false,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          initialValue: controller.endTime,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "选择结束时间",
              data: controller.treeResult?.data,
              isRequired: false),
          onSaved: controller.onChangeEndTime,
          onChanged: controller.onChangeEndTime,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetOrg() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhOrgDataPicker(
          showBottomLine: true,
          autoValidate: true,
          titleWidth: 116.px,
          minHeight: 44.px,
          showFullName: false,
          checkState: true,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(
              title: "所属连队",
              data: controller.treeResult?.data ?? <OrgTreeItem>[],
              isRequired: false),
          initialValue: controller.orgData,
          validator: (v) {
            if (v == null) {
              return null;
            }
            if (((v as List).last["orgId"] == null) ||
                (((v as List).last["list"])?.isEmpty ?? true)) {
              return null;
            } else {
              return "请选到最小级别";
            }
          },
          onChange: controller.onChangeOrg,
          onSaved: controller.onSaveOrg,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetMainCanal() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          key: ValueKey(controller.orgCode),
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          initialValue: controller.mainCanal,
          item: FormItem(
              title: "干",
              isRequired: false,
              data: controller.mainCanalDict ?? []),
          onSaved: controller.onSaveMainCanal,
          onChange: controller.onChangeMainCanal,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetBranchCanal() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          key: ValueKey(controller.mainCanal),
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          initialValue: controller.branchCanal,
          item: FormItem(
              title: "支",
              isRequired: false,
              data: controller.branchCanalDict ?? []),
          onSaved: controller.onSaveBranchCanal,
          onChange: controller.onChangeBranchCanal,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetLateralCanal() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          key: ValueKey("${controller.mainCanal}-${controller.branchCanal}"),
          showBottomLine: false,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 116.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          initialValue: controller.lateralCanal,
          item: FormItem(
              title: "斗",
              isRequired: false,
              data: controller.lateralCanalDict ?? []),
          onSaved: controller.onSaveLateralCanal,
          onChange: controller.onChangeLateralCanal,
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetFullSearchControl() {
    return GestureDetector(
        onTap: controller.fullSearchTrigger,
        child: SizedBox(
          height: 44.px,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(controller.fullSearch ? "收起" : "展开",
                  style: TextStyle(
                      fontSize: 14.px,
                      color: const Color.fromRGBO(51, 51, 51, 0.4))),
              Icon(
                controller.fullSearch
                    ? Icons.keyboard_arrow_up
                    : Icons.keyboard_arrow_down,
                size: 20.px,
                color: const Color.fromRGBO(51, 51, 51, 0.4),
              )
            ],
          ),
        ));
  }

  Widget _widgetSearch() {
    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.disabled,
            child: Column(
              children: [
                Divider(
                  color: const Color.fromRGBO(51, 51, 51, 0.05),
                  height: 1.px,
                ),
                _widgetStartDate(),
                _widgetEndDate(),
                Visibility(
                    maintainState: true,
                    visible: controller.fullSearch,
                    child: _widgetOrg()),
                Visibility(
                    maintainState: true,
                    visible: controller.fullSearch,
                    child: _widgetMainCanal()),
                Visibility(
                    maintainState: true,
                    visible: controller.fullSearch,
                    child: _widgetBranchCanal()),
                Visibility(
                    maintainState: true,
                    visible: controller.fullSearch,
                    child: _widgetLateralCanal()),
                _widgetFullSearchControl()
              ],
            )));
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  _Controller(this.context);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  List<DictNode>? mainCanalDict;
  List<DictNode>? branchCanalDict;
  List<DictNode>? lateralCanalDict;

  List<DictNode>? canalDict;

  OrgTreeResult? treeResult;

  DateTime? startTime;

  void onClickReset() {
    startTime = null;
    endTime = null;
    orgCode = null;
    orgData = null;

    mainCanalDict = null;
    branchCanalDict = null;
    lateralCanalDict = null;
    mainCanal = null;
    branchCanal = null;
    lateralCanal = null;
    canalDict = null;
    notifyListeners();
  }

  void onChangeStartTime(DateTime? time) {
    Log.d("onChangeStartTime $time");
    if (time == null) {
      startTime = null;
      return;
    }
    startTime = time;
    notifyListeners();
  }

  DateTime? endTime;
  void onChangeEndTime(DateTime? time) {
    Log.d("onChangeEndTime $time");
    if (time == null) {
      endTime = null;
      return;
    }
    endTime = time;
    notifyListeners();
  }

  String? orgCode;
  List? orgData;
  void onSaveOrg(list) {
    Log.d("onSaveOrg $list");
    orgData = list;
    orgCode = list.last["orgCode"];
  }

  void onChangeOrg(data) {
    var list = data as List;
    if (list.isEmpty) {
      return;
    }
    //Log.d("onChangeOrg: length:${list.length} ");
    //Log.d("onChangeOrg: length:${list.last} ");
    orgData = data;
    if ((list.last["orgId"] == null) ||
        ((list.last["list"])?.isEmpty ?? true)) {
      orgCode = list.last["orgCode"];
      loadMainCanalDict();
    } else {
      mainCanalDict = null;
      branchCanalDict = null;
      lateralCanalDict = null;
      notifyListeners();
    }
  }

  DictNode? mainCanal;
  void onChangeMainCanal(DictNode? node) {
    Log.d("onChangeMainCanalOrg $node");
    mainCanal = node;

    if (mainCanal != null) {
      loadBranchCanalDict();
    } else {
      mainCanalDict = null;
      branchCanalDict = null;
      lateralCanalDict = null;
      notifyListeners();
    }
  }

  void onSaveMainCanal(DictNode? node) {
    Log.d("onSaveMainCanalOrg $node");
    mainCanal = node;
  }

  DictNode? branchCanal;
  void onChangeBranchCanal(DictNode? node) {
    Log.d("onChangeBranchCanal $node");
    branchCanal = node;
    if (branchCanal != null) {
      loadLateralCanalDict();
    } else {
      branchCanalDict = null;
      lateralCanalDict = null;
      notifyListeners();
    }
  }

  void onSaveBranchCanal(DictNode? node) {
    Log.d("onSaveBranchCanal $node");
    branchCanal = node;
  }

  DictNode? lateralCanal;
  void onChangeLateralCanal(DictNode? node) {
    Log.d("onChangeLateralCanal $node");
    lateralCanal = node;
    notifyListeners();
  }

  void onSaveLateralCanal(DictNode? node) {
    Log.d("onSaveLateralCanal $node");
    lateralCanal = node;
  }

  void loadMainCanalDict() {
    mainCanalDict = null;
    branchCanalDict = null;
    lateralCanalDict = null;
    mainCanal = null;
    branchCanal = null;
    lateralCanal = null;
    notifyListeners();
    var data = {
      // "year": 2024,
      "orgCode": orgCode,
      "canalType": 0,
    };
    Log.d("loadMainCanalDict $data");
    XinJiangWaterManageService()
        .canalList(data, cancelToken: createCancelToken())
        .then((result) {
      Log.d("loadMainCanalDict result $result");
      if (result.code == 0 && result.success == true && result.data != null) {
        mainCanalDict = (result.data as List?)
            ?.map<DictNode>((item) =>
                DictNode(code: item["mainCanal"], name: item["mainCanal"]))
            .toList();
        _loadingStatus = LoadingStatus.success;
        notifyListeners();
      }
    }).onError(handleError);
  }

  void loadBranchCanalDict() {
    branchCanalDict = null;
    lateralCanalDict = null;
    branchCanal = null;
    lateralCanal = null;
    notifyListeners();
    var data = {
      // "year": 2024,
      "orgCode": orgCode,
      "canalType": 1,
      "mainCanal": mainCanal?.code
    };

    XinJiangWaterManageService()
        .canalList(data, cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true && result.data != null) {
        branchCanalDict = (result.data as List?)
            ?.map<DictNode>((item) =>
                DictNode(code: item["branchCanal"], name: item["branchCanal"]))
            .toList();
        notifyListeners();
      }
    }).onError(handleError);
  }

  void loadLateralCanalDict() {
    lateralCanalDict = null;
    lateralCanal = null;
    notifyListeners();
    var data = {
      // "year": 2024,
      "orgCode": orgCode,
      "canalType": 2,
      "mainCanal": mainCanal?.code,
      "branchCanal": branchCanal?.code
    };
    XinJiangWaterManageService()
        .canalList(data, cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true && result.data != null) {
        lateralCanalDict = (result.data as List?)
            ?.map<DictNode>((item) => DictNode(
                code: item["lateralCanal"], name: item["lateralCanal"]))
            .toList();
        notifyListeners();
      }
    }).onError(handleError);
  }

  DictNode? cancel;
  void onChangeCanal(DictNode? node) {
    Log.d("onChangeCanal $node");
    cancel = node;
  }

  void onSaveCanal(DictNode? node) {
    Log.d("onSaveMainCanalOrg $node");
    cancel = node;
  }

  void loadData() {
    Future.wait([
      XinJiangWaterManageService()
          .queryOrgFarmTreeByStaff({}, cancelToken: createCancelToken()),
    ]).then((list) {
      treeResult = list[0] as OrgTreeResult;

      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(handleError);
  }

  void _getFirstOrg(List<OrgTreeItem>? list, Map data) {
    if (list == null || list.isEmpty) return;

    if (data["orgName"] != null) {
      data["orgName"] = "${data["orgName"] ?? ""}/${list.first.orgName}";
    } else {
      data["orgName"] = list.first.orgName;
    }
    if (list.first.children == null || list.first.children!.isEmpty) {
      data["orgCode"] = list.first.orgCode;
      data["orgFullName"] = list.first.orgFullName;
    } else {
      return _getFirstOrg(list.first.children, data);
    }
  }

  bool _fullSearch = false;

  bool get fullSearch => _fullSearch;

  void fullSearchTrigger() {
    _fullSearch = !_fullSearch;
    notifyListeners();
  }

  int nextCount = 0;

  void next() {
    Navigator.of(context)
        .push(CupertinoPageRoute(builder: (_) => const WaterSettleApplyPage()))
        .then((result) {
      if (result == true) {
        nextCount++;
        notifyListeners();
      }
    });
  }
}

class _ContextWidget extends StatefulWidget {
  final DateTime? usageBeginDate;
  final DateTime? usageEndDate;
  final String? orgCode;
  final String? mainCanal;
  final String? branchCanal;
  final String? lateralCanal;
  const _ContextWidget(
      {super.key,
      required this.usageBeginDate,
      required this.usageEndDate,
      required this.orgCode,
      required this.mainCanal,
      required this.branchCanal,
      required this.lateralCanal});

  @override
  State<_ContextWidget> createState() => __ContextWidgetState();
}

class __ContextWidgetState extends State<_ContextWidget>
    with AutoDisposeStateMixin {
  late final ScrollController _scrollController;

  late final _ContentController controller;

  @override
  void initState() {
    super.initState();

    _scrollController = useScrollController(ScrollController());
    _scrollController.addListener(_scrollListener);
    controller = useChangeNotifier(_ContentController(
      context,
      usageBeginDate: widget.usageBeginDate,
      usageEndDate: widget.usageEndDate,
      orgCode: widget.orgCode,
      mainCanal: widget.mainCanal,
      branchCanal: widget.branchCanal,
      lateralCanal: widget.lateralCanal,
    ))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading) return _widgetLoading();
    return _widgetBody();
  }

  //加载中
  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  void _scrollListener() async {
    if (!mounted) {
      return;
    }
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      Log.d("_scrollController scroll to bottom");

      controller.loadMore();
    }
  }

  Widget _widgetTotal() {
    Widget child = Row(
      children: [
        Expanded(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "总用水量",
              style: TextStyle(
                  fontSize: 16.px,
                  color: const Color.fromRGBO(51, 51, 51, 0.4)),
            ),
            SizedBox(
              height: 8.px,
            ),
            Text(
              "${controller.totalWaterCons ?? "-"}m³",
              style: TextStyle(
                  fontFamily: "BEBAS",
                  fontSize: 24.px,
                  fontWeight: FontWeight.w600,
                  color: const Color.fromRGBO(51, 51, 51, 1)),
            ),
          ],
        )),
        VerticalDivider(
          width: 1.px,
          color: const Color.fromRGBO(51, 51, 51, 0.05),
          indent: 20.px,
          endIndent: 20.px,
        ),
        Expanded(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "总金额",
              style: TextStyle(
                  fontSize: 16.px,
                  color: const Color.fromRGBO(51, 51, 51, 0.4)),
            ),
            SizedBox(
              height: 8.px,
            ),
            Text(
              "${controller.totalAmount ?? "-"}元",
              style: TextStyle(
                  fontFamily: "BEBAS",
                  fontSize: 24.px,
                  fontWeight: FontWeight.w600,
                  color: const Color.fromRGBO(51, 51, 51, 1)),
            ),
          ],
        )),
      ],
    );

    return Container(
        height: 120.px,
        margin: EdgeInsets.only(left: 12.px, right: 12.px, bottom: 5.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(15.px)),
        ),
        child: child);
  }

  Widget _widgetBody() {
    return Column(
      children: [
        Expanded(
            child: RefreshIndicator(
                color: const Color.fromRGBO(2, 139, 93, 1),
                onRefresh: controller.refresh,
                child: Scrollbar(
                    controller: _scrollController,
                    child: CustomScrollView(
                      controller: _scrollController,
                      physics: const ClampingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      slivers: [
                        SliverToBoxAdapter(
                          child: _widgetTotal(),
                        ),
                        if (controller.items.isNotEmpty) ...[
                          _widgetList(),
                          SliverToBoxAdapter(
                            child: _loadMore(context),
                          ),
                        ],
                        SliverPadding(padding: EdgeInsets.only(bottom: 50.px))
                      ],
                    )))),
      ],
    );
  }

  Widget _widgetList() {
    return SliverList.builder(
      itemBuilder: (context, index) => WaterSettleItemWidget(
        item: controller.items[index],
        onClickFlow: controller.onClickProcessWaterUse,
        onPressed: controller.onClickItem,
        index: index,
        onCheck: (int index, bool value) {},
      ),
      itemCount: controller.items.length,
    );
  }

  Widget _loadMore(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 20.px, right: 20.px, bottom: 5.px),
      child: Text(
        controller.needLoadMore ? "加载更多" : "没有更多数据了",
        style: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(24, 66, 56, 0.4)),
        textAlign: TextAlign.center,
      ),
    );
  }
}

class _ContentController extends AutoDisposeChangeNotifier
    with LoadMoreChangeNotifier<WaterApplyItem> {
  @override
  final BuildContext context;
  final String? orgCode;
  final String? mainCanal;
  final String? branchCanal;
  final String? lateralCanal;

  final DateTime? usageBeginDate;
  final DateTime? usageEndDate;

  _ContentController(
    this.context, {
    required this.usageBeginDate,
    required this.usageEndDate,
    required this.orgCode,
    required this.mainCanal,
    required this.branchCanal,
    required this.lateralCanal,
  });

  //加载数据
  void loadData() {
    reload(showLoading: true);
  }

  @override
  List<WaterApplyItem> items = [];

  num? totalAmount;
  num? totalWaterCons;

  Future _futureWithTotal() {
    var data = <String, dynamic>{};

    if (usageBeginDate != null) {
      data["usageBeginDate"] = dateFormat.format(usageBeginDate!);
    }

    if (usageEndDate != null) {
      data["usageEndDate"] = dateFormat.format(usageEndDate!);
    }
    if (orgCode != null) {
      data["orgCode"] = orgCode;
    }

    if (mainCanal != null) {
      data["mainCanals"] = [mainCanal];
    }
    if (branchCanal != null) {
      data["branchCanals"] = [branchCanal];
    }
    if (lateralCanal != null) {
      data["lateralCanals"] = [lateralCanal];
    }

    var pageData = Map<String, dynamic>.from(data)
      ..addAll({
        "page": page,
        "rows": row,
      });

    return Future.wait([
      XinJiangWaterManageService()
          .waterSettleTotalWaterApply(data, cancelToken: createCancelToken()),
      XinJiangWaterManageService().querySettleWaterApplyByPage(pageData,
          cancelToken: createCancelToken())
    ]).then((list) {
      var result = list[0];
      if (result.code == 0 && result.success == true) {
        totalAmount = list[0].data["amount"] as num?;
        totalWaterCons = list[0].data["waterCons"] as num?;
      }
      result = list[1];
      if (result.code == 0 && result.success == true) {
        var page = PageModel.fromJson(result.data);
        var loadItems = page.records?.map<WaterApplyItem>((item) {
              return WaterApplyItem.fromJson(item);
            }).toList() ??
            [];

        total = page.total ?? 0;
        items.clear();
        items.addAll(loadItems);
      }
      loadingStatus = LoadingStatus.success;
      notifyListeners();
    });
  }

  //重新加载
  @override
  Future reloadFuture({
    bool showLoading = false, //不显示数据直接显示 loading
    bool loadingMore = false, //显示加载更多的提示
    bool refresh = false,
  }) {
    if (showLoading || refresh) {
      return _futureWithTotal();
    }
    var data = <String, dynamic>{};

    if (usageBeginDate != null) {
      data["usageBeginDate"] = dateFormat.format(usageBeginDate!);
    }

    if (usageEndDate != null) {
      data["usageEndDate"] = dateFormat.format(usageEndDate!);
    }
    if (orgCode != null) {
      data["orgCode"] = orgCode;
    }

    if (mainCanal != null) {
      data["mainCanals"] = [mainCanal];
    }
    if (branchCanal != null) {
      data["branchCanals"] = [branchCanal];
    }
    if (lateralCanal != null) {
      data["lateralCanals"] = [lateralCanal];
    }

    var pageData = Map<String, dynamic>.from(data)
      ..addAll({
        "page": page,
        "rows": row,
      });
    return XinJiangWaterManageService()
        .querySettleWaterApplyByPage(pageData, cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true) {
        var page = PageModel.fromJson(result.data);
        var loadItems = page.records?.map<WaterApplyItem>((item) {
              return WaterApplyItem.fromJson(item);
            }).toList() ??
            [];

        total = page.total ?? 0;
        if (refresh || showLoading) {
          items.clear();
        }
        //Log.d("load success ");

        items.addAll(loadItems);
      }
      loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        loadingStatus = LoadingStatus.success;
      });
    });
  }

  void onClickItem(WaterApplyItem item) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => MyWaterUseDetailPage(
                  waId: item.waId! as int,
                )))
        .then((result) {
      if (result == true) {}
    });
  }

  void onClickProcessWaterUse(WaterApplyItem item) {
    if (item.waId == null) {
      return;
    }
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => FlowWaterApplyInfoPage(
                  waId: item.waId!,
                )))
        .then((result) {
      if (result == true) {}
    });
  }
}
