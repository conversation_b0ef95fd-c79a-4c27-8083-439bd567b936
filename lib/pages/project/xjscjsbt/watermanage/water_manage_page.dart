import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/utils/collection_extensions.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:fluwx/fluwx.dart';
import 'package:oktoast/oktoast.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

//水费管理
class WaterManagePage extends StatefulWidget {
  const WaterManagePage({super.key});

  @override
  State<StatefulWidget> createState() => _WaterManagePageState();
}

class _WaterManagePageState extends State<WaterManagePage>
    with AutoDisposeStateMixin {
  late final _Controller controller;
  @override
  void initState() {
    super.initState();

    controller = useChangeNotifier(_Controller(context))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  Widget _backgroundWidget() {
    return Stack(
      fit: StackFit.expand,
      children: [
        SvgPicture.asset(
            alignment: Alignment.topCenter,
            fit: BoxFit.fitWidth,
            ImageHelper.wrapAssets("bg_dahing_tab_service.svg")),
        Positioned(
          top: 77.px,
          left: 24.5.px,
          child: SvgPicture.asset(
            alignment: Alignment.topCenter,
            fit: BoxFit.fitWidth,
            ImageHelper.wrapAssets("bg2_dahing_tab_service.svg"),
          ),
        ),
        Positioned(
          top: 109.5.px,
          left: 27.px,
          child: SvgPicture.asset(
            alignment: Alignment.topCenter,
            fit: BoxFit.fitWidth,
            ImageHelper.wrapAssets("title_water_price.svg"),
          ),
        ),
        Positioned(
          top: 81.5.px,
          right: 27.5.px,
          child: Image.asset(
            ImageHelper.wrapAssets("land_contract_headIcon.png"),
            width: 131.px,
          ),
        ),
      ],
    );
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _bodyWidget() {
    return Column(
      children: [
        AppBar(
          toolbarHeight: 40.px,
          backgroundColor: Colors.transparent,
          titleSpacing: 0,
          leading: const BackButton(color: Colors.white),
        ),
        SizedBox(
          height: 164.px,
        ),
        if (controller.isLoading) ...[
          SizedBox(
            height: 50.px,
          ),
          _widgetLoading(context)
        ],
        if (controller.topItems.isNotEmpty)
          Container(
            width: 351.px,
            decoration: BoxDecoration(
                color: const Color.fromRGBO(255, 255, 255, 0.5),
                borderRadius: BorderRadius.all(Radius.circular(9.px))),
            margin: EdgeInsets.only(bottom: 5.px),
            padding: EdgeInsets.only(
                top: 15.px, left: 18.px, right: 15.px, bottom: 18.px),
            child: Wrap(
              runSpacing: 20.px,
              children: [
                ...controller.topItems.map((e) {
                  return MenuItemView(
                    item: e,
                    isGroup: false,
                  );
                })
              ],
            ),
          ),
        if (controller.bottomGroups.isNotEmpty)
          Expanded(
            child: CustomScrollView(
              slivers: [
                SliverPadding(padding: EdgeInsets.only(top: 10.px)),
                SliverToBoxAdapter(
                    child: Column(
                  children: [
                    ...controller.bottomGroups.map((group) {
                      return Container(
                        width: 351.px,
                        decoration: BoxDecoration(
                            color: const Color.fromRGBO(255, 255, 255, 0.5),
                            borderRadius:
                                BorderRadius.all(Radius.circular(9.px))),
                        margin: EdgeInsets.only(bottom: 15.px),
                        padding: EdgeInsets.only(
                            top: 15.px,
                            left: 18.px,
                            right: 15.px,
                            bottom: 18.px),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              group.authName ?? "",
                              style: TextStyle(
                                  fontSize: 12.px,
                                  fontWeight: FontWeight.w400,
                                  color: const Color.fromRGBO(0, 0, 0, 0.4)),
                            ),
                            SizedBox(
                              height: 15.px,
                            ),
                            SizedBox(
                              width: 315.px,
                              child: Wrap(
                                runSpacing: 20.px,
                                children: [
                                  ...(group.children ?? []).map((e) {
                                    return MenuItemView(
                                      item: e,
                                      isGroup: true,
                                    );
                                  })
                                ],
                              ),
                            )
                          ],
                        ),
                      );
                    })
                  ],
                ))
              ],
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: Stack(
            fit: StackFit.expand,
            children: [_backgroundWidget(), _bodyWidget()],
          )),
    );
  }
}

//定制服务器下发的 icon
const _overlayMenuIcons = [
  {
    "authName": "我的水费",
    "icon": "ic_myWater.svg",
  },
  {
    "authName": "接水管理",
    "icon": "ic_waterContaining.svg",
  },
  {
    "authName": "配水管理",
    "icon": "ic_waterDistribution.svg",
  },
  {
    "authName": "调度管理",
    "icon": "ic_dispatchWater.svg",
  },
  {
    "authName": "结算管理",
    "icon": "ic_water_settle.svg",
  },
];

class MenuItemView extends StatelessWidget {
  final bool isGroup;
  final MenuConfigItem item;
  const MenuItemView({super.key, required this.item, this.isGroup = false});

  Widget _widgetIcon() {
    var overlayMenuIcon = _overlayMenuIcons
        .firstWhereOrNull((test) => test["authName"] == item.authName);

    if (overlayMenuIcon != null) {
      return SvgPicture.asset(
          alignment: Alignment.center,
          fit: BoxFit.cover,
          width: 40.px,
          height: 40.px,
          ImageHelper.wrapAssets(overlayMenuIcon["icon"] as String));
    } else {
      return Image.asset(
          width: 40.px,
          height: 40.px,
          ImageHelper.wrapOldAssets("${item.icon!}.png"),
          errorBuilder: (context, error, stackTrace) {
        return SvgPicture.asset(
          ImageHelper.wrapAssets("ic_default.svg"),
          width: 40.px,
          height: 40.px,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (item.url!.startsWith("gh_")) {
          Fluwx fluwx = Fluwx();
          fluwx.open(target: MiniProgram(username: item.url!));
        } else if (item.url!.startsWith("pages")) {
          NativeUtil.openUni({"path": item.url});
        } else {
          Navigator.of(context)
              .pushNamed(item.url ?? "无效路径", arguments: item)
              .then((res) {
            GlobalServiceView.needShowServiceBtn('service');
          });
        }
      },
      child: Container(
        width: 78.75.px,
        color: Colors.transparent,
        child: Column(
          children: [
            _widgetIcon(),
            SizedBox(
              height: 5.px,
            ),
            Text(
              item.authName ?? "",
              strutStyle: StrutStyle(fontSize: 12.px, forceStrutHeight: true),
              style: TextStyle(
                  color: const Color.fromRGBO(51, 51, 51, 1),
                  fontSize: 12.px,
                  fontWeight: FontWeight.w600),
            )
          ],
        ),
      ),
    );
  }
}

class MenuItem {
  String icon;
  String title;
  String path;
  MenuItem({required this.icon, required this.title, required this.path});
}

class MenuGroup {
  String title;
  List<MenuItem> children;
  MenuGroup({required this.title, required this.children});
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  _Controller(this.context);

  List<MenuConfigItem> topItems = [];
  List<MenuConfigItem> bottomGroups = [];

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    BDHResponsitory.getSubMenuConfig("waterPrice",
            cancelToken: createCancelToken())
        .then((res) {
      Log.d(res.toJson());
      if (res.code == 0 && res.success == true) {
        if (res.data?.isNotEmpty ?? false) {
          topItems = res.data?[0].children ?? [];
        } else {
          topItems = [];
        }

        _loadingStatus = LoadingStatus.success;
        notifyListeners();
      }
    }).onError((e, s) {
      Log.e("getSubMenuConfig error", error: e, stackTrace: s);
      var request = RequestException.handleError(e);
      if (request.isCancel) {
        return;
      }
      if (!context.mounted) {
        return;
      }
      showToast(request.message ?? "请求失败,请稍后再试");
    });
  }
}
