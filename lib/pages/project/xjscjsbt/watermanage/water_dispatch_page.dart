import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import 'water_containing_tab_page.dart';
import 'water_dispatch_tab_page.dart';

//调度管理
class WaterDispatchPage extends StatefulWidget {
  const WaterDispatchPage({super.key});

  @override
  State<StatefulWidget> createState() => _WaterDispatchPageState();
}

class _WaterDispatchPageState extends State<WaterDispatchPage>
    with TickerProviderStateMixin, AutoDisposeStateMixin {
  late final TabController _tabController;
  int _tabIndex = 1;
  @override
  void initState() {
    super.initState();

    _tabController = useTabController(
        TabController(initialIndex: _tabIndex, length: 3, vsync: this));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("调度管理"),
        bottom: PreferredSize(
            preferredSize: Size.fromHeight(44.px),
            child: TDTabBar(
                indicatorColor: const Color.fromRGBO(10, 174, 108, 1),
                controller: _tabController,
                labelColor: const Color.fromRGBO(10, 174, 108, 1),
                dividerColor: Colors.transparent,
                showIndicator: true,
                onTap: (idx) {
                  setState(() {
                    _tabIndex = idx;
                  });
                },
                tabs: const [
                  TDTab(
                    text: "用水申请",
                  ),
                  TDTab(
                    text: "放水申请",
                  ),
                  TDTab(
                    text: "完成",
                  )
                ])),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(top: false, child: _widgetBody()),
    );
  }

  Widget _widgetBody() {
    if (_tabIndex == 0) {
      return WaterDispatchTabPage(
        key: ValueKey(_tabIndex),
        auditLevel: 2,
        showBottomApprovalAll: true,
        showReject: false,
        showApproval: false,
        itemClickable: true,
        showFlow: false,
        showDistributionName: true,
        showFallback: false,
      );
    } else if (_tabIndex == 1) {
      return WaterDispatchTabPage(
        key: ValueKey(_tabIndex),
        auditLevel: 5,
        showBottomApprovalAll: false,
        showReject: false,
        showApproval: false,
        itemClickable: true,
        showFlow: false,
        showDistributionName: false,
        showFallback: false,
      );
    } else if (_tabIndex == 2) {
      return WaterDispatchTabPage(
        key: ValueKey(_tabIndex),
        auditLevel: 6,
        showBottomApprovalAll: false,
        showReject: false,
        showApproval: false,
        itemClickable: true,
        showFlow: false,
        showDistributionName: false,
        showFallback: false,
      );
    }
    return const SizedBox.shrink();
  }
}
