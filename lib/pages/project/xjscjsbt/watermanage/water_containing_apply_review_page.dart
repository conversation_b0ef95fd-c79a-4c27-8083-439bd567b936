import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_dropdown_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/widget/bdh_steps2.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../utils/auto_dispose_state_extension.dart';
import '../../../../utils/provider/view_state_widget.dart';
import 'const_dict.dart';
import 'model/page_model.dart';
import 'model/water_apply_item_model.dart';
import 'request/xinjiang_water_manage_service.dart';
import 'water_apply_detail_page.dart';
import 'water_containing_apply_review_next_page.dart';
import 'widget/apply_item_review_widget.dart';

//接水管理-用水申请-审核
class WaterContainingApplyReviewPage extends StatefulWidget {
  const WaterContainingApplyReviewPage({super.key});

  @override
  State<WaterContainingApplyReviewPage> createState() =>
      _WaterContainingApplyReviewPageState();
}

class _WaterContainingApplyReviewPageState
    extends State<WaterContainingApplyReviewPage> with AutoDisposeStateMixin {
  String? usageBeginDate;
  String? usageEndDate;

  @override
  void initState() {
    super.initState();
  }

  void onClickReset() {
    setState(() {
      usageBeginDate = null;
      usageEndDate = null;
    });
  }

  PreferredSizeWidget _widgetAppBar() {
    return AppBar(
      toolbarHeight: kTextTabBarHeight,
      title: const Text("用水审核"),
      actions: [
        GestureDetector(
          onTap: onClickReset,
          child: Text("重置",
              style: TextStyle(
                fontSize: 14.px,
                fontWeight: FontWeight.w500,
                color: const Color.fromRGBO(30, 192, 106, 1),
              )),
        ),
        SizedBox(
          width: 15.px,
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _widgetAppBar(),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(top: false, child: _widgetBody(context)),
    );
  }

  Widget _widgetBody(BuildContext context) {
    return Column(
      children: [
        _widgetStep(context),
        _widgetSubMenu(),
        SizedBox(height: 6.px),
        Expanded(child: _widgetList(context))
      ],
    );
  }

  Widget _widgetStep(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: BdhStepsHorizontal(
          steps: waterApplyReviewStepDict,
          activeIndex: 0,
          outerIconSize: 30.px,
          innerIconSize: 24.px,
        ));
  }

  Widget _widgetSubMenu() {
    return Container(
        height: 42.px,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: Row(
          children: [
            Expanded(
                child: BdhDropDownDatePicker(
              placeholder: "选择开始日期",
              initialValue: usageBeginDate,
              maxWidth: 120.px,
              onChange: (v) {
                setState(() {
                  usageBeginDate = v;
                });
              },
              item: FormItem(title: "选择开始日期", data: [], isRequired: true),
            )),
            VerticalDivider(
              color: const Color.fromRGBO(51, 51, 51, 0.1),
              width: 1.px,
              indent: 9.px,
              endIndent: 9.px,
            ),
            Expanded(
                child: BdhDropDownDatePicker(
              placeholder: "选择结束日期",
              maxWidth: 120.px,
              initialValue: usageEndDate,
              onChange: (v) {
                setState(() {
                  usageEndDate = v;
                });
              },
              item: FormItem(title: "选择开始日期", data: [], isRequired: true),
            )),
          ],
        ));
  }

  Widget _widgetList(BuildContext context) {
    return _ContextWidget(
      key: ValueKey("$usageBeginDate-$usageEndDate"),
      usageBeginDate: usageBeginDate,
      usageEndDate: usageEndDate,
    );
  }
}

class _ContextWidget extends StatefulWidget {
  final String? usageBeginDate;
  final String? usageEndDate;
  const _ContextWidget(
      {super.key, required this.usageBeginDate, required this.usageEndDate});

  @override
  State<_ContextWidget> createState() => __ContextWidgetState();
}

class __ContextWidgetState extends State<_ContextWidget>
    with AutoDisposeStateMixin {
  late final ScrollController _scrollController;

  late final _ContentController controller;

  @override
  void initState() {
    super.initState();

    _scrollController = useScrollController(ScrollController());
    _scrollController.addListener(_scrollListener);
    controller = useChangeNotifier(_ContentController(context,
        usageBeginDate: widget.usageBeginDate,
        usageEndDate: widget.usageEndDate))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading) return _widgetLoading();
    return _widgetBody();
  }

  Widget _widgetBottom() {
    return Container(
        color: Colors.white,
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.only(
            left: 20.px, right: 20.px, bottom: 12.px, top: 12.px),
        child: Center(
            child: BdhTextButton(
          width: double.infinity,
          height: 40.px,
          text: '下一步',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(22.px)),
          backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          disableBackgroundColor: Colors.grey.shade400,
          pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: controller.itemCheckedIndex != null
              ? controller.onClickNext
              : null,
        )));
  }

  void _scrollListener() async {
    if (!mounted) {
      return;
    }
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      Log.d("_scrollController scroll to bottom");

      controller.loadMore();
    }
  }

  Widget _widgetBody() {
    return Column(
      children: [
        Expanded(
            child: RefreshIndicator(
                color: const Color.fromRGBO(2, 139, 93, 1),
                onRefresh: controller.refresh,
                child: Scrollbar(
                    controller: _scrollController,
                    child: CustomScrollView(
                      controller: _scrollController,
                      physics: const ClampingScrollPhysics(
                          parent: AlwaysScrollableScrollPhysics()),
                      slivers: [
                        if (controller.items.isNotEmpty) ...[
                          _widgetList(),
                          SliverToBoxAdapter(
                            child: _loadMore(context),
                          ),
                        ],
                        if (controller.items.isEmpty)
                          SliverFillRemaining(
                              hasScrollBody: false, child: _widgetEmpty()),
                      ],
                    )))),
        if (controller.items.isNotEmpty) _widgetBottom()
      ],
    );

    ;
  }

  Widget _widgetEmpty() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            ImageHelper.wrapAssets("icon_nodata.svg"),
            width: 201.5.px,
            height: 100.px,
          ),
          SizedBox.square(
            dimension: 45.px,
          ),
          Text(
            "未找到任何记录",
            style: TextStyle(
                color: const Color.fromRGBO(44, 44, 52, 1), fontSize: 14.px),
          ),
        ],
      ),
    );
  }

  //用水记录列表
  Widget _widgetList() {
    return SliverList.builder(
      itemBuilder: (context, index) {
        return ApplyItemReviewWidget(
          checked: controller.itemCheckedIndex == index,
          checkable: true,
          item: controller.items[index],
          onPressed: controller.onClickItem,
          onPressedPhone: controller.onPressedPhone,
          onCheck: controller.onItemCheck,
          index: index,
        );
      },
      itemCount: controller.items.length,
    );
  }

  Widget _loadMore(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 20.px, right: 20.px, bottom: 5.px),
      child: Text(
        controller.needLoadMore ? "加载更多" : "没有更多数据了",
        style: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(24, 66, 56, 0.4)),
        textAlign: TextAlign.center,
      ),
    );
  }
}

//加载中
Widget _widgetLoading() {
  return const Center(
    child: ViewStateBusyWidget(),
  );
}

class _ContentController extends AutoDisposeChangeNotifier
    with
        LoadMoreChangeNotifier<WaterApplyItem>,
        SingleCheckChangeNotifier<WaterApplyItem> {
  @override
  final BuildContext context;
  final String? usageBeginDate;
  final String? usageEndDate;

  _ContentController(this.context, {this.usageBeginDate, this.usageEndDate});

  //加载数据
  void loadData() {
    reload(showLoading: true);
  }

  @override
  List<WaterApplyItem> items = [];

  //重新加载
  @override
  Future reloadFuture({
    bool showLoading = false, //不显示数据直接显示 loading
    bool loadingMore = false, //显示加载更多的提示
    bool refresh = false,
  }) {
    var data = <String, dynamic>{
      "auditLevel": 1,
      "page": page,
      "rows": row,
    };

    if (usageBeginDate != null) {
      data["usageBeginDate"] = usageBeginDate;
    }

    if (usageEndDate != null) {
      data["usageEndDate"] = usageEndDate;
    }

    return XinJiangWaterManageService()
        .waterApplyHandleReceiverPendingPage(data,
            cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true) {
        var page = PageModel.fromJson(result.data);
        var loadItems = page.records?.map<WaterApplyItem>((item) {
              return WaterApplyItem.fromJson(item);
            }).toList() ??
            [];

        total = page.total ?? 0;
        if (refresh || showLoading) {
          itemCheckedIndex = null;
          items.clear();
        }
        Log.d("load success ");
        items.addAll(loadItems);
      }
      loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        loadingStatus = LoadingStatus.success;
      });
    });
  }

  void onClickNext() {
    if (itemCheckedIndex == null) {
      return;
    }
    var item = items[itemCheckedIndex!];
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => WaterContainingApplyReviewNextPage(
                  item: item,
                )))
        .then((result) {
      if (result == true) {
        reload(showLoading: true);
      }
    });
  }

  void onClickItem(WaterApplyItem item) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => WaterApplyDetailPage(
                  waId: item.waId!,
                  showApproval: false,
                )))
        .then((result) {
      if (result == true) {
        reload(showLoading: true);
      }
    });
  }

  void onPressedPhone(WaterApplyItem item) {
    if (item.operatePhone?.isEmpty ?? true) {
      return;
    }

    launchUrl(Uri.parse("tel:${item.operatePhone}"));
  }
}
