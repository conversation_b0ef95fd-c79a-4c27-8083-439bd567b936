import 'dart:io';

import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/request/xinjiang_water_manage_service.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:oktoast/oktoast.dart';
import 'package:open_filex/open_filex.dart';
import 'package:permission_handler/permission_handler.dart';

import 'const_dict.dart';
import 'model/water_payment_record_model.dart';

//用户-缴费详情
class MyWaterPaymentDetailPage extends StatefulWidget {
  final PaymentRecordItem item;
  const MyWaterPaymentDetailPage({super.key, required this.item});

  @override
  State<MyWaterPaymentDetailPage> createState() =>
      _MyWaterPaymentDetailPageState();
}

class _MyWaterPaymentDetailPageState extends State<MyWaterPaymentDetailPage>
    with AutoDisposeStateMixin {
  late final ScrollController _scrollController;

  late final _Controller controller;

  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());
    controller = useChangeNotifier(_Controller(context, widget.item))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("缴费详情"),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child:
              controller.isLoading ? _widgetLoading(context) : _widgetList()),
    );
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetList() {
    return SingleChildScrollView(
      controller: _scrollController,
      child: Column(
        children: [
          ColoredBox(
              color: Colors.white,
              child: Column(
                children: [
                  SizedBox(
                    height: 20.px,
                  ),
                  _widgetImage(),
                  SizedBox(
                    height: 20.px,
                  ),
                  _widgetMoney(),
                  SizedBox(
                    height: 36.px,
                  ),
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),
                  SizedBox(
                    height: 20.px,
                  ),
                  _widgetSummary(
                    "状态",
                    controller.item?.isDeposited == "是"
                        ? "${controller.item?.recordType}成功"
                        : "审核中",
                    textColor: controller.item?.isDeposited == "是"
                        ? const Color.fromRGBO(30, 192, 106, 1)
                        : Colors.red,
                  ),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("缴费户名", controller.item?.growerName ?? "-"),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary(
                      "缴费编号", controller.item?.recordId?.toString() ?? "-"),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("缴费单位", controller.item?.orgName ?? "-"),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("缴费日期", createTime),
                  SizedBox(
                    height: 10.px,
                  ),
                ],
              )),
          SizedBox(
            height: 6.px,
          ),
          if (controller.item?.recordType == "缴费")
            ColoredBox(color: Colors.white, child: _widgetTicket()),
        ],
      ),
    );
  }

  String get createTime {
    if (widget.item.createTime == null) {
      return "-";
    }
    int timestampInMilliseconds =
        widget.item.createTime!; // 例如：2021-04-01T00:00:00Z

    // 将时间戳转换为DateTime对象
    DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch(timestampInMilliseconds);

    // 创建DateFormat对象并指定格式
    // 例如：2021-04-01 00:00:00

    // 格式化日期时间
    return timeFormat.format(dateTime);
  }

  Widget _widgetImage() {
    return Center(
        child: SvgPicture.asset(
            alignment: Alignment.center,
            fit: BoxFit.cover,
            width: 60.px,
            height: 60.px,
            ImageHelper.wrapAssets("ic_water_account.svg")));
  }

  Widget _widgetSummary(String title, String text, {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
          width: 82.px,
          child: Text(
            title,
            style: TextStyle(
                color: const Color.fromRGBO(51, 51, 51, 0.4),
                fontWeight: FontWeight.w500,
                fontSize: 12.px),
          ),
        ),
        Expanded(
            child: Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetMoney() {
    return Center(
        child: Text(
      "${controller.item?.recordType == "缴费" ? "+" : "-"}${controller.item?.amount}",
      style: TextStyle(
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w600,
          fontSize: 28.px),
    ));
  }

  Widget _widgetTicket() {
    return GestureDetector(
        onTap: controller.onClickTicket,
        child: Padding(
            padding: EdgeInsets.only(top: 12.px, bottom: 12.px),
            child: Row(
              children: [
                SizedBox(
                  width: 20.px,
                ),
                Expanded(
                    child: Text(
                  "查看票据",
                  style: TextStyle(
                      color: const Color.fromRGBO(51, 51, 51, 1),
                      fontWeight: FontWeight.w600,
                      fontSize: 12.px),
                )),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 24.px,
                  color: const Color.fromRGBO(51, 51, 51, 0.2),
                ),
                SizedBox(
                  width: 20.px,
                )
              ],
            )));
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  final BuildContext context;
  final PaymentRecordItem originItem;
  _Controller(this.context, this.originItem);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  PaymentRecordItem? item;
  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    XinJiangWaterManageService()
        .getPaymentRecordInfo(originItem.recordId,
            cancelToken: createCancelToken())
        .then((result) {
      item = PaymentRecordItem.fromJson(result.data);
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    });
  }

  void onClickTicket() {
    Log.d("onClickTicket");
    showLoading(context, content: "正在加载..   ");
    XinJiangWaterManageService().waterChargeTickets({
      "ids": [originItem.recordId]
    }, "", cancelToken: createCancelToken(), onReceiveProgress: (p0, p1) {
      Log.d("onReceiveProgress :$p0  $p1  ");
    }).then((response) {
      if (!context.mounted) {
        return;
      }
      String savePath = "${Directory.systemTemp.path}/tmp.pdf";
      File file = File(savePath);
      if (file.existsSync()) {
        file.deleteSync();
      }
      File(savePath).writeAsBytesSync(response.data);

      var types = {};
      if (Platform.isAndroid) {
        types = {
          ".docx":
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          ".pdf": "application/pdf",
          ".txt": "text/plain",
        };
      } else if (Platform.isIOS) {
        types = {
          ".docx": "com.microsoft.word.docx",
          ".pdf": "com.adobe.pdf",
          ".txt": "public.plain-text"
        };
      }
      if (file.existsSync()) {
        Permission.storage.status.then((status) {
          if (!context.mounted) {
            return;
          }
          if (!status.isGranted) {
            Permission.storage.request().then((PermissionStatus status) {
              if (!context.mounted) {
                return;
              }
              if (status.isGranted) {
                OpenFilex.open(savePath,
                    type: types[file.path.split(".").last]);
              } else {
                showToast("请允许访问文件权限");
              }
            });
          } else {
            OpenFilex.open(savePath, type: types[file.path.split(".").last]);
          }
        });
      }
    }).whenComplete(() {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
    });
  }
}
