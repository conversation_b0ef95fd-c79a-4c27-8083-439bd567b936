import 'package:bdh_smart_agric_app/components/form/bdh_dropdown_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/widget/bdh_steps2.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:oktoast/oktoast.dart';

import '../../../../utils/auto_dispose_state_extension.dart';
import '../../../../utils/image_util.dart';
import '../../../../utils/provider/view_state_widget.dart';
import 'const_dict.dart';
import 'model/water_planting_info_model.dart';
import 'my_water_planting_info_apply_page.dart';
import 'request/xinjiang_water_manage_service.dart';
import 'widget/planting_info_item_widget.dart';
import 'widget/search_appbar.dart';

//种植信息填写
class MyWaterPlantingInfoPage extends StatefulWidget {
  //首页直接进入的则为 true
  final bool isCheck;
  //首页直接进入的会把加载的数据直接传过来
  final List<WaterPlantingInfoItem>? itemList;
  const MyWaterPlantingInfoPage(
      {super.key, this.isCheck = false, this.itemList});

  @override
  State<MyWaterPlantingInfoPage> createState() =>
      _MyWaterPlantingInfoPageState();
}

class _MyWaterPlantingInfoPageState extends State<MyWaterPlantingInfoPage>
    with AutoDisposeStateMixin {
  late final _Controller controller;
  @override
  void initState() {
    super.initState();
    controller = useChangeNotifier(_Controller(context, widget.isCheck))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  PreferredSizeWidget _widgetAppBar() {
    return AppBar(
      title: const Text("种植信息填写"),
      // actions: [
      //   if (!controller.isLoading && controller.itemList.isNotEmpty)
      //     GestureDetector(
      //       onTap: controller.triggerSearchAppBar,
      //       child: SvgPicture.asset(
      //           alignment: Alignment.center,
      //           fit: BoxFit.cover,
      //           width: 24.px,
      //           colorFilter:
      //               const ColorFilter.mode(Colors.black, BlendMode.srcIn),
      //           ImageHelper.wrapAssets("ic_search.svg")),
      //     ),
      //   SizedBox(
      //     width: 15.px,
      //   )
      // ],
    );
  }

  PreferredSizeWidget _widgetAppBarSearch() {
    return SearchAppBar(
      onBackClick: controller.triggerSearchAppBar,
      onSearch: controller.onSearch,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          controller.showSearchAppBar ? _widgetAppBarSearch() : _widgetAppBar(),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: controller.isLoading
              ? _widgetLoading(context)
              : _widgetBody(context)),
    );
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody(BuildContext context) {
    var children = [
      if (widget.isCheck) ...[
        _widgetStep(context),
        SizedBox(height: 12.px),
        _widgetWarn(context),
      ],
      if (!widget.isCheck) ...[
        SizedBox(height: 6.px),
        _widgetYearChoose(context)
      ],
      SizedBox(height: 6.px),
      Expanded(child: _widgetList(context))
    ];

    return Column(
      children: children,
    );
  }

  Widget _widgetStep(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: BdhStepsHorizontal(
          steps: plantingApplyStepDict,
          activeIndex: 0,
          outerIconSize: 30.px,
          innerIconSize: 24.px,
        ));
  }

  Widget _widgetWarn(BuildContext context) {
    return Text(
      "请完成全部地块的种植计划，才能进行充值业务",
      style: TextStyle(
          color: const Color.fromRGBO(254, 44, 85, 1),
          fontSize: 13.px,
          fontWeight: FontWeight.w600),
    );
  }

  Widget _widgetYearChoose(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 12.px,
        ),
        SizedBox(
            height: 20.px,
            child: BdhDropDownSingleDataPicker(
              placeHolder: "选择日期",
              checkState: true,
              maxWidth: 120.px,
              initialValue: controller.chosenYear,
              onChange: (v) {
                controller.chosenYear = v;
              },
              item: FormItem(
                  title: "选择日期", data: controller.yearDict, isRequired: true),
            ))
      ],
    );
  }

  Widget _widgetList(BuildContext context) {
    return _ContextWidget(
      key: ValueKey(controller.chosenYear?.name ?? ""),
      chosenYear: controller.chosenYear,
      isCheck: widget.isCheck,
      itemList: widget.itemList,
    );
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  final bool isCheck;
  @override
  final BuildContext context;

  _Controller(this.context, this.isCheck);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  List<WaterPlantingInfoItem> itemList = [];

  List<DictNode> yearDict = [];
  DictNode? _chosenYear;
  DictNode? get chosenYear => _chosenYear;
  set chosenYear(DictNode? node) {
    _chosenYear = node;
    notifyListeners();
  }

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }

    //如果是首页进来的，没有必要再去请求年份字典
    if (isCheck) {
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
      return;
    }
    UserInfo? userInfo = StorageUtil.userInfo();
    var data = {};
    if (userInfo?.data?.idCard != null) {
      data["idNumber"] = userInfo?.data?.idCard;
    }

    if (_chosenYear != null) {
      data["statYear"] = _chosenYear!.name!;
    }

    Future.wait([
      XinJiangWaterManageService()
          .getDicByKey("year_cd", cancelToken: createCancelToken()),
    ]).then((list) {
      Log.d("result is $list");
      yearDict = list[0].data ?? [];

      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  Null _handlerError(Object error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  void onSearch(String text) {}

  bool _showSearchAppBar = false;
  bool get showSearchAppBar => _showSearchAppBar;

  void triggerSearchAppBar() {
    _showSearchAppBar = !_showSearchAppBar;
    notifyListeners();
  }

  //年度 0: "全部",1: "今年",2: "历年"
  int yearNo = 0;

  void changeYearNo(int? no) {
    if (no == null) {
      return;
    }
    yearNo = no;
    notifyListeners();
  }
}

class _ContextWidget extends StatefulWidget {
  final DictNode? chosenYear;
  final bool isCheck;

  final List<WaterPlantingInfoItem>? itemList;
  const _ContextWidget(
      {super.key, this.chosenYear, this.isCheck = false, this.itemList});

  @override
  State<_ContextWidget> createState() => __ContextWidgetState();
}

class __ContextWidgetState extends State<_ContextWidget>
    with AutoDisposeStateMixin {
  late final ScrollController _scrollController;

  late final _ContentController controller;

  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());
    controller = useChangeNotifier(_ContentController(context,
        chosenYear: widget.chosenYear,
        isCheck: widget.isCheck,
        originItemList: widget.itemList))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  void dispose() {
    Log.d("__ContextWidgetState dispose");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading) return _widgetLoading();

    return _widgetBody();
  }

  //加载中
  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  //申请机动地地块-列表为空
  Widget _widgetEmpty() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            ImageHelper.wrapAssets("icon_nodata.svg"),
            width: 201.5.px,
            height: 100.px,
          ),
          SizedBox.square(
            dimension: 45.px,
          ),
          Text(
            "未找到任何记录",
            style: TextStyle(
                color: const Color.fromRGBO(44, 44, 52, 1), fontSize: 14.px),
          ),
        ],
      ),
    );
  }

  Widget _widgetBody() {
    var list = controller.itemList;

    if (list.isEmpty) {
      return _widgetEmpty();
    }

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverList.builder(
          itemBuilder: (context, index) => PlantingInfoItemWidget(
            piId: list[index].piId,
            auditResult: list[index].auditResult,
            auditStatus: list[index].auditStatus,
            index: index,
            plotNo: list[index].plotNo,
            plotArea: list[index].plotArea,
            onClickProcess: controller.onClickProcess,
            onClickEdit: controller.onClickEdit,
          ),
          itemCount: list.length,
        )
      ],
    );
  }
}

class _ContentController extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  final DictNode? chosenYear;

  final bool isCheck;

  final List<WaterPlantingInfoItem>? originItemList;

  _ContentController(
    this.context, {
    this.chosenYear,
    this.isCheck = false,
    this.originItemList,
  });

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  List<WaterPlantingInfoItem> itemList = [];

  bool get needWritePlantInfo {
    if (itemList.isEmpty) {
      return true;
    }
    bool need = false;
    for (var item in itemList) {
      if (item.piId == null || item.auditResult != "审核通过") {
        need = true;
      }
    }
    return need;
  }

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }

    //如果是首次进入,且首页已经请求了数据，不需要再次请求，直接用就行
    if (_loadingStatus == LoadingStatus.init && originItemList != null) {
      itemList.addAll(originItemList!);
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
      return;
    }

    _loadingStatus = LoadingStatus.loading;
    notifyListeners();

    UserInfo? userInfo = StorageUtil.userInfo();
    var data = {};
    if (userInfo?.data?.idCard != null) {
      data["idNumber"] = userInfo?.data?.idCard;
    }

    if (chosenYear != null) {
      data["statYear"] = chosenYear!.name!;
    }
    Log.d("loadData :$data");
    var requestFunction = isCheck
        ? XinJiangWaterManageService().getFarmerPlot
        : XinJiangWaterManageService().getPlantInfoList;

    requestFunction(data, cancelToken: createCancelToken()).then((result) {
      Log.d("result is $result");

      itemList = result.data == null
          ? []
          : (result.data as List)
              .map<WaterPlantingInfoItem>((data) =>
                  WaterPlantingInfoItem.fromJson(data as Map<String, Object?>))
              .toList();
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  Null _handlerError(Object error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  void onSearch(String text) {}

  void onClickEdit(int index) {
    var item = itemList[index];
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => MyWaterPlantingInfoApplyPage(
                  item: item,
                )))
        .then((result) {
      if (!context.mounted) {
        return;
      }
      //如果有修改并且不是首页直接进来的则需要退出当前页面
      if (result == true) {
        if (!isCheck) {
          Navigator.maybePop(context, true);
        } else {
          loadData();
        }
      }
    });
  }

  void onClickProcess(int index) {
    // Navigator.of(context)
    //     .push(CupertinoPageRoute(builder: (_) => FlowPlantingApplyInfoPage()))
    //     .then((result) {
    //   if (result == true) {}
    // });
  }

  bool _showSearchAppBar = false;
  bool get showSearchAppBar => _showSearchAppBar;

  void triggerSearchAppBar() {
    _showSearchAppBar = !_showSearchAppBar;
    notifyListeners();
  }

  //年度 0: "全部",1: "今年",2: "历年"
  int yearNo = 0;

  void changeYearNo(int? no) {
    if (no == null) {
      return;
    }
    yearNo = no;
    notifyListeners();
  }
}
