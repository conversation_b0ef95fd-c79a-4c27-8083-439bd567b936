import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input_small.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/request/xinjiang_water_manage_service.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/widget/bdh_steps2.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../components/form/bdh_single_data_picker.dart';
import '../../../../utils/auto_dispose_state_extension.dart';
import 'const_dict.dart';
import 'model/page_model.dart';
import 'model/water_manage_index_model.dart';
import 'water_settle_apply_next_page.dart';
import 'widget/water_settle_account_item_widget.dart';

final _userTypeDict = [
  DictNode(code: "农户", name: "农户"),
  DictNode(code: "企业", name: "企业"),
];

//配水管理-用水申请-审核
class WaterSettleApplyPage extends StatefulWidget {
  const WaterSettleApplyPage({super.key});

  @override
  State<WaterSettleApplyPage> createState() => _WaterSettleApplyPageState();
}

class _WaterSettleApplyPageState extends State<WaterSettleApplyPage>
    with AutoDisposeStateMixin {
  late final _Controller controller;
  late final TextEditingController _textEditingController;
  late final TextEditingController _textEditingController2;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _textEditingController = useTextController(TextEditingController());
    _textEditingController2 = useTextController(TextEditingController());
    controller = useChangeNotifier(_Controller(context))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  PreferredSizeWidget _widgetAppBar() {
    return AppBar(
      toolbarHeight: kTextTabBarHeight,
      title: const Text("水费结算"),
      actions: [
        GestureDetector(
          onTap: controller.onClickReset,
          child: Text("重置",
              style: TextStyle(
                fontSize: 14.px,
                fontWeight: FontWeight.w500,
                color: const Color.fromRGBO(30, 192, 106, 1),
              )),
        ),
        SizedBox(
          width: 15.px,
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: _widgetAppBar(),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: SafeArea(
            top: false,
            child: controller.isLoading
                ? _widgetLoading(context)
                : _widgetBody()));
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    return Column(
      children: [
        _widgetStep(),
        _widgetSearch(),
        Expanded(
            child: _ContextWidget(
                key: ValueKey(
                    "${controller.growerType ?? ""}-${controller.growerName ?? ""}-${controller.idNumber ?? ""}"),
                growerType: controller.growerType,
                growerName: controller.growerName,
                idNumber: controller.idNumber))
      ],
    );
  }

  Widget _widgetSearch() {
    return Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: Column(
          children: [
            SizedBox(height: 10.px),
            ColoredBox(
                color: Colors.white,
                child: Column(
                  children: [
                    Divider(
                      color: const Color.fromRGBO(51, 51, 51, 0.05),
                      height: 1.px,
                      indent: 20.px,
                      endIndent: 20.px,
                    ),
                    _widgetUserType(),
                    _widgetUserName(),
                    _widgetIdNumber(),
                  ],
                ))
          ],
        ));
  }

  Widget _widgetUserType() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhSingleDataPicker(
          showBottomLine: true,
          showArrow: true,
          checkState: true,
          textAlign: TextAlign.right,
          titleWidth: 130.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(title: "类型", data: _userTypeDict, isRequired: false),
          initialValue: controller.growerType == null
              ? null
              : DictNode(
                  code: controller.growerType, name: controller.growerType),
          validator: (v) {
            if (v == null) {
              return "类型不能为空";
            }
            return null;
          },
          onChange: (v) {
            controller.growerType = v.code;
          },
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetUserName() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhTextInputSmall(
          showBottomLine: true,
          textAlign: TextAlign.right,
          titleWidth: 130.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(title: "姓名/企业名称", isRequired: false),
          clearButtonBode: OverlayVisibilityMode.editing,
          controller: _textEditingController,
          initialValue: controller.growerName,
          validator: (v) {
            if (v == null) {
              return "姓名/企业名称不能为空";
            }
            return null;
          },
          onChange: (v) {
            controller.growerName = v;
          },
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetIdNumber() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhTextInputSmall(
          showBottomLine: true,
          textAlign: TextAlign.right,
          titleWidth: 130.px,
          valueSpace: 0,
          minHeight: 44.px,
          titleStyle: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 1)),
          placeholderStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          item: FormItem(title: "身份证/组织代码", isRequired: false),
          clearButtonBode: OverlayVisibilityMode.editing,
          controller: _textEditingController2,
          initialValue: controller.idNumber,
          validator: (v) {
            if (v == null) {
              return "身份证/组织代码不能为空";
            }
            return null;
          },
          onChange: (v) {
            Log.d("onChange: $v");
            controller.idNumber = v;
          },
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetStep() {
    return Container(
        padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: BdhStepsHorizontal(
          steps: waterApplySettleStepDict,
          activeIndex: 0,
          outerIconSize: 30.px,
          innerIconSize: 24.px,
        ));
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  _Controller(this.context);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  void loadData() {
    _loadingStatus = LoadingStatus.success;
    notifyListeners();
  }

  String? _growerType;

  String? get growerType => _growerType;

  set growerType(String? growerType) {
    _growerType = growerType;
    notifyListeners();
  }

  String? _growerName;
  String? get growerName => _growerName;

  set growerName(String? growerName) {
    _growerName = growerName;
    notifyListeners();
  }

  String? _idNumber;

  String? get idNumber => _idNumber;

  set idNumber(String? idNumber) {
    _idNumber = idNumber;
    notifyListeners();
  }

  void onClickReset() {
    _growerType = null;
    _growerName = null;
    _idNumber = null;
    notifyListeners();
  }
}

class _ContextWidget extends StatefulWidget {
  final String? growerType;
  final String? growerName;
  final String? idNumber;
  const _ContextWidget(
      {super.key,
      required this.growerType,
      required this.growerName,
      required this.idNumber});

  @override
  State<_ContextWidget> createState() => __ContextWidgetState();
}

class __ContextWidgetState extends State<_ContextWidget>
    with AutoDisposeStateMixin {
  late final ScrollController _scrollController;

  late final _ContentController controller;

  @override
  void initState() {
    super.initState();

    _scrollController = useScrollController(ScrollController());
    _scrollController.addListener(_scrollListener);
    controller = useChangeNotifier(_ContentController(context,
        growerType: widget.growerType,
        growerName: widget.growerName,
        idNumber: widget.idNumber))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading) return _widgetLoading();
    return _widgetBody();
  }

  Widget _widgetBottom() {
    return Container(
        width: MediaQuery.of(context).size.width,
        padding: EdgeInsets.only(
            left: 20.px, right: 20.px, bottom: 12.px, top: 12.px),
        child: Center(
            child: BdhTextButton(
          width: double.infinity,
          height: 40.px,
          text: '下一步',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(22.px)),
          backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          disableBackgroundColor: Colors.grey.shade400,
          pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: controller.itemCheckedIndex != null
              ? controller.onClickNext
              : null,
        )));
  }

  void _scrollListener() async {
    if (!mounted) {
      return;
    }
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      Log.d("_scrollController scroll to bottom");

      controller.loadMore();
    }
  }

  Widget _widgetBody() {
    return Stack(
      children: [
        RefreshIndicator(
            color: const Color.fromRGBO(2, 139, 93, 1),
            onRefresh: controller.refresh,
            child: Scrollbar(
                controller: _scrollController,
                child: CustomScrollView(
                  controller: _scrollController,
                  physics: const ClampingScrollPhysics(
                      parent: AlwaysScrollableScrollPhysics()),
                  slivers: [
                    if (controller.items.isNotEmpty) ...[
                      _widgetList(),
                      SliverToBoxAdapter(
                        child: _loadMore(context),
                      ),
                    ],
                    if (controller.items.isEmpty)
                      SliverFillRemaining(
                          hasScrollBody: false, child: _widgetEmpty()),
                    SliverPadding(padding: EdgeInsets.only(bottom: 50.px))
                  ],
                ))),
        if (controller.items.isNotEmpty)
          Positioned(bottom: 0, child: _widgetBottom())
      ],
    );
  }

  Widget _widgetEmpty() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            ImageHelper.wrapAssets("icon_nodata.svg"),
            width: 201.5.px,
            height: 100.px,
          ),
          SizedBox.square(
            dimension: 45.px,
          ),
          Text(
            "未找到任何记录",
            style: TextStyle(
                color: const Color.fromRGBO(44, 44, 52, 1), fontSize: 14.px),
          ),
        ],
      ),
    );
  }

  //用水记录列表
  Widget _widgetList() {
    return SliverList.builder(
      itemBuilder: (context, index) {
        return WaterSettleAccountItemWidget(
          checked: controller.itemCheckedIndex == index,
          item: controller.items[index],
          onCheck: controller.onItemCheck,
          index: index,
        );
      },
      itemCount: controller.items.length,
    );
  }

  Widget _loadMore(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 20.px, right: 20.px, bottom: 5.px),
      child: Text(
        controller.needLoadMore ? "加载更多" : "没有更多数据了",
        style: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(24, 66, 56, 0.4)),
        textAlign: TextAlign.center,
      ),
    );
  }
}

//加载中
Widget _widgetLoading() {
  return const Center(
    child: ViewStateBusyWidget(),
  );
}

class _ContentController extends AutoDisposeChangeNotifier
    with LoadMoreChangeNotifier<MyAcc>, SingleCheckChangeNotifier<MyAcc> {
  @override
  final BuildContext context;
  final String? growerType;
  final String? growerName;
  final String? idNumber;

  _ContentController(this.context,
      {required this.growerType,
      required this.growerName,
      required this.idNumber});

  //加载数据
  void loadData() {
    reload(showLoading: true);
  }

  @override
  List<MyAcc> items = [];

  //重新加载
  @override
  Future reloadFuture({
    bool showLoading = false, //不显示数据直接显示 loading
    bool loadingMore = false, //显示加载更多的提示
    bool refresh = false,
  }) {
    var data = <String, dynamic>{
      "auditLevel": 1,
      "page": page,
      "rows": row,
    };

    if (growerType != null) {
      data["growerType"] = growerType;
    }

    if (growerName != null) {
      data["growerName"] = growerName;
    }

    if (idNumber != null) {
      data["idNumber"] = idNumber;
    }

    return XinJiangWaterManageService()
        .querySettleAccountByPage(data, cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true) {
        var page = PageModel.fromJson(result.data);
        var loadItems = page.records?.map<MyAcc>((item) {
              return MyAcc.fromJson(item);
            }).toList() ??
            [];

        total = page.total ?? 0;
        if (refresh || showLoading) {
          itemCheckedIndex = null;
          items.clear();
        }
        Log.d("load success ");
        items.addAll(loadItems);
      }
      loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        loadingStatus = LoadingStatus.success;
      });
    });
  }

  void onClickNext() {
    if (itemCheckedIndex == null) {
      return;
    }
    var item = items[itemCheckedIndex!];
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => WaterSettleApplyNextPage(item: item)))
        .then((result) {
      if (!context.mounted) {
        return;
      }
      if (result == true) {
        Navigator.maybePop(context, true);
      }
    });
  }

  void onClickItem(MyAcc item) {}
}
