import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../utils/auto_dispose_state_extension.dart';
import '../../../../utils/log.dart';
import '../../../../utils/provider/view_state_widget.dart';
import 'model/water_distribution_Item_model.dart';
import 'model/water_distribution_detail_model.dart';
import 'request/xinjiang_water_manage_service.dart';
import 'water_apply_detail_page.dart';
import 'widget/distribution_water_apply_item_widget.dart';

//接水管理-用水申请-已经完成-详情
class WaterDistributionDetailPage extends StatefulWidget {
  final WaterDistributionItem item;
  const WaterDistributionDetailPage({super.key, required this.item});

  @override
  State<WaterDistributionDetailPage> createState() =>
      _WaterDistributionDetailPageState();
}

class _WaterDistributionDetailPageState
    extends State<WaterDistributionDetailPage> with AutoDisposeStateMixin {
  late ScrollController _scrollController;

  late final _Controller controller;
  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());
    controller = useChangeNotifier(_Controller(context, widget.item))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  PreferredSizeWidget _widgetAppBar() {
    return AppBar(
      toolbarHeight: kTextTabBarHeight,
      title: const Text("放水详情"),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _widgetAppBar(),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: controller.isLoading
              ? _widgetLoading(context)
              : _widgetBody(context)),
    );
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody(BuildContext context) {
    return Column(
      children: [
        _widgetSubMenu(),
        SizedBox(height: 6.px),
        Expanded(child: _widgetList(context))
      ],
    );
  }

  Widget _widgetSummary(String title, String text, {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
          width: 82.px,
          child: Text(
            title,
            style: TextStyle(
                color: const Color.fromRGBO(51, 51, 51, 0.4),
                fontWeight: FontWeight.w500,
                fontSize: 12.px),
          ),
        ),
        Expanded(
            child: Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetSubMenu() {
    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: Column(
          children: [
            Divider(
              color: const Color.fromRGBO(51, 51, 51, 0.05),
              height: 1.px,
            ),
            SizedBox(
              height: 20.px,
            ),
            _widgetSummary("配水员",
                controller.waterDistributionDetail?.distributorName ?? "-"),
            SizedBox(
              height: 10.px,
            ),
            _widgetSummary("调度员",
                controller.waterDistributionDetail?.regulatorName ?? "-"),
            SizedBox(
              height: 10.px,
            ),
            // _widgetSummary("身份证", item.idNimber ),
            // SizedBox(
            //   height: 10.px,
            // ),
            _widgetSummary("用水区间",
                "${controller.waterDistributionDetail?.usageBeginDate} 至 ${controller.waterDistributionDetail?.usageEndDate}"),

            SizedBox(
              height: 10.px,
            ),
            _widgetSummary("放水状态",
                controller.waterDistributionDetail?.releaseStatus ?? "-"),
            SizedBox(
              height: 10.px,
            ),
            _widgetSummary("总用水量",
                "${controller.waterDistributionDetail?.waterUsage ?? "-"}m³"),
            SizedBox(
              height: 20.px,
            ),
          ],
        ));
  }

  Widget _widgetList(BuildContext context) {
    var list = controller.waterDistributionDetail?.waterApplies ?? [];
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverList.builder(
          itemBuilder: (context, index) => DistributionWaterApplyItemWidget(
            onPressed: controller.onClickItem,
            item: list[index],
            onPressedPhone: controller.onPressedPhone,
            checkable: false,
            onCheck: (int index, bool value) {},
            index: index,
          ),
          itemCount: list.length,
        )
      ],
    );
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  final WaterDistributionItem item;

  _Controller(this.context, this.item);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  WaterDistributionDetail? waterDistributionDetail;

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    _loadingStatus = LoadingStatus.loading;
    notifyListeners();

    XinJiangWaterManageService()
        .waterApplyHandleDistributorReleaseInfo(item.wrId,
            cancelToken: createCancelToken())
        .then((result) {
      Log.d("result is $result");

      waterDistributionDetail =
          WaterDistributionDetail.fromJson(result.data ?? {});
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(handleError);
  }

  void onClickItem(WaterApplies item) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => WaterApplyDetailPage(
                  waId: item.waId!,
                  showApproval: false,
                )))
        .then((result) {
      if (result == true) {}
    });
  }

  void onPressedPhone(WaterApplies item) {
    if (item.operatePhone?.isEmpty ?? true) {
      return;
    }

    launchUrl(Uri.parse("tel:${item.operatePhone}"));
  }
}
