import 'package:intl/intl.dart';

import '../../../../model/dict_tree_model.dart';
import 'widget/bdh_steps2.dart';

const priceRecordTitleDict = [
  "不超过定额部分",
  "超过额定不足50%",
  "超过额定50%不足1倍",
  "超过额定1倍不足1.5倍",
];

const moneyDict = [100, 300, 500, 1000, 3000, 5000];
const waterDict = [100, 300, 500, 1000, 3000, 5000];

const plotTypeDict = {
  1: "身份地",
  2: "经营地",
};

const payTypeDict = {
  1: "线上",
  2: "线下",
};

const recordTypeDict = {
  1: "缴费",
  2: "退费",
};

List<BdhStepsItemData> plantingApplyStepDict = const [
  BdhStepsItemData(title: "选择地块"),
  BdhStepsItemData(title: "种植信息")
];

const waterApplyReviewStepDict = [
  BdhStepsItemData(title: "选择用水记录"),
  BdhStepsItemData(title: "填写用水信息")
];

const waterApplySettleStepDict = [
  BdhStepsItemData(title: "选择用户"),
  BdhStepsItemData(title: "填写结算信息")
];

typedef IndexValueChanged<T> = void Function(int index, T value);
typedef ValueCallback<T> = void Function(T value);

final dateFormat = DateFormat("yyyy-MM-dd");
final dateFormatyyyyMM = DateFormat("yyyy年MM月");
final dateFormatyyyyMM2 = DateFormat("yyyyMM");
final timeFormat = DateFormat('yyyy-MM-dd HH:mm:ss');

const costDict = [
  "不超过定额部分",
  "超过额定不足50%",
  "超过额定50%不足1倍",
  "超过额定1倍不足1.5倍",
];
