import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/request/xinjiang_water_manage_service.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:oktoast/oktoast.dart';

import 'const_dict.dart';
import 'model/water_apply_detail_model.dart';

//用户-结算详情
class MyWaterUseDetailPage extends StatefulWidget {
  final int waId;
  const MyWaterUseDetailPage({super.key, required this.waId});

  @override
  State<MyWaterUseDetailPage> createState() => _MyWaterUseDetailPageState();
}

class _MyWaterUseDetailPageState extends State<MyWaterUseDetailPage>
    with AutoDisposeStateMixin {
  late final ScrollController _scrollController;

  late final _Controller controller;

  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());
    controller = useChangeNotifier(_Controller(context, widget.waId))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("结算详情"),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child:
              controller.isLoading ? _widgetLoading(context) : _widgetList()),
    );
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  String get plotText {
    // if (controller.model?.plotType == 1) {
    //   return "身份地";
    // } else if (controller.model?.plotType == 2) {
    //   return "经营地";
    // }
    return controller.model?.plotType ?? "-";
  }

  String get usageTimeText {
    return controller.model?.usageBeginDate ??
        controller.model?.actualUsageDate ??
        controller.model?.usageDate ??
        "-";
  }

  Widget _widgetList() {
    return SingleChildScrollView(
      controller: _scrollController,
      child: Column(
        children: [
          ColoredBox(
              color: Colors.white,
              child: Column(
                children: [
                  SizedBox(
                    height: 20.px,
                  ),
                  _widgetImage(),
                  SizedBox(
                    height: 20.px,
                  ),
                  _widgetMoney(),
                  SizedBox(
                    height: 36.px,
                  ),
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),
                  SizedBox(
                    height: 20.px,
                  ),
                  _widgetSummary("缴费户名", controller.model?.growerName ?? ""),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("所在单位", controller.model?.orgName ?? ""),
                  SizedBox(
                    height: 10.px,
                  ),
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("土地编号", controller.model?.plotNo ?? ""),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("类型", plotText),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("用途信息",
                      "${controller.model?.cropName}  ${controller.model?.plotArea} 亩"),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("位置信息", "${controller.model?.canal}"),
                  SizedBox(
                    height: 10.px,
                  ),
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),

                  SizedBox(
                    height: 10.px,
                  ),
                  // _widgetSummary(
                  //     "申请用水量", "${controller.model?.waterCons ?? "-"} m³"),
                  // SizedBox(
                  //   height: 10.px,
                  // ),
                  _widgetSummary("本次用水量",
                      "${controller.model?.actualWaterUsage ?? "-"} m³"),

                  SizedBox(
                    height: 10.px,
                  ),
                  // _widgetSummary("申请日期", controller.model?.createTime ?? "-"),
                  // SizedBox(
                  //   height: 10.px,
                  // ),
                  _widgetSummary("放水日期", usageTimeText),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("结算用水总量",
                      "${controller.model?.accumWaterConsume ?? "-"} m³"),
                  SizedBox(
                    height: 10.px,
                  ),
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),
                  ..._widgetPriceRecords(),
                  SizedBox(
                    height: 10.px,
                  ),
                ],
              )),
          SizedBox(
            height: 6.px,
          ),
        ],
      ),
    );
  }

  List<Widget> _widgetPriceRecords() {
    return controller.model?.priceRecords
            ?.asMap()
            .map<int, List<Widget>>((index, item) {
              var list = <Widget>[
                SizedBox(
                  height: 10.px,
                ),
                _widgetSummary3(item.priceLevelDesc ?? costDict[index],
                    "${item.waterCons}m³", "${item.amount}元")
              ];

              return MapEntry(index, list);
            })
            .values
            .toList()
            .fold<List<Widget>>(<Widget>[],
                (List<Widget> list, List<Widget> item) {
              list.addAll(item);
              return list;
            }) ??
        [];
  }

  Widget _widgetImage() {
    return Center(
        child: SvgPicture.asset(
            alignment: Alignment.center,
            fit: BoxFit.cover,
            width: 60.px,
            height: 60.px,
            ImageHelper.wrapAssets("ic_water_account.svg")));
  }

  Widget _widgetSummary(String title, String text, {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
          width: 100.px,
          child: Text(
            title,
            style: TextStyle(
                color: const Color.fromRGBO(51, 51, 51, 0.4),
                fontWeight: FontWeight.w500,
                fontSize: 12.px),
          ),
        ),
        Expanded(
            child: Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetSummary3(String title, String text, String subText,
      {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
          width: 220.px,
          child: Text(
            title,
            style: TextStyle(
                color: const Color.fromRGBO(51, 51, 51, 0.4),
                fontWeight: FontWeight.w500,
                fontSize: 12.px),
          ),
        ),
        Expanded(
            child: Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        )),
        Text(
          subText,
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetMoney() {
    return Center(
        child: Text(
      "-${controller.model?.amount}",
      style: TextStyle(
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w600,
          fontSize: 28.px),
    ));
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;
  final int waId;

  _Controller(this.context, this.waId);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  WaterApplyDetailModel? model;

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    XinJiangWaterManageService()
        .waterHandleInfo(waId, cancelToken: createCancelToken())
        .then((result) {
      model = WaterApplyDetailModel.fromJson(result.data);
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  Null _handlerError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  void onClickTicket() {}
}
