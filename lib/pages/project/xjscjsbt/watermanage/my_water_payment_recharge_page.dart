import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_api_request.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/pos_bank_data_model.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import 'const_dict.dart';
import 'model/water_manage_index_model.dart';

//用户-账户充值
class MyWaterPaymentRechargePage extends StatefulWidget {
  final MyAcc item;
  const MyWaterPaymentRechargePage({super.key, required this.item});

  @override
  State<MyWaterPaymentRechargePage> createState() =>
      _MyWaterPaymentRechargePageState();
}

class _MyWaterPaymentRechargePageState extends State<MyWaterPaymentRechargePage>
    with AutoDisposeStateMixin, WidgetsBindingObserver {
  late final ScrollController _scrollController;

  late final _Controller controller;

  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());

    controller = useChangeNotifier(_Controller(context, widget.item))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();

    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    //自己的逻辑
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    //自己的逻辑
    if (state == AppLifecycleState.resumed) {
      controller.checkBankChargeStatus();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("账户充值"),
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: SafeArea(
          top: false,
          child: controller.isLoading
              ? _widgetLoading(context)
              : Stack(
                  children: [
                    _widgetList(),
                    Positioned(bottom: 0, child: _widgetSubmitButton())
                  ],
                ),
        ));
  }

  Widget _widgetSubmitButton() {
    return Padding(
        padding: EdgeInsets.only(left: 24.px, right: 24.px, bottom: 12.px),
        child: BdhTextButton(
          width: 327.px,
          height: 40.px,
          text: '缴费',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(22.px)),
          backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          disableBackgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: controller._onSubmit,
        ));
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetList() {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverToBoxAdapter(
            child: ColoredBox(
                color: Colors.white,
                child: Column(
                  children: [
                    Divider(
                      color: const Color.fromRGBO(51, 51, 51, 0.05),
                      height: 1.px,
                      indent: 20.px,
                      endIndent: 20.px,
                    ),
                    SizedBox(
                      height: 10.px,
                    ),
                    _widgetSummary("余额", "${widget.item.accBalance ?? "0"}元"),
                    SizedBox(
                      height: 10.px,
                    ),
                    Divider(
                      color: const Color.fromRGBO(51, 51, 51, 0.05),
                      height: 1.px,
                      indent: 20.px,
                      endIndent: 20.px,
                    ),
                    SizedBox(
                      height: 10.px,
                    ),
                    _widgetSummary("缴费户名", "${widget.item.growerName ?? "-"}"),
                    SizedBox(
                      height: 10.px,
                    ),
                    Divider(
                      color: const Color.fromRGBO(51, 51, 51, 0.05),
                      height: 1.px,
                      indent: 20.px,
                      endIndent: 20.px,
                    ),
                    SizedBox(
                      height: 10.px,
                    ),
                    _widgetSummary("账户类型", widget.item.growerType ?? ""),
                    SizedBox(
                      height: 10.px,
                    ),
                    Divider(
                      color: const Color.fromRGBO(51, 51, 51, 0.05),
                      height: 1.px,
                      indent: 20.px,
                      endIndent: 20.px,
                    ),
                    SizedBox(
                      height: 10.px,
                    ),
                    _widgetSummary("缴费单位", "${widget.item.orgName}"),
                    SizedBox(
                      height: 10.px,
                    ),
                    Divider(
                      color: const Color.fromRGBO(51, 51, 51, 0.05),
                      height: 1.px,
                      indent: 20.px,
                      endIndent: 20.px,
                    ),
                    SizedBox(
                      height: 10.px,
                    ),
                    _widgetMoney(),
                    SizedBox(
                      height: 20.px,
                    ),
                  ],
                ))),
        _widgetMoneyDict(),
        SliverPadding(
          padding: EdgeInsets.only(
              left: 26.px, right: 26.px, top: 8.px, bottom: 84.px),
          sliver: SliverToBoxAdapter(
            child: _widgetWarn(),
          ),
        )
      ],
    );
  }

  Widget _widgetWarn() {
    return Text(
      "每次缴费不能超过全年的费用的30%",
      textAlign: TextAlign.center,
      style: TextStyle(
        fontSize: 14.px,
        fontWeight: FontWeight.w500,
        fontFamily: "BEBAS",
        color: Colors.red,
      ),
    );
  }

  Widget _widgetMoneyDict() {
    return SliverPadding(
        padding: EdgeInsets.only(left: 8.px, right: 8.px, top: 8.px),
        sliver: SliverGrid.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3, // 列数
                crossAxisSpacing: 8.px, // 交叉轴间距
                mainAxisSpacing: 8.px, // 主轴间距
                mainAxisExtent: 54.px),
            itemCount: moneyDict.length,
            itemBuilder: (context, index) {
              var item = moneyDict[index];
              bool checked = controller.moneyIndexChecked == index;
              return GestureDetector(
                  onTap: () {
                    controller.onClickMoneyCheck(index);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: checked
                          ? const Color.fromRGBO(30, 192, 106, 1)
                          : Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(8.px)),
                    ),
                    height: 54.px,
                    child: Center(
                        child: Text(
                      "$item元",
                      style: TextStyle(
                          color: checked ? Colors.white : Colors.black,
                          fontWeight: FontWeight.w500,
                          fontSize: 16.px),
                    )),
                  ));
            }));
  }

  Widget _widgetSummary(String title, String text, {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Text(
          title,
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500,
              fontSize: 16.px),
        ),
        const Spacer(),
        Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 16.px),
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetMoney() {
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: 20.px,
            ),
            SizedBox(
              width: 82.px,
              child: Text(
                "缴费金额",
                style: TextStyle(
                    color: const Color.fromRGBO(51, 51, 51, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 16.px),
              ),
            )
          ],
        ),
        SizedBox(
          height: 18.px,
        ),
        Row(
          children: [
            SizedBox(
              width: 20.px,
            ),
            Text(
              "¥",
              style: TextStyle(
                  color: const Color.fromRGBO(51, 51, 51, 1),
                  fontWeight: FontWeight.w600,
                  fontFamily: "BEBAS",
                  fontSize: 20.px),
            ),
            SizedBox(
              width: 20.px,
            ),
            Expanded(
                child: CupertinoTextField.borderless(
              focusNode: controller._focusNode,
              onChanged: controller.onTextChange,
              controller: controller._editingController,
              placeholder: "请输入金额",
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              clearButtonMode: OverlayVisibilityMode.editing,
              padding: EdgeInsets.zero,
              style: TextStyle(
                fontSize: 36.px,
                fontWeight: FontWeight.w600,
                fontFamily: "BEBAS",
                color: const Color.fromRGBO(44, 44, 52, 1),
              ),
              placeholderStyle: TextStyle(
                  fontSize: 36.px,
                  fontWeight: FontWeight.w400,
                  fontFamily: "BEBAS",
                  color: const Color.fromRGBO(51, 51, 51, 0.4)),
            )),
            SizedBox(
              width: 20.px,
            ),
          ],
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;
  final MyAcc item;

  late final TextEditingController _editingController;

  late FocusNode _focusNode;

  _Controller(this.context, this.item) {
    _focusNode = createFocusNode();
    _editingController = createTextController();
  }

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  String? inputMoney;

  int? moneyIndexChecked;

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }

    _loadingStatus = LoadingStatus.success;
    notifyListeners();
  }

  void onClickMoneyCheck(int index) {
    moneyIndexChecked = index;

    _editingController.text = moneyDict[index].toString();
    notifyListeners();
  }

  void onTextChange(String text) {
    inputMoney = text;

    if (moneyIndexChecked != null &&
        text != moneyDict[moneyIndexChecked!].toString()) {
      moneyIndexChecked = null;
      notifyListeners();
    }
  }

  void _onSubmit() {
    if (_editingController.text.isEmpty) {
      showToast('请输入或选择缴费金额');
      return;
    }
    String numberString = _editingController.text;

    if (!RegUtil.isNumericAmount(numberString)) {
      showToast('缴费金额输入有误, 请重新输入');
      return;
    }
    double number = double.parse(numberString); // 字符串转浮点数
    if (number <= 0.0) {
      showToast('缴费金额不能为零');
      return;
    }

    showConfirmDialog(context,
            cancel: "取消", confirm: "确认", message: "每次缴费不能超过全年的费用的30%,是否继续缴费?")
        .then((result) {
      if (result == true) {
        checkIsCanReCharge();
      }
    });
  }

//检查是否可以缴费 [app 支付 step1]
  void checkIsCanReCharge() {
    //先把请求状态设置为空
    posBankDataModel = null;
    DateTime now = DateTime.now();
    int currentYear = now.year;
    String yearString = currentYear.toString();
    String numberString = _editingController.text;
    double number = double.parse(numberString); // 字符串转浮点数
    String formattedNumber = number.toStringAsFixed(2); // 将浮点数格式化为保留两位有效数字的字符串
    showLoading(context, content: "正在提交 ", barrierDismissible: false);
    PosResponsitory.checkIsCanReCharge({
      "accId": item.accId,
      "year": yearString,
      "amount": formattedNumber,
    }, cancelToken: createCancelToken())
        .then((res) {
      Log.d(res);
      if (!context.mounted) {
        return;
      }
      if (res['code'] == 0) {
        getOnLineChargeBankSdkInfo();
      } else {
        hideLoading(context);
        // showToast(res['msg']);
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    });
  }

  PosBankDataModel? posBankDataModel;

  //检查支付的状态
  //这里应该弹框让让用户自己确认？
  void checkBankChargeStatus() {
    var orderId = posBankDataModel?.data?.orderId;
    if (orderId == null) {
      return;
    }
    //只允许检查一次?
    //posBankDataModel = null;
    showLoading(context, content: "正检查 ", barrierDismissible: false);
    PosResponsitory.getOnLineChargeQueryStatus({'onlinePayOrderId': orderId},
            cancelToken: createCancelToken())
        .then((result) {
      if (!context.mounted) {
        return;
      }
      Log.d("result is ${result}");
      hideLoading(context);
      // bus.emit("pay");
      // Navigator.of(context).pop(true);
      //只允许检查一次?
      posBankDataModel = null;
      if (result.code == 0 && result.success == true) {
        Navigator.of(context).pop(true);
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    });
  }

//获取银行sdk信息 [app 支付 step2]
  void getOnLineChargeBankSdkInfo() {
    String numberString = _editingController.text;
    double number = double.parse(numberString); // 字符串转浮点数
    String formattedNumber = number.toStringAsFixed(2); // 将浮点数格式化为保留两位有效数字的字符串

    PosResponsitory.getOnLineChargeBankSdkInfo({
      "accId": item.accId,
      "amount": formattedNumber,
    }, cancelToken: createCancelToken())
        .then((res) {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
      if (res.code == 0) {
        // app 拉起农行支付 参数要在外多面包一层map
        Map<String, dynamic> params = {'tokenId': res.data?.tokenId ?? ''};
        Map<String, dynamic> paramsEnd = {'tokenId': params};
        //缓存请求的实体
        posBankDataModel = res;
        //获取支付状态 [app 支付 step3]
        NativeUtil.openABCToPay(paramsEnd);
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    });
  }
}
