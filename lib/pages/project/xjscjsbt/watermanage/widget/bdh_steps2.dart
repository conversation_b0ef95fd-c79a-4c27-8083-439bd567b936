import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/material.dart';

class BdhStepsItemData {
  const BdhStepsItemData({required this.title, this.content});

  /// 标题
  final String title;
  final String? content;
}

class BdhStepsHorizontalItem extends StatelessWidget {
  final BdhStepsItemData data;
  final int index;
  final int stepsCount;
  final int activeIndex;
  final double? iconTextSize;
  final double? titleTextSize;
  final double? outerIconSize;
  final double? innerIconSize;

  final Color? innerIconColor;
  final Color? outerIconColor;

  const BdhStepsHorizontalItem({
    super.key,
    required this.data,
    required this.index,
    required this.stepsCount,
    required this.activeIndex,
    this.iconTextSize,
    this.titleTextSize,
    this.outerIconSize,
    this.innerIconSize,
    this.innerIconColor,
    this.outerIconColor,
  });

  @override
  Widget build(BuildContext context) {
    /// 步骤条数字颜色
    var stepsNumberTextColor = const Color.fromRGBO(255, 255, 255, 1);

    /// 步骤条标题颜色
    var stepsTitleColor = const Color.fromRGBO(44, 44, 52, 1);

    Color innerIconActiveColor =
        innerIconColor ?? const Color.fromRGBO(10, 174, 108, 1);
    Color outerIconActiveColor =
        outerIconColor ?? const Color.fromRGBO(10, 174, 108, 0.2);

    /// 激活索引大于当前索引
    if (activeIndex < index) {
      /// 激活索引小于当前索引
      stepsTitleColor = const Color.fromRGBO(44, 44, 52, 0.4);
    }

    /// 步骤条icon图标组件，默认为索引文字
    Widget? stepsIconWidget = Text(
      (index + 1).toString(),
      style: TextStyle(
        color: stepsNumberTextColor,
        fontWeight: FontWeight.w700,
        fontSize: iconTextSize ?? 10.px,
      ),
    );

    // icon组件容器大小
    // double iconContainerSize = 22;

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(
              flex: 1,
              child: Visibility(
                visible: index == 0 ? false : true,
                child: DottedLine(
                  lineLength: double.infinity,
                  dashRadius: 1.5.px,
                  dashLength: 3.px,
                  dashGapLength: 3.px,
                  lineThickness: 3.px,
                  dashColor: activeIndex >= index
                      ? innerIconActiveColor
                      : const Color.fromRGBO(44, 44, 52, 0.4),
                ),
              ),
            ),
            Container(
              width: outerIconSize ?? 21.px,
              height: outerIconSize ?? 21.px,
              alignment: Alignment.center,
              margin: EdgeInsets.all(5.px),
              decoration: BoxDecoration(
                  color: activeIndex < index
                      ? const Color.fromRGBO(173, 177, 185, 0.2)
                      : outerIconActiveColor,
                  borderRadius: BorderRadius.all(
                      Radius.circular((outerIconSize ?? 21.px) / 2))),
              child: Container(
                width: innerIconSize ?? 16.px,
                height: innerIconSize ?? 16.px,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: activeIndex < index
                        ? const Color.fromRGBO(173, 177, 185, 1)
                        : innerIconActiveColor,
                    borderRadius: BorderRadius.all(
                        Radius.circular((innerIconSize ?? 16.px) / 2))),
                child: stepsIconWidget,
              ),
            ),
            Expanded(
              flex: 1,
              child: Visibility(
                visible: index == stepsCount - 1 ? false : true,
                child: DottedLine(
                  lineLength: double.infinity,
                  dashRadius: 1.5.px,
                  dashLength: 3.px,
                  dashGapLength: 3.px,
                  lineThickness: 3.px,
                  dashColor: activeIndex > index
                      ? innerIconActiveColor
                      : const Color.fromRGBO(44, 44, 52, 0.4),
                ),
              ),
            ),
          ],
        ),
        Container(
          alignment: Alignment.center,
          child: Text(
            data.title,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: stepsTitleColor,
              fontSize: titleTextSize ?? 12.px,
            ),
          ),
        ),
      ],
    );
  }
}

class BdhStepsHorizontal extends StatelessWidget {
  final List<BdhStepsItemData> steps;
  final int activeIndex;

  final double? iconTextSize;
  final double? titleTextSize;

  final double? outerIconSize;
  final double? innerIconSize;
  final Color? innerIconColor;
  final Color? outerIconColor;

  const BdhStepsHorizontal({
    super.key,
    required this.steps,
    required this.activeIndex,
    this.iconTextSize,
    this.titleTextSize,
    this.outerIconSize,
    this.innerIconSize,
    this.innerIconColor,
    this.outerIconColor,
  });

  @override
  Widget build(BuildContext context) {
    final stepsCount = steps.length;
    List<Widget> stepsHorizontalItem = steps.asMap().entries.map((item) {
      return Expanded(
        flex: 1,
        child: BdhStepsHorizontalItem(
          index: item.key,
          data: item.value,
          stepsCount: stepsCount,
          activeIndex: activeIndex,
          iconTextSize: iconTextSize,
          titleTextSize: titleTextSize,
          outerIconSize: outerIconSize,
          innerIconSize: innerIconSize,
          innerIconColor: innerIconColor,
          outerIconColor: outerIconColor,
        ),
      );
    }).toList();

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: stepsHorizontalItem,
    );
  }
}

class BdhStepsVerticalItem extends StatelessWidget {
  final BdhStepsItemData data;
  final int index;
  final int stepsCount;
  final int activeIndex;

  final double? iconTextSize;
  final double? titleTextSize;
  final double? outerIconSize;
  final double? innerIconSize;

  const BdhStepsVerticalItem({
    super.key,
    required this.data,
    required this.index,
    required this.stepsCount,
    required this.activeIndex,
    this.iconTextSize,
    this.titleTextSize,
    this.outerIconSize,
    this.innerIconSize,
  });

  @override
  Widget build(BuildContext context) {
    /// 步骤条数字颜色
    var stepsNumberTextColor = const Color.fromRGBO(255, 255, 255, 1);

    /// 步骤条标题颜色
    var stepsTitleColor = const Color.fromRGBO(44, 44, 52, 1);

    /// 激活索引大于当前索引
    if (activeIndex < index) {
      /// 激活索引小于当前索引
      stepsTitleColor = const Color.fromRGBO(44, 44, 52, 0.4);
    }

    /// 步骤条icon图标组件，默认为索引文字
    Widget? stepsIconWidget = Text(
      (index + 1).toString(),
      style: TextStyle(
        color: stepsNumberTextColor,
        fontWeight: FontWeight.w700,
        fontSize: iconTextSize ?? 10.px,
      ),
    );

    return SizedBox(
      height: 90.px,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: outerIconSize ?? 21.px,
                height: outerIconSize ?? 21.px,
                alignment: Alignment.center,
                margin: EdgeInsets.all(5.px),
                decoration: BoxDecoration(
                    color: activeIndex < index
                        ? const Color.fromRGBO(173, 177, 185, 0.2)
                        : const Color.fromRGBO(10, 174, 108, 0.2),
                    borderRadius: BorderRadius.all(
                        Radius.circular((outerIconSize ?? 21.px) / 2))),
                child: Container(
                  width: innerIconSize ?? 16.px,
                  height: innerIconSize ?? 16.px,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      color: activeIndex < index
                          ? const Color.fromRGBO(173, 177, 185, 1)
                          : const Color.fromRGBO(10, 174, 108, 1),
                      borderRadius: BorderRadius.all(
                          Radius.circular((innerIconSize ?? 16.px) / 2))),
                  child: stepsIconWidget,
                ),
              ),
              Expanded(
                flex: 1,
                child: Visibility(
                  visible: index == stepsCount - 1 ? false : true,
                  child: DottedLine(
                    lineLength: double.infinity,
                    direction: Axis.vertical,
                    dashRadius: 1.5.px,
                    dashLength: 3.px,
                    dashGapLength: 3.px,
                    lineThickness: 3.px,
                    dashColor: activeIndex > index
                        ? const Color.fromRGBO(10, 174, 108, 1)
                        : const Color.fromRGBO(44, 44, 52, 0.4),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            width: 6.px,
          ),
          Expanded(
            flex: 1,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 8.px,
                ),
                Text(
                  data.title,
                  style: TextStyle(
                    fontWeight: (activeIndex == index)
                        ? FontWeight.w600
                        : FontWeight.w400,
                    color: stepsTitleColor,
                    fontSize: 16.px,
                    height: 1.2,
                  ),
                ),
                SizedBox(
                  height: 3.px,
                ),
                Text(
                  data.content ?? "",
                  style: TextStyle(
                    fontWeight: FontWeight.w400,
                    color: Colors.grey,
                    fontSize: 16.px,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class BdhStepsVertical extends StatelessWidget {
  final List<BdhStepsItemData> steps;
  final int activeIndex;

  final double? iconTextSize;
  final double? titleTextSize;

  final double? outerIconSize;
  final double? innerIconSize;

  const BdhStepsVertical({
    super.key,
    required this.steps,
    required this.activeIndex,
    this.iconTextSize,
    this.titleTextSize,
    this.outerIconSize,
    this.innerIconSize,
  });

  @override
  Widget build(BuildContext context) {
    final stepsCount = steps.length;
    List<Widget> stepsVerticalItem = steps.asMap().entries.map((item) {
      return BdhStepsVerticalItem(
        index: item.key,
        data: item.value,
        stepsCount: stepsCount,
        activeIndex: activeIndex,
        iconTextSize: iconTextSize,
        titleTextSize: titleTextSize,
        outerIconSize: outerIconSize,
        innerIconSize: innerIconSize,
      );
    }).toList();

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: stepsVerticalItem,
    );
  }
}
