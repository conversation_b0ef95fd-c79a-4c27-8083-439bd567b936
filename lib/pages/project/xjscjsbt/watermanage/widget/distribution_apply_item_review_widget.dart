import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../const_dict.dart';
import '../model/water_apply_item_model.dart';

//审核状态
const _stateList = {
  -1: {
    "text": "未填写",
    "textColor": Color.fromRGBO(254, 105, 58, 1),
    "barColor": [
      Color.fromRGBO(255, 110, 66, 1),
      Color.fromRGBO(248, 78, 21, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(248, 78, 21, 0.1)
  },
  0: {
    "text": "审核中",
    "textColor": Color.fromRGBO(153, 153, 153, 1),
    "barColor": [
      Color.fromRGBO(226, 231, 228, 1),
      Color.fromRGBO(169, 183, 180, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(226, 231, 228, 1)
  },
  1: {
    "text": "审核完成",
    "textColor": Color.fromRGBO(30, 192, 106, 1),
    "barColor": [
      Color.fromRGBO(63, 201, 145, 1),
      Color.fromRGBO(0, 171, 114, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(30, 192, 106, 0.1)
  },
  2: {
    "text": "未通过",
    "textColor": Color.fromRGBO(153, 153, 153, 1),
    "barColor": [
      Color.fromRGBO(226, 231, 228, 1),
      Color.fromRGBO(169, 183, 180, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(226, 231, 228, 1)
  },
};

class DistributionApplyItemReviewWidget extends StatelessWidget {
  final WaterApplyItem item;

  final ValueCallback<WaterApplyItem>? onPressed;

  final ValueCallback<WaterApplyItem>? onPressedPhone;

  final IndexValueChanged<bool> onCheck;

  final int index;

  final bool checkable;
  final bool checked;

  final ValueCallback<WaterApplyItem>? onClickReject;
  final ValueCallback<WaterApplyItem>? onClickApproval;
  final ValueCallback<WaterApplyItem>? onClickFlow;

  const DistributionApplyItemReviewWidget(
      {super.key,
      required this.onPressed,
      required this.item,
      this.checkable = false,
      this.checked = false,
      required this.onPressedPhone,
      required this.onCheck,
      required this.index,
      this.onClickReject,
      this.onClickApproval,
      this.onClickFlow});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          onPressed?.call(item);
        },
        child: Container(
          margin: EdgeInsets.only(
              left: 12.px, right: 12.px, bottom: 6.px, top: 6.px),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.px),
          ),
          child: Stack(
            children: [
              Positioned(
                  right: 0,
                  bottom: 0,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8.px), // 圆角矩形裁剪
                    child: SvgPicture.asset(
                        alignment: Alignment.center,
                        fit: BoxFit.cover,
                        width: 100.px,
                        ImageHelper.wrapAssets(
                            "bg_water_apply_item_bottom.svg")),
                  )),
              Column(
                children: [
                  Container(
                      decoration: hasBottom
                          ? BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                                      width: 1.px,
                                      color:
                                          const Color.fromRGBO(0, 0, 0, 0.05))),
                            )
                          : null,
                      child: Row(children: [
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: _stateList[item.drainageStatus == "处理中"
                                        ? 0
                                        : 1]?["barColor"] as List<Color>? ??
                                    const [
                                      Color.fromRGBO(226, 231, 228, 1),
                                      Color.fromRGBO(169, 183, 180, 1),
                                    ]),
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(4.px),
                                bottomRight: Radius.circular(4.px)),
                          ),
                          width: 4.px,
                          height: 36.px,
                        ),
                        Expanded(
                            child: Padding(
                                padding: EdgeInsets.only(
                                    left: 16.px,
                                    right: 20.px,
                                    top: 20.px,
                                    bottom: 10.px),
                                child: Column(
                                  children: [
                                    _widgetTitle(),
                                    // SizedBox(
                                    //   height: 10.px,
                                    // ),
                                    // _widgetAreaInfo(),
                                    // SizedBox(
                                    //   height: 10.px,
                                    // ),
                                    // _widgetColumn1(),
                                    SizedBox(
                                      height: 10.px,
                                    ),
                                    _widgetColumn2(),
                                    SizedBox(
                                      height: 10.px,
                                    ),
                                    _widgetColumn3(),
                                    SizedBox(
                                      height: 10.px,
                                    ),
                                    _widgetColumn4(),
                                    SizedBox(
                                      height: 10.px,
                                    ),
                                    _widgetColumn5()
                                  ],
                                )))
                      ])),
                  if (hasBottom) ...[
                    SizedBox.square(
                      dimension: 6.px,
                    ),
                    _widgetBottom(),
                    SizedBox.square(
                      dimension: 6.px,
                    ),
                  ]
                ],
              )
            ],
          ),
        ));
  }

  bool get hasBottom {
    if (onClickFlow != null ||
        onClickReject != null ||
        onClickApproval != null) {
      return true;
    }
    return false;
  }

  Widget _widgetBottom() {
    return Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
      const Spacer(),
      if (onClickFlow != null) ...[
        SizedBox.square(
          dimension: 6.px,
        ),
        _widgetButton("查看流程", onPressed: () {
          onClickFlow?.call(item);
        })
      ],
      if (onClickReject != null) ...[
        SizedBox.square(
          dimension: 6.px,
        ),
        _widgetButton("驳回", onPressed: () {
          onClickReject?.call(item);
        })
      ],
      if (onClickApproval != null) ...[
        SizedBox.square(
          dimension: 6.px,
        ),
        _widgetButton("审核", onPressed: () {
          onClickApproval?.call(item);
        })
      ],
      SizedBox.square(
        dimension: 12.px,
      ),
    ]);
  }

  Widget _widgetButton(String text, {VoidCallback? onPressed}) {
    return BdhTextButton(
      width: 72.px,
      height: 26.px,
      text: text,
      textSize: 12.px,
      textFontWeight: FontWeight.w500,
      borderRadius: BorderRadius.all(Radius.circular(13.px)),
      side: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(30, 192, 106, 1),
      ),
      pressedSide: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(30, 192, 106, 1),
      ),
      disableSide: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(1, 1, 1, 0.1),
      ),
      onPressed: onPressed,
    );
  }

  Widget _widgetColumn1() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          "种植作物:",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
        SizedBox.square(
          dimension: 6.px,
        ),
        Text(
          item.cropName ?? "-",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
        Expanded(
            child: Text(
          "第${item.waterFreq}次用水",
          textAlign: TextAlign.end,
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        )),
      ],
    );
  }

  Widget _widgetColumn2() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          "申请用水量:",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
        SizedBox(
          width: 6.px,
        ),
        Text(
          "${item.actualWaterUsage ?? item.waterCons ?? "-"}m³",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
        SizedBox(
          width: 36.px,
        ),
        Text(
          "面积:",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
        SizedBox.square(
          dimension: 6.px,
        ),
        Text(
          "${item.plotArea}亩",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
      ],
    );
  }

  Widget _widgetColumn3() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          "申请用水日期:",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
        SizedBox.square(
          dimension: 6.px,
        ),
        Text(
          item.usageDate ?? "",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
      ],
    );
  }

  Widget _widgetColumn4() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          "申请日期:",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
        SizedBox.square(
          dimension: 6.px,
        ),
        Text(
          item.createTime ?? "",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
      ],
    );
  }

  Widget _widgetColumn5() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          "接水员:",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
        SizedBox.square(
          dimension: 6.px,
        ),
        Text(
          item.receiverName ?? "-",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500,
              fontSize: 14.px),
        ),
      ],
    );
  }

  Widget _widgetTitle() {
    Widget titleWidget = Text(
      "${item.orgName ?? ""} ${item.canal ?? ""}",
      style: TextStyle(
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w500,
          fontSize: 14.px),
    );

    if (checkable) {
      titleWidget = GestureDetector(
        onTap: () {
          onCheck.call(index, !checked);
        },
        child: titleWidget,
      );
    }
    titleWidget = Expanded(child: titleWidget);
    return Row(
      children: [
        if (checkable) ...[
          BdhRadio(
            iconSize: 16.px,
            checked: checked,
            onCheckBoxChanged: (selected) {
              onCheck.call(index, selected);
            },
          ),
          SizedBox(
            width: 6.px,
          )
        ],
        titleWidget,
        GestureDetector(
            onTap: () {
              onPressedPhone?.call(item);
            },
            child: Row(
              children: [
                SvgPicture.asset(
                  ImageHelper.wrapAssets("ic_telephone.svg"),
                  width: 24.px,
                  height: 24.px,
                ),
                Text(
                  "拨打电话",
                  style: TextStyle(
                      color: const Color.fromRGBO(30, 192, 106, 1),
                      fontWeight: FontWeight.w500,
                      fontSize: 14.px),
                )
              ],
            )),
      ],
    );
  }

  Widget _widgetAreaNo() {
    return Row(
      children: [
        Expanded(
            child: Text(
          item.plotNo ?? "-",
          style: TextStyle(
              fontFamily: "BEBAS",
              color: const Color.fromRGBO(0, 152, 91, 1),
              fontSize: 24.px,
              fontWeight: FontWeight.w400),
        ))
      ],
    );
  }

  Widget _widgetAreaInfo() {
    return Container(
      padding:
          EdgeInsets.only(left: 10.px, right: 10.px, top: 10.px, bottom: 10.px),
      decoration: BoxDecoration(
        color: const Color.fromRGBO(243, 245, 249, 0.4),
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                " 地块编号:",
                style: TextStyle(
                    color: const Color.fromRGBO(51, 51, 51, 0.4),
                    fontWeight: FontWeight.w500,
                    fontSize: 13.px),
              ),
              Expanded(
                  child: Text(
                item.canal ?? "",
                textAlign: TextAlign.end,
                style: TextStyle(
                    color: const Color.fromRGBO(51, 51, 51, 0.4),
                    fontWeight: FontWeight.w500,
                    fontSize: 13.px),
              )),
              SizedBox.square(
                dimension: 12.px,
              ),
            ],
          ),
          SizedBox(
            height: 10.px,
          ),
          _widgetAreaNo()
        ],
      ),
    );
  }
}
