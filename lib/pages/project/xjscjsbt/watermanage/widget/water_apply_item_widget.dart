//申请记录 item
import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../const_dict.dart';
import '../model/water_manage_index_model.dart';

//审核状态
const _stateList = {
  0: {
    "text": "处理中",
    "textColor": Color.fromRGBO(254, 105, 58, 1),
    "barColor": [
      Color.fromRGBO(255, 110, 66, 1),
      Color.fromRGBO(248, 78, 21, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(248, 78, 21, 0.1)
  },
  1: {
    "text": "已放水",
    "textColor": Color.fromRGBO(30, 192, 106, 1),
    "barColor": [
      Color.fromRGBO(63, 201, 145, 1),
      Color.fromRGBO(0, 171, 114, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(30, 192, 106, 0.1)
  },
};

class WaterApplyItemWidget extends StatelessWidget {
  final MyApply item;
  final ValueCallback<MyApply> onClickProcess;
  final ValueCallback<MyApply> onClick;

  const WaterApplyItemWidget(
      {super.key,
      required this.item,
      required this.onClickProcess,
      required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          onClick.call(item);
        },
        child: Container(
            margin: EdgeInsets.only(
                top: 6.px, bottom: 6.px, left: 12.px, right: 12.px),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.px),
            ),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: _stateList[item.drainageStatus == "已放水" ? 1 : 0]
                                ?["barColor"] as List<Color>? ??
                            const [
                              Color.fromRGBO(226, 231, 228, 1),
                              Color.fromRGBO(169, 183, 180, 1),
                            ]),
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(4.px),
                        bottomRight: Radius.circular(4.px)),
                  ),
                  width: 4.px,
                  height: 36.px,
                ),
                Expanded(
                    child: Stack(
                  children: [
                    Positioned(
                        right: 0,
                        child: Container(
                          height: 24.px,
                          padding: EdgeInsets.only(left: 8.px, right: 8.px),
                          decoration: BoxDecoration(
                            color:
                                _stateList[item.drainageStatus == "已放水" ? 1 : 0]
                                        ?["stateBackgroundColor"] as Color? ??
                                    const Color.fromRGBO(234, 234, 234, 1),
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(8.px),
                                bottomLeft: Radius.circular(8.px)),
                          ),
                          child: Center(
                              child: Text(
                            _stateList[item.drainageStatus == "已放水" ? 1 : 0]
                                    ?["text"] as String? ??
                                "",
                            style: TextStyle(
                                color: _stateList[item.drainageStatus == "已放水"
                                        ? 1
                                        : 0]?["textColor"] as Color? ??
                                    const Color.fromRGBO(234, 234, 234, 1),
                                fontWeight: FontWeight.w600,
                                fontSize: 12.px),
                          )),
                        )),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          height: 16.px,
                        ),
                        Row(
                          children: [
                            SizedBox.square(
                              dimension: 16.px,
                            ),
                            Text(
                              "地块编号:",
                              style: TextStyle(
                                  color: const Color.fromRGBO(44, 44, 52, 1),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16.px),
                            ),
                            SizedBox.square(
                              dimension: 6.px,
                            ),
                            Text(
                              item.plotNo ?? "",
                              style: TextStyle(
                                  color: const Color.fromRGBO(51, 51, 51, 1),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16.px),
                            ),
                          ],
                        ),
                        SizedBox.square(
                          dimension: 13.px,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            SizedBox.square(
                              dimension: 16.px,
                            ),
                            SizedBox(
                                width: 70.px,
                                child: Text(
                                  "种 植 作 物 :",
                                  textAlign: TextAlign.justify,
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(51, 51, 51, 0.4),
                                      fontWeight: FontWeight.w500,
                                      fontSize: 12.px),
                                )),
                            SizedBox.square(
                              dimension: 6.px,
                            ),
                            Text(
                              item.cropName ?? "",
                              style: TextStyle(
                                  color: const Color.fromRGBO(51, 51, 51, 1),
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12.px),
                            ),
                            SizedBox.square(
                              dimension: 12.px,
                            ),
                            _widgetTitleRight(),
                          ],
                        ),
                        SizedBox.square(
                          dimension: 4.px,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            SizedBox.square(
                              dimension: 16.px,
                            ),
                            Flexible(
                                fit: FlexFit.tight,
                                flex: 1,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    SizedBox(
                                        width: 70.px,
                                        child: Text(
                                          "申请用水量:",
                                          textAlign: TextAlign.justify,
                                          style: TextStyle(
                                              color: const Color.fromRGBO(
                                                  51, 51, 51, 0.4),
                                              fontWeight: FontWeight.w500,
                                              fontSize: 12.px),
                                        )),
                                    SizedBox.square(
                                      dimension: 6.px,
                                    ),
                                    Text(
                                      "${item.actualWaterUsage ?? (item.waterCons ?? "-")}m³",
                                      style: TextStyle(
                                          color: const Color.fromRGBO(
                                              51, 51, 51, 1),
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12.px),
                                    ),
                                  ],
                                )),
                            Flexible(
                                fit: FlexFit.tight,
                                flex: 1,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      "面 积 :",
                                      style: TextStyle(
                                          color: const Color.fromRGBO(
                                              51, 51, 51, 0.4),
                                          fontWeight: FontWeight.w500,
                                          fontSize: 12.px),
                                    ),
                                    SizedBox.square(
                                      dimension: 6.px,
                                    ),
                                    Text.rich(
                                      TextSpan(children: [
                                        TextSpan(
                                            text: item.plotArea?.toString() ??
                                                ""),
                                        const TextSpan(text: " 亩 ")
                                      ]),
                                      style: TextStyle(
                                          color: const Color.fromRGBO(
                                              51, 51, 51, 1),
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12.px),
                                    ),
                                  ],
                                ))
                          ],
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            SizedBox.square(
                              dimension: 16.px,
                            ),
                            Flexible(
                                fit: FlexFit.tight,
                                flex: 1,
                                child: Row(
                                  children: [
                                    SizedBox(
                                        width: 70.px,
                                        child: Text(
                                          "放 水 日 期 :",
                                          style: TextStyle(
                                              color: const Color.fromRGBO(
                                                  51, 51, 51, 0.4),
                                              fontWeight: FontWeight.w500,
                                              fontSize: 12.px),
                                        )),
                                    SizedBox.square(
                                      dimension: 6.px,
                                    ),
                                    Text(
                                      item.usageDate ?? "",
                                      style: TextStyle(
                                          color: const Color.fromRGBO(
                                              51, 51, 51, 1),
                                          fontWeight: FontWeight.w600,
                                          fontSize: 12.px),
                                    ),
                                  ],
                                )),
                            item.drainageStatus == "已放水"
                                ? Flexible(
                                    fit: FlexFit.tight,
                                    flex: 1,
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Text(
                                          "结 算 金 额 :",
                                          style: TextStyle(
                                              color: const Color.fromRGBO(
                                                  51, 51, 51, 0.4),
                                              fontWeight: FontWeight.w500,
                                              fontSize: 12.px),
                                        ),
                                        SizedBox.square(
                                          dimension: 6.px,
                                        ),
                                        Text.rich(
                                          TextSpan(children: [
                                            const TextSpan(text: "¥"),
                                            TextSpan(text: formatNumberMoney)
                                          ]),
                                          style: TextStyle(
                                              fontFamily: "BEBAS",
                                              color: const Color.fromRGBO(
                                                  0, 152, 91, 1),
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16.px),
                                        ),
                                      ],
                                    ))
                                : Flexible(
                                    fit: FlexFit.tight,
                                    flex: 1,
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        const Spacer(),
                                        _widgetProcessButton(),
                                        SizedBox.square(
                                          dimension: 12.px,
                                        ),
                                      ],
                                    ))
                          ],
                        ),
                        SizedBox.square(
                          dimension: 12.px,
                        ),
                      ],
                    ),
                  ],
                ))
              ],
            )));
  }

  Widget _widgetProcessButton() {
    return BdhTextButton(
      width: 80.px,
      height: 24.px,
      text: '查看流程',
      textFontWeight: FontWeight.w500,
      textSize: 13.px,
      borderRadius: BorderRadius.all(Radius.circular(22.px)),
      backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
      disableBackgroundColor: const Color.fromRGBO(30, 192, 106, 1),
      pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: () {
        onClickProcess.call(item);
      },
    );
  }

  String get formatNumberMoney {
    if (item.amount == null) {
      return "";
    }
    // 创建一个NumberFormat对象，指定货币格式和地区（例如"en_US"为美国）
    final format = NumberFormat.decimalPattern("en_US");
    // 格式化数字
    return format.format(item.amount!);
  }

  String get address {
    var str = "";
    if (item.plotRel?.regimentCanal != null) {
      str += "${item.plotRel?.regimentCanal}团";
    }
    if (item.plotRel?.companyCanal != null) {
      str += "${item.plotRel?.companyCanal}连";
    }
    if (item.plotRel?.mainCanal != null) {
      str += "${item.plotRel?.mainCanal}干";
    }
    if (item.plotRel?.branchCanal != null) {
      str += "${item.plotRel?.branchCanal}支";
    }
    if (item.plotRel?.lateralCanal != null) {
      str += "${item.plotRel?.lateralCanal}斗";
    }
    if (str.isEmpty) str = "-";
    return str;
  }

  Widget _widgetTitleRight() {
    return Container(
        height: 17.px,
        padding: EdgeInsets.only(right: 4.px, left: 4.px),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: const Color.fromRGBO(255, 147, 6, 0.2),
          borderRadius: BorderRadius.circular(3.px),
        ),
        child: Center(
            child: Text(address,
                style: TextStyle(
                  fontSize: 12.px,
                  color: const Color.fromRGBO(255, 147, 6, 1),
                  fontWeight: FontWeight.w500,
                ))));
  }

  Widget _widgetPayButton() {
    return BdhTextButton(
      width: 80.px,
      height: 24.px,
      text: '查看流程',
      textFontWeight: FontWeight.w500,
      textSize: 13.px,
      borderRadius: BorderRadius.all(Radius.circular(22.px)),
      backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
      disableBackgroundColor: const Color.fromRGBO(30, 192, 106, 1),
      pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: () {
        onClickProcess.call(item);
      },
    );
  }
}
