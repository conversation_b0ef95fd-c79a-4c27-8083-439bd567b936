//申请记录 item
import 'dart:math';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

import '../const_dict.dart';

//审核状态
const _stateList = {
  -1: {
    "text": "未填写",
    "textColor": Color.fromRGBO(254, 105, 58, 1),
    "barColor": [
      Color.fromRGBO(255, 110, 66, 1),
      Color.fromRGBO(248, 78, 21, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(248, 78, 21, 0.1)
  },
  0: {
    "text": "审核中",
    "textColor": Color.fromRGBO(153, 153, 153, 1),
    "barColor": [
      Color.fromRGBO(226, 231, 228, 1),
      Color.fromRGBO(169, 183, 180, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(226, 231, 228, 1)
  },
  1: {
    "text": "审核完成",
    "textColor": Color.fromRGBO(30, 192, 106, 1),
    "barColor": [
      Color.fromRGBO(63, 201, 145, 1),
      Color.fromRGBO(0, 171, 114, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(30, 192, 106, 0.1)
  },
  2: {
    "text": "未通过",
    "textColor": Color.fromRGBO(153, 153, 153, 1),
    "barColor": [
      Color.fromRGBO(226, 231, 228, 1),
      Color.fromRGBO(169, 183, 180, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(226, 231, 228, 1)
  },
};

class PlantingInfoItemWidget extends StatelessWidget {
  final String? plotNo;
  final double? plotArea;
  final String? auditResult;
  final String? auditStatus;
  final num? piId;
  final int index;
  final ValueCallback<int> onClickProcess;
  final ValueCallback<int> onClickEdit;

  const PlantingInfoItemWidget(
      {super.key,
      required this.plotNo,
      required this.plotArea,
      required this.auditResult,
      required this.auditStatus,
      required this.index,
      required this.onClickProcess,
      required this.onClickEdit,
      required this.piId});

  int? get state {
    if (piId == null) {
      return -1;
    } else if (auditStatus == "审核中") {
      return 0;
    } else if (auditStatus == "审核完成") {
      if (auditResult == "审核通过") {
        return 1;
      } else if (auditResult == "审核未通过" || auditResult == "退回") {
        return 2;
      }
    }
    return 0;
  }

  String get buttonText {
    if (state == 0) return "去填写";
    return "去修改";
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        margin:
            EdgeInsets.only(top: 6.px, bottom: 6.px, left: 12.px, right: 12.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.px),
        ),
        child: Row(
          children: [
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: _stateList[state]?["barColor"] as List<Color>? ??
                        const [
                          Color.fromRGBO(226, 231, 228, 1),
                          Color.fromRGBO(169, 183, 180, 1),
                        ]),
                borderRadius: BorderRadius.only(
                    topRight: Radius.circular(4.px),
                    bottomRight: Radius.circular(4.px)),
              ),
              width: 4.px,
              height: 36.px,
            ),
            Expanded(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Row(
                  children: [
                    const Spacer(),
                    Container(
                      height: 24.px,
                      padding: EdgeInsets.only(left: 8.px, right: 8.px),
                      decoration: BoxDecoration(
                        color: _stateList[state]?["stateBackgroundColor"]
                                as Color? ??
                            const Color.fromRGBO(234, 234, 234, 1),
                        borderRadius: BorderRadius.only(
                            topRight: Radius.circular(8.px),
                            bottomLeft: Radius.circular(8.px)),
                      ),
                      child: Center(
                          child: Text(
                        _stateList[state]?["text"] as String? ?? "",
                        style: TextStyle(
                            color: _stateList[state]?["textColor"] as Color? ??
                                const Color.fromRGBO(234, 234, 234, 1),
                            fontWeight: FontWeight.w600,
                            fontSize: 12.px),
                      )),
                    )
                  ],
                ),
                Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SizedBox.square(
                            dimension: 24.px,
                          ),
                          Text(
                            "土地编号:",
                            style: TextStyle(
                                color: const Color.fromRGBO(51, 51, 51, 0.4),
                                fontWeight: FontWeight.w500,
                                fontSize: 16.px),
                          ),
                          SizedBox.square(
                            dimension: 6.px,
                          ),
                          Text(
                            plotNo ?? "",
                            style: TextStyle(
                                color: const Color.fromRGBO(51, 51, 51, 1),
                                fontWeight: FontWeight.w500,
                                fontSize: 16.px),
                          ),
                        ],
                      ),
                      SizedBox.square(
                        dimension: 4.px,
                      ),
                      Row(
                        children: [
                          SizedBox.square(
                            dimension: 24.px,
                          ),
                          Text(
                            "地块大小:",
                            style: TextStyle(
                                color: const Color.fromRGBO(51, 51, 51, 0.4),
                                fontWeight: FontWeight.w500,
                                fontSize: 16.px),
                          ),
                          SizedBox.square(
                            dimension: 6.px,
                          ),
                          Text.rich(
                            TextSpan(children: [
                              TextSpan(text: "$plotArea"),
                              const TextSpan(text: " 亩")
                            ]),
                            style: TextStyle(
                                color: const Color.fromRGBO(51, 51, 51, 1),
                                fontWeight: FontWeight.w500,
                                fontSize: 16.px),
                          ),
                        ],
                      ),
                      SizedBox.square(
                        dimension: 4.px,
                      ),
                      Row(
                        children: [
                          SizedBox.square(
                            dimension: 24.px,
                          ),
                          const Spacer(),
                          if (showFlowButton) ...[
                            _widgetFlowButton(),
                            SizedBox.square(
                              dimension: 12.px,
                            ),
                          ],
                          if (showEditButton) ...[
                            _widgetEditButton(),
                            SizedBox.square(
                              dimension: 12.px,
                            ),
                          ]
                        ],
                      ),
                      SizedBox.square(
                        dimension: 12.px,
                      ),
                    ],
                  )),
                ]),
              ],
            ))
          ],
        ));
  }

  bool get showFlowButton {
    return false;
  }

  bool get showEditButton {
    return true;
  }

  Widget _widgetEditButton() {
    return BdhTextButton(
      width: 80.px,
      height: 24.px,
      text: buttonText,
      textFontWeight: FontWeight.w500,
      textSize: 13.px,
      borderRadius: BorderRadius.all(Radius.circular(22.px)),
      backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
      disableBackgroundColor: const Color.fromRGBO(30, 192, 106, 1),
      pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: () {
        onClickEdit.call(index);
      },
    );
  }

  Widget _widgetFlowButton() {
    return BdhTextButton(
      width: 80.px,
      height: 24.px,
      text: '查看流程',
      textFontWeight: FontWeight.w500,
      textSize: 13.px,
      borderRadius: BorderRadius.all(Radius.circular(22.px)),
      backgroundColor: const Color.fromRGBO(254, 105, 58, 1),
      disableBackgroundColor: const Color.fromRGBO(254, 105, 58, 1),
      pressedBackgroundColor: const Color.fromRGBO(254, 105, 58, 1),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: () {
        onClickProcess.call(index);
      },
    );
  }
}
