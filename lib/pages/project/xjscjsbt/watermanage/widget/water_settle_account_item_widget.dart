//水费账户 item

import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/model/water_manage_index_model.dart';

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';

import 'package:flutter/material.dart';
import '../const_dict.dart';

class WaterSettleAccountItemWidget extends StatelessWidget {
  final MyAcc item;
  final int index;
  final bool checked;
  final IndexValueChanged<bool> onCheck;
  const WaterSettleAccountItemWidget(
      {super.key,
      required this.item,
      required this.index,
      required this.onCheck,
      required this.checked});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          onCheck.call(index, !checked);
        },
        child: Container(
            margin: EdgeInsets.only(top: 12.px, left: 12.px, right: 12.px),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.px),
            ),
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Color.fromRGBO(63, 201, 145, 1),
                          Color.fromRGBO(0, 171, 114, 1),
                        ]),
                    borderRadius: BorderRadius.only(
                        topRight: Radius.circular(4.px),
                        bottomRight: Radius.circular(4.px)),
                  ),
                  width: 4.px,
                  height: 36.px,
                ),
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                        padding: EdgeInsets.only(
                            top: 12.px,
                            left: 12.px,
                            right: 6.px,
                            bottom: 12.px),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            _widgetTitle(),
                            SizedBox(
                              height: 4.px,
                            ),
                            _widgetSummary(),
                          ],
                        )),
                  ],
                ))
              ],
            )));
  }

  //标题
  Widget _widgetTitle() {
    return Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
      SizedBox(
        width: 10.px,
      ),
      BdhRadio(
        iconSize: 16.px,
        checked: checked,
        onCheckBoxChanged: (selected) {
          onCheck.call(index, selected);
        },
      ),
      SizedBox(
        width: 4.px,
      ),
      Expanded(
          child: Text("${item.growerName}  $idNumberHide",
              strutStyle: StrutStyle(fontSize: 19.px),
              style: TextStyle(
                  fontSize: 14.px,
                  color: const Color.fromRGBO(0, 0, 0, 1),
                  fontWeight: FontWeight.w500))),
    ]);
  }

  //副标题
  Widget _widgetSummary() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 12.px,
        ),
        Expanded(
            child: Text.rich(
                TextSpan(children: [
                  const TextSpan(
                      text: "账户余额: ",
                      style: TextStyle(
                        color: Color.fromRGBO(0, 0, 0, 0.4),
                      )),
                  const TextSpan(text: "¥"),
                  TextSpan(text: "${item.accBalance}"),
                ]),
                style: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w500,
                ))),
        Text(phoneHide,
            strutStyle: StrutStyle(fontSize: 19.px),
            style: TextStyle(
                fontSize: 14.px,
                color: const Color.fromRGBO(0, 0, 0, 1),
                fontWeight: FontWeight.w500))
      ],
    );
  }

  String get phoneHide {
    if (item.operatorPhone == null) {
      return "-";
    }
    String masked =
        item.operatorPhone!.replaceFirst(RegExp(r'\d{4}'), '****', 3);
    return masked;
  }

  String get idNumberHide {
    var idNumber = item.growerType == "企业" ? item.operatorIdNum : item.idNumber;
    if (idNumber == null) {
      return "-";
    }
    int length = idNumber!.length;
    int start = 3;
    int maskCount = length - 8;
    if (length <= 8) {
      start = 1;
      maskCount = length - 2;
    }
    String star = List.filled(maskCount, "*").fold("", (a, b) => "$a$b");
    String masked =
        item.idNumber!.replaceFirst(RegExp("\\d{$maskCount}"), star, start);
    return masked;
  }
}
