//结果确认提示框
import 'package:bdh_smart_agric_app/components/bdh_dialog.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class FlowNoticeAlert extends StatelessWidget {
  final String text;
  const FlowNoticeAlert({
    super.key,
    required this.text,
  });

  @override
  Widget build(BuildContext context) {
    return BdhSimpleAlertDialog(
      scrollable: true,
      backgroundDecoration: const BoxDecoration(),
      contentPadding: const EdgeInsets.all(0),
      content: SizedBox(
          width: 278.px,
          height: 295.px,
          child: Stack(
            fit: StackFit.expand,
            children: [
              Positioned(
                  bottom: 0,
                  child: SvgPicture.asset(
                      width: 278.px,
                      height: 257.px,
                      "assets/images/bg_dialog.svg")),
              Positioned(
                  top: 0,
                  right: 18.px,
                  child: Image.asset(
                      width: 104.5.px,
                      height: 133.5.px,
                      ImageHelper.wrapAssets("pic_book.png"))),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    height: 151.px,
                  ),
                  Expanded(
                      child: Padding(
                    padding: EdgeInsets.only(
                        left: 20.px, right: 20.px, bottom: 20.px),
                    child: Center(
                      child: Text(
                          textAlign: TextAlign.center,
                          text,
                          style: TextStyle(
                              fontSize: 14.px,
                              decoration: TextDecoration.none,
                              fontWeight: FontWeight.w500)),
                    ),
                  )),
                  Padding(
                      padding: EdgeInsets.only(
                          bottom: 20.px, left: 20.px, right: 20.px),
                      child: SizedBox(
                          height: 40.px, child: _widgetBottom(context))),
                ],
              )
            ],
          )),
    );
  }

  Widget _widgetBottom(BuildContext context) {
    return Row(
      children: [
        Expanded(
            child: TextButton(
          style: ButtonStyle(
              shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(9.px))),
              backgroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.disabled)) {
                  return const Color.fromRGBO(1, 1, 1, 0.02);
                } else if (states.contains(WidgetState.pressed)) {
                  return const Color.fromRGBO(1, 1, 1, 0.02);
                } else {
                  return Colors.transparent;
                }
              }),
              side: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.disabled)) {
                  return const BorderSide(
                    width: 0,
                    color: Color.fromRGBO(1, 1, 1, 0.1),
                  );
                } else if (states.contains(WidgetState.pressed)) {
                  return BorderSide(
                    width: 1.5.px,
                    color: const Color.fromRGBO(11, 182, 130, 0.8),
                  );
                } else {
                  return BorderSide(
                    width: 1.5.px,
                    color: const Color.fromRGBO(11, 182, 130, 1),
                  );
                }
              }),
              foregroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.disabled)) {
                  return Colors.white;
                } else if (states.contains(WidgetState.pressed)) {
                  return const Color.fromRGBO(11, 182, 130, 0.8);
                } else {
                  return const Color.fromRGBO(11, 182, 130, 1);
                }
              }),
              overlayColor: WidgetStateProperty.all(Colors.transparent)),
          onPressed: () {
            Navigator.maybeOf(context)?.pop(false);
          },
          child: Text(
            "暂不处理",
            style: TextStyle(
                fontSize: 15.px,
                decoration: TextDecoration.none,
                fontWeight: FontWeight.w500),
          ),
        )),
        SizedBox(
          width: 8.px,
        ),
        Expanded(
            child: TextButton(
          style: ButtonStyle(
              shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(9.px))),
              backgroundColor: WidgetStateProperty.resolveWith((states) {
                if (states.contains(WidgetState.disabled)) {
                  return const Color.fromRGBO(1, 1, 1, 0.1);
                } else if (states.contains(WidgetState.pressed)) {
                  return const Color.fromRGBO(0, 177, 117, 0.8);
                } else {
                  return const Color.fromRGBO(0, 177, 117, 1);
                }
              }),
              overlayColor: WidgetStateProperty.all(Colors.transparent)),
          onPressed: () {
            Navigator.maybeOf(context)?.pop(true);
          },
          child: Text(
            "去处理",
            style: TextStyle(
                fontSize: 14.px,
                decoration: TextDecoration.none,
                color: Colors.white),
          ),
        )),
      ],
    );
  }
}

Future<bool?> showNoticeAlertDialog(BuildContext context,
    {required String text}) {
  return showDialog<bool>(
    context: context,
    barrierDismissible: true,
    builder: (BuildContext dialogContext) {
      return FlowNoticeAlert(
        text: text,
      );
    },
  );
}
