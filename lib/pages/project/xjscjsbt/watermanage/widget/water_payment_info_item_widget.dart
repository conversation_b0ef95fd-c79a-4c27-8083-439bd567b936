//水费账户 item

import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../const_dict.dart';
import '../model/water_payment_record_model.dart';

class WaterPaymentInfoItemWidget extends StatelessWidget {
  final PaymentRecordItem item;
  final ValueCallback<PaymentRecordItem> onClick;

  const WaterPaymentInfoItemWidget({
    super.key,
    required this.onClick,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          onClick.call(item);
        },
        child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                    padding: EdgeInsets.only(
                        top: 12.px, left: 12.px, right: 6.px, bottom: 12.px),
                    decoration: BoxDecoration(
                      border: Border(
                          bottom: BorderSide(
                              width: 1.px,
                              color: const Color.fromRGBO(0, 0, 0, 0.05))),
                    ),
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          _widgetImage(),
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox.square(
                                      dimension: 10.px,
                                    ),
                                    Expanded(child: _widgetTitle()),
                                    _widgetPayType(),
                                    SizedBox.square(
                                      dimension: 6.px,
                                    ),
                                  ]),
                              SizedBox.square(
                                dimension: 4.px,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox.square(
                                    dimension: 12.px,
                                  ),
                                  Expanded(child: _widgetSummary()),
                                  _widgetMoney(),
                                  SizedBox.square(
                                    dimension: 6.px,
                                  ),
                                ],
                              ),
                            ],
                          )),
                        ])),
              ],
            )));
  }

  //图片
  Widget _widgetImage() {
    return SvgPicture.asset(
        alignment: Alignment.center,
        fit: BoxFit.cover,
        width: 48.px,
        ImageHelper.wrapAssets("ic_water_account.svg"));
  }

  //标题
  Widget _widgetTitle() {
    return Text("${item.growerName ?? "-"} | ${item.orgName ?? "-"}",
        strutStyle: StrutStyle(fontSize: 19.px),
        style: TextStyle(
            fontSize: 14.px,
            color: const Color.fromRGBO(0, 0, 0, 1),
            fontWeight: FontWeight.w600));
  }

  //缴费类型
  Widget _widgetPayType() {
    return Text("${item.chargeChannel}${item.recordType}",
        strutStyle: StrutStyle(fontSize: 19.px),
        style: TextStyle(
            fontSize: 12.px,
            color: item.recordType == 1
                ? const Color.fromRGBO(30, 192, 106, 1)
                : Colors.blue,
            fontWeight: FontWeight.w500));
  }

  //缴费金额
  Widget _widgetMoney() {
    return Text.rich(
        TextSpan(children: [
          const TextSpan(text: "¥"),
          TextSpan(text: "${item.amount ?? "-"}"),
        ]),
        style: TextStyle(
            fontSize: 14.px,
            color: const Color.fromRGBO(0, 0, 0, 1),
            fontWeight: FontWeight.w500));
  }

  //副标题
  Widget _widgetSummary() {
    return Text(createTime,
        style: TextStyle(
          color: const Color.fromRGBO(0, 0, 0, 0.4),
          fontSize: 12.px,
          fontWeight: FontWeight.w500,
        ));
  }

  String get createTime {
    if (item.createTime == null) {
      return "-";
    }
    int timestampInMilliseconds = item.createTime!; // 例如：2021-04-01T00:00:00Z

    // 将时间戳转换为DateTime对象
    DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch(timestampInMilliseconds);

    // 创建DateFormat对象并指定格式
    // 例如：2021-04-01 00:00:00

    // 格式化日期时间
    return timeFormat.format(dateTime);
  }
}
