import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

import '../const_dict.dart';
import '../model/water_distribution_Item_model.dart';

//审核状态
const _stateList = {
  "处理中": {
    "text": "未放水",
    "textColor": Color.fromRGBO(254, 105, 58, 1),
    "barColor": [
      Color.fromRGBO(255, 110, 66, 1),
      Color.fromRGBO(248, 78, 21, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(248, 78, 21, 0.1)
  },
  "已放水": {
    "text": "已放水",
    "textColor": Color.fromRGBO(30, 192, 106, 1),
    "barColor": [
      Color.fromRGBO(63, 201, 145, 1),
      Color.fromRGBO(0, 171, 114, 1),
    ],
    "stateBackgroundColor": Color.fromRGBO(30, 192, 106, 0.1)
  },
};

class WaterDistributionItemWidget extends StatelessWidget {
  final bool checked;
  final bool checkable;
  final int index;
  final WaterDistributionItem item;
  final ValueCallback<WaterDistributionItem>? onClick;
  final ValueCallback<WaterDistributionItem>? onClickFallback;
  final ValueCallback<WaterDistributionItem>? onClickReject;
  final ValueCallback<WaterDistributionItem>? onClickApproval;
  final ValueCallback<WaterDistributionItem>? onClickFlow;

  final IndexValueChanged<bool> onCheck;
  const WaterDistributionItemWidget(
      {super.key,
      required this.checked,
      required this.index,
      required this.item,
      required this.onClick,
      this.onClickReject,
      this.onClickApproval,
      required this.checkable,
      required this.onCheck,
      this.onClickFallback,
      this.onClickFlow});

  String get titleText {
    var text = "";

    if ((item.mainCanal?.isEmpty ?? true) &&
        (item.branchCanal?.isEmpty ?? true) &&
        (item.lateralCanal?.isEmpty ?? true)) {
      text = "${item.distributorName ?? "-"}-${item.waterUsage ?? "-"}m³";
    } else {
      var mainCanal =
          (item.mainCanal?.isNotEmpty ?? false) ? item.mainCanal : "-";
      var branchCanal =
          (item.mainCanal?.isNotEmpty ?? false) ? item.branchCanal : "-";
      var lateralCanal =
          (item.mainCanal?.isNotEmpty ?? false) ? item.lateralCanal : "-";
      text = "${item.orgName} $mainCanal干$branchCanal支$lateralCanal斗";
    }

    return text;
  }

  @override
  Widget build(BuildContext context) {
    Widget titleWidget = Text(
      titleText,
      style: TextStyle(
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w600,
          fontSize: 16.px),
    );

    if (checkable) {
      titleWidget = GestureDetector(
        onTap: () {
          onCheck.call(index, !checked);
        },
        child: titleWidget,
      );
    }

    return GestureDetector(
        onTap: () {
          onClick?.call(item);
        },
        child: Container(
            margin: EdgeInsets.only(
                top: 5.px, bottom: 5.px, left: 12.px, right: 12.px),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.px),
            ),
            child: Column(
              children: [
                Container(
                    decoration: hasBottom
                        ? BoxDecoration(
                            border: Border(
                                bottom: BorderSide(
                                    width: 1.px,
                                    color:
                                        const Color.fromRGBO(0, 0, 0, 0.05))),
                          )
                        : null,
                    child: Row(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                colors: _stateList[item.releaseStatus]
                                        ?["barColor"] as List<Color>? ??
                                    const [
                                      Color.fromRGBO(226, 231, 228, 1),
                                      Color.fromRGBO(169, 183, 180, 1),
                                    ]),
                            borderRadius: BorderRadius.only(
                                topRight: Radius.circular(4.px),
                                bottomRight: Radius.circular(4.px)),
                          ),
                          width: 4.px,
                          height: 36.px,
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                height: 16.px,
                              ),
                              Row(
                                children: [
                                  SizedBox.square(
                                    dimension: 12.px,
                                  ),
                                  if (checkable) ...[
                                    BdhRadio(
                                      iconSize: 16.px,
                                      checked: checked,
                                      onCheckBoxChanged: (selected) {
                                        onCheck.call(index, selected);
                                      },
                                    ),
                                    SizedBox.square(
                                      dimension: 3.px,
                                    )
                                  ],
                                  titleWidget,
                                ],
                              ),
                              SizedBox.square(
                                dimension: 13.px,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  SizedBox.square(
                                    dimension: 16.px,
                                  ),
                                  Text(
                                    "申请放水日期:",
                                    textAlign: TextAlign.justify,
                                    style: TextStyle(
                                        color: const Color.fromRGBO(
                                            51, 51, 51, 0.4),
                                        fontWeight: FontWeight.w500,
                                        fontSize: 12.px),
                                  ),
                                  SizedBox.square(
                                    dimension: 6.px,
                                  ),
                                  Text(
                                    "${item.usageBeginDate} 至 ${item.usageEndDate}",
                                    style: TextStyle(
                                        color:
                                            const Color.fromRGBO(51, 51, 51, 1),
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12.px),
                                  ),
                                  SizedBox.square(
                                    dimension: 12.px,
                                  ),
                                ],
                              ),
                              SizedBox.square(
                                dimension: 4.px,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  SizedBox.square(
                                    dimension: 16.px,
                                  ),
                                  Flexible(
                                      fit: FlexFit.tight,
                                      flex: 1,
                                      child: Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          SizedBox(
                                              width: 70.px,
                                              child: Text(
                                                "申请用水量:",
                                                textAlign: TextAlign.justify,
                                                style: TextStyle(
                                                    color: const Color.fromRGBO(
                                                        51, 51, 51, 0.4),
                                                    fontWeight: FontWeight.w500,
                                                    fontSize: 12.px),
                                              )),
                                          SizedBox.square(
                                            dimension: 6.px,
                                          ),
                                          Text(
                                            "${item.waterUsage ?? "-"}m³",
                                            style: TextStyle(
                                                color: const Color.fromRGBO(
                                                    51, 51, 51, 1),
                                                fontWeight: FontWeight.w600,
                                                fontSize: 12.px),
                                          ),
                                          SizedBox.square(
                                            dimension: 12.px,
                                          ),
                                          Text(
                                            "提交时间:",
                                            textAlign: TextAlign.justify,
                                            style: TextStyle(
                                                color: const Color.fromRGBO(
                                                    51, 51, 51, 0.4),
                                                fontWeight: FontWeight.w500,
                                                fontSize: 12.px),
                                          ),
                                          SizedBox.square(
                                            dimension: 6.px,
                                          ),
                                          Text(
                                            "${item.createTime}",
                                            style: TextStyle(
                                                color: const Color.fromRGBO(
                                                    51, 51, 51, 1),
                                                fontWeight: FontWeight.w600,
                                                fontSize: 12.px),
                                          ),
                                        ],
                                      )),
                                ],
                              ),
                              SizedBox.square(
                                dimension: 4.px,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  SizedBox.square(
                                    dimension: 16.px,
                                  ),
                                  Text(
                                    "配水员:",
                                    textAlign: TextAlign.justify,
                                    style: TextStyle(
                                        color: const Color.fromRGBO(
                                            51, 51, 51, 0.4),
                                        fontWeight: FontWeight.w500,
                                        fontSize: 12.px),
                                  ),
                                  SizedBox.square(
                                    dimension: 6.px,
                                  ),
                                  Text(
                                    item.distributorName ?? "-",
                                    style: TextStyle(
                                        color:
                                            const Color.fromRGBO(51, 51, 51, 1),
                                        fontWeight: FontWeight.w600,
                                        fontSize: 12.px),
                                  ),
                                  SizedBox.square(
                                    dimension: 12.px,
                                  ),
                                ],
                              ),
                              SizedBox.square(
                                dimension: 12.px,
                              ),
                            ],
                          ),
                        )
                      ],
                    )),
                if (hasBottom) ...[
                  SizedBox.square(
                    dimension: 6.px,
                  ),
                  _widgetBottom(),
                  SizedBox.square(
                    dimension: 6.px,
                  ),
                ]
              ],
            )));
  }

  bool get hasBottom {
    if (onClickFlow != null ||
        onClickFallback != null ||
        onClickReject != null ||
        onClickApproval != null) {
      return true;
    }
    return false;
  }

  Widget _widgetBottom() {
    return Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
      const Spacer(),
      if (onClickFlow != null) ...[
        SizedBox.square(
          dimension: 6.px,
        ),
        _widgetButton("查看流程", onPressed: () {
          onClickFlow?.call(item);
        })
      ],
      if (onClickReject != null) ...[
        SizedBox.square(
          dimension: 6.px,
        ),
        _widgetButton("驳回", onPressed: () {
          onClickReject?.call(item);
        })
      ],
      if (onClickFallback != null) ...[
        SizedBox.square(
          dimension: 6.px,
        ),
        _widgetButton("退回", onPressed: () {
          onClickFallback?.call(item);
        })
      ],
      if (onClickApproval != null) ...[
        SizedBox.square(
          dimension: 6.px,
        ),
        _widgetButton("审核", onPressed: () {
          onClickApproval?.call(item);
        })
      ],
      SizedBox.square(
        dimension: 12.px,
      ),
    ]);
  }

  Widget _widgetButton(String text, {VoidCallback? onPressed}) {
    return BdhTextButton(
      width: 72.px,
      height: 26.px,
      text: text,
      textSize: 12.px,
      textFontWeight: FontWeight.w500,
      borderRadius: BorderRadius.all(Radius.circular(13.px)),
      side: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(30, 192, 106, 1),
      ),
      pressedSide: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(30, 192, 106, 1),
      ),
      disableSide: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(1, 1, 1, 0.1),
      ),
      onPressed: onPressed,
    );
  }
}
