import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:oktoast/oktoast.dart';

class SearchAppBar extends StatefulWidget implements PreferredSizeWidget {
  @override
  final Size preferredSize;
  final VoidCallback onBackClick;

  final ValueChanged<String> onSearch;
  const SearchAppBar(
      {super.key, required this.onBackClick, required this.onSearch})
      : preferredSize = const Size.fromHeight(kTextTabBarHeight);

  @override
  State<SearchAppBar> createState() => _SearchAppBarState();
}

class _SearchAppBarState extends State<SearchAppBar> {
  final TextEditingController _searchTextEditingController =
      TextEditingController();
  final FocusScopeNode _searchFocusNode = FocusScopeNode();

  bool _showDeleteIcon = false;

  @override
  void initState() {
    super.initState();

    _searchTextEditingController.addListener(_textChangeListener);
  }

  @override
  void dispose() {
    super.dispose();
    _searchTextEditingController.dispose();
  }

  void _textChangeListener() {
    if (_searchTextEditingController.text.isEmpty) {
      setState(() {
        _showDeleteIcon = false;
      });
    } else {
      setState(() {
        _showDeleteIcon = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppBar(
      toolbarHeight: kTextTabBarHeight,
      leading: BackButton(onPressed: widget.onBackClick),
      actions: [
        GestureDetector(
          onTap: _onEditComplete,
          child: SizedBox(
              height: 24.px,
              child: Text(
                '搜索',
                style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: const Color.fromRGBO(30, 192, 106, 1)),
              )),
        ),
        SizedBox(
          width: 15.px,
        ),
      ],
      title: _widgetSearch(),
    );
  }

  void _onEditComplete() {
    if (_searchTextEditingController.text.isEmpty) {
      showToast('请输入搜索内容');
      return;
    }
    _searchFocusNode.unfocus();
  }

  void _onClean() {
    _searchTextEditingController.text = '';
    _searchFocusNode.unfocus();
  }

  Widget _widgetSearch() {
    return Container(
      padding: EdgeInsets.only(left: 5.px, right: 8.px),
      height: 32.px,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(21.px),
        color: const Color.fromRGBO(241, 245, 243, 1),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
              alignment: Alignment.center,
              fit: BoxFit.cover,
              width: 24.px,
              colorFilter: const ColorFilter.mode(
                  Color.fromRGBO(210, 210, 210, 1), BlendMode.srcIn),
              ImageHelper.wrapAssets("ic_search.svg")),
          SizedBox(
            width: 5.px,
          ),
          Expanded(
            child: CupertinoTextField.borderless(
              style: TextStyle(
                fontSize: 13.px,
                color: Colors.black,
                fontWeight: FontWeight.w400,
              ),
              textInputAction: TextInputAction.search,
              padding: EdgeInsets.zero,
              controller: _searchTextEditingController,
              placeholder: "请输入..",
              placeholderStyle: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w500,
                  color: const Color.fromRGBO(210, 210, 210, 1)),
              focusNode: _searchFocusNode,
              onEditingComplete: _onEditComplete,
            ),
          ),
          SizedBox(
            width: 5.px,
          ),
          _showDeleteIcon
              ? GestureDetector(
                  onTap: _onClean,
                  child: Image.asset(
                      width: 16.px,
                      height: 16.px,
                      ImageHelper.wrapAssets('delteIcons.png')),
                )
              : Container(),
        ],
      ),
    );
  }
}
