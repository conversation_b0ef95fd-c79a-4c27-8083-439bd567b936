import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/request/xinjiang_water_manage_service.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import '../const_dict.dart';

class PricePredict extends StatefulWidget {
  final num? yearNo;
  final String? orgCode;
  final String? cropCode;
  final String? waterCons;
  final String? plotNo;
  final num? plotArea;
  final num? accId;
  final num? waId;
  final ValueCallback<List?>? onPriceChange;
  const PricePredict(
      {super.key,
      this.yearNo,
      this.orgCode,
      this.cropCode,
      this.waterCons,
      this.plotNo,
      this.plotArea,
      this.accId,
      this.onPriceChange,
      this.waId});

  @override
  State<PricePredict> createState() => _PricePredictState();
}

class _PricePredictState extends State<PricePredict>
    with AutoDisposeStateMixin {
  String? price;
  bool _isLoading = false;
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _loadData();
    });
  }

  void _loadData() {
    if (_isLoading) {
      return;
    }

    if (widget.waterCons == null ||
        widget.waterCons!.isEmpty ||
        widget.plotNo == null) {
      setState(() {
        _isLoading = false;
        price = null;
      });
      widget.onPriceChange?.call(null);
      return;
    }

    try {
      var d = double.parse(widget.waterCons!);
      if (d <= 0) {
        setState(() {
          _isLoading = false;
          price = null;
        });
        widget.onPriceChange?.call(null);
        return;
      }
    } catch (error, stackTrace) {
      Log.e("", error: error, stackTrace: stackTrace);
      setState(() {
        _isLoading = false;
        price = null;
      });
      widget.onPriceChange?.call(null);
      return;
    }

    _isLoading = true;
    setState(() {
      _isLoading = true;
      price = null;
      widget.onPriceChange?.call(null);
    });

    var data = {
      "yearNo": DateTime.now().year,
      "orgCode": widget.orgCode,
      "cropCode": widget.cropCode,
      "plotNo": widget.plotNo,
      "plotArea": widget.plotArea,
      "accId": widget.accId,
      "waterCons": widget.waterCons,
      "waId": widget.waId
    };
    Log.d("data is $data");
    XinJiangWaterManageService()
        .estimatePrice(data, cancelToken: useCancelToken(CancelToken()))
        .then((result) {
      if (!disposed && context.mounted) {
        var data = (result.data as List?);
        widget.onPriceChange?.call(data);
        setState(() {
          _isLoading = false;
          price = data?.fold<double>(0, (k, v) {
            return k + (v["amount"] ?? 0);
          }).toStringAsFixed(2);
        });
      }
    }).onError(_handlerError);
  }

  Null _handlerError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (disposed || !context.mounted) {
      return;
    }
    errorDo?.call();

    showToast(request.message ?? "预估水价失败,请稍后再试");
    setState(() {
      _isLoading = false;
      price = null;
    });
  }

  @override
  Widget build(BuildContext context) {
    var children = <Widget>[
      Text("预计水价: ",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500,
              fontSize: 16.px))
    ];

    if (_isLoading) {
      children.add(SizedBox(
        width: 12.px,
        height: 12.px,
        child: const CircularProgressIndicator(),
      ));
    } else {
      children.add(Text(
        "${price ?? "-"}元",
        style: TextStyle(
            color: const Color.fromRGBO(30, 192, 106, 1),
            fontWeight: FontWeight.w500,
            fontSize: 16.px),
      ));
    }

    return Row(
      children: children,
    );
  }
}
