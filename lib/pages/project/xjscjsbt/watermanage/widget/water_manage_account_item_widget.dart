//水费账户 item
import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/model/water_manage_index_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../const_dict.dart';

class WaterManageAccountItemWidget extends StatelessWidget {
  final MyAcc item;
  final ValueCallback<MyAcc> onClickRecharge;
  final ValueCallback<MyAcc> onClickUseWater;

  const WaterManageAccountItemWidget(
      {super.key,
      required this.item,
      required this.onClickRecharge,
      required this.onClickUseWater});

  bool get showPayButton {
    return item.growerType == "企业" ? false : true;
  }

  bool get showApplyButton {
    return item.busMode == "结算模式" ? false : true;
  }

  bool get showButton {
    return showPayButton || showApplyButton;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          if (showApplyButton) {
            onClickUseWater.call(item);
          } else if (showPayButton) {
            onClickRecharge.call(item);
          }
          // onClickUseWater.call(item);
        },
        child: Container(
            margin: EdgeInsets.only(
                top: 6.px, bottom: 6.px, left: 12.px, right: 12.px),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.px),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                    padding: EdgeInsets.only(
                        top: 12.px, left: 12.px, right: 6.px, bottom: 12.px),
                    decoration: showButton
                        ? BoxDecoration(
                            border: Border(
                                bottom: BorderSide(
                                    width: 1.px,
                                    color:
                                        const Color.fromRGBO(0, 0, 0, 0.05))),
                          )
                        : null,
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          _widgetImage(),
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox.square(
                                      dimension: 10.px,
                                    ),
                                    Expanded(child: _widgetTitle()),
                                  ]),
                              SizedBox.square(
                                dimension: 4.px,
                              ),
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox.square(
                                    dimension: 12.px,
                                  ),
                                  Expanded(child: _widgetSummary())
                                ],
                              ),
                              SizedBox.square(
                                dimension: 4.px,
                              ),
                            ],
                          )),
                          if (showButton)
                            Icon(
                              Icons.arrow_forward_ios,
                              color: const Color.fromRGBO(0, 0, 0, 0.1),
                              size: 24.px,
                            )
                        ])),
                if (showButton) ...[
                  SizedBox.square(
                    dimension: 10.px,
                  ),
                  Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
                    const Spacer(),
                    if (showPayButton) ...[
                      _widgetPayButton(),
                      SizedBox.square(
                        dimension: 10.px,
                      )
                    ],
                    if (showApplyButton) ...[
                      _widgetApplyButton(),
                      SizedBox.square(
                        dimension: 10.px,
                      )
                    ],
                  ]),
                  SizedBox.square(
                    dimension: 10.px,
                  ),
                ],
              ],
            )));
  }

  //图片
  Widget _widgetImage() {
    return SvgPicture.asset(
        alignment: Alignment.center,
        fit: BoxFit.cover,
        width: 48.px,
        ImageHelper.wrapAssets("ic_water_account.svg"));
  }

  //标题
  Widget _widgetTitle() {
    return Text("${item.orgName} | ${item.growerName}",
        strutStyle: StrutStyle(fontSize: 19.px),
        style: TextStyle(
            fontSize: 14.px,
            color: const Color.fromRGBO(0, 0, 0, 1),
            fontWeight: FontWeight.w600));
  }

  //副标题
  Widget _widgetSummary() {
    return Text.rich(
        TextSpan(children: [
          const TextSpan(
              text: "余额: ",
              style: TextStyle(
                color: Color.fromRGBO(0, 0, 0, 0.4),
              )),
          const TextSpan(text: "¥"),
          TextSpan(text: "${item.accBalance}"),
        ]),
        style: TextStyle(
          fontSize: 12.px,
          fontWeight: FontWeight.w600,
        ));
  }

  Widget _widgetPayButton() {
    return BdhTextButton(
      width: 72.px,
      height: 26.px,
      text: '账户充值',
      textSize: 12.px,
      textFontWeight: FontWeight.w500,
      borderRadius: BorderRadius.all(Radius.circular(13.px)),
      side: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(30, 192, 106, 1),
      ),
      pressedSide: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(30, 192, 106, 1),
      ),
      disableSide: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(1, 1, 1, 0.1),
      ),
      onPressed: () {
        onClickRecharge.call(item);
      },
    );
  }

  Widget _widgetApplyButton() {
    return BdhTextButton(
      width: 72.px,
      height: 26.px,
      text: '申请用水',
      textSize: 12.px,
      textFontWeight: FontWeight.w500,
      borderRadius: BorderRadius.all(Radius.circular(13.px)),
      side: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(30, 192, 106, 1),
      ),
      pressedSide: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(30, 192, 106, 1),
      ),
      disableSide: BorderSide(
        width: 1.px,
        color: const Color.fromRGBO(1, 1, 1, 0.1),
      ),
      onPressed: () {
        onClickUseWater.call(item);
      },
    );
  }
}
