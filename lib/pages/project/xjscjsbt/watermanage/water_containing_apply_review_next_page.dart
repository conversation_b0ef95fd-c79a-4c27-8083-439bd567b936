import 'dart:io';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_time_picker.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/debounce_throttle_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import '../../../../utils/dialog_extensions.dart';
import 'const_dict.dart';
import 'model/water_apply_detail_model.dart';
import 'model/water_apply_item_model.dart';
import 'request/xinjiang_water_manage_service.dart';
import 'widget/bdh_steps2.dart';
import 'widget/notice_alert_dialog.dart';
import 'widget/price_predict.dart';

//接水管理-用水申请-审核-下一步
class WaterContainingApplyReviewNextPage extends StatefulWidget {
  final WaterApplyItem item;
  const WaterContainingApplyReviewNextPage({super.key, required this.item});

  @override
  State<WaterContainingApplyReviewNextPage> createState() =>
      _WaterContainingApplyReviewNextPageState();
}

class _WaterContainingApplyReviewNextPageState
    extends State<WaterContainingApplyReviewNextPage>
    with AutoDisposeStateMixin, WidgetsBindingObserver {
  late final ScrollController _scrollController;

  late final _Controller controller;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  final GlobalKey _inputKey = GlobalKey();

  late final DebounceValueListener<double> _debounceValueListener;
  bool keyboardVisible = false;

  @override
  void initState() {
    super.initState();

    _scrollController = useScrollController(ScrollController());

    controller = useChangeNotifier(_Controller(context, widget.item))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();

    WidgetsBinding.instance.addObserver(this);
    _debounceValueListener =
        DebounceValueListener<double>(const Duration(milliseconds: 200));
    keyboardVisible = false;
    _debounceValueListener.valueChanged = _viewInsetBottomChange;
  }

  @override
  void dispose() {
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
    _debounceValueListener.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    if (!Platform.isAndroid) {
      return;
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final currentInset = MediaQuery.of(context).viewInsets.bottom;
      _debounceValueListener.add(currentInset);
    });
  }

  void _viewInsetBottomChange(double inset) {
    var visible = false;
    if (inset > 0) {
      visible = true;
    } else {
      visible = false;
    }
    if (visible != keyboardVisible) {
      keyboardVisible = visible;
      if (keyboardVisible && controller._focusNode.hasFocus) {
        final context = _inputKey.currentContext!;
        final renderBox = context.findRenderObject() as RenderBox;
        _scrollController.position.ensureVisible(
          renderBox,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
      if (!keyboardVisible && controller._focusNode.hasFocus) {
        controller._focusNode.unfocus();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("用水审核"),
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: SafeArea(
            top: false,
            child: controller.isLoading
                ? _widgetLoading(context)
                : Stack(
                    children: [
                      _widgetBody(),
                      Positioned(bottom: 0, child: _widgetSubmitButton())
                    ],
                  )));
  }

  Widget _widgetStep() {
    return Container(
        padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: BdhStepsHorizontal(
          steps: waterApplyReviewStepDict,
          activeIndex: 1,
          outerIconSize: 30.px,
          innerIconSize: 24.px,
        ));
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  String get plotArea {
    if (controller.model?.plotArea == null) {
      return "-";
    }
    return "${controller.model?.plotArea}亩";
  }

  Widget _widgetBody() {
    return Column(
      children: [
        _widgetStep(),
        SizedBox(
          height: 10.px,
        ),
        Expanded(child: _widgetList())
      ],
    );
  }

  Widget _widgetList() {
    return Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: Scrollbar(
            controller: _scrollController,
            child: CustomScrollView(
              controller: _scrollController,
              physics: const ClampingScrollPhysics(
                  parent: AlwaysScrollableScrollPhysics()),
              slivers: [
                SliverToBoxAdapter(
                    child: ColoredBox(
                        color: Colors.white,
                        child: Column(
                          children: [
                            SizedBox(
                              height: 10.px,
                            ),
                            _widgetSummary(
                              "农户姓名",
                              controller.model?.growerName ?? "",
                            ),

                            SizedBox(
                              height: 10.px,
                            ),
                            _widgetSummary(
                                "所在单位", controller.model?.orgName ?? ""),
                            SizedBox(
                              height: 10.px,
                            ),
                            Divider(
                              color: const Color.fromRGBO(51, 51, 51, 0.05),
                              height: 1.px,
                              indent: 20.px,
                              endIndent: 20.px,
                            ),

                            SizedBox(
                              height: 10.px,
                            ),
                            _widgetSummary(
                                "土地编号", controller.model?.plotNo ?? "-"),
                            SizedBox(
                              height: 10.px,
                            ),
                            //地块类型：1身份地；2经营地
                            _widgetSummary("类型",
                                "${controller.model?.cropName ?? "-"}  ${controller.model?.plotArea ?? "-"} 亩"),
                            SizedBox(
                              height: 10.px,
                            ),
                            _widgetSummary("申请用水量",
                                "${controller.model?.actualWaterUsage ?? (controller.model?.waterCons ?? "-")} m³"),
                            SizedBox(
                              height: 10.px,
                            ),
                            _widgetSummary(
                                "申请放水日期", controller.model?.usageDate ?? "-"),
                            SizedBox(
                              height: 10.px,
                            ),

                            Divider(
                              color: const Color.fromRGBO(51, 51, 51, 0.05),
                              height: 1.px,
                              indent: 20.px,
                              endIndent: 20.px,
                            ),
                            SizedBox(
                              height: 10.px,
                            ),
                            _widgetSummary("放水次数",
                                "第${controller.model?.waterFreq ?? "-"}次放水"),

                            SizedBox(
                              height: 10.px,
                            ),
                            // Divider(
                            //   color: const Color.fromRGBO(51, 51, 51, 0.05),
                            //   height: 1.px,
                            //   indent: 20.px,
                            //   endIndent: 20.px,
                            // ),
                            // ..._widgetPriceRecords(),
                            // SizedBox(
                            //   height: 10.px,
                            // ),
                            // Divider(
                            //   color: const Color.fromRGBO(51, 51, 51, 0.05),
                            //   height: 1.px,
                            //   indent: 20.px,
                            //   endIndent: 20.px,
                            // ),
                          ],
                        ))),
                SliverPadding(padding: EdgeInsets.only(bottom: 10.px)),
                SliverToBoxAdapter(
                    child: ColoredBox(
                        color: Colors.white,
                        child: Column(children: [
                          _widgetWaterUsageDate(),
                          SizedBox(
                            height: 10.px,
                          ),
                          _widgetWaterUse(),
                          SizedBox(
                            height: 20.px,
                          ),
                        ]))),
                _widgetMoneyDict()
              ],
            )));
  }

  String get usageTimeText {
    return controller.model?.usageBeginDate ??
        controller.model?.actualUsageDate ??
        controller.model?.usageDate ??
        "-";
  }

  Widget _widgetWaterUsageDate() {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        Expanded(
            child: BdhDatePicker(
          item: FormItem(title: "用水日期", isRequired: true),
          initialValue: controller.waterUsageDate,
          checkState: true,
          showBottomLine: true,
          showArrow: true,
          textAlign: TextAlign.right,
          minHeight: 44.px,
          titleWidth: 116.px,
          titleStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w500),
          placeholderStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontWeight: FontWeight.w500),
          textStyle: TextStyle(
              fontSize: 16.px,
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600),
          dateFormat: dateFormat,
          onChanged: (v) {
            controller.waterUsageDate = v;
          },
          onSaved: (v) {
            controller.waterUsageDate = v;
          },
          validator: (v) {
            if (v == null) {
              return "用水日期不能为空";
            }
            var nowDate = DateTime.now();
            nowDate = nowDate.subtract(const Duration(days: 1));
            if (v.isBefore(nowDate)) {
              return "用水日期必须晚于当前时间";
            }
            return null;
          },
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  List<Widget> _widgetPriceRecords() {
    return controller.model?.priceRecords
            ?.asMap()
            .map<int, List<Widget>>((index, item) {
              var list = <Widget>[
                SizedBox(
                  height: 10.px,
                ),
                _widgetSummary3(
                    item.priceLevelDesc ?? priceRecordTitleDict[index],
                    "${item.waterCons}m³",
                    "${item.amount}元")
              ];

              return MapEntry(index, list);
            })
            .values
            .toList()
            .fold<List<Widget>>(<Widget>[],
                (List<Widget> list, List<Widget> item) {
              list.addAll(item);
              return list;
            }) ??
        [];
  }

  Widget _widgetMoneyDict() {
    return SliverPadding(
        padding:
            EdgeInsets.only(left: 8.px, right: 8.px, top: 8.px, bottom: 84.px),
        sliver: SliverGrid.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3, // 列数
                crossAxisSpacing: 8.px, // 交叉轴间距
                mainAxisSpacing: 8.px, // 主轴间距
                mainAxisExtent: 54.px),
            itemCount: waterDict.length,
            itemBuilder: (context, index) {
              var item = waterDict[index];
              bool checked = controller.waterIndexChecked == index;
              return GestureDetector(
                  onTap: () {
                    controller.onClickWaterCheck(index);
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: checked
                          ? const Color.fromRGBO(30, 192, 106, 1)
                          : Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(8.px)),
                    ),
                    height: 54.px,
                    child: Center(
                        child: Text(
                      "${item}m³",
                      style: TextStyle(
                          color: checked ? Colors.white : Colors.black,
                          fontWeight: FontWeight.w500,
                          fontSize: 16.px),
                    )),
                  ));
            }));
  }

  Widget _widgetSummary(String title, String text, {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
          width: 116.px,
          child: Text.rich(TextSpan(children: [
            TextSpan(
              text: title,
              style: TextStyle(
                  color: const Color.fromRGBO(51, 51, 51, 0.4),
                  fontWeight: FontWeight.w500,
                  fontSize: 12.px),
            )
          ])),
        ),
        const Spacer(),
        Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetSummary3(String title, String text, String text2,
      {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
            width: 150.px,
            child: Text.rich(TextSpan(children: [
              TextSpan(
                text: title,
                style: TextStyle(
                    color: const Color.fromRGBO(51, 51, 51, 0.4),
                    fontWeight: FontWeight.w500,
                    fontSize: 12.px),
              )
            ]))),
        Expanded(
            child: Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        )),
        Text(
          text2,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetWaterUse() {
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              width: 20.px,
            ),
            Text.rich(TextSpan(children: [
              TextSpan(
                text: "*",
                style: TextStyle(
                    color: Colors.red,
                    fontSize: 16.px,
                    fontWeight: FontWeight.w500),
              ),
              TextSpan(
                text: "申请用水量",
                style: TextStyle(
                    color: const Color.fromRGBO(51, 51, 51, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 16.px),
              )
            ])),
            const Spacer(),
            PricePredict(
              key: ValueKey(
                  "${controller._editingController.text}-${controller.model?.cropCode}-${controller.model?.plotNo}-${controller.model?.plotArea}"),
              waterCons: controller._editingController.text,
              yearNo: controller.model?.yearNo,
              orgCode: controller.model?.orgCode,
              cropCode: controller.model?.cropCode,
              plotNo: controller.model?.plotNo,
              plotArea: controller.model?.plotArea,
              accId: controller.model?.accId,
              waId: controller.model?.waId,
              onPriceChange: controller.onPricePredictChange,
            ),
            SizedBox(
              width: 20.px,
            ),
          ],
        ),
        SizedBox(
          height: 18.px,
        ),
        Row(
          children: [
            SizedBox(
              width: 20.px,
            ),
            Expanded(
                child: CupertinoTextField.borderless(
              key: _inputKey,
              focusNode: controller._focusNode,
              controller: controller._editingController,
              placeholder: "请输入用水量",
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              prefix: _buildClearButton(),
              prefixMode: OverlayVisibilityMode.editing,
              padding: EdgeInsets.zero,
              style: TextStyle(
                fontSize: 36.px,
                fontWeight: FontWeight.w600,
                fontFamily: "BEBAS",
                color: const Color.fromRGBO(44, 44, 52, 1),
              ),
              placeholderStyle: TextStyle(
                  fontSize: 36.px,
                  fontWeight: FontWeight.w400,
                  fontFamily: "BEBAS",
                  color: const Color.fromRGBO(51, 51, 51, 0.4)),
            )),
            Text(
              "m³",
              style: TextStyle(
                  color: const Color.fromRGBO(51, 51, 51, 1),
                  fontWeight: FontWeight.w600,
                  fontFamily: "BEBAS",
                  fontSize: 20.px),
            ),
            SizedBox(
              width: 20.px,
            ),
          ],
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetSubmitButton() {
    return Padding(
        padding: EdgeInsets.only(left: 24.px, right: 24.px, bottom: 12.px),
        child: BdhTextButton(
          width: 327.px,
          height: 40.px,
          text: '申请用水',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(22.px)),
          backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          disableBackgroundColor: Colors.grey.shade300,
          pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: controller.priceRecords == null ? null : _onSubmit,
        ));
  }

  void _onSubmit() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      String text = controller._editingController.text;
      if (text.isEmpty) {
        showToast("请输入用水量");
        return;
      }

      try {
        double actualWaterUsage = double.parse(text);
        if (actualWaterUsage <= 0) {
          showToast("用水量必须大于 0");
          return;
        }

        showConfirmDialog(context,
                message: "确认提交吗?", cancel: "取消", confirm: "确认")
            .then((result) {
          if (result == true) {
            controller.submit(actualWaterUsage);
          }
        });
      } catch (error, stackTrace) {
        Log.e("actualWaterUsage pause error",
            error: error, stackTrace: stackTrace);
        showToast("请输入正确用水量");
      }
    }
  }

  Widget _buildClearButton() {
    final String clearLabel =
        CupertinoLocalizations.of(context).clearButtonLabel;

    return Semantics(
      button: true,
      label: clearLabel,
      child: GestureDetector(
        onTap: () {
          controller._editingController.clear();
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 6.0),
          child: Icon(
            CupertinoIcons.clear_thick_circled,
            size: 18.0,
            color: CupertinoDynamicColor.resolve(
                const CupertinoDynamicColor.withBrightness(
                  color: Color(0x33000000),
                  darkColor: Color(0x33FFFFFF),
                ),
                context),
          ),
        ),
      ),
    );
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;
  final WaterApplyItem item;

  late final TextEditingController _editingController;

  late FocusNode _focusNode;

  _Controller(this.context, this.item) {
    _focusNode = createFocusNode();
    _editingController = createTextController()..addListener(onTextChange);
  }

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  String? inputWater;

  int? waterIndexChecked;

  //加载数据
  WaterApplyDetailModel? model;

  DateTime? _waterUsageDate;
  DateTime? get waterUsageDate => _waterUsageDate;
  set waterUsageDate(DateTime? date) {
    _waterUsageDate = date;
    notifyListeners();
  }

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    XinJiangWaterManageService()
        .waterHandleInfo(item.waId, cancelToken: createCancelToken())
        .then((result) {
      model = WaterApplyDetailModel.fromJson(result.data);
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  Null _handlerError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  void onClickWaterCheck(int index) {
    waterIndexChecked = index;

    _editingController.text = waterDict[index].toString();
    notifyListeners();
  }

  void onTextChange() {
    String text = _editingController.text;
    Log.d("onTextChange $text");
    inputWater = text;

    if (waterIndexChecked != null &&
        text != waterDict[waterIndexChecked!].toString()) {
      waterIndexChecked = null;
    }
    notifyListeners();
  }

  void submit(double actualWaterUsage) {
    var data = {
      "waId": item.waId,
      "actualWaterUsage": actualWaterUsage,
      "actualUsageDate": dateFormat.format(waterUsageDate!),
    };

    Log.d("data is $data");
    showLoading(context, content: "正在提交 ", barrierDismissible: false);
    XinJiangWaterManageService()
        .waterApplyHandleReceiverSubmit(data, cancelToken: createCancelToken())
        .then((result) {
      if (!context.mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);
      if (result.success == true && result.code == 0) {
        showToast("提交成功");
        Navigator.maybePop(context, true);
      }
    }).onError((error, stackTrace) {
      _handlerError(error, stackTrace, errorDo: () {
        BrnLoadingDialog.dismiss(context);
      });
    });
  }

  List? priceRecords;

  void onPricePredictChange(List? value) {
    Log.d("onPricePredictChange $value");
    if (priceRecords != value) {
      priceRecords = value;
      notifyListeners();
    }
  }
}
