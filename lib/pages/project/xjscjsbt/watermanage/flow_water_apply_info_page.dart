import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/widget/bdh_steps2.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import 'model/flow_item_model.dart';
import 'request/xinjiang_water_manage_service.dart';

class FlowWaterApplyInfoPage extends StatefulWidget {
  final num waId;
  const FlowWaterApplyInfoPage({super.key, required this.waId});

  @override
  State<FlowWaterApplyInfoPage> createState() => _FlowWaterApplyInfoPageState();
}

class _FlowWaterApplyInfoPageState extends State<FlowWaterApplyInfoPage>
    with AutoDisposeStateMixin {
  bool _isLoading = false;

  List<FlowItem> items = [];
  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _loadData();
    });
  }

  void _loadData() {
    if (_isLoading) {
      return;
    }
    _isLoading = true;
    setState(() {});

    XinJiangWaterManageService()
        .waterApplyViewProcess(widget.waId,
            cancelToken: useCancelToken(CancelToken()))
        .then((result) {
      if (!disposed && context.mounted) {
        _isLoading = false;
        items = (result.data as List?)?.map((item) {
              return FlowItem.fromJson(item);
            }).toList() ??
            [];
        _isLoading = false;
        setState(() {});
      }
    }).onError(_handlerError);
  }

  Null _handlerError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (disposed || !context.mounted) {
      return;
    }
    errorDo?.call();

    showToast(request.message ?? "预估水价失败,请稍后再试");
    setState(() {
      _isLoading = false;
    });
  }

  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody(BuildContext context) {
    var steps = items
        .map<BdhStepsItemData>((item) => BdhStepsItemData(
            title:
                "${item.auditName ?? ""} ${item.createName ?? ""} ${item.auditValue ?? ""}",
            content: item.createTime ?? ""))
        .toList();
    return CustomScrollView(
      slivers: [
        SliverPadding(
          padding: EdgeInsets.all(20.px),
          sliver: SliverToBoxAdapter(
              child: BdhStepsVertical(
                  outerIconSize: 30.px,
                  innerIconSize: 24.px,
                  steps: steps,
                  activeIndex: steps.length - 1)),
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("查看流程"),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: _isLoading ? _widgetLoading(context) : _widgetBody(context)),
    );
  }
}
