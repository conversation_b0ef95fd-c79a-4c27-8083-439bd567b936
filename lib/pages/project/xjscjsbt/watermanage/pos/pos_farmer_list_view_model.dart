import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_api_request.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/pos_farm_list_model.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_refresh_list_model.dart';

class PosFarmerListViewModel extends ViewStateRefreshListModel {
  int growerType = 0; //类型
  String growerName = ''; // 姓名/企业名称
  String orgCode = ''; //身份证/组织代码

  @override
  Future<List<RecordsItemModel>> loadData({int? pageNum}) async {
    var items = await PosResponsitory.queryAccountInfoByPage({
      'growerType': growerType, //类型
      'growerName': growerName, // 姓名/企业名称
      'orgCode': orgCode, //身份证/组织代码
      'page': pageNum,
      'rows': 10,
    });
    return items.data?.records ?? [];
  }
}
