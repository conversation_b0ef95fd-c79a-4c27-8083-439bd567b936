import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/farm_type_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/pos_bank_data_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/pos_farm_list_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:bdh_smart_agric_app/utils/request/request_no_data.dart';
import 'package:dio/dio.dart';

class PosResponsitory {
  //字典接口 查pos 用户类型
  static Future<FarmTypeModel> getPosUserTypeByKey(String key) async {
    var response = await waterManageHttp.post("/api/common/dict/$key");
    // print(response.data);
    return FarmTypeModel.fromJson(response.data);
  }

  // 查询账户信息
  static Future<PosFarmModel> queryAccountInfoByPage(dynamic data) async {
    var res = await waterManageHttp.post("/water/charge/queryAccountInfoByPage",
        data: data);
    return PosFarmModel.fromJson(res.data);
  }

//检查是否可以缴费 [app 支付 step1]
  static Future checkIsCanReCharge(dynamic data,
      {CancelToken? cancelToken}) async {
    var res = await waterManageHttp.post('/water/charge/checkInsert',
        data: data, cancelToken: cancelToken);
    return res.data;
  }

//获取银行sdk信息 [app 支付 step2]
  static Future<PosBankDataModel> getOnLineChargeBankSdkInfo(dynamic data,
      {CancelToken? cancelToken}) async {
    var res = await waterManageHttp.post('/onLinecharge/bankSdkInfo',
        data: data, cancelToken: cancelToken);
    return PosBankDataModel.fromJson(res.data);
  }

//获取支付状态 [app 支付 step3]
  static Future<RequestNoData> getOnLineChargeQueryStatus(dynamic data,
      {CancelToken? cancelToken}) async {
    var res = await waterManageHttp.post('/onLinecharge/queryStatus',
        data: data, cancelToken: cancelToken);
    return RequestNoData.fromJson(res.data);
  }

// 调交付接口 [app 支付 step4]
  static Future appPay(dynamic data, {CancelToken? cancelToken}) async {
    var res = await waterManageHttp.post('/app/app/pay',
        data: data, cancelToken: cancelToken);
    return res.data;
  }

//------pos-----------------
//通过app线上缴费 pos 支付
  static Future appPayReCharge(dynamic data, {CancelToken? cancelToken}) async {
    var res = await waterManageHttp.post('/app/app/pay',
        data: data, cancelToken: cancelToken);
    return res.data;
  }
}
