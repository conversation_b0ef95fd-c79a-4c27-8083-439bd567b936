import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_api_request.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/pos_farm_list_model.dart';
import 'package:bdh_smart_agric_app/utils/bdh_number_to_chinese.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';

// ignore: must_be_immutable
class PosRechargePage extends StatefulWidget {
  RecordsItemModel model;
  PosRechargePage({super.key, required this.model});

  @override
  State<PosRechargePage> createState() => _PosRechargePageState();
}

class _PosRechargePageState extends State<PosRechargePage> {
  final TextEditingController _controller = TextEditingController();
  final List<AmountButtonModel> buttonList = [
    AmountButtonModel(amount: 100, label: '100元', isSeleted: false),
    AmountButtonModel(amount: 300, label: '300元', isSeleted: false),
    AmountButtonModel(amount: 500, label: '500元', isSeleted: false),
    AmountButtonModel(amount: 1000, label: '1000元', isSeleted: false),
    AmountButtonModel(amount: 3000, label: '3000元', isSeleted: false),
    AmountButtonModel(amount: 5000, label: '5000元', isSeleted: false),
  ];
  String displayedAmount = '';

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: HexColor("#F3F5F9"),
        appBar: AppBar(
          title: const Text('账户充值'),
        ),
        body: Container(
          decoration: BoxDecoration(color: HexColor("#F3F5F9")),
          child: Column(
            children: [
              Expanded(
                flex: 12,
                child: Column(
                  children: [
                    Container(
                      decoration: const BoxDecoration(color: Colors.white),
                      padding: EdgeInsets.only(top: 10.px, bottom: 0.px),
                      width: 375.px,
                      child: Column(
                        children: [
                          PosReChargeItemView(
                            itemName: '余额',
                            itemValue: '${widget.model.accBalance}元',
                          ),
                          PosReChargeItemView(
                            itemName: '缴费户名',
                            itemValue: widget.model.growerName ?? '',
                            itemValue1: widget.model.accCode ?? '',
                          ),
                          getLineView(),
                          PosReChargeItemView(
                            itemName: '账户类型',
                            itemValue: widget.model.growerType ?? '',
                          ),
                          PosReChargeItemView(
                              itemName: '缴费单位',
                              itemValue: widget.model.orgName ?? ''),
                          getLineView(),
                          getInputView(),
                          Container(
                            decoration:
                                BoxDecoration(color: HexColor("#F3F5F9")),
                            height: 200.px,
                            width: 375.px,
                            padding: EdgeInsets.only(
                                top: 20.px, left: 10.px, right: 10.px),
                            child: GridView.builder(
                              gridDelegate:
                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                childAspectRatio: 2,
                                crossAxisSpacing: 10,
                                mainAxisSpacing: 10,
                              ),
                              itemCount: buttonList.length,
                              itemBuilder: (context, index) {
                                AmountButtonModel model = buttonList[index];

                                return AmountButton(
                                  model: model,
                                  clickedBtnCallBack: (currentModel) {
                                    // _controller.text = '${currentModel.amount}';
                                    _controller.text = formatCurrency(
                                        '${currentModel.amount}');
                                    for (AmountButtonModel itemModel
                                        in buttonList) {
                                      if (itemModel == model) {
                                        itemModel.isSeleted =
                                            !itemModel.isSeleted;
                                      } else {
                                        itemModel.isSeleted = false;
                                      }
                                    }
                                    formatterInputAmount(
                                        '${currentModel.amount}');
                                    setState(() {});
                                  },
                                );
                              },
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
              Expanded(
                flex: 2,
                child: Column(
                  children: [
                    GestureDetector(
                      onTap: () {
                        checkIsCanReCharge();
                      },
                      child: BDHUserCommmonButton(
                        width: 327.px,
                        height: 40.px,
                        title: '去缴费',
                        borderRadius: 50.px,
                        marginBottom: 16.px,
                        marginTop: 20.px,
                        gradientList: const [
                          Color.fromRGBO(30, 192, 106, 1),
                          Color.fromRGBO(30, 192, 106, 1)
                        ],
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ));
  }

  Widget getLineView() {
    return Container(
      margin: EdgeInsets.only(top: 20.px),
      padding: EdgeInsets.only(top: 10.px, bottom: 10.px),
      height: 1,
      width: 335.px,
      color: const Color.fromRGBO(0, 0, 0, 0.05),
    );
  }

  Widget getInputView() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text.rich(
                TextSpan(children: [
                  TextSpan(
                    text: "*",
                    style: TextStyle(
                        color: Colors.red,
                        fontSize: 16.px,
                        fontWeight: FontWeight.w500),
                  ),
                  TextSpan(
                    text: '缴费金额',
                    style:
                        TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
                  )
                ]),
              ),
              SizedBox(
                width: 250.px,
                // decoration: BoxDecoration(border: Border.all(width: 1)),
                child: Text(
                  // '200万1千1百21元',
                  displayedAmount,
                  maxLines: 2,
                  textAlign: TextAlign.right,
                  style: TextStyle(
                      overflow: TextOverflow.ellipsis,
                      fontSize: 16.px,
                      fontWeight: FontWeight.w500,
                      color: const Color.fromRGBO(0, 0, 0, 0.4)),
                ),
              ),
            ],
          ),
          Container(
            width: 375.px,
            // height: 80,
            // decoration: BoxDecoration(border: Border.all(width: 1)),
            padding: EdgeInsets.only(top: 20.px),
            child: Container(
              constraints: BoxConstraints(
                maxWidth: 300.px,
                minWidth: 50.px,
              ),
              child: CupertinoTextField.borderless(
                textAlign: TextAlign.center,
                keyboardType: TextInputType.number,
                style: TextStyle(
                  fontSize: 36.px,
                  color: HexColor('#000000'),
                  // color: Colors.blue,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Bebas',
                ),
                // textInputAction: TextInputAction.search,
                padding: EdgeInsets.zero,
                controller: _controller,
                placeholder: "请输入充值金额",
                placeholderStyle: TextStyle(
                    fontSize: 20.px,
                    fontWeight: FontWeight.w500,
                    color: HexColor('#A8B8B1')),
                // focusNode: focusScopeNode,
                // autofocus: true,
                // onTapOutside: (e) => {focusNode.unfocus()},
                onChanged: (value) {
                  clearBtnAtction();
                  formatterInputAmount(value);
                },

                onTap: () {
                  Logger().i(' textfiled  点击--onTap----');
                },
                onEditingComplete: () {
                  // var formatter =
                  //     NumberFormat.currency(locale: 'zh_CN', symbol: '￥');
                  // String formattedAmount = formatter.format();
                  Logger().i('textfiled 点击--onEditingComplete----');
                },
                inputFormatters: [
                  // FilteringTextInputFormatter.digitsOnly,
                  CurrencyFormatter(), // 使用自定义的货币格式化器
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String removeLastCharacter(String input) {
    if (input.isEmpty) {
      return input; // 如果字符串为空，直接返回
    }
    return input.substring(0, input.length - 1); // 删除最后一个字符
  }

  clearBtnAtction() {
    for (AmountButtonModel model in buttonList) {
      model.isSeleted = false;
    }
    setState(() {});
  }

//格式化显示: '200万1千1百21元',
  formatterInputAmount(String value) {
    String currentDisplayValue = '';
    if (value.contains('¥')) {
      currentDisplayValue = ConvertNumberToChineseMoneyWords()
          .toChinese(removeLastCharacter(value));
    } else {
      currentDisplayValue = ConvertNumberToChineseMoneyWords().toChinese(value);
    }
    setState(() {
      displayedAmount = currentDisplayValue;
    });
  }

  //查询是否可以缴费
  checkIsCanReCharge() {
    DateTime now = DateTime.now();
    int currentYear = now.year;
    String yearString = currentYear.toString();

    // 假设有一个数字字符串
    String numberString = _controller.text;
    String newString = numberString.replaceAll(RegExp(r'[^0-9.]'), '');
    // 将字符串转换为浮点数
    double number = double.parse(newString);
    // 将浮点数格式化为保留两位有效数字的字符串
    String formattedNumber = number.toStringAsFixed(2);

    PosResponsitory.checkIsCanReCharge({
      "accId": widget.model.accId,
      "year": yearString,
      "amount": formattedNumber,
    }).then((res) {
      // appPayReCharge();
      if (res['code'] == 0) {
        callNativewPosRechargeAction(); //调用pos机app支付
      } else {
        showToast(res['msg']);
      }
    });
  }

  //callNativePos
  callNativewPosRechargeAction() {
    NativeUtil.openPosRecharge({'data1': '119test'}).then((res) {
      //查询支付接口回调

      //调用支付接口
      appPayReCharge();
    });

    // NativeUtil.openTxFace(params).then((faceRes) {
    //   if (faceRes == "0") {
    //   } else if (faceRes == "1") {}
    //   var faceParams = {
    //     "yearNo": widget.year,
    //     "rationServiceType": 2,
    //     ...(jsonDecode(res.data) as Map<String, dynamic>),
    //     "requestResult": faceRes
    //   };
    //   BdhLandResponsitory.appNfSaveRationFaceDetail(faceParams).then((res) {
    //     bus.emit("refreshLandAuthList");
    //     showToast("基本田打卡提交成功，请等待管理员审核");
    //     Navigator.of(context).pop();
    //   });
    // });
  }

  //app 充值缴费
  appPayReCharge() {
    DateTime now = DateTime.now();
    int currentYear = now.year;
    String yearString = currentYear.toString();
    var paramter = {
      "accId": widget.model.accId,
      "year": yearString,
      "amount": 300.01,
    };
    PosResponsitory.appPayReCharge(paramter).then((res) {});
  }
} // end state

// ignore: must_be_immutable
class PosReChargeItemView extends StatelessWidget {
  String itemName;
  String itemValue;
  String? itemValue1;
  PosReChargeItemView(
      {super.key,
      required this.itemName,
      required this.itemValue,
      this.itemValue1});

  @override
  Widget build(BuildContext context) {
    return Container(
      // decoration: BoxDecoration(border: Border.all(width: 1)),
      padding: EdgeInsets.only(left: 20.px, right: 0.px, top: 10.px),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
              width: 80.px,
              child: Text(itemName,
                  maxLines: 1,
                  style: TextStyle(
                    overflow: TextOverflow.clip,
                    fontWeight: FontWeight.w500,
                    fontSize: 12.px,
                    color: const Color.fromRGBO(51, 51, 51, 0.4),
                  ))),
          SizedBox(height: 10.px),
          SizedBox(
              width: 260.px,
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        itemValue,
                        maxLines: 1,
                        style: TextStyle(
                          overflow: TextOverflow.clip,
                          fontWeight: FontWeight.w500,
                          fontSize: 12.px,
                          color: const Color.fromRGBO(41, 41, 52, 1),
                        ),
                      ),
                      // (itemValue1 ?? '').isEmpty
                      //     ? Container()
                      //     : Container(
                      //         decoration:
                      //             BoxDecoration(border: Border.all(width: 1)),
                      //         padding: EdgeInsets.only(left: 5.px),
                      //         width: 150.px,
                      //         child: Text(
                      //           itemValue1 ?? '',
                      //           maxLines: 1,
                      //           style: TextStyle(
                      //             overflow: TextOverflow.clip,
                      //             fontWeight: FontWeight.w500,
                      //             fontSize: 12.px,
                      //             color: const Color.fromRGBO(41, 41, 52, 1),
                      //           ),
                      //         ),
                      //       )
                    ],
                  ),
                  Row(
                    children: [
                      (itemValue1 ?? '').isEmpty
                          ? Container()
                          : Text(
                              itemValue1 ?? '',
                              maxLines: 1,
                              style: TextStyle(
                                overflow: TextOverflow.clip,
                                fontWeight: FontWeight.w500,
                                fontSize: 12.px,
                                color: const Color.fromRGBO(41, 41, 52, 1),
                              ),
                            )
                    ],
                  )
                ],
              )),
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class AmountButton extends StatefulWidget {
  AmountButtonModel model;
  Function(AmountButtonModel model) clickedBtnCallBack;
  AmountButton({
    super.key,
    required this.model,
    required this.clickedBtnCallBack,
  });

  @override
  State<AmountButton> createState() => _AmountButtonState();
}

class _AmountButtonState extends State<AmountButton> {
  // void _handleTap(BuildContext context) {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.clickedBtnCallBack(widget.model);
      },
      child: Container(
        decoration: BoxDecoration(
          color: widget.model.isSeleted
              ? const Color.fromRGBO(30, 192, 106, 1)
              : Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(
          child: Text(
            widget.model.label,
            style: TextStyle(
                fontSize: 16.px,
                color: widget.model.isSeleted
                    ? Colors.white
                    : const Color.fromRGBO(0, 0, 0, 1),
                fontFamily: 'PingFang SC'),
          ),
        ),
      ),
    );
  }
}

class AmountButtonModel {
  int amount;
  String label;
  bool isSeleted;
  AmountButtonModel({
    required this.amount,
    required this.label,
    required this.isSeleted,
  });
}

class CurrencyFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // 如果删除操作，直接返回
    if (newValue.text.length < oldValue.text.length) {
      return newValue;
    }

    // 去掉之前的格式化内容，只保留数字和小数点
    String newString = newValue.text.replaceAll(RegExp(r'[^0-9.]'), '');
    String? endStr = truncateToSecondDot(newString);

    // 格式化数字为货币格式
    NumberFormat numberFormat =
        NumberFormat("#,##0.00", "zh_CN"); // 支持小数点，设置 locale 为中文
    String formattedString = '';

    // // 尝试解析和格式化数字
    // try {
    //   // double number = double.parse(newString);
    //   print('-----------+++++++---${currentStr}');
    //   if (isLastCharDot(currentStr ?? '')) {
    //     formattedString = currentStr ?? '';
    //   } else {
    //     formattedString = numberFormat.format(currentStr);
    //   }
    // } catch (e) {
    //   // 如果解析失败，返回原始值
    //   return newValue;
    // }

    formattedString = endStr ?? '';
    // 添加货币符号
    formattedString += '¥';

    // 返回格式化后的值
    return TextEditingValue(
      text: formattedString,
      selection: TextSelection.collapsed(offset: formattedString.length),
    );
  }

  bool isLastCharDot(String input) {
    // 检查字符串是否为空或长度为0
    if (input.isEmpty) {
      return false;
    }
    // 获取字符串的最后一个字符，并判断是否是点
    return input.codeUnitAt(input.length - 1) == '.'.codeUnitAt(0);
  }

  String? truncateToSecondDot(String input) {
    // 查找第二个点的位置
    int firstDotIndex = input.indexOf('.');
    if (firstDotIndex == -1) {
      // 如果没有找到点，返回null或者根据需求处理
      return input;
    }

    int secondDotIndex = input.indexOf('.', firstDotIndex + 1);
    if (secondDotIndex == -1) {
      // 如果没有找到第二个点，返回原始字符串或者根据需求处理
      return input;
    }

    // 截取到第二个点之前
    return input.substring(0, secondDotIndex);
  }
}

// 通用的货币格式化方法
String formatCurrency(String amount) {
  NumberFormat numberFormat = NumberFormat("#,###");
  return '${numberFormat.format(int.parse(amount))}¥';
}
