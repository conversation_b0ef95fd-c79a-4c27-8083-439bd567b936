class FarmTypeModel {
  bool? success;
  int? code;
  String? msg;
  List<PosUserType>? data;

  FarmTypeModel({
    required this.success,
    required this.code,
    required this.msg,
    required this.data,
  });

//{success: true, code: 0, msg: success, data: [{key: 0, value: 农户}, {key: 1, value: 企业}]}
  FarmTypeModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = <PosUserType>[];
    if (json['data'] != null) {
      json['data'].forEach((v) {
        data!.add(PosUserType.fromJson(v));
      });
    }
  }
}

class PosUserType {
  late int? key;
  late String? value;
  PosUserType({
    this.key,
    this.value,
  });
  PosUserType.fromJson(Map<String, dynamic> json) {
    key = json['key'];
    value = json['value'];
  }
}
