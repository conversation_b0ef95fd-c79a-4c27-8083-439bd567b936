class PosFarmModel {
  bool? success;
  int? code;
  String? msg;
  Data? data;

  PosFarmModel({this.success, this.code, this.msg, this.data});

  PosFarmModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null ? new Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['success'] = this.success;
    data['code'] = this.code;
    data['msg'] = this.msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  List<RecordsItemModel>? records;

  Data({this.records});

  Data.fromJson(Map<String, dynamic> json) {
    if (json['records'] != null) {
      records = <RecordsItemModel>[];
      json['records'].forEach((v) {
        records!.add(new RecordsItemModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.records != null) {
      // data['records'] = this.records!.map((v) => v.toJson()).toList();
      data['records'] = this.records!.map((v) {
        v.isSelected = false;
        return v;
      }).toList();
    }
    return data;
  }
}

class RecordsItemModel {
  bool? isSelected;
  int? accId;
  String? orgCode;
  String? orgName;
  String? accCode;
  String? growerType;
  String? growerName;
  String? idNumber;
  double? accBalance;
  int? piId;
  // Null? statusCd;
  // int? createBy;
  // int? createTime;
  // Null? updateBy;
  // Null? updateTime;
  // Null? remark;
  // Null? params;
  // Null? con;
  // Null? year;
  // Null? operatorName;
  // Null? operatorPhone;
  // Null? operatorIdNum;

  RecordsItemModel({
    this.isSelected,
    this.accId,
    this.orgCode,
    this.orgName,
    this.accCode,
    this.growerType,
    this.growerName,
    this.idNumber,
    this.accBalance,
    this.piId,
    // this.statusCd,
    // this.createBy,
    // this.createTime,
    // this.updateBy,
    // this.updateTime,
    // this.remark,
    // this.params,
    // this.con,
    // this.year,
    // this.operatorName,
    // this.operatorPhone,
    // this.operatorIdNum
  });

  RecordsItemModel.fromJson(Map<String, dynamic> json) {
    isSelected = json['isSelected'];
    accId = json['accId'];
    orgCode = json['orgCode'];
    orgName = json['orgName'];
    accCode = json['accCode'];
    growerType = json['growerType'];
    growerName = json['growerName'];
    idNumber = json['idNumber'];
    accBalance = json['accBalance'];
    piId = json['piId'];
    // statusCd = json['statusCd'];
    // createBy = json['createBy'];
    // createTime = json['createTime'];
    // updateBy = json['updateBy'];
    // updateTime = json['updateTime'];
    // remark = json['remark'];
    // params = json['params'];
    // con = json['con'];
    // year = json['year'];
    // operatorName = json['operatorName'];
    // operatorPhone = json['operatorPhone'];
    // operatorIdNum = json['operatorIdNum'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['isSelected'] = this.isSelected;
    data['accId'] = this.accId;
    data['orgCode'] = this.orgCode;
    data['orgName'] = this.orgName;
    data['accCode'] = this.accCode;
    data['growerType'] = this.growerType;
    data['growerName'] = this.growerName;
    data['idNumber'] = this.idNumber;
    data['accBalance'] = this.accBalance;
    data['piId'] = this.piId;
    // data['statusCd'] = this.statusCd;
    // data['createBy'] = this.createBy;
    // data['createTime'] = this.createTime;
    // data['updateBy'] = this.updateBy;
    // data['updateTime'] = this.updateTime;
    // data['remark'] = this.remark;
    // data['params'] = this.params;
    // data['con'] = this.con;
    // data['year'] = this.year;
    // data['operatorName'] = this.operatorName;
    // data['operatorPhone'] = this.operatorPhone;
    // data['operatorIdNum'] = this.operatorIdNum;
    return data;
  }
}
