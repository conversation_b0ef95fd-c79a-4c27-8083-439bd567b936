class PosBankDataModel {
  bool? success;
  int? code;
  String? msg;
  BankDataModel? data;

  PosBankDataModel({
    required this.success,
    required this.code,
    required this.msg,
    required this.data,
  });

  PosBankDataModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = BankDataModel.fromJson(json['data']);
    // data = <BankDataModel>[];
    // if (json['data'] != null) {
    //   json['data'].forEach((v) {
    //     data!.add(BankDataModel.fromJson(v));
    //   });
    // }
  }
}

class BankDataModel {
  late String? tokenId;
  late String? orderId;
  late String? IOSSdkFlag;
  late String? paymentURL;
  BankDataModel({
    this.tokenId,
    this.orderId,
    this.IOSSdkFlag,
    this.paymentURL,
  });
  BankDataModel.fromJson(Map<String, dynamic> json) {
    tokenId = json['tokenId'];
    orderId = json['orderId'];
    IOSSdkFlag = json['IOSSdkFlag'];
    paymentURL = json['paymentURL'];
  }
}

//Response ({"success":true,"code":0,"msg":"success",
//"data":{
//"tokenId":"17404087180009859232",
//"orderId":"SF250224150627613551",
//"IOSSdkFlag":"CallbackID=railwaypay&TokenID=17404087180009859232&Method=pay",
//"paymentURL":"https://mobile.abchina.com/mpaynew/mpay/index?TOKEN=17404087180009859232"}})