import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_pos_text_input.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_api_request.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_chooseType_view.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_farmer_list_view_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/farm_type_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/pos_farm_list_model.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/debounce_throttle_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/provider_widget.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class PosFarmerList extends StatefulWidget {
  const PosFarmerList({super.key});

  @override
  State<PosFarmerList> createState() => _PosFarmerListState();
}

class _PosFarmerListState extends State<PosFarmerList> {
  List<PosUserType> userTypeList = [];
  int growerType = 0;
  String growerName = '';
  String orgCode = '';
  final _debounce = Debouncer(milliseconds: 1200);
  final TextEditingController _nameEditController = TextEditingController();
  final TextEditingController _codeEditController = TextEditingController();
  FocusScopeNode focusScopeNodeName = FocusScopeNode();
  FocusScopeNode focusScopeNodeCode = FocusScopeNode();
  RecordsItemModel? currentSelectedModle;

  @override
  void initState() {
    super.initState();
    getUserType();
  }

  @override
  void dispose() {
    super.dispose();
    focusScopeNodeName.dispose();
    focusScopeNodeCode.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: HexColor("#F3F5F9"),
        appBar: AppBar(
          title: const Text('农户列表'),
        ),
        body: ProviderWidget(
          builder: (BuildContext context, ChangeNotifier model, child) {
            model as PosFarmerListViewModel;
            return Column(
              children: [
                Container(
                  width: 375.px,
                  color: Colors.white,
                  child: Column(
                    children: [
                      PosChoosetypeView(
                        title: '类型',
                        choosedValue: '请选择类型',
                        padding: EdgeInsets.only(left: 20.px, right: 20.px),
                        fontSize: 16.px,
                        fontWeight: FontWeight.w500,
                        itemList: userTypeList,
                        chooseTypeCallBack: (type) {
                          // form['growerType'] = type;
                          model.growerType = type;
                          // _debounce.run(() {
                          // });
                          model.initData();
                        },
                      ),
                      BdhPosTextInput(
                        item: FormItem(title: "姓名/企业名称"),
                        // initialValue: widget.info.nickName,
                        // controller: _nameEditController,
                        // focusNode: focusScopeNodeName,
                        padding: EdgeInsets.only(left: 12.px, right: 12.px),
                        fontSize: 16.px,
                        fontWeight: FontWeight.w500,
                        placeHolder: "请输入名称姓名/企业名称",
                        onChange: (v) {
                          // form["growerName"] = v;
                          model.growerName = v;
                          _debounce.run(() {
                            model.initData();
                            // clearFoucus();
                          });
                        },
                      ),
                      BdhPosTextInput(
                        item: FormItem(title: "身份证/组织代码"),
                        // initialValue: widget.info.nickName,
                        // controller: _codeEditController,
                        // focusNode: focusScopeNodeCode,
                        padding: EdgeInsets.only(left: 12.px, right: 12.px),
                        fontSize: 16.px,
                        fontWeight: FontWeight.w500,
                        placeHolder: "身份证/组织代码",
                        textInputType: TextInputType.number,
                        onChange: (v) {
                          // form["orgCode"] = v;
                          // Logger().i('身份证/组织代码onchange = $v');
                          model.orgCode = v;
                          _debounce.run(() {
                            model.initData();
                            // clearFoucus();
                          });
                        },
                      ),
                    ],
                  ),
                ),
                Expanded(
                  flex: 12,
                  child: Container(
                      // decoration: BoxDecoration(border: Border.all(width: 1)),
                      padding: EdgeInsets.only(top: 12.px),
                      width: 375.px,
                      // color: HexColor("#F3F5F9"),
                      child: model.isBusy
                          ? const ViewStateBusyWidget()
                          : model.isEmpty
                              ? const BdhEmptyView()
                              : SmartRefresher(
                                  controller: model.refreshController,
                                  enablePullDown: true,
                                  enablePullUp: true,
                                  onRefresh: model.refresh,
                                  onLoading: model.loadMore,
                                  child: ListView.separated(
                                    itemCount: model.list.length,
                                    shrinkWrap: true,
                                    scrollDirection: Axis.vertical,
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      RecordsItemModel itemModel =
                                          model.list[index];
                                      return GestureDetector(
                                        onTap: () {
                                          userClickedItem(itemModel, model);
                                          // clearFoucus();
                                        },
                                        child: Container(
                                          width: 375.px,
                                          height: 78.px,
                                          alignment: Alignment.center,
                                          child: PosFarmItem(
                                            model: itemModel,
                                          ),
                                        ),
                                      );
                                    },
                                    separatorBuilder:
                                        (BuildContext context, int index) {
                                      return SizedBox(
                                        height: 12.px,
                                        width: 375.px,
                                      );
                                    },
                                  ),
                                )),
                ),
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      model.list.isEmpty
                          ? Container()
                          : GestureDetector(
                              onTap: () {
                                if (currentSelectedModle == null ||
                                    (currentSelectedModle?.isSelected ??
                                            false) ==
                                        false) {
                                  showToast('请选择一个缴费账号');
                                  return;
                                }
                                Navigator.of(context).pushNamed(
                                    RouteName.posRechargePage,
                                    arguments: currentSelectedModle);
                              },
                              child: BDHUserCommmonButton(
                                width: 327.px,
                                height: 40.px,
                                title: '去缴费',
                                borderRadius: 50.px,
                                marginBottom: 16.px,
                                marginTop: 20.px,
                                gradientList: const [
                                  Color.fromRGBO(30, 192, 106, 1),
                                  Color.fromRGBO(30, 192, 106, 1)
                                ],
                              ),
                            )
                    ],
                  ),
                )
              ],
            );
          },
          model: PosFarmerListViewModel(),
          onModelReady: (ChangeNotifier model) {
            model as PosFarmerListViewModel;
            model.growerName = growerName;
            model.orgCode = orgCode;
            model.growerType = growerType;
            model.initData();
          },
        ));
  } //end build

  clearFoucus() {
    focusScopeNodeCode.unfocus();
    focusScopeNodeName.unfocus();
  }

  userClickedItem(
    RecordsItemModel seletedModel,
    PosFarmerListViewModel viewModel,
  ) {
    for (RecordsItemModel model in viewModel.list) {
      if (model.accId == seletedModel.accId) {
        seletedModel.isSelected = !(seletedModel.isSelected ?? false);
      } else {
        model.isSelected = false;
      }
      if (model.isSelected ?? false) {
        currentSelectedModle = model;
      }
      setState(() {});
    }
  }

  getUserType() {
    PosResponsitory.getPosUserTypeByKey("growerType").then((res) {
      // Logger().i('用户类型:res=$res');
      setState(() {
        userTypeList = res.data ?? [];
      });
    });
  }
} //end state

// ignore: must_be_immutable
class PosFarmItem extends StatelessWidget {
  RecordsItemModel model;
  PosFarmItem({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(top: 12.px, bottom: 10.px),
        height: 78.px,
        width: 351.px,
        decoration: BoxDecoration(
            // border: Border.all(width: 1),
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.px)),
        child: Row(
          children: [
            SizedBox(
              height: 78.px,
              width: 4.px,
              child: Center(
                child: Image.asset(
                  width: 4.px,
                  height: 36.px,
                  ImageHelper.wrapAssets(model.isSelected ?? false
                      ? "posRightSideSelectedImg.png"
                      : "posRightSideImg.png"),
                ),
              ),
            ),
            Container(
              constraints: BoxConstraints(maxWidth: 340.px),
              padding: EdgeInsets.only(left: 16.px),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Image.asset(
                            width: 16.px,
                            height: 16.px,
                            ImageHelper.wrapAssets(model.isSelected ?? false
                                ? "privacy_agreement_selected.png"
                                : "privacy_agreement_unselected.png"),
                          ),
                          SizedBox(width: 3.px),
                          SizedBox(
                            width: 55.px,
                            child: Text(
                              model.growerName ?? '',
                              maxLines: 1,
                              style: TextStyle(
                                overflow: TextOverflow.clip,
                                fontWeight: FontWeight.w600,
                                fontSize: 16.px,
                                color: const Color.fromRGBO(44, 44, 52, 1),
                              ),
                            ),
                          ),
                          SizedBox(width: 5.px),
                          SizedBox(
                            width: 150.px,
                            child: Text(
                              // '65***********12134',
                              model.accCode ?? '',
                              maxLines: 1,
                              style: TextStyle(
                                overflow: TextOverflow.ellipsis,
                                fontWeight: FontWeight.w600,
                                fontSize: 16.px,
                                color: const Color.fromRGBO(44, 44, 52, 1),
                              ),
                            ),
                          )
                        ],
                      ),
                      Container(
                        alignment: Alignment.centerRight,
                        width: 90.px,
                        child: Text(
                          // '184团1连',
                          model.orgName ?? '',
                          maxLines: 1,
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            overflow: TextOverflow.clip,
                            fontWeight: FontWeight.w600,
                            fontSize: 16.px,
                            color: const Color.fromRGBO(44, 44, 52, 1),
                          ),
                        ),
                      )
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        '账户余额：',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 13.px,
                          color: const Color.fromRGBO(51, 51, 51, 0.4),
                        ),
                      ),
                      Text(
                        // '125.5元',
                        '${model.accBalance}元',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 13.px,
                          color: const Color.fromRGBO(44, 44, 52, 1),
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ],
        ));
  }
}

// class PosUserType {
//   late int? key;
//   late String? value;
//   PosUserType({
//     this.key,
//     this.value,
//   });
//   PosUserType.fromJson(Map<String, dynamic> json) {
//     key = json['key'];
//     value = json['value'];
//   }
// }

// {
// 	success: true,
// 	code: 0,
// 	msg: success,
// 	data: {
// 		records: [{
// 			accId: 5,
// 			orgCode: 860101,
// 			orgName: 1,
// 			accCode: 860101101,
// 			growerType: 农户,
// 			growerName: 测试1,
// 			idNumber: 01,
// 			accBalance: 0.0,
// 			piId: 8,
// 			statusCd: null,
// 			createBy: 1,
// 			createTime: 1739001501000,
// 			updateBy: null,
// 			updateTime: null,
// 			remark: null,
// 			params: null,
// 			con: null,
// 			year: null,
// 			operatorName: null,
// 			operatorPhone: null,
// 			operatorIdNum: null
// 		}, {
// 			accId: 7,
// 			orgCode: 860101,
// 			orgName: 宝泉岭分公司,
// 			accCode: 86011230 ** ** * ,
// 			growerType: 农户,
// 			growerName: 张三,
// 			idNumber: 230 ** ** * ,
// 			accBalance: 0.0,
// 			piId: 16,
// 			statusCd: null,
// 			createBy: 38650,
// 			createTime: 1739153290729,
// 			updateBy: null,
// 			updateTime: null,
// 			remark: null,
// 			params: null,
// 			con: null,
// 			year: null,
// 			operatorName: null,
// 			operatorPhone: null,
// 			operatorIdNum: null
// 		}, ]
// 	}
// }

// Map<String, dynamic> form = {
//   'growerType': 0, //类型
//   'growerName': '', // 姓名/企业名称
//   'orgCode': '', //身份证/组织代码
//   'page': 1,
//   'rows': 10,
// };

// {"page":1,"rows":10,"orgCode":"860101","growerType":1,"growerName":"123456","idNumber":"123456","operatorName":"12","operatorIdNum":"123456"}

// getAccountInfo() {
//   PosResponsitory.queryAccountInfoByPage(form).then((res) {
//     // String jsonString = json.encode(res);
//     PosFarmModel model = PosFarmModel.fromJson(res);
//     Logger().i('农户信息:res=$res----$model');
//     listModel.clear();
//     setState(() {
//       listModel = model.data?.records ?? [];
//     });
//   });
// }
