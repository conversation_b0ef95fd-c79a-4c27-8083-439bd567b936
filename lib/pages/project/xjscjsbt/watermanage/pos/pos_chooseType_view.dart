import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/pos/pos_model/farm_type_model.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

// ignore: must_be_immutable
class PosChoosetypeView extends StatefulWidget {
  Function(int type) chooseTypeCallBack;
  FontWeight? fontWeight;
  EdgeInsetsGeometry? padding;
  String title;
  String? choosedValue;
  List<PosUserType> itemList;
  double? fontSize;
  PosChoosetypeView({
    super.key,
    this.fontWeight,
    this.padding,
    this.fontSize,
    this.choosedValue,
    required this.title,
    required this.itemList,
    required this.chooseTypeCallBack,
  });

  @override
  State<PosChoosetypeView> createState() => _PosChoosetypeViewState();
}

class _PosChoosetypeViewState extends State<PosChoosetypeView> {
  String choosedValue = '';
  Color chooseValueColor = const Color(0xFFFFCDD2);
  @override
  void initState() {
    super.initState();
    choosedValue = widget.choosedValue ?? '';
    chooseValueColor = choosedValue == '请选择类型'
        ? const Color.fromRGBO(0, 0, 0, 0.4)
        : const Color.fromRGBO(0, 0, 0, 1);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: widget.padding ??
          const EdgeInsets.only(left: 0, right: 0, top: 0, bottom: 0),
      decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(
                  width: 1.px,
                  color: const Color.fromRGBO(226, 235, 231, 0.6)))),
      constraints: BoxConstraints(minHeight: 44.px),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text.rich(TextSpan(children: [
                TextSpan(
                  text: widget.title,
                  style: TextStyle(
                      fontSize: widget.fontSize ?? 16.px,
                      fontWeight: widget.fontWeight ?? FontWeight.w500),
                )
              ])),
              Row(children: [
                GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      BrnMultiDataPicker(
                        context: context,
                        title: widget.title,
                        delegate: BrnPosRowDelegate(
                            firstSelectedIndex: 0, list: widget.itemList),
                        confirmClick: (list) {
                          widget.chooseTypeCallBack(list[0]);
                          String chooseValue =
                              widget.itemList[list[0]].value ?? '';
                          setState(() {
                            choosedValue = chooseValue;
                            chooseValueColor = choosedValue == '请选择类型'
                                ? const Color.fromRGBO(0, 0, 0, 0.4)
                                : const Color.fromRGBO(0, 0, 0, 1);
                          });
                        },
                      ).show();
                    },
                    child: Container(
                      alignment: Alignment.centerRight,
                      width: 200.px,
                      height: 30.px,
                      // color: Colors.red,
                      child: Text(
                        choosedValue,
                        style: TextStyle(
                            fontSize: widget.fontSize,
                            fontWeight: FontWeight.w400,
                            color: chooseValueColor),
                      ),
                    ))
                // SizedBox(width: 10.px),
              ])
            ],
          ),
        ],
      ),
    );
  }
}

class BrnPosRowDelegate implements BrnMultiDataPickerDelegate {
  int firstSelectedIndex = 0;
  int secondSelectedIndex = 0;
  int thirdSelectedIndex = 0;
  List<PosUserType> list = [];

  BrnPosRowDelegate(
      {this.firstSelectedIndex = 0,
      this.secondSelectedIndex = 0,
      required this.list});

  @override
  int numberOfComponent() {
    return 1;
  }

  @override
  int numberOfRowsInComponent(int component) {
    return list.length;
  }

  @override
  String titleForRowInComponent(int component, int index) {
    return list[index].value ?? "";
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  selectRowInComponent(int component, int row) {
    if (0 == component) {
      firstSelectedIndex = row;
    }
  }

  @override
  int initSelectedRowForComponent(int component) {
    if (0 == component) {
      return firstSelectedIndex;
    }
    return 0;
  }
}
