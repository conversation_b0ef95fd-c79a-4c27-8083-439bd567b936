import 'package:bdh_smart_agric_app/components/form/bdh_dropdown_date_time_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_dropdown_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/const_dict.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/model/water_manage_index_model.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:oktoast/oktoast.dart';

import '../../../../model/request_no_data.dart';
import 'flow_water_apply_info_page.dart';
import 'model/water_use_record_model.dart';
import 'my_water_use_detail_page.dart';
import 'request/xinjiang_water_manage_service.dart';
import 'widget/water_apply_item_widget.dart';

//用户-用水记录
class MyWaterUseInfoPage extends StatefulWidget {
  const MyWaterUseInfoPage({super.key});

  @override
  State<MyWaterUseInfoPage> createState() => _MyWaterUseInfoPageState();
}

class _MyWaterUseInfoPageState extends State<MyWaterUseInfoPage>
    with AutoDisposeStateMixin {
  late final _Controller controller;

  @override
  void initState() {
    super.initState();
    controller = useChangeNotifier(_Controller(context))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("用水记录"),
        actions: [
          GestureDetector(
            onTap: controller.onClickReset,
            child: Text("重置",
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                  color: const Color.fromRGBO(30, 192, 106, 1),
                )),
          ),
          SizedBox(
            width: 15.px,
          )
        ],
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: controller.isLoading
              ? _widgetLoading(context)
              : Column(
                  children: [_widgetSubMenu(), Expanded(child: _widgetList())],
                )),
    );
  }

  Widget _widgetList() {
    return _ContextWidget(
      key: ValueKey(
          "${controller.chosenAccount?.code}-${controller.chosenMonthOfYear}"),
      chosenAccount: controller.chosenAccount,
      monthOfYear: controller.chosenMonthOfYear,
    );
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetSubMenu() {
    return Container(
        height: 42.px,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: Row(
          children: [
            Expanded(
                child: BdhDropdownDateTimePicker(
              placeHolder: "选择日期",
              maxWidth: 120.px,
              checkState: true,
              initialValue: controller.chosenMonthOfYear,
              minimumYear: 2024,
              maximumYear: DateTime.now().year,
              onChange: (v) {
                Log.d("v is $v");
                if (v == null) {
                  return;
                }
                controller.chosenMonthOfYear = v;
              },
              dateFormat: dateFormatyyyyMM,
            )),
            VerticalDivider(
              color: const Color.fromRGBO(51, 51, 51, 0.1),
              width: 1.px,
              indent: 9.px,
              endIndent: 9.px,
            ),
            Expanded(
                child: BdhDropDownSingleDataPicker(
              placeHolder: "请选择账户",
              maxWidth: 120.px,
              checkState: true,
              initialValue: controller.chosenAccount,
              onChange: (v) {
                Log.d("v is $v");
                controller.chosenAccount = v;
              },
              item: FormItem(
                  title: "请选择账户",
                  data: controller.accountDict,
                  isRequired: true),
            )),
          ],
        ));
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  _Controller(this.context);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  List<DictNode> yearDict = [];
  DateTime? _chosenMonthOfYear;
  DateTime? get chosenMonthOfYear => _chosenMonthOfYear;
  set chosenMonthOfYear(DateTime? node) {
    _chosenMonthOfYear = node;
    notifyListeners();
  }

  List<DictNode> accountDict = [];
  DictNode? _chosenAccount;
  DictNode? get chosenAccount => _chosenAccount;
  set chosenAccount(DictNode? node) {
    _chosenAccount = node;
    notifyListeners();
  }

  void onClickReset() {
    _chosenMonthOfYear = null;
    _chosenAccount = null;
    notifyListeners();
  }

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }

    Future.wait([
      XinJiangWaterManageService()
          .getDicByKey("year_cd", cancelToken: createCancelToken()),
      XinJiangWaterManageService().getWaterAccList({
        "idNumber": StorageUtil.userInfo()?.data?.idCard ?? "",
      }, cancelToken: createCancelToken())
    ]).then((list) {
      if (disposed) {
        return;
      }
      yearDict = (list[0] as DictList).data ?? [];

      accountDict = ((list[1] as RequestNoData).data as List?)
              ?.map<DictNode>((item) => DictNode(
                  code: item["accId"].toString(),
                  name: "${item["orgName"]} | ${item["growerName"]}"))
              .toList() ??
          [];
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  Null _handlerError(Object error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (disposed || !context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }
}

class _ContextWidget extends StatefulWidget {
  final DateTime? monthOfYear;
  final DictNode? chosenAccount;
  const _ContextWidget({super.key, this.chosenAccount, this.monthOfYear});

  @override
  State<_ContextWidget> createState() => __ContextWidgetState();
}

class __ContextWidgetState extends State<_ContextWidget>
    with AutoDisposeStateMixin {
  late final ScrollController _scrollController;

  late final _ContentController controller;

  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());
    controller = useChangeNotifier(_ContentController(
      context,
      chosenAccount: widget.chosenAccount,
      monthOfYear: widget.monthOfYear,
    ))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  void dispose() {
    Log.d("__ContextWidgetState dispose");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading) return _widgetLoading();

    return _widgetBody();
  }

  //加载中
  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  //申请机动地地块-列表为空
  Widget _widgetEmpty() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SvgPicture.asset(
            ImageHelper.wrapAssets("icon_nodata.svg"),
            width: 201.5.px,
            height: 100.px,
          ),
          SizedBox.square(
            dimension: 45.px,
          ),
          Text(
            "未找到任何记录",
            style: TextStyle(
                color: const Color.fromRGBO(44, 44, 52, 1), fontSize: 14.px),
          ),
        ],
      ),
    );
  }

  Widget _widgetBody() {
    var list = controller.waterUseResultModelList ?? [];

    var viewList = list.map<List<Widget>>((e) {
      return _widgetSubList(e);
    }).fold<List<Widget>>(
        [],
        (previousValue, element) =>
            previousValue..addAll(element)).toList(growable: true);

    if (viewList.isEmpty) {
      return _widgetEmpty();
    }

    viewList.add(SliverPadding(padding: EdgeInsets.only(bottom: 12.px)));
    return CustomScrollView(
      controller: _scrollController,
      slivers: viewList,
    );
  }

  List<Widget> _widgetSubList(WaterUseResultModel item) {
    return [
      SliverToBoxAdapter(child: _widgetListTitle(context, item.dateStr ?? "")),
      SliverList.builder(
        itemBuilder: (context, index) => WaterApplyItemWidget(
          item: item.waterApplyList![index],
          onClickProcess: controller.onClickProcessWaterUse,
          onClick: controller.onClickItemWaterUse,
        ),
        itemCount: item.waterApplyList?.length ?? 0,
      )
    ];
  }

  //水费账户标题
  Widget _widgetListTitle(BuildContext context, String text) {
    return Container(
        margin:
            EdgeInsets.only(top: 6.px, bottom: 6.px, left: 12.px, right: 12.px),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text(formatDate(text),
                style: TextStyle(
                    fontSize: 16.px,
                    color: const Color.fromRGBO(51, 51, 51, 1),
                    fontWeight: FontWeight.w600)),
          ],
        ));
  }

  final dateFormat = DateFormat("y年M月");

  String formatDate(String text) {
    int year = int.parse(text.substring(0, 4));
    int month = int.parse(text.substring(4, 6));
    return dateFormat.format(DateTime(year, month, 1));
  }
}

class _ContentController extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;
  final DateTime? monthOfYear;

  final DictNode? chosenAccount;

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  _ContentController(this.context, {this.monthOfYear, this.chosenAccount});

  List<WaterUseResultModel>? waterUseResultModelList;

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    var data = {};
    if (StorageUtil.userInfo()?.data?.idCard != null) {
      data["idNumber"] = StorageUtil.userInfo()?.data?.idCard;
    }

    if (monthOfYear != null) {
      data["monthOfYear"] = dateFormatyyyyMM2.format(monthOfYear!);
    }
    if (chosenAccount?.code != null) {
      data["accId"] = int.parse(chosenAccount!.code!);
    }
    XinJiangWaterManageService()
        .waterApplyList(data, cancelToken: createCancelToken())
        .then((result) {
      if (disposed) {
        return;
      }
      waterUseResultModelList = result.data
              ?.map<WaterUseResultModel>(
                  (item) => WaterUseResultModel.fromJson(item))
              .toList() ??
          [];
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  Null _handlerError(Object error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  void onClickItemWaterUse(MyApply item) {
    if (item.waId == null) {
      return;
    }
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => MyWaterUseDetailPage(waId: item.waId!)))
        .then((result) {
      if (result == true) {}
    });
  }

  void onClickProcessWaterUse(MyApply item) {
    if (item.waId == null) {
      return;
    }
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => FlowWaterApplyInfoPage(
                  waId: item.waId!,
                )))
        .then((result) {
      if (result == true) {}
    });
  }

  @override
  void dispose() {
    Log.d("------_ContentController  dispose");
    super.dispose();
  }
}
