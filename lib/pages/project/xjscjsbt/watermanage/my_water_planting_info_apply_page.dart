//种植信息填写
import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input_small.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/request/xinjiang_water_manage_service.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/widget/bdh_steps2.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:oktoast/oktoast.dart';

import '../../../../utils/auto_dispose_state_extension.dart';
import '../../../../utils/image_util.dart';
import '../../../../utils/provider/view_state_widget.dart';
import 'const_dict.dart';
import 'model/water_planting_info_model.dart';
import 'my_water_planting_info_page.dart';

//种植信息填写
class MyWaterPlantingInfoApplyPage extends StatefulWidget {
  final WaterPlantingInfoItem item;
  const MyWaterPlantingInfoApplyPage({super.key, required this.item});

  @override
  State<MyWaterPlantingInfoApplyPage> createState() =>
      _WaterPlantingInfoApplyPageState();
}

class _WaterPlantingInfoApplyPageState
    extends State<MyWaterPlantingInfoApplyPage> with AutoDisposeStateMixin {
  late ScrollController _scrollController;

  late final _Controller controller;

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());

    controller = useChangeNotifier(_Controller(context, widget.item))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  PreferredSizeWidget _widgetAppBar() {
    return AppBar(
      toolbarHeight: kTextTabBarHeight,
      title: const Text("种植申请"),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _widgetAppBar(),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: controller.isLoading
              ? _widgetLoading(context)
              : _widgetBody(context)),
    );
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  //加载中
  Widget _widgetBody(BuildContext context) {
    return Column(
      children: [
        _widgetStep(context),
        SizedBox(height: 12.px),
        Expanded(child: _widgetList(context)),
        _widgetSubmitButton(),
        SizedBox(height: 12.px),
      ],
    );
  }

  Widget _widgetStep(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(15.px),
              bottomRight: Radius.circular(15.px)),
        ),
        child: BdhStepsHorizontal(
          steps: plantingApplyStepDict,
          activeIndex: 1,
          outerIconSize: 30.px,
          innerIconSize: 24.px,
        ));
  }

  Widget _widgetSubmitButton() {
    return Center(
      child: BdhTextButton(
        width: 327.px,
        height: 40.px,
        text: "保存",
        textFontWeight: FontWeight.w500,
        textSize: 16.px,
        borderRadius: BorderRadius.all(Radius.circular(22.px)),
        backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
        disableBackgroundColor: const Color.fromRGBO(30, 192, 106, 1),
        pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
        foregroundColor: Colors.white,
        disableForegroundColor: Colors.white,
        pressedForegroundColor: Colors.white,
        onPressed: _onClickSubmit,
      ),
    );
  }

  Widget _widgetList(BuildContext context) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverToBoxAdapter(
          child: WidgetArea(
            formKey: _formKey,
            item: widget.item,
            resultList: controller.resultList,
            cropList: controller.cropList,
            onCropSave: controller.onCropSave,
            onAreaSave: controller.onAreaSave,
          ),
        ),
        SliverPadding(padding: EdgeInsets.only(top: 10.px)),
      ],
    );
  }

  void _onClickSubmit() {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      if (controller.resultList.isEmpty) {
        return;
      }
      double totalArea = 0;
      totalArea = controller.resultList.fold(totalArea, (v, e) {
        return double.parse(e["area"]) + v;
      });
      if (totalArea != widget.item.plotArea) {
        showToast("种植面积之和必须等于地块面积");
        return;
      }
      controller.onClickSubmit();
    }
  }
}

class CropItemWidget extends StatelessWidget {
  final List<DictNode> cropList;
  final DictNode? cropInitialValue;
  final String? areaInitValue;
  final int index;
  final IndexValueChanged<DictNode?> onCropSave;
  final IndexValueChanged<String?> onAreaSave;
  const CropItemWidget(
      {super.key,
      required this.cropList,
      this.cropInitialValue,
      this.areaInitValue,
      required this.index,
      required this.onCropSave,
      required this.onAreaSave});

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    width: 1.px,
                    color: const Color.fromRGBO(226, 235, 231, 0.6)))),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    child: BdhSingleDataPicker(
                  initialValue: cropInitialValue,
                  showBottomLine: false,
                  showArrow: false,
                  checkState: true,
                  textAlign: TextAlign.left,
                  valueSpace: 20.px,
                  minHeight: 40.px,
                  // valueStart: "#",
                  titleStyle: TextStyle(
                      fontSize: 16.px,
                      color: const Color.fromRGBO(51, 51, 51, 0.4)),
                  placeholderStyle: TextStyle(
                      fontSize: 16.px,
                      color: const Color.fromRGBO(51, 51, 51, 0.4)),
                  textStyle: TextStyle(
                      fontSize: 16.px,
                      color: const Color.fromRGBO(51, 51, 51, 1),
                      fontWeight: FontWeight.w600),
                  placeholder: "请选择种植作物",
                  suffixBuilder: (context, field) {
                    return GestureDetector(
                      onTap: () {
                        context
                            .findAncestorStateOfType<
                                _WaterPlantingInfoApplyPageState>()!
                            .controller
                            .onClickDeleteCrop(index);
                      },
                      child: Text(
                        "删除作物",
                        style: TextStyle(
                            color: Colors.red,
                            fontSize: 14.px,
                            fontWeight: FontWeight.w300),
                      ),
                    );
                  },
                  item: FormItem(
                      title: "种植作物:", isRequired: false, data: cropList),
                  onSaved: (v) {
                    onCropSave.call(index, v);
                  },
                  onChange: (v) {
                    onCropSave.call(index, v);
                  },
                  validator: (v) {
                    if (v == null) {
                      return "种植作物不能为空";
                    }
                    return null;
                  },
                )),
              ],
            ),
            BdhTextInputSmall(
              showBottomLine: false,
              textAlign: TextAlign.left,
              valueSpace: 20.px,
              minHeight: 40.px,
              valueStart: "#",
              fontSize: 16.px,
              textInputType: TextInputType.number,
              placeHolder: "请输入种植面积",
              titleStyle: TextStyle(
                  fontSize: 16.px,
                  color: const Color.fromRGBO(51, 51, 51, 0.4)),
              textStyle: TextStyle(
                  fontSize: 16.px,
                  color: const Color.fromRGBO(51, 51, 51, 1),
                  fontWeight: FontWeight.w600),
              initialValue: areaInitValue?.toString(),
              item:
                  FormItem(title: "种植面积:", isRequired: false, isCanEdit: true),
              suffixBuilder: (context, filed) {
                return Text(" 亩",
                    style: TextStyle(
                        fontSize: 16.px,
                        color: const Color.fromRGBO(51, 51, 51, 1),
                        fontWeight: FontWeight.w600));
              },
              onSaved: (v) {
                onAreaSave.call(index, v);
              },
              onChange: (v) {
                onAreaSave.call(index, v);
              },
              validator: (v) {
                if (v == null) {
                  return "种植面积不能为空";
                }
                try {
                  double c = double.parse(v);
                  if (c <= 0) {
                    return "种植面积必须大于 0";
                  }
                } catch (e, s) {
                  Log.e(e.toString(), error: e, stackTrace: s);
                }
                return null;
              },
            )
          ],
        ));
  }
}

class WidgetArea extends StatelessWidget {
  final GlobalKey formKey;
  final List<Map> resultList;
  final WaterPlantingInfoItem item;
  final IndexValueChanged<DictNode?> onCropSave;
  final IndexValueChanged<String?> onAreaSave;
  final List<DictNode> cropList;
  const WidgetArea(
      {super.key,
      required this.formKey,
      required this.resultList,
      required this.onCropSave,
      required this.onAreaSave,
      required this.item,
      required this.cropList});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 12.px, right: 12.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Stack(
        children: [
          Positioned(
              right: 0,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.px), // 圆角矩形裁剪
                child: SvgPicture.asset(
                    alignment: Alignment.center,
                    fit: BoxFit.cover,
                    width: 100.px,
                    ImageHelper.wrapAssets("bg_water_apply_item.svg")),
              )),
          Padding(
              padding: EdgeInsets.only(
                  left: 20.px, right: 20.px, top: 20.px, bottom: 10.px),
              child: Column(
                children: [
                  _widgetAreaNoTitle(),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetAreaNo(),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetAreaInfo(),
                  SizedBox(
                    height: 10.px,
                  ),
                  if (resultList.isNotEmpty)
                    Container(
                        decoration: BoxDecoration(
                            border: Border(
                                bottom: BorderSide(
                                    width: 1.px,
                                    color: const Color.fromRGBO(
                                        226, 235, 231, 0.6))))),
                  _widgetCorpList(),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetAddCropButton(context)
                ],
              ))
        ],
      ),
    );
  }

  Widget _widgetAddCropButton(BuildContext context) {
    return Center(
      child: BdhTextButton(
        width: 220.px,
        height: 40.px,
        text: "添加作物",
        textFontWeight: FontWeight.w500,
        textSize: 16.px,
        borderRadius: BorderRadius.all(Radius.circular(22.px)),
        backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
        disableBackgroundColor: const Color.fromRGBO(30, 192, 106, 1),
        pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
        foregroundColor: Colors.white,
        disableForegroundColor: Colors.white,
        pressedForegroundColor: Colors.white,
        onPressed: () {
          context
              .findAncestorStateOfType<_WaterPlantingInfoApplyPageState>()!
              .controller
              .onClickAddCrop();
        },
      ),
    );
  }

  Widget _widgetCorpList() {
    var widgetList = resultList
        .asMap()
        .map<int, Widget>((index, node) => MapEntry(
            index,
            CropItemWidget(
              key: ValueKey(index),
              index: index,
              cropList: cropList,
              cropInitialValue: node["crop"],
              areaInitValue: node["area"],
              onAreaSave: onAreaSave,
              onCropSave: onCropSave,
            )))
        .values
        .toList();
    return Form(
        key: formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: Column(
          children: widgetList,
        ));
  }

  Widget _widgetAreaNoTitle() {
    return Row(
      children: [
        SvgPicture.asset(
            alignment: Alignment.center,
            fit: BoxFit.cover,
            width: 28.px,
            height: 28.px,
            ImageHelper.wrapAssets("ic_water_planting_apply_title.svg")),
        SizedBox(
          width: 10.px,
        ),
        Text(
          "土地编号:",
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 0.4),
              fontSize: 16.px,
              fontWeight: FontWeight.w500),
        )
      ],
    );
  }

  Widget _widgetAreaNo() {
    return Row(
      children: [
        Text(
          item.plotNo ?? "",
          style: TextStyle(
              fontFamily: "BEBAS",
              color: const Color.fromRGBO(0, 152, 91, 1),
              fontSize: 24.px,
              fontWeight: FontWeight.w400),
        )
      ],
    );
  }

  Widget _widgetAreaInfo() {
    return Container(
      padding:
          EdgeInsets.only(left: 20.px, right: 20.px, top: 12.px, bottom: 12.px),
      decoration: BoxDecoration(
        color: const Color.fromRGBO(243, 245, 249, 0.4),
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Flexible(
                  flex: 1,
                  fit: FlexFit.tight,
                  child: Text.rich(
                    TextSpan(children: [
                      const TextSpan(
                          text: "类型: ",
                          style: TextStyle(
                              color: Color.fromRGBO(51, 51, 51, 0.4))),
                      TextSpan(
                          text: item.plotType ?? "-",
                          style: const TextStyle(
                              color: Color.fromRGBO(51, 51, 51, 1))),
                    ]),
                    style:
                        TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500),
                  )),
              Flexible(
                  flex: 1,
                  fit: FlexFit.tight,
                  child: Text.rich(
                    TextSpan(children: [
                      const TextSpan(
                          text: "面积: ",
                          style: TextStyle(
                              color: Color.fromRGBO(51, 51, 51, 0.4))),
                      TextSpan(
                          text: "${item.plotArea ?? "-"}亩",
                          style: const TextStyle(
                              color: Color.fromRGBO(51, 51, 51, 1))),
                    ]),
                    style:
                        TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500),
                  ))
            ],
          ),
          SizedBox(
            height: 10.px,
          ),
          Row(
            children: [
              Text.rich(
                TextSpan(children: [
                  const TextSpan(
                      text: "位置: ",
                      style: TextStyle(color: Color.fromRGBO(51, 51, 51, 0.4))),
                  TextSpan(
                      text: address,
                      style: const TextStyle(
                          color: Color.fromRGBO(51, 51, 51, 1))),
                ]),
                style: TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500),
              )
            ],
          )
        ],
      ),
    );
  }

  String get address {
    var str = "";
    if (item.regimentCanal != null) {
      str += "${item.regimentCanal}团";
    }
    if (item.companyCanal != null) {
      str += "${item.companyCanal}连";
    }
    if (item.mainCanal != null) {
      str += "${item.mainCanal}干";
    }
    if (item.branchCanal != null) {
      str += "${item.branchCanal}支";
    }
    if (item.lateralCanal != null) {
      str += "${item.lateralCanal}斗";
    }
    if (str.isEmpty) str = "-";
    return str;
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  final WaterPlantingInfoItem item;

  _Controller(this.context, this.item);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  List<DictNode> cropList = [];

  List<Map> resultList = [];

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    XinJiangWaterManageService()
        .cropList({}, cancelToken: createCancelToken()).then((result) {
      Log.d("result is $result");

      cropList = (result.data as List)
          .map<DictNode>((item) =>
              DictNode(code: item["raiseCropsCd"], name: item["raiseCropsNm"]))
          .toList();

      resultList = item.attrs?.map<Map>((result) {
            return {
              "crop": DictNode(code: result.cropCode, name: result.cropName),
              "area": result.plotArea?.toString()
            };
          }).toList() ??
          [];

      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  void onClickAddCrop() {
    resultList.add({});
    notifyListeners();
  }

  void onClickDeleteCrop(int index) {
    Log.d("onClickDeleteCrop $index");
    resultList.removeAt(index);
    notifyListeners();
  }

  void onCropSave(int index, DictNode? value) {
    resultList[index]["crop"] = value;
  }

  void onAreaSave(int index, String? value) {
    resultList[index]["area"] = value;
  }

  void onClickSubmit() {
    if (resultList.isEmpty) {
      return;
    }
    var attrs = resultList.map<Map>((item) {
      var map = {};
      var node = item["crop"] as DictNode?;
      map["plotArea"] = item["area"];
      map["cropCode"] = node?.code;
      map["cropName"] = node?.name;
      return map;
    }).toList();

    var data = {
      "statYear": DateTime.now().year, //年
      "orgCode": item.orgCode, //单位
      "growerType": item.growerType, //类型：1农户；2企业
      "growerName": item.growerName, //农户姓名或企业名
      "idNumber": item.idNumber, //农户或代办人身份证
      "operatorName": item.operatorName, //代办人名
      "operatorPhone": item.operatorPhone, //代办人手机号
      "plotNo": item.plotNo, //新疆地块编码
      "plotName": item.plotName, //地块名
      "operatorIdNum": item.operatorIdNum,
      "farmerId": item.farmerId,
      "companyId": item.companyId,
      "attrs": attrs
    };
    var requestFunction = XinJiangWaterManageService().insertPlantInfo;
    if (item.piId != null) {
      data["piId"] = item.piId;
      requestFunction = XinJiangWaterManageService().updatePLantInfo;
    }
    Log.d("data is $data");
    showLoading(context, content: "正在提交 ", barrierDismissible: false);
    requestFunction(data, cancelToken: createCancelToken()).then((result) {
      if (!context.mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);
      if (result.success == true && result.code == 0) {
        showToast("保存成功");
        Navigator.maybePop(context, true);
      }
    }).onError((error, stackTrace) {
      _handlerError(error, stackTrace, errorDo: () {
        BrnLoadingDialog.dismiss(context);
      });
    });
  }

  Null _handlerError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }
}
