import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/request/xinjiang_water_manage_service.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';

import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import 'const_dict.dart';
import 'model/water_apply_detail_model.dart';

//申请记录详情
class WaterApplyDetailPage extends StatefulWidget {
  final num waId;
  final bool showApproval;
  const WaterApplyDetailPage(
      {super.key, required this.waId, required this.showApproval});

  @override
  State<WaterApplyDetailPage> createState() => _WaterApplyDetailPageState();
}

class _WaterApplyDetailPageState extends State<WaterApplyDetailPage>
    with AutoDisposeStateMixin {
  late final ScrollController _scrollController;

  late final _Controller controller;

  @override
  void initState() {
    super.initState();
    _scrollController = useScrollController(ScrollController());
    controller = useChangeNotifier(_Controller(context, widget.waId))
      ..addListener(() {
        setState(() {});
      })
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("记录详情"),
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: SafeArea(
            top: false,
            child: controller.isLoading
                ? _widgetLoading(context)
                : Stack(
                    fit: StackFit.expand,
                    children: [
                      _widgetList(),
                      if (widget.showApproval)
                        Positioned(bottom: 0, child: _widgetSubmitButton())
                    ],
                  )));
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  String get plotText {
    // if (controller.model?.plotType == 1) {
    //   return "身份地";
    // } else if (controller.model?.plotType == 2) {
    //   return "经营地";
    // }
    return controller.model?.plotType ?? "-";
  }

  String get recordAmount {
    var amount = controller.model?.amount;

    if (amount == null) {
      double a = 0;
      amount = controller.model?.priceRecords?.fold(a, (p, v) {
        return (p ?? 0) + (v.amount ?? 0);
      });
    }
    return amount == null ? "-" : "${amount.toStringAsFixed(2)}元";
  }

  Widget _widgetList() {
    return SingleChildScrollView(
      controller: _scrollController,
      child: Column(
        children: [
          ColoredBox(
              color: Colors.white,
              child: Column(
                children: [
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("缴费户名", controller.model?.growerName ?? ""),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("所在单位", controller.model?.orgName ?? ""),
                  SizedBox(
                    height: 10.px,
                  ),
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("土地编号", controller.model?.plotNo ?? ""),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("类型", plotText),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("种植信息",
                      "${controller.model?.cropName}  ${controller.model?.plotArea}亩"),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("位置信息", "${controller.model?.canal}"),
                  SizedBox(
                    height: 10.px,
                  ),
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("本次用水量",
                      "${controller.model?.actualWaterUsage ?? "-"}${controller.model?.actualWaterUsage == null ? "" : " m³"}"),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("累计用水量",
                      "${controller.model?.accumWaterConsume ?? "-"}${controller.model?.accumWaterConsume == null ? "" : " m³"}"),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("本次金额", recordAmount),
                  SizedBox(
                    height: 10.px,
                  ),
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("申请放水日期", usageTimeText),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary("接水员", controller.model?.receiverName ?? "-"),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary(
                      "配水员", controller.model?.distributorName ?? "-"),
                  SizedBox(
                    height: 10.px,
                  ),
                  _widgetSummary(
                      "放水状态", controller.model?.drainageStatus ?? ""),
                  SizedBox(
                    height: 10.px,
                  ),
                  Divider(
                    color: const Color.fromRGBO(51, 51, 51, 0.05),
                    height: 1.px,
                    indent: 20.px,
                    endIndent: 20.px,
                  ),
                  // ..._widgetPriceRecords(),
                  //  SizedBox(
                  //    height: 20.px,
                  //  ),
                ],
              )),
          SizedBox(
            height: 6.px,
          ),
        ],
      ),
    );
  }

  String get usageTimeText {
    return controller.model?.usageBeginDate ??
        controller.model?.actualUsageDate ??
        controller.model?.usageDate ??
        "-";
  }

  List<Widget> _widgetPriceRecords() {
    return controller.model?.priceRecords
            ?.asMap()
            .map<int, List<Widget>>((index, item) {
              var list = <Widget>[
                SizedBox(
                  height: 10.px,
                ),
                _widgetSummary3(
                    item.priceLevelDesc ?? priceRecordTitleDict[index],
                    "${item.waterCons}m³",
                    "${item.amount}元")
              ];

              return MapEntry(index, list);
            })
            .values
            .toList()
            .fold<List<Widget>>(<Widget>[],
                (List<Widget> list, List<Widget> item) {
              list.addAll(item);
              return list;
            }) ??
        [];
  }

  Widget _widgetSummary(String title, String text, {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
          width: 100.px,
          child: Text(
            title,
            style: TextStyle(
                color: const Color.fromRGBO(51, 51, 51, 0.4),
                fontWeight: FontWeight.w500,
                fontSize: 12.px),
          ),
        ),
        Expanded(
            child: Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        )),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetSummary3(String title, String text, String subText,
      {Color? textColor}) {
    return Row(
      children: [
        SizedBox(
          width: 20.px,
        ),
        SizedBox(
          width: 220.px,
          child: Text(
            title,
            style: TextStyle(
                color: const Color.fromRGBO(51, 51, 51, 0.4),
                fontWeight: FontWeight.w500,
                fontSize: 12.px),
          ),
        ),
        Expanded(
            child: Text(
          text,
          style: TextStyle(
              color: textColor ?? const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        )),
        Text(
          subText,
          style: TextStyle(
              color: const Color.fromRGBO(51, 51, 51, 1),
              fontWeight: FontWeight.w600,
              fontSize: 12.px),
        ),
        SizedBox(
          width: 20.px,
        ),
      ],
    );
  }

  Widget _widgetSubmitButton() {
    return Padding(
        padding: EdgeInsets.only(left: 24.px, right: 24.px, bottom: 12.px),
        child: BdhTextButton(
          width: 327.px,
          height: 40.px,
          text: '完成放水',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(22.px)),
          backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          disableBackgroundColor: const Color.fromRGBO(30, 192, 106, 1),
          pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: controller.onClickSubmit,
        ));
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;
  final num waId;

  _Controller(this.context, this.waId);

  //加载状态
  LoadingStatus _loadingStatus = LoadingStatus.init;
  LoadingStatus get loadingStatus => _loadingStatus;

  bool get isLoading =>
      _loadingStatus == LoadingStatus.loading ||
      _loadingStatus == LoadingStatus.init;

  WaterApplyDetailModel? model;

  //加载数据
  void loadData() {
    if (_loadingStatus == LoadingStatus.loading) {
      return;
    }
    XinJiangWaterManageService()
        .waterHandleInfo(waId, cancelToken: createCancelToken())
        .then((result) {
      model = WaterApplyDetailModel.fromJson(result.data);
      _loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(_handlerError);
  }

  Null _handlerError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }
    errorDo?.call();
    showToast(request.message ?? "请求失败,请稍后再试");
  }

  void onClickSubmit() {}
}
