import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/request/xinjiang_water_manage_service.dart';
import 'package:bdh_smart_agric_app/pages/project/xjscjsbt/watermanage/water_containing_apply_review_page.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import 'water_containing_tab_page.dart';
import 'widget/notice_alert_dialog.dart';

//接水管理
class WaterContainingPage extends StatefulWidget {
  const WaterContainingPage({super.key});

  @override
  State<StatefulWidget> createState() => _WaterContainingPageState();
}

class _WaterContainingPageState extends State<WaterContainingPage>
    with TickerProviderStateMixin, AutoDisposeStateMixin {
  late final TabController _tabController;
  int _tabIndex = 1;
  @override
  void initState() {
    super.initState();

    _tabController = useTabController(
        TabController(initialIndex: _tabIndex, length: 3, vsync: this));

    loadData();
  }

  void loadData() {
    var data = {
      "auditLevel": 1,
      "page": 1,
      "rows": 1,
    };

    XinJiangWaterManageService()
        .waterApplyHandleReceiverPendingNumber(data,
            cancelToken: useCancelToken(CancelToken()))
        .then((result) {
      if (result.code == 0 && result.success == true) {
        var count = result.data;

        if (count > 0) {
          showApplyReviewDialog(count);
        }
      }
    }).onError(_handlerError);
  }

  void showApplyReviewDialog(int count) async {
    var result =
        await showNoticeAlertDialog(context, text: "$count个农户已经提交用水申请\n快去处理吧。");
    if (result == true) {
      if (!context.mounted) {
        return;
      }
      Navigator.of(context)
          .push(CupertinoPageRoute(
              builder: (_) => const WaterContainingApplyReviewPage()))
          .then((result) {
        if (result == true) {}
      });
    }
  }

  Null _handlerError(Object? error, StackTrace stackTrace,
      {VoidCallback? errorDo}) {
    Log.e("_handlerError error", error: error, stackTrace: stackTrace);
    var request = RequestException.handleError(error);
    errorDo?.call();
    if (request.isCancel) {
      return;
    }
    if (!context.mounted) {
      return;
    }

    showToast(request.message ?? "请求失败,请稍后再试");
  }

  void onClickDeal() {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => const WaterContainingApplyReviewPage()))
        .then((result) {
      if (result == true) {}
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("接水管理"),
        actions: [
          GestureDetector(
            onTap: onClickDeal,
            child: Text("去处理",
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                  color: const Color.fromRGBO(30, 192, 106, 1),
                )),
          ),
          SizedBox(
            width: 15.px,
          )
        ],
        bottom: PreferredSize(
            preferredSize: Size.fromHeight(44.px),
            child: TDTabBar(
                indicatorColor: const Color.fromRGBO(10, 174, 108, 1),
                controller: _tabController,
                labelColor: const Color.fromRGBO(10, 174, 108, 1),
                dividerColor: Colors.transparent,
                showIndicator: true,
                onTap: (idx) {
                  setState(() {
                    _tabIndex = idx;
                  });
                },
                tabs: const [
                  TDTab(
                    text: "用水申请",
                  ),
                  TDTab(
                    text: "放水申请",
                  ),
                  TDTab(
                    text: "完成",
                  )
                ])),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(top: false, child: _widgetBody()),
    );
  }

  Widget _widgetBody() {
    if (_tabIndex == 0) {
      return WaterContainingTabPage(
        key: ValueKey(_tabIndex),
        auditLevel: 2,
        showBottomApprovalAll: false,
        showReject: true,
        showApproval: false,
        itemClickable: true,
        showFlow: true,
        showDistributionName: false,
        showFallback: false,
      );
    } else if (_tabIndex == 1) {
      return WaterContainingTabPage(
        key: ValueKey(_tabIndex),
        auditLevel: 5,
        showBottomApprovalAll: true,
        showReject: false,
        showApproval: false,
        itemClickable: true,
        showFlow: false,
        showDistributionName: false,
        showFallback: false,
      );
    } else if (_tabIndex == 2) {
      return WaterContainingTabPage(
        key: ValueKey(_tabIndex),
        auditLevel: 6,
        showBottomApprovalAll: false,
        showReject: false,
        showApproval: false,
        itemClickable: true,
        showFlow: false,
        showDistributionName: true,
        showFallback: false,
      );
    }
    return const SizedBox.shrink();
  }
}
