import 'package:bdh_smart_agric_app/model/bank_model.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';

import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bank_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import 'bank_add_card.dart';
import 'bank_card.dart';

//我的银行卡
class BankListInfoPage extends StatelessWidget {
  //是否是大兴
  final bool isDaxing;

  const BankListInfoPage({super.key, this.isDaxing = false});
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("我的银行卡"),
        ),
        backgroundColor: BDHColor.backgroundColor,
        body: _ContentWidget(isDaxing: isDaxing));
  }
}

class _ContentWidget extends StatefulWidget {
  final bool isDaxing;
  const _ContentWidget({required this.isDaxing});

  @override
  State<_ContentWidget> createState() => __ContentWidgetState();
}

class __ContentWidgetState extends State<_ContentWidget>
    with AutoDisposeStateMixin {
  final ScrollController _scrollController = ScrollController();

  BankLoadState bankLoadState = BankLoadState();

  late BankService _bankService;

  @override
  void initState() {
    super.initState();

    _bankService =
        widget.isDaxing ? BankService.daxing() : BankService.common();

    WidgetsBinding.instance.addPostFrameCallback((d) {
      _reloadBankListInfo();
    });

    //widget dispose时需要释放_scrollController
    autoDispose(() {
      _scrollController.dispose();
    });
  }

  //初次加载银行卡列表和相关字典
  Future<BankLoadState> _loadBankListInfoState() async {
    //CancelToken cancelToken1 = ;
    //CancelToken cancelToken2 = useCancelToken(CancelToken());
    //CancelToken cancelToken3 = useCancelToken(CancelToken());

    //widget dispose时需要停止请求
    // autoDispose(() {
    //   cancelToken1.cancel();
    //   cancelToken2.cancel();
    //   cancelToken3.cancel();
    // });

    //获取银行卡类型字典
    var result1 = await _bankService.bankcardTypeNo(
        cancelToken: useCancelToken(CancelToken()));

    Log.d("request bankcardTypeNo ${result1.toString()}}");
    if (!mounted) {
      return BankLoadState(status: LoadingStatus.cancel);
    }
    var state1 =
        result1.transform<BankLoadState, RequestException>((response, error) {
      Log.d(
          "request bankcardTypeNo ${response?.toString()} ${error.toString()}");
      if (error != null) {
        if (error.isCancel) {
          Log.d("request bankcardTypeNo cancel ");
          return ResultSuccess(BankLoadState(status: LoadingStatus.cancel));
        }
        showToast(error.message ?? "请求失败,请稍后重试");
        return ResultSuccess(
            BankLoadState(status: LoadingStatus.error, error: error.message));
      }

      if (response != null && response.code == 0 && response.success == true) {
        return ResultSuccess(BankLoadState(
            status: LoadingStatus.loading,
            bankcardTypeNoList: response.data ?? []));
      }

      return ResultSuccess(
          BankLoadState(status: LoadingStatus.error, error: response?.msg));
    }).success!;

    if (state1.status == LoadingStatus.error ||
        state1.status == LoadingStatus.cancel) {
      return state1;
    }

    //获取银行名称字典
    var result2 =
        await _bankService.bankName(cancelToken: useCancelToken(CancelToken()));
    if (!mounted) {
      return BankLoadState(status: LoadingStatus.cancel);
    }
    var state2 =
        result2.transform<BankLoadState, RequestException>((response, error) {
      if (error != null) {
        if (error.isCancel) {
          return ResultSuccess(BankLoadState(status: LoadingStatus.cancel));
        }
        showToast(error.message ?? "请求失败,请稍后重试");
        return ResultSuccess(
            BankLoadState(status: LoadingStatus.error, error: error.message));
      }

      if (response != null && response.code == 0 && response.success == true) {
        return ResultSuccess(BankLoadState(
            status: LoadingStatus.loading, bankNameList: response.data ?? []));
      }

      return ResultSuccess(
          BankLoadState(status: LoadingStatus.error, error: response?.msg));
    }).success!;

    if (state2.status == LoadingStatus.error ||
        state2.status == LoadingStatus.cancel) {
      return state2;
    }

    //获取银行卡列表
    var result3 = await _bankService.queryBank(
        cancelToken: useCancelToken(CancelToken()));
    if (!mounted) {
      return BankLoadState(status: LoadingStatus.cancel);
    }
    var state3 =
        result3.transform<BankLoadState, RequestException>((response, error) {
      if (error != null) {
        if (error.isCancel) {
          return ResultSuccess(BankLoadState(status: LoadingStatus.cancel));
        }
        showToast(error.message ?? "请求失败,请稍后重试");
        return ResultSuccess(
            BankLoadState(status: LoadingStatus.error, error: error.message));
      }

      if (response != null && response.code == 0 && response.success == true) {
        return ResultSuccess(BankLoadState(
            status: LoadingStatus.loading, bankList: response.data ?? []));
      }

      return ResultSuccess(
          BankLoadState(status: LoadingStatus.error, error: response?.msg));
    }).success!;

    if (state3.status == LoadingStatus.error ||
        state3.status == LoadingStatus.cancel) {
      return state3;
    }

    //列表为空
    bool isEmpty = state3.bankList.isEmpty;
    return BankLoadState(
        status: LoadingStatus.success,
        isEmpty: isEmpty,
        bankcardTypeNoList: state1.bankcardTypeNoList,
        bankNameList: state2.bankNameList,
        bankList: state3.bankList);
  }

//刷新银行卡列表
  void _reloadBankListInfo() {
    setState(() {
      bankLoadState = BankLoadState();
    });
    _loadBankListInfoState().then((model) {
      if (!mounted) {
        return;
      }
      setState(() {
        bankLoadState = model;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (bankLoadState.status == LoadingStatus.success) {
      return _body(context);
    } else {
      return _loading(context);
    }
  }

  Widget? _itemBuilder(BuildContext context, int index) {
    Log.d("_itemBuilder ${bankLoadState.bankList.length} $index");
    Widget child = BankCard(
      index: index,
      bankLoadState: bankLoadState,
      bankNode: bankLoadState.bankList[index],
      onDelete: _onBankDelete,
      onSetDefault: _onBankDefault,
    );

    return GestureDetector(
      onTap: () {
        _onBankClick(index: index);
      },
      child: child,
    );
  }

  //点击添加银行卡
  void _onBankClick({int? index}) async {
    BankNode? bankNode;
    if (index != null) {
      bankNode = bankLoadState.bankList[index];
    }
    var result = await showModalBottomSheet<bool>(
        context: context,
        useSafeArea: true,
        isScrollControlled: true,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: BankAddCardPage(
              bankLoadState: bankLoadState,
              originNode: bankNode,
              isDaxing: widget.isDaxing,
            ),
          );
        });

    Log.d("_onBankClick result is $result");
    if (result == true) {
      _reloadBankListInfo();
    }
  }

  //点击删除银行卡
  void _onBankDelete(int index, BankNode node) async {
    var bankAccountId = node.bankAccountId?.toString() ?? "";
    if (bankAccountId.isEmpty) {
      return;
    }
    BrnLoadingDialog.show(context,
        content: "删除中..  ", barrierDismissible: false);
    var result = await _bankService.removeBankcard(
        bankAccountId: bankAccountId.toString(),
        cancelToken: useCancelToken(CancelToken()));
    if (!mounted) {
      return;
    }
    BrnLoadingDialog.dismiss(context);

    result.onError((error) {
      if (error.isCancel) {
        //如果是用户取消则什么都不需要做
        return;
      }
      showToast(error.message ?? "请求失败,请稍后重试");
    }).onSuccess((response) {
      if (response.code == 0 && response.success == true) {
        showToast("删除成功");
        _reloadBankListInfo();
      }
    });
  }

  //点击设置银行卡默认
  void _onBankDefault(int index, BankNode node) async {
    var bankAccountId = node.bankAccountId?.toString() ?? "";
    if (bankAccountId.isEmpty) {
      return;
    }
    BrnLoadingDialog.show(context,
        content: "设置中..  ", barrierDismissible: false);
    var result = await _bankService.setDefault(
        bankAccountId: bankAccountId.toString(),
        cancelToken: useCancelToken(CancelToken()));

    if (!mounted) {
      return;
    }
    BrnLoadingDialog.dismiss(context);

    result.onError((error) {
      if (error.isCancel) {
        //如果是用户取消则什么都不需要做
        return;
      }
      showToast(error.message ?? "请求失败,请稍后重试");
    }).onSuccess((response) {
      if (response.code == 0 && response.success == true) {
        showToast("设置成功");
        _reloadBankListInfo();
      }
    });
  }

  //加载中
  Widget _loading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  //银行卡列表为空
  Widget _empty(BuildContext context) {
    return Column(
      children: [
        const Expanded(
            child: BdhEmptyView(
          tipInfo: "您未添加任何银行卡",
        )),
        Padding(
          padding: EdgeInsets.only(bottom: 20.px),
          child: _addBankButtonWidget(),
        )
      ],
    );
  }

  //添加银行卡按钮
  Widget _addBankButtonWidget() {
    return Center(
        child: Padding(
            padding: EdgeInsets.only(top: 14.px),
            child: TextButton(
              onPressed: _onBankClick,
              style: ButtonStyle(
                  splashFactory: NoSplash.splashFactory,
                  minimumSize: WidgetStateProperty.all(Size(200.px, 40.px)),
                  backgroundColor: WidgetStateProperty.resolveWith((states) {
                    if (states.contains(WidgetState.disabled)) {
                      return Colors.white60;
                    } else if (states.contains(WidgetState.pressed)) {
                      return Colors.white24;
                    } else {
                      return Colors.white;
                    }
                  }),
                  shape: WidgetStateProperty.all(const RoundedRectangleBorder(
                      side: BorderSide.none,
                      borderRadius: BorderRadius.all(Radius.circular(20))))),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: EdgeInsets.only(right: 10.px, bottom: 1.px),
                    child: Icon(
                      Icons.add_circle,
                      size: 15.px,
                      color: const Color.fromRGBO(0, 152, 91, 1),
                    ),
                  ),
                  Text(
                    "添加银行卡",
                    strutStyle:
                        StrutStyle(fontSize: 15.px, forceStrutHeight: true),
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      letterSpacing: 0,
                      wordSpacing: 0,
                      height: 0,
                      fontSize: 15.px,
                      color: const Color.fromRGBO(44, 44, 52, 1),
                    ),
                  )
                ],
              ),
            )));
  }

  //银行卡列表
  Widget _body(BuildContext context) {
    var padding = MediaQuery.of(context).padding;
    Log.d("padding2 is ${padding.bottom}");
    if (bankLoadState.isEmpty) {
      return _empty(context);
    }
    return Scrollbar(
      controller: _scrollController,
      child: CustomScrollView(
        controller: _scrollController,
        slivers: [
          SliverPadding(
            padding: EdgeInsets.only(top: 15.px),
          ),
          SliverList.builder(
            itemBuilder: _itemBuilder,
            itemCount: bankLoadState.bankList.length,
          ),
          SliverToBoxAdapter(child: _addBankButtonWidget()),
          SliverPadding(
            padding: EdgeInsets.only(top: 10.px),
          ),
        ],
      ),
    );
  }
}
