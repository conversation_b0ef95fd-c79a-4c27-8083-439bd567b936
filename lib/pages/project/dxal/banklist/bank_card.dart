import 'dart:core';

import 'package:bdh_smart_agric_app/model/bank_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_swipe_action_cell/flutter_swipe_action_cell.dart';

const bankColorList = {
  //默认
  0: [Color.fromRGBO(35, 43, 46, 0.5), Color.fromRGBO(35, 43, 46, 1)],
  //建设银行 rgba(32, 122, 197, 1) rgba(19, 92, 152, 1)
  3: [Color.fromRGBO(32, 122, 197, 1), Color.fromRGBO(19, 92, 152, 1)],
  //农业银行
  4: [Color.fromRGBO(10, 174, 108, 1), Color.fromRGBO(2, 139, 93, 1)],
  33: [Color.fromRGBO(35, 43, 46, 0.5), Color.fromRGBO(35, 43, 46, 1)],
  //招商银行 rgba(242, 119, 122, 1) rgba(217, 64, 68, 1)
  34: [Color.fromRGBO(242, 119, 122, 1), Color.fromRGBO(217, 64, 68, 1)],
};

typedef ActionCallback = void Function(int index, BankNode bankNode);

class BankCardSwipe extends StatefulWidget {
  final Widget child;
  final int index;
  final BankLoadState bankLoadState;
  final BankNode bankNode;

  final ActionCallback onDelete;
  final ActionCallback onSetDefault;

  const BankCardSwipe(
      {super.key,
      required this.child,
      required this.index,
      required this.bankLoadState,
      required this.bankNode,
      required this.onDelete,
      required this.onSetDefault});

  @override
  State<BankCardSwipe> createState() => _BankCardSwipeState();
}

class _BankCardSwipeState extends State<BankCardSwipe> {
  late SwipeActionController controller;

  @override
  void initState() {
    controller = SwipeActionController();
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(bottom: 10.px),
        child: SwipeActionCell(
          controller: controller,
          index: widget.index,
          backgroundColor: Colors.transparent,
          // Required!
          key: ValueKey(widget.bankNode),
          // Animation default value below
          // deleteAnimationDuration: 400,
          selectedForegroundColor: Colors.black.withAlpha(30),
          trailingActions: [
            SwipeAction(
                title: "删除",
                backgroundRadius: 8.px,
                nestedAction: SwipeNestedAction(title: "确认"),
                onTap: (handler) async {
                  await handler(false);
                  //setState(() {});
                  widget.onDelete(widget.index, widget.bankNode);
                }),
            SwipeAction(
                title: "默认",
                backgroundRadius: 8.px,
                color: Colors.grey,
                onTap: (handler) async {
                  await handler(false);
                  //setState(() {});
                  widget.onSetDefault(widget.index, widget.bankNode);
                }),
          ],

          child: widget.child,
        ));
  }
}

//银行卡
class BankCard extends StatelessWidget {
  final BankLoadState bankLoadState;
  final BankNode bankNode;
  final int index;
  final ActionCallback onDelete;
  final ActionCallback onSetDefault;

  const BankCard(
      {super.key,
      required this.bankLoadState,
      required this.bankNode,
      required this.index,
      required this.onDelete,
      required this.onSetDefault});

  // Widget _defaultCardWidget() {
  //   return Card(
  //       elevation: 0,
  //       shape: const RoundedRectangleBorder(
  //           borderRadius: BorderRadius.all(Radius.circular(5))),
  //       color: Colors.black26,
  //       child: Padding(
  //         padding: EdgeInsets.only(
  //             top: 5.px, bottom: 5.px, left: 10.px, right: 10.px),
  //         child: Text(
  //           "默认卡",
  //           style: TextStyle(color: Colors.white60, fontSize: 12.px),
  //         ),
  //       ));
  // }

  @override
  Widget build(BuildContext context) {
    Widget child = Container(
        width: 351.px,
        height: 113.px,
        margin: EdgeInsets.only(left: 12.px, right: 12.px),
        decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                bankColorList[bankNode.bankName ?? 0]?[0] ??
                    const Color.fromRGBO(35, 43, 46, 0.5),
                bankColorList[bankNode.bankName ?? 0]?[1] ??
                    const Color.fromRGBO(35, 43, 46, 1)
              ],
            ),
            borderRadius: BorderRadius.circular(8.px)),
        child: Stack(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(left: 15.px, top: 15.px),
                  width: 40.px,
                  height: 40.px,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(40.px)),
                  child: Center(
                    child: Image.asset(
                        width: 25.px,
                        height: 25.px,
                        ImageHelper.wrapAssets(
                            "pay/bankLogo/${bankNode.bankName}.png"),
                        errorBuilder: (context, error, stackTrace) {
                      return const SizedBox.shrink();
                    }),
                  ),
                ),
                Padding(
                    padding: EdgeInsets.only(left: 15.px, top: 16.px),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(bottom: 4.px),
                          child: Text(
                            bankLoadState.getBankRealName(bankNode) ?? "",
                            strutStyle: StrutStyle(
                                fontSize: 16.px, forceStrutHeight: true),
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.only(bottom: 20.px),
                          child: Text(
                            bankLoadState.getBankTypeName(bankNode) ?? "",
                            strutStyle: StrutStyle(
                                fontSize: 12.px, forceStrutHeight: true),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12.px,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                        Text(
                          bankLoadState.getBankAccountHide(bankNode) ?? "",
                          strutStyle: StrutStyle(
                              fontSize: 21.px, forceStrutHeight: true),
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 21.px,
                              fontWeight: FontWeight.w400),
                        ),
                      ],
                    ))
              ],
            ),
            // bankNode.isDefault == 1
            //     ? Positioned(
            //         right: 10.px,
            //         top: 10.px,
            //         child: _defaultCardWidget(),
            //       )
            //     : const SizedBox.shrink()
          ],
        ));

    return BankCardSwipe(
      bankLoadState: bankLoadState,
      index: index,
      bankNode: bankNode,
      onDelete: onDelete,
      onSetDefault: onSetDefault,
      child: child,
    );
  }
}
