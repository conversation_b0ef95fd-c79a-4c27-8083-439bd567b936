import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_bank_list_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_ocr_bank_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_radio_group_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input_small.dart';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/bank_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/bank_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

//我的->银行卡->添加银行卡
class BankAddCardPage extends StatefulWidget {
  final BankLoadState bankLoadState;

  final BankNode? originNode;

  final bool isDaxing;

  const BankAddCardPage(
      {super.key,
      required this.bankLoadState,
      this.originNode,
      required this.isDaxing});

  @override
  State<BankAddCardPage> createState() => _BankAddCardPageState();
}

class _BankAddCardPageState extends State<BankAddCardPage>
    with SingleTickerProviderStateMixin, AutoDisposeStateMixin {
  final GlobalKey _formKey = GlobalKey<FormState>();

  //银行卡类型
  // 0 储蓄卡 1 信用卡 2 存折
  String? bankcardTypeNo;
  //银行卡名称 ID
  String? bankName;
  //存折照片路径
  String? bankcardPhotoPath;

  //银行卡ID
  String? bankAccountId;

  late final AnimationController _animationController;
  //银行卡账号或者存折号
  late final TextEditingController _bankAccountEditingController;
  //开户网点
  late final TextEditingController _networkEditingController;
  //联行号
  late final TextEditingController _lineNumberEditingController;

  late final BankService _bankService;
  late FocusScopeNode _bankAccountFocusNode;

  @override
  void initState() {
    _bankService =
        widget.isDaxing ? BankService.daxing() : BankService.common();
    bankAccountId = widget.originNode?.bankAccountId?.toString();
    //默认是储蓄卡
    bankcardTypeNo = widget.originNode?.bankcardTypeNo?.toString() ?? "0";

    bankName = widget.originNode?.bankName?.toString();
    //bankAccount = widget.originNode?.bankAccount?.toString();
    //network = widget.originNode?.network;
    //lineNumber = widget.originNode?.lineNumber;
    //bankcardPhotoPath = widget.originNode?.bankcardPhotoPath;

    _animationController =
        useAnimationController(AnimationController(vsync: this));
    _bankAccountEditingController = useTextController(TextEditingController(
        text: widget.originNode?.bankAccount?.toString()));
    _lineNumberEditingController = useTextController(
        TextEditingController(text: widget.originNode?.lineNumber));
    _networkEditingController = useTextController(
        TextEditingController(text: widget.originNode?.network));

    _bankAccountFocusNode = useFocusScopeNode(FocusScopeNode());

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheet(
        enableDrag: false,
        backgroundColor: Colors.white,
        animationController: _animationController,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.px)),
        ),
        onClosing: () {},
        builder: (context) {
          return Container(
            constraints: BoxConstraints(maxHeight: 524.px),
            padding: EdgeInsets.only(left: 15.px, right: 15.px, bottom: 20.px),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                          padding: EdgeInsets.only(left: 4.px),
                          child: Icon(
                            Icons.close,
                            size: 24.px,
                            color: Colors.transparent,
                          )),
                      const Spacer(),
                      Text(
                        "请填写本人银行信息",
                        strutStyle:
                            StrutStyle(fontSize: 16.px, forceStrutHeight: true),
                        style: TextStyle(
                            color: const Color.fromRGBO(44, 44, 52, 1),
                            fontSize: 16.px,
                            fontWeight: FontWeight.w600),
                      ),
                      const Spacer(),
                      GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Padding(
                              padding: EdgeInsets.only(right: 4.px),
                              child: Icon(
                                Icons.close,
                                size: 24.px,
                                color: const Color.fromRGBO(115, 116, 131, 1),
                              ))),
                    ],
                  ),
                ),
                Expanded(
                    child: CustomScrollView(
                        physics: const NeverScrollableScrollPhysics(),
                        slivers: [
                      SliverToBoxAdapter(
                        child: Form(
                            key: _formKey,
                            autovalidateMode: AutovalidateMode.disabled,
                            child: Column(
                              children: [
                                BdhRadioGroupPicker(
                                  key: const ValueKey("bankcardTypeNo"),
                                  iconSize: 18.px,
                                  initialValue: bankcardTypeNo,
                                  item: FormItem(
                                      title: "银行卡类别",
                                      isRequired: true,
                                      data: widget
                                          .bankLoadState.bankcardTypeNoList),
                                  fontSize: 14.px,
                                  // textInputType: TextInputType.number,
                                  onChange: (v) {
                                    //debugPrint("bankcardTypeNo on change $v");
                                    setState(() {
                                      bankcardTypeNo = v;
                                      if (v == "0" || v == "1") {
                                        bankcardPhotoPath = null;
                                      } else if (v == "2") {
                                        _lineNumberEditingController.text = "";
                                        _networkEditingController.text = "";
                                        //lineNumber = null;
                                        //network = null;
                                      }
                                    });
                                  },
                                ),
                                BdhBankListPicker(
                                  fontSize: 14.px,
                                  initialValue: widget.bankLoadState
                                      .getBankByCode(bankName),
                                  item: FormItem(
                                      title: "银行列表",
                                      isRequired: true,
                                      data: widget.bankLoadState.bankNameList),
                                  placeHolder: "请选择银行",
                                  showScan: bankcardTypeNo == "0",
                                  validator: (value) {
                                    if (value == null) {
                                      return "银行不能为空";
                                    }

                                    return null;
                                  },
                                  onChange: (v) {
                                    bankName = v.code;
                                  },
                                  onScan: (FormFieldState<DictNode> field) {
                                    var account =
                                        _bankAccountEditingController.text;
                                    if (account.isEmpty) {
                                      showToast("请输入银行卡号");
                                      return;
                                    }
                                    _scanBankName(account);
                                  },
                                ),
                                bankcardTypeNo == "2"
                                    ? BdhImagePicker(
                                        maxCount: 1,
                                        fontSize: 14.px,
                                        initialValue: bankcardPhotoPath == null
                                            ? []
                                            : [
                                                BDHFile(url: bankcardPhotoPath!)
                                              ],
                                        item: FormItem(
                                            title: "上传存折卡号页", isRequired: true),
                                        showTitle: true,
                                        validator: (value) {
                                          if (value?.isEmpty ?? false) {
                                            return "存折必须上传卡号页照片";
                                          }
                                          return null;
                                        },
                                        onChange: (List<BDHFile>? fileList) {
                                          Log.d(
                                              "fileList: ${fileList?.length}");
                                          if (fileList?.isNotEmpty ?? false) {
                                            bankcardPhotoPath =
                                                fileList?.first.url;
                                          } else {
                                            bankcardPhotoPath = null;
                                          }
                                        },
                                      )
                                    : const SizedBox.shrink(),
                                BdhOcrBankPicker(
                                  fontSize: 14.px,
                                  item: FormItem(
                                      title: bankcardTypeNo == "2"
                                          ? "存折号"
                                          : "银行卡号",
                                      isRequired: true),
                                  placeHolder: bankcardTypeNo == "2"
                                      ? "请填写存折号"
                                      : "请填写银行卡号",
                                  initialValue:
                                      _bankAccountEditingController.text,
                                  controller: _bankAccountEditingController,
                                  showScan: bankcardTypeNo == "0" ||
                                      bankcardTypeNo == "1",
                                  focusNode: _bankAccountFocusNode,
                                  onScanSuccess: (v) {
                                    Log.d("onScanSuccess $v");
                                    if (v != null) {
                                      _bankAccountFocusNode.unfocus();
                                      _scanBankName(v);
                                    }
                                  },
                                  onChange: (v) {
                                    Log.d("bankAccount on change2 $v");
                                    //bankAccount = v;
                                    //if (v != null) {
                                    //  _getBankName(v);
                                    //}
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return "";
                                    }

                                    return null;
                                  },
                                ),
                                (bankcardTypeNo == "0" || bankcardTypeNo == "1")
                                    ? BdhTextInputSmall(
                                        item: FormItem(
                                            title: "开户网点", isRequired: true),
                                        fontSize: 14.px,
                                        placeHolder: "请填写开户网点",
                                        initialValue:
                                            _networkEditingController.text,
                                        controller: _networkEditingController,
                                        onChange: (v) {
                                          //network = v;
                                        },
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return "";
                                          }

                                          return null;
                                        },
                                      )
                                    : const SizedBox.shrink(),
                                ((bankcardTypeNo == "0" ||
                                            bankcardTypeNo == "1") &&
                                        !false)
                                    ? BdhTextInputSmall(
                                        item: FormItem(
                                            title: "联行号", isRequired: true),
                                        fontSize: 14.px,
                                        placeHolder: "请填写联行号",
                                        initialValue:
                                            _lineNumberEditingController.text,
                                        controller:
                                            _lineNumberEditingController,
                                        // textInputType: TextInputType.number,
                                        onChange: (v) {
                                          //lineNumber = v;
                                        },
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return "";
                                          } else if (value.length != 12) {
                                            return " 请输入12位联行号";
                                          }

                                          return null;
                                        },
                                      )
                                    : const SizedBox.shrink(),
                              ],
                            )),
                      ),
                    ])),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    GestureDetector(
                      onTap: _onSubmitClick,
                      child: BDHButtonGreen(
                          width: 345.px, height: 44.px, title: "提交"),
                    )
                  ],
                )
              ],
            ),
          );
        });
  }

  void _scanBankName(
    String account,
  ) {
    BrnLoadingDialog.show(context,
        content: "正在获取银行...  ", barrierDismissible: false);
    _bankService.queryBankName(bankAccount: account).then((result) {
      if (!mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);

      result.onError((error) {
        if (error.isCancel) {
          //如果是用户取消则什么都不需要做
          return;
        }
        showToast(error.message ?? "请求失败,请稍后重试");
      }).onSuccess((response) {
        if (response.code == 0 && response.success == true) {
          Log.d("bankName is = ${response.msg}");
          setState(() {
            bankName = response.msg;
          });
        }
      });
    });
  }

  Future _submit() async {
    var bankAccount = _bankAccountEditingController.text.isEmpty
        ? null
        : _bankAccountEditingController.text;
    var lineNumber = _lineNumberEditingController.text.isEmpty
        ? null
        : _lineNumberEditingController.text;
    var network = _networkEditingController.text.isEmpty
        ? null
        : _networkEditingController.text;

    String? photoPath = "${urlConfig.microfront}$bankcardPhotoPath";

    if (bankcardTypeNo == "0" || bankcardTypeNo == "1") {
      photoPath = null;
    } else if (bankcardTypeNo == "2") {
      lineNumber = null;
      network = null;
    }

    Log.d(
        "_submit bankAccountId:$bankAccountId,bankAccount:$bankAccount,bankcardTypeNo:$bankcardTypeNo,lineNumber:$lineNumber,network:$network,photoPath:$photoPath");

    var result = await _bankService.addBankcard(
        bankAccountId: bankAccountId,
        bankAccount: bankAccount!,
        bankName: bankName!,
        bankcardTypeNo: int.parse(bankcardTypeNo!),
        lineNumber: lineNumber,
        network: network,
        bankcardPhotoPath: photoPath);
    if (!mounted) {
      return;
    }
    BrnLoadingDialog.dismiss(context);

    result.onError((error) {
      if (error.isCancel) {
        //如果是用户取消则什么都不需要做
        return;
      }
      showToast(error.message ?? "请求失败,请稍后重试");
    }).onSuccess((response) {
      if (response.code == 0 && response.success == true) {
        showToast("操作成功");
        Navigator.pop(context, true);
      }
    });
  }

  void _onSubmitClick() async {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }

    var state = _formKey.currentState as FormState;
    bool validate = state.validate();
    if (!validate) {
      return;
    }

    state.save();
    BrnLoadingDialog.show(context,
        content: "提交中...  ", barrierDismissible: false);

    var bankAccount = _bankAccountEditingController.text;

    //如果是储蓄卡需要再次校验银行卡号与银行对应关系
    if (bankcardTypeNo == "0") {
      var result = await _bankService.queryBankName(bankAccount: bankAccount);
      if (!mounted) {
        return;
      }

      result.onSuccess((response) async {
        if (response.code == 0 && response.success == true) {
          if (response.msg != bankName) {
            var serverName =
                widget.bankLoadState.getBankByCode(response.msg)?.name ?? "未知";
            var originName =
                widget.bankLoadState.getBankByCode(bankName)?.name ?? "未知";
            bool? result = await showConfirmDialog(context,
                barrierDismissible: false,
                title: "提示",
                cancel: '取消',
                confirm: '确定',
                message: "银行卡号系统校验为$serverName,本次录入为$originName,是否继续操作?");
            if (!mounted) {
              return;
            }
            if (result == true) {
              _submit();
            } else {
              BrnLoadingDialog.dismiss(context);
            }
            return;
          }
          _submit();
        } else {
          BrnLoadingDialog.dismiss(context);
        }
      });
      return;
    }
    _submit();
  }
}
