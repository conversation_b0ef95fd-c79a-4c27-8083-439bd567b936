import 'dart:io';
import 'package:bdh_smart_agric_app/components/bdh_network_image.dart';
import 'package:bdh_smart_agric_app/components/refresh_header_footer/bdh_refresh_footer.dart';
import 'package:bdh_smart_agric_app/components/refresh_header_footer/bdh_refresh_header.dart';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/model/user_org_code_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/index/price_statistics.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/news_subview/bdh_text_scaler.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/leave_message/leave_message_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/model/channel_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/model/sub_company_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/news_subview/bdh_channel_fix.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/itemcell/item_cell_row_v4.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/model/search_content_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/premium_classroom.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_view.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/home_service.dart';
import 'package:bdh_smart_agric_app/utils/request/video_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:bdh_smart_agric_app/viewmodel/user_model.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:logger/web.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class DxalNewsCommonList extends StatefulWidget {
  final OrgModelV2 orgModelV2;
  final double fontScale;
  final Function(bool) checkUserOrgCallBack;
  const DxalNewsCommonList({
    super.key,
    required this.orgModelV2,
    required this.fontScale,
    required this.checkUserOrgCallBack,
  });

  @override
  State<DxalNewsCommonList> createState() =>
      _DxalNewsCommonListState(orgModelV2: orgModelV2);
}

class _DxalNewsCommonListState extends State<DxalNewsCommonList> {
  //   with AutomaticKeepAliveClientMixin {
  // @override
  // bool get wantKeepAlive => true;
  _DxalNewsCommonListState({this.orgModelV2});

  OrgModelV2? orgModelV2;
  List<ChannelModel> channelModelList = []; // 频道1
  List<ChannelModel> channelModelListTwo = []; // 频道2
  List<ChannelModel> channelModelListThree = []; // 频道3
  int optionType = 1;
  int _totalPage = 1; // 默认总页数为1页
  int _pageNum = 1;
  final int _pageSize = 10;
  bool _isAllDataLoaded = false;
  bool _isNOData = false;
  bool _showLoading = false;
  String channelUid = '';
  String channelNameOne = '';
  String channelName = '';
  String keyWordEmploy = '职工互动';
  String keyWordChannelOne = '厂务公开';
  String keyWordHeadlines = '头条';
  String keyWordNewsHeadlines = '新闻动态';
  bool showChooseFarmView = true;
  String farmName = '';
  // double pinnedHeight = 115.px;
  // double pinnedHeight = MediaQuery.of(context).padding.top
  double pinnedHeight = Platform.isIOS
      ? (kToolbarHeight + kBottomNavigationBarHeight + 15.px)
      : (kToolbarHeight + kBottomNavigationBarHeight);
  double postionscorll = 0.0;
  // String orgCodeEmployList = '';
  // int _totalPage_leaveMsg = 1; // 默认总页数为1页
  // int _pageNum_leaveMsg = 1;
  // int _pageSize_leaveMsg = 20;

  List<SearchContentModel> banners = []; // 轮播列表
  List<SearchContentModel> topNews = []; // 置顶三条
  List<SearchContentModel> recommend = []; // 首屏推位+常规
  List<LeaveMessageModel> leaveMsg = []; //职工互动

  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    bus.on("ListScrolled", (postion) {
      if (mounted) {
        setState(() {
          postionscorll = postion;
        });
      }
    });
    bus.on('RefeshLeaveMsg', (e) {
      getLeaveMsgData();
    });
    bus.on("SelectdFarmAction", (res) {
      OrgModelV2 model = res;
      selectedFarmModelCallBack(model);
    });
    bus.on("SelectedCompany", (model) {
      selectedFarmModelCallBack(model);
    });
    saveOrgCode(orgModelV2?.orgCode ?? '86');
    optionType = 1;
    getTreeChannel(orgModelV2!, optionType);
    farmName = '选择农场';
    showChooseFarmView = orgModelV2!.children!.isEmpty ? false : true;
  }

  saveOrgCode(String selectedOrgCode) {
    Map currentOrg = {"orgCode": selectedOrgCode};
    context.read<UserModel>().setCurrentOrg(currentOrg);
  }

  selectedFarmModelCallBack(OrgModelV2 model) {
    saveOrgCode(model.orgCode ?? '');
    if (model.isSelected ?? false) {
      if (model.isCompany ?? false) {
        farmName = '选择农场';
      } else {
        farmName = model.orgName ?? '';
      }
    } else {
      // (model.isCompany ?? false)
      //     ? farmName = model.orgName ?? ''
      //     : farmName = '选择农场';
      farmName = '选择农场';
    }
    String userOrgCode = StorageUtil.orgCode() ?? '';
    String selcetedOrgCode = context.read<UserModel>().currentOrg!["orgCode"];
    bool showLeaveMsgBtn =
        UserOrgCodeModel.checkUserOrg(userOrgCode, selcetedOrgCode);
    widget.checkUserOrgCallBack(showLeaveMsgBtn);
    // bus.emit('SelectdFarmAction', model);
    // OrgModelV2 model = res;
    optionType = (model.isCompany ?? false) ? 1 : 2;
    resetList();
    getTreeChannel(model, optionType);
  }

  resetList() {
    _pageNum = 1;
    channelName = '';
    channelNameOne = '';
    banners.clear();
    topNews.clear();
    recommend.clear();
    leaveMsg.clear();
    channelModelListTwo.clear();
    channelModelListThree.clear();
    channelModelList.clear();
  }

  getData() {
    getBanner(); //轮播
    getHomeTopNews(); //首屏置顶
    getHomeRecommend(); //首屏推位 测试
  }

  @override
  Widget build(BuildContext context) {
    return SmartRefresher(
      enablePullDown: true,
      enablePullUp: true,
      onRefresh: _onRefresh,
      onLoading: _onLoading,
      header: const BdhRefreshHeader(),
      footer: BdhRefreshFooter(
        mode: _refreshController.footerStatus!,
        isAllDataLoaded: _isAllDataLoaded,
        isNOData: _isNOData,
      ),
      controller: _refreshController,
      child: (channelName == keyWordEmploy) &&
              (channelNameOne == keyWordChannelOne)
          ? CustomScrollView(
              slivers: [
                SliverOverlapInjector(
                  handle:
                      NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                ),
                BdhChannelFix(pinnedOffset: pinnedHeight, child: channelView()),
                getLeaveMegView(),
              ],
            )
          : CustomScrollView(
              slivers: [
                SliverOverlapInjector(
                  handle:
                      NestedScrollView.sliverOverlapAbsorberHandleFor(context),
                ),
                BdhChannelFix(pinnedOffset: pinnedHeight, child: channelView()),
                SliverToBoxAdapter(child: topBannerView()),
                SliverToBoxAdapter(child: topNewView()),
                SliverToBoxAdapter(child: topChartsView()),
                getRecommendNewsView(),
              ],
            ),
    );
  }

  Widget channelView() {
    return Container(
      padding: postionscorll == 0.0
          ? EdgeInsets.only(top: 0.px, bottom: 0.px)
          : EdgeInsets.only(top: 4.px, bottom: 2.px),
      // padding: EdgeInsets.only(top: 4.px, bottom: 2.px),
      decoration: const BoxDecoration(
        color: Colors.white,
      ),
      child: Column(
        children: [
          //空 频道
          channelModelList.isEmpty && channelModelListTwo.isEmpty
              ? Column(
                  children: [
                    // SizedBox(height: 2.px),
                    oneCompanyChannelView(channelModelList),
                    Container(
                      width: 375.px,
                      height: 1.px,
                      color: const Color.fromRGBO(226, 235, 231, 0.4),
                    ),
                  ],
                )
              : Container(),

          //分公司 频道
          // channelModelList.length > 1
          channelModelList.isNotEmpty
              ? Column(
                  children: [
                    // SizedBox(height: 2.px),
                    oneCompanyChannelView(channelModelList),
                    Container(
                      width: 375.px,
                      height: 1.px,
                      color: const Color.fromRGBO(226, 235, 231, 0.4),
                    ),
                  ],
                )
              : Container(),

          //two农场
          channelModelListTwo.isNotEmpty
              ? Column(
                  children: [
                    SizedBox(height: 2.px),
                    seconedChannelView(channelModelListTwo),
                    // Container(
                    //   width: 375.px,
                    //   height: 1.px,
                    //   color: const Color.fromRGBO(226, 235, 231, 0.4),
                    // ),
                  ],
                )
              : Container(),

          //three农场
          channelModelListThree.isNotEmpty
              ? Column(
                  children: [
                    // SizedBox(height: 1.px),
                    threeChannelView(channelModelListThree),
                    Container(
                      width: 375.px,
                      height: 1.px,
                      color: const Color.fromRGBO(226, 235, 231, 0.4),
                    ),
                  ],
                )
              : Container(),
        ],
      ),
    );
  }

  Widget topChartsView() {
    String selcetedOrgCode = context.read<UserModel>().currentOrg!["orgCode"];
    Map selectedOrgCode = {"orgCode": selcetedOrgCode};
    return //86, 8604, 860405
        // (context.read<UserModel>().currentOrg!["orgCode"] == '86' ||
        (context.read<UserModel>().currentOrg!["orgCode"] == '8604' ||
                    context.read<UserModel>().currentOrg!["orgCode"] ==
                        '860405') &&
                (channelNameOne == keyWordHeadlines ||
                    channelNameOne == keyWordNewsHeadlines)
            ? Stack(
                children: [
                  Container(
                    padding: EdgeInsets.only(top: 10.px),
                    width: 375.px,
                    height: 236.px,
                    child: PriceStatistics(currentOrg: selectedOrgCode),
                  ),
                  Positioned(
                      bottom: 0,
                      child: GestureDetector(
                        onTap: () {
                          Logger().i('点击');
                          MenuConfigItem? menuConfigItem =
                              StorageUtil.readServiceItem();
                          Logger().i('menuConfigItem=$menuConfigItem');
                          if (menuConfigItem != null) {
                            for (var menus in menuConfigItem.children!) {
                              if (menus.authCode == "topMenu") {
                                for (var menuItem in menus.children!) {
                                  if (menuItem.authCode == "ricetransaction") {
                                    Navigator.of(context)
                                        .pushNamed(menuItem.url ?? "无效路径",
                                            arguments: menuItem)
                                        .then((res) {
                                      GlobalServiceView.needShowServiceBtn(
                                          'home');
                                    });
                                  }
                                }
                              }
                            }
                          }
                        },
                        child: Container(
                          height: 140.px,
                          width: 375.px,
                          color: Colors.transparent,
                          // color: Colors.red,
                        ),
                      ))
                ],
              )
            : Container();

    // return Container(
    //   child: Column(
    //     children: [
    //       // SizedBox(height: 84.px),
    //       SizedBox(
    //           width: 375.px,
    //           height: 216.px,
    //           child: PriceStatistics(
    //             currentOrg: selectedOrgCode,
    //           )),
    //     ],
    //   ),
    // );
  }

  Widget topBannerView() {
    return banners.isEmpty
        ? Container()
        : Container(
            margin: EdgeInsets.only(
                top: 10.px, left: 4.px, right: 4.px, bottom: 10.px),
            width: 375.px,
            // height: 205.px,
            // width: 351.px,
            height: 161.px,
            child: ClipRRect(
              borderRadius: BorderRadius.circular(3.px),
              child: Swiper(
                scale: 0.9,
                // fade: 0.1,
                curve: Curves.easeInToLinear,
                onTap: (index) {},
                autoplay: true,
                itemCount: banners.length,
                // pagination: SwiperPagination(),
                pagination: SwiperPagination(
                    margin: EdgeInsets.only(right: 10.px, bottom: 5.px),
                    builder: SwiperCustomPagination(builder: (context, config) {
                      return ConstrainedBox(
                        constraints: const BoxConstraints.expand(height: 90.0),
                        child: Row(
                          children: <Widget>[
                            Expanded(
                              child: Align(
                                alignment: Alignment.bottomRight,
                                child: const DotSwiperPaginationBuilder(
                                        activeSize: 5.0,
                                        size: 5.0,
                                        color:
                                            Color.fromRGBO(255, 255, 255, 0.4),
                                        activeColor: Colors.white)
                                    .build(context, config),
                              ),
                            )
                          ],
                        ),
                      );
                    })),
                // control: SwiperControl(),
                itemBuilder: (context, index) {
                  // print(
                  //     '${urlConfig.microfront}/${banners[index].imageList ?? [].first}');
                  String bannerStr = '';
                  if (banners[index].imageList!.isNotEmpty) {
                    bannerStr = (banners[index].imageList ?? []).first;
                  }

                  SearchContentModel model = banners[index];
                  return GestureDetector(
                    // behavior: HitTestBehavior.opaque,
                    onTap: () {
                      goToDitailPage(model);
                    },
                    child: Stack(
                      children: [
                        Positioned.fill(
                          child: Column(
                            children: [
                              ClipRRect(
                                borderRadius: BorderRadius.circular(3.px),
                                child: BdhNetworkImage(
                                    url: '${urlConfig.microfront}/$bannerStr',
                                    width: 351.px,
                                    fit: BoxFit.cover,
                                    height: 160.px),
                              ),
                            ],
                          ),
                        ),
                        Positioned(
                          left: 8.px,
                          bottom: 1.px,
                          child: Image.asset(
                            width: 351.px,
                            height: 42.px,
                            ImageHelper.wrapAssets('bannerTextBG.png'),
                          ),
                        ),
                        Positioned(
                          left: 8.px,
                          bottom: 1.px,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(width: 5.px),
                              SizedBox(
                                width: 280.px,
                                height: 30.px,
                                child: Text(
                                  banners[index].title ?? '',
                                  textScaler: BdhTextScaler(
                                      textScaleFactor: widget.fontScale),
                                  overflow: TextOverflow.ellipsis,
                                  softWrap: false,
                                  maxLines: 1,
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16.px,
                                      fontWeight: FontWeight.w600),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          );
  }

// subview 置顶 3条
  Widget topNewView() {
    return ListView.builder(
        padding: const EdgeInsets.only(top: 0),
        shrinkWrap: true,
        scrollDirection: Axis.vertical,
        itemCount: topNews.length,
        // NeverScrollablePhysics
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (BuildContext context, int index) {
          SearchContentModel model = topNews[index];
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              // Logger().i('点击cell行----$index');
              goToDitailPage(model);
            },
            child: itemCellRowV4(
              model,
              widget.fontScale,
              false,
              userClickedStar,
            ),
          );
        });
  }

//首屏推位
  Widget getRecommendNewsView() {
    return (topNews.isEmpty && recommend.isEmpty)
        ? _showLoading
            ? SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.only(top: 200.px),
                  child: const Center(
                    child: SpinKitCircle(
                      // color: HexColor('#16B760'),
                      color: Color.fromRGBO(0, 127, 255, 1),
                      size: 50.0,
                    ),
                  ),
                ),
              )
            : SliverToBoxAdapter(
                child: Padding(
                  padding: EdgeInsets.only(top: 150.px),
                  child: const BdhEmptyView(),
                ),
              )
        : SliverList.builder(
            // padding: const EdgeInsets.only(top: 0),
            // shrinkWrap: true,
            // scrollDirection: Axis.vertical,
            itemCount: recommend.length,
            // physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (BuildContext context, int index) {
              SearchContentModel model = recommend[index];
              return (model.belongPlate ?? 1) == '1'
                  ? GestureDetector(
                      behavior: HitTestBehavior.opaque,
                      onTap: () {
                        goToDitailPage(model);
                      },
                      child: itemCellRowV4(model, widget.fontScale, false,
                          userClickedStar, clicedRowCallBack),
                    )
                  : itemCellRowV4(model, widget.fontScale, false,
                      userClickedStar, clicedRowCallBack);
            });
  }

//员工互动
  Widget getLeaveMegView() {
    return leaveMsg.isEmpty
        ? SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.only(top: 100.px),
              child: const BdhEmptyView(),
            ),
          )
        : SliverList.separated(
            // padding: EdgeInsets.only(top: 0.px),
            // shrinkWrap: true,
            // scrollDirection: Axis.vertical,
            itemCount: leaveMsg.length + 1,
            // physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (BuildContext context, int index) {
              if (index == leaveMsg.length) {
                return Container();
              } else {
                LeaveMessageModel model = leaveMsg[index];
                return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    Navigator.of(context)
                        .pushNamed('leaveMessageDitail', arguments: model)
                        .then((res) {
                      getLeaveMsgData();
                    }).then((res) {
                      GlobalServiceView.needShowServiceBtn('home');
                    });

                    // Navigator.of(context)
                    //     .push(CupertinoPageRoute(builder: (context) {
                    //   return LeaveMessageDitail(
                    //     model: model,
                    //   );
                    // })).then((res) {
                    //   getLeaveMsgData();
                    // });
                  },
                  child: leaveMsgItem(model),
                );
              }
            },
            separatorBuilder: (BuildContext context, int index) {
              return Container(
                  width: 375.px,
                  height: 8.px,
                  color: const Color.fromRGBO(226, 235, 231, 0.2));
            },
          );
  }

//留言item
  Widget leaveMsgItem(LeaveMessageModel model) {
    List<String> leaveMsgList = model.replyList ?? [];
    return Container(
      // padding: EdgeInsets.symmetric(horizontal: 15.px),
      padding:
          EdgeInsets.only(top: 15.px, bottom: 15.px, left: 12.px, right: 12.px),
      decoration: const BoxDecoration(color: Colors.white),
      // height: 120.px,
      width: 375.px,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          //1 标题
          Row(
            children: [
              SizedBox(
                width: 345.px,
                child: Text(
                  model.title ?? '',
                  textScaler: BdhTextScaler(textScaleFactor: widget.fontScale),
                  textAlign: TextAlign.start,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 20.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#31394C'),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 3.px),
          //2.时间+作者
          Row(
            children: [
              Text(
                '${model.realName ?? ''} ${model.publishTime ?? ''}',
                // model.content ?? '',
                textScaler: BdhTextScaler(textScaleFactor: widget.fontScale),
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 0, 0, 0.3),
                  fontFamily: 'PingFang HK',
                ),
              ),
              SizedBox(
                width: 5.px,
              ),
              Container(
                  padding: EdgeInsets.only(
                      left: 5.px, right: 5.px, top: 1.px, bottom: 1.px),
                  decoration: const BoxDecoration(
                    color: Color.fromRGBO(214, 249, 232, 0.6),
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        model.orgName ?? '',
                        textScaler:
                            BdhTextScaler(textScaleFactor: widget.fontScale),
                        style: TextStyle(
                          fontSize: 14.px,
                          fontWeight: FontWeight.w500,
                          color: HexColor('#16B760'),
                          fontFamily: 'PingFang HK',
                        ),
                      )
                    ],
                  )),
            ],
          ),
          SizedBox(height: 13.px),
          //3.内容
          Row(
            children: [
              Expanded(
                child: Text(
                  model.content ?? '',
                  textScaler: BdhTextScaler(textScaleFactor: widget.fontScale),
                  maxLines: 2,
                  softWrap: true,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 15.px,
                    fontWeight: FontWeight.w500,
                    color: HexColor('#31394C'),
                  ),
                ),
              )
            ],
          ),
          SizedBox(height: 10.px),
          //line
          Container(
              width: 375.px,
              height: 1.px,
              color: const Color.fromRGBO(226, 235, 231, 0.2)),

          //留言内容
          ListView.builder(
              shrinkWrap: true,
              scrollDirection: Axis.vertical,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: leaveMsgList.length,
              itemBuilder: (BuildContext ctx, int i) {
                String msg = leaveMsgList[i];

                // return Text.rich(TextSpan(children: [
                //   TextSpan(
                //       text: '回复:',
                //       style: TextStyle(
                //         fontSize: 15.px,
                //         fontWeight: FontWeight.w500,
                //         color: HexColor('#16B760'),
                //       )),
                //   TextSpan(
                //     text: msg,
                //   ),
                // ]));

                return Text('回复: $msg',
                    textScaler:
                        BdhTextScaler(textScaleFactor: widget.fontScale),
                    style: TextStyle(
                        fontSize: 15.px,
                        fontWeight: FontWeight.w500,
                        color: HexColor('#16B760')));
              })
        ],
      ),
    );
  }

// 复位
  reSetChildChannel(List<ChannelModel> childChannel) {
    // List<ChannelModel> childChannel = widget.channelModel.childChannel ?? [];
    for (var i = 0; i < childChannel.length; i++) {
      if (i == 0) {
        childChannel[i].isSeleced = true;
      } else {
        childChannel[i].isSeleced = false;
      }
    }
    setState(() {});
  }

//选择农场
  Widget getChooseFarmView() {
    return Container();
    // return showChooseFarmView
    //     ? GestureDetector(
    //         onTap: () {
    //           showModalBottomSheet(
    //               backgroundColor: Colors.transparent,
    //               useSafeArea: true,
    //               context: context,
    //               builder: (ctx) {
    //                 return ChooseFarmPopview(
    //                   currentOrgModel: orgModelV2!,
    //                   selectedFarmModelCallBack: selectedFarmModelCallBack,
    //                 );
    //               });
    //         },
    //         child: Container(
    //           padding: EdgeInsets.only(right: 10.px),
    //           child: Row(
    //             mainAxisAlignment: MainAxisAlignment.start,
    //             children: [
    //               SizedBox(
    //                 child: Text(
    //                   textAlign: TextAlign.left,
    //                   farmName,
    //                   style: Platform.isIOS
    //                       ? TextStyle(
    //                           fontSize: 16.px,
    //                           fontWeight: FontWeight.w700,
    //                           color: const Color.fromRGBO(102, 102, 102, 1),
    //                         )
    //                       : TextStyle(
    //                           fontSize: 15.px,
    //                           fontWeight: FontWeight.w500,
    //                           color: const Color.fromRGBO(102, 102, 102, 1),
    //                         ),
    //                   maxLines: 1,
    //                 ),
    //               ),
    //               Image.asset(
    //                 width: 24.px,
    //                 height: 24.px,
    //                 ImageHelper.wrapAssets('choose_farm_triangel.png'),
    //               ),
    //               Container(
    //                 height: 16.px,
    //                 width: 1.px,
    //                 color: const Color.fromRGBO(0, 0, 0, 0.1),
    //               )
    //             ],
    //           ),
    //         ),
    //       )
    //     : Container();
  }

// 公司频道view
  Widget emptyChannleVie(List<ChannelModel> childChannelList) {
    return Container(
        // decoration: _showLoading
        //     ? null
        //     : banners.isEmpty
        //         ? BoxDecoration(color: HexColor('FFFFFF'))
        //         : null,
        // width: 351.px,
        // decoration: BoxDecoration(border: Border.all(width: 1)),
        padding: EdgeInsets.only(left: 12.px, right: 12.px),
        width: 375.px,
        height: 40.px,
        child: const Row(
          children: [],
        ));
  }

// 公司频道view
  Widget oneCompanyChannelView(List<ChannelModel> childChannelList) {
    List<ChannelModel> childChannel = childChannelList;
    return Container(
        // decoration: _showLoading
        //     ? null
        //     : banners.isEmpty
        //         ? BoxDecoration(color: HexColor('FFFFFF'))
        //         : null,
        decoration: _showLoading
            ? null
            : banners.isEmpty
                ? BoxDecoration(color: HexColor('FFFFFF'))
                : BoxDecoration(color: HexColor('FFFFFF')),
        // width: 351.px,

        padding: EdgeInsets.only(left: 12.px, right: 12.px),
        width: 375.px,
        height: 40.px,
        child: Row(
          children: [
            // getChooseFarmView(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.only(top: 0),
                scrollDirection: Axis.horizontal,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: childChannel
                      .map((channelModel) => Container(
                            // decoration: BoxDecoration(
                            //     border: Border.all(width: 1, color: Colors.blue)),
                            margin: EdgeInsets.only(right: 10.px),
                            child: TopBarCompanyItemViewScale(
                                fontScale: widget.fontScale,
                                item: TopBarItemScale(
                                    channelModel.channelName ?? "",
                                    channelModel.isSeleced ?? false),
                                onClick: (d) {
                                  _pageNum = 1;
                                  _isAllDataLoaded = false;
                                  for (var i = 0;
                                      i < childChannel.length;
                                      i++) {
                                    if (childChannel[i].channelName ==
                                        d.title) {
                                      childChannel[i].isSeleced = true;
                                      // Logger().i(
                                      //     '当前选择的二级频道= ${channelModel.channelName}---${channelModel.uid}');
                                      setState(() {
                                        channelUid = childChannel[i].uid ?? '';
                                        // channelName = childChannel[i].channelName ?? '';
                                        channelNameOne =
                                            childChannel[i].channelName ?? '';
                                        channelModelListThree =
                                            childChannel[i].childChannel ?? [];
                                      });
                                      // saveOrgCode(childChannel[i].orgCode ?? '');
                                      ChannelModel channelTwoModelItem =
                                          childChannel[i];
                                      if (channelTwoModelItem
                                          .childChannel!.isNotEmpty) {
                                        ChannelModel? channelThreeModelFrist =
                                            channelTwoModelItem
                                                .childChannel?.firstOrNull;
                                        channelName = channelThreeModelFrist!
                                                .channelName ??
                                            '';
                                        channelUid =
                                            channelThreeModelFrist.uid ?? '';
                                        if ((channelName == keyWordEmploy) &&
                                            (channelNameOne ==
                                                keyWordChannelOne)) {
                                          getLeaveMsgData();
                                          showFloatBtn(true);
                                        } else {
                                          getData();
                                          showFloatBtn(false);
                                        }
                                      } else {
                                        getData();
                                        showFloatBtn(false);
                                      }
                                    } else {
                                      childChannel[i].isSeleced = false;
                                    }
                                  }
                                  // reSetChildChannel(channelModelList);
                                  setState(() {});
                                }),
                          ))
                      .toList(),
                ),
              ),
            ),
          ],
        ));
  }

// 二级频道view
  Widget seconedChannelView(List<ChannelModel> childChannelList) {
    List<ChannelModel> childChannel = childChannelList;
    return Container(
        decoration: const BoxDecoration(color: Colors.white),
        // decoration: _showLoading
        //     ? null
        //     : banners.isEmpty
        //         ? BoxDecoration(color: HexColor('FFFFFF'))
        //         : null,
        padding: EdgeInsets.only(left: 12.px, right: 12.px),
        width: 375.px,
        height: 40.px,
        child: Row(
          children: [
            // getChooseFarmView(),
            Expanded(
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: childChannel
                      .map((channelModel) => Container(
                            margin: EdgeInsets.only(right: 10.px),
                            child: TopBarCompanyItemViewScale(
                                fontScale: widget.fontScale,
                                item: TopBarItemScale(
                                    channelModel.channelName ?? "",
                                    channelModel.isSeleced ?? false),
                                onClick: (d) {
                                  _pageNum = 1;
                                  _isAllDataLoaded = false;
                                  for (var i = 0;
                                      i < childChannel.length;
                                      i++) {
                                    if (childChannel[i].channelName ==
                                        d.title) {
                                      childChannel[i].isSeleced = true;
                                      // Logger().i(
                                      //     '当前选择的二级频道= ${channelModel.channelName}---${channelModel.uid}');
                                      setState(() {
                                        channelUid = childChannel[i].uid ?? '';
                                        // channelName = childChannel[i].channelName ?? '';
                                        channelNameOne =
                                            childChannel[i].channelName ?? '';
                                        channelModelListThree =
                                            childChannel[i].childChannel ?? [];
                                      });
                                      // saveOrgCode(childChannel[i].orgCode ?? '');
                                      ChannelModel channelTwoModelItem =
                                          childChannel[i];
                                      if (channelTwoModelItem
                                          .childChannel!.isNotEmpty) {
                                        ChannelModel? channelThreeModelFrist =
                                            channelTwoModelItem
                                                .childChannel?.firstOrNull;
                                        channelName = channelThreeModelFrist!
                                                .channelName ??
                                            '';
                                        channelUid =
                                            channelThreeModelFrist.uid ?? '';
                                        if ((channelName == keyWordEmploy) &&
                                            (channelNameOne ==
                                                keyWordChannelOne)) {
                                          getLeaveMsgData();
                                          showFloatBtn(true);
                                        } else {
                                          setState(() {
                                            channelUid =
                                                channelThreeModelFrist.uid ??
                                                    '';
                                          });
                                          getData();
                                          showFloatBtn(false);
                                        }
                                      } else {
                                        getData();
                                        showFloatBtn(false);
                                      }
                                    } else {
                                      childChannel[i].isSeleced = false;
                                    }
                                  }
                                  reSetChildChannel(channelModelListThree);
                                  setState(() {});
                                }),
                          ))
                      .toList(),
                ),
              ),
            )
          ],
        ));
  }

  // 3级频道view
  Widget threeChannelView(List<ChannelModel> childChannelList) {
    List<ChannelModel> childChannel = childChannelList;
    return Container(
      // decoration: const BoxDecoration(color: Colors.white),
      decoration: _showLoading
          ? null
          : banners.isEmpty
              ? BoxDecoration(color: HexColor('FFFFFF'))
              : null,
      padding: EdgeInsets.only(left: 12.px, right: 12.px),
      width: 375.px,
      // height: 32.px,
      height: 40.px,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: childChannel
              .map((channelModel) => Container(
                    margin: EdgeInsets.only(right: 10.px),
                    child: TopBarItemViewScale(
                        fontScale: widget.fontScale,
                        item: TopBarItemScale(channelModel.channelName ?? "",
                            channelModel.isSeleced ?? false),
                        onClick: (d) {
                          _pageNum = 1;
                          _isAllDataLoaded = false;
                          for (var i = 0; i < childChannel.length; i++) {
                            if (childChannel[i].channelName == d.title) {
                              childChannel[i].isSeleced = true;
                              // Logger().i(
                              //     '当前选择的二级频道= ${channelModel.channelName}---${channelModel.uid}');
                              setState(() {
                                channelUid = childChannel[i].uid ?? '';
                                channelName = childChannel[i].channelName ?? '';
                                // orgCodeEmployList =
                                //     childChannel[i].orgCode ?? '';
                              });
                              // saveOrgCode(childChannel[i].orgCode ?? '');
                              if (channelName == keyWordEmploy &&
                                  channelNameOne == keyWordChannelOne) {
                                getLeaveMsgData();
                                showFloatBtn(true);
                              } else {
                                getData();
                                showFloatBtn(false);
                              }
                            } else {
                              childChannel[i].isSeleced = false;
                            }
                          }
                          setState(() {});
                        }),
                  ))
              .toList(),
        ),
      ),
    );
  }

// event below:-----

//进入详情
  void goToDitailPage(SearchContentModel model) {
    if ((model.belongPlate ?? '') == '2') {
      // Logger().i('田间视频');
      // 田间视频
    } else if ((model.belongPlate ?? '') == '1') {
      if (model.contentType == '1' || model.contentType == '3') {
        // 图文
        Navigator.of(context)
            .pushNamed('photoArticleDitail', arguments: model)
            .then((res) {
          GlobalServiceView.needShowServiceBtn('home');
        });
        // Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
        //   return PhotoArticleDitail(model: model);
        // })).then((res) {
        //   // getData();
        // });
      } else if (model.contentType == '2') {
        //内容视频
        Navigator.of(context)
            .pushNamed('videoPlayView', arguments: model)
            .then((res) {
          GlobalServiceView.needShowServiceBtn('home');
        });
        // Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
        //   return VideoPlayView(model: model);
        //   // return VideoPlayView(
        //   //     playUrl:
        //   //         'http://10.11.14.211:30879/oss-huawei-obs/mobile_app/bdh_agri_farm_clothes/1825430439771242496.mp4');
        // })).then((res) {
        //   // getData();
        // });
      }
    }
  }

  //点击 田间视频
  void clicedRowCallBack(SearchContentModel model) {
    // Logger().i(model.title);
    if ((model.contentId ?? '') != '') {
      Navigator.of(context).pushNamed('premiumClassroom', arguments: {
        'type': EntryType.topic,
        'videoId': model.contentId ?? 0,
        'notShowGuidePage': true,
      }).then((res) {
        loadPlantVideoDetail(model);
        GlobalServiceView.needShowServiceBtn('home');
      });
      // Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
      //   return PremiumClassroom(
      //     type: EntryType.topic,
      //     videoId: model.contentId ?? 0,
      //     notShowGuidePage: true,
      //   );
      // })).then((res) {
      //   // getData();
      //   loadPlantVideoDetail(model);
      // });
    } else {
      showToast('无效播放链接');
      return;
    }
  }

//net 点赞
  loadPlantVideoDetail(SearchContentModel model) {
    VideoResponsitory.videoDetail(model.contentId ?? 0).then((res) {
      model.isLike = res.data?.isLike;
      model.likeCount = res.data?.likeCount;
      setState(() {});
    });
  }

//net 点赞 田间视频
  void userClickedStar(SearchContentModel model) {
    // Logger().i(model);
    HomeService.plantVideoClickeStar({
      "videoId": model.contentId ?? 0,
    }).then((res) {
      // Logger().i('点赞 田间视频=');
      // Logger().i(res);
      if (res['code'] == 0 && res['success'] == true) {
        showToast(res['msg']);
        // 刷新列表数据
        getHomeRecommend();
      }
    });
  }

  void _onRefresh() async {
    // Logger().i('下拉刷新');
    _pageNum = 1;
    setState(() {
      _isAllDataLoaded = false;
    });
    // getData();
    // channelName == keyWordEmploy ? getData() : getLeaveMsgData();
    (channelName == keyWordEmploy) && (channelNameOne == keyWordChannelOne)
        ? getLeaveMsgData()
        : getData();
    _refreshController.refreshCompleted();
  }

  // void _onLoading() async {
  //   _pageNum += 1;
  //   Logger().i('上拉加载');
  //   if (_pageNum > _totalPage) {
  //     _pageNum = _totalPage;
  //     setState(() {
  //       _isAllDataLoaded = true;
  //     });
  //     _refreshController.loadComplete();
  //     return;
  //   } else {
  //     setState(() {
  //       _isAllDataLoaded = false;
  //     });
  //   }
  //   // getHomeRecommend();
  //   // channelName == keyWordEmploy ? getHomeRecommend() : getLeaveMsgData();
  //   (channelName == keyWordEmploy) && (channelNameOne == keyWordChannelOne)
  //       ? getLeaveMsgData()
  //       : getData();
  //   _refreshController.loadComplete();
  // }

  void _onLoading() async {
    if (_isAllDataLoaded) {
      _refreshController.loadComplete();
      return;
    }
    _pageNum += 1;
    // Logger().i('上拉加载');
    (channelName == keyWordEmploy) && (channelNameOne == keyWordChannelOne)
        ? getLeaveMsgData()
        : getData();
    _refreshController.loadComplete();
  }

//net: 轮播
  getBanner() {
    String selcetedOrgCode = context.read<UserModel>().currentOrg!["orgCode"];
    HomeService.getBanner({
      "orgCode": selcetedOrgCode,
      // "orgCode": StorageUtil.orgCode(),
      // "channelUid": "1823972992783220736",
      "channelUid": channelUid,
      // "contentOpen": "",
    }).then((res) {
      // Logger().i('轮播res= ');
      // Logger().i(res);
      if (res['code'] == 0 && res['success'] == true) {
        int carouselNum = res['data']['carouselNum'];
        final dataList = res['data']['channelRotateList'];
        List<SearchContentModel> tempModelList = [];
        for (int i = 0; i < dataList.length; i++) {
          SearchContentModel model = SearchContentModel.fromJson(dataList[i]);
          if (model.image == null) {
            model.imageList = [];
          } else {
            List<String> imgStrList = (model.image ?? '').split(',');
            model.imageList = imgStrList;
          }
          // model.contentType = '3'; //debug
          tempModelList.add(model);
        }
        setState(() {
          // banners = tempModelList.length > 6
          //     ? tempModelList.sublist(0, 6)
          //     : tempModelList;

          banners = tempModelList.sublist(
              0,
              carouselNum > tempModelList.length
                  ? tempModelList.length
                  : carouselNum);
        });
      }
    });
  }

  //net: 首屏置顶 3条
  getHomeTopNews() {
    String selcetedOrgCode = context.read<UserModel>().currentOrg!["orgCode"];
    HomeService.getHomeTopNews({
      "orgCode": selcetedOrgCode,
      // "orgCode": StorageUtil.orgCode(),
      // "channelUid": "1823972992783220736",
      // "channelUid": widget.channelModel.uid ?? '',
      "channelUid": channelUid,
      // "contentOpen": "",
    }).then((res) {
      // Logger().i('首屏置顶res= ');
      // Logger().i(res);
      if (res['code'] == 0 && res['success'] == true) {
        final dataList = res['data'];
        List<SearchContentModel> tempModelList = [];
        for (int i = 0; i < dataList.length; i++) {
          SearchContentModel model = SearchContentModel.fromJson(dataList[i]);
          model.isHomeTopNews = true;
          if (model.image == null) {
            model.imageList = [];
          } else {
            List<String> imgStrList = (model.image ?? '').split(',');
            model.imageList = imgStrList;
          }
          tempModelList.add(model);
        }
        setState(() {
          topNews = tempModelList.length > 3
              ? tempModelList.sublist(0, 3)
              : tempModelList;
        });
      }
    });
  }

//net 首屏推位+常规
  getHomeRecommend() {
    String selcetedOrgCode = context.read<UserModel>().currentOrg!["orgCode"];
    // Logger().i('channelUid = $channelUid');
    // EasyLoading.show();
    setState(() {
      _showLoading = true;
    });
    HomeService.getHomeRecommend({
      // "channelUid": widget.channelModel.uid ?? '',
      "channelUid": channelUid,
      "contentOpen": "",
      // "orgCode": "",
      "orgCode": selcetedOrgCode,
      "pageNo": _pageNum,
      "pageSize": _pageSize
    }).then((res) {
      if (mounted) {
        setState(() {
          _showLoading = false;
        });
      }

      if (res['code'] == -1) {
        // 频道异常
        // bus.emit('upDateOrg', channelUid);
        return;
      }
      // Logger().i('首屏推位+常规=');
      // Logger().i(res);
      // EasyLoading.dismiss();
      final dataList = res['data']['dataList'];
      _totalPage = res['data']['pageTotal'];
      List<SearchContentModel> tempModelList = [];
      List<SearchContentModel> plantModelList = [];
      for (int i = 0; i < dataList.length; i++) {
        SearchContentModel model = SearchContentModel.fromJson(dataList[i]);
        if (model.image == null) {
          model.imageList = [];
        } else {
          List<String> imgStrList = (model.image ?? '').split(',');
          model.imageList = imgStrList;
        }
        if ((model.belongPlate ?? '') == '2') {
          plantModelList.add(model);
        }
        tempModelList.add(model);
      }

      if (_pageNum == 1) {
        // 田间视频处理
        if (plantModelList.isNotEmpty) {
          plantModelList.length >= 2
              ? plantModelList = plantModelList.sublist(0, 2)
              : ();

//田间视频位置
          // SearchContentModel currentPlantVideoModel = SearchContentModel();
          for (int i = 0; i < tempModelList.length; i++) {
            SearchContentModel model = tempModelList[i];
            if ((model.belongPlate ?? '') == '2') {
              model.plantVidioList?.addAll(plantModelList);
              // currentPlantVideoModel = model;
              break;
            }
          }

          // tempModelList.removeWhere((v) => ((v.belongPlate ?? '') == '2'));
          tempModelList.removeWhere((v) =>
              (((v.belongPlate ?? '') == '2') && (v.plantVidioList!.isEmpty)));

          // 排序处理
          // if (tempModelList.length >= 6) {
          //   // tempModelList.removeAt(plantVideoCurretnIndex);
          //   tempModelList.insert(5, currentPlantVideoModel);
          // } else {
          //   tempModelList.add(currentPlantVideoModel);
          // }
        }
        recommend = [];
        // Logger().i('----------田间视频处理----------');
        // Logger().i(tempModelList);

        setState(() {
          recommend = tempModelList;
        });
      } else {
        // if (tempModelList.length < 10 && tempModelList.isNotEmpty) {
        if (tempModelList.length < 10) {
          _isAllDataLoaded = true;
          _isNOData = true;
        } else {
          _isAllDataLoaded = false;
          _isNOData = false;
        }

        setState(() {
          recommend.addAll(tempModelList);
        });
      }
    });
  }

//net: 首页频道树
  getTreeChannel(OrgModelV2 model, int optionType) {
    // channelModelList.clear();
    // channelModelListTwo.clear();
    // channelModelListThree.clear;

    setState(() {
      _showLoading = true;
    });
    String orgCode = model.orgCode ?? '';
    // String orgCode = orgModelV2?.orgCode ?? '';
    // Logger().i('orgCode= $orgCode');
    HomeService.getTreeChannel(
      {"orgCode": orgCode},
    ).then((res) {
      setState(() {
        _showLoading = false;
      });
      // Logger().i('首页频道树res= ');
      // Logger().i(res);
      if (res['code'] == 0 && res['success'] == true) {
        final dataList = res['data'];
        List<ChannelModel> tempModelList = [];
        for (int i = 0; i < dataList.length; i++) {
          ChannelModel model = ChannelModel.fromJson(dataList[i]);
          List<ChannelModel> tempsubChannerList = [];
          if (dataList[i]['childChannel'] != null) {
            for (int j = 0; j < dataList[i]['childChannel'].length; j++) {
              ChannelModel childChannelModel =
                  ChannelModel.fromJson(dataList[i]['childChannel'][j]);
              j == 0
                  ? childChannelModel.isSeleced = true
                  : childChannelModel.isSeleced = false;
              tempsubChannerList.add(childChannelModel);
            }
          }
          model.childChannel = tempsubChannerList;
          tempModelList.add(model);
          // Logger().i(model.channelName);
        }

        setState(() {
          optionType == 1
              ? (channelModelList = tempModelList)
              : (channelModelListTwo = tempModelList);
        });

        if (optionType == 1) {
          // if (channelModelList.isNotEmpty) {
          //   ChannelModel? firstChannelModel = channelModelList.firstOrNull;
          //   if (firstChannelModel != null) {
          //     channelUid = firstChannelModel.uid ?? '';
          //   }
          //   reSetChildChannel(channelModelList);
          //   getData();
          // }
          if (channelModelList.isNotEmpty) {
            ChannelModel? firstChannelModel = channelModelList.firstOrNull;
            if (firstChannelModel != null) {
              channelUid = firstChannelModel.uid ?? '';
              channelNameOne = firstChannelModel.channelName ?? '';
              if (firstChannelModel.childChannel!.isNotEmpty) {
                ChannelModel? channelSecondModelFrist =
                    firstChannelModel.childChannel?.firstOrNull;
                channelName = channelSecondModelFrist!.channelName ?? '';
                if ((channelName == keyWordEmploy) &&
                    (channelNameOne == keyWordChannelOne)) {
                  getLeaveMsgData();
                  showFloatBtn(true);
                } else {
                  reSetChildChannel(channelModelList);
                  getData();
                  showFloatBtn(false);
                }
              } else {
                reSetChildChannel(channelModelList);
                getData();
                showFloatBtn(false);
              }
            } else {
              showFloatBtn(false);
            }
          } else {
            showFloatBtn(false);
          }
        } else if (optionType == 2) {
          if (channelModelListTwo.isEmpty) {
            setState(() {
              banners.clear();
              topNews.clear();
              recommend.clear();
            });
          } else {
            ChannelModel? secondeChannelModelFrist =
                channelModelListTwo.firstOrNull;
            channelNameOne = secondeChannelModelFrist?.channelName ?? '';
            if (secondeChannelModelFrist != null) {
              if (secondeChannelModelFrist.childChannel!.isNotEmpty) {
                channelModelListThree = secondeChannelModelFrist.childChannel!;
                reSetChildChannel(channelModelListThree);
                ChannelModel? channelThreeModelFrist =
                    channelModelListThree.firstOrNull;
                if (channelThreeModelFrist != null) {
                  channelUid = channelThreeModelFrist.uid ?? '';
                  channelName = channelThreeModelFrist.channelName ?? '';
                  // saveOrgCode(channelThreeModelFrist.orgCode ?? '');
                  if ((channelName == keyWordEmploy) &&
                      (channelNameOne == keyWordChannelOne)) {
                    showFloatBtn(true);
                    getLeaveMsgData();
                  } else {
                    showFloatBtn(false);
                    getData();
                  }
                }
              } else {
                reSetChildChannel(channelModelListTwo);
                channelUid = secondeChannelModelFrist.uid ?? '';
                // saveOrgCode(secondeChannelModelFrist.orgCode ?? '');
                getData();
                showFloatBtn(false);
              }
            }
          }
        }
      }
    });
  }

  //net 职工互动 数据
  getLeaveMsgData() {
    // Logger().i('职工互动 数据');
    // EasyLoading.show();
    HomeService.getLeaveMessage({
      // "orgCode": orgCodeEmployList,
      "orgCode": context.read<UserModel>().currentOrg!["orgCode"],
      "pageNo": _pageNum,
      "pageSize": _pageSize,
    }).then((res) {
      // EasyLoading.dismiss();
      // Logger().i('职工互动res= ');
      // Logger().i(res);
      final dataList = res['data']['dataList'];
      _totalPage = res['data']['pageTotal'];
      List<LeaveMessageModel> tempModelList = [];
      for (int i = 0; i < dataList.length; i++) {
        LeaveMessageModel model = LeaveMessageModel.fromJson(dataList[i]);
        tempModelList.add(model);
      }
      if (_pageNum == 1) {
        setState(() {
          leaveMsg = tempModelList;
        });
      } else {
        if (tempModelList.length < 10) {
          _isAllDataLoaded = true;
        } else {
          _isAllDataLoaded = false;
        }

        setState(() {
          leaveMsg.addAll(tempModelList);
        });
      }
    });
  }

  showFloatBtn(bool showFloatBtn) {
    // bus.emit('showFloatingBtn', showFloatBtn);
    DxalShowFloatBtnNotifiation(showFloatBtn).dispatch(context);
  }
} // end state

class DxalShowFloatBtnNotifiation extends Notification {
  DxalShowFloatBtnNotifiation(this.showFloatBtn);
  bool showFloatBtn;
}

class TopBarItemScale {
  String title;
  bool isSelect;
  TopBarItemScale(this.title, this.isSelect);
}

// ----------二级频道显示菜单样式------------
class TopBarItemViewScale extends StatelessWidget {
  final double fontScale;
  final TopBarItemScale item;
  final Function(TopBarItemScale) onClick;
  const TopBarItemViewScale(
      {super.key,
      required this.fontScale,
      required this.item,
      required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // behavior: HitTestBehavior.opaque,
      onTap: () {
        onClick(item);
      },
      child: item.isSelect
          ? BdhTagScale(
              padding: EdgeInsets.only(
                  left: 10.px, right: 10.px, top: 3.px, bottom: 3.px),
              name: item.title,
              fontScale: fontScale,
              color: const Color.fromRGBO(22, 183, 96, 1),
              //  color: const Color.fromRGBO(0, 0, 0, 1),
              bgColor: const Color.fromRGBO(214, 249, 232, 1),
              fontWeight: FontWeight.w500,
              fontSize: 15.px,
            )
          : BdhTagScale(
              padding: EdgeInsets.only(
                  left: 10.px, right: 10.px, top: 3.px, bottom: 3.px),
              name: item.title,
              fontScale: fontScale,
              // color: const Color.fromRGBO(131, 149, 142, 1),
              color: const Color.fromRGBO(0, 0, 0, 0.4),
              bgColor: const Color.fromRGBO(241, 245, 243, 0.4),
              fontWeight: FontWeight.w300,
              fontSize: 15.px,
            ),
    );
  }
}

class BdhTagScale extends StatelessWidget {
  final double fontScale;
  final Color color;
  final Color bgColor;
  final FontWeight fontWeight;
  final double fontSize;
  final String name;
  final EdgeInsets? padding;
  const BdhTagScale(
      {super.key,
      required this.name,
      required this.fontScale,
      required this.fontSize,
      required this.color,
      required this.bgColor,
      required this.fontWeight,
      this.padding});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? EdgeInsets.only(left: 10.px, right: 10.px),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(20.px)),
          // color: Color.fromRGBO(color.red, color.green, color.blue, 0.06),
          // color: const Color.fromRGBO(214, 249, 232, 1),
          color: bgColor),
      child: Text(
        name,
        textScaler: BdhTextScaler(textScaleFactor: fontScale),
        style: TextStyle(
          fontSize: fontSize,
          color: color,
          fontWeight: fontWeight,
        ),
      ),
    );
  }
}

// --------公司频道显示菜单样式-----------
class TopBarCompanyItemViewScale extends StatelessWidget {
  final double fontScale;
  final TopBarItemScale item;
  final Function(TopBarItemScale) onClick;
  const TopBarCompanyItemViewScale(
      {super.key,
      required this.fontScale,
      required this.item,
      required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // behavior: HitTestBehavior.opaque,
      onTap: () {
        onClick(item);
      },
      child: item.isSelect
          ? BdhCompanyTagScale(
              padding: EdgeInsets.only(
                  left: 0.px, right: 0.px, top: 3.px, bottom: 3.px),
              name: item.title,
              fontScale: fontScale,
              // color: const Color.fromRGBO(16, 195, 135, 1),
              color: const Color.fromRGBO(15, 197, 99, 1),
              // color: const Color.fromRGBO(22, 183, 96, 1),
              // color: const Color.fromRGBO(0, 0, 0, 1),
              bgColor: HexColor('#FFFFFF'),
              // bottomBorderColor: const Color.fromRGBO(0, 0, 0, 1),
              bottomBorderColor: const Color.fromRGBO(15, 197, 99, 1),
              // bottomBorderColor: const Color.fromRGBO(30, 216, 116, 1),
              fontWeight: FontWeight.w600,
              fontSize: 15.px,
            )
          : BdhCompanyTagScale(
              padding: EdgeInsets.only(
                  left: 0.px, right: 0.px, top: 3.px, bottom: 3.px),
              name: item.title,
              fontScale: fontScale,
              color: const Color.fromRGBO(153, 153, 153, 1),
              // color: const Color.fromRGBO(0, 0, 0, 0.4),
              bgColor: HexColor('#FFFFFF'),
              bottomBorderColor: HexColor('#FFFFFF'),
              fontWeight: FontWeight.w500,
              fontSize: 15.px,
            ),
    );
  }
}

class BdhCompanyTagScale extends StatelessWidget {
  final double fontScale;
  final Color color;
  final Color bgColor;
  final Color bottomBorderColor;
  final FontWeight fontWeight;
  final double fontSize;
  final String name;
  final EdgeInsets? padding;
  const BdhCompanyTagScale({
    super.key,
    required this.name,
    required this.fontScale,
    required this.color,
    required this.bgColor,
    required this.bottomBorderColor,
    required this.fontWeight,
    required this.fontSize,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 2.px, right: 2.px),
      child: Container(
        padding: padding ?? EdgeInsets.only(left: 10.px, right: 10.px),
        decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(width: 1.5.px, color: bottomBorderColor),
            // ),
            color: bgColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Text(
              name,
              textScaler: BdhTextScaler(textScaleFactor: fontScale),
              style: TextStyle(
                fontSize: fontSize,
                color: color,
                fontWeight: fontWeight,
              ),
            ),
            Row(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 4.px),
                  height: 2.px,
                  width: 25.px,
                  color: bottomBorderColor,
                )
              ],
            )
            // Container(
            //   height: 2.px,
            // )
          ],
        ),
      ),
    );
  }
}
