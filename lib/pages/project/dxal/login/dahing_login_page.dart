import 'dart:async';
import 'dart:convert';

import 'package:bdh_smart_agric_app/components/form/bdh_text_input_small.dart';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:oktoast/oktoast.dart';

import 'package:encrypt/encrypt.dart' as enc;

import '../../../home/<USER>/components/bdh_check.dart';
import 'widget/sms_code_widget.dart';

//大兴用户登录
class DaHingLoginPage extends StatefulWidget {
  const DaHingLoginPage({super.key});

  @override
  State<StatefulWidget> createState() => DaHingLoginPageState();
}

class DaHingLoginPageState extends State<DaHingLoginPage>
    with AutoDisposeStateMixin {
  final GlobalKey _formKey = GlobalKey<FormState>();

  late final ScrollController _scrollController;
  //手机号
  late final TextEditingController _phoneController;
  //密码
  late final TextEditingController _passwordController;
  //短信验证码
  late final TextEditingController _smsController;

  late final TapGestureRecognizer _tapGestureRecognizer;

  late final TapGestureRecognizer _tapGestureRecognizer2;

  late final FocusNode _phoneFocusNode;

  late final FocusNode _passwordFocusNode;

  late final FocusNode _smsFocusNode;

  //同意隐私协议
  bool _isChecked = false;
  //显示密码明文
  bool _isShowPassword = false;
  //短信登录
  bool _isSmsLogin = false;

  @override
  void initState() {
    super.initState();

    //通过 use 在 dispose 时会自动调用销毁,防止内存泄露
    _scrollController = useScrollController(ScrollController());
    _passwordController = useTextController(TextEditingController());
    _phoneController = useTextController(TextEditingController());
    _smsController = useTextController(TextEditingController());
    _phoneFocusNode = useFocusNode(FocusNode());
    _passwordFocusNode = useFocusNode(FocusNode());
    _smsFocusNode = useFocusNode(FocusNode());

    _tapGestureRecognizer = useTapGestureRecognizer(
        TapGestureRecognizer()..onTap = _onClickUserAgreement);

    _tapGestureRecognizer2 = useTapGestureRecognizer(
        TapGestureRecognizer()..onTap = _onClickPrivacy);
  }

  void _onClickUserAgreement() {
    if (!mounted) {
      return;
    }
    Navigator.of(context).push(CupertinoPageRoute(
        builder: (_) => const HtmlPage(
            title: "用户协议",
            content: "https://www.bdhic.com/Anticipation.html")));
  }

  void _onClickPrivacy() {
    if (!mounted) {
      return;
    }
    Navigator.of(context).push(CupertinoPageRoute(
        builder: (_) => const HtmlPage(
            title: "隐私政策",
            content: "https://smartagric.bdhic.com/privacy.html")));
  }

  //通过此方式让输入法消失
  void _unfocus() {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  //点击提交
  void _onLoginClick() {
    //隐藏输入法
    _unfocus();
    var state = _formKey.currentState as FormState;

    //校验表单合法性
    bool validate = state.validate();
    if (!validate) {
      return;
    }
    //校验是否同意隐私协议
    if (!_isChecked) {
      showToast("请先阅读并同意隐私协议");
      return;
    }

    if (_isSmsLogin) {
      //这里多加了一层校验,实际上可以不需要
      if (_phoneController.text.isEmpty || _smsController.text.isEmpty) {
        showToast("手机号或验证码不能为空");
        return;
      }
      _loginSms();
    } else {
      if (_phoneController.text.isEmpty || _passwordController.text.isEmpty) {
        showToast("手机号或密码不能为空");
        return;
      }
      _login();
    }
  }

  //普通登录
  void _login() async {
    var loginName = _phoneController.text;
    var password = _passwordController.text;
    var bytes = utf8.encode(password.toUpperCase());
    //第一层MD5
    var firstMD5 = '${md5.convert(bytes)}'.toUpperCase();
    //第二层MD5
    var md5Password = '${md5.convert(utf8.encode(firstMD5))}'.toLowerCase();

    var key = enc.Key.fromUtf8('T8d7areaVthcV9ir');
    var iv = enc.IV.fromUtf8('2I5cTwvg9oCzTChP');
    var e = enc.Encrypter(enc.AES(key, mode: enc.AESMode.cbc));
    var enData = e.encrypt(password, iv: iv);
    var password1 = enData.base64;
    BrnLoadingDialog.show(context,
        content: "登录中..  ", barrierDismissible: false);
    BDHResponsitory.login({
      "loginName": loginName,
      "password": md5Password,
      "password1": password1,
      "systemCode": "bdh-app-gathering"
    }, cancelToken: useCancelToken(CancelToken()))
        .then((result) {
      if (!mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);
      if (result.success ?? false) {
        //TODO 默认登录逻辑这里请求获取角色列表，但是短信验证码登录方式中却没有获取角色列表的逻辑,
        //检测代码发现用到角色列表的业务中又重复请求了网络获取最新的角色列表，因此这里将获取角色列表的逻辑删除了
        result.data!.ifRegistered = "1";
        String? orgCode = result.data?.pluginInfo?.orgInfos?.first.orgCode;
        GlobalServiceView.orgCode = orgCode ?? '';
        StorageManager.storage!.setItem(kUser, jsonEncode(result.toJson()));
        Navigator.of(context).pushReplacementNamed(RouteName.tabMain);
      }
    }).onError((error, stackTrace) {
      if (!mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);
      _showNetworkError(error, stackTrace);
    });
  }

  //验证码登录
  void _loginSms() {
    var loginName = _phoneController.text;
    var code = _smsController.text;

    BrnLoadingDialog.show(context,
        content: "登录中..  ", barrierDismissible: false);
    BDHResponsitory.login({
      "telephone": loginName,
      "code": code,
      "loginType": 1,
      "systemCode": "bdh-app"
    }).then((result) {
      if (!mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);
      if (result.success ?? false) {
        result.data!.ifRegistered = "1";
        String? orgCode = result.data?.pluginInfo?.orgInfos?.first.orgCode;
        GlobalServiceView.orgCode = orgCode ?? '';
        StorageManager.storage!.setItem(kUser, jsonEncode(result.toJson()));
        Navigator.of(context).pushReplacementNamed(RouteName.tabMain);
      }
    }).onError((error, stackTrace) {
      if (!mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);
      _showNetworkError(error, stackTrace);
    });
  }

  //点击注册新用户
  void _onRegisterClick() {
    _unfocus();
    Navigator.of(context).pushNamed(RouteName.register);
  }

  //点击忘记密码
  void _onForgetPasswordClick() {
    _unfocus();
    Navigator.pushNamed(context, RouteName.forgetPassword);
  }

  //点击游客模式
  void _onTouristLoginClick() {
    _unfocus();
    //调用临时账号注册接口,生成游客userInfo
    BDHResponsitory.touristLogin(cancelToken: (CancelToken())).then((result) {
      if (!mounted) {
        return;
      }
      if (result.success ?? false) {
        StorageManager.storage!.setItem(kUser, jsonEncode(result.toJson()));
        Navigator.of(context).pushReplacementNamed(RouteName.tabMain);
      }
    }).onError((error, stackTrace) {
      if (!mounted) {
        return;
      }
      _showNetworkError(error, stackTrace);
    });
  }

  //往往是网络异常，例如未联网，未找到服务器,服务器长时间未响应等，
  //RequestException.handleError 方法中有拦截异常的处理方式。
  void _showNetworkError(Object? error, StackTrace stackTrace) {
    FlutterError.dumpErrorToConsole(FlutterErrorDetails(
        exception: error ?? "unknown error", stack: stackTrace));

    var exception = RequestException.handleError(error);
    if (exception.isCancel) {
      return;
    }
    if (exception.message != null) {
      showToast(exception.message!);
    }
  }

  //发送验证码,返回 true 表示进入倒计时
  FutureOr<bool> _onSendSmsClick() async {
    if (RegUtil.isPhoneNumber(_phoneController.text)) {
      _unfocus();
      BrnLoadingDialog.show(context,
          content: "请求中..  ", barrierDismissible: false);
      var result = await BDHResponsitory.getSmsCode(
          {"phone": _phoneController.text, "template": ""}).then((result) {
        if (!mounted) {
          return false;
        }
        BrnLoadingDialog.dismiss(context);
        if (result.success ?? false) {
          showToast(result.msg ?? "");
          _smsFocusNode.requestFocus();
          return true;
        }
        return false;
      }).onError((error, stackTrace) {
        if (!mounted) {
          return false;
        }
        BrnLoadingDialog.dismiss(context);
        _showNetworkError(error, stackTrace);
        return false;
      });

      return result;
    } else {
      showToast("请输入正确的手机号");
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: PopScope(
        onPopInvokedWithResult: (b, r) {},
        child: GestureDetector(
          onTap: () {
            _unfocus();
          },
          child: Stack(
            fit: StackFit.expand,
            children: [_backgroundWidget(), _bodyWidget()],
          ),
        ),
      ),
    );
  }

  Widget _backgroundWidget() {
    return SvgPicture.asset(
        alignment: Alignment.topCenter,
        fit: BoxFit.fitWidth,
        ImageHelper.wrapAssets("dahing_login_head.svg"));
  }

  Widget _bodyWidget() {
    var mediaQuery = MediaQuery.of(context);

    return SafeArea(
        child: SingleChildScrollView(
      physics: const ClampingScrollPhysics(),
      controller: _scrollController,
      child: Container(
          height: mediaQuery.size.height -
              mediaQuery.viewPadding.bottom -
              mediaQuery.padding.top,
          padding: EdgeInsets.only(
              left: 30.px,
              right: 30.px,
              top: 104.px - mediaQuery.padding.top,
              bottom: 25.px),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Text(
                "你好,\n欢迎使用大垦农服",
                strutStyle: StrutStyle(
                  fontSize: 29.px,
                  forceStrutHeight: true,
                ),
                style: TextStyle(
                    fontSize: 23.px,
                    fontWeight: FontWeight.w600,
                    color: const Color.fromRGBO(42, 43, 45, 1)),
              ),
              Padding(
                  padding: EdgeInsets.only(top: 15.px),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _isSmsLogin = false;
                          });
                          _unfocus();
                        },
                        child: Text("密码登录",
                            strutStyle: StrutStyle(
                              fontSize: 21.px,
                              forceStrutHeight: true,
                            ),
                            style: TextStyle(
                                fontSize: 15.px,
                                fontWeight: FontWeight.w600,
                                color: _isSmsLogin
                                    ? const Color.fromRGBO(44, 44, 52, 0.4)
                                    : const Color.fromRGBO(19, 162, 104, 1))),
                      ),
                      Container(
                        height: 15.px,
                        width: 2.px,
                        color: const Color.fromRGBO(217, 217, 217, 1),
                        margin: EdgeInsets.only(left: 15.px, right: 15.px),
                        alignment: Alignment.center,
                      ),
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _isSmsLogin = true;
                          });
                          _unfocus();
                        },
                        child: Text("验证码登录",
                            strutStyle: StrutStyle(
                              fontSize: 21.px,
                              forceStrutHeight: true,
                            ),
                            style: TextStyle(
                                fontSize: 15.px,
                                fontWeight: FontWeight.w600,
                                color: !_isSmsLogin
                                    ? const Color.fromRGBO(44, 44, 52, 0.4)
                                    : const Color.fromRGBO(19, 162, 104, 1))),
                      ),
                    ],
                  )),
              _formWidget(),
              const Spacer(),
              _bottomWidget()
            ],
          )),
    ));
  }

  Widget _bottomWidget() {
    return Center(
        child: SizedBox(
      width: 103.px,
      height: 29.px,
      child: TextButton(
        style: ButtonStyle(
          backgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return Colors.grey;
            } else if (states.contains(WidgetState.pressed)) {
              return const Color.fromRGBO(1, 1, 1, 0.1);
            } else {
              return Colors.transparent;
            }
          }),
          side: WidgetStatePropertyAll(BorderSide(
            width: 1.px,
            color: const Color.fromRGBO(226, 228, 228, 1),
          )),
          overlayColor: WidgetStateProperty.all(Colors.transparent),
        ),
        onPressed: _onTouristLoginClick,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(
                width: 13.px, ImageHelper.wrapAssets("icon_people.svg")),
            SizedBox(
              width: 2.px,
            ),
            Text(
              "游客模式",
              strutStyle: StrutStyle(
                fontSize: 12.px,
                forceStrutHeight: true,
              ),
              style: TextStyle(
                  fontSize: 12.px,
                  decoration: TextDecoration.none,
                  color: const Color.fromRGBO(44, 44, 52, 0.6)),
            ),
          ],
        ),
      ),
    ));
  }

  Widget _formWidget() {
    return Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: Column(
          children: [
            BdhTextInputSmall(
              item: FormItem(isRequired: false),
              fontSize: 14.px,
              placeHolder: "请输入手机号码",
              initialValue: _phoneController.text,
              controller: _phoneController,
              textInputType: TextInputType.phone,
              focusNode: _phoneFocusNode,
              onChange: (v) {},
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "";
                }
                // else if (!RegUtil.isPhoneNumber(value)) {
                //   return "请输入正确的手机号";
                // }

                return null;
              },
            ),
            Visibility(
                visible: !_isSmsLogin,
                child: BdhTextInputSmall(
                  item: FormItem(isRequired: false),
                  fontSize: 14.px,
                  placeHolder: "请输入密码",
                  initialValue: _passwordController.text,
                  controller: _passwordController,
                  focusNode: _passwordFocusNode,
                  onChange: (v) {},
                  obscureText: _isShowPassword ? false : true,
                  validator: (value) {
                    if (_isSmsLogin) {
                      return null;
                    }
                    if (value == null || value.isEmpty) {
                      return "";
                    }

                    return null;
                  },
                  rightWidgetBuilder: (context, field) {
                    return _isShowPassword
                        ? GestureDetector(
                            onTap: () {
                              setState(() {
                                _isShowPassword = false;
                              });
                            },
                            child: Padding(
                                padding: EdgeInsets.all(12.px),
                                child: SvgPicture.asset(
                                    width: 20.px,
                                    ImageHelper.wrapAssets(
                                        "icon_password_show.svg"))))
                        : GestureDetector(
                            onTap: () {
                              setState(() {
                                _isShowPassword = true;
                              });
                            },
                            child: Padding(
                                padding: EdgeInsets.all(12.px),
                                child: SvgPicture.asset(
                                    width: 20.px,
                                    ImageHelper.wrapAssets(
                                        "icon_password_hide.svg"))));
                  },
                )),
            Visibility(
                visible: _isSmsLogin,
                maintainState: true,
                child: BdhTextInputSmall(
                  item: FormItem(isRequired: false),
                  fontSize: 14.px,
                  placeHolder: "请输入验证码",
                  textInputType: TextInputType.number,
                  initialValue: _smsController.text,
                  controller: _smsController,
                  focusNode: _smsFocusNode,
                  onChange: (v) {},
                  rightWidgetBuilder: (context, field) {
                    return SmsCodeWidget(
                      validator: _onSendSmsClick,
                    );
                  },
                  validator: (value) {
                    if (!_isSmsLogin) {
                      return null;
                    }
                    if (value == null || value.isEmpty) {
                      return "";
                    }

                    return null;
                  },
                )),
            Container(
              margin: EdgeInsets.only(top: 15.px, bottom: 50.px),
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  BdhCheck(
                      width: 15.px,
                      isCircle: true,
                      isCheck: _isChecked,
                      onClick: (check) {
                        setState(() {
                          _isChecked = check;
                        });
                      }),
                  SizedBox(
                    width: 5.px,
                  ),
                  Text.rich(
                      strutStyle: StrutStyle(
                        fontSize: 12.px,
                        forceStrutHeight: true,
                      ),
                      TextSpan(children: [
                        TextSpan(
                            text: "我已阅读并同意",
                            style: TextStyle(
                                color: const Color.fromRGBO(44, 44, 52, 1),
                                fontSize: 12.px)),
                        TextSpan(
                          recognizer: _tapGestureRecognizer,
                          text: "《用户协议》",
                          style: TextStyle(
                              color: const Color.fromRGBO(22, 183, 96, 1),
                              fontSize: 12.px),
                        ),
                        TextSpan(
                            text: "和",
                            style: TextStyle(
                                color: const Color.fromRGBO(44, 44, 52, 1),
                                fontSize: 12.px)),
                        TextSpan(
                          recognizer: _tapGestureRecognizer2,
                          text: "《隐私政策》",
                          style: TextStyle(
                              color: const Color.fromRGBO(10, 174, 108, 1),
                              fontSize: 12.px),
                        ),
                      ]))
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                    width: 315.px,
                    height: 49.px,
                    child: TextButton(
                      style: ButtonStyle(
                          shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(9.px))),
                          backgroundColor:
                              WidgetStateProperty.resolveWith((states) {
                            if (states.contains(WidgetState.disabled)) {
                              return Colors.grey;
                            } else if (states.contains(WidgetState.pressed)) {
                              return const Color.fromRGBO(23, 156, 102, 0.8);
                            } else {
                              return const Color.fromRGBO(23, 156, 102, 1);
                            }
                          }),
                          overlayColor:
                              WidgetStateProperty.all(Colors.transparent)),
                      onPressed: _onLoginClick,
                      child: Text(
                        "登录",
                        style: TextStyle(
                            fontSize: 17.px,
                            decoration: TextDecoration.none,
                            color: Colors.white),
                      ),
                    )),
              ],
            ),
            Padding(
                padding: EdgeInsets.only(top: 15.px),
                child: Row(
                  children: [
                    TextButton(
                      style: ButtonStyle(
                        padding: const WidgetStatePropertyAll(EdgeInsets.zero),
                        elevation: const WidgetStatePropertyAll(0),
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        foregroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.disabled)) {
                            return Colors.grey;
                          } else if (states.contains(WidgetState.pressed)) {
                            return const Color.fromRGBO(44, 44, 52, 0.5);
                          } else {
                            return const Color.fromRGBO(44, 44, 52, 1);
                          }
                        }),
                      ),
                      onPressed: _onRegisterClick,
                      child: Text(
                        "新用户注册",
                        strutStyle: StrutStyle(
                          fontSize: 16.8.px,
                          forceStrutHeight: true,
                        ),
                        style: TextStyle(
                          fontSize: 12.px,
                          decoration: TextDecoration.none,
                        ),
                      ),
                    ),
                    const Spacer(),
                    TextButton(
                      style: ButtonStyle(
                        padding: const WidgetStatePropertyAll(EdgeInsets.zero),
                        elevation: const WidgetStatePropertyAll(0),
                        overlayColor:
                            const WidgetStatePropertyAll(Colors.transparent),
                        foregroundColor:
                            WidgetStateProperty.resolveWith((states) {
                          if (states.contains(WidgetState.disabled)) {
                            return Colors.grey;
                          } else if (states.contains(WidgetState.pressed)) {
                            return const Color.fromRGBO(44, 44, 52, 0.5);
                          } else {
                            return const Color.fromRGBO(44, 44, 52, 1);
                          }
                        }),
                      ),
                      onPressed: _onForgetPasswordClick,
                      child: Text(
                        "忘记密码",
                        strutStyle: StrutStyle(
                          fontSize: 16.8.px,
                          forceStrutHeight: true,
                        ),
                        style: TextStyle(
                          fontSize: 12.px,
                          decoration: TextDecoration.none,
                        ),
                      ),
                    ),
                  ],
                )),
          ],
        ));
  }
}
