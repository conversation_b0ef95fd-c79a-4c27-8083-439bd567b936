//获取验证码
import 'dart:async';

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/widgets.dart';

class SmsCodeWidget extends StatefulWidget {
  final String btnText;

  //点击后需要等到多久才能再次点击
  final int waitSeconds;

  final FutureOr<bool> Function()? validator;

  final Color? textColor;

  const SmsCodeWidget({
    super.key,
    this.btnText = "获取验证码",
    this.waitSeconds = 60,
    this.validator,
    this.textColor,
  });

  @override
  State<SmsCodeWidget> createState() => _SmsCodeWidgetState();
}

class _SmsCodeWidgetState extends State<SmsCodeWidget> {
  Timer? _timer;

  int? _timeoutSeconds;

  String get _btnText {
    return _timeoutSeconds == null ? widget.btnText : "请$_timeoutSeconds秒后再试";
  }

  void _startTimer(int waitSeconds) {
    _cancelTimer();

    setState(() {
      _timeoutSeconds ??= waitSeconds;
    });
    //记时开始时间
    final nowTime = DateTime.now();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      int diffSeconds = DateTime.now().difference(nowTime).inSeconds;
      if (diffSeconds >= waitSeconds) {
        _timer?.cancel();

        setState(() {
          _timeoutSeconds = null;
        });
      } else {
        setState(() {
          _timeoutSeconds = waitSeconds - diffSeconds;
        });
      }
    });
  }

  void _cancelTimer() {
    if (_timer != null && _timer!.isActive) {
      _timer?.cancel();
    }
    _timer = null;
    _timeoutSeconds = null;
  }

  void _onSendClick() async {
    bool canSend = true;
    if (widget.validator != null) {
      var result = widget.validator!();
      if (result is Future) {
        canSend = await result;
      } else {
        canSend = result;
      }
    }
    canSend = canSend && _timeoutSeconds == null;
    if (canSend) {
      _startTimer(widget.waitSeconds);
    }
  }

  @override
  void dispose() {
    super.dispose();
    _cancelTimer();
  }

  @override
  Widget build(BuildContext context) {
    Widget child = Padding(
        padding: EdgeInsets.only(left: 15.px),
        child: Text.rich(
            strutStyle: StrutStyle(fontSize: 14.px, forceStrutHeight: true),
            TextSpan(
              text: _btnText,
              style: TextStyle(
                  color: _timeoutSeconds == null
                      ? widget.textColor ??
                          const Color.fromRGBO(10, 174, 108, 1)
                      : const Color.fromRGBO(44, 44, 52, 0.4),
                  fontSize: 14.px,
                  fontWeight: FontWeight.w600),
            )));
    return GestureDetector(
      onTap: _timeoutSeconds == null ? _onSendClick : null,
      child: child,
    );
  }

  @override
  void didUpdateWidget(covariant SmsCodeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.waitSeconds != widget.waitSeconds ||
        oldWidget.btnText != widget.btnText) {
      _cancelTimer();
      setState(() {});
    }
  }
}
