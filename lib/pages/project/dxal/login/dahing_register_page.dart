import 'dart:async';
import 'dart:convert';

import 'package:bdh_smart_agric_app/components/form/bdh_text_input_small.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:oktoast/oktoast.dart';

import '../../../home/<USER>/components/bdh_check.dart';
import 'widget/sms_code_widget.dart';

//大兴用户注册
class DaHingRegisterPage extends StatefulWidget {
  const DaHingRegisterPage({super.key});

  @override
  State<StatefulWidget> createState() => DaHingRegisterPageState();
}

class DaHingRegisterPageState extends State<DaHingRegisterPage>
    with AutoDisposeStateMixin {
  final GlobalKey _formKey = GlobalKey<FormState>();

  late final ScrollController _scrollController;
  //手机号
  late final TextEditingController _phoneController;
  //密码
  late final TextEditingController _passwordController;
  //重复输入密码
  late final TextEditingController _passwordController2;
  //短信验证码
  late final TextEditingController _smsController;

  late final TapGestureRecognizer _tapGestureRecognizer;

  late final TapGestureRecognizer _tapGestureRecognizer2;

  late final FocusScopeNode _phoneFocusNode;

  late final FocusScopeNode _passwordFocusNode;

  late final FocusScopeNode _passwordFocusNode2;

  late final FocusScopeNode _smsFocusNode;

  //显示密码明文
  bool _isShowPassword = false;

  //同意隐私协议
  bool _isChecked = false;

  @override
  void initState() {
    super.initState();

    //通过 use 在 dispose 时会自动调用销毁,防止内存泄露
    _scrollController = useScrollController(ScrollController());
    _passwordController = useTextController(TextEditingController());
    _passwordController2 = useTextController(TextEditingController());
    _phoneController = useTextController(TextEditingController());
    _smsController = useTextController(TextEditingController());
    _phoneFocusNode = useFocusScopeNode(FocusScopeNode());
    _passwordFocusNode = useFocusScopeNode(FocusScopeNode());
    _passwordFocusNode2 = useFocusScopeNode(FocusScopeNode());
    _smsFocusNode = useFocusScopeNode(FocusScopeNode());

    _tapGestureRecognizer = useTapGestureRecognizer(
        TapGestureRecognizer()..onTap = _onClickUserAgreement);

    _tapGestureRecognizer2 = useTapGestureRecognizer(
        TapGestureRecognizer()..onTap = _onClickPrivacy);
  }

  void _onClickUserAgreement() {
    if (!mounted) {
      return;
    }
    Navigator.of(context).push(CupertinoPageRoute(
        builder: (_) => const HtmlPage(
            title: "用户协议",
            content: "https://www.bdhic.com/Anticipation.html")));
  }

  void _onClickPrivacy() {
    if (!mounted) {
      return;
    }
    Navigator.of(context).push(CupertinoPageRoute(
        builder: (_) => const HtmlPage(
            title: "隐私政策",
            content: "https://smartagric.bdhic.com/privacy.html")));
  }

  //通过此方式让输入法消失
  void _unfocus() {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  //点击提交
  void _onRegisterClick() {
    //隐藏输入法
    _unfocus();
    var state = _formKey.currentState as FormState;

    //校验表单合法性
    bool validate = state.validate();
    if (!validate) {
      return;
    }

    //校验是否同意隐私协议
    if (!_isChecked) {
      showToast("请先阅读并同意隐私协议");
      return;
    }
    //这里多加了一层校验,实际上可以不需要
    if (_phoneController.text.isEmpty || _smsController.text.isEmpty) {
      showToast("手机号或验证码不能为空");
      return;
    }

    if (_passwordController.text.isEmpty) {
      showToast("请输入密码");
      return;
    }
    if (_passwordController2.text.isEmpty) {
      showToast("请输入确认密码");
      return;
    }

    if (_passwordController2.text != _passwordController.text) {
      showToast("两次密码不一致");
      return;
    }
    _register();
  }

  void _register() {
    var password = _passwordController.text;
    var bytes = utf8.encode(password.toUpperCase());
    //第一层MD5
    var firstMD5 = '${md5.convert(bytes)}'.toUpperCase();
    //第二层MD5
    var md5Password = '${md5.convert(utf8.encode(firstMD5))}'.toLowerCase();

    BrnLoadingDialog.show(context,
        content: "正在注册..  ", barrierDismissible: false);
    BDHResponsitory.register({
      "loginName": _phoneController.text,
      "telephone": _phoneController.text,
      "code": _smsController.text,
      "password": md5Password,
      "systemCode": "bdh-app"
    }, cancelToken: useCancelToken(CancelToken()))
        .then((result) {
      if (!mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);
      if (result.success ?? false) {
        showToast(result.msg ?? "注册成功");
        Navigator.of(context).pop(true);
      }
    }).onError((error, stackTrace) {
      if (!mounted) {
        return;
      }
      BrnLoadingDialog.dismiss(context);
      _showNetworkError(error, stackTrace);
    });
  }

  //往往是网络异常，例如未联网，未找到服务器,服务器长时间未响应等，
  //RequestException.handleError 方法中有拦截异常的处理方式。
  void _showNetworkError(Object? error, StackTrace stackTrace) {
    FlutterError.dumpErrorToConsole(FlutterErrorDetails(
        exception: error ?? "unknown error", stack: stackTrace));

    var exception = RequestException.handleError(error);
    if (exception.isCancel) {
      return;
    }
    if (exception.message != null) {
      showToast(exception.message!);
    }
  }

  //发送验证码,返回 true 表示进入倒计时
  FutureOr<bool> _onSendSmsClick() async {
    if (RegUtil.isPhoneNumber(_phoneController.text)) {
      _unfocus();
      BrnLoadingDialog.show(context,
          content: "请求中..  ", barrierDismissible: false);
      var result = await BDHResponsitory.getRegisterSmsCode(
          {"phone": _phoneController.text, "template": ""}).then((result) {
        if (!mounted) {
          return false;
        }
        BrnLoadingDialog.dismiss(context);
        if (result.success ?? false) {
          showToast(result.msg ?? "");
          _smsFocusNode.requestFocus();
          return true;
        }
        return false;
      }).onError((error, stackTrace) {
        if (!mounted) {
          return false;
        }
        BrnLoadingDialog.dismiss(context);
        _showNetworkError(error, stackTrace);
        return false;
      });

      return result;
    } else {
      showToast("请输入正确的手机号");
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      body: PopScope(
        onPopInvokedWithResult: (b, r) {},
        child: GestureDetector(
          onTap: () {
            _unfocus();
          },
          child: Stack(
            fit: StackFit.expand,
            children: [_backgroundWidget(), _bodyWidget()],
          ),
        ),
      ),
    );
  }

  Widget _backgroundWidget() {
    return SvgPicture.asset(
        alignment: Alignment.topCenter,
        fit: BoxFit.fitWidth,
        ImageHelper.wrapAssets("dahing_login_head.svg"));
  }

  Widget _bodyWidget() {
    var mediaQuery = MediaQuery.of(context);

    return SafeArea(
        child: CustomScrollView(
      physics: const ClampingScrollPhysics(),
      controller: _scrollController,
      slivers: [
        const SliverAppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text("新用户注册"),
        ),
        SliverToBoxAdapter(
          child: Container(
              height: mediaQuery.size.height -
                  mediaQuery.viewPadding.bottom -
                  mediaQuery.padding.top -
                  kToolbarHeight,
              padding:
                  EdgeInsets.only(left: 30.px, right: 30.px, bottom: 25.px),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  SizedBox(
                      height: 104.px - mediaQuery.padding.top - kToolbarHeight),
                  Text(
                    "你好,\n欢迎使用大垦农服",
                    strutStyle: StrutStyle(
                      fontSize: 29.px,
                      forceStrutHeight: true,
                    ),
                    style: TextStyle(
                        fontSize: 23.px,
                        fontWeight: FontWeight.w600,
                        color: const Color.fromRGBO(42, 43, 45, 1)),
                  ),
                  _formWidget(),
                ],
              )),
        ),
      ],
    ));
  }

  Widget _formWidget() {
    return Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.disabled,
        child: Column(
          children: [
            SizedBox(
              height: 20.px,
            ),
            BdhTextInputSmall(
              item: FormItem(isRequired: false),
              fontSize: 14.px,
              placeHolder: "请输入手机号码",
              initialValue: _phoneController.text,
              controller: _phoneController,
              textInputType: TextInputType.phone,
              focusNode: _phoneFocusNode,
              onChange: (v) {},
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "";
                } else if (!RegUtil.isPhoneNumber(value)) {
                  return "请输入正确的手机号";
                }

                return null;
              },
            ),
            BdhTextInputSmall(
              item: FormItem(isRequired: false),
              fontSize: 14.px,
              placeHolder: "请输入验证码",
              textInputType: TextInputType.number,
              initialValue: _smsController.text,
              controller: _smsController,
              focusNode: _smsFocusNode,
              onChange: (v) {},
              rightWidgetBuilder: (context, field) {
                return SmsCodeWidget(
                  validator: _onSendSmsClick,
                );
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "";
                }

                return null;
              },
            ),
            BdhTextInputSmall(
              item: FormItem(isRequired: false),
              fontSize: 14.px,
              placeHolder: "请输入密码",
              initialValue: _passwordController.text,
              controller: _passwordController,
              focusNode: _passwordFocusNode,
              onChange: (v) {},
              obscureText: _isShowPassword ? false : true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "";
                }

                return null;
              },
              rightWidgetBuilder: (context, field) {
                return _isShowPassword
                    ? GestureDetector(
                        onTap: () {
                          setState(() {
                            _isShowPassword = false;
                          });
                        },
                        child: Padding(
                            padding: EdgeInsets.all(12.px),
                            child: SvgPicture.asset(
                                width: 20.px,
                                ImageHelper.wrapAssets(
                                    "icon_password_show.svg"))))
                    : GestureDetector(
                        onTap: () {
                          setState(() {
                            _isShowPassword = true;
                          });
                        },
                        child: Padding(
                            padding: EdgeInsets.all(12.px),
                            child: SvgPicture.asset(
                                width: 20.px,
                                ImageHelper.wrapAssets(
                                    "icon_password_hide.svg"))));
              },
            ),
            BdhTextInputSmall(
              item: FormItem(isRequired: false),
              fontSize: 14.px,
              placeHolder: "请再次输入密码",
              initialValue: _passwordController2.text,
              controller: _passwordController2,
              focusNode: _passwordFocusNode2,
              onChange: (v) {},
              obscureText: _isShowPassword ? false : true,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return "";
                }

                return null;
              },
              rightWidgetBuilder: (context, field) {
                return _isShowPassword
                    ? GestureDetector(
                        onTap: () {
                          setState(() {
                            _isShowPassword = false;
                          });
                        },
                        child: Padding(
                            padding: EdgeInsets.all(12.px),
                            child: SvgPicture.asset(
                                width: 20.px,
                                ImageHelper.wrapAssets(
                                    "icon_password_show.svg"))))
                    : GestureDetector(
                        onTap: () {
                          setState(() {
                            _isShowPassword = true;
                          });
                        },
                        child: Padding(
                            padding: EdgeInsets.all(12.px),
                            child: SvgPicture.asset(
                                width: 20.px,
                                ImageHelper.wrapAssets(
                                    "icon_password_hide.svg"))));
              },
            ),
            Container(
              margin: EdgeInsets.only(top: 15.px, bottom: 50.px),
              alignment: Alignment.centerLeft,
              child: Row(
                children: [
                  BdhCheck(
                      width: 15.px,
                      isCircle: true,
                      isCheck: _isChecked,
                      onClick: (check) {
                        setState(() {
                          _isChecked = check;
                        });
                      }),
                  SizedBox(
                    width: 5.px,
                  ),
                  Text.rich(
                      strutStyle: StrutStyle(
                        fontSize: 12.px,
                        forceStrutHeight: true,
                      ),
                      TextSpan(children: [
                        TextSpan(
                            text: "我已阅读并同意",
                            style: TextStyle(
                                color: const Color.fromRGBO(44, 44, 52, 1),
                                fontSize: 12.px)),
                        TextSpan(
                          recognizer: _tapGestureRecognizer,
                          text: "《用户协议》",
                          style: TextStyle(
                              color: const Color.fromRGBO(22, 183, 96, 1),
                              fontSize: 12.px),
                        ),
                        TextSpan(
                            text: "和",
                            style: TextStyle(
                                color: const Color.fromRGBO(44, 44, 52, 1),
                                fontSize: 12.px)),
                        TextSpan(
                          recognizer: _tapGestureRecognizer2,
                          text: "《隐私政策》",
                          style: TextStyle(
                              color: const Color.fromRGBO(10, 174, 108, 1),
                              fontSize: 12.px),
                        ),
                      ]))
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                    width: 315.px,
                    height: 49.px,
                    child: TextButton(
                      style: ButtonStyle(
                          shape: WidgetStatePropertyAll(RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(9.px))),
                          backgroundColor:
                              WidgetStateProperty.resolveWith((states) {
                            if (states.contains(WidgetState.disabled)) {
                              return Colors.grey;
                            } else if (states.contains(WidgetState.pressed)) {
                              return const Color.fromRGBO(23, 156, 102, 0.8);
                            } else {
                              return const Color.fromRGBO(23, 156, 102, 1);
                            }
                          }),
                          overlayColor:
                              WidgetStateProperty.all(Colors.transparent)),
                      onPressed: _onRegisterClick,
                      child: Text(
                        "确认",
                        style: TextStyle(
                            fontSize: 17.px,
                            decoration: TextDecoration.none,
                            color: Colors.white),
                      ),
                    )),
              ],
            ),
          ],
        ));
  }
}
