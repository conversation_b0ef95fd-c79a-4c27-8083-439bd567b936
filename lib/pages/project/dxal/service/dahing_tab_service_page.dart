import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/search_new_page.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/news_message_home.dart';
import 'package:bdh_smart_agric_app/utils/collection_extensions.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluwx/fluwx.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';

class DaHingTabServicePage extends StatefulWidget {
  final MenuConfigItem item;
  const DaHingTabServicePage({super.key, required this.item});

  @override
  State<StatefulWidget> createState() => _DaHingTabServicePageState();
}

class _DaHingTabServicePageState extends State<DaHingTabServicePage> {
  List<MenuConfigItem> topItems = [];
  List<MenuConfigItem> bottomGroups = [];

  @override
  void initState() {
    super.initState();
    for (var v in widget.item.children!) {
      if (v.authCode == "topMenu") {
        for (var topItem in v.children!) {
          topItems.add(topItem);
        }
      }
      // for tests
      //topItems.add(MenuConfigItem(authName: "土地承包", icon: topItems[0].icon));
      //topItems.add(MenuConfigItem(authName: "粮食交易", icon: topItems[0].icon));
      //topItems.add(MenuConfigItem(authName: "线上缴费", icon: topItems[0].icon));
      //topItems.add(MenuConfigItem(authName: "农业补贴", icon: topItems[0].icon));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "amKeep"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "ampKeep"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "cascades"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "costanalysis"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "court"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "do_machine"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "drip"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "find_machine"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "gasup"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "gpt"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "greenHouse"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "highStandard"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "invest"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "pumping"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "releaseIcon"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "samefill"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "sponsor"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "weather"));
      //topItems.add(MenuConfigItem(authName: "Name1", icon: "yangguangbaoxian"));

      // bottomGroups
      //     .add(MenuConfigItem(authName: "authName1", children: topItems));
      // bottomGroups
      //     .add(MenuConfigItem(authName: "authName2", children: topItems));
      // bottomGroups
      //     .add(MenuConfigItem(authName: "authName3", children: topItems));

      if (v.authCode == "bottomMenu") {
        for (var bottomGroup in v.children!) {
          bottomGroups.add(bottomGroup);
        }
      }
    }
  }

  Widget _backgroundWidget() {
    return Stack(
      fit: StackFit.expand,
      children: [
        SvgPicture.asset(
            alignment: Alignment.topCenter,
            fit: BoxFit.fitWidth,
            ImageHelper.wrapAssets("bg_dahing_tab_service.svg")),
        Positioned(
          top: 77.px,
          left: 24.5.px,
          child: SvgPicture.asset(
            alignment: Alignment.topCenter,
            fit: BoxFit.fitWidth,
            ImageHelper.wrapAssets("bg2_dahing_tab_service.svg"),
          ),
        ),
        Positioned(
          top: 109.5.px,
          left: 27.px,
          child: SvgPicture.asset(
            alignment: Alignment.topCenter,
            fit: BoxFit.fitWidth,
            ImageHelper.wrapAssets("title_dahing_tab_service.svg"),
          ),
        ),
        Positioned(
          top: 81.5.px,
          right: 27.5.px,
          child: Image.asset(
            ImageHelper.wrapAssets("land_contract_headIcon.png"),
            width: 131.px,
          ),
        ),
      ],
    );
    // return;
    // return Image.asset(
    //     fit: BoxFit.fitWidth,
    //     alignment: Alignment.topCenter,
    //     ImageHelper.wrapAssets("bg_dahing_tab_service.png"));
  }

  Widget _bodyWidget() {
    return Column(
      children: [
        AppBar(
          toolbarHeight: 40.px,
          backgroundColor: Colors.transparent,
          titleSpacing: 0,
          actions: [
            // GestureDetector(
            //   onTap: () {
            //     NativeUtil.openUni({"path": "/pages/gpt2/index"});
            //   },
            //   child: Image.asset(
            //       width: 24.px,
            //       height: 24.px,
            //       ImageHelper.wrapAssets("bdh_chat.png")),
            // ),
            //  SizedBox(
            //     width: 5.px,
            //   ),
            GestureDetector(
              onTap: () {
                Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                  return const NewsMessageHome();
                }));
              },
              child: SvgPicture.asset(
                  alignment: Alignment.center,
                  fit: BoxFit.cover,
                  width: 24.px,
                  ImageHelper.wrapAssets("ic_chat.svg")),
            ),
            SizedBox(
              width: 5.px,
            ),
            GestureDetector(
              onTap: () {
                Navigator.of(context)
                    .push(CupertinoPageRoute(builder: (context) {
                  return const SearchNewPage();
                }));
              },
              child: SvgPicture.asset(
                  alignment: Alignment.center,
                  fit: BoxFit.cover,
                  width: 24.px,
                  colorFilter:
                      const ColorFilter.mode(Colors.white, BlendMode.srcIn),
                  ImageHelper.wrapAssets("ic_search.svg")),
            ),
            SizedBox(
              width: 20.px,
            ),
          ],
        ),
        SizedBox(
          height: 134.px,
        ),
        Container(
          width: 351.px,
          decoration: BoxDecoration(
              color: const Color.fromRGBO(255, 255, 255, 0.5),
              borderRadius: BorderRadius.all(Radius.circular(9.px))),
          margin: EdgeInsets.only(bottom: 5.px),
          padding: EdgeInsets.only(
              top: 15.px, left: 18.px, right: 15.px, bottom: 18.px),
          child: Wrap(
            runSpacing: 20.px,
            children: [
              ...topItems.map((e) {
                return MenuItemView(
                  item: e,
                  isGroup: false,
                );
              })
            ],
          ),
        ),
        if (bottomGroups.isNotEmpty)
          Expanded(
            child: CustomScrollView(
              slivers: [
                SliverPadding(padding: EdgeInsets.only(top: 10.px)),
                SliverToBoxAdapter(
                    child: Column(
                  children: [
                    ...bottomGroups.map((group) {
                      return Container(
                        width: 351.px,
                        decoration: BoxDecoration(
                            color: const Color.fromRGBO(255, 255, 255, 0.5),
                            borderRadius:
                                BorderRadius.all(Radius.circular(9.px))),
                        margin: EdgeInsets.only(bottom: 15.px),
                        padding: EdgeInsets.only(
                            top: 15.px,
                            left: 18.px,
                            right: 15.px,
                            bottom: 18.px),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              group.authName ?? "",
                              style: TextStyle(
                                  fontSize: 12.px,
                                  fontWeight: FontWeight.w400,
                                  color: const Color.fromRGBO(0, 0, 0, 0.4)),
                            ),
                            SizedBox(
                              height: 15.px,
                            ),
                            SizedBox(
                              width: 315.px,
                              child: Wrap(
                                runSpacing: 20.px,
                                children: [
                                  ...(group.children ?? []).map((e) {
                                    return MenuItemView(
                                      item: e,
                                      isGroup: true,
                                    );
                                  })
                                ],
                              ),
                            )
                          ],
                        ),
                      );
                    })
                  ],
                ))
              ],
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: Stack(
        fit: StackFit.expand,
        children: [_backgroundWidget(), _bodyWidget()],
      ),
    );
  }
}

//定制服务器下发的 icon
const _overlayMenuIcons = [
  {
    "authName": "粮食交易",
    "icon": "ic_grain_trading.svg",
    "backgroundColors": [
      Color.fromRGBO(16, 185, 163, 1),
      Color.fromRGBO(16, 185, 163, 0.5)
    ]
  },
  {
    "authName": "土地承包",
    "icon": "ic_info_collect.svg",
    "backgroundColors": [
      Color.fromRGBO(7, 204, 151, 1),
      Color.fromRGBO(133, 234, 197, 1)
    ]
  },
  {
    "authName": "线上缴费",
    "icon": "ic_pay_online.svg",
    "backgroundColors": [
      Color.fromRGBO(255, 172, 18, 1),
      Color.fromRGBO(245, 228, 132, 1)
    ]
  },
  {
    "authName": "农业补贴",
    "icon": "ic_agricultural_subsidy.svg",
    "backgroundColors": [
      Color.fromRGBO(23, 119, 255, 1),
      Color.fromRGBO(89, 195, 255, 1)
    ]
  },
];

class MenuItemView extends StatelessWidget {
  final bool isGroup;
  final MenuConfigItem item;
  const MenuItemView({super.key, required this.item, this.isGroup = false});

  Widget _widgetIcon() {
    var overlayMenuIcon = _overlayMenuIcons
        .firstWhereOrNull((test) => test["authName"] == item.authName);

    if (overlayMenuIcon != null) {
      return Container(
          width: 40.px,
          height: 40.px,
          decoration: BoxDecoration(
              gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: overlayMenuIcon["backgroundColors"] as List<Color>),
              borderRadius: BorderRadius.all(Radius.circular(10.px))),
          child: Center(
              child: SvgPicture.asset(
                  alignment: Alignment.center,
                  fit: BoxFit.cover,
                  width: 24.px,
                  height: 24.px,
                  ImageHelper.wrapAssets(overlayMenuIcon["icon"] as String))));
    } else {
      return Image.asset(
          width: 40.px,
          height: 40.px,
          ImageHelper.wrapOldAssets("${item.icon!}.png"),
          errorBuilder: (context, error, stackTrace) {
        return Image.asset(
            width: 40.px,
            height: 40.px,
            ImageHelper.wrapAssets("menu_collect.png"));
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (item.url!.startsWith("gh_")) {
          Fluwx fluwx = Fluwx();
          fluwx.open(target: MiniProgram(username: item.url!));
        } else if (item.url!.startsWith("pages")) {
          //跳转uni小程序
          //土地承包特殊处理
          if (item.authName == "土地承包") {
            Navigator.of(context).pushNamed(RouteName.bdhLandContract);
          } else {
            NativeUtil.openUni({"path": item.url});
          }
        } else {
          Navigator.of(context)
              .pushNamed(item.url ?? "无效路径", arguments: item)
              .then((res) {
            GlobalServiceView.needShowServiceBtn('service');
          });
        }
      },
      child: Container(
        width: 78.75.px,
        color: Colors.transparent,
        child: Column(
          children: [
            _widgetIcon(),
            SizedBox(
              height: 5.px,
            ),
            Text(
              item.authName ?? "",
              textAlign: TextAlign.center,
              strutStyle: StrutStyle(fontSize: 12.px, forceStrutHeight: true),
              style: TextStyle(
                  color: const Color.fromRGBO(51, 51, 51, 1),
                  fontSize: 12.px,
                  fontWeight: FontWeight.w500),
            )
          ],
        ),
      ),
    );
  }
}

class MenuItem {
  String icon;
  String title;
  String path;
  MenuItem({required this.icon, required this.title, required this.path});
}

class MenuGroup {
  String title;
  List<MenuItem> children;
  MenuGroup({required this.title, required this.children});
}
