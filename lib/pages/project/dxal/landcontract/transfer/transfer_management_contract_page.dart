import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_steps.dart';
import 'package:bdh_smart_agric_app/model/query_my_contract_result_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_check.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import 'application_for_management_page.dart';
import 'transfer_management_plot_page.dart';

class TransferManagementContractPage extends StatefulWidget {
  final String title;
  final List<String>? steps;
  final int? stepIndex;
  const TransferManagementContractPage(
      {super.key, required this.title, this.steps, this.stepIndex});

  @override
  State<StatefulWidget> createState() => _TransferManagementContractPageState();
}

class _TransferManagementContractPageState
    extends State<TransferManagementContractPage> {
  List<MyContractItem> items = [];
  List<BdhStepsItemData> steps = [
    BdhStepsItemData(title: "选择合同"),
    BdhStepsItemData(title: "选择地块"),
    BdhStepsItemData(title: "选择对象"),
    BdhStepsItemData(title: "填写内容")
  ];
  bool isLoading = false;
  String searchkey = "";

  @override
  void initState() {
    super.initState();
    // Future.delayed(const Duration(milliseconds: 500), () {
    //   showDialog(
    //       context: context,
    //       builder: (ctx) {
    //         return const ProcessContainer();
    //       });
    // });
    loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 2),
      appBar: AppBar(
        title: const Text("经营权流转申请"),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Container(
              width: 375.px,
              height: 66.px,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(15.px),
                      bottomRight: Radius.circular(15.px))),
              child: BdhStepsHorizontal(
                steps: steps,
                activeIndex: 0,
              ),
            ),
            SizedBox(
              height: 10.px,
            ),
            LandSearchBar(
              placeholder: "请输入合同号",
              onSearch: (item) {
                searchkey = item.keyword ?? "";
                loadData();
              },
            ),
            Container(
              width: 351.px,
              padding: EdgeInsets.only(top: 15.px),
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  45.px -
                  66.px -
                  25.px -
                  37.px,
              child: isLoading
                  ? const ViewStateBusyWidget()
                  : (items.isEmpty
                      ? const BdhEmptyView()
                      : ListView.builder(
                          itemCount: items.length,
                          itemBuilder: (ctx, idx) {
                            return ContractItemView(item: items[idx]);
                          })),
            ),
          ],
        );
      }),
    );
  }

  loadData() {
    setState(() {
      isLoading = true;
    });
    LandResponsitory.queryMyContract({"searchKey": searchkey}).then((res) {
      setState(() {
        isLoading = false;
        items = res.data ?? [];
      });
    });
  }
}

class LandSearchBar extends StatefulWidget {
  final List<String>? filterData;
  final String? placeholder;
  final Function(SearchItem) onSearch;
  final Function(String)? onTypeChange;
  const LandSearchBar(
      {super.key,
      this.filterData,
      this.placeholder,
      required this.onSearch,
      this.onTypeChange});

  @override
  State<StatefulWidget> createState() => _LandSearchBarSearchBarState();
}

class _LandSearchBarSearchBarState extends State<LandSearchBar> {
  String curType = "查人员";
  String? keyword;
  TextEditingController textEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 345.px,
      height: 37.px,
      padding: EdgeInsets.only(left: 16.px, right: 16.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(5.px))),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          widget.filterData != null
              ? GestureDetector(
                  onTap: () {
                    TDPicker.showMultiPicker(context, onConfirm: (list) {
                      setState(() {
                        curType = (widget.filterData ?? [])[list[0]];
                      });
                      if (widget.onTypeChange != null) {
                        widget.onTypeChange!(curType);
                      }
                      Navigator.of(context).pop();
                    }, data: [widget.filterData ?? []]);
                  },
                  child: SizedBox(
                    width: 75.px,
                    child: Row(
                      children: [
                        Text(
                          curType,
                          style: TextStyle(
                              fontSize: 12.px, fontWeight: FontWeight.w500),
                        ),
                        const Icon(
                          Icons.arrow_drop_down,
                          color: Color.fromRGBO(122, 121, 124, 1),
                        ),
                        Container(
                          width: 1.px,
                          height: 16.px,
                          color: const Color.fromRGBO(217, 217, 217, 1),
                        )
                      ],
                    ),
                  ),
                )
              : Container(),
          SizedBox(
            width: widget.filterData != null ? 215.px : 297.px,
            child: CupertinoTextField.borderless(
              controller: textEditingController,
              padding: EdgeInsets.zero,
              placeholder: widget.placeholder ?? "请输入关键字",
              placeholderStyle: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w400,
                  color: Colors.grey),
            ),
          ),
          GestureDetector(
            onTap: () {
              widget.onSearch(SearchItem(curType, textEditingController.text));
            },
            child: Image.asset(
                width: 16.px,
                height: 16.px,
                ImageHelper.wrapAssets("search_grey.png")),
          )
        ],
      ),
    );
  }
}

class SearchItem {
  String? type;
  String? keyword;
  SearchItem(this.type, this.keyword);
}

class ContractItem {
  String? year;
  String? contractNo;
  num? plotArea;
  int? plotCount;
  int? transferCount;
  int? untransferCount;
  String? startDate;
  String? endDate;
  ContractItem(
      {this.year,
      this.contractNo,
      this.plotArea,
      this.plotCount,
      this.transferCount,
      this.untransferCount,
      this.startDate,
      this.endDate});
}

class ContractItemView extends StatefulWidget {
  final MyContractItem item;
  const ContractItemView({super.key, required this.item});

  @override
  State<StatefulWidget> createState() => ContractItemViewState();
}

class ContractItemViewState extends State<ContractItemView> {
  bool isCheck = false;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 351.px,
      height: 99.px,
      padding: EdgeInsets.all(16.px),
      margin: EdgeInsets.only(bottom: 10.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Tag(
                      name: "${widget.item.yearNo ?? 2024}",
                      color: const Color.fromRGBO(10, 174, 108, 1)),
                  SizedBox(
                    width: 10.px,
                  ),
                  Text(
                    widget.item.serialNumber ?? "",
                    style:
                        TextStyle(fontSize: 16.px, fontWeight: FontWeight.w600),
                  )
                ],
              ),
              BdhCheck(
                  isCheck: isCheck,
                  width: 15.px,
                  onClick: (check) {
                    setState(() {
                      isCheck = check;
                    });
                    if (isCheck == true) {
                      showDialog(
                          context: context,
                          builder: (ctx) {
                            return Align(
                              alignment: Alignment.bottomCenter,
                              child: Container(
                                margin: EdgeInsets.only(bottom: 20.px),
                                child: GestureDetector(
                                  onTap: () {
                                    Navigator.of(context).push(
                                        CupertinoPageRoute(builder: (ctx) {
                                      return TransferManagementPlotPage(
                                        contractId: widget.item.contractId!,
                                        details:
                                            widget.item.contractDetailList ??
                                                [],
                                      );
                                    }));
                                  },
                                  child: BDHButtonGreen(
                                      width: 341.px,
                                      height: 50.px,
                                      title: "下一步"),
                                ),
                              ),
                            );
                          }).then((res) {
                        setState(() {
                          isCheck = false;
                        });
                      });
                    }
                  })
            ],
          ),
          Row(
            children: [
              Row(
                children: [
                  Text(
                    "总面积",
                    style: TextStyle(
                        fontSize: 12.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(41, 41, 52, 0.6)),
                  ),
                  SizedBox(
                    width: 5.px,
                  ),
                  Text("${widget.item.totalArea ?? 0}亩",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w500,
                          color: const Color.fromRGBO(41, 41, 52, 1)))
                ],
              ),
              Container(
                margin: EdgeInsets.only(left: 10.px, right: 10.px),
                width: 1.px,
                height: 9.px,
                color: const Color.fromRGBO(223, 223, 223, 1),
              ),
              Row(
                children: [
                  Text(
                    "总地块",
                    style: TextStyle(
                        fontSize: 12.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(41, 41, 52, 0.6)),
                  ),
                  SizedBox(
                    width: 5.px,
                  ),
                  Text("${widget.item.plotCount}块",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w500,
                          color: const Color.fromRGBO(41, 41, 52, 1)))
                ],
              ),
            ],
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  BorderContainer(
                      color: const Color.fromRGBO(10, 174, 108, 1),
                      name: "已流转",
                      count: widget.item.transferPlotCount ?? 0),
                  SizedBox(
                    width: 5.px,
                  ),
                  BorderContainer(
                      color: const Color.fromRGBO(255, 147, 6, 1),
                      name: "未流转",
                      count: (widget.item.plotCount ?? 0) -
                          (widget.item.transferPlotCount ?? 0)),
                ],
              ),
              Text(
                  "${(widget.item.contractStartDate ?? "").split(" ").first}~${(widget.item.contractEndDate ?? "").split(" ").first}",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 0.6),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w400))
            ],
          )
        ],
      ),
    );
  }
}

class BorderContainer extends StatelessWidget {
  final String name;
  final num count;
  final Color color;
  const BorderContainer(
      {super.key,
      required this.color,
      required this.name,
      required this.count});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          border: Border.all(width: 0.5.px, color: color),
          borderRadius: BorderRadius.all(Radius.circular(2.px))),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.only(left: 2.px, right: 2.px),
            decoration: BoxDecoration(
                color: Color.fromRGBO(color.red, color.green, color.blue, 0.15),
                border: Border(right: BorderSide(width: 0.5.px, color: color))),
            child: Text(
              name,
              style: TextStyle(
                  color: color, fontSize: 11.px, fontWeight: FontWeight.w400),
            ),
          ),
          Container(
            padding: EdgeInsets.only(left: 2.px, right: 2.px),
            decoration: const BoxDecoration(color: Colors.white),
            child: Text(
              "$count块",
              style: TextStyle(
                  color: color, fontSize: 11.px, fontWeight: FontWeight.w400),
            ),
          ),
        ],
      ),
    );
  }
}

class ProcessContainer extends StatelessWidget {
  const ProcessContainer({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 260.px,
        height: 347.px,
        padding: EdgeInsets.all(25.px),
        decoration: BoxDecoration(
            image: DecorationImage(
                fit: BoxFit.fill,
                image:
                    AssetImage(ImageHelper.wrapAssets("role_background.png")))),
        child: Material(
          color: Colors.transparent,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "查看流程",
                style: TextStyle(fontSize: 17.px, fontWeight: FontWeight.w600),
              ),
              SizedBox(
                width: 266.px,
                height: 141.5.px,
                child: SingleChildScrollView(
                  child: BdhStepsVertical(steps: [
                    BdhStepsItemData(
                        title: "提交审核", content: "2024-10-08 06:54"),
                    BdhStepsItemData(
                        title: "连队管理人员审核", content: "2024-10-08 06:54"),
                    BdhStepsItemData(title: "团场审批", content: "2024-10-08 06:54")
                  ], activeIndex: 1),
                ),
              ),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: BDHButtonGreen(
                  width: 160.px,
                  height: 45.px,
                  title: "关闭",
                  borderRadius: 22.5.px,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
