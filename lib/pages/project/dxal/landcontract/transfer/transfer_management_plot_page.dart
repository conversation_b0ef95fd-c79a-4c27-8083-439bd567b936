import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_steps.dart';
import 'package:bdh_smart_agric_app/components/bdh_tag.dart';
import 'package:bdh_smart_agric_app/model/query_my_contract_result_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_check.dart';

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import 'transfer_management_contract_page.dart';
import 'transfer_management_object.dart';

class TransferManagementPlotPage extends StatefulWidget {
  final num contractId;
  final List<ContractDetail> details;
  const TransferManagementPlotPage(
      {super.key, required this.details, required this.contractId});

  @override
  State<StatefulWidget> createState() => _TransferManagementPlotPageState();
}

class _TransferManagementPlotPageState
    extends State<TransferManagementPlotPage> {
  List<ContractDetail> items = [];

  @override
  void initState() {
    super.initState();
    setState(() {
      items = widget.details;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 2),
      appBar: AppBar(
        title: const Text("经营权流转申请"),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Container(
              width: 375.px,
              height: 66.px,
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(15.px),
                      bottomRight: Radius.circular(15.px))),
              child: BdhStepsHorizontal(
                steps: [
                  BdhStepsItemData(title: "选择合同"),
                  BdhStepsItemData(title: "选择地块"),
                  BdhStepsItemData(title: "选择对象"),
                  BdhStepsItemData(title: "填写内容")
                ],
                activeIndex: 1,
              ),
            ),
            SizedBox(
              height: 10.px,
            ),
            LandSearchBar(
              placeholder: "请输入地块名称",
              onSearch: (item) {
                items = widget.details.where((e) {
                  return e.landNumberNo!.contains(item.keyword!);
                }).toList();
                setState(() {});
              },
            ),
            Container(
              width: 351.px,
              padding: EdgeInsets.only(top: 15.px),
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  45.px -
                  66.px -
                  25.px -
                  37.px,
              child: ListView.builder(
                  itemCount: items.length,
                  itemBuilder: (ctx, idx) {
                    return PlotItemView(item: items[idx]);
                  }),
            ),
            GestureDetector(
              onTap: () {
                List<ContractDetail> details = widget.details.where((e) {
                  return e.isSelect == true;
                }).toList();
                if (details.isEmpty) {
                  showToast("请至少选择一个地块");
                  return;
                }

                Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                  return TransferManagementObjectPage(
                    title: "经营权流转申请",
                    contractId: widget.contractId,
                    landNo: [
                      ...details.map((e) {
                        return e.landNumberNo ?? '';
                      })
                    ],
                  );
                }));
              },
              child: BDHButtonGreen(width: 347.px, height: 45.px, title: "下一步"),
            )
          ],
        );
      }),
    );
  }
}

class PlotItem {
  String? name;
  num? area;
  String? state;
  PlotItem({this.area, this.state, this.name});
}

class PlotItemView extends StatefulWidget {
  final ContractDetail item;
  const PlotItemView({super.key, required this.item});

  @override
  State<StatefulWidget> createState() => _PlotItemViewState();
}

class _PlotItemViewState extends State<PlotItemView> {
  bool isCheck = false;
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 351.px,
      height: 72.px,
      padding: EdgeInsets.all(13.px),
      margin: EdgeInsets.only(bottom: 10.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.item.landNumberNo ?? "",
                style: TextStyle(fontSize: 16.px, fontWeight: FontWeight.w600),
              ),
              BdhCheck(
                  isCheck: isCheck,
                  width: 15.px,
                  onClick: (check) {
                    widget.item.isSelect = check;
                    setState(() {
                      isCheck = check;
                    });
                  })
            ],
          ),
          Row(
            children: [
              BdhTag(
                  name: widget.item.transferStatus == 1 ? "已流转" : "未流转",
                  color: widget.item.transferStatus == 1
                      ? const Color.fromRGBO(10, 174, 108, 1)
                      : const Color.fromRGBO(255, 147, 6, 1))
            ],
          ),
        ],
      ),
    );
  }
}
