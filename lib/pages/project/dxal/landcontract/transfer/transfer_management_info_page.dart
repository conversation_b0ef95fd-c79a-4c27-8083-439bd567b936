import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_steps.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/transfer_application_result_model.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class TransferManageInfoPage extends StatefulWidget {
  final String title;
  final List<String>? steps;
  final int? stepIndex;
  final TransferApplicationItem? applyInfo;
  final num contractId;
  final List<String> landNos;
  final num? toFarmerId;
  final num? toPartnerId;

  const TransferManageInfoPage({
    super.key,
    required this.title,
    this.steps,
    this.stepIndex,
    this.applyInfo,
    required this.contractId,
    required this.landNos,
    this.toFarmerId,
    this.toPartnerId,
  });

  @override
  State<StatefulWidget> createState() => _TransferManageInfoPageState();
}

class _TransferManageInfoPageState extends State<TransferManageInfoPage> {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  Map<String, dynamic> form = {};
  List<DictNode> transTypeDics = [];
  List<DictNode> payTypeDics = [];

  @override
  void initState() {
    super.initState();

    Future.delayed(const Duration(microseconds: 100), () {
      loadOrg();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(239, 241, 245, 1),
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Visibility(
                visible: widget.stepIndex != null,
                child: Container(
                  width: 375.px,
                  height: 66.px,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                          bottomLeft: Radius.circular(15.px),
                          bottomRight: Radius.circular(15.px))),
                  child: BdhStepsHorizontal(
                    steps: widget.steps != null
                        ? widget.steps!
                            .map((e) => BdhStepsItemData(title: e))
                            .toList()
                        : [BdhStepsItemData(title: "默认")],
                    activeIndex: widget.stepIndex ?? 0,
                  ),
                )),
            Container(
              alignment: Alignment.center,
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  45.px -
                  (widget.stepIndex != null ? 86.px : 20.px),
              child: SingleChildScrollView(
                child: Form(
                    key: key,
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: 20.px),
                          padding: EdgeInsets.only(left: 10.px, right: 10.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            children: [
                              transTypeDics.isEmpty
                                  ? Container()
                                  : BdhSingleDataPicker(
                                      initialValue:
                                          widget.applyInfo?.transType != null
                                              ? DictNode(name: "否", code: "0")
                                              : null,
                                      item: FormItem(
                                          title: "流转方式",
                                          data: transTypeDics,
                                          isRequired: true),
                                      onSaved: (v) {
                                        form["transType"] =
                                            int.parse(v?.code ?? "0");
                                      },
                                    ),
                              BdhDatePicker(
                                item: FormItem(title: "流转开始", isRequired: true),
                                initialValue: widget
                                            .applyInfo?.transStartDate !=
                                        null
                                    ? DateFormat('yyyy-MM-dd hh:mm:ss').parse(
                                        widget.applyInfo!.transStartDate!)
                                    : null,
                                onSaved: (v) {
                                  form["transStartDate"] = v != null
                                      ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                          .format(v)
                                      : null;
                                },
                              ),
                              BdhDatePicker(
                                item: FormItem(title: "流转结束", isRequired: true),
                                initialValue: widget.applyInfo?.transEndDate !=
                                        null
                                    ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                        .parse(widget.applyInfo!.transEndDate!)
                                    : null,
                                onSaved: (v) {
                                  form["transEndDate"] = v != null
                                      ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                          .format(v)
                                      : null;
                                },
                              ),
                              BdhDatePicker(
                                item: FormItem(title: "交付时间", isRequired: true),
                                initialValue: widget.applyInfo?.deliveryDate !=
                                        null
                                    ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                        .parse(widget.applyInfo!.deliveryDate!)
                                    : null,
                                onSaved: (v) {
                                  form["deliveryDate"] = v != null
                                      ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                          .format(v)
                                      : null;
                                },
                              ),
                              BdhTextInput(
                                initialValue: widget.applyInfo?.deliveryMethod,
                                item: FormItem(title: "交付方式", isRequired: true),
                                onSaved: (v) {
                                  form["deliveryMethod"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "交付方式不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                initialValue:
                                    "${widget.applyInfo?.totalFee ?? ""}",
                                item: FormItem(
                                  title: "流转价格",
                                  isRequired: true,
                                ),
                                unit: "元",
                                onSaved: (v) {
                                  form["totalFee"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "流转价格不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhDatePicker(
                                item: FormItem(title: "支付时间", isRequired: true),
                                initialValue: widget.applyInfo?.payDate != null
                                    ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                        .parse(widget.applyInfo!.payDate!)
                                    : null,
                                onSaved: (v) {
                                  form["payDate"] = v != null
                                      ? DateFormat('yyyy-MM-dd hh:mm:ss')
                                          .format(v)
                                      : null;
                                },
                              ),
                              payTypeDics.isNotEmpty
                                  ? BdhSingleDataPicker(
                                      initialValue: widget
                                                  .applyInfo?.payMethod !=
                                              null
                                          ? DictNode(
                                              name: "否",
                                              code:
                                                  "${widget.applyInfo?.payMethod}")
                                          : null,
                                      item: FormItem(
                                          title: "支付方式",
                                          data: payTypeDics,
                                          isRequired: true),
                                      onSaved: (v) {
                                        form["payMethod"] =
                                            int.parse(v?.code ?? "0");
                                      },
                                    )
                                  : Container(),
                              BdhTextInput(
                                item: FormItem(title: "经营项目", isRequired: true),
                                initialValue: widget.applyInfo?.projectName,
                                onSaved: (v) {
                                  form["projectName"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "经营项目不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item: FormItem(
                                    title: "农业政策性补贴", isRequired: true),
                                initialValue: widget.applyInfo?.agpSubsidyAgree,
                                onSaved: (v) {
                                  form["agpSubsidyAgree"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "农业政策性补贴不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item: FormItem(title: "补偿", isRequired: true),
                                initialValue: widget.applyInfo?.compenAgree,
                                onSaved: (v) {
                                  form["compenAgree"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "补偿不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item: FormItem(title: "违约金额", isRequired: true),
                                initialValue:
                                    "${widget.applyInfo?.breachAmount ?? ""}",
                                unit: "元",
                                onSaved: (v) {
                                  form["breachAmount"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "违约金额不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item:
                                    FormItem(title: "滞纳金比例", isRequired: true),
                                initialValue:
                                    "${widget.applyInfo?.lateFeePer ?? ""}",
                                onSaved: (v) {
                                  form["lateFeePer"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "滞纳金比例不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                item:
                                    FormItem(title: "双方约定事项", isRequired: true),
                                initialValue:
                                    widget.applyInfo?.otherMatters ?? "",
                                onSaved: (v) {
                                  form["otherMatters"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "双方约定事项不能为空";
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    )),
              ),
            ),
            GestureDetector(
              onTap: () {
                if (key.currentState!.validate()) {
                  key.currentState!.save();

                  TDToast.showLoadingWithoutText(
                      context: context, preventTap: true);
                  form["fromContractId"] = widget.contractId;
                  form["landNumberNos"] = widget.landNos;
                  form["toFarmerId"] = widget.toFarmerId;
                  form["toPartnerId"] = widget.toPartnerId;
                  // print(jsonEncode(form));
                  LandResponsitory.saveTransfer(form).then((res) {
                    TDToast.dismissLoading();
                    if (res.code == 0) {
                      showToast("保存成功");
                      Navigator.of(context).popUntil((pre) {
                        return pre.settings.name ==
                            RouteName.daHingLandContract;
                      });
                    }
                  });
                }
              },
              child: BDHButtonGreen(width: 347.px, height: 45.px, title: "保存"),
            )
          ],
        );
      }),
    );
  }

  loadOrg() {
    Future.wait([
      LandResponsitory.getDicByKey("trans_type"),
      LandResponsitory.getDicByKey("ol_payment_method"),
    ]).then((list) {
      transTypeDics = list[0].data ?? [];
      payTypeDics = list[1].data ?? [];
      setState(() {});
    });
  }
}
