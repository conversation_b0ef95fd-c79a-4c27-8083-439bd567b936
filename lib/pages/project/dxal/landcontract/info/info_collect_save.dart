import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_org_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_vertify_image_picker.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/utils/dic_util.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class InfoCollectSavePage extends StatefulWidget {
  final LandBaseInfo? baseInfo;
  final String title;
  final List<String>? steps;
  final int? stepIndex;

  const InfoCollectSavePage(
      {super.key,
      required this.title,
      this.steps,
      this.stepIndex,
      this.baseInfo});

  @override
  State<StatefulWidget> createState() => _InfoCollectSavePageState();
}

class _InfoCollectSavePageState extends State<InfoCollectSavePage> {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  Map<String, dynamic> form = {};
  OrgTreeResult? treeResult;
  TextEditingController textEditingController1 = TextEditingController();
  TextEditingController textEditingController2 = TextEditingController();
  TextEditingController textEditingController3 = TextEditingController();
  TextEditingController textEditingController4 = TextEditingController();
  TextEditingController textEditingController5 = TextEditingController();
  List<DictNode> nations = [];
  List<DictNode> sexs = [];

  String btnText = "";

  @override
  void initState() {
    super.initState();

    Future.delayed(const Duration(microseconds: 100), () {
      loadOrg();
    });

    textEditingController1.text = widget.baseInfo?.name ?? "";
    textEditingController2.text = widget.baseInfo?.idNumber ?? "";
    textEditingController3.text = widget.baseInfo?.domicilePlace ?? "";
    textEditingController4.text = widget.baseInfo?.issuingAuthority ?? "";
    textEditingController5.text = widget.baseInfo?.validDate ?? "";

    switch (widget.baseInfo?.buttonType) {
      case 0:
        btnText = "提交";
        break;
      case 1:
        btnText = "修改并提交";
        break;
      case 2:
        btnText = "农场审核中";
        break;
      case 3:
        btnText = "信息变更审核中";
        break;
      default:
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(239, 241, 245, 1),
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Container(
              alignment: Alignment.center,
              height: cons.maxHeight -
                  MediaQuery.of(context).padding.bottom -
                  65.px,
              child: SingleChildScrollView(
                child: Form(
                    key: key,
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: 20.px),
                          padding: EdgeInsets.only(left: 10.px, right: 10.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            children: [
                              BdhTextInput(
                                controller: textEditingController1,
                                initialValue: widget.baseInfo?.name,
                                item: FormItem(title: "姓名", isRequired: true),
                                onSaved: (v) {
                                  form["name"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "姓名不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                controller: textEditingController2,
                                initialValue: widget.baseInfo?.idNumber,
                                item: FormItem(title: "身份证", isRequired: true),
                                onSaved: (v) {
                                  form["idNumber"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "身份证不能为空";
                                  }
                                  return null;
                                },
                              ),
                              treeResult != null
                                  ? BdhOrgDataPicker(
                                      item: FormItem(
                                          title: "资格所在单位",
                                          data: treeResult?.data,
                                          isRequired: true),
                                      initialValue:
                                          widget.baseInfo?.organizationNo !=
                                                  null
                                              ? [
                                                  {
                                                    "orgCode": widget.baseInfo
                                                        ?.organizationNo,
                                                    "orgName": widget.baseInfo
                                                        ?.organizationName
                                                  }
                                                ]
                                              : null,
                                      validator: (v) {
                                        if (v == null) {
                                          return "资格所在单位不能为空";
                                        }
                                        if (((v as List).last["orgCode"]
                                                    as String)
                                                .length !=
                                            10) {
                                          return "请选到连队级别";
                                        }
                                        return null;
                                      },
                                      onSaved: (v) {
                                        form["organizationNo"] =
                                            (v as List).last["orgCode"];
                                        form["organizationName"] =
                                            (v).last["orgName"];
                                      },
                                    )
                                  : Container(),
                              BdhTextInput(
                                item:
                                    FormItem(title: "社会保险号码", isRequired: true),
                                initialValue: widget.baseInfo?.socialSn ??
                                    widget.baseInfo?.idNumber,
                                onSaved: (v) {
                                  form["socialSn"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "社会保险号码不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                controller: textEditingController3,
                                initialValue: widget.baseInfo?.domicilePlace,
                                item:
                                    FormItem(title: "户籍所在地", isRequired: true),
                                onSaved: (v) {
                                  form["domicilePlace"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "户籍所在地不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                controller: textEditingController4,
                                initialValue: widget.baseInfo?.issuingAuthority,
                                item: FormItem(title: "签发机关", isRequired: true),
                                onSaved: (v) {
                                  form["issuingAuthority"] = v;
                                },
                                validator: (v) {
                                  if (v == null) {
                                    return "签发机关不能为空";
                                  }
                                  return null;
                                },
                              ),
                              BdhTextInput(
                                controller: textEditingController5,
                                item: FormItem(title: "有效期限", isRequired: true),
                                initialValue: widget.baseInfo?.validDate,
                                onSaved: (v) {
                                  form["validDate"] = v;
                                },
                                validator: (v) {
                                  if (v == null || v == "") {
                                    return "有效期限不能为空";
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 20.px),
                          padding: EdgeInsets.all(15.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            children: [
                              const Row(
                                children: [
                                  Text(
                                    "*",
                                    style: TextStyle(color: Colors.red),
                                  ),
                                  Text("上传本人照片及身份证正反面")
                                ],
                              ),
                              SizedBox(
                                height: 15.px,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  BdhVertifyImagePicker(
                                    item: FormItem(title: "本人照片"),
                                    initialValue:
                                        widget.baseInfo?.photoPath != null
                                            ? BDHFile(
                                                url: widget.baseInfo?.photoPath)
                                            : null,
                                    onSaved: (v) {
                                      form["photoPath"] = v?.url;
                                    },
                                    validator: (v) {
                                      if (v == null) {
                                        return "本人照片不能为空";
                                      }
                                      return null;
                                    },
                                  ),
                                  BdhVertifyImagePicker(
                                    onUpload: (v) {
                                      //法大大校验
                                      UserInfo? userInfo =
                                          StorageUtil.userInfo();
                                      LandResponsitory.ocrIdCard({
                                        "picUrl": v.url,
                                        "accoundId": userInfo?.data?.id,
                                        "systemId": "systemlandcontract"
                                      }).then((res) {
                                        if (res.code == 0) {
                                          if (res.data["姓名"] != null &&
                                              res.data["公民身份号码"] != null) {
                                            textEditingController1.text =
                                                res.data["姓名"]["words"];
                                            textEditingController2.text =
                                                res.data["公民身份号码"]["words"];
                                            textEditingController3.text =
                                                res.data["住址"]["words"];

                                            form["address"] =
                                                res.data["住址"]["words"];
                                            form["sex"] =
                                                DicUtil.dictNameToCode(
                                                    res.data["性别"]["words"],
                                                    sexs);
                                            form["nation"] =
                                                DicUtil.dictNameToCode(
                                                    res.data["民族"]["words"],
                                                    nations);
                                          } else {
                                            showToast("身份证正面识别失败");
                                          }
                                        } else {
                                          // showToast("身份证识别失败");
                                        }
                                      });
                                    },
                                    item: FormItem(title: "身份证正面"),
                                    initialValue: widget.baseInfo?.idFront !=
                                            null
                                        ? BDHFile(url: widget.baseInfo?.idFront)
                                        : null,
                                    onSaved: (v) {
                                      form["idFront"] = v?.url;
                                    },
                                    validator: (v) {
                                      if (v == null) {
                                        return "身份证正面不能为空";
                                      }
                                      return null;
                                    },
                                  ),
                                  BdhVertifyImagePicker(
                                    onUpload: (v) {
                                      //法大大校验
                                      UserInfo? userInfo =
                                          StorageUtil.userInfo();
                                      LandResponsitory.ocrIdCard({
                                        "picUrl": v.url,
                                        "accoundId": userInfo?.data?.id,
                                        "systemId": "systemlandcontract"
                                      }).then((res) {
                                        if (res.code == 0) {
                                          if (res.data["签发机关"] != null &&
                                              res.data["签发日期"] != null) {
                                            textEditingController4.text =
                                                res.data["签发机关"]["words"];
                                            textEditingController5.text =
                                                "${res.data["签发日期"]["words"]}-${res.data["失效日期"]["words"]}";
                                          } else {
                                            showToast("身份证反面识别失败");
                                          }
                                        } else {
                                          // showToast("身份证反面识别失败");
                                        }
                                      });
                                    },
                                    item: FormItem(title: "身份证反面"),
                                    initialValue: widget.baseInfo?.idBack !=
                                            null
                                        ? BDHFile(url: widget.baseInfo?.idBack)
                                        : null,
                                    onSaved: (v) {
                                      form["idBack"] = v?.url;
                                    },
                                    validator: (v) {
                                      if (v == null) {
                                        return "身份证反面不能为空";
                                      }
                                      return null;
                                    },
                                  )
                                ],
                              )
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 20.px, bottom: 20.px),
                          padding: EdgeInsets.all(15.px),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.px))),
                          width: 347.px,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Text("上传身份证复印件"),
                              SizedBox(
                                height: 15.px,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  BdhVertifyImagePicker(
                                    item: FormItem(title: "身份证复印件"),
                                    initialValue: widget
                                                .baseInfo?.idPictureUrl !=
                                            null
                                        ? BDHFile(
                                            url: widget.baseInfo?.idPictureUrl)
                                        : null,
                                    onSaved: (v) {
                                      form["idPictureUrl"] = v?.url;
                                    },
                                  ),
                                ],
                              )
                            ],
                          ),
                        )
                      ],
                    )),
              ),
            ),
            GestureDetector(
              onTap: () {
                if (widget.baseInfo?.isSave == false) {
                  showToast("当前记录正在审核中，请等待农场审核或退回后再进行修改");
                  return;
                }
                if (key.currentState!.validate()) {
                  key.currentState!.save();
                  form["farmerId"] = widget.baseInfo?.farmerId;
                  form["isEmployee"] = widget.baseInfo?.isEmployee;
                  TDToast.showLoadingWithoutText(
                      context: context, preventTap: true);
                  LandResponsitory.updateResidentInfo(form).then((res) {
                    TDToast.dismissLoading();
                    if (res.code == 0) {
                      showToast("保存成功");
                      bus.emit("refreshLandInfo");
                      Navigator.of(context).popUntil((pre) {
                        return pre.settings.name ==
                            RouteName.daHingLandContract;
                      });
                    }
                  });
                }
              },
              child: BDHButtonGreen(
                width: 347.px,
                height: 45.px,
                title: btnText,
                color: widget.baseInfo?.isSave == false
                    ? Colors.grey
                    : const Color.fromRGBO(22, 183, 96, 1),
              ),
            )
          ],
        );
      }),
    );
  }

  loadOrg() {
    TDToast.showLoadingWithoutText(context: context, preventTap: true);
    Future.wait([
      LandResponsitory.getOrgData(),
      LandResponsitory.getDicByKey("nation"),
      LandResponsitory.getDicByKey("sex")
    ]).then((list) {
      TDToast.dismissLoading();
      setState(() {
        treeResult = (list[0] as OrgTreeResult);
        nations = (list[1] as DictList).data ?? [];
        sexs = (list[2] as DictList).data ?? [];
      });
    });
  }
}
