import 'dart:async';
import 'dart:convert';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_steps.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_ocr_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_sms.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/pages/product/realname/real_name_artificial_auth_page.dart';
import 'package:bdh_smart_agric_app/utils/dic_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import '../info/info_collect.dart';

class RealNameAuthPage extends StatefulWidget {
  final String title;
  final List<String>? steps;
  final int? stepIndex;
  final dynamic bridgeData;

  const RealNameAuthPage({
    super.key,
    this.steps,
    this.stepIndex,
    required this.title,
    this.bridgeData,
  });

  @override
  State<StatefulWidget> createState() => _RealNameAuthPageState();
}

class _RealNameAuthPageState extends State<RealNameAuthPage> {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  Map<String, dynamic> form = {};
  TextEditingController textEditingController1 = TextEditingController();
  TextEditingController textEditingController2 = TextEditingController();
  TextEditingController phoneController = TextEditingController();
  Timer? timer;
  int count = 0;
  bool isRealName = false;
  bool isPolling = false;
  List<DictNode> sexDics = [];
  List<DictNode> nations = [];

  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(microseconds: 100), () {
      loadDics();
    });
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(239, 241, 245, 1),
      appBar: AppBar(
        title: Text(widget.title),
      ),
      body: isPolling
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text("实名信息回写中..."),
                  SizedBox(
                    height: 15.px,
                  ),
                  GestureDetector(
                    onTap: () {
                      timer?.cancel();
                      Navigator.of(context).pop();
                    },
                    child: BDHButtonGreen(
                        width: 150.px, height: 44.px, title: "返回"),
                  )
                ],
              ),
            )
          : (isRealName
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text("实名认证成功!"),
                      GestureDetector(
                        onTap: () {
                          timer?.cancel();
                          Navigator.of(context).pop();
                        },
                        child: BDHButtonGreen(
                            width: 150.px, height: 44.px, title: "返回"),
                      )
                    ],
                  ),
                )
              : LayoutBuilder(builder: (ctx, cons) {
                  return Column(
                    children: [
                      Container(
                        width: 375.px,
                        height: 66.px,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(15.px),
                                bottomRight: Radius.circular(15.px))),
                        child: BdhStepsHorizontal(
                          steps: widget.steps != null
                              ? widget.steps!
                                  .map((e) => BdhStepsItemData(title: e))
                                  .toList()
                              : [BdhStepsItemData(title: "默认")],
                          activeIndex: widget.stepIndex ?? 0,
                        ),
                      ),
                      SizedBox(
                        height: cons.maxHeight -
                            MediaQuery.of(context).padding.bottom -
                            45.px -
                            66.px -
                            15.px,
                        child: SingleChildScrollView(
                          child: Form(
                              autovalidateMode: AutovalidateMode.always,
                              key: key,
                              child: Column(
                                children: [
                                  Container(
                                      margin: EdgeInsets.only(top: 20.px),
                                      padding: EdgeInsets.all(16.px),
                                      decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(8.px))),
                                      width: 347.px,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "请拍摄并上传你的身份证照片，请勿翻拍",
                                            style: TextStyle(
                                                fontSize: 14.px,
                                                fontWeight: FontWeight.w500),
                                          ),
                                          SizedBox(
                                            height: 10.px,
                                          ),
                                          BdhOcrImagePicker(
                                            item: FormItem(),
                                            icon: "upload_id.png",
                                            onUpload: (v) {
                                              //法大大校验
                                              UserInfo? userInfo =
                                                  StorageUtil.userInfo();
                                              // print(jsonEncode({
                                              //   "picUrl": v.url,
                                              //   "accoundId": userInfo?.data?.id,
                                              //   "systemId": "systemlandcontract"
                                              // }));
                                              LandResponsitory.ocrIdCard({
                                                "picUrl": v.url,
                                                "accoundId": userInfo?.data?.id,
                                                "systemId": "systemlandcontract"
                                              }).then((res) {
                                                if (res.code == 0) {
                                                  if (res.data["姓名"] != null &&
                                                      res.data["公民身份号码"] !=
                                                          null) {
                                                    textEditingController1
                                                            .text =
                                                        res.data["姓名"]["words"];
                                                    textEditingController2
                                                            .text =
                                                        res.data["公民身份号码"]
                                                            ["words"];
                                                    form["address"] =
                                                        res.data["住址"]["words"];
                                                    form["domicilePlace"] =
                                                        res.data["住址"]["words"];
                                                    form["sex"] =
                                                        DicUtil.dictNameToCode(
                                                            res.data["性别"]
                                                                ["words"],
                                                            sexDics);
                                                    form["nation"] =
                                                        DicUtil.dictNameToCode(
                                                            res.data["民族"]
                                                                ["words"],
                                                            nations);
                                                  } else {
                                                    showToast("身份证正面识别失败");
                                                  }
                                                } else {
                                                  // showToast("身份证识别失败");
                                                }
                                              });
                                            },
                                            onSaved: (v) {
                                              form["faceImagePath"] = v?.url;
                                            },
                                            validator: (v) {
                                              if (v == null) {
                                                return "身份证不能为空!";
                                              }
                                              return null;
                                            },
                                          )
                                        ],
                                      )),
                                  Container(
                                    margin: EdgeInsets.only(top: 20.px),
                                    padding: EdgeInsets.only(
                                        left: 10.px, right: 10.px),
                                    decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(8.px))),
                                    width: 347.px,
                                    child: Column(
                                      children: [
                                        BdhTextInput(
                                          controller: textEditingController1,
                                          item: FormItem(
                                              title: "姓名", isRequired: true),
                                          onSaved: (v) {
                                            form["name"] = v;
                                          },
                                          validator: (v) {
                                            if (v == null || v.isEmpty) {
                                              return "姓名不能为空";
                                            }
                                            return null;
                                          },
                                        ),
                                        BdhTextInput(
                                          controller: textEditingController2,
                                          item: FormItem(
                                              title: "身份证", isRequired: true),
                                          onSaved: (v) {
                                            form["identNo"] = v;
                                          },
                                          validator: (v) {
                                            if (v == null || v.isEmpty) {
                                              return "身份证号码不能为空";
                                            }
                                            return null;
                                          },
                                        ),
                                        BdhSms(
                                          initialValue: SmsItem(
                                              StorageUtil.userInfo()
                                                      ?.data
                                                      ?.telephone ??
                                                  "",
                                              ""),
                                          item: FormItem(
                                              title: "手机号码", isRequired: true),
                                          onSaved: (v) {
                                            form["phoneNo"] = v?.phone;
                                            form["verifyCode"] = v?.code;
                                          },
                                          validator: (v) {
                                            if (v?.phone == null ||
                                                v?.phone == "" ||
                                                v?.code == "" ||
                                                v?.code == null) {
                                              return "手机号码和验证码不能为空";
                                            }
                                            return null;
                                          },
                                        )
                                      ],
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                        top: 15.px, bottom: 15.px),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "拍摄要求须知",
                                          style: TextStyle(
                                              fontSize: 12.px,
                                              fontWeight: FontWeight.w600,
                                              color: const Color.fromRGBO(
                                                  44, 44, 52, 1)),
                                        ),
                                        SizedBox(
                                          height: 10.px,
                                        ),
                                        Image.asset(
                                            width: 349.5.px,
                                            height: 58.px,
                                            ImageHelper.wrapAssets(
                                                "photo_standard.png"))
                                      ],
                                    ),
                                  )
                                ],
                              )),
                        ),
                      ),
                      GestureDetector(
                        onTap: () async {
                          // NativeUtil.openFdd("http://www.baidu.com");
                          // poll();
                          // return;

                          if (key.currentState!.validate()) {
                            key.currentState!.save();
                            form["accountId"] =
                                StorageUtil.userInfo()?.data?.id;
                            form["systemId"] = "systemlandcontract";
                            form["certLcFlag"] = 1;

                            if (kDebugMode) {
                              print(jsonEncode(form));
                            }
                            //1.先校验身份证是否重合
                            TDToast.showLoadingWithoutText(
                                context: context, preventTap: true);
                            var existFarmerResult =
                                await LandResponsitory.existsFarmer(
                                    {"idNumber": form["identNo"]});
                            if (existFarmerResult.data == false) {
                              print(jsonEncode(form));
                              var verifiyResult =
                                  await LandResponsitory.authDataVerification(
                                      form);
                              TDToast.dismissLoading();
                              if (verifiyResult.code == 0) {
                                if (verifiyResult.msg == 'limited') {
                                  //转人工
                                  BrnDialogManager.showConfirmDialog(context,
                                      title: "提示",
                                      cancel: '取消',
                                      confirm: '确定',
                                      message: "您的扫脸认证尝试次数已达上限，是否立即进入人工审核",
                                      onConfirm: () {
                                    Navigator.of(context).pop();
                                    Navigator.of(context).push(
                                        CupertinoPageRoute(builder: (ctx) {
                                      return const RealNameArtificialAuthPage();
                                    }));
                                  }, onCancel: () {
                                    Navigator.of(context).popUntil((e) {
                                      return e.settings.name ==
                                          RouteName.tabMain;
                                    });
                                  });
                                  return;
                                }
                                if (mounted) {
                                  NativeUtil.openFdd(verifiyResult.data);
                                  //开始轮询实名状态
                                  poll();
                                }
                              }
                            } else {
                              TDToast.dismissLoading();
                              showToast("身份证号码已存在");
                            }
                          }
                        },
                        child: BDHButtonGreen(
                            width: 347.px, height: 45.px, title: "下一步"),
                      )
                    ],
                  );
                })),
    );
  }

  poll() {
    setState(() {
      isPolling = true;
    });
    var accountId = StorageUtil.userInfo()?.data?.id;
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      count = count + 1;
      if (count > 200) {
        timer.cancel();
      }
      LandResponsitory.getResidentInfoStatus(accountId!).then((res) {
        if (res.data == "2" && isRealName == false) {
          isRealName = true;
          setState(() {
            isPolling = false;
          });
          timer.cancel();
          NativeUtil.closeFdd();

          //查询实名
          LandResponsitory.getResidentInfo().then((info) {
            Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
              return InfoCollectPage(
                title: widget.title,
                steps: widget.steps,
                stepIndex: (widget.stepIndex ?? 0) + 1,
                baseInfo: info.data,
              );
            }));
          });
        }
      });
    });
  }

  loadDics() {
    TDToast.showLoadingWithoutText(context: context, preventTap: true);
    Future.wait([
      LandResponsitory.getDicByKey("sex"),
      LandResponsitory.getDicByKey("nation")
    ]).then((list) {
      sexDics = list[0].data ?? [];
      nations = list[1].data ?? [];
      TDToast.dismissLoading();
    });
  }
}
