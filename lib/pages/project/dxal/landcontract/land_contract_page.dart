import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/model/enterprise_info_result_model.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/pages/project/dxal/landcontract/info/info_collect_save.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';
import 'contract/land_contract_sign_page.dart';
import 'info/info_collect.dart';
import 'realname/real_name_auth_page.dart';

class DaHingLandContractPage extends StatefulWidget {
  const DaHingLandContractPage({super.key});

  @override
  State<StatefulWidget> createState() => _DaHingLandContractPageState();
}

class _DaHingLandContractPageState extends State {
  List<MenuItem> items = [
    MenuItem("信息采集", "menu_collect.png", "path"),
    MenuItem("合同签订", "menu_contract.png", "path"),
  ];
  LandBaseInfo? landBaseInfo;
  Partner? partner;

  @override
  void initState() {
    super.initState();
    bus.on("refreshLandInfo", (v) {
      loadInfo();
    });
    Future.delayed(const Duration(milliseconds: 100), () {
      getRealNameStatus();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF4F5F9),
      body: LayoutBuilder(builder: (ctx, cons) {
        return SizedBox(
            width: cons.maxWidth,
            height: cons.maxHeight,
            child: Stack(
              children: [
                Stack(
                  children: [
                    Image.asset(
                        width: 375.px,
                        ImageHelper.wrapAssets("land_contract_background.png")),
                    Positioned(
                      bottom: 100.px,
                      right: 0,
                      child: Image.asset(
                        ImageHelper.wrapAssets("land_contract_headIcon.png"),
                        width: 131.px,
                      ),
                    ),
                  ],
                ),
                landBaseInfo != null
                    ? Positioned(
                        left: 12.px,
                        top: 217.px,
                        child: Container(
                          width: 351.px,
                          height: 103.px,
                          decoration: BoxDecoration(
                              image: DecorationImage(
                                  fit: BoxFit.cover,
                                  image: AssetImage(ImageHelper.wrapAssets(
                                      "land_contract_menu_background.png")))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              ...items.map((e) {
                                return MenuItemView(
                                  item: e,
                                  onClick: (item) {
                                    if (item.name == "信息采集") {
                                      Navigator.of(context).push(
                                          CupertinoPageRoute(builder: (ctx) {
                                        return InfoCollectSavePage(
                                          baseInfo: landBaseInfo,
                                          title: "信息采集",
                                        );
                                      }));
                                    }

                                    if (item.name == "合同签订") {
                                      if (landBaseInfo == null) {
                                        showRoleDialog();
                                      } else {
                                        Navigator.of(context).push(
                                            CupertinoPageRoute(builder: (ctx) {
                                          return const LandContractSignPage();
                                        }));
                                      }
                                    }
                                  },
                                );
                              })
                            ],
                          ),
                        ))
                    : Container(),
                // const BottomAll()
              ],
            ));
      }),
    );
  }

  getRealNameStatus() async {
    TDToast.showLoadingWithoutText(context: context, preventTap: true);
    var accountId = StorageUtil.userInfo()?.data?.id;
    var result = await LandResponsitory.getRealNameStatus(accountId);
    if (result.data == null) {
      TDToast.dismissLoading();

      //还没有实名认证
      showRoleDialog();
    } else {
      loadInfo();
    }
  }

  showRoleDialog() {
    if (mounted) {
      showDialog(
          context: context,
          barrierDismissible: false,
          useRootNavigator: false,
          builder: (ctx) {
            return RoleSelectContainer(
              onSelect: (roleItem) {
                StorageManager.sharedPreferences
                    ?.setString("landRoleName", roleItem.roleName);
                if (roleItem.roleName == "职工" || roleItem.roleName == "非职工") {
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return RealNameAuthPage(
                      title: "基本信息-${roleItem.roleName}",
                      stepIndex: 0,
                      steps: const ["实名认证", "信息补充"],
                    );
                  }));
                }
              },
            );
          });
    }
  }

  showRoleDialogAuthed() {
    if (mounted) {
      showDialog(
          context: context,
          barrierDismissible: false,
          builder: (ctx) {
            return RoleSelectContainer(
              onSelect: (roleItem) {
                StorageManager.sharedPreferences
                    ?.setString("landRoleName", roleItem.roleName);
                if (roleItem.roleName == "职工") {
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return InfoCollectPage(
                      baseInfo: landBaseInfo,
                      title: "基本信息-职工",
                    );
                  }));
                } else if (roleItem.roleName == "非职工") {
                  Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                    return InfoCollectPage(
                      baseInfo: landBaseInfo,
                      title: "基本信息-非职工",
                    );
                  }));
                }
              },
            );
          });
    }
  }

  //查询基本信息和企业信息
  loadInfo() {
    Future.wait([
      LandResponsitory.getResidentInfo(),
    ]).then((resList) {
      TDToast.dismissLoading();
      landBaseInfo = (resList[0]).data;
      setState(() {});
      if (landBaseInfo?.organizationNo != null) {
        return;
      } else {
        showRoleDialogAuthed();
      }
    });
  }

  @override
  dispose() {
    super.dispose();
    bus.off("refreshLandInfo");
  }
}

class MenuItem {
  String name;
  String icon;
  String path;
  MenuItem(this.name, this.icon, this.path);
}

class MenuItemView extends StatelessWidget {
  final MenuItem item;
  final Function(MenuItem) onClick;
  const MenuItemView({super.key, required this.item, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClick(item);
      },
      child: Container(
        height: 63.px,
        width: 63.px,
        margin: EdgeInsets.all(10.px),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Image.asset(
                width: 40.px, height: 40.px, ImageHelper.wrapAssets(item.icon)),
            Text(
              item.name,
              style: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w500,
                  color: const Color.fromRGBO(51, 51, 51, 1)),
            )
          ],
        ),
      ),
    );
  }
}

class RoleItem {
  String roleName;
  String desc;
  bool isSelect;
  RoleItem(this.roleName, this.desc, this.isSelect);
}

class RoleSelectContainer extends StatefulWidget {
  final Function(RoleItem) onSelect;
  const RoleSelectContainer({super.key, required this.onSelect});

  @override
  State<StatefulWidget> createState() => _RoleSelectContainerState();
}

class _RoleSelectContainerState extends State<RoleSelectContainer> {
  List<RoleItem> roleItems = [
    RoleItem("职工", "职工身份请选择此入口", true),
    RoleItem("非职工", "非职工身份请选择此入口", false),
    // RoleItem("企业", "企业代办人员选择此入口", false)
  ];
  RoleItem curItem = RoleItem("职工", "职工身份请选择此入口", true);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        width: 319.px,
        height: 323.5.px,
        padding: EdgeInsets.all(27.px),
        decoration: BoxDecoration(
            image: DecorationImage(
                image:
                    AssetImage(ImageHelper.wrapAssets("role_background.png")))),
        child: Material(
          color: Colors.transparent,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                margin: EdgeInsets.only(bottom: 10.px),
                width: 265.px,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "身份选择入口",
                      style: TextStyle(
                        fontSize: 23.px,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    Text(
                      "请根据您的身份选择入口",
                      style: TextStyle(
                        fontSize: 12.px,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                width: 266.px,
                height: 118.5.px,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    ...roleItems.map((e) {
                      return GestureDetector(
                        onTap: () {
                          for (var v in roleItems) {
                            if (v.roleName == e.roleName) {
                              v.isSelect = true;
                            } else {
                              v.isSelect = false;
                            }
                          }
                          curItem = e;
                          setState(() {});
                        },
                        child: e.isSelect == true
                            ? Container(
                                width: 82.px,
                                height: 118.5.px,
                                margin: EdgeInsets.only(right: 10.px),
                                padding: EdgeInsets.only(
                                    left: 11.px,
                                    right: 11.px,
                                    top: 22.px,
                                    bottom: 22.px),
                                decoration: BoxDecoration(
                                    image: DecorationImage(
                                        fit: BoxFit.cover,
                                        image: AssetImage(
                                            ImageHelper.wrapAssets(
                                                "role_select.png")))),
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: [
                                    Text(
                                      e.roleName,
                                      style: TextStyle(
                                          fontSize: 14.px,
                                          fontWeight: FontWeight.w600),
                                    ),
                                    Text(e.desc,
                                        style: TextStyle(
                                            fontSize: 12.px,
                                            fontWeight: FontWeight.w400))
                                  ],
                                ),
                              )
                            : Container(
                                margin: EdgeInsets.only(right: 10.px),
                                decoration: BoxDecoration(
                                    color: const Color.fromRGBO(0, 0, 0, 0.03),
                                    borderRadius: BorderRadius.all(
                                        Radius.circular(8.px))),
                                width: 82.px,
                                height: 118.5.px,
                                padding: EdgeInsets.only(
                                    left: 11.px,
                                    right: 11.px,
                                    top: 22.px,
                                    bottom: 22.px),
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: [
                                    Text(
                                      e.roleName,
                                      style: TextStyle(
                                          fontSize: 14.px,
                                          fontWeight: FontWeight.w600),
                                    ),
                                    Text(e.desc,
                                        style: TextStyle(
                                            fontSize: 12.px,
                                            fontWeight: FontWeight.w400))
                                  ],
                                ),
                              ),
                      );
                    })
                  ],
                ),
              ),
              SizedBox(
                height: 25.px,
              ),
              GestureDetector(
                onTap: () {
                  Navigator.of(context).pop();
                  widget.onSelect(curItem);
                },
                child: BDHButtonGreen(
                  width: 225.px,
                  height: 45.px,
                  title: "进入",
                  borderRadius: 22.5.px,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

//下面的信息
class BottomAll extends StatefulWidget {
  const BottomAll({super.key});

  @override
  State<BottomAll> createState() => _BottomAllState();
}

class _BottomAllState extends State<BottomAll> {
  bool isState = true;
  List<BottomItem> listems = [
    BottomItem("menu_collect.png", "信息采集", true, "退回原因"),
    BottomItem("menu_collect.png", "信息采集11111111", false, "退回原因11111111"),
  ];
  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 15.px,
      top: 335.px,
      child: SizedBox(
        width: 345.px,
        height: MediaQuery.of(context).size.height - 340.px,
        child: !isState
            ? SvgPicture.asset(
                ImageHelper.wrapAssets("icon_nodata.svg"),
                width: 172, // 调整为 double 类型
                height: 70, // 调整为 double 类型
              )
            : ListView.builder(
                shrinkWrap: true,
                itemCount: listems.length,
                // physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (BuildContext context, int index) {
                  return GestureDetector(
                      onTap: () {
                        // print(111);
                      },
                      child: BottomInfo(bottmData: listems[index]));
                }),
      ),
    );
  }
}

class BottomItem {
  String icon;
  String title;
  bool isReturn;
  String text;
  BottomItem(this.icon, this.title, this.isReturn, this.text);
}

class BottomInfo extends StatelessWidget {
  final BottomItem bottmData;
  const BottomInfo({super.key, required this.bottmData});
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
            decoration: BoxDecoration(
              color: const Color.fromARGB(255, 254, 254, 254),
              borderRadius: BorderRadius.circular(9.0),
              border: Border.all(
                color: Colors.white,
                width: 1.0, // 边框宽度
              ),
            ),
            padding: EdgeInsets.only(
                left: 16.px, right: 16.px, top: 16.px, bottom: 16.px),
            child: Center(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Image.asset(
                            width: 40.px,
                            ImageHelper.wrapAssets(bottmData.icon)),
                        SizedBox(width: 10.px),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    constraints:
                                        BoxConstraints(maxWidth: 200.px),
                                    child: Text(
                                      bottmData.title,
                                      style: TextStyle(
                                          color: const Color.fromRGBO(
                                              44, 44, 52, 1),
                                          fontSize: 15.px,
                                          fontWeight: FontWeight.w900),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                  ),
                                  SizedBox(
                                    width: 8.px,
                                  ),
                                  Container(
                                      decoration: BoxDecoration(
                                        color: const Color.fromRGBO(
                                            243, 84, 66, 0.1),
                                        borderRadius:
                                            BorderRadius.circular(2.0),
                                      ),
                                      child: bottmData.isReturn
                                          ? Padding(
                                              padding: EdgeInsets.only(
                                                  left: 3.px,
                                                  right: 3.px,
                                                  bottom: 2.px),
                                              child: Text(
                                                "退回",
                                                style: TextStyle(
                                                    color: const Color.fromRGBO(
                                                        243, 84, 66, 1),
                                                    fontSize: 12.px,
                                                    fontWeight:
                                                        FontWeight.w400),
                                              ))
                                          : Container())
                                ],
                              ),
                              SizedBox(
                                height: 8.px,
                              ),
                              Text(
                                bottmData.text,
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w400),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ],
                          ),
                        ),
                        Icon(
                          Icons.chevron_right,
                          size: 22.px,
                          color: const Color.fromARGB(195, 187, 187, 184),
                        ),
                        //  const Icon('chevron_right' as IconData?)
                        // ),
                      ],
                    )
                  ]),
            )),
        SizedBox(height: 10.px),
      ],
    );
  }
}
