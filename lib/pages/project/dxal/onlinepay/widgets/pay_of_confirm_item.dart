import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class PayOfConfirmItemWidget extends StatefulWidget {
  final Map<String, dynamic> confirmPayItem;

  const PayOfConfirmItemWidget({super.key, required this.confirmPayItem});

  @override
  State<PayOfConfirmItemWidget> createState() => _PayOfConfirmItemWidgetState();
}

class _PayOfConfirmItemWidgetState extends State<PayOfConfirmItemWidget> {
  final Color _themeColor = const Color.fromRGBO(0, 152, 91, 1);
  final Color _labelTextColor = const Color.fromRGBO(44, 44, 52, 1);
  bool isShowDetail = false;
  bool isChecked = false;
  List<dynamic> subjectResult = [];

  @override
  void initState() {
    super.initState();
    subjectResult = widget.confirmPayItem['list'] ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 351.px,
      alignment: Alignment.centerLeft,
      padding:
          EdgeInsets.only(top: 16.px, left: 16.px, right: 16.px, bottom: 12.px),
      margin: EdgeInsets.only(bottom: 10.px),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(getPayItemTitle(widget.confirmPayItem) ?? "",
                      style: TextStyle(
                          fontSize: 18.px,
                          color: const Color.fromRGBO(44, 44, 52, 1))),
                  SizedBox(
                    height: 8.px,
                  ),
                  SizedBox(
                    width: 220.px,
                    child: Text(widget.confirmPayItem['organizationName'] ?? "",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style:
                            TextStyle(fontSize: 14.px, color: _labelTextColor)),
                  )
                ],
              ),
              const Spacer(),
              Text(
                  widget.confirmPayItem['totalAmout'].toStringAsFixed(2) ??
                      "0.00",
                  style: TextStyle(
                      fontSize: 24.px,
                      fontFamily: "bayon",
                      letterSpacing: 1,
                      color: _themeColor,
                      fontWeight: FontWeight.w600)),
            ],
          ),
          SizedBox(
            height: 15.px,
          ),
          Divider(
            color: const Color.fromRGBO(218, 221, 229, 1),
            height: 1.px,
          ),
          SizedBox(
            height: 12.px,
          ),
          Row(
            children: [
              Text(
                widget.confirmPayItem['payDate'] ?? "",
                style: TextStyle(fontSize: 12.px, color: _labelTextColor),
              ),
              const Spacer(),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  setState(() {
                    isShowDetail = !isShowDetail;
                  });
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Text(isShowDetail ? "收起" : "展开",
                        style:
                            TextStyle(fontSize: 12.px, color: _labelTextColor)),
                    Icon(
                      isShowDetail
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: _labelTextColor,
                      size: 18.px,
                    )
                  ],
                ),
              ),
            ],
          ),
          isShowDetail
              ? Container(
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(top: 12.px, left: 39.px, right: 0.px),
                  padding: EdgeInsets.only(
                      bottom: 7.px, top: 7.px, right: 12.px, left: 12.px),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: const Color.fromRGBO(243, 245, 249, 1),
                  ),
                  child: Column(
                    children: subjectResult
                        .map((item) => Container(
                            padding: EdgeInsets.only(
                                bottom: 5.px,
                                top: 5.px,
                                right: 0.px,
                                left: 0.px),
                            child: Row(children: [
                              Text(item['chargeSubjectName'] ?? "",
                                  style: TextStyle(
                                      fontSize: 12.px,
                                      color:
                                          const Color.fromRGBO(44, 44, 52, 1))),
                              const Spacer(),
                              Text(
                                  "${item['chargeAmount']?.toStringAsFixed(2) ?? "0.00"}",
                                  style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: "bayon",
                                    letterSpacing: 1,
                                    color: _themeColor,
                                  ))
                            ])))
                        .toList(),
                  ),
                )
              : Container()
        ],
      ),
    );
  }

  getPayItemTitle(item) {
    switch (item['chargeCategoryType']) {
      case 1:
        return "承包费";
      case 2:
        return "其他收费";
      case 3:
        return "种子费";
      default:
        return "";
    }
  }

  @override
  void didUpdateWidget(covariant PayOfConfirmItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.confirmPayItem != widget.confirmPayItem) {
      setState(() {});
    }
  }
}
