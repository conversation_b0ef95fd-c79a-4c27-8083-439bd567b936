import 'dart:typed_data';

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:signature/signature.dart';

class DrawView extends StatefulWidget {
  final SignatureController signatureController;
  final Function biggerCallback; // 放大回调
  final Function completeCallback; //
  final Function? previewCallback; //
  final double width;
  final double height;
  final bool landScape; // 是否横屏

  const DrawView(
      {super.key,
      required this.signatureController,
      required this.biggerCallback,
      required this.completeCallback,
      this.previewCallback,
      required this.width,
      required this.height,
      required this.landScape});

  @override
  State<StatefulWidget> createState() => _DrawViewState();
}

class _DrawViewState extends State<DrawView> {
  final Color _themeColor = const Color.fromRGBO(0, 152, 91, 1);
  late bool isEmpty;
  bool isPreview = false;
  Uint8List? _signatureImage;

  @override
  void initState() {
    super.initState();

    if (widget.signatureController.value.isNotEmpty) {
      isEmpty = false;
    } else {
      isEmpty = true;
    }

    // 监听画板
    widget.signatureController.addListener(() {
      bool tmpIsEmpty = true;
      if (widget.signatureController.value.isNotEmpty) {
        tmpIsEmpty = false;
      } else {
        tmpIsEmpty = true;
      }
      if (isEmpty != tmpIsEmpty) {
        if (mounted) {
          setState(() {
            isEmpty = tmpIsEmpty;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Expanded(
            child: _signatureImage == null && !isPreview
                ? Stack(
                    alignment: Alignment.center,
                    children: [
                      // 暂无签名
                      Offstage(
                        offstage: isEmpty ? false : true,
                        child: Text(
                          '请在此处签名',
                          style: TextStyle(
                            fontSize: 36.px,
                            color: const Color.fromRGBO(165, 172, 180, 1.0),
                          ),
                        ),
                      ),
                      Signature(
                        controller: widget.signatureController,
                        width: widget.width,
                        backgroundColor: Colors.transparent,
                      ),
                    ],
                  )
                : Container(
                    width: widget.width,
                    color: Colors.white,
                    child: Image.memory(_signatureImage!),
                  )),
        Divider(height: 0.1.px, color: Colors.grey.shade300),
        Container(
          padding: EdgeInsets.only(top: 4.px, right: 12.px, bottom: 4.px),
          height: 28.px,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 47.5.px,
                height: 20.px,
                margin: EdgeInsets.only(left: 10.px),
                child: ElevatedButton(
                    style: TextButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.px)),
                    ),
                    onPressed: () {
                      widget.signatureController.clear();
                      setState(() {
                        _signatureImage = null;
                        isPreview = false;
                      });
                    },
                    child: Text("重写",
                        style: TextStyle(
                            fontSize: 7.5.px,
                            color: Colors.black,
                            fontWeight: FontWeight.w500))),
              ),
              Container(
                width: 47.5.px,
                height: 20.px,
                margin: EdgeInsets.only(left: 10.px),
                child: ElevatedButton(
                    style: TextButton.styleFrom(
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.px)),
                    ),
                    onPressed: () {
                      widget.signatureController
                          .toPngBytes()
                          .then((imageBytes) {
                        setState(() {
                          _signatureImage = imageBytes;
                          isPreview = true;
                        });
                      });
                    },
                    child: Text("预览",
                        style: TextStyle(
                            fontSize: 7.5.px,
                            color: Colors.black,
                            fontWeight: FontWeight.w500))),
              ),
              // 完成
              Container(
                width: 47.5.px,
                height: 20.px,
                margin: EdgeInsets.only(left: 10.px),
                child: ElevatedButton(
                    style: TextButton.styleFrom(
                      backgroundColor: _themeColor,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4.px)),
                    ),
                    onPressed: () {
                      if (isEmpty) {
                        showToast('请先签名');
                        return;
                      }
                      widget.signatureController
                          .toPngBytes()
                          .then((imageBytes) {
                        setState(() {
                          _signatureImage = imageBytes;
                          widget.completeCallback(_signatureImage);
                        });
                      });
                    },
                    child: Text("确定",
                        style: TextStyle(
                            fontSize: 7.5.px,
                            color: Colors.white,
                            fontWeight: FontWeight.w500))),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
