import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PayOfMineItemWidget extends StatefulWidget {
  // final String? title;
  // final String? subTitle;
  // final String? price;
  // final String? date;
  final Map<String, dynamic> payItem;

  const PayOfMineItemWidget({super.key, required this.payItem});

  @override
  State<PayOfMineItemWidget> createState() => _PayOfMineItemWidgetState();
}

class _PayOfMineItemWidgetState extends State<PayOfMineItemWidget> {
  final Color _themeColor = const Color.fromRGBO(0, 152, 91, 1);
  bool isShowDetail = false;
  List<dynamic> subjectItems = [];

  @override
  void initState() {
    super.initState();
    setState(() {
      subjectItems = widget.payItem['params'] ?? [];
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 351.px,
      margin: EdgeInsets.only(top: 5.px, bottom: 5.px),
      padding:
          EdgeInsets.only(left: 16.px, top: 18.px, right: 12.px, bottom: 19.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(6.px))),
      child: Column(
        children: [
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        widget.payItem['serialNumber'] ?? "",
                        style: TextStyle(
                            fontSize: 16.px,
                            color: const Color.fromRGBO(44, 44, 52, 1)),
                      ),
                      _buildItemType(
                        widget.payItem['chargeCategoryType'] ?? 1,
                      )
                    ],
                  ),
                  SizedBox(height: 6.px),
                  SizedBox(
                    width: 200.px,
                    child: Text(
                      widget.payItem['organizationName'] ?? "",
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 12.px,
                          color: const Color.fromRGBO(124, 124, 128, 1)),
                    ),
                  )
                ],
              ),
              const Spacer(),
              Text(
                widget.payItem['amout'] == null
                    ? '0.00'
                    : '${widget.payItem['amout'].toStringAsFixed(2)}',
                style: TextStyle(
                    fontFamily: "bayon",
                    letterSpacing: 1,
                    color: _themeColor,
                    fontSize: 24.px),
              )
            ],
          ),
          SizedBox(
            height: 15.px,
          ),
          Divider(
            height: 1.px,
            color: const Color.fromRGBO(218, 221, 229, 1),
            thickness: 1,
          ),
          SizedBox(
            height: 13.px,
          ),
          Row(children: [
            Text(widget.payItem['payDate'] ?? "",
                style: TextStyle(
                    fontSize: 12.px,
                    color: const Color.fromRGBO(124, 124, 128, 1))),
            const Spacer(),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 26.px,
                width: 60.px,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4.px),
                    border: Border.all(
                        width: 0.5.px,
                        color: const Color.fromRGBO(117, 117, 121, 1.0))),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(isShowDetail ? "收起" : "展开",
                        style: TextStyle(
                            fontSize: 12.px,
                            fontWeight: FontWeight.w500,
                            color: const Color.fromRGBO(117, 117, 121, 1.0))),
                    Icon(
                      isShowDetail
                          ? Icons.keyboard_arrow_up
                          : Icons.keyboard_arrow_down,
                      color: const Color.fromRGBO(117, 117, 121, 1.0),
                      size: 16.px,
                    )
                  ],
                ),
              ),
              onTap: () {
                setState(() {
                  isShowDetail = !isShowDetail;
                });
              },
            ),
            // SizedBox(
            //   width: 10.px,
            // ),
            // SizedBox(
            //   width: 60.px,
            //   height: 26.px,
            //   child: TextButton(
            //       style: TextButton.styleFrom(
            //           minimumSize: Size(60.px, 26.px),
            //           padding: EdgeInsets.zero,
            //           shape: RoundedRectangleBorder(
            //               borderRadius: BorderRadius.circular(4.px),
            //               side: BorderSide(width: 0.5.px, color: _themeColor))),
            //       onPressed: () {
            //         Navigator.of(context)
            //             .push(CupertinoPageRoute(builder: (ctx) {
            //           return SignatureNamePage();
            //         }));
            //       },
            //       child: Text("补签字",
            //           style: TextStyle(
            //               fontSize: 12.px,
            //               fontWeight: FontWeight.w500,
            //               color: _themeColor))),
            // ),
            // SizedBox(
            //   width: 10.px,
            // ),
            // SizedBox(
            //   width: 60.px,
            //   height: 26.px,
            //   child: TextButton(
            //       style: TextButton.styleFrom(
            //           minimumSize: Size(60.px, 26.px),
            //           padding: EdgeInsets.zero,
            //           shape: RoundedRectangleBorder(
            //               borderRadius: BorderRadius.circular(4.px),
            //               side: BorderSide(width: 0.5.px, color: _themeColor))),
            //       onPressed: () {
            //         Navigator.of(context)
            //             .push(CupertinoPageRoute(builder: (ctx) {
            //           return TakeFacePhotoPage();
            //         }));
            //       },
            //       child: Text("补拍照",
            //           style: TextStyle(
            //               fontSize: 12.px,
            //               fontWeight: FontWeight.w500,
            //               color: _themeColor))),
            // ),
          ]),
          isShowDetail
              ? Container(
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(top: 12.px, left: 0.px, right: 0.px),
                  padding: EdgeInsets.all(12.px),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: const Color.fromRGBO(243, 245, 249, 1),
                  ),
                  child: Column(
                    children: subjectItems
                        .map((item) => Container(
                            padding: EdgeInsets.only(
                                bottom: 5.px,
                                top: 5.px,
                                right: 0.px,
                                left: 0.px),
                            child: Row(children: [
                              Text(item['chargeSubjectName'] ?? "",
                                  style: TextStyle(
                                      fontSize: 12.px,
                                      color: const Color.fromRGBO(
                                          92, 94, 106, 1))),
                              const Spacer(),
                              Text(
                                  "${item['amout']?.toStringAsFixed(2) ?? "0.00"}",
                                  style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: "bayon",
                                    color: _themeColor,
                                  ))
                            ])))
                        .toList(),
                  ),
                )
              : Container()
        ],
      ),
    );
  }

  Widget _buildItemType(int type) {
    switch (type) {
      case 1:
        return Container(
            alignment: Alignment.center,
            height: 19.px,
            margin: EdgeInsets.only(left: 6.px),
            padding: EdgeInsets.only(left: 8.px, right: 8.px, bottom: 2.px),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.px),
                color: const Color.fromRGBO(2, 139, 93, 0.1)),
            child: Text("承包费",
                style: TextStyle(
                    fontSize: 12.px,
                    fontWeight: FontWeight.w400,
                    color: _themeColor)));

      case 2:
        return Container(
            alignment: Alignment.center,
            height: 19.px,
            margin: EdgeInsets.only(left: 6.px),
            padding: EdgeInsets.only(left: 8.px, right: 8.px, bottom: 2.px),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.px),
                color: const Color.fromRGBO(91, 145, 250, 0.15)),
            child: Text("其他收费",
                style: TextStyle(
                    fontSize: 12.px,
                    fontWeight: FontWeight.w400,
                    color: const Color.fromRGBO(41, 102, 255, 1))));
      case 3:
        return Container(
            alignment: Alignment.center,
            height: 19.px,
            margin: EdgeInsets.only(left: 6.px),
            padding: EdgeInsets.only(left: 8.px, right: 8.px, bottom: 2.px),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.px),
                color: const Color.fromRGBO(91, 145, 250, 0.15)),
            child: Text("种子费",
                style: TextStyle(
                    fontSize: 12.px,
                    fontWeight: FontWeight.w400,
                    color: const Color.fromRGBO(246, 69, 117, 1.0))));
    }
    return Container();
  }

  @override
  void didUpdateWidget(covariant PayOfMineItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.payItem != widget.payItem) {
      setState(() {});
      // 强制竖屏
      SystemChrome.setPreferredOrientations(
          [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    }
  }
}
