import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PaymentItemWidget extends StatefulWidget {
  final ValueChanged<bool?>? onSelectChanged;
  final Map<String, dynamic> precinctItem;
  final bool isSelected;

  const PaymentItemWidget(
      {super.key,
      required this.precinctItem,
      required this.onSelectChanged,
      required this.isSelected});

  @override
  State<PaymentItemWidget> createState() => _PaymentItemWidgetState();
}

class _PaymentItemWidgetState extends State<PaymentItemWidget> {
  final Color _themeColor = const Color.fromRGBO(0, 152, 91, 1);
  final Color _labelTextColor = const Color.fromRGBO(51, 51, 51, 1);
  bool isShowDetail = false;
  bool isChecked = false;
  List<dynamic> subjectResult = [];

  // String? title;
  // String? subTitle;
  //
  // _PaymentItemWidgetState(this.title, this.subTitle);

  void handleCheckboxChange(bool value) {
    setState(() {
      isChecked = value;
    });
    widget.onSelectChanged!(isChecked);
  }

  @override
  void initState() {
    super.initState();
    subjectResult = widget.precinctItem['subjectResult'] ?? [];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 351.px,
      alignment: Alignment.centerLeft,
      padding:
          EdgeInsets.only(top: 16.px, left: 2.px, right: 16.px, bottom: 16.px),
      margin: EdgeInsets.only(top: 10.px),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Colors.white,
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              CupertinoCheckbox(
                  activeColor: _themeColor,
                  value: isChecked || widget.isSelected,
                  shape: const StadiumBorder(),
                  onChanged: (s) {
                    handleCheckboxChange(!isChecked);
                  }),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(getPayItemTitle(widget.precinctItem) ?? "",
                      style: TextStyle(
                          fontSize: 18.px,
                          color: const Color.fromRGBO(44, 44, 52, 1))),
                  SizedBox(
                    height: 8.px,
                  ),
                  Text(widget.precinctItem['organizationName'] ?? "",
                      style: TextStyle(fontSize: 14.px, color: _labelTextColor))
                ],
              ),
              const Spacer(),
              Column(
                children: [
                  Row(
                    children: [
                      Text(
                          widget.precinctItem['unpayedAmount']
                                  .toStringAsFixed(2) ??
                              "0.00",
                          style: TextStyle(
                              fontSize: 18.px,
                              fontFamily: "bayon",
                              letterSpacing: 1,
                              color: _themeColor,
                              fontWeight: FontWeight.w600)),
                      Text("\u2006元",
                          style: TextStyle(
                              fontSize: 12.px, color: _labelTextColor)),
                    ],
                  ),
                  SizedBox(
                    height: 7.px,
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      setState(() {
                        isShowDetail = !isShowDetail;
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(isShowDetail ? "收起明细" : "查看明细",
                            style: TextStyle(
                                fontSize: 12.px, color: _labelTextColor)),
                        Icon(
                          isShowDetail
                              ? Icons.keyboard_arrow_up
                              : Icons.keyboard_arrow_down,
                          color: _labelTextColor,
                          size: 18.px,
                        )
                      ],
                    ),
                  ),
                ],
              )
            ],
          ),
          isShowDetail
              ? Container(
                  alignment: Alignment.centerLeft,
                  margin: EdgeInsets.only(top: 12.px, left: 39.px, right: 0.px),
                  padding: EdgeInsets.only(
                      bottom: 7.px, top: 7.px, right: 12.px, left: 12.px),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(6),
                    color: const Color.fromRGBO(243, 245, 249, 1),
                  ),
                  child: Column(
                    children: subjectResult
                        .map((item) => Container(
                            padding: EdgeInsets.only(
                                bottom: 5.px,
                                top: 5.px,
                                right: 0.px,
                                left: 0.px),
                            child: Row(children: [
                              Text(item['chargeSubjectName'] ?? "",
                                  style: TextStyle(
                                      fontSize: 12.px,
                                      color:
                                          const Color.fromRGBO(44, 44, 52, 1))),
                              const Spacer(),
                              Text(
                                  "${item['unpayedAmount']?.toStringAsFixed(2) ?? "0.00"}元",
                                  style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    fontFamily: "bayon",
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                  ))
                            ])))
                        .toList(),
                  ),
                )
              : Container()
        ],
      ),
    );
  }

  getPayItemTitle(item) {
    switch (item['chargeCategoryType']) {
      case 1:
        return "承包费";
      case 2:
        return "其他收费";
      case 3:
        return "种子费";
      default:
        return "";
    }
  }

  @override
  void didUpdateWidget(covariant PaymentItemWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.precinctItem != widget.precinctItem ||
        oldWidget.isSelected != widget.isSelected) {
      setState(() {});
    }
  }
}
