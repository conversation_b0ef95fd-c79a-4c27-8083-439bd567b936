import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:logger/web.dart';
import 'dart:async';
import 'dart:io';

class TakeFacePhotoPage extends StatefulWidget {
  @override
  _TakeFacePhotoPageState createState() => _TakeFacePhotoPageState();
}

class _TakeFacePhotoPageState extends State<TakeFacePhotoPage>
    with WidgetsBindingObserver {
  late CameraController _controller;
  late Future<void> _initializeControllerFuture;
  List<CameraDescription> _cameras = <CameraDescription>[];

  bool isHasTakePhoto = false;

  String selectedImagePath = '';

  bool isTaking = false;

  bool isInit = false;

  @override
  void initState() {
    super.initState();
    availableCameras().then((res) {
      _cameras = res;
      Logger().i('_cameras = $_cameras');
      for (int i = 0; i < _cameras.length; i++) {
        CameraDescription item = _cameras[i];
        Logger().i('name = ${item.lensDirection}');
        if (item.lensDirection == CameraLensDirection.front) {
          _initializeCameraController(item);
          break;
        }
      }
    });
    WidgetsBinding.instance.addObserver(this);
    // _initializeCameraController();
  }

  _initializeCameraController(CameraDescription item) {
    final CameraController cameraController = CameraController(
      // 使用默认的前置摄像头
      // const CameraDescription(
      //     name: 'FRONT',
      //     lensDirection: CameraLensDirection.front,
      //     sensorOrientation: 0),
      item,
      ResolutionPreset.max,
      enableAudio: false,
      imageFormatGroup: ImageFormatGroup.jpeg,
    );

    _controller = cameraController;
    _initializeControllerFuture = _controller.initialize();
    isInit = true;
    // If the controller is updated then update the UI.
    cameraController.addListener(() {
      if (mounted) {
        setState(() {});
      }
      if (cameraController.value.hasError) {}
    });
  }
  // Future<void> _initializeCameraController(CameraDescription item) async {
  //   final CameraController cameraController = CameraController(
  //     // 使用默认的前置摄像头
  //     // const CameraDescription(
  //     //     name: 'FRONT',
  //     //     lensDirection: CameraLensDirection.front,
  //     //     sensorOrientation: 0),
  //     item,
  //     ResolutionPreset.max,
  //     enableAudio: false,
  //     imageFormatGroup: ImageFormatGroup.jpeg,
  //   );

  //   _controller = cameraController;
  //   _initializeControllerFuture = _controller.initialize();
  //   // If the controller is updated then update the UI.
  //   cameraController.addListener(() {
  //     if (mounted) {
  //       setState(() {});
  //     }
  //     if (cameraController.value.hasError) {}
  //   });
  // }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      if (isInit) {
        _controller.resumePreview();
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const Color themeColor = Color.fromRGBO(0, 152, 91, 1);
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            icon: const Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
              size: 24,
            )),
        title: const Text(
          '人脸照片采集',
          style: TextStyle(
              color: Colors.white, fontSize: 18, fontWeight: FontWeight.w400),
        ),
        backgroundColor: themeColor,
      ),
      body: Container(
        color: Colors.white,
        alignment: Alignment.center,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Stack(children: [
              selectedImagePath == '' && isInit
                  ? CameraPreview(_controller)
                  : Image.file(File(selectedImagePath), fit: BoxFit.cover),
              const Image(
                  fit: BoxFit.cover,
                  image: AssetImage(
                      "assets/images/pay/pay_photo_front_cover.png")),
              // Container(
              //   decoration: const BoxDecoration(
              //       image: DecorationImage(
              //           image: AssetImage(
              //               "assets/images/pay/pay_photo_front_cover.png"))),
              //   child: Column(
              //     children: [],
              //   ),
              // )
              Positioned(
                  child: Center(
                child: Container(
                  margin: EdgeInsets.only(top: 30.px),
                  height: 40.px,
                  width: 280.px,
                  alignment: Alignment.center,
                  child: Text(
                    '请将脸放入框内，确保人脸完整',
                    style: TextStyle(
                        color: Colors.redAccent,
                        fontSize: 18.px,
                        fontWeight: FontWeight.w400),
                  ),
                ),
              )),
              Positioned(
                  bottom: 220.px,
                  left: 40.px,
                  right: 40.px,
                  child: selectedImagePath == ""
                      ? Container(
                          height: 46.px,
                          width: 280.px,
                          child: ElevatedButton(
                            onPressed: () {
                              onTakePicture();
                            },
                            style: ButtonStyle(
                              backgroundColor:
                                  WidgetStateProperty.all(themeColor),
                              shape: WidgetStateProperty.all(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8.0),
                                ),
                              ),
                            ),
                            child: const Text(
                              '拍照',
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  letterSpacing: 4,
                                  fontWeight: FontWeight.w400),
                            ),
                          ),
                        )
                      : SizedBox(
                          height: 46.px,
                          width: 280.px,
                          child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                SizedBox(
                                  width: 120.px,
                                  child: ElevatedButton.icon(
                                    onPressed: () {
                                      // onRetakeButtonPressed();
                                      // _initializeCameraController()
                                      // ;
                                      setState(() {
                                        selectedImagePath = '';
                                      });
                                    },
                                    style: ButtonStyle(
                                        backgroundColor:
                                            WidgetStateProperty.all(
                                                const Color.fromRGBO(
                                                    152, 152, 154, 1.0)),
                                        shape: WidgetStateProperty.all(
                                          RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.0),
                                          ),
                                        )),
                                    icon: const Icon(
                                      Icons.refresh,
                                      color: Colors.white,
                                    ),
                                    label: const Text("重拍",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 18,
                                            fontWeight: FontWeight.w400)),
                                  ),
                                ),
                                SizedBox(
                                  width: 120.px,
                                  child: ElevatedButton.icon(
                                      onPressed: () {
                                        Navigator.of(context)
                                            .pop(selectedImagePath);
                                      },
                                      style: ButtonStyle(
                                          backgroundColor:
                                              WidgetStateProperty.all(
                                                  themeColor),
                                          shape: WidgetStateProperty.all(
                                            RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8.0),
                                            ),
                                          )),
                                      icon: const Icon(
                                        Icons.check,
                                        color: Colors.white,
                                      ),
                                      label: const Text("确定",
                                          style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 18,
                                              fontWeight: FontWeight.w400))),
                                )
                              ])))
            ]),
          ],
        ),
      ),
    );
  }

  Future<void> onTakePicture() async {
    Logger().i('takePicture--1');
    setState(() {
      isTaking = true;
    });
    _controller.takePicture().then((XFile? file) async {
      Logger().i('takePicture--then---2');
      if (mounted) {
        // onPausePreview();
        if (file != null) {
          // print("cropImage targetFile:${targetFile}");
          // if (targetFile != null) {
          //   selectedImagePath = targetFile.path;
          //   // await SaveToAlbumUtil.saveLocalImage(targetFile.path);
          // }
          setState(() {
            isHasTakePhoto = true;
            selectedImagePath = file.path;
          });
        } else {
          // 没有获得图片，重试
        }
        setState(() {
          isTaking = false;
        });
      }
    });
  }

  Future<void> onPausePreview() async {
    final CameraController? cameraController = _controller;

    if (cameraController == null || !cameraController.value.isInitialized) {
      print('Error: select a camera first.');
      Logger().i('Error: select a camera first.');
      return;
    }

    if (!cameraController.value.isPreviewPaused) {
      Logger().i('Error: select a camera first---pausePreview.');
      await cameraController.pausePreview();
    }
  }

  void onRetakeButtonPressed() {
    setState(() {
      isHasTakePhoto = false;
    });
    selectedImagePath = '';
    onResumePreview();
  }

  Future<void> onResumePreview() async {
    if (_controller.value.isPreviewPaused) {
      await _controller.resumePreview();
    } else {
      await _controller.pausePreview();
    }
    if (mounted) {
      setState(() {});
    }
  }
}
