import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:signature/signature.dart';

import 'widgets/draw_view.dart';

/// @file signature_name_page.dart
///
/// <AUTHOR>
/// @date 2024/11/28 11:26
/// @description 手写签名功能
///
///
class SignatureNamePage extends StatefulWidget {
  const SignatureNamePage({super.key});

  @override
  State<SignatureNamePage> createState() => _SignatureNamePageState();
}

class _SignatureNamePageState extends State<SignatureNamePage> {
  @override
  void initState() {
    super.initState();
    // 强制横屏
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.landscapeLeft, DeviceOrientation.landscapeRight]);
  }

  final SignatureController _signatureController = SignatureController(
    penStrokeWidth: 2.px, // 线条宽度
    penColor: Colors.black, // 线条颜色
    exportBackgroundColor: Colors.transparent, // 导出图片背景色
  );

  bool landScape = false;

  @override
  Widget build(BuildContext context) {
    // 手写板横屏
    return Scaffold(
        appBar: AppBar(
          toolbarHeight: 20.px,
          leading: IconButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              icon: const Icon(
                Icons.arrow_back_ios,
                color: Colors.black,
                size: 24,
              )),
          title: const Text(
            '签字确认',
            style: TextStyle(
                color: Colors.black, fontSize: 18, fontWeight: FontWeight.w400),
          ),
          backgroundColor: Colors.white,
        ),
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          color: Colors.grey.shade100,
          child: DrawView(
            signatureController: _signatureController,
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.height,
            landScape: landScape,
            biggerCallback: () {
              setState(() {
                landScape = false;
              });
            },
            completeCallback: (Uint8List? signatureImage) {
              Navigator.pop(context, signatureImage);
            },
          ),
        ));
  }

  @override
  void dispose() {
    // 强制竖屏
    SystemChrome.setPreferredOrientations(
        [DeviceOrientation.portraitUp, DeviceOrientation.portraitDown]);
    _signatureController.dispose();
    super.dispose();
  }
}
