import 'package:bdh_smart_agric_app/components/jh_cascade_tree_picker.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/project/dxal/onlinepay/widgets/pay_of_confirm_item.dart';
import 'package:bdh_smart_agric_app/utils/request/dahing_online_pay_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../../utils/color_util.dart';

class PayToConfirmPage extends StatefulWidget {
  final List<dynamic> selectYears;
  final OrgTreeResult? orgTree;

  const PayToConfirmPage({super.key, required this.selectYears, this.orgTree});

  @override
  State<PayToConfirmPage> createState() => _PayToConfirmPageState();
}

class _PayToConfirmPageState extends State<PayToConfirmPage>
    with AutomaticKeepAliveClientMixin {
  final Color _themeColor = const Color.fromRGBO(0, 152, 91, 1);
  String _currentYear = "";
  FixedExtentScrollController? yearSelectionController;
  int _currentYearIndex = -1;
  int _currentSelectedYearIndex = -1;
  Map<dynamic, dynamic>? currentOrg;

  dynamic confirmPaymentRecord;

  List<dynamic> confirmPaymentList = [];

  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    var now = DateTime.now();
    _currentYear = now.year.toString();

    loadToConfirmChargePayment();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Material(
      child: Column(
        children: [
          Container(
            width: 375.px,
            height: 285.px,
            padding: EdgeInsets.only(top: 40.px),
            decoration: const BoxDecoration(
                image: DecorationImage(
                    image:
                        AssetImage("assets/images/pay/pay_confirm_head_bg.png"),
                    fit: BoxFit.cover)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BackButton(
                  color: Colors.white,
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      margin: EdgeInsets.only(left: 25.px),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  "未支付(元)",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14.px,
                                      fontWeight: FontWeight.w400),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () {
                                    showModalBottomSheet(
                                        context: context,
                                        elevation: 10,
                                        enableDrag: false,
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.px)),
                                        builder: (BuildContext context) {
                                          return showBottomSelectYearsPicker();
                                        });
                                  },
                                  child: Container(
                                    width: 62.px,
                                    height: 19.px,
                                    margin: EdgeInsets.only(left: 5.px),
                                    padding: EdgeInsets.only(
                                        left: 6.px, right: 2.px),
                                    decoration: BoxDecoration(
                                        color: _themeColor,
                                        borderRadius:
                                            BorderRadius.circular(16.px)),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(_currentYear,
                                            style: TextStyle(
                                                fontSize: 12.px,
                                                color: Colors.white)),
                                        Icon(
                                          Icons.arrow_drop_down,
                                          color: Colors.white,
                                          size: 16.px,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 12.px),
                              child: Text(
                                confirmPaymentRecord != null
                                    ? confirmPaymentRecord["totalAmout"]
                                        ?.toStringAsFixed(2)
                                    : "0.0",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 38.px,
                                    fontFamily: "bayon",
                                    letterSpacing: 1,
                                    fontWeight: FontWeight.bold),
                              ),
                            )
                          ]),
                    ),
                    Container(
                      height: 19.px,
                      margin: EdgeInsets.only(right: 23.px),
                      child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          showBottomMultiSelectPicker(context);
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                                currentOrg == null
                                    ? "请选择单位"
                                    : currentOrg?['orgName'],
                                style: TextStyle(
                                    fontSize: 12.px, color: Colors.white)),
                            Icon(
                              Icons.arrow_drop_down,
                              color: Colors.white,
                              size: 16.px,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Container(
                  margin: EdgeInsets.only(left: 12.px),
                  width: 351.px,
                  height: 107.px,
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                          image:
                              AssetImage("assets/images/pay/pay_card_bg.png"),
                          fit: BoxFit.cover)),
                  child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "承包费(元)",
                              style: TextStyle(
                                  color: const Color.fromRGBO(44, 44, 52, 1),
                                  fontSize: 14.px),
                            ),
                            SizedBox(height: 8.px),
                            Text(
                              confirmPaymentRecord != null
                                  ? confirmPaymentRecord["contractAmout"]
                                      ?.toStringAsFixed(2)
                                  : "0.0",
                              style: TextStyle(
                                  color: const Color.fromRGBO(0, 152, 91, 1),
                                  fontFamily: "bayon",
                                  letterSpacing: 1,
                                  fontSize: 22.px),
                            )
                          ],
                        ),
                        VerticalDivider(
                            width: 1.px,
                            color: const Color.fromRGBO(227, 229, 234, 1),
                            thickness: 1,
                            indent: 26.px,
                            endIndent: 27.5.px),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "其他收费(元)",
                              style: TextStyle(
                                  color: const Color.fromRGBO(44, 44, 52, 1),
                                  fontSize: 14.px),
                            ),
                            SizedBox(height: 8.px),
                            Text(
                              confirmPaymentRecord != null
                                  ? confirmPaymentRecord["otherAmout"]
                                      ?.toStringAsFixed(2)
                                  : "0.0",
                              style: TextStyle(
                                  color: const Color.fromRGBO(0, 152, 91, 1),
                                  fontFamily: "bayon",
                                  letterSpacing: 1,
                                  fontSize: 22.px),
                            ),
                          ],
                        ),
                        VerticalDivider(
                            width: 1.px,
                            color: const Color.fromRGBO(227, 229, 234, 1),
                            thickness: 1,
                            indent: 26.px,
                            endIndent: 27.5.px),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "种子收费(元)",
                              style: TextStyle(
                                  color: const Color.fromRGBO(44, 44, 52, 1),
                                  fontSize: 14.px),
                            ),
                            SizedBox(height: 8.px),
                            Text(
                              confirmPaymentRecord != null
                                  ? confirmPaymentRecord["zztotalAmout"]
                                      ?.toStringAsFixed(2)
                                  : "0.0",
                              style: TextStyle(
                                  color: const Color.fromRGBO(0, 152, 91, 1),
                                  fontFamily: "bayon",
                                  letterSpacing: 1,
                                  fontSize: 22.px),
                            ),
                          ],
                        )
                      ]),
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(left: 12.px, top: 16.px, right: 12.px),
            child: Column(
              children: [
                Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: 10.px),
                        width: 4.px,
                        height: 16.px,
                        decoration: BoxDecoration(
                            color: _themeColor,
                            borderRadius:
                                BorderRadius.all(Radius.circular(4.px))),
                      ),
                      Text("待支付明细",
                          style: TextStyle(
                              color: const Color.fromRGBO(44, 44, 52, 1),
                              fontWeight: FontWeight.w500,
                              fontSize: 16.px)),
                    ]),
              ],
            ),
          ),
          Expanded(
            child: confirmPaymentList.isEmpty
                ? SizedBox(
                    height: 200.px,
                    child: Center(
                      child: _isLoading
                          ? const SpinKitCircle(
                              // color: HexColor('#16B760'),
                              color: Color.fromRGBO(0, 127, 255, 1),
                              size: 50.0,
                            )
                          : Text(
                              "暂无数据",
                              style: TextStyle(
                                  color: Colors.grey, fontSize: 16.px),
                            ),
                    ),
                  )
                : Container(
                    padding: EdgeInsets.only(top: 10.px, bottom: 8.px),
                    child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        primary: true,
                        child: Column(
                            children: confirmPaymentList
                                .map((item) => PayOfConfirmItemWidget(
                                      confirmPayItem: item,
                                    ))
                                .toList())),
                  ),
          )
        ],
      ),
    );
  }

  Widget showBottomSelectYearsPicker() {
    if (_currentYearIndex == -1) {
      _currentYearIndex =
          widget.selectYears.indexWhere((item) => item["name"] == _currentYear);
      yearSelectionController =
          FixedExtentScrollController(initialItem: _currentYearIndex);
    } else {
      yearSelectionController =
          FixedExtentScrollController(initialItem: _currentYearIndex);
    }
    return BottomSheet(
        onClosing: () {},
        enableDrag: false,
        builder: (BuildContext context) {
          return Container(
              padding: EdgeInsets.only(left: 10.px, right: 10.px, top: 10.px),
              height: 280.px,
              width: 375.px,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "取消",
                              style: TextStyle(
                                  fontSize: 16.px, color: Colors.redAccent),
                            )),
                        Text(
                          "选择年份",
                          style: TextStyle(
                              color: const Color.fromRGBO(44, 44, 52, 1),
                              fontSize: 18.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextButton(
                            onPressed: () {
                              setState(() {
                                _currentYearIndex = _currentSelectedYearIndex;
                                _currentYear = widget
                                    .selectYears[_currentYearIndex]['name'];
                              });
                              loadToConfirmChargePayment();
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "确定",
                              style: TextStyle(
                                  color: Colors.blueAccent, fontSize: 16.px),
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 220.px,
                      child: CupertinoPicker(
                          scrollController: yearSelectionController,
                          itemExtent: 40.px,
                          squeeze: 1.5,
                          diameterRatio: 1,
                          onSelectedItemChanged: (index) {
                            setState(() {
                              _currentSelectedYearIndex = index;
                            });
                          },
                          children: widget.selectYears
                              .map((item) => Center(
                                    child: Text(item['name']),
                                  ))
                              .toList()),
                    )
                  ]));
        });
  }

  showBottomMultiSelectPicker(BuildContext context) {
    var tempData = [];
    for (var e in widget.orgTree!.data!) {
      tempData.add(e.toJson());
    }
    JhCascadeTreePicker.show(context,
        data: tempData,
        valueKey: "orgCode",
        labelKey: "orgName",
        childrenKey: "list",
        clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
      setState(() {
        currentOrg = (ress as List).last;
        loadToConfirmChargePayment();
      });
    });
  }

  @override
  bool get wantKeepAlive => true;

  loadData() async {
    DahingOnlinePayResponsitory.querychargePayment({"year": _currentYear})
        .then((value) {});
  }

  void loadToConfirmChargePayment() async {
    setState(() {
      _isLoading = true;
    });
    var params = {
      "yearNo": _currentYear,
      "organizationNo": currentOrg?["orgCode"],
    };
    DahingOnlinePayResponsitory.querychargePayment(params).then((res) {
      setState(() {
        _isLoading = false;
      });
      if (res.success!) {
        setState(() {
          confirmPaymentRecord = res.data;
          confirmPaymentList = confirmPaymentRecord["list"] ?? [];
        });
      }
    }, onError: (err) {
      setState(() {
        _isLoading = false;
      });
    });
  }
}
