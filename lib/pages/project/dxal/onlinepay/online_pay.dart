import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/request/dahing_online_pay_service.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:flutter/material.dart';
import '../../../../model/org_tree_list_model.dart';
import 'pay_of_mine.dart';
import 'pay_to_confirm.dart';
import 'pay_undo.dart';

class DaHingOnlinePay extends StatefulWidget {
  const DaHingOnlinePay({super.key});

  @override
  State<StatefulWidget> createState() => OnlinePayState();
}

class OnlinePayState extends State<DaHingOnlinePay> {
  int _currentIndex = 0;
  PageController pageController = PageController();

  final Color _themeColor = const Color.fromRGBO(0, 152, 91, 1);
  List<dynamic> _selectYears = [];
  OrgTreeResult? treeResult;

  @override
  void initState() {
    super.initState();
    bus.on("returnfun", (v) {
      setState(() {
        _currentIndex = 1;
        pageController.jumpToPage(1);
      });
    });
    getSelectYears();
    loadOrg();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
          controller: pageController,
          physics: const NeverScrollableScrollPhysics(),
          children: [
            UnPaymentPage(
              orgTree: treeResult,
              selectYears: _selectYears,
            ),
            PayToConfirmPage(
              orgTree: treeResult,
              selectYears: _selectYears,
            ),
            PayOfMinePage(
              orgTree: treeResult,
              selectYears: _selectYears,
            ),
          ]),
      bottomNavigationBar: BottomNavigationBar(
          currentIndex: _currentIndex,
          // fixedColor: const Color(0xff0081ff),
          //选中的颜色
          type: BottomNavigationBarType.fixed,
          onTap: (index) {
            setState(() {
              _currentIndex = index;
              pageController.jumpToPage(index);
            });
          },
          selectedLabelStyle: TextStyle(color: _themeColor),
          selectedItemColor: _themeColor,
          selectedFontSize: 12,
          unselectedFontSize: 10,
          items: [
            BottomNavigationBarItem(
              icon: ImageIcon(
                  const AssetImage("assets/images/pay/ic_to_be_pay.png"),
                  color: _currentIndex == 0 ? _themeColor : null,
                  size: 18),
              label: '待支付',
            ),
            BottomNavigationBarItem(
              icon: ImageIcon(
                  const AssetImage("assets/images/pay/ic_pay_confirm.png"),
                  color: _currentIndex == 1 ? _themeColor : null,
                  size: 18),
              label: '待确认',
            ),
            BottomNavigationBarItem(
              icon: ImageIcon(
                  const AssetImage("assets/images/pay/ic_pay_mine.png"),
                  color: _currentIndex == 2 ? _themeColor : null,
                  size: 18),
              label: '我的支付',
            )
          ]),
    );
  }

  // 获取选择年份
  void getSelectYears() {
    DahingOnlinePayResponsitory.querySelectYears({}).then((result) {
      if (result.success!) {
        List<dynamic> data = result.data;
        setState(() {
          _selectYears = data;
        });
      }
    });
  }

  // 加载组织机构
  loadOrg() {
    LandResponsitory.getOrgData().then((res) {
      setState(() {
        treeResult = res;
      });
    });
  }

  @override
  void dispose() {
    bus.off("returnfun");
    super.dispose();
  }
}
