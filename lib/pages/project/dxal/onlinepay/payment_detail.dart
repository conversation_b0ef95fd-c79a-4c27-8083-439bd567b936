import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/request/dahing_online_pay_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:oktoast/oktoast.dart';
import 'package:permission_handler/permission_handler.dart';

import 'signature_name_page.dart';
import 'take_face_photo_page.dart';
import 'widgets/payment_item_widget.dart';

class PaymentDetailPage extends StatefulWidget {
  final Map<String, dynamic> payItemInfo;
  final String? year;

  const PaymentDetailPage({super.key, required this.payItemInfo, this.year});

  @override
  State<PaymentDetailPage> createState() => _PaymentDetailPageState();
}

class _PaymentDetailPageState extends State<PaymentDetailPage>
    with WidgetsBindingObserver {
  final Color _themeColor = const Color.fromRGBO(0, 152, 91, 1);
  bool isCheckedAll = false;
  String facePhotoImagePath = "";
  Uint8List? signatureImage;
  dynamic paymentDetail;
  List<dynamic> precinctList = [];
  double totalPayAmount = 0.0;
  List<dynamic> bankList = [];
  dynamic paymentBankDetail;
  List<dynamic> showPaymentBankList = [];
  String? _bankType;
  String? organizationNo = "";

  String signatureUrl = '';
  String facePhotoUrl = '';
  Timer? _timer;
  int count = 0;
  bool isPaySuccess = false;

  dynamic curPayData;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    getBankList();
    getUnPaymentInfoDetail(widget.year, widget.payItemInfo['organizationNo']);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: const Color.fromRGBO(243, 245, 249, 1),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 375.px,
              height: 198.px,
              padding: EdgeInsets.only(top: 40.px),
              decoration: const BoxDecoration(
                  image: DecorationImage(
                      image:
                          AssetImage("assets/images/pay/pay_detail_top_bg.png"),
                      fit: BoxFit.cover)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Stack(
                    alignment: Alignment.centerLeft,
                    children: [
                      BackButton(
                        color: Colors.white,
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                      Center(
                        child: Text("北大荒线上支付",
                            style: TextStyle(
                                color: Colors.white, fontSize: 18.sp)),
                      )
                    ],
                  ),
                  const Spacer(),
                  Container(
                    margin: EdgeInsets.only(left: 12.px),
                    width: 351.px,
                    height: 107.px,
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                            image:
                                AssetImage("assets/images/pay/pay_card_bg.png"),
                            fit: BoxFit.cover)),
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            margin: EdgeInsets.only(left: 18.px),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  paymentDetail == null
                                      ? ""
                                      : paymentDetail['organizationName'],
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(44, 44, 52, 1),
                                      fontSize: 18.px),
                                ),
                                SizedBox(height: 2.px),
                                Text(
                                  "本次合计支付",
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(44, 44, 52, 1),
                                      fontSize: 12.px),
                                )
                              ],
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(right: 18.px),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text("¥",
                                    style: TextStyle(
                                        color: _themeColor, fontSize: 17.px)),
                                Text(
                                  paymentDetail == null
                                      ? "0.00"
                                      : paymentDetail['unpayedAmount']
                                          .toStringAsFixed(2),
                                  style: TextStyle(
                                      fontFamily: "bayon",
                                      letterSpacing: 1,
                                      color: _themeColor,
                                      fontSize: 28.px),
                                )
                              ],
                            ),
                          )
                        ]),
                  ),
                ],
              ),
            ),
            Container(
                padding: EdgeInsets.only(left: 12.px, top: 16.px, right: 12.px),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            margin: EdgeInsets.only(right: 10.px),
                            width: 4.px,
                            height: 16.px,
                            decoration: BoxDecoration(
                                color: _themeColor,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(4.px))),
                          ),
                          Text("待支付明细",
                              style: TextStyle(
                                  fontSize: 14.px,
                                  fontWeight: FontWeight.w600,
                                  color: const Color.fromRGBO(44, 44, 52, 1))),
                        ]),
                    SizedBox(height: 2.px),
                    SingleChildScrollView(
                        child: Column(
                            children: precinctList
                                .map((item) => PaymentItemWidget(
                                      precinctItem: item,
                                      isSelected: isCheckedAll,
                                      onSelectChanged: (isSelected) {
                                        // print("工程费用选中状态：$isSelected");
                                        item['isSelected'] = isSelected;
                                        handleItemSelectChanged(item);
                                      },
                                    ))
                                .toList())),
                  ],
                )),
            const Spacer(),
            Container(
              width: 375.px,
              height: 57.px,
              color: Colors.white,
              padding: EdgeInsets.only(left: 4.px, right: 12.px),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CupertinoCheckbox(
                      activeColor: _themeColor,
                      value: isCheckedAll,
                      shape: const StadiumBorder(),
                      onChanged: (s) {
                        handleCheckedAll(!isCheckedAll);
                      }),
                  Text(
                    "全选",
                    style: TextStyle(
                        fontSize: 12.px,
                        color: const Color.fromRGBO(128, 128, 129, 1.0)),
                  ),
                  const Spacer(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text("合计: ",
                          style: TextStyle(
                              fontSize: 12.px,
                              fontWeight: FontWeight.w500,
                              color: const Color.fromRGBO(51, 51, 51, 1))),
                      Text("¥\u2006",
                          style: TextStyle(
                              height: 5.5.px,
                              color: const Color.fromRGBO(221, 51, 27, 1),
                              fontSize: 12.px)),
                      Text(
                        totalPayAmount.toStringAsFixed(2),
                        style: TextStyle(
                            fontFamily: "bayon",
                            letterSpacing: 1,
                            color: const Color.fromRGBO(221, 51, 27, 1),
                            fontSize: 20.px),
                      )
                    ],
                  ),
                  SizedBox(
                    width: 10.px,
                  ),
                  ElevatedButton(
                      style: TextButton.styleFrom(
                        backgroundColor: _themeColor,
                        minimumSize: Size(125.px, 40.px),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(6.px)),
                      ),
                      onPressed: () {
                        // print("支付总金额：$totalPayAmount");
                        getPaymentBank();
                      },
                      child: Text(
                        "确认",
                        style: TextStyle(fontSize: 15.px, color: Colors.white),
                      ))
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget handleShowBankList() {
    return BottomSheet(
        onClosing: () {},
        builder: (BuildContext context) {
          return Container(
              padding: EdgeInsets.only(left: 12.px, right: 12.px),
              height: 560.px,
              width: 375.px,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: EdgeInsets.only(
                        top: 20.px,
                      ),
                      padding: EdgeInsets.only(left: 16.px, right: 16.px),
                      height: 43.px,
                      decoration: BoxDecoration(
                          color: _themeColor,
                          borderRadius: BorderRadius.circular(6.px)),
                      child: Row(children: [
                        Text("本次支付合计",
                            style: TextStyle(
                                color: Colors.white,
                                fontSize: 14.px,
                                fontWeight: FontWeight.w400)),
                        const Spacer(),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Text("¥\u2006",
                                style: TextStyle(
                                    height: 2.5.px,
                                    color: Colors.white,
                                    fontSize: 14.px)),
                            Text(
                              totalPayAmount.toStringAsFixed(2),
                              style: TextStyle(
                                  fontFamily: "bayon",
                                  letterSpacing: 1,
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 19.px),
                            )
                          ],
                        ),
                      ]),
                    ),
                    Expanded(
                        child: SingleChildScrollView(
                            child: Column(
                                children: showPaymentBankList
                                    .map((item) => Container(
                                        margin: EdgeInsets.only(top: 10.px),
                                        padding: EdgeInsets.only(
                                            left: 16.px, right: 16.px),
                                        decoration: BoxDecoration(
                                            color: const Color.fromRGBO(
                                                255, 255, 255, 1),
                                            borderRadius:
                                                BorderRadius.circular(6.px)),
                                        child: RadioListTile(
                                          activeColor: _themeColor,
                                          contentPadding: EdgeInsets.zero,
                                          dense: true,
                                          secondary: Image(
                                              width: 25.px,
                                              height: 25.px,
                                              image: AssetImage(
                                                  "assets/images/pay/bankLogo/${item['code']}.png")),
                                          title: Text(item['name'],
                                              style: TextStyle(
                                                  color: const Color.fromRGBO(
                                                      51, 51, 51, 1),
                                                  fontSize: 16.px,
                                                  fontWeight: FontWeight.w400)),
                                          subtitle: Text(
                                              getBankCardNum(
                                                  item['bankAccount'] ?? ""),
                                              style: TextStyle(
                                                  color: const Color.fromRGBO(
                                                      153, 153, 153, 1),
                                                  fontSize: 14.px)),
                                          value: item['code'],
                                          groupValue: _bankType,
                                          onChanged: (type) {
                                            (context as Element)
                                                .markNeedsBuild();
                                            _bankType = type;
                                          },
                                          controlAffinity:
                                              ListTileControlAffinity.trailing,
                                        )))
                                    .toList()))),
                    Container(
                      margin: EdgeInsets.only(bottom: 20.px),
                      width: 375.px,
                      child: ElevatedButton(
                          style: TextButton.styleFrom(
                            backgroundColor: _themeColor,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6.px)),
                          ),
                          onPressed: () {
                            if (_bankType == null) {
                              showToast("请选择支付账号");
                              return;
                            }
                            Navigator.of(context).pop();
                            showGeneralDialog(
                                context: context,
                                pageBuilder: (BuildContext context,
                                    Animation<double> animation,
                                    Animation<double> secondaryAnimation) {
                                  return showConfirmDialog(context);
                                });
                          },
                          child: Text(
                            "确认支付",
                            style:
                                TextStyle(fontSize: 15.px, color: Colors.white),
                          )),
                    )
                  ]));
        });
  }

  /*支付确认弹框*/
  Widget showConfirmDialog(context) {
    return Center(
      child: Container(
        height: 260.px,
        width: 280.px,
        padding: EdgeInsets.only(
            left: 30.px, right: 30.px, top: 32.px, bottom: 25.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(9.px),
        ),
        child: Column(children: [
          Image(
              width: 55.px,
              height: 55.px,
              image: const AssetImage("assets/images/pay/ic_pay_tip.png")),
          SizedBox(
            height: 20.px,
          ),
          Text(
            "提示",
            style: TextStyle(
                fontSize: 17.px,
                fontWeight: FontWeight.w500,
                decoration: TextDecoration.none,
                color: const Color.fromRGBO(0, 0, 0, 0.85)),
          ),
          SizedBox(
            height: 10.px,
          ),
          Text(
            "是否确定支付？",
            style: TextStyle(
                fontSize: 15.px,
                fontWeight: FontWeight.w400,
                decoration: TextDecoration.none,
                color: const Color.fromRGBO(0, 0, 0, 0.85)),
          ),
          SizedBox(
            height: 30.px,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 95.px,
                height: 40.px,
                child: ElevatedButton(
                    style: TextButton.styleFrom(
                      backgroundColor: const Color.fromRGBO(203, 204, 208, 1.0),
                      minimumSize: Size(125.px, 40.px),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.px)),
                    ),
                    onPressed: () {
                      Navigator.of(context).pop(false);
                    },
                    child: Text("取消",
                        style: TextStyle(
                            fontSize: 15.px,
                            color: Colors.white,
                            fontWeight: FontWeight.w500))),
              ),
              Container(
                width: 95.px,
                height: 40.px,
                margin: EdgeInsets.only(left: 10.px),
                child: ElevatedButton(
                    style: TextButton.styleFrom(
                      backgroundColor: _themeColor,
                      minimumSize: Size(95.px, 40.px),
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.px)),
                    ),
                    onPressed: () {
                      Navigator.of(context).pop(true);
                      showGeneralDialog(
                          context: context,
                          pageBuilder: (BuildContext context,
                              Animation<double> animation,
                              Animation<double> secondaryAnimation) {
                            return showPayInfoDialog(context);
                          });
                    },
                    child: Text("确定",
                        style: TextStyle(
                            fontSize: 15.px,
                            color: Colors.white,
                            fontWeight: FontWeight.w500))),
              )
            ],
          )
        ]),
      ),
    );
  }

  Widget showPayInfoDialog(context) {
    return Center(
      child: SizedBox(
        height: 567.px,
        width: 323.px,
        child: Column(
          children: [
            Stack(children: [
              Container(
                  margin: EdgeInsets.only(top: 40.px),
                  width: 323.px,
                  height: 527.px,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(18)),
                    image: DecorationImage(
                        image: AssetImage(
                            "assets/images/pay/bg_pay_info_dialog.png"),
                        fit: BoxFit.cover),
                  ),
                  child: Column(children: [
                    SizedBox(height: 112.px),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context)
                            .push(CupertinoPageRoute(builder: (ctx) {
                          return const SignatureNamePage();
                        })).then((result) async {
                          if (result != null) {
                            (context as Element).markNeedsBuild();

                            signatureImage = result;

                            var formData = FormData.fromMap({
                              "file": MultipartFile.fromBytes(
                                  signatureImage!.buffer.asUint8List(),
                                  filename:
                                      "signature_${DateTime.now().millisecondsSinceEpoch}.png",
                                  contentType: MediaType("image", "png")),
                            });
                            toUploadFile(formData, 1);
                          }
                        });
                      },
                      child: SizedBox(
                        width: 279.px,
                        height: 153.px,
                        child: signatureImage == null
                            ? Image(
                                width: 279.px,
                                height: 153.px,
                                image: const AssetImage(
                                    "assets/images/pay/pay_info_signature_name.png"))
                            : Image.memory(
                                signatureImage!,
                                width: 279.px,
                                height: 153.px,
                              ),
                      ),
                    ),
                    SizedBox(height: 14.px),
                    GestureDetector(
                      onTap: () async {
                        if (Platform.isAndroid) {
                          var status = await Permission.camera.status;
                          if (!status.isGranted) {
                            await Permission.camera
                                .request()
                                .then((PermissionStatus status) {
                              if (status.isGranted) {
                                Navigator.of(context)
                                    .push(CupertinoPageRoute(builder: (ctx) {
                                  return TakeFacePhotoPage();
                                })).then((result) async {
                                  if (result != null) {
                                    setState(() {
                                      facePhotoImagePath = result;
                                    });
                                    (context as Element).markNeedsBuild();
                                    var formData = FormData.fromMap({
                                      "file": await MultipartFile.fromFile(
                                          facePhotoImagePath,
                                          filename:
                                              "facePhoto_${DateTime.now().millisecondsSinceEpoch}.png",
                                          contentType:
                                              MediaType("image", "png")),
                                    });
                                    toUploadFile(formData, 2);
                                  }
                                });
                              }
                            });
                          } else {
                            Navigator.of(context)
                                .push(CupertinoPageRoute(builder: (ctx) {
                              return TakeFacePhotoPage();
                            })).then((result) async {
                              if (result != null) {
                                setState(() {
                                  facePhotoImagePath = result;
                                });
                                (context as Element).markNeedsBuild();
                                var formData = FormData.fromMap({
                                  "file": await MultipartFile.fromFile(
                                      facePhotoImagePath,
                                      filename:
                                          "facePhoto_${DateTime.now().millisecondsSinceEpoch}.png",
                                      contentType: MediaType("image", "png")),
                                });
                                toUploadFile(formData, 2);
                              }
                            });
                          }
                        } else {
                          Navigator.of(context)
                              .push(CupertinoPageRoute(builder: (ctx) {
                            return TakeFacePhotoPage();
                          })).then((result) async {
                            if (result != null) {
                              setState(() {
                                facePhotoImagePath = result;
                              });
                              (context as Element).markNeedsBuild();
                              var formData = FormData.fromMap({
                                "file": await MultipartFile.fromFile(
                                    facePhotoImagePath,
                                    filename:
                                        "facePhoto_${DateTime.now().millisecondsSinceEpoch}.png",
                                    contentType: MediaType("image", "png")),
                              });
                              toUploadFile(formData, 2);
                            }
                          });
                        }
                      },
                      child: Container(
                        width: 279.px,
                        height: 153.px,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(11.px),
                        ),
                        child: facePhotoImagePath == ""
                            ? Image(
                                width: 279.px,
                                height: 153.px,
                                image: const AssetImage(
                                    "assets/images/pay/pay_info_take_photo.png"))
                            : Image.file(
                                File(facePhotoImagePath),
                                fit: BoxFit.fill,
                                width: 279.px,
                                height: 153.px,
                              ),
                      ),
                    ),
                    SizedBox(height: 20.px),
                    ElevatedButton(
                      onPressed: facePhotoImagePath.isNotEmpty &&
                              signatureImage != null
                          ? () {
                              toChargePayment();
                            }
                          : null,
                      style: TextButton.styleFrom(
                        backgroundColor: facePhotoImagePath.isNotEmpty &&
                                signatureImage != null
                            ? _themeColor
                            : Colors.grey.shade400,
                        minimumSize: Size(279.px, 45.px),
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(25.px)),
                      ),
                      child: Text(
                        "下一步",
                        style: TextStyle(
                            fontSize: 16.px,
                            color: Colors.white,
                            fontWeight: FontWeight.w600),
                      ),
                    )
                  ])),
              Positioned(
                right: 18.5.px,
                child: Image(
                  image: const AssetImage(
                      "assets/images/pay/pay_info_top_tip.png"),
                  width: 106.px,
                  height: 109.px,
                ),
              ),
              Positioned(
                right: 0.px,
                top: 30.5.px,
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      facePhotoImagePath = "";
                      signatureImage = null;
                      signatureUrl = "";
                      facePhotoUrl = "";
                      _bankType = null;
                    });
                    Navigator.of(context).pop();
                  },
                  child: Image(
                    image: const AssetImage(
                        "assets/images/pay/pay_info_top_close.png"),
                    width: 20.px,
                    height: 20.px,
                  ),
                ),
              )
            ])
          ],
        ),
      ),
    );
  }

  void getUnPaymentInfoDetail(String? year, String orgNo) async {
    DahingOnlinePayResponsitory.queryOnLineUnpaySubjectDetail(
        {"organizationNo": orgNo, "yearNo": year}).then((res) {
      if (res.success!) {
        setState(() {
          paymentDetail = res.data;
          precinctList = paymentDetail['precinctList'];
          organizationNo = paymentDetail['organizationNo'];
        });
      }
    }, onError: (e) {});
  }

  @override
  void didUpdateWidget(covariant PaymentDetailPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (signatureImage != null || facePhotoImagePath != "") {
      setState(() {});
    }
  }

  void handleCheckedAll(bool value) {
    setState(() {
      isCheckedAll = value;
      for (var element in precinctList) {
        element['isSelected'] = value;
      }
      setState(() {
        totalPayAmount = 0;
        precinctList
            .where((item) => item['isSelected'] == true)
            .forEach((element) {
          totalPayAmount += element['unpayedAmount'];
        });
      });
    });
  }

  void handleItemSelectChanged(item) {
    isCheckedAll = precinctList.every((item) => item['isSelected'] == true);
    setState(() {
      totalPayAmount = 0;
      precinctList
          .where((item) => item['isSelected'] == true)
          .forEach((element) {
        totalPayAmount += element['unpayedAmount'];
      });
    });
  }

  void getBankList() {
    DahingOnlinePayResponsitory.queryBankList({}).then((res) {
      if (res.success!) {
        setState(() {
          bankList = res.data;
        });
      }
    });
  }

  void getPaymentBank() {
    var verificationData = [];
    precinctList.where((item) => item['isSelected'] == true).forEach((child) {
      List<dynamic> subjectResult = child['subjectResult'];
      for (var element in subjectResult) {
        verificationData.add({
          "organizationNo": element["organizationNo"],
          "organizationName": element['organizationName'],
          "chargeSubjectCode": element['chargeSubjectCode'],
          "chargeSubjectName": element['chargeSubjectName'],
          "bankAccountList": element['bankAccountList'],
          "yearNo": widget.year,
          "unpayedAmount": element['unpayedAmount'],
          "payItemType": element['payItemType'],
          "chargeCategoryType": element['chargeCategoryType'],
        });
      }
    });

    if (verificationData.isEmpty) {
      showToast("请选择信息");
      return;
    }
    DahingOnlinePayResponsitory.cardValidentOrg(jsonEncode(verificationData))
        .then((result) {
      if (result.success!) {
        setState(() {
          paymentBankDetail = result.data;
          List<dynamic> paymentItemResult = paymentBankDetail['result'];
          List<dynamic> bankAccountList =
              paymentItemResult.first['bankAccountList'];
          List<dynamic> bankNameList = paymentBankDetail['bankName'];
          showPaymentBankList = [];
          for (var element in bankNameList) {
            var bank = bankList
                .firstWhere((item) => item['code'] == element.toString());
            bank['bankAccount'] = bankAccountList.firstWhere(
                (item) => item['bankName'] == element)['bankAccount'];
            showPaymentBankList.add(bank);
          }
          showModalBottomSheet(
              context: context,
              elevation: 10,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8.px)),
              builder: (BuildContext context) {
                return handleShowBankList();
              });
        });
      } else {
        showToast(result.msg!);
      }
    }, onError: (e) {});
  }

  String getBankCardNum(String cardNum) {
    if (cardNum.length >= 16) {
      return cardNum.replaceRange(8, 16, "**** ****");
    } else if (cardNum.length > 8) {
      // 隐藏中间部分，只显示前4位和后4位
      String prefix = cardNum.substring(0, 4);
      String suffix = cardNum.substring(cardNum.length - 4);
      return "$prefix **** $suffix";
    } else {
      // 长度太短，直接返回原卡号
      return cardNum;
    }
  }

  void toUploadFile(FormData formData, int type) {
    DahingOnlinePayResponsitory.uploadFile(formData).then((result) {
      if (result.success!) {
        // print(result.data);
        setState(() {
          if (type == 1) {
            signatureUrl = result.data;
          } else if (type == 2) {
            facePhotoUrl = result.data;
          }
        });
      } else {
        showToast(result.msg!);
      }
    }, onError: (e) {});
  }

  void toChargePayment() {
    var bankInfo =
        showPaymentBankList.firstWhere((item) => item["code"] == _bankType);

    List<dynamic> paymentItemResult = paymentBankDetail['result'];
    for (var item in paymentItemResult) {
      item.remove('bankAccountList');
      item['photoPath'] = facePhotoUrl;
      item['signPath'] = signatureUrl;
      item['corporateBankCode'] = bankInfo['code'];
      item['corporateBankAccount'] = bankInfo['bankAccount'];
      item['chargeAmount'] = item['unpayedAmount'];
    }
    var param = {
      "corporateBankCode": _bankType,
      "farmerBankCode": _bankType,
      "chargeAmount": totalPayAmount,
      "yearNo": widget.year,
      "telephone": StorageUtil.userInfo()?.data?.telephone,
      "organizationNo": organizationNo,
      "payOrderDetailList": paymentItemResult
    };

    DahingOnlinePayResponsitory.chargePayment(param).then((result) {
      curPayData = result.data;
      if (result.success!) {
        Map<String, dynamic> params = {};
        if (_bankType == "3") {
          params['tokenId'] = result.data;
          NativeUtil.openCCBToPay(params);
        } else if (_bankType == "4") {
          params['tokenId'] = result.data;
          NativeUtil.openABCToPay(params);
        } else if (_bankType == "6") {
          params['params'] = jsonEncode(result.data);
          // 工商行业
          NativeUtil.openICBCToPay(params);
        }
      } else {
        showToast(result.msg!);
      }
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      if (curPayData == null) {
        return;
      }
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        count = count++;
        if (count > 100) {
          _timer?.cancel();
        }
        DahingOnlinePayResponsitory.getpayOrderState(
            {"onlinePayOrderId": (curPayData as Map)["orderId"]}).then((res) {
          if (res.data == '0' && isPaySuccess == false) {
            isPaySuccess = true;
            _timer?.cancel();
            bus.emit("returnfun");
            Navigator.of(context).popUntil((route) {
              return route.settings.name == RouteName.daHingOnlinePay;
            });
          }
        });
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _timer?.cancel();
    super.dispose();
  }
}
