import 'dart:io';

import 'package:bdh_smart_agric_app/components/bdh_network_image.dart';
import 'package:bdh_smart_agric_app/const/url_config_const.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/rice_user_home_result_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_segment_line.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/version/bdh_newversion_view.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/gps/gps_receiver.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/rice_price_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

class DaHingTabUserPage extends StatefulWidget {
  const DaHingTabUserPage({super.key});

  @override
  State<StatefulWidget> createState() => _TabUserPageState();
}

class _TabUserPageState extends State<DaHingTabUserPage>
    with AutomaticKeepAliveClientMixin {
  RiceUserHomeInfo? userHomeInfo;
  bool hasNewVersion = false;
  String currentCity = '';

  @override
  void initState() {
    super.initState();
    loadData();
    check();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    var mediaQuery = MediaQuery.of(context);
    return Scaffold(
      backgroundColor: const Color.fromRGBO(241, 245, 247, 1),
      body: Stack(
        children: [
          Image.asset(
              width: 375.px,
              fit: BoxFit.fitWidth,
              ImageHelper.wrapAssets("bg_dahing_tab_user.png")),
          SingleChildScrollView(
              child: Padding(
            padding: EdgeInsets.only(left: 20.px, right: 20.px),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 40.px + mediaQuery.padding.top,
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context)
                        .pushNamed('userinfoEdit', arguments: userHomeInfo!)
                        .then((res) {
                      loadData();
                      GlobalServiceView.needShowServiceBtn('user');
                    });
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            color: const Color.fromRGBO(255, 255, 255, 0.3),
                            borderRadius: BorderRadius.circular(36.px)),
                        height: 72.px,
                        width: 72.px,
                        child: Center(
                          child: ClipOval(
                            child: userHomeInfo == null
                                ? Image.asset(
                                    width: 70.px,
                                    height: 70.px,
                                    fit: BoxFit.fill,
                                    ImageHelper.wrapAssets("header.png"))
                                : BdhNetworkImage(
                                    fit: BoxFit.cover,
                                    url:
                                        "${urlConfig.microfront}${userHomeInfo?.pics ?? "/oss-huawei-obs/mobile_app/bdh_agri_farm_clothes/1863414911422431232.png"}",
                                    width: 70.px,
                                    height: 70.px,
                                    errorImage: "header.png",
                                  ),
                          ),
                        ),
                      ),
                      Icon(
                        Icons.arrow_forward_ios,
                        color: const Color.fromRGBO(0, 0, 0, 0.4),
                        size: 20.px,
                      )
                    ],
                  ),
                ),
                SizedBox(height: 16.px),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      userHomeInfo?.nickName ?? "默认用户",
                      style: TextStyle(
                          fontSize: 24.px, fontWeight: FontWeight.w500),
                    ),
                    if (userHomeInfo != null) ...[
                      SizedBox(width: 8.px),
                      getGenderView(),
                      SizedBox(width: 5.px),
                      getUserCertificationStatusView()
                    ]
                  ],
                ),
                SizedBox(height: 10.px),
                getUserBrief(),
                SizedBox(height: 10.px),
                if (userHomeInfo != null)
                  Row(
                    children: [
                      if (userHomeInfo?.orgName != null) ...[
                        getAreaBelongView(),
                        SizedBox(height: 10.px),
                      ],
                      if (userHomeInfo?.phone != null) ...[
                        getPhoneView(),
                      ],
                    ],
                  ),
                SizedBox(height: 24.px),
                Container(
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(8.px))),
                  child: Column(
                    children: [
                      UserMenuItemView(
                        backgroundColor: Colors.transparent,
                        item: UserMenuItem(
                            title: "作品与赞过",
                            icon: "red_heart.png",
                            onClick: () {
                              Navigator.of(context)
                                  .pushNamed(RouteName.worksStarsPage)
                                  .then((res) {
                                GlobalServiceView.needShowServiceBtn('user');
                              });
                            }),
                      ),
                      ClipRRect(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(8.px),
                            topRight: Radius.circular(8.px)),
                        child: UserMenuItemView(
                          backgroundColor: Colors.transparent,
                          item: UserMenuItem(
                              title: "银行卡",
                              icon: "credit_card.png",
                              onClick: () {
                                //根据 ConfigUrl.envType 配置决定是用 dahing 还是用默认银行卡接口
                                Navigator.of(context)
                                    .pushNamed(RouteName.bankList);
                              }),
                        ),
                      ),

                      UserMenuItemView(
                        backgroundColor: Colors.transparent,
                        item: UserMenuItem(
                          title: "消息通知",
                          icon: "chatMessageIcon.png",
                          onClick: () {
                            Navigator.of(context)
                                .pushNamed('newsMessageHome')
                                .then((res) {
                              GlobalServiceView.needShowServiceBtn('user');
                            });
                            // Navigator.of(context)
                            //     .push(CupertinoPageRoute(builder: (ctx) {
                            //   return const NewsMessageHome();
                            // }));
                          },
                        ),
                      ),
                      UserMenuItemView(
                        backgroundColor: Colors.transparent,
                        item: UserMenuItem(
                            title: "客服",
                            icon: "custom_service.png",
                            customWidget: Text(
                              "0451-52997800",
                              style: TextStyle(
                                  color: const Color.fromRGBO(22, 183, 96, 1),
                                  fontSize: 16.px,
                                  fontWeight: FontWeight.w400),
                            ),
                            onClick: () {
                              launchUrl(Uri.parse("tel:045152997800"));
                            }),
                      ),
                      // Container(
                      //     height: 5.px,
                      //     width: 375.px,
                      //     color: const Color.fromRGBO(226, 235, 231, 0.2)),
                      UserMenuItemView(
                        backgroundColor: Colors.transparent,
                        item: UserMenuItem(
                            title: "设置",
                            icon: "gear_blue.png",
                            customWidget: hasNewVersion
                                ? ClipOval(
                                    child: Container(
                                      height: 8.px,
                                      width: 8.px,
                                      color: Colors.red,
                                    ),
                                  )
                                : Container(),
                            onClick: () {
                              Navigator.of(context)
                                  .pushNamed('settingPage')
                                  .then((res) {
                                GlobalServiceView.needShowServiceBtn('user');
                              });
                              // Navigator.of(context)
                              //     .push(CupertinoPageRoute(builder: (ctx) {
                              //   return const SettingPage();
                              // }));
                            }),
                      ),
                      // UserMenuItemView(
                      //   item: UserMenuItem(
                      //       title: "wechat",
                      //       icon: "gear_blue.png",
                      //       customWidget: hasNewVersion
                      //           ? ClipOval(
                      //               child: Container(
                      //                 height: 8.px,
                      //                 width: 8.px,
                      //                 color: Colors.red,
                      //               ),
                      //             )
                      //           : Container(),
                      //       onClick: () {
                      //         Navigator.of(context)
                      //             .push(CupertinoPageRoute(builder: (ctx) {
                      //           return const UserAuthWechat();
                      //         }));
                      //       }),
                      // ),
                    ],
                  ),
                )
              ],
            ),
          ))
        ],
      ),
    );
  }

  Widget getAreaBelongView() {
    return Row(
      children: [
        Text(
          "所属:",
          style: TextStyle(
              fontSize: 13.px,
              fontWeight: FontWeight.w400,
              color: const Color.fromRGBO(81, 107, 97, 1)),
        ),
        SizedBox(width: 2.px),
        Text(
          (userHomeInfo?.orgName ?? '').contains('北大荒农垦集团')
              ? '北大荒集团'
              : (userHomeInfo?.orgName ?? '-'),
          style: TextStyle(
              fontSize: 13.px,
              fontWeight: FontWeight.w400,
              color: const Color.fromRGBO(81, 107, 97, 1)),
        ),
      ],
    );
  }

  Widget getGenderView() {
    return Container(
      height: 16.px,
      width: 16.px,
      alignment: Alignment.center,
      decoration: BoxDecoration(
          color: userHomeInfo?.sex == 1
              ? const Color.fromRGBO(47, 128, 237, 1)
              : Colors.red,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Center(
          child: userHomeInfo?.sex == 1
              ? Icon(
                  Icons.male,
                  size: 12.px,
                  color: Colors.white,
                )
              : Icon(
                  Icons.female,
                  size: 12.px,
                  color: Colors.white,
                )),
    );
  }

  Widget getLocationView() {
    return Text(
      // GpsReceiver.getInstance().locationResult?.addressCity ?? "",
      currentCity.isEmpty ? "" : currentCity,
      style: TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 13.px,
          color: const Color.fromRGBO(81, 107, 97, 1)),
    );
  }

  Widget getPhoneView() {
    return Row(
      children: [
        Text(
          "手机:",
          style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 13.px,
              color: const Color.fromRGBO(81, 107, 97, 1)),
        ),
        SizedBox(width: 2.px),
        Text(
          userHomeInfo?.phone ?? "-",
          style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 13.px,
              color: const Color.fromRGBO(81, 107, 97, 1)),
        )
      ],
    );
  }

  Widget getGatherIdView() {
    return Container(
      alignment: Alignment.center,
      padding:
          EdgeInsets.only(left: 10.px, right: 10.px, top: 2.px, bottom: 2.px),
      decoration: BoxDecoration(
          color: const Color.fromRGBO(226, 235, 231, 0.4),
          borderRadius: BorderRadius.all(Radius.circular(25.px))),
      height: 20.px,
      child: Row(
        children: [
          Image.asset(ImageHelper.wrapAssets("tag.png")),
          Text(
            userHomeInfo?.gatherId ?? "",
            style: TextStyle(
                fontSize: 11.px, color: const Color.fromRGBO(81, 107, 97, 1)),
          )
        ],
      ),
    );
  }

  Widget getUserBrief() {
    return Text(userHomeInfo?.brief ?? "这个人很懒，什么都没有写～",
        overflow: TextOverflow.ellipsis,
        maxLines: 1,
        style: TextStyle(
            fontSize: 13.px,
            fontWeight: FontWeight.w400,
            color: const Color.fromRGBO(81, 107, 97, 1)));
  }

  Widget getUserCertificationStatusView() {
    return Container(
      height: 20.px,
      alignment: Alignment.center,
      padding:
          EdgeInsets.only(left: 6.px, right: 6.px, top: 2.px, bottom: 2.px),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.px),
          gradient: LinearGradient(
              colors: (userHomeInfo?.certificationApprove == '') ||
                      (userHomeInfo?.certificationApprove == null)
                  ? [
                      const Color.fromRGBO(253, 213, 152, 0.2),
                      const Color.fromRGBO(239, 205, 141, 0.2)
                    ]
                  : [
                      const Color.fromRGBO(253, 213, 152, 1),
                      const Color.fromRGBO(239, 205, 141, 1)
                    ]),
          color: HexColor('#D78400')),
      child: Text(
        (userHomeInfo?.certificationApprove == '') ||
                (userHomeInfo?.certificationApprove == null)
            ? '未认证'
            : userHomeInfo?.certificationApprove == '0'
                ? '待审核'
                : userHomeInfo?.certificationApprove == '1'
                    ? '已认证'
                    : userHomeInfo?.certificationApprove == '2'
                        ? '驳回'
                        : '',
        textAlign: TextAlign.center,
        strutStyle: StrutStyle(fontSize: 10.px),
        style: TextStyle(
            fontSize: 10.px, color: const Color.fromRGBO(215, 132, 0, 1)),
      ),
    );
  }

  loadData() {
    if (StorageUtil.telephone() != null) {
      currentCity = GpsReceiver.getInstance().locationResult?.addressCity ?? "";
      PriceResponsitory.userHome({}).then((res) {
        if (!mounted) return;
        // PriceResponsitory.riceUserHome({}).then((res) {
        setState(() {
          userHomeInfo = res.data;
        });
      });
    }
  }

  check() {
    if (Platform.isAndroid) {
      GetCurrentInstallVersion.check(needShowDialog: false).then((res) {
        setState(() {
          hasNewVersion = res['haveNewVersion'];
          // versionResult = res['VersionResult'];
        });
      });
    }
    bus.on('UserChangeBottomTabbarAction', (res) {
      loadData();
    });
  }

  @override
  bool get wantKeepAlive => true;
}

class UserMenuItem {
  String title;
  String? icon;
  Widget? customWidget;
  Function onClick;
  UserMenuItem(
      {required this.title,
      this.icon,
      this.customWidget,
      required this.onClick});
}

class UserMenuItemView extends StatelessWidget {
  final UserMenuItem item;
  final Color? backgroundColor;
  const UserMenuItemView({super.key, required this.item, this.backgroundColor});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        item.onClick();
      },
      child: Container(
        padding: EdgeInsets.only(left: 15.px, right: 15.px),
        color: backgroundColor ?? Colors.white,
        // width: 345.px,
        width: 375.px,
        child: Column(
          children: [
            SizedBox(
              height: 51.px,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      item.icon != null
                          ? Image.asset(
                              width: 24.px,
                              height: 24.px,
                              ImageHelper.wrapAssets(item.icon!))
                          : Container(),
                      SizedBox(
                        width: 10.px,
                      ),
                      Text(
                        item.title,
                        style: TextStyle(
                            fontWeight: FontWeight.w400, fontSize: 16.px),
                      )
                    ],
                  ),
                  Row(
                    children: [
                      item.customWidget ?? Container(),
                      Container(
                        margin: EdgeInsets.only(left: 5.px),
                        child: Image.asset(
                            width: 24.px,
                            height: 24.px,
                            ImageHelper.wrapAssets("arrow_right.png")),
                      )
                    ],
                  )
                ],
              ),
            ),
            BdhSegmentLine(
              width: 345.px,
              opacity: 0.4,
            )
          ],
        ),
      ),
    );
  }
}
