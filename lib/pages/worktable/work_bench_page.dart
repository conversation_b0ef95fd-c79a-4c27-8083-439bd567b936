import 'package:bdh_smart_agric_app/components/bdh_network_image.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/main_layout_Model_bdh_digital.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page_inappweb.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:external_app_launcher/external_app_launcher.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fluwx/fluwx.dart';
import 'package:url_launcher/url_launcher.dart';

class WorkBenchPage extends StatefulWidget {
  const WorkBenchPage({super.key});

  @override
  State<WorkBenchPage> createState() => _WorkBenchPageState();
}

class _WorkBenchPageState extends State<WorkBenchPage>
    with AutomaticKeepAliveClientMixin {
  List<MenuConfigItem> bottomGroups = [];
  bool showLoading = false;
  String token = '';

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    getData();
    // getMenuTreeData();
    // getMainLayoutData();
    token = StorageUtil.userInfo()?.data?.token ?? '';
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      // backgroundColor: HexColor("#F3F5F9"),
      backgroundColor: Colors.transparent,
      body: showLoading
          ? const ViewStateBusyWidget()
          : SingleChildScrollView(
              child: SizedBox(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    ...bottomGroups.map((group) {
                      return getGroupView(group);
                    })
                  ],
                ),
              ),
            ),
    );
  }

  Widget getGroupView(MenuConfigItem group) {
    return Center(
      child: Container(
        width: 361.px,
        decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(8.px))),
        margin: EdgeInsets.only(bottom: 8.px),
        padding: EdgeInsets.only(
            top: 15.px, left: 10.px, right: 10.px, bottom: 15.px),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              width: 365.px,
              margin: EdgeInsets.only(bottom: 20.px, left: 5.px, right: 5.px),
              child: Text(
                group.authName ?? "",
                style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w700,
                    color: const Color.fromRGBO(0, 0, 0, 1)),
              ),
            ),
            SizedBox(
              width: 341.px,
              child: Wrap(
                runAlignment: WrapAlignment.center,
                alignment: WrapAlignment.start,
                spacing: 0.px,
                runSpacing: 20.px,
                children: [
                  ...(group.children ?? []).map((e) {
                    return MenuItemBdhDigitalView(
                      item: e,
                      isGroup: true,
                      token: token,
                    );
                  })
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  getData() {
    setState(() {
      showLoading = true;
    });
    Future.wait([
      BDHResponsitory.getSubMenuConfig("digitizationService"),
      BdhDigitalService.getMainLayoutBdhDigital()
    ]).then((list) {
      MenuConfigResult res0 = list[0] as MenuConfigResult;
      MainLayoutModelBdhDigital res = list[1] as MainLayoutModelBdhDigital;

      ///---处理res0 Data----
      if ((res0.data ?? []).isNotEmpty) {
        bottomGroups.addAll(res0.data?.first.children ?? []);
        // sortItem();
      }

      ///---处理res Data----
      List<Categor> categoryList = [];
      MenuConfigItem xtItem = MenuConfigItem(authName: '协同办公');
      List<MenuConfigItem> xtMenuConfigItemList = [];
      for (int i = 0; i < res.data.categories.length; i++) {
        Categor item = res.data.categories[i];
        if (item.group == '协同办公' &&
            (!(item.name ?? '').contains('_pc')) &&
            (!(item.name ?? '').contains('已办')) &&
            (!(item.name ?? '').contains('已发'))) {
          categoryList.add(item);
          MenuConfigItem tempItem = MenuConfigItem();
          tempItem.authName = item.name ?? '';
          tempItem.path = item.path;
          Map dataMap = getXTBGIconName(item);
          String localIconName = dataMap['iconName'];
          String authCode = dataMap['authCode'];
          tempItem.isNetIcon = false;
          tempItem.authCode = authCode;
          tempItem.icon = localIconName;
          tempItem.url = '';
          xtMenuConfigItemList.add(tempItem);
        }
      }
      xtItem.children = xtMenuConfigItemList;
      bottomGroups.add(xtItem);

      /// 排序-- sortItem();
      List<MenuConfigItem> bottomGroupsTemp = bottomGroups.where((item) {
        return item.authName != '协同办公';
      }).toList();
      if (bottomGroupsTemp.isNotEmpty) {
        try {
          bottomGroupsTemp.insert(
              0, bottomGroups.firstWhere((item) => item.authName == '协同办公'));
          bottomGroups.clear();
          setState(() {
            bottomGroups = bottomGroupsTemp;
          });
        } catch (e) {}
      }
      setState(() {
        showLoading = false;
      });
    });
  }

//获取 '协同办公' 本地icon name
  Map<String, String> getXTBGIconName(Categor item) {
    Map<String, String> dataMap = {'iconName': 'amKeep', 'authCode': 'other'};
    switch (item.name) {
      case '考勤记录':
        dataMap = {'iconName': 'amKeep', 'authCode': 'attendance'};
      case '会议管理':
        dataMap = {'iconName': 'xtbg01', 'authCode': 'meeting'};
        break;
      case 'i北大荒':
        dataMap = {'iconName': 'xtbg02', 'authCode': 'dingding'};
        break;
      case '报表中心':
        dataMap = {'iconName': 'xtbg03', 'authCode': 'report'};
        break;
      case '文档中心':
        dataMap = {'iconName': 'xtbg04', 'authCode': 'doc'};
        break;
      case '公文管理':
        dataMap = {'iconName': 'xtbg05', 'authCode': 'office'};
        break;
      case '协同管理':
        dataMap = {'iconName': 'xtbg06', 'authCode': 'oa'};
        break;
      case '全文搜索':
        dataMap = {'iconName': 'xtbg07', 'authCode': 'search'};
        break;
      case '新闻':
        dataMap = {'iconName': 'xtbg08', 'authCode': 'news'};
        break;
      case '公告':
        dataMap = {'iconName': 'xtbg09', 'authCode': 'announcement'};
        break;
      case '督办':
        dataMap = {'iconName': 'xtbg10', 'authCode': 'supervise'};
        break;
      case '待办':
        dataMap = {'iconName': 'amKeep', 'authCode': 'todo'};
        break;
      case '签到':
        dataMap = {'iconName': 'amKeep', 'authCode': 'userSign'};
        break;
      default:
        dataMap = {'iconName': 'amKeep', 'authCode': 'other'};
    }
    return dataMap;
  }

  /// --保留--
/*
  getMenuTreeData() {
    BDHResponsitory.getSubMenuConfig("digitizationService").then((res) {
      Log.i('res=$res');
      if ((res.data ?? []).isNotEmpty) {
        setState(() {
          showLoading = false;
        });
        bottomGroups.addAll(res.data?.first.children ?? []);
        sortItem();
      }
    });
  }

//获取 '协同办公'
  getMainLayoutData() {
    BdhDigitalService.getMainLayoutBdhDigital().then((res) {
      List<Categor> categoryList = [];
      MenuConfigItem xtItem = MenuConfigItem(authName: '协同办公');
      List<MenuConfigItem> xtMenuConfigItemList = [];
      for (int i = 0; i < res.data.categories.length; i++) {
        Categor item = res.data.categories[i];
        if (item.group == '协同办公' &&
            (!(item.name ?? '').contains('_pc')) &&
            (!(item.name ?? '').contains('已办')) &&
            (!(item.name ?? '').contains('已发'))) {
          categoryList.add(item);
          MenuConfigItem tempItem = MenuConfigItem();
          tempItem.authName = item.name ?? '';
          tempItem.path = item.path;
          // if ((item.cuIcon ?? '').startsWith('https')) {
          //   tempItem.isNetIcon = true;
          //   tempItem.icon = item.cuIcon ?? '';
          // } else {
          //   String iconName = '';
          //   tempItem.isNetIcon = false;
          //   if ((item.cuIcon ?? '').contains('/static/index')) {
          //     List<String> parts = (item.cuIcon ?? '').split('/');
          //     for (int i = 0; i < parts.length; i++) {
          //       String itmsStr = parts[i];
          //       if (itmsStr.contains('.png')) {
          //         iconName = itmsStr.split('.')[0];
          //         break;
          //       }
          //     }
          //   } else {
          //     iconName = tempItem.icon = item.cuIcon ?? 'amKeep';
          //   }
          //   tempItem.icon = iconName;
          // }
          Map dataMap = getXTBGIconName(item);
          // String localIconName = getXTBGIconName(item);
          String localIconName = dataMap['iconName'];
          String authCode = dataMap['authCode'];
          tempItem.isNetIcon = false;
          tempItem.authCode = authCode;
          tempItem.icon = localIconName;
          tempItem.url = '';
          xtMenuConfigItemList.add(tempItem);
        }
      }
      xtItem.children = xtMenuConfigItemList;
      showLoading = false;
      bottomGroups.add(xtItem);
      sortItem();
    });
  }

  // sortItem() {
    List<MenuConfigItem> bottomGroupsTemp = bottomGroups.where((item) {
      return item.authName != '协同办公';
    }).toList();
    if (bottomGroupsTemp.isNotEmpty) {
      try {
        bottomGroupsTemp.insert(
            0, bottomGroups.firstWhere((item) => item.authName == '协同办公'));
        bottomGroups.clear();
        setState(() {
          // showLoading = false;
          bottomGroups = bottomGroupsTemp;
        });
      } catch (e) {}
    }
  }
  */
}

class MenuItemBdhDigitalView extends StatelessWidget {
  final bool? isGroup;
  final String? token;
  final MenuConfigItem item;

  const MenuItemBdhDigitalView(
      {super.key, required this.item, this.isGroup, this.token});

  Future<bool> isWeChatInstalled() async {
    const wechatUrl = 'com.alibaba.android.rimet.chinabdh://';
    return await canLaunchUrl(Uri.parse(wechatUrl));
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        BDHResponsitory.saveMenuUse({
          "menuCode": item.authCode,
          "menuName": item.authName,
          "menuType": "2", //所有服务类型
        }).then((res) {
          Log.i(res.msg);
        });

        if (item.url!.startsWith("gh_")) {
          Fluwx fluwx = Fluwx();
          fluwx.open(target: MiniProgram(username: item.url!));
        } else if (item.url!.startsWith("pages") ||
            item.url!.startsWith("/pages")) {
          NativeUtil.openUni({"path": item.url});
        } else if (item.url!.startsWith("http")) {
          String url = item.url ?? '';

          if (item.authName == "数字人力系统") {
            url += '&cop-token=$token&ismobile=true';
          }

          Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
            // return HtmlPage(title: item.authName ?? "", content: url);
            return HtmlPageInappweb(content: url, title: item.authName ?? "");
          }));
        } else if (item.path != null) {
          // if (item.authName == '协同管理') {
          //   Navigator.of(context).push(CupertinoPageRoute(builder: (context) {
          //     return HtmlPageInappweb(
          //         content: item.path!, title: item.authName ?? "");
          //   }));
          // }

          if (item.authName == 'i北大荒') {
            const pakageName = 'com.alibaba.android.rimet.chinabdh';
            // const pakageSheme = 'dd.work.exclusive4chinabdh://';
            const pakageSheme = 'dingtalk://';
            // const pakageSheme =
            //     'dingtalk://applink.dingtalk.com/pages/example/components';
            // const pakageSheme = 'weixin://';

            final isInstalled = await LaunchApp.isAppInstalled(
              androidPackageName: pakageName,
              iosUrlScheme: pakageSheme,
            );
            // Logger().i('res== $isInstalled');
            if (isInstalled) {
              //打开
              final openRes = await LaunchApp.openApp(
                  androidPackageName: pakageName,
                  iosUrlScheme: pakageSheme,
                  appStoreLink: null,
                  openStore: false);
              // Log.i('openRes== $openRes');
            } else {
              launchUrl(Uri.parse(
                  'https://ysfljq.bdhic.com/adm/download/?tenantId=bit'));
            }
          } else if (item.path!.startsWith("pages") ||
              item.path!.startsWith("/pages")) {
            NativeUtil.openUni({"path": item.path});
          } else if (item.path!.startsWith("http")) {
            Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
              // return HtmlPage(title: item.authName ?? "", content: item.path!);
              return HtmlPageInappweb(
                  content: item.path!, title: item.authName ?? "");
            }));
          }
        } else if (item.authCode == 'digRank') {
          Navigator.of(context).pushNamed(RouteName.rankBdhDigitalPage);
        } else {
          Navigator.of(context)
              .pushNamed(item.url ?? "无效路径", arguments: item)
              .then((res) {});
        }
      },
      child: isGroup == true
          ? Container(
              // decoration: BoxDecoration(border: Border.all(width: 1)),
              width: 68.px,
              // height: 83.px,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  (item.isNetIcon ?? false)
                      ? BdhNetworkImage(
                          fit: BoxFit.cover,
                          url: item.icon ?? '',
                          width: 44.px,
                          height: 44.px,
                          errorImage: "amKeep.png",
                        )
                      : Image.asset(
                          width: 44.px,
                          height: 44.px,
                          item.icon == null || item.icon == 'amkeep'
                              ? ImageHelper.wrapServiceApplicationAssets(
                                  "amKeep.png")
                              : ImageHelper.wrapServiceApplicationAssets(
                                  "${item.icon!}.png"),
                        ),
                  SizedBox(height: 5.px),
                  SizedBox(
                    width: 50.px,
                    child: Text(
                      item.authName ?? '',
                      // (item.authName ?? '').length > 6
                      //     ? (item.authName ?? '').substring(0, 6)
                      //     : (item.authName ?? ''),
                      maxLines: 2,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400),
                    ),
                  )
                ],
              ),
            )
          : Container(
              width: 68.px,
              // height: 90.px,
              color: Colors.transparent,
              child: Column(
                children: [
                  (item.isNetIcon ?? false)
                      ? BdhNetworkImage(
                          fit: BoxFit.cover,
                          url: item.icon ?? '',
                          width: 44.px,
                          height: 44.px,
                          errorImage: "amKeep.png",
                        )
                      : Image.asset(
                          width: 44.px,
                          height: 44.px,
                          item.icon == null || item.icon == 'amkeep'
                              ? ImageHelper.wrapServiceApplicationAssets(
                                  "amKeep.png")
                              : ImageHelper.wrapServiceApplicationAssets(
                                  "${item.icon!}.png"),
                        ),
                  SizedBox(height: 5.px),
                  SizedBox(
                    width: 50.px,
                    child: Text(
                      item.authName ?? '',
                      // (item.authName ?? '').length > 6
                      //     ? (item.authName ?? '').substring(0, 6)
                      //     : (item.authName ?? ''),
                      maxLines: 2,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: Colors.black,
                          fontSize: 15.px,
                          fontWeight: FontWeight.w400),
                    ),
                  )
                ],
              ),
            ),
    );
  }
}
