import 'dart:async';
import 'dart:convert';

import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:oktoast/oktoast.dart';

// ignore: must_be_immutable
class SacnPageBdhDigital extends StatefulWidget {
  MobileScannerController controller;
  SacnPageBdhDigital({super.key, required this.controller});

  @override
  State<SacnPageBdhDigital> createState() => _SacnPageBdhDigitaleState();
}

class _SacnPageBdhDigitaleState extends State<SacnPageBdhDigital>
    with WidgetsBindingObserver {
  Barcode? _barcode;
  // MobileScannerController? controller; //暂时保留
  bool useScanWindow = !kIsWeb;
  bool autoZoom = false;
  bool invertImage = false;
  bool returnImage = false;

  Size desiredCameraResolution = const Size(1920, 1080);
  DetectionSpeed detectionSpeed = DetectionSpeed.unrestricted;
  int detectionTimeoutMs = 1000;

  bool useBarcodeOverlay = true;
  BoxFit boxFit = BoxFit.contain;
  bool enableLifecycle = false;

  /// Hides the MobileScanner widget while the MobileScannerController is
  /// rebuilding
  bool hideMobileScannerWidget = false;

  List<BarcodeFormat> selectedFormats = const [
    BarcodeFormat.qrCode,
    BarcodeFormat.codebar,
  ];
  StreamSubscription<Object?>? _subscription;

  Future<void> start() async {
    // try { //暂时保留
    //   await Future<void>.delayed(
    //       const Duration(seconds: 1)); // comment then it works
    //   controller = MobileScannerController(
    //     autoStart: false,
    //     detectionSpeed: DetectionSpeed.normal,
    //     facing: CameraFacing.back,
    //     cameraResolution: const Size(1920, 2560),
    //   );

    //   if (mounted) setState(() {});
    //   // Wait for the UI to settle
    //   await Future<void>.delayed(const Duration(milliseconds: 100));

    //   // Start controller
    //   await controller?.start();
    // } on Exception catch (err, stack) {
    //   debugPrintStack(stackTrace: stack, label: err.toString());
    // }

    try {
      if (mounted) setState(() {});
      // Wait for the UI to settle
      await Future<void>.delayed(const Duration(milliseconds: 100));
      // Start controller
      await widget.controller.start();
    } on Exception catch (err, stack) {
      debugPrintStack(stackTrace: stack, label: err.toString());
    }
  }

  @override
  void initState() {
    start();
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    // _subscription = controller!.barcodes.listen(_handleBarcode);
    // unawaited(controller!.start());
  }

  @override
  Future<void> dispose() async {
    WidgetsBinding.instance.removeObserver(this);
    unawaited(_subscription?.cancel());
    _subscription = null;
    super.dispose();
    await widget.controller.dispose();
    // widget.controller = null;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // if (widget.controller.value.hasCameraPermission) {
    //   return;
    // }
    Logger().i('扫码结果didChangeAppLifecycleState>>>>>: $state');
    switch (state) {
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
      case AppLifecycleState.paused:
        return;
      case AppLifecycleState.resumed:
        _subscription = widget.controller.barcodes.listen(_handleBarcode);
        unawaited(widget.controller.start());
      case AppLifecycleState.inactive:
        unawaited(_subscription?.cancel());
        _subscription = null;
        unawaited(widget.controller.stop());
    }
  }

  Widget _barcodePreview(Barcode? value) {
    if (value == null) {
      return const Text(
        'scan',
        overflow: TextOverflow.fade,
        style: TextStyle(color: Colors.white),
      );
    }

    return Text(
      value.displayValue ?? 'No display value.',
      overflow: TextOverflow.fade,
      style: const TextStyle(color: Colors.white),
    );
  }

  void _handleBarcode(BarcodeCapture barcodes) {
    setState(() {
      _barcode = barcodes.barcodes.firstOrNull;
    });
    if (_barcode != null) {
      String ticket = _barcode!.rawValue ?? '';
      try {
        Map ticketMap = jsonDecode(ticket);
        if (ticketMap.containsKey('ticket')) {
          String ticketStr = ticketMap['ticket'];
          // {"ticket":"b15e93a396734c67951749a57f7b6673"}
          unawaited(widget.controller.stop());
          Navigator.pushReplacementNamed(
              context, RouteName.scanLoginConfirmPage,
              arguments: ticketStr);
        } else {
          // unawaited(controller!.stop());
          // showToast('扫码返回参数异常');
          errBackAction();
        }
      } catch (err) {
        errBackAction();
      }
    }
    // Logger().i('扫码结果: $barcodes');
  }

  void errBackAction() {
    unawaited(widget.controller.stop());
    showToast('扫码返回参数异常');
    Future.delayed(const Duration(seconds: 1), () {
      Navigator.pop(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('扫码')),
      backgroundColor: Colors.black,
      body: Builder(builder: (BuildContext context) {
        if (widget.controller == null) return const SizedBox.expand();
        return ValueListenableBuilder<MobileScannerState>(
          valueListenable: widget.controller,
          builder: (BuildContext context, MobileScannerState state, _) {
            return Stack(
              children: [
                MobileScanner(
                  fit: BoxFit.cover,
                  controller: widget.controller,
                  onDetect: _handleBarcode,
                  // onDetectError: (Object object, StackTrace trace) {
                  //   Logger().i('扫码结果err: $object======$trace');
                  //   //Waiting for the barcode module to be downloaded. Please wait.
                  // },
                ),
                const QrCodeScanAnimation(),
                // Align(
                //   alignment: Alignment.bottomCenter,
                //   child: Container(
                //     alignment: Alignment.bottomCenter,
                //     height: 100,
                //     color: const Color.fromRGBO(0, 0, 0, 0.4),
                //     child: Row(
                //       mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                //       children: [
                //         Expanded(
                //             child: Center(child: _barcodePreview(_barcode))),
                //       ],
                //     ),
                //   ),
                // ),
              ],
            );
          },
        );
      }),
    );
  }
}

class QrCodeScanAnimation extends StatefulWidget {
  const QrCodeScanAnimation({super.key});
  @override
  // ignore: library_private_types_in_public_api
  _QrCodeScanAnimation createState() => _QrCodeScanAnimation();
}

class _QrCodeScanAnimation extends State<QrCodeScanAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: CustomPaint(
        painter: ScannerPainter(_controller),
        size: Size(250, 250),
      ),
    );
  }
}

class ScannerPainter extends CustomPainter {
  final Animation<double> animation;

  ScannerPainter(this.animation) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    final paintColor = Colors.blue;
    final borderPaint = Paint()
      ..color = paintColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3;

    // 绘制四角边框
    final cornerLength = 25.0;

    // 左上角
    canvas.drawLine(Offset.zero, Offset(cornerLength, 0), borderPaint);
    canvas.drawLine(Offset.zero, Offset(0, cornerLength), borderPaint);

    // 右上角
    canvas.drawLine(
        Offset(width, 0), Offset(width - cornerLength, 0), borderPaint);
    canvas.drawLine(Offset(width, 0), Offset(width, cornerLength), borderPaint);

    // 左下角
    canvas.drawLine(
        Offset(0, height), Offset(cornerLength, height), borderPaint);
    canvas.drawLine(
        Offset(0, height), Offset(0, height - cornerLength), borderPaint);

    // 右下角
    canvas.drawLine(Offset(width, height), Offset(width - cornerLength, height),
        borderPaint);
    canvas.drawLine(Offset(width, height), Offset(width, height - cornerLength),
        borderPaint);

    // 绘制扫描线
    final linePaint = Paint()
      ..shader = LinearGradient(
        colors: [Colors.transparent, paintColor, Colors.transparent],
        stops: [0.1, 0.5, 0.9],
      ).createShader(Rect.fromLTWH(0, 0, width, 3))
      ..strokeWidth = 3;

    final lineY = height * animation.value;
    canvas.drawLine(
      Offset(0, lineY),
      Offset(width, lineY),
      linePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
