import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class ScanLoginConfirmPage extends StatefulWidget {
  String ticket;
  ScanLoginConfirmPage({super.key, required this.ticket});

  @override
  State<ScanLoginConfirmPage> createState() => _ScanLoginConfirmPageState();
}

class _ScanLoginConfirmPageState extends State<ScanLoginConfirmPage> {
  @override
  void initState() {
    super.initState();
    scanSuccese();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('确认登录')),
      body: Column(
        children: [
          Container(
            alignment: Alignment.center,
            margin: EdgeInsets.only(top: 100.px),
            child: Image.asset(
                height: 100.px,
                width: 100.px,
                ImageHelper.wrapAssets('computerBDHDigitalImg.png')),
          ),
          SizedBox(height: 15.px),
          Text(
            '登录数字北大荒平台',
            style: TextStyle(
                fontSize: 15.px,
                fontWeight: FontWeight.w400,
                color: const Color.fromRGBO(0, 0, 0, 1)),
          ),
          Container(
            padding: EdgeInsets.only(left: 30.px, right: 30.px, top: 20.px),
            child: Text(
              '请确认该设备在您身边并且是您本人登录，不是本人登录可能会导致您的账号被盗，还可能引发职务问题，需承担相应责任。确保安全，谨慎验证后登录。',
              maxLines: 5,
              softWrap: true,
              style: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 0, 0, 0.5)),
            ),
          ),

          SizedBox(height: 150.px),
          //确认
          GestureDetector(
            onTap: () {
              scanLogin();
            },
            child: BDHUserLoginButton(
              width: 140.px,
              height: 40.px,
              title: '确认',
              borderRadius: 2.px,
              marginBottom: 16.px,
              bgLinearGradientColors: const [
                Color.fromRGBO(0, 127, 255, 1),
                Color.fromRGBO(61, 156, 255, 1)
              ],
              marginTop: 20.px,
            ),
          ),
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              margin: EdgeInsets.only(top: 5.px),
              width: 140.px,
              height: 40.px,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  // color: color ?? const Color.fromRGBO(22, 183, 96, 1),
                  borderRadius: BorderRadius.all(Radius.circular(2.px))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '拒绝',
                    style: TextStyle(
                        fontFamily: "PingFang SC",
                        color: Colors.black,
                        fontWeight: FontWeight.w500,
                        fontSize: 16.px),
                  )
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  scanSuccese() {
    BdhDigitalService.scanSuccessBdhDigital({'ticket': widget.ticket})
        .then((res) {
      showToast('扫码成功');
    });
  }

  scanLogin() {
    TDToast.showLoadingWithoutText(context: context, preventTap: true);
    BdhDigitalService.scanLoginSuccessBdhDigital({'ticket': widget.ticket})
        .then((res) {
      TDToast.dismissLoading();
      showToast('登录成功');
      Navigator.pop(context);
    });
  }
}
