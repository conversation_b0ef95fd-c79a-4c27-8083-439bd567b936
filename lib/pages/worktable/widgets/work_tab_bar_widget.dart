import 'dart:ffi';

import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class WorkTabBarWidget extends StatefulWidget {
  final List<String> tabs;

  final List<bool> hasUnread;

  final ValueChanged<int?>? onSelectChanged;

  final int? selectedIndex;

  const WorkTabBarWidget(
      {super.key,
      required this.tabs,
      required this.hasUnread,
      this.onSelectChanged,
      this.selectedIndex});

  @override
  State<WorkTabBarWidget> createState() => _WorkTabBarWidgetState();
}

class _WorkTabBarWidgetState extends State<WorkTabBarWidget> {
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    currentIndex = widget.selectedIndex ?? 0;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 36.h,
      width: 200.px,
      child: ListView.builder(
        padding: EdgeInsets.only(left: 8.w),
        scrollDirection: Axis.horizontal,
        itemCount: widget.tabs.length,
        itemBuilder: (context, index) => buildTabs(index, widget.tabs[index],
            widget.hasUnread[index], currentIndex == index),
      ),
    );
  }

  buildTabs(int index, String tab, bool hasUnread, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          widget.onSelectChanged!(index);
          currentIndex = index;
        });
      },
      child: SizedBox(
        width: 76.w,
        height: 36.h,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Text(
              tab,
              style: TextStyle(
                  fontSize: isSelected ? 22.px : 18.px,
                  color: isSelected
                      ? const Color.fromRGBO(0, 0, 0, 1)
                      : const Color.fromRGBO(0, 0, 1, 0.4)),
            ),
            Positioned(
              top: 4,
              right: 4,
              child: Image.asset(
                width: 10.px,
                height: 10.px,
                ImageHelper.wrapAssets((currentIndex == index)
                    ? 'isSelectedWrokTable.png'
                    : 'isUnSelectedWrokTable.png'),
              ),
            )
            // hasUnread
            //     ? Positioned(
            //         top: 4,
            //         right: 4,
            //         child: Container(
            //           width: 6.px,
            //           height: 6.px,
            //           decoration: BoxDecoration(
            //             borderRadius: BorderRadius.circular(4.px),
            //             color: Colors.red,
            //           ),
            //         ))
            //     : Container(),
          ],
        ),
      ),
    );
  }

  @override
  void didUpdateWidget(covariant WorkTabBarWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedIndex != widget.selectedIndex ||
        oldWidget.hasUnread != widget.hasUnread ||
        oldWidget.tabs != widget.tabs) {
      setState(() {
        currentIndex = widget.selectedIndex ?? 0;
      });
    }
  }
}
