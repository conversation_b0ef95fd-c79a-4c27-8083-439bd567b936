import 'package:flutter/material.dart';

enum BubblePosition { top, bottom, left, right }

class BubbleBorder extends ShapeBorder {
  final double borderRadius;
  final double arrowWidth;
  final double arrowHeight;
  final BubblePosition position;
  final Color color;

  const BubbleBorder({
    this.borderRadius = 8,
    this.arrowWidth = 16,
    this.arrowHeight = 10,
    this.position = BubblePosition.top,
    this.color = Colors.transparent,
  });

  @override
  EdgeInsetsGeometry get dimensions {
    switch (position) {
      case BubblePosition.top:
        return EdgeInsets.only(top: arrowHeight);
      case BubblePosition.bottom:
        return EdgeInsets.only(bottom: arrowHeight);
      case BubblePosition.left:
        return EdgeInsets.only(left: arrowHeight);
      case BubblePosition.right:
        return EdgeInsets.only(right: arrowHeight);
    }
  }

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) => Path();

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    final path = Path();
    final adjustedRect = rect.deflate(arrowHeight);

    switch (position) {
      case BubblePosition.top:
        _drawTopBubble(path, adjustedRect);
      case BubblePosition.bottom:
        _drawBottomBubble(path, adjustedRect);
      // case BubblePosition.left:
      //   _drawLeftBubble(path, adjustedRect);
      // case BubblePosition.right:
      //   _drawRightBubble(path, adjustedRect);
      case BubblePosition.left:
      case BubblePosition.right:
    }

    return path;
  }

  void _drawTopBubble(Path path, Rect rect) {
    final arrowCenterX = rect.width - arrowWidth / 2;
    path
      ..moveTo(arrowCenterX - arrowWidth / 2, rect.top + arrowHeight)
      ..lineTo(arrowCenterX, rect.top)
      ..lineTo(arrowCenterX + arrowWidth / 2, rect.top + arrowHeight)
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTRB(
          rect.left,
          rect.top + arrowHeight,
          rect.right,
          rect.bottom,
        ),
        Radius.circular(borderRadius),
      ));
  }

  void _drawBottomBubble(Path path, Rect rect) {
    final arrowCenterX = rect.width / 2;
    path
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTRB(
          rect.left,
          rect.top,
          rect.right,
          rect.bottom - arrowHeight,
        ),
        Radius.circular(borderRadius),
      ))
      ..moveTo(arrowCenterX - arrowWidth / 2, rect.bottom - arrowHeight)
      ..lineTo(arrowCenterX, rect.bottom)
      ..lineTo(arrowCenterX + arrowWidth / 2, rect.bottom - arrowHeight);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawPath(getOuterPath(rect), paint);
  }

  @override
  ShapeBorder scale(double t) => this;
}
