class AppCenterMenuModel {
  bool? success;
  int? code;
  String? msg;
  List<Datum>? data;

  AppCenterMenuModel({
    this.success,
    this.code,
    this.msg,
    this.data,
  });

  factory AppCenterMenuModel.fromJson(Map<String, dynamic> json) =>
      AppCenterMenuModel(
        success: json["success"],
        code: json["code"],
        msg: json["msg"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "code": code,
        "msg": msg,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  int? id;
  int? createBy;
  DateTime? createTime;
  int? updateBy;
  DateTime? updateTime;
  dynamic statusCd;
  dynamic createrName;
  dynamic updaterName;
  String? menuName;
  String? menuIconUrl;
  String? menuUrl;
  int? sort;
  String? enable;
  String? menuClass;
  String? menuCode;
  dynamic params;

  Datum({
    this.id,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.statusCd,
    this.createrName,
    this.updaterName,
    this.menuName,
    this.menuIconUrl,
    this.menuUrl,
    this.sort,
    this.enable,
    this.menuClass,
    this.menuCode,
    this.params,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        id: json["id"],
        createBy: json["createBy"],
        createTime: json["createTime"] == null
            ? null
            : DateTime.parse(json["createTime"]),
        updateBy: json["updateBy"],
        updateTime: json["updateTime"] == null
            ? null
            : DateTime.parse(json["updateTime"]),
        statusCd: json["statusCd"],
        createrName: json["createrName"],
        updaterName: json["updaterName"],
        menuName: json["menuName"],
        menuIconUrl: json["menuIconUrl"],
        menuUrl: json["menuUrl"],
        sort: json["sort"],
        enable: json["enable"],
        menuClass: json["menuClass"],
        menuCode: json["menuCode"],
        params: json["params"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "createBy": createBy,
        "createTime": createTime?.toIso8601String(),
        "updateBy": updateBy,
        "updateTime": updateTime?.toIso8601String(),
        "statusCd": statusCd,
        "createrName": createrName,
        "updaterName": updaterName,
        "menuName": menuName,
        "menuIconUrl": menuIconUrl,
        "menuUrl": menuUrl,
        "sort": sort,
        "enable": enable,
        "menuClass": menuClass,
        "menuCode": menuCode,
        "params": params,
      };
}
