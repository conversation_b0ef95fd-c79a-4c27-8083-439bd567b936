import 'package:bdh_smart_agric_app/generated/json/base/json_field.dart';
import 'package:bdh_smart_agric_app/generated/json/task_to_do_entity.g.dart';
import 'dart:convert';
export 'package:bdh_smart_agric_app/generated/json/task_to_do_entity.g.dart';

// @JsonSerializable()
// class TaskToDoEntity {
//   String? receiveTime = '';
//   String? senderName = '';
//   int? readState = 0;
//   String? subject = '';
//   String? edocMark = '';
//   int? importantLevel = 0;
//   int? type = 0;
//   String? urlLink = '';
//   String? createDate = '';

//   TaskToDoEntity();

//   factory TaskToDoEntity.fromJson(Map<String, dynamic> json) =>
//       $TaskToDoEntityFromJson(json);

//   Map<String, dynamic> toJson() => $TaskToDoEntityToJson(this);

//   @override
//   String toString() {
//     return jsonEncode(this);
//   }
// }

class TaskToDoEntity {
  String? receiveTime = '';
  String? senderName = '';
  int? readState = 0;
  String? subject = '';
  String? edocMark = '';
  int? importantLevel = 0;
  dynamic type = 0;
  String? urlLink = '';
  String? createDate = '';
  String? cellType;

  TaskToDoEntity({
    this.receiveTime,
    this.senderName,
    this.readState,
    this.subject,
    this.edocMark,
    this.importantLevel,
    this.type,
    this.urlLink,
    this.createDate,
    this.cellType,
  });

  factory TaskToDoEntity.fromJson(Map<String, dynamic> json) => TaskToDoEntity(
        receiveTime: json["receiveTime"],
        senderName: json["senderName"],
        readState: json["readState"],
        subject: json["subject"],
        edocMark: json["edocMark"],
        importantLevel: json["importantLevel"],
        type: json["type"],
        urlLink: json["urlLink"],
        createDate: json["createDate"],
        cellType: json["cellType"],
      );

  Map<String, dynamic> toJson() => {
        "receiveTime": receiveTime,
        "senderName": senderName,
        "readState": readState,
        "subject": subject,
        "edocMark": edocMark,
        "importantLevel": importantLevel,
        "type": type,
        "urlLink": urlLink,
        "createDate": createDate,
        "cellType": cellType,
      };
}
