import 'package:bdh_smart_agric_app/pages/user/html_page.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page_inappweb.dart';
import 'package:bdh_smart_agric_app/pages/worktable/services/work_service.dart';
import 'package:bdh_smart_agric_app/pages/worktable/work_to_do_page.dart';
import 'package:bdh_smart_agric_app/pages/worktable/work_web_page.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:logger/web.dart';
import 'package:oktoast/oktoast.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../utils/color_util.dart';
import '../../utils/event_bus.dart';
import '../message/bdh_empty_View.dart';
import 'entity/task_to_do_entity.dart';

class WorkTaskPage extends StatefulWidget {
  final String title;
  final String dataType;
  final String? showMoreUrl;
  const WorkTaskPage(
      {super.key,
      required this.title,
      required this.dataType,
      this.showMoreUrl});

  @override
  State<WorkTaskPage> createState() => _WorkTaskPageState();
}

class _WorkTaskPageState extends State<WorkTaskPage>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  List<TaskToDoEntity> taskRecords = [];
  String currentDataType = '';
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  int pageNum = 1;
  int pageSize = 10;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    setState(() {
      currentDataType = widget.dataType;
    });
    getTaskToDoRecords();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      color: const Color.fromRGBO(243, 245, 249, 1),
      child: Container(
        margin: EdgeInsets.only(left: 10.px, right: 10.px),
        width: 355.px,
        child: taskRecords.isEmpty
            ? Center(
                child: _isLoading
                    ? const SpinKitCircle(
                        // color: HexColor('#16B760'),
                        color: Color.fromRGBO(0, 127, 255, 1),
                        size: 60.0,
                      )
                    : const BdhEmptyView(),
              )
            : SmartRefresher(
                enablePullUp: false,
                onRefresh: _onRefresh,
                onLoading: _onLoadMore,
                controller: _refreshController,
                child: ListView.builder(
                    padding: EdgeInsets.only(bottom: 10.px),
                    itemCount: taskRecords.length,
                    itemBuilder: (ctx, idx) {
                      return _buildTaskItem(taskRecords[idx]);
                    }),
              ),
      ),
    );
  }

  Widget getShowMoreView() {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
          return HtmlPageInappweb(
              content: widget.showMoreUrl ?? '', title: widget.title);
          // return HtmlPage(
          //     title: widget.title, content: widget.showMoreUrl ?? '');
        }));
      },
      child: Container(
        margin: EdgeInsets.only(top: 10.px, bottom: 20.px),
        height: 60.px,
        width: 375.px,
        color: Colors.transparent,
        child: Center(
          child: Text(
            '查看更多',
            style: TextStyle(fontSize: 15.px),
          ),
        ),
      ),
    );
  }

  Widget _buildTaskItem(TaskToDoEntity taskRecord) {
    return taskRecord.cellType == 'showMore'
        ? getShowMoreView()
        : GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) =>
                        // HtmlPage(title: "详情", content: taskRecord.urlLink ?? ""),
                        HtmlPageInappweb(
                            content: taskRecord.urlLink ?? "", title: '详情')),
              ).then((res) {
                getTaskToDoRecords();
              });
            },
            child: Container(
              padding: EdgeInsets.only(
                  top: 12.px, bottom: 8.px, left: 8.px, right: 8.px),
              margin: EdgeInsets.only(top: 8.px),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8.px),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    taskRecord.subject!,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style:
                        TextStyle(fontSize: 16.px, fontWeight: FontWeight.w500),
                  ),
                  Visibility(
                    visible: taskRecord.type == 4,
                    child: Container(
                      margin: EdgeInsets.only(top: 5.px),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Text(
                            "——${taskRecord.edocMark ?? ''}",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 14.px,
                                color: const Color.fromRGBO(0, 0, 1, 0.24),
                                fontWeight: FontWeight.w400),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 5.px, bottom: 5.px),
                    child: Row(children: [
                      Text(
                        taskRecord.type == 1 ? '[协同]' : '[公文]',
                        style: TextStyle(
                            fontSize: 14.px,
                            color: const Color.fromRGBO(0, 0, 1, 0.4)),
                      ),
                      SizedBox(
                        width: 5.px,
                      ),
                      Text(
                        taskRecord.senderName!,
                        style: TextStyle(
                            fontSize: 14.px,
                            color: const Color.fromRGBO(0, 0, 1, 0.72)),
                      ),
                      SizedBox(
                        width: 4.px,
                      ),
                      Visibility(
                        visible: taskRecord.type == 4,
                        child: Icon(CupertinoIcons.paperclip, size: 14.px),
                      )
                    ]),
                  ),
                  SizedBox(
                    height: 10.px,
                  ),
                  const Divider(
                    height: 0.5,
                    color: Color.fromRGBO(152, 165, 178, 0.1),
                  ),
                  SizedBox(
                    height: 10.px,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        taskRecord.receiveTime!,
                        style: TextStyle(
                            fontSize: 13.px,
                            fontWeight: FontWeight.w300,
                            color: const Color.fromRGBO(0, 0, 1, 0.32)),
                      ),
                    ],
                  )
                ],
              ),
            ),
          );
  }

  void _onRefresh() async {
    pageNum = 1;
    getTaskToDoRecords();
  }

  void _onLoadMore() async {
    pageNum++;
    getTaskToDoRecords();
  }

  void getTaskToDoRecords() {
    Map<String, dynamic> params = {'pageSize': pageSize, 'type': ''};

    String url = WorkService.taskToDo;
    if (widget.title == '待办') {
      url = WorkService.taskToDo;
    } else if (widget.title == '已办') {
      url = WorkService.taskDone;
    } else if (widget.title == '已发') {
      url = WorkService.taskSent;
    }

    params['type'] = currentDataType;
    setState(() {
      taskRecords.clear();
      _isLoading = true;
    });

    WorkService.getTaskByType(url, params).then((result) {
      _refreshController.refreshCompleted();
      if (result.success == true) {
        if (result.data == null) {
          setState(() {
            _isLoading = false;
            taskRecords = [];
          });
          showToast(result.msg ?? '');
        } else {
          List<TaskToDoEntity> records = (result.data as List<dynamic>)
              .map((item) => TaskToDoEntity.fromJson(item))
              .toList();

          TaskToDoEntity cellModel = TaskToDoEntity(cellType: 'showMore');
          records.add(cellModel);
          setState(() {
            _isLoading = false;
            taskRecords = records;
          });
        }

        if (widget.title == '待办') {
          if (taskRecords.isNotEmpty) {
            bus.emit("hasTaskTodo", true);
          } else {
            bus.emit("hasTaskTodo", false);
          }
        }
      } else {
        setState(() {
          _isLoading = false;
          taskRecords = [];
        });
      }
    });

    // WorkService.getTaskByType(url, params).then((result) {
    //   if (result.success == true) {
    //     if (mounted) {
    //       setState(() {
    //         _isLoading = false;
    //         List<TaskToDoEntity> records = (result.data as List<dynamic>)
    //             .map((item) => TaskToDoEntity.fromJson(item))
    //             .toList();
    //         taskRecords = records;
    //         if (widget.title == '待办') {
    //           if (taskRecords.isNotEmpty) {
    //             bus.emit("hasTaskTodo", true);
    //           } else {
    //             bus.emit("hasTaskTodo", false);
    //           }
    //         }
    //         _refreshController.refreshCompleted();
    //       });
    //     }
    //   }
    // });
  }

  @override
  void didUpdateWidget(covariant WorkTaskPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.dataType != widget.dataType) {
      setState(() {
        currentDataType = widget.dataType;
        getTaskToDoRecords();
      });
    }
  }

  @override
  bool get wantKeepAlive => true;
}
