import 'dart:io';

import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/bordercast_view/home_broadcast_view.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/provider/message_bdh_digital_provider.dart';
import 'package:bdh_smart_agric_app/pages/version/bdh_newversion_view.dart';
import 'package:bdh_smart_agric_app/pages/worktable/widgets/scan_login/sacn_page_bdh_digital.dart';
import 'package:bdh_smart_agric_app/pages/worktable/widgets/work_tab_bar_widget.dart';
import 'package:bdh_smart_agric_app/pages/worktable/work_bench_page.dart';
import 'package:bdh_smart_agric_app/pages/worktable/work_to_do_page.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/cover_tool.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:logger/logger.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:provider/provider.dart';

import '../../model/menu_config_model.dart';
import '../../utils/event_bus.dart';

class WorkTablePage extends StatefulWidget {
  final MenuConfigItem item;

  const WorkTablePage({super.key, required this.item});

  @override
  State<WorkTablePage> createState() => _WorkTablePageState();
}

class _WorkTablePageState extends State<WorkTablePage>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  // final List<String> tabs = ['待办', '工作'];
  final List<String> tabs = ['工作', '待办'];
  int selectedIndex = 0;
  PageController pageController = PageController(viewportFraction: 1);
  List<bool> hasUnread = [false, false];
  Barcode? _barcode;
  bool _appBadgeSupported = false;

  @override
  void initState() {
    super.initState();
    versionUpdate();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);
    // SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    bus.on('hasTaskTodo', (e) {
      setState(() {
        hasUnread[0] = e;
      });
    });
    getmessageData();
    initPlatformState();
  }

  initPlatformState() async {
    bool appBadgeSupported;
    try {
      bool res = await FlutterAppBadger.isAppBadgeSupported();
      appBadgeSupported = res;
    } on PlatformException {
      appBadgeSupported = false;
    }
    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;
    setState(() {
      _appBadgeSupported = appBadgeSupported;
    });
  }

  versionUpdate() {
    bool? haveShowHomeBroadcaseView =
        StorageManager.sharedPreferences?.getBool('haveShowHomeBroadcaseView');
    bool isBefore20250131 = FormatTool().isBefore20250131();
    Logger().i(isBefore20250131);
    if (Platform.isAndroid) {
      Future.delayed(const Duration(seconds: 2), () {
        GetCurrentInstallVersion.check(context: context, needShowDialog: true)
            .then((res) {
          Logger().i('res = $res');
          bool haveNewVersion = res['haveNewVersion'];
          if (isBefore20250131 &&
              !haveNewVersion &&
              !(haveShowHomeBroadcaseView ?? false)) {
            Future.delayed(const Duration(seconds: 1), () {
              showDialog(
                  context: context,
                  barrierDismissible: false,
                  builder: (ctx) {
                    return PopScope(
                        canPop: false,
                        child: Center(
                          child: HomeBroadcastView(clickedClose: () {
                            StorageManager.sharedPreferences!
                                .setBool('haveShowHomeBroadcaseView', true);
                          }),
                        ));
                  });
            });
          }
        });
      });
    } else {
      if (isBefore20250131 && !(haveShowHomeBroadcaseView ?? false)) {
        Future.delayed(const Duration(seconds: 1), () {
          showDialog(
              context: context,
              barrierDismissible: false,
              builder: (ctx) {
                return PopScope(
                    canPop: false,
                    child: Center(
                      child: HomeBroadcastView(clickedClose: () {
                        StorageManager.sharedPreferences!
                            .setBool('haveShowHomeBroadcaseView', true);
                      }),
                    ));
              });
        });
      }
    }
  }

  void getmessageData() {
    MessageBdhDigitalProvider tempProvider =
        Provider.of<MessageBdhDigitalProvider>(context, listen: false);
    tempProvider.refreshCooperateAndNewsMessage();
  }

  @override
  void dispose() {
    super.dispose();
    // 离开页面时恢复状态栏
    // SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Scaffold(
      backgroundColor: HexColor("#F3F5F9"),
      body: Stack(
        children: [
          Image.asset(
              width: 375.px,
              height: 812.px,
              fit: BoxFit.fitWidth,
              ImageHelper.wrapAssets("workTabelBG.png")),
          Column(
            children: [
              Container(
                // color: Colors.red,
                margin: EdgeInsets.only(bottom: 12.px),
                alignment: Alignment.bottomCenter,
                padding: EdgeInsets.only(left: 0.px, top: 20.px),
                height: 85.px,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    WorkTabBarWidget(
                      tabs: tabs,
                      hasUnread: hasUnread,
                      selectedIndex: selectedIndex,
                      onSelectChanged: (index) {
                        pageController.animateToPage(index!,
                            duration: const Duration(milliseconds: 500),
                            curve: Curves.easeInOut);
                      },
                    ),
                    Consumer<MessageBdhDigitalProvider>(
                        builder: (context, messageProvider, child) {
                      int unReadCountCooperate =
                          messageProvider.unReadCountCooperate;
                      int unReadCountNews = messageProvider.unReadCountNews;
                      int unReadSystemCount = messageProvider.unReadSystemCount;
                      int unReadNoticeCount = messageProvider.unReadNoticeCount;
                      // int count = unReadCountCooperate +
                      //     unReadCountNews +
                      //     unReadSystemCount +
                      //     unReadNoticeCount;
                      int count = unReadCountCooperate +
                          unReadSystemCount +
                          unReadNoticeCount;
                      _appBadgeSupported
                          ? (count == 0
                              ? FlutterAppBadger.removeBadge()
                              : FlutterAppBadger.updateBadgeCount(count))
                          : ();
                      return Container(
                        child: Row(
                          children: [
                            //消息入口
                            Stack(
                              clipBehavior: Clip.none,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    Navigator.pushNamed(context,
                                            RouteName.messageBdhDigitalPage)
                                        .then((res) {
                                      getmessageData();
                                    });
                                  },
                                  child: Container(
                                    padding: EdgeInsets.only(right: 15.px),
                                    // height: 30.px,
                                    // width: 30.px,
                                    color: Colors.transparent,
                                    child: Image.asset(
                                        height: 24.px,
                                        width: 24.px,
                                        ImageHelper.wrapAssets(
                                            'messageIconBdhDigital.png')),
                                  ),
                                ),
                                Positioned(
                                  top: -12.px,
                                  right: 3.px,
                                  child: count == 0
                                      ? Container()
                                      : Container(
                                          height: 18.px,
                                          padding: EdgeInsets.only(
                                              left: 5.px, right: 5.px),
                                          decoration: BoxDecoration(
                                              color: Colors.red,
                                              borderRadius:
                                                  BorderRadius.circular(10.px)),
                                          child: Align(
                                            alignment: Alignment.center,
                                            child: Text(
                                              '$count',
                                              style: TextStyle(
                                                  fontSize: 12.px,
                                                  color: Colors.white),
                                            ),
                                          ),
                                        ),
                                ),
                              ],
                            ),
                            //扫码入口
                            GestureDetector(
                              onTap: () {
                                PermissionUtil.requestCameraPermission(
                                        context, "头像")
                                    .then((res) {
                                  if (res == true) {
                                    MobileScannerController controller =
                                        MobileScannerController(
                                      autoStart: false,
                                      detectionSpeed: DetectionSpeed.normal,
                                      facing: CameraFacing.back,
                                      cameraResolution: const Size(1920, 2560),
                                    );

                                    Navigator.pushNamed(
                                        context, RouteName.sacnPageBdhDigital,
                                        arguments: controller);
                                  }
                                });
                                // Navigator.push(context,
                                //     CupertinoPageRoute(builder: (_) {
                                //   return const SacnPageBdhDigital();
                                // }));
                                // Navigator.pushNamed(
                                //     context, RouteName.sacnPageBdhDigital);
                              },
                              child: Container(
                                padding: EdgeInsets.only(right: 10.px),
                                // height: 30.px,
                                // width: 40.px,
                                color: Colors.transparent,
                                child: Image.asset(
                                    height: 24.px,
                                    width: 24.px,
                                    ImageHelper.wrapAssets(
                                        'scanIconBdhDigital.png')),
                              ),
                            ),
                          ],
                        ),
                      );
                    }),
                  ],
                ),
              ),
              selectedIndex == 0
                  ? const Divider(
                      height: 5,
                      color: Color.fromRGBO(152, 165, 178, 0.1),
                    )
                  : Container(),
              Expanded(
                child: PageView(
                  controller: pageController,
                  physics: const NeverScrollableScrollPhysics(),
                  onPageChanged: (index) {
                    setState(() {
                      selectedIndex = index;
                    });
                  },
                  children: const [
                    WorkBenchPage(),
                    WorkToDoPage(),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}
