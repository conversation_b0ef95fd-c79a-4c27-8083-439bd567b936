import 'package:bdh_smart_agric_app/pages/worktable/entity/app_center_menu_model.dart';
import 'package:bdh_smart_agric_app/pages/worktable/services/work_service.dart';
import 'package:bdh_smart_agric_app/pages/worktable/widgets/triangle_shape.dart';
import 'package:bdh_smart_agric_app/pages/worktable/work_task_page.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class WorkToDoPage extends StatefulWidget {
  const WorkToDoPage({super.key});

  @override
  State<WorkToDoPage> createState() => _WorkToDoPageState();
}

class _WorkToDoPageState extends State<WorkToDoPage>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  late TabController _tabController;

  PageController pageController = PageController(viewportFraction: 1);

  List<String> tabs = ['待办', '已办', '已发'];
  List<String> dataTypes = ['', '1', '4'];
  List<String> showMoreList = [];
  List<WorkItemModel> tabsList = [
    WorkItemModel(name: '待办', isSelected: true, index: 0),
    WorkItemModel(name: '已办', isSelected: false, index: 1),
    WorkItemModel(name: '已发', isSelected: false, index: 2),
  ];
  String waitHandle = '';
  String completeHandle = '';
  String completeSend = '';

  String dataType = '';

  @override
  void initState() {
    super.initState();
    getAppCenterMenus();
    _tabController = TabController(vsync: this, length: tabs.length);
  }

  getAppCenterMenus() {
    WorkService.getAppCenterMenus({}).then((res) {
      List<String> tempList = [];
      List<Datum> modelList = res.data ?? [];
      for (int i = 0; i < modelList.length; i++) {
        Datum item = modelList[i];
        if (item.menuCode == 'waitHandle') {
          tempList.add(item.menuUrl ?? '');
        } else if (item.menuCode == 'completeHandle') {
          completeHandle = item.menuUrl ?? '';
          tempList.add(item.menuUrl ?? '');
        } else if (item.menuCode == 'completeSend') {
          completeSend = item.menuUrl ?? '';
          tempList.add(item.menuUrl ?? '');
        }
      }
      showMoreList.clear();
      setState(() {
        showMoreList.addAll(tempList);
      });
    });
  }

  dealWithSelectedWithModel(WorkItemModel model) {
    for (int i = 0; i < tabsList.length; i++) {
      WorkItemModel item = tabsList[i];
      if (item.name == model.name) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    }
    setState(() {});
  }

  Widget getItem(WorkItemModel model) {
    return GestureDetector(
      onTap: () {
        dealWithSelectedWithModel(model);
        pageController.animateToPage(model.index,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.px),
        margin: EdgeInsets.only(left: 10.px),
        height: 28.px,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.px),
          border: model.isSelected
              ? Border.all(
                  width: 0.5, color: const Color.fromRGBO(0, 127, 255, 1))
              : Border.all(
                  width: 0.01, color: Color.fromRGBO(245, 245, 245, 1)),
          color: model.isSelected
              ? const Color.fromRGBO(0, 127, 255, 0.1)
              : const Color.fromRGBO(245, 245, 245, 1),
        ),
        child: Align(
          alignment: Alignment.center,
          child: Text(
            model.name,
            style: TextStyle(
              color: model.isSelected
                  ? const Color.fromRGBO(0, 127, 255, 1)
                  : const Color.fromRGBO(34, 34, 34, 0.6),
              fontSize: 12.px,
              fontWeight: model.isSelected ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      color: const Color.fromRGBO(243, 245, 249, 1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 48.px,
            width: 375.px,
            decoration: const BoxDecoration(
              border: Border(
                  bottom: BorderSide(
                      width: 1, color: Color.fromRGBO(152, 165, 178, 0.1))),
              color: Colors.transparent,
            ),
            child: Row(
              children: [
                Container(
                  child: Row(
                    children: tabsList.map((e) => getItem(e)).toList(),
                  ),
                ),
                const Spacer(),
                PopupMenuButton<String>(
                  // icon: const Icon(Icons.format_list_bulleted_outlined,
                  icon: Image.asset(
                      height: 24.px,
                      width: 24.px,
                      ImageHelper.wrapAssets('listImgBDHDigtal.png')),
                  // color: Color.fromRGBO(152, 165, 178, 1), size: 24),
                  onSelected: (value) {
                    setState(() {
                      dataType = value;
                    });
                  },
                  shape: RoundedRectangleBorder(
                    // 菜单形状
                    borderRadius: BorderRadius.circular(8),
                    side: const BorderSide(color: Colors.white),
                  ),
                  color: Colors.white,
                  offset: const Offset(0, 40),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                        value: '',
                        child: Row(
                          children: [
                            Icon(
                              dataType == ''
                                  ? Icons.radio_button_checked_rounded
                                  : Icons.radio_button_unchecked_rounded,
                              color:
                                  dataType == '' ? Colors.green : Colors.grey,
                              size: 20,
                            ),
                            SizedBox(
                              width: 2.px,
                            ),
                            const Text('全部'),
                          ],
                        )),
                    PopupMenuItem(
                        value: '4',
                        child: Row(children: [
                          Icon(
                            dataType == '4'
                                ? Icons.radio_button_checked_rounded
                                : Icons.radio_button_unchecked_rounded,
                            color: dataType == '4' ? Colors.green : Colors.grey,
                            size: 20,
                          ),
                          SizedBox(
                            width: 2.px,
                          ),
                          const Text('公文'),
                        ])),
                    PopupMenuItem(
                        value: '1',
                        child: Row(children: [
                          Icon(
                            dataType == '1'
                                ? Icons.radio_button_checked_rounded
                                : Icons.radio_button_unchecked_rounded,
                            color: dataType == '1' ? Colors.green : Colors.grey,
                            size: 20,
                          ),
                          SizedBox(
                            width: 2.px,
                          ),
                          const Text('协同'),
                        ])),
                  ],
                )
              ],
            ),
          ),
          /* Container(
            color: Colors.white,
            child: Row(
              children: [
                TabBar(
                  tabAlignment: TabAlignment.start,
                  indicatorSize: TabBarIndicatorSize.tab,
                  onTap: (tab) {
                    pageController.animateToPage(tab,
                        duration: const Duration(milliseconds: 500),
                        curve: Curves.easeInOut);
                  },
                  labelStyle: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.w600),
                  unselectedLabelStyle: const TextStyle(fontSize: 16),
                  isScrollable: true,
                  controller: _tabController,
                  labelColor: Colors.black,
                  indicatorWeight: 1,
                  indicatorPadding: const EdgeInsets.symmetric(horizontal: 10),
                  unselectedLabelColor: const Color.fromRGBO(0, 0, 1, 0.4),
                  dividerColor: Colors.transparent,
                  indicatorColor: Colors.green,
                  tabs: tabs.map((e) => Tab(text: e)).toList(),
                ),
                const Spacer(),
                // IconButton(
                //     onPressed: () {},
                //     icon: const Icon(Icons.format_list_bulleted_outlined,
                //         color: Color.fromRGBO(152, 165, 178, 1), size: 24)),

                PopupMenuButton<String>(
                  icon: const Icon(Icons.format_list_bulleted_outlined,
                      color: Color.fromRGBO(152, 165, 178, 1), size: 24),
                  onSelected: (value) {
                    setState(() {
                      dataType = value;
                    });
                  },
                  shape: RoundedRectangleBorder(
                    // 菜单形状
                    borderRadius: BorderRadius.circular(8),
                    side: const BorderSide(color: Colors.white),
                  ),
                  color: Colors.white,
                  offset: const Offset(0, 40),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                        value: '',
                        child: Row(
                          children: [
                            Icon(
                              dataType == ''
                                  ? Icons.radio_button_checked_rounded
                                  : Icons.radio_button_unchecked_rounded,
                              color:
                                  dataType == '' ? Colors.green : Colors.grey,
                              size: 20,
                            ),
                            SizedBox(
                              width: 2.px,
                            ),
                            const Text('全部'),
                          ],
                        )),
                    PopupMenuItem(
                        value: '4',
                        child: Row(children: [
                          Icon(
                            dataType == '4'
                                ? Icons.radio_button_checked_rounded
                                : Icons.radio_button_unchecked_rounded,
                            color: dataType == '4' ? Colors.green : Colors.grey,
                            size: 20,
                          ),
                          SizedBox(
                            width: 2.px,
                          ),
                          const Text('公文'),
                        ])),
                    PopupMenuItem(
                        value: '1',
                        child: Row(children: [
                          Icon(
                            dataType == '1'
                                ? Icons.radio_button_checked_rounded
                                : Icons.radio_button_unchecked_rounded,
                            color: dataType == '1' ? Colors.green : Colors.grey,
                            size: 20,
                          ),
                          SizedBox(
                            width: 2.px,
                          ),
                          const Text('协同'),
                        ])),
                  ],
                )
              ],
            ),
          ),
          */
          Expanded(
              child: PageView(
            controller: pageController,
            physics: const NeverScrollableScrollPhysics(),
            onPageChanged: (index) {
              WorkItemModel model = tabsList[index];
            },
            children: _buildPageItems(),
          )),
        ],
      ),
    );
  }

  List<Widget> _buildPageItems() {
    final List<Widget> widgets = List.empty(growable: true);
    for (var index = 0; index < tabs.length; index++) {
      // String moreUrl = showMoreList[index];
      widgets.add(WorkTaskPage(
        title: tabs.elementAt(index),
        dataType: dataType,
        showMoreUrl: showMoreList.isNotEmpty ? showMoreList[index] : '',
      ));
    }
    return widgets;
  }

  @override
  bool get wantKeepAlive => true;
}

class WorkItemModel {
  int index;
  String name;
  bool isSelected;
  WorkItemModel(
      {required this.name, required this.isSelected, required this.index});
}
