import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WorkWebPage extends StatefulWidget {
  final String url;
  const WorkWebPage({super.key, required this.url});

  @override
  State<StatefulWidget> createState() => _WorkWebPageState();
}

class _WorkWebPageState extends State<WorkWebPage> {
  final WebViewController _webViewController = WebViewController();
  @override
  void initState() {
    super.initState();
    _webViewController.setJavaScriptMode(JavaScriptMode.unrestricted);
    _webViewController.loadRequest(Uri.parse(widget.url));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("详情"),
      ),
      body: WebViewWidget(controller: _webViewController),
    );
  }
}
