import 'dart:convert';

import 'package:bdh_smart_agric_app/pages/worktable/entity/app_center_menu_model.dart';
import 'package:dio/dio.dart';

import '../../../../model/request_no_data.dart';
import '../../../../utils/request/bdh_api.dart';

/// 工作台服务
class WorkService {
  static String taskToDo = "/seeyon/api/getPendingByUserCode";
  static String taskDone = "/seeyon/api/getDoneByUserCode";
  static String taskSent = "/seeyon/api/getSentByUserCode";

  /// 根据类型获取数据
  static Future<RequestNoData> getTaskByType(url, params,
      {CancelToken? cancelToken}) async {
    var response =
        await authHttp.post(url, data: params, cancelToken: cancelToken);
    // String jsonEncode(response);
    return RequestNoData.fromJson(response.data);
  }

  /// 获取待办列表
  static Future<RequestNoData> getPendingByUserCode(params,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post("/seeyon/api/getPendingByUserCode",
        data: params, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  // 获取已完成列表
  static Future<RequestNoData> getDoneByUserCode(params,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post("/seeyon/api/getDoneByUserCode",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  // 获取已发送列表
  static Future<RequestNoData> getSentByUserCode(params,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post("/seeyon/api/getSentByUserCode",
        data: params, cancelToken: cancelToken);
    return RequestNoData.fromJson(response.data);
  }

  // 获取待办 查看更多链接
  static Future<AppCenterMenuModel> getAppCenterMenus(params,
      {CancelToken? cancelToken}) async {
    var response = await authHttp.post("/seeyon/api/getAppCenterMenus",
        data: params, cancelToken: cancelToken);
    return AppCenterMenuModel.fromJson(response.data);
  }
}
