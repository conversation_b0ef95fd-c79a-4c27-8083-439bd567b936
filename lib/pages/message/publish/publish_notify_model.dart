//系统通知 模型
class PublishNotifyModel {
  late int? noticeId;
  late String? orgCode;
  late String? orgName;
  late String? noticeType;
  late String? sendTime;
  late String? noticeName;
  late String? noticeContent;
  late String? isRead;
  late int? readCount;
  late int? noReadCount;

  PublishNotifyModel({
    this.noticeId,
    this.orgCode,
    this.orgName,
    this.noticeType,
    this.sendTime,
    this.noticeName,
    this.noticeContent,
    this.isRead,
    this.readCount,
    this.noReadCount,
  });

  PublishNotifyModel.fromJson(Map<String, dynamic> json) {
    noticeId = json['noticeId'];
    orgCode = json['orgCode'];
    orgName = json['orgName'];
    noticeType = json['noticeType'];
    sendTime = json['sendTime'];
    noticeName = json['noticeName'];
    noticeContent = json['noticeContent'];
    isRead = json['isRead'];
    readCount = json['readCount'];
    noReadCount = json['noReadCount'];
  }
}


//    "noticeId": 248,
// I/flutter (  617): │ 💡         "orgCode": "860405",
// I/flutter (  617): │ 💡         "orgName": "北大荒农垦集团有限公司-牡丹江分公司-八五六农场",
// I/flutter (  617): │ 💡         "recOrgCode": "8604050107;",
// I/flutter (  617): │ 💡         "recOrgName": "北大荒农垦集团有限公司-牡丹江分公司-八五六农场-第一管理区",
// I/flutter (  617): │ 💡         "noticeType": "1",
// I/flutter (  617): │ 💡         "sendType": null,
// I/flutter (  617): │ 💡         "sendTime": "2024-08-07 15:29:58",
// I/flutter (  617): │ 💡         "noticeName": "3`23",
// I/flutter (  617): │ 💡         "noticeContent": "<p>341234</p>",
// I/flutter (  617): │ 💡         "dataStatus": "1",
// I/flutter (  617): │ 💡         "releaseMode": "1",
// I/flutter (  617): │ 💡         "roleType": "1",
// I/flutter (  617): │ 💡         "impBatchId": null,
// I/flutter (  617): │ 💡         "remark": null,
// I/flutter (  617): │ 💡         "createBy": null,
// I/flutter (  617): │ 💡         "createTime": null,
// I/flutter (  617): │ 💡         "updateBy": null,
// I/flutter (  617): │ 💡         "updateTime": null,
// I/flutter (  617): │ 💡         "statusCd": null,
// I/flutter (  617): │ 💡         "params": null,
// I/flutter (  617): │ 💡         "list": null,
// I/flutter (  617): │ 💡         "annexList": null,
// I/flutter (  617): │ 💡         "statsTime": null,
// I/flutter (  617): │ 💡         "totalCount": 1,
// I/flutter (  617): │ 💡         "noReadCount": 0,
// I/flutter (  617): │ 💡         "readCount": 1,
// I/flutter (  617): │ 💡         "recSystemCode": null,
// I/flutter (  617): │ 💡         "recSystemName": "数字北大荒app",
// I/flutter (  617): │ 💡         "recRoleId": "-1",
// I/flutter (  617): │ 💡         "recRoleName": "数字北大荒app-ALL",
// I/flutter (  617): │ 💡         "impBatchDesc": null