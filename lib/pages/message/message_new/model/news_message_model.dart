class NewsMessageModel {
  late String? createTime;
  late String? updateTime;
  late String? noticeUid;
  late String? category;
  late String? title; //系统消息 标题
  late String? content;
  late String? contentStr;
  late String? userName;
  late String? telephone;
  late String? orgCode;
  late String? orgName;
  late String? readStatus;
  late String? sendTime;
  late String? readTime;
  late int? statusCd;
  late int? messageId;
  late int? userId;
  late int? noticeId;
  late String? dataStatus; // 状态：1-待发布 2-审核中 3-已驳回 4-已发布 5-已撤回
  late String? receiverType;
  late String? receiverOrg; // "receiverOrg": "860301,860302,
  late String? receiverOrgName; //"八五九农场,胜利农场
  late List<ReadStatusStatisticModel>? readStatusStatistic;
  late List<ReadListModel>? unReadList;
  late List<ReadListModel>? readList;
  late int? senderId;
  late String? senderName;

  NewsMessageModel({
    this.createTime,
    this.updateTime,
    this.noticeUid,
    this.category,
    this.title,
    this.content,
    this.contentStr = '',
    this.userName,
    this.telephone,
    this.orgCode,
    this.orgName,
    this.readStatus,
    this.sendTime,
    this.readTime,
    this.statusCd,
    this.messageId,
    this.userId,
    this.noticeId,
    this.dataStatus,
    this.receiverType,
    this.receiverOrg,
    this.receiverOrgName,
    this.readStatusStatistic,
    this.unReadList,
    this.readList,
    this.senderName,
    this.senderId,
  });

  NewsMessageModel.fromJson(Map<String, dynamic> json) {
    senderName = json['senderName'];
    senderId = json['senderId'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    noticeUid = json['noticeUid'];
    category = json['category'];
    title = json['title'];
    content = json['content'];
    userName = json['userName'];
    telephone = json['telephone'];
    orgCode = json['orgCode'];
    orgCode = json['orgCode'];
    orgName = json['orgName'];
    readStatus = json['readStatus'];
    sendTime = json['sendTime'];
    readTime = json['readTime'];
    statusCd = json['statusCd'];
    userId = json['userId'];
    messageId = json['messageId'];
    noticeId = json['noticeId'];
    dataStatus = json['dataStatus'];
    receiverType = json['receiverType'];
    receiverOrg = json['receiverOrg'];
    receiverOrgName = json['receiverOrgName'];
    // readStatusStatistic = json['readStatusStatistic'];
    if (json['readStatusStatistic'] != null) {
      readStatusStatistic = <ReadStatusStatisticModel>[];
      json['readStatusStatistic'].forEach((v) {
        readStatusStatistic!.add(ReadStatusStatisticModel.fromJson(v));
      });
    } else {
      readStatusStatistic = [];
    }

    if (json['unReadList'] != null) {
      unReadList = <ReadListModel>[];
      json['unReadList'].forEach((v) {
        unReadList?.add(ReadListModel.fromJson(v));
      });
    } else {
      unReadList = [];
    }

    if (json['readList'] != null) {
      readList = <ReadListModel>[];
      json['readList'].forEach((v) {
        readList?.add(ReadListModel.fromJson(v));
      });
    } else {
      readList = [];
    }
  }
  /*
     "createBy": null,
I/flutter (23886): │ 💡       "createTime": "2024-08-23 14:29:15",
I/flutter (23886): │ 💡       "updateBy": null,
I/flutter (23886): │ 💡       "updateTime": "2024-08-23 14:29:14",
I/flutter (23886): │ 💡       "remark": null,
I/flutter (23886): │ 💡       "statusCd": 1,
I/flutter (23886): │ 💡       "messageId": 2,
I/flutter (23886): │ 💡       "noticeUid": "1826867709481058304",
I/flutter (23886): │ 💡       "category": "3",
I/flutter (23886): │ 💡       "title": "修改3",
I/flutter (23886): │ 💡       "content": "测试修改3",
I/flutter (23886): │ 💡       "userId": 38731,
I/flutter (23886): │ 💡       "userName": "马永欣",
I/flutter (23886): │ 💡       "telephone": "18627924036",
I/flutter (23886): │ 💡       "orgCode": "860406",
I/flutter (23886): │ 💡       "orgName": "八五七农场",
I/flutter (23886): │ 💡       "readStatus": "1",
I/flutter (23886): │ 💡       "sendTime": "2024-08-23 14:29:14",
I/flutter (23886): │ 💡       "readTime": "2024-08-23 14:39:51"
                               "senderId": 39052,
I/flutter (24084): │ 💡         "senderName": "李邢珠"
*/
}

class ReadStatusStatisticModel {
  // late String? key;
  late String? code;
  late String? name;
  late int? cnt;
  ReadStatusStatisticModel(
      {
      // this.key,
      this.code,
      this.name,
      this.cnt});
  ReadStatusStatisticModel.fromJson(Map<String, dynamic> json) {
    // key = json['key'];
    code = json['code'];
    name = json['name'];
    cnt = json['cnt'];
  }

  /*
  "readStatusStatistic": [
I/flutter (32025): │ 💡           {
I/flutter (32025): │ 💡             "key": "1831593071129657344",
I/flutter (32025): │ 💡             "code": "0",
I/flutter (32025): │ 💡             "name": "未读",
I/flutter (32025): │ 💡             "cnt": 1
I/flutter (32025): │ 💡           }
I/flutter (32025): │ 💡         ]

 */
}

//未读人员信息
class ReadListModel {
  late String? createTime;
  late String? updateBy;
  late String? updateTime;
  late String? remark;
  late int? statusCd;
  late int? messageId;
  late String? noticeUid;
  late String? title;
  late String? content;
  late int? userId;
  late String? userName;
  late String? telephone;
  late String? orgCode;
  late String? orgName;
  late String? readStatus;
  late String? sendTime;

  ReadListModel({
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.remark,
    this.statusCd,
    this.messageId,
    this.noticeUid,
    this.title,
    this.content,
    this.userId,
    this.userName,
    this.telephone,
    this.orgCode,
    this.orgName,
    this.readStatus,
    this.sendTime,
  });

  ReadListModel.fromJson(Map<String, dynamic> json) {
    createTime = json['createTime'];
    updateBy = json['updateBy'];
    updateTime = json['updateTime'];
    remark = json['remark'];
    statusCd = json['statusCd'];
    messageId = json['messageId'];
    noticeUid = json['noticeUid'];
    title = json['title'];
    content = json['content'];
    userId = json['userId'];
    userName = json['userName'];
    telephone = json['telephone'];
    orgCode = json['orgCode'];
    orgName = json['orgName'];
    readStatus = json['readStatus'];
    sendTime = json['sendTime'];
  }

  /*
  
    "createBy": null,
I/flutter (29326): │ 💡         "createTime": "2024-09-05 18:59:21",
I/flutter (29326): │ 💡         "updateBy": null,
I/flutter (29326): │ 💡         "updateTime": "2024-09-05 18:59:21",
I/flutter (29326): │ 💡         "remark": null,
I/flutter (29326): │ 💡         "statusCd": 1,
I/flutter (29326): │ 💡         "messageId": 19,
I/flutter (29326): │ 💡         "noticeUid": "1831647557793611776",
I/flutter (29326): │ 💡         "category": null,
I/flutter (29326): │ 💡         "title": "3333333",
I/flutter (29326): │ 💡         "content": "3333333",
I/flutter (29326): │ 💡         "userId": 38813,
I/flutter (29326): │ 💡         "userName": "吴于虎",
I/flutter (29326): │ 💡         "telephone": "13556565656",
I/flutter (29326): │ 💡         "orgCode": "860307",
I/flutter (29326): │ 💡         "orgName": "前进农场",
I/flutter (29326): │ 💡         "readStatus": "0",
I/flutter (29326): │ 💡         "sendTime": "2024-09-05 18:59:21",
I/flutter (29326): │ 💡         "readTime": null

   */
}
