import 'package:bdh_smart_agric_app/pages/home/<USER>/news_subview/bdh_text_scaler.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_view.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/pubulish_msg/new_message_publish_record_search.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/pubulish_msg/new_publish_html_ditail.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/pubulish_msg/new_publish_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/viewmodel/font_scale_model.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:fluwx/fluwx.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../components/bdh_share_view.dart';
import '../../../../components/refresh_header_footer/bdh_refresh_footer.dart';
import '../../../../components/refresh_header_footer/bdh_refresh_header.dart';
import '../../../../const/url_config_const.dart';
import '../../../../utils/color_util.dart';
import '../../../../utils/image_util.dart';
import '../../../../utils/request/message_new_service.dart';
import '../../../home/<USER>/news_subview/bdh_change_font.dart';
import '../model/news_message_model.dart';

class NewMessagePublishRecord extends StatefulWidget {
  const NewMessagePublishRecord({
    super.key,
  });

  @override
  State<NewMessagePublishRecord> createState() =>
      _NewMessagePublishRecordState();
}

class _NewMessagePublishRecordState extends State<NewMessagePublishRecord> {
  int _totalPage = 1; // 默认总页数为1页
  int _pageNum = 1;
  final int _pageSize = 10;
  bool _isAllDataLoaded = false;
  bool _showLoading = false;
  double fontScale = 1.0;
  late List<NewsMessageModel> messageRecordList = [];
  Fluwx fluwx = Fluwx();

  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    getMessageRecordListDate();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    double fontScaleA = context.watch<FontScaleModel>().fontScale;
    setState(() {
      fontScale = fontScaleA;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          //top bg
          Positioned(
              top: 0,
              left: 0,
              right: 0,
              height: 294.px,
              child: Image.asset(
                width: 375.px,
                height: 294.px,
                ImageHelper.wrapAssets("messageBGImg.png"),
              )),

          Positioned.fill(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                SizedBox(height: 40.px),
                // 第一行 返回, 字体, 搜索
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    //返回
                    Expanded(
                        flex: 2,
                        child: SizedBox(
                            height: 40.px,
                            // color: Colors.red,
                            child: Row(
                              children: [
                                Container(
                                  padding: EdgeInsets.only(left: 10.px),
                                  child: GestureDetector(
                                    onTap: () {
                                      Navigator.of(context).pop();
                                    },
                                    child: Image.asset(
                                        width: 24.px,
                                        height: 24.px,
                                        ImageHelper.wrapAssets(
                                            "backBlackIcon.png")),
                                  ),
                                ),
                              ],
                            ))),
                    //通知范围
                    Expanded(
                        flex: 3,
                        child: SizedBox(
                            height: 40.px,
                            // decoration:
                            // BoxDecoration(border: Border.all(width: 1)),
                            child: Center(
                              child: Text(
                                '发布记录',
                                textScaler:
                                    BdhTextScaler(textScaleFactor: fontScale),
                                style: TextStyle(
                                    fontSize: 16.px,
                                    fontWeight: FontWeight.w600),
                              ),
                            ))),

                    Expanded(
                      flex: 2,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          //字号按钮
                          Container(
                            padding: EdgeInsets.only(right: 15.px),
                            child: GestureDetector(
                              onTap: () {
                                showModalBottomSheet(
                                    backgroundColor: Colors.transparent,
                                    useSafeArea: true,
                                    context: context,
                                    builder: (ctx) {
                                      return BdhChangeFont(
                                        // bigfont: bigfont,
                                        fontScaleCallBack: (isBigFont) {
                                          // setState(() {
                                          //   bigfont = isBigFont;
                                          //   fontScale =
                                          //       bigfont ? 1.24 : 1.0;
                                          // });
                                        },
                                      );
                                    });
                                // setState(() {
                                //   // showBigFont = !showBigFont;
                                // });
                              },
                              child: Image.asset(
                                  width: 26.px,
                                  height: 26.px,
                                  ImageHelper.wrapAssets("font_size.png")),
                            ),
                          ),

                          //搜索按钮
                          Container(
                            padding: EdgeInsets.only(right: 10.px),
                            child: GestureDetector(
                              onTap: () {
                                Navigator.of(context).push(
                                    CupertinoPageRoute(builder: (context) {
                                  // return const SearchNewPage();
                                  return const NewMessagePublishRecordSearch();
                                }));
                              },
                              child: Image.asset(
                                  width: 24.px,
                                  height: 24.px,
                                  ImageHelper.wrapAssets("findBalckIcon.png")),

                              // /font_size
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                Center(
                    child: Container(
                        width: 375.px,
                        height: 1,
                        color: const Color.fromRGBO(226, 235, 231, 0.4))),

                Expanded(
                  child: messageRecordList.isEmpty
                      ? const BdhEmptyView()
                      : SmartRefresher(
                          enablePullDown: true,
                          enablePullUp: true,
                          onRefresh: _onRefresh,
                          onLoading: _onLoading,
                          controller: _refreshController,
                          header: const BdhRefreshHeader(),
                          footer: BdhRefreshFooter(
                              mode: _refreshController.footerStatus!,
                              isAllDataLoaded: _isAllDataLoaded),
                          child: ListView.separated(
                            padding: EdgeInsets.only(top: 0.px),
                            shrinkWrap: true,
                            scrollDirection: Axis.vertical,
                            itemCount: messageRecordList.length + 1,
                            itemBuilder: (BuildContext context, int index) {
                              if (index == messageRecordList.length) {
                                return Container();
                              } else {
                                NewsMessageModel model =
                                    messageRecordList[index];
                                return GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () {
                                      Logger().i('点击cell行----$index');
                                      goToAction(model);
                                    },
                                    child: messageRecordItem(model));
                              }
                            },
                            separatorBuilder:
                                (BuildContext context, int index) {
                              return Container(
                                  width: 375.px,
                                  height: 8.px,
                                  color:
                                      const Color.fromRGBO(226, 235, 231, 0.2));
                            },
                          ),
                        ),
                )
              ],
            ),
          ),
          Positioned(
            child: _showLoading
                ? const Center(
                    child: SpinKitCircle(
                      // color: HexColor('#16B760'),
                      color: Color.fromRGBO(0, 127, 255, 1),
                      size: 50.0,
                    ),
                  )
                : Container(),
          )
        ],
      ),
    );
  } //end build

  Widget messageRecordItem(NewsMessageModel model) {
    String statusStr = '';
    // 1-待发布 2-审核中 3-已驳回 4-已发布 5-已撤回
    if (model.dataStatus == '1') {
      statusStr = '待发布';
    } else if (model.dataStatus == '2') {
      statusStr = '审核中';
    } else if (model.dataStatus == '3') {
      statusStr = '已驳回';
    } else if (model.dataStatus == '4') {
      statusStr = '已发布';
    } else if (model.dataStatus == '5') {
      statusStr = '已撤回';
    } else {
      statusStr = '待发布';
    }

    String unReadCountStr = '';
    List<ReadStatusStatisticModel> readList = model.readStatusStatistic ?? [];
    if (readList.isNotEmpty) {
      ReadStatusStatisticModel model = readList.first;
      unReadCountStr = '${model.cnt ?? 0}${model.name ?? ''}';
    }

    return Container(
      // padding: EdgeInsets.symmetric(horizontal: 15.px),
      padding:
          EdgeInsets.only(top: 15.px, bottom: 15.px, left: 15.px, right: 15.px),
      // decoration: BoxDecoration(border: Border.all(width: 1)),
      // height: 120.px,
      width: 375.px,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  model.title ?? '',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  textAlign: TextAlign.start,
                  style: TextStyle(
                    fontSize: 20.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#31394C'),
                  ),
                ),
              )
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  // model.content ?? '',
                  model.contentStr ?? '',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w300,
                    color: const Color.fromRGBO(0, 0, 0, 0.6),
                    fontFamily: 'PingFang HK',
                  ),
                ),
              )
            ],
          ),
          SizedBox(height: 20.px),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  flex: 1,
                  child: SizedBox(
                    height: 30.px,
                    child: SizedBox(
                      // decoration: BoxDecoration(border: Border.all(width: 1)),
                      // width: 165.px,
                      height: 32.px,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.only(left: 5.px, right: 5.px),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(2.px),
                              color: const Color.fromRGBO(226, 235, 231, 0.4),
                            ),
                            child: Text(
                              statusStr,
                              textScaler:
                                  BdhTextScaler(textScaleFactor: fontScale),
                              style: TextStyle(
                                fontSize: 14.px,
                                fontWeight: FontWeight.w400,
                                color: HexColor('#A8B8B1'),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  )),
              Expanded(
                  flex: 3,
                  child: SizedBox(
                    // color: Colors.red,
                    height: 30.px,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        model.dataStatus == '4' &&
                                (model.readStatusStatistic ?? []).isNotEmpty
                            ? Text(
                                // '999未读',
                                unReadCountStr,
                                textScaler:
                                    BdhTextScaler(textScaleFactor: fontScale),
                                style: TextStyle(
                                  fontSize: 14.px,
                                  fontWeight: FontWeight.w500,
                                  color: const Color.fromRGBO(255, 184, 0, 1),
                                ),
                              )
                            : Container(),
                        SizedBox(
                          width: 10.px,
                        ),
                        Text(
                          model.createTime ?? '',
                          overflow: TextOverflow.ellipsis,
                          textScaler: BdhTextScaler(textScaleFactor: fontScale),
                          style: TextStyle(
                            fontSize: 14.px,
                            fontWeight: FontWeight.w500,
                            color: const Color.fromRGBO(0, 0, 0, 0.3),
                          ),
                        )
                      ],
                    ),
                  ))
            ],
          ),

          (model.dataStatus == '1' ||
                  model.dataStatus == '3' ||
                  model.dataStatus == '5')
              ? needPubishedRow(model, statusStr)
              : (model.dataStatus == '4'
                  ? pubishedRow(model, statusStr)
                  : Container()),
          // (model.dataStatus == '1' ||
          //         model.dataStatus == '3' ||
          //         model.dataStatus == '5')
          //     ? needPubishedRow(model, statusStr)
          //     : (model.dataStatus == '2'
          //         ? onExamineRow(model, statusStr)
          //         : (model.dataStatus == '4'
          //             ? pubishedRow(model, statusStr)
          //             : Container())),
        ],
      ),
    );
  }

// 1-待发布 2-审核中 3-已驳回 4-已发布 5-已撤回
  goToAction(NewsMessageModel model) {
    if (model.dataStatus == '4') {
      //已经发布 进详情
      Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
        return NewPublishHtmlDitail(model: model);
        // return NewPublishDitail(model: model);
      })).then((res) {
        getMessageRecordListDate();
      });
    }

    // if (model.dataStatus == '1') {
    //   Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
    //     return NewPublishPage(model: model);
    //   }));
    // } else if (model.dataStatus == '2') {
    // } else if (model.dataStatus == '3') {
    // } else if (model.dataStatus == '4') {
    // } else if (model.dataStatus == '5') {
    // } else {}
  }

//撤回消息
// revokeMes(model);

// 审核中2 [撤回消息]
  Widget onExamineRow(NewsMessageModel model, String statusStr) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          onTap: () {
            revokeMes(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 165.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  width: 20.px,
                  height: 20.px,
                  ImageHelper.wrapAssets('replyIcon.png'),
                ),
                SizedBox(
                  width: 3.px,
                ),
                Text(
                  '撤回消息',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

// 待发布1, 已撤回5 , 已驳回 [编辑, 删除, 提交审核]
  Widget needPubishedRow(NewsMessageModel model, String statusStr) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        //1.编辑
        GestureDetector(
          onTap: () {
            edit(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 108.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '编辑',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                ),
              ],
            ),
          ),
        ),
        //删除
        GestureDetector(
          onTap: () {
            deletMes(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 108.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '删除',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                ),
              ],
            ),
          ),
        ),
        //提交审核
        GestureDetector(
          onTap: () {
            commitMes(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 108.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '提交审核',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

// 已发布4 [撤回消息, 分享微信]
  Widget pubishedRow(NewsMessageModel model, String statusStr) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          onTap: () {
            revokeMes(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 165.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                model.dataStatus == '4'
                    ? Image.asset(
                        width: 20.px,
                        height: 20.px,
                        ImageHelper.wrapAssets('replyIcon.png'),
                      )
                    : Container(),
                SizedBox(
                  width: 3.px,
                ),
                Text(
                  '撤回消息',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                )
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            gotoShareWechat(model);
          },
          child: Container(
            decoration: BoxDecoration(
                color: const Color.fromRGBO(22, 183, 96, 0.1),
                border: Border.all(
                  width: 1,
                  color: const Color.fromRGBO(226, 235, 231, 0.6),
                )),
            width: 165.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  width: 20.px,
                  height: 20.px,
                  ImageHelper.wrapAssets('wechatIcom.png'),
                ),
                SizedBox(
                  width: 3.px,
                ),
                Text(
                  '发送消息到微信',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#16B760'),
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }

// ---- evnet below -----

  //下拉刷新
  void _onRefresh() async {
    Logger().i('下拉刷新');
    _pageNum = 1;
    setState(() {
      _isAllDataLoaded = true;
    });
    getMessageRecordListDate();
    _refreshController.refreshCompleted();
  }

//上拉加载
  void _onLoading() async {
    _pageNum += 1;
    Logger().i('上拉加载');
    if (_pageNum > _totalPage) {
      _pageNum = _totalPage;
      setState(() {
        _isAllDataLoaded = true;
      });
      _refreshController.loadComplete();
      return;
    } else {
      setState(() {
        _isAllDataLoaded = false;
      });
    }
    getMessageRecordListDate();
    _refreshController.loadComplete();
  }

//获取发布历史列表
  getMessageRecordListDate() {
    // EasyLoading.show();
    setState(() {
      _showLoading = true;
    });
    MessageNewResponsitory.getMessageRecordListDate({
      "pageNo": _pageNum,
      "pageSize": _pageSize,
    }).then((res) {
      // EasyLoading.dismiss();
      setState(() {
        _showLoading = false;
      });
      Logger().i('获取发布历史列表');
      Logger().i(res);
      final recordsDataList = res['data']['dataList'];
      _totalPage = res['data']['pageTotal']; // 总页数
      List<NewsMessageModel> tempModelList = [];
      for (int i = 0; i < recordsDataList.length; i++) {
        NewsMessageModel model = NewsMessageModel.fromJson(recordsDataList[i]);
        model.contentStr = model.content ?? '';
        RegExp regExp = RegExp(r'>([^<]+)</');
        Iterable<Match> matches = regExp.allMatches(model.content ?? '');
        if (matches.isNotEmpty) {
          for (var match in matches) {
            String contentStr = match.group(1)!;
            model.contentStr = contentStr;
            break;
          }
        }
        tempModelList.add(model);
      }
      if (_pageNum == 1) {
        messageRecordList = [];
        setState(() {
          messageRecordList = tempModelList;
        });
      } else {
        setState(() {
          messageRecordList.addAll(tempModelList);
        });
      }
    });
  }

  Future<bool> isWXInstalled() async {
    var result = await fluwx.isWeChatInstalled;
    if (!result) {
      showToast("无法打开微信 请检查是否安装了微信");
      return false;
    }
    return true;
  }

  //微信分享
  gotoShareWechat(NewsMessageModel itemModel) {
    Logger().i(' 微信分享');
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        useSafeArea: true,
        context: context,
        builder: (ctx) {
          return BdhShareView(
            shareWechatCallBack: () async {
              if (kDebugMode) {
                print('点击微信');
              }
              if (await isWXInstalled()) {
                //分享后打开的图文连接
                String linkUrl =
                    "${urlConfig.h5}/argic/notLoggedPage?noticeId=${itemModel.noticeId}";
                //分享的小图片
                // String imageUrl =
                //     "${urlConfig.microfront}${widget.model.image!.split(",").first}";

                /// 分享到好友
                var model = WeChatShareWebPageModel(
                  //链接
                  linkUrl,
                  //标题
                  title: itemModel.title ?? '',
                  //摘要
                  description: itemModel.content ?? "",
                  //小图
                  // thumbnail: WeChatImage.network(imageUrl),
                  //微信消息
                  scene: WeChatScene.session,
                );

                fluwx.share(model);
              }
            },
            shareWechatMomentsCallBack: () async {
              if (kDebugMode) {
                print('点击微信朋友圈');
              }
              if (await isWXInstalled()) {
                //分享后打开的图文连接
                String linkUrl =
                    "${urlConfig.h5}/argic/notLoggedPage?noticeId=${itemModel.noticeId}";
                //分享的小图片
                // String imageUrl =
                //     "${urlConfig.microfront}${widget.model.image!.split(",").first}";

                /// 分享到好友
                var model = WeChatShareWebPageModel(
                  //链接
                  linkUrl,
                  //标题
                  title: itemModel.title ?? '',
                  //摘要
                  description: itemModel.content ?? "",
                  //小图
                  // thumbnail: WeChatImage.network(imageUrl),
                  //微信消息
                  scene: WeChatScene.timeline,
                );

                fluwx.share(model);
                // BDHResponsitory.shareToWeChat({
                //   "contentUid": ditailModel.uid,
                //   "contentType": ditailModel.contentType,
                //   "forwardClick": 1
                // }).then((res) {
                //   print('分享完成');
                // });
              }
            },
          );
        });
  }

  //撤回net
  revokeMes(NewsMessageModel model) {
    MessageNewResponsitory.revokeMes(model.noticeId ?? 0).then((res) {
      Logger().i('撤回通知');
      Logger().i(res);
      if (res['code'] == 0 && res['success'] == true) {
        showToast('${res['msg']}');
        _onRefresh();
      }
    });
  }

  //删除net
  deletMes(NewsMessageModel model) {
    MessageNewResponsitory.deletMes(model.noticeId ?? 0).then((res) {
      Logger().i('删除通知');
      Logger().i(res);
      if (res['code'] == 0 && res['success'] == true) {
        showToast('${res['msg']}');
        _onRefresh();
      }
    });
  }

  //提交审核net
  commitMes(NewsMessageModel model) {
    MessageNewResponsitory.commitMsg(model.noticeId ?? 0).then((res) {
      if (res['code'] == 0 && res['success'] == true) {
        showToast('${res['msg']}');
        _onRefresh();
      }
    });
  }

  //编辑
  edit(NewsMessageModel model) {
    Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
      return NewPublishPage(model: model);
    })).then((res) {
      getMessageRecordListDate();
    });
  }
} //end status
