import 'package:bdh_smart_agric_app/pages/home/<USER>/news_subview/bdh_text_scaler.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_view.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/pubulish_msg/new_publish_html_ditail.dart';
import 'package:bdh_smart_agric_app/pages/message/message_new/pubulish_msg/new_publish_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
// import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:logger/web.dart';
import 'package:oktoast/oktoast.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../components/bdh_share_view.dart';
import '../../../../components/refresh_header_footer/bdh_refresh_footer.dart';
import '../../../../components/refresh_header_footer/bdh_refresh_header.dart';
import '../../../../utils/color_util.dart';
import '../../../../utils/image_util.dart';
import '../../../../utils/request/message_new_service.dart';
import '../../../../viewmodel/font_scale_model.dart';
import '../model/news_message_model.dart';

class NewMessagePublishRecordSearch extends StatefulWidget {
  const NewMessagePublishRecordSearch({super.key});

  @override
  State<NewMessagePublishRecordSearch> createState() =>
      _NewMessagePublishRecordSearchState();
}

class _NewMessagePublishRecordSearchState
    extends State<NewMessagePublishRecordSearch> {
  int _totalPage = 1; // 默认总页数为1页
  int _pageNum = 1;
  final int _pageSize = 10;
  bool _isAllDataLoaded = false;
  bool _showLoading = false;
  late List<NewsMessageModel> messageRecordList = [];
  bool showDeletIcon = false;
  late String searchKeyWord = '';
  late int index = 0;
  double fontScale = 1.0;
  final FocusScopeNode focusScopeNode = FocusScopeNode();
  TextEditingController searchController = TextEditingController();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    getMessageRecordListDate();
    searchController.addListener(() {
      setState(() {
        showDeletIcon = searchController.text.isNotEmpty ? true : false;
        searchKeyWord = searchController.text;
      });
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    double fontScaleA = context.watch<FontScaleModel>().fontScale;
    setState(() {
      fontScale = fontScaleA;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          leading: IconButton(
            onPressed: () {
              focusScopeNode.unfocus();
              Future.delayed(const Duration(milliseconds: 300), () {
                Navigator.of(context).pop();
              });
            },
            icon: Image.asset(
                width: 22.px,
                height: 22.px,
                ImageHelper.wrapAssets("backBlackIcon.png")),
          ),
          actions: [
            GestureDetector(
              onTap: () {
                Logger().i('搜索');
                if (searchController.text.isEmpty) {
                  showToast('请输入搜索内容');
                  return;
                }
                focusScopeNode.unfocus();
                setState(() {
                  searchKeyWord = searchController.text;
                });
                getMessageRecordListDate();
              },
              child: Text(
                '搜索',
                textScaler: BdhTextScaler(textScaleFactor: fontScale),
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w600,
                  color: HexColor('#16B760'),
                ),
              ),
            ),
            SizedBox(
              width: 15.px,
            ),
          ],
          title: searchView(),
        ),
        body: messageRecordList.isEmpty
            ? const BdhEmptyView()
            : Stack(
                children: [
                  SmartRefresher(
                    enablePullDown: true,
                    enablePullUp: true,
                    onRefresh: _onRefresh,
                    onLoading: _onLoading,
                    controller: _refreshController,
                    header: const BdhRefreshHeader(),
                    footer: BdhRefreshFooter(
                        mode: _refreshController.footerStatus!,
                        isAllDataLoaded: _isAllDataLoaded),
                    child: ListView.separated(
                      padding: EdgeInsets.only(top: 0.px),
                      shrinkWrap: true,
                      scrollDirection: Axis.vertical,
                      itemCount: messageRecordList.length + 1,
                      itemBuilder: (BuildContext context, int index) {
                        if (index == messageRecordList.length) {
                          return Container();
                        } else {
                          NewsMessageModel model = messageRecordList[index];
                          return GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                Logger().i('点击cell行----$index');
                                goToAction(model);
                              },
                              child: messageRecordItem(model));
                        }
                      },
                      separatorBuilder: (BuildContext context, int index) {
                        return Container(
                            width: 375.px,
                            height: 8.px,
                            color: const Color.fromRGBO(226, 235, 231, 0.2));
                      },
                    ),
                  ),
                  Positioned(
                    child: _showLoading
                        ? const Center(
                            child: SpinKitCircle(
                              // color: HexColor('#16B760'),
                              color: Color.fromRGBO(0, 127, 255, 1),
                              size: 50.0,
                            ),
                          )
                        : Container(),
                  )
                ],
              ));
  } //end buid

// 搜索视图
  Widget searchView() {
    return Container(
      padding: EdgeInsets.only(left: 12.px, right: 12.px),
      height: 32.px,
      // width: 300.px,
      decoration: BoxDecoration(
        // border: Border.all(width: 1),
        borderRadius: BorderRadius.circular(21),
        color: BDHColor.black03,
      ),
      child: Row(
        children: [
          Image.asset(
            width: 24.px,
            height: 24.px,
            ImageHelper.wrapAssets('search_grey.png'),
          ),
          SizedBox(
            width: 5.px,
          ),
          Expanded(
            child: CupertinoTextField.borderless(
              style: TextStyle(
                fontSize: 13.px,
                color: HexColor('#000000'),
                fontWeight: FontWeight.w400,
              ),
              textInputAction: TextInputAction.search,
              padding: EdgeInsets.zero,
              controller: searchController,
              placeholder: "请输入搜索内容",
              placeholderStyle: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w500,
                  color: HexColor('#A8B8B1')),
              focusNode: focusScopeNode,
              // autofocus: true,
              // onTapOutside: (e) => {focusNode.unfocus()},
              // onChanged: (e) => {},
              onEditingComplete: () {
                Logger().i('textfiled 点击--onEditingComplete----');
                setState(() {
                  searchKeyWord = searchController.text;
                });
                getMessageRecordListDate();
                focusScopeNode.unfocus();
              },
            ),
          ),
          SizedBox(
            width: 5.px,
          ),
          showDeletIcon
              ? GestureDetector(
                  onTap: () {
                    searchController.text = '';
                    getMessageRecordListDate();
                  },
                  child: Image.asset(
                      width: 16.px,
                      height: 16.px,
                      ImageHelper.wrapAssets('delteIcons.png')),
                )
              : Container(),
        ],
      ),
    );
  }

  Widget messageRecordItem(NewsMessageModel model) {
    String statusStr = '';
    // 1-待发布 2-审核中 3-已驳回 4-已发布 5-已撤回
    if (model.dataStatus == '1') {
      statusStr = '待发布';
    } else if (model.dataStatus == '2') {
      statusStr = '审核中';
    } else if (model.dataStatus == '3') {
      statusStr = '已驳回';
    } else if (model.dataStatus == '4') {
      statusStr = '已发布';
    } else if (model.dataStatus == '5') {
      statusStr = '已撤回';
    } else {
      statusStr = '待发布';
    }

    String unReadCountStr = '';
    List<ReadStatusStatisticModel> readList = model.readStatusStatistic ?? [];
    if (readList.isNotEmpty) {
      ReadStatusStatisticModel model = readList.first;
      unReadCountStr = '${model.cnt ?? 0}${model.name ?? ''}';
    }
    return Container(
      // padding: EdgeInsets.symmetric(horizontal: 15.px),
      padding:
          EdgeInsets.only(top: 15.px, bottom: 15.px, left: 15.px, right: 15.px),
      // decoration: BoxDecoration(border: Border.all(width: 1)),
      // height: 120.px,
      width: 375.px,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  model.title ?? '',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  textAlign: TextAlign.start,
                  style: TextStyle(
                    fontSize: 20.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#31394C'),
                  ),
                ),
              )
            ],
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  model.content ?? '',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w300,
                    color: const Color.fromRGBO(0, 0, 0, 0.6),
                    fontFamily: 'PingFang HK',
                  ),
                ),
              )
            ],
          ),
          SizedBox(height: 20.px),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                  flex: 1,
                  child: SizedBox(
                    height: 30.px,
                    child: SizedBox(
                      // decoration: BoxDecoration(border: Border.all(width: 1)),
                      // width: 165.px,
                      height: 32.px,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.only(left: 5.px, right: 5.px),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(2.px),
                              color: const Color.fromRGBO(226, 235, 231, 0.4),
                            ),
                            child: Text(
                              statusStr,
                              textScaler:
                                  BdhTextScaler(textScaleFactor: fontScale),
                              style: TextStyle(
                                fontSize: 14.px,
                                fontWeight: FontWeight.w400,
                                color: HexColor('#A8B8B1'),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  )),
              Expanded(
                  flex: 3,
                  child: SizedBox(
                    height: 30.px,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          // '999未读',
                          unReadCountStr,
                          textScaler: BdhTextScaler(textScaleFactor: fontScale),
                          style: TextStyle(
                            fontSize: 14.px,
                            fontWeight: FontWeight.w500,
                            color: const Color.fromRGBO(255, 184, 0, 1),
                          ),
                        ),
                        SizedBox(
                          width: 10.px,
                        ),
                        Text(
                          model.createTime ?? '',
                          textScaler: BdhTextScaler(textScaleFactor: fontScale),
                          style: TextStyle(
                            fontSize: 14.px,
                            fontWeight: FontWeight.w500,
                            color: const Color.fromRGBO(0, 0, 0, 0.3),
                          ),
                        )
                      ],
                    ),
                  ))
            ],
          ),
          (model.dataStatus == '1' ||
                  model.dataStatus == '3' ||
                  model.dataStatus == '5')
              ? needPubishedRow(model, statusStr)
              : (model.dataStatus == '4'
                  ? pubishedRow(model, statusStr)
                  : Container()),
        ],
      ),
    );
  }

// 1-待发布 2-审核中 3-已驳回 4-已发布 5-已撤回
  goToAction(NewsMessageModel model) {
    if (model.dataStatus == '4') {
      //已经发布 进详情
      Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
        return NewPublishHtmlDitail(model: model);
        // return const NewMessagePublishRecordSearch();
      })).then((res) {
        getMessageRecordListDate();
      });
    }

    // if (model.dataStatus == '1') {
    //   Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
    //     return NewPublishPage(model: model);
    //   }));
    // } else if (model.dataStatus == '2') {
    // } else if (model.dataStatus == '3') {
    // } else if (model.dataStatus == '4') {
    // } else if (model.dataStatus == '5') {
    // } else {}
  }

//撤回消息
// revokeMes(model);

// 审核中2 [撤回消息]
  Widget onExamineRow(NewsMessageModel model, String statusStr) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        GestureDetector(
          onTap: () {
            revokeMes(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 165.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  width: 20.px,
                  height: 20.px,
                  ImageHelper.wrapAssets('replyIcon.png'),
                ),
                SizedBox(
                  width: 3.px,
                ),
                Text(
                  '撤回消息',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

// 待发布1, 已撤回5 , 已驳回 [编辑, 删除, 提交审核]
  Widget needPubishedRow(NewsMessageModel model, String statusStr) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        //1.编辑
        GestureDetector(
          onTap: () {
            edit(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 108.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '编辑',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                ),
              ],
            ),
          ),
        ),
        //删除
        GestureDetector(
          onTap: () {
            deletMes(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 108.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '删除',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                ),
              ],
            ),
          ),
        ),
        //提交审核
        GestureDetector(
          onTap: () {
            commitMes(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 108.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '提交审核',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

// 已发布4 [撤回消息, 分享微信]
  Widget pubishedRow(NewsMessageModel model, String statusStr) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          onTap: () {
            revokeMes(model);
          },
          child: Container(
            decoration: BoxDecoration(
                border: Border.all(
              width: 1,
              color: const Color.fromRGBO(226, 235, 231, 0.6),
            )),
            width: 165.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                model.dataStatus == '4'
                    ? Image.asset(
                        width: 20.px,
                        height: 20.px,
                        ImageHelper.wrapAssets('replyIcon.png'),
                      )
                    : Container(),
                SizedBox(
                  width: 3.px,
                ),
                Text(
                  '撤回消息',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#000000'),
                  ),
                )
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            gotoShareWechat();
          },
          child: Container(
            decoration: BoxDecoration(
                color: const Color.fromRGBO(22, 183, 96, 0.1),
                border: Border.all(
                  width: 1,
                  color: const Color.fromRGBO(226, 235, 231, 0.6),
                )),
            width: 165.px,
            height: 32.px,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  width: 20.px,
                  height: 20.px,
                  ImageHelper.wrapAssets('wechatIcom.png'),
                ),
                SizedBox(
                  width: 3.px,
                ),
                Text(
                  '发送消息到微信',
                  textScaler: BdhTextScaler(textScaleFactor: fontScale),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600,
                    color: HexColor('#16B760'),
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }

// ---- evnet below -----

  //下拉刷新
  void _onRefresh() async {
    Logger().i('下拉刷新');
    _pageNum = 1;
    setState(() {
      _isAllDataLoaded = true;
    });
    getMessageRecordListDate();
    _refreshController.refreshCompleted();
  }

//上拉加载
  void _onLoading() async {
    _pageNum += 1;
    Logger().i('上拉加载');
    if (_pageNum > _totalPage) {
      _pageNum = _totalPage;
      setState(() {
        _isAllDataLoaded = true;
      });
      _refreshController.loadComplete();
      return;
    } else {
      setState(() {
        _isAllDataLoaded = false;
      });
    }
    getMessageRecordListDate();
    _refreshController.loadComplete();
  }

//获取发布历史列表
  getMessageRecordListDate() {
    // EasyLoading.show();
    setState(() {
      _showLoading = true;
    });
    MessageNewResponsitory.getMessageRecordListDate({
      "pageNo": _pageNum,
      "pageSize": _pageSize,
      "keyword": searchKeyWord,
    }).then((res) {
      setState(() {
        _showLoading = false;
      });
      // EasyLoading.dismiss();
      Logger().i('获取发布历史列表');
      Logger().i(res);
      final recordsDataList = res['data']['dataList'];
      _totalPage = res['data']['pageTotal']; // 总页数
      List<NewsMessageModel> tempModelList = [];
      for (int i = 0; i < recordsDataList.length; i++) {
        NewsMessageModel model = NewsMessageModel.fromJson(recordsDataList[i]);
        model.contentStr = model.content ?? '';
        RegExp regExp = RegExp(r'>([^<]+)</');
        Iterable<Match> matches = regExp.allMatches(model.content ?? '');
        if (matches.isNotEmpty) {
          for (var match in matches) {
            String contentStr = match.group(1)!;
            model.contentStr = contentStr;
            break;
          }
        }
        tempModelList.add(model);
      }
      if (_pageNum == 1) {
        messageRecordList = [];
        setState(() {
          messageRecordList = tempModelList;
        });
      } else {
        setState(() {
          messageRecordList.addAll(tempModelList);
        });
      }
      Future.delayed(const Duration(microseconds: 50), () {
        if (mounted) {
          FocusScope.of(context).requestFocus(focusScopeNode);
        }
      });
    });
  }

  //微信分享
  gotoShareWechat() {
    Logger().i(' 微信分享');
    showModalBottomSheet(
        backgroundColor: Colors.transparent,
        useSafeArea: true,
        context: context,
        builder: (ctx) {
          return BdhShareView(
            shareWechatCallBack: () {
              if (kDebugMode) {
                print('点击微信');
              }
            },
            shareWechatMomentsCallBack: () {
              if (kDebugMode) {
                print('点击微信朋友圈');
              }
            },
          );
        });
  }

  //撤回net
  revokeMes(NewsMessageModel model) {
    MessageNewResponsitory.revokeMes(model.noticeId ?? 0).then((res) {
      Logger().i('撤回通知');
      Logger().i(res);
    });
  }

  //删除net
  deletMes(NewsMessageModel model) {
    MessageNewResponsitory.deletMes(model.noticeId ?? 0).then((res) {
      Logger().i('删除通知');
      Logger().i(res);
      if (res['code'] == 0 && res['success'] == true) {
        showToast('${res['msg']}');
        _onRefresh();
      }
    });
  }

  //提交审核net
  commitMes(NewsMessageModel model) {
    MessageNewResponsitory.commitMsg(model.noticeId ?? 0).then((res) {
      if (res['code'] == 0 && res['success'] == true) {
        showToast('${res['msg']}');
        _onRefresh();
      }
    });
  }

  //编辑
  edit(NewsMessageModel model) {
    Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
      return NewPublishPage(model: model);
    })).then((res) {
      getMessageRecordListDate();
    });
  }
} // end state


