import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:easy_stepper/easy_stepper.dart';
import 'package:flutter/material.dart';

class ForgetPwdStep extends StatefulWidget {
  int activeStep;
  ForgetPwdStep({super.key, required this.activeStep});

  @override
  State<ForgetPwdStep> createState() => _ForgetPwdStepState();
}

class _ForgetPwdStepState extends State<ForgetPwdStep> {
  // int activeStep = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          EasyStepper(
            activeStep: widget.activeStep,
            alignment: Alignment.center,
            lineStyle: LineStyle(
              lineLength: 70.px,
              lineWidth: 20,
              // lineSpace: 20,
              lineType: LineType.normal,
              lineThickness: 2,
              defaultLineColor: const Color.fromRGBO(131, 149, 142, 0.1),
              // lineThickness: 10.px,
            ),
            activeStepTextColor: const Color.fromRGBO(0, 0, 0, 1),
            finishedStepTextColor: const Color.fromRGBO(0, 0, 0, 1),
            // internalPadding: 0,
            showLoadingAnimation: false,
            stepRadius: 8,
            showStepBorder: false,
            steps: [
              EasyStep(
                customStep: widget.activeStep == 0
                    ? Image.asset(ImageHelper.wrapAssets('stepActivityImg.png'))
                    : Image.asset(
                        ImageHelper.wrapAssets('stepUnActivityImg.png')),
                customTitle: getCustomTitleView('信息输入', widget.activeStep, 0),
              ),
              EasyStep(
                customStep: widget.activeStep == 1
                    ? Image.asset(ImageHelper.wrapAssets('stepActivityImg.png'))
                    : Image.asset(
                        ImageHelper.wrapAssets('stepUnActivityImg.png')),
                customTitle: getCustomTitleView('人身核验', widget.activeStep, 1),
              ),
              EasyStep(
                customStep: widget.activeStep == 2
                    ? Image.asset(ImageHelper.wrapAssets('stepActivityImg.png'))
                    : Image.asset(
                        ImageHelper.wrapAssets('stepUnActivityImg.png')),
                customTitle: getCustomTitleView('新密码', widget.activeStep, 2),
              ),
              EasyStep(
                customStep: widget.activeStep == 3
                    ? Image.asset(ImageHelper.wrapAssets('stepActivityImg.png'))
                    : Image.asset(
                        ImageHelper.wrapAssets('stepUnActivityImg.png')),
                customTitle: getCustomTitleView('成功', widget.activeStep, 3),
              ),
            ],
            // onStepReached: (index) => setState(() => activeStep = index),
          ),
          // GestureDetector(
          //   onTap: () {
          //     setState(() {
          //       activeStep += 1;
          //       if (activeStep > 4) {
          //         activeStep = 0;
          //       }
          //     });
          //   },
          //   child: Container(
          //     height: 50,
          //     width: 100,
          //     color: Colors.red,
          //   ),
          // )
        ],
      ),
    );
  }

  Widget getStepView(int activeStep, int current) {
    return Container(
      decoration: BoxDecoration(border: Border.all(width: 1)),
      // height: 40.px,
      // width: 40.px,
      padding: EdgeInsets.only(bottom: 15.px),
      child: activeStep == current
          ? Image.asset(ImageHelper.wrapAssets('stepActivityImg.png'))
          : Image.asset(ImageHelper.wrapAssets('stepUnActivityImg.png')),
    );
  }

  Widget getCustomTitleView(String info, int activeStep, int current) {
    return Container(
        // decoration: BoxDecoration(border: Border.all(width: 1)),
        padding: EdgeInsets.only(top: 10.px),
        child: Center(
          child: Text(
            info,
            style: current == activeStep
                ? TextStyle(
                    color: Colors.black,
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600)
                : TextStyle(
                    color: Color.fromRGBO(0, 0, 0, 0.2),
                    fontSize: 14.px,
                    fontWeight: FontWeight.w600),
          ),
        ));
  }
}
