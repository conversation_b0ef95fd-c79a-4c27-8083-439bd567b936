import 'dart:io';

import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';

class BdhDigitalIdentityCardUploadView extends FormField<IDCardItem> {
  final String? netImgUrl;
  final String mainTitle;
  final String subTitle;
  final String imageName;
  final Function()? clickedCloseBtn;
  final Function()? clickedOpen;
  BdhDigitalIdentityCardUploadView({
    super.key,
    super.initialValue,
    super.validator,
    super.onSaved,
    this.netImgUrl,
    required this.mainTitle,
    required this.subTitle,
    required this.imageName,
    this.clickedCloseBtn,
    this.clickedOpen,
  }) : super(builder: (field) {
          openImage(ImageSource type, ImageType imageType) {
            ImagePicker picker = ImagePicker();

            picker.pickImage(source: type, imageQuality: 10).then((image) {
              Navigator.of(field.context).pop();
              //图片上传
              if (image != null) {
                // XFile processedImage =
                RotateImageUtil.rotateImageIfNeeded(File(image.path))
                    .then((processedImage) {
                  processedImage.readAsBytes().then((bytes) {
                    String? mimeType = lookupMimeType(processedImage.name);
                    if (mimeType != null) {
                      FormData postData = FormData.fromMap({
                        "file": MultipartFile.fromBytes(bytes,
                            filename: processedImage.name,
                            contentType: MediaType(mimeType.split("/").first,
                                mimeType.split("/").last))
                      });

                      BdhDigitalService.uploadFileBdhDigital(postData)
                          .then((value) {
                        if (imageType == ImageType.portrait) {
                          field.value!.portrait = value;
                          field.didChange(field.value);
                        } else {
                          field.value!.nationalEmblem = value;
                          field.didChange(field.value);
                        }
                      });
                    } else {
                      showToast("未知文件");
                    }
                  });
                });

                // image.readAsBytes().then((bytes) {
                //   String? mimeType = lookupMimeType(image.name);
                //   if (mimeType != null) {
                //     FormData postData = FormData.fromMap({
                //       "file": MultipartFile.fromBytes(bytes,
                //           filename: image.name,
                //           contentType: MediaType(mimeType.split("/").first,
                //               mimeType.split("/").last))
                //     });

                //     BdhDigitalService.uploadFileBdhDigital(postData)
                //         .then((value) {
                //       if (imageType == ImageType.portrait) {
                //         field.value!.portrait = value;
                //         field.didChange(field.value);
                //       } else {
                //         field.value!.nationalEmblem = value;
                //         field.didChange(field.value);
                //       }
                //     });
                //   } else {
                //     showToast("未知文件");
                //   }
                // });
              }
            });
          }

          return Container(
            height: 128.px,
            width: 311.px,
            decoration: BoxDecoration(
                color: const Color.fromRGBO(242, 242, 246, 1),
                // color: Colors.red,
                borderRadius: BorderRadius.circular(8.px)),
            child: Row(
              children: [
                Expanded(
                    flex: 1,
                    child: Container(
                      padding: EdgeInsets.only(left: 12.px),
                      decoration: const BoxDecoration(
                          // color: Color.fromRGBO(242, 242, 246, 1),
                          ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            mainTitle,
                            style: const TextStyle(
                                fontSize: 16, fontWeight: FontWeight.w600),
                          ),
                          Text(
                            subTitle,
                            style: const TextStyle(
                                fontSize: 12, fontWeight: FontWeight.w400),
                          ),
                        ],
                      ),
                    )),
                Expanded(
                  flex: 1,
                  child: GestureDetector(
                    onTap: () {
                      if (clickedOpen != null) {
                        clickedOpen();
                      }
                      showModalBottomSheet(
                          backgroundColor: Colors.transparent,
                          useSafeArea: true,
                          context: field.context,
                          builder: (ctx) {
                            return CameraPhotoSelect(sourceSelect: (e) {
                              openImage(e, ImageType.portrait);
                            });
                          });
                    },
                    child: Container(
                        padding: EdgeInsets.all(10.px),
                        child: field.value?.portrait == null && netImgUrl == ''
                            ? Image.asset(
                                width: 152.px,
                                height: 108.px,
                                ImageHelper.wrapAssets(imageName),
                              )
                            : SizedBox(
                                width: 152.px,
                                height: 108.px,
                                child: Stack(
                                  children: [
                                    Positioned(
                                      left: 0,
                                      bottom: 0,
                                      child: Image.network(
                                          width: 135.px,
                                          height: 108.32.px,
                                          fit: BoxFit.fitWidth,
                                          // "${urlConfig.microfront}${field.value?.portrait?.url}",
                                          field.value?.portrait?.imgUrl ??
                                              netImgUrl ??
                                              ''),
                                    ),
                                    Positioned(
                                        top: 5,
                                        right: 5,
                                        child: GestureDetector(
                                          onTap: () {
                                            field.value!.portrait = null;
                                            field.didChange(field.value);
                                            if (clickedCloseBtn != null) {
                                              clickedCloseBtn();
                                            }
                                          },
                                          child: Image.asset(
                                              width: 20.px,
                                              height: 20.px,
                                              ImageHelper.wrapAssets(
                                                  "delete.png")),
                                        ))
                                  ],
                                ),
                              )),
                  ),
                ),
              ],
            ),
          );
        });
}

class IDCardItem {
  BDHFile? portrait;
  BDHFile? nationalEmblem;
  IDCardItem({this.portrait, this.nationalEmblem});
}

enum ImageType { portrait, nationalEmblem }
