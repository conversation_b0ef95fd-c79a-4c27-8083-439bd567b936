import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

//
class BdhDigitalLoginInputView extends StatelessWidget {
  String titleName;
  String tipName;
  TextEditingController controller;
  TextInputType? textInputType;
  Widget? rightSubView;
  BdhDigitalLoginInputView({
    super.key,
    required this.titleName,
    required this.tipName,
    required this.controller,
    this.textInputType,
    this.rightSubView,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 340.px,
      height: 50.px,
      decoration: BoxDecoration(
          // color: Colors.lightBlue[50],
          color: Colors.white,
          border: Border(
              bottom: BorderSide(
                  width: 0.5.px,
                  color: const Color.fromRGBO(226, 235, 231, 0.6)))),
      child: Row(
        children: [
          Container(
            // decoration: BoxDecoration(border: Border.all(width: 1)),
            width: 68.px,
            child: Text.rich(TextSpan(children: [
              TextSpan(
                text: "*",
                style: TextStyle(
                    color: Colors.red,
                    fontSize: 16.px,
                    fontWeight: FontWeight.w500),
              ),
              TextSpan(
                text: titleName,
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                ),
              )
            ])),
          ),
          SizedBox(width: 30.px),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  // decoration: BoxDecoration(border: Border.all(width: 1)),
                  child: CupertinoTextField.borderless(
                    keyboardType: (textInputType != null)
                        ? textInputType
                        : TextInputType.text,
                    padding: EdgeInsets.zero,
                    controller: controller,
                    placeholder: tipName,
                    // obscureText: true,
                    style: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(0, 0, 0, 1)),
                    placeholderStyle: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(0, 0, 0, 0.2)),
                  ),
                ),
                //错误提示
                // Visibility(
                //   visible: true,
                //   child: Text(
                //     textAlign: TextAlign.start,
                //     "错误提示",
                //     style: TextStyle(color: Colors.red, fontSize: 12.px),
                //   ),
                // )
              ],
            ),
          ),
          SizedBox(width: 10.px),
          (rightSubView != null) ? rightSubView! : Container(),
        ],
      ),
    );
  }
}
