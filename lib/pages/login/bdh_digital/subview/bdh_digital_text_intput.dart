import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BDHDigitalTextInput extends FormField<String> {
  bool? showStar;
  bool? showBottomLine;
  double? titleWidth;
  String titleName;
  String tipName;
  TextEditingController controller;
  TextInputType? textInputType;
  Widget? rightSubView;
  bool? isShowPWD;
  bool? showErrTip;
  String? errTipInfo;
  bool? isChooseDepartment;
  bool? twoLineShowDepartmentText;
  FocusScopeNode? focusScopeNode;
  final Function(String)? onChange;
  final Function()? clickedChooseDepartmentCallBack;
  BDHDigitalTextInput({
    super.key,
    super.validator,
    super.onSaved,
    super.initialValue,
    required this.titleName,
    required this.tipName,
    required this.controller,
    this.rightSubView,
    this.isShowPWD,
    this.showErrTip = false,
    this.showStar = true,
    this.showBottomLine = true,
    this.titleWidth,
    this.errTipInfo = '',
    this.isChooseDepartment,
    this.twoLineShowDepartmentText,
    this.focusScopeNode,
    this.clickedChooseDepartmentCallBack,
    this.textInputType,
    this.onChange,
  }) : super(builder: (field) {
          return Container(
            width: 340.px,
            height: 50.px,
            decoration: BoxDecoration(
                color: Colors.white,
                border: showBottomLine ?? true
                    ? Border(
                        bottom: BorderSide(
                            width: 0.5.px,
                            color: const Color.fromRGBO(226, 235, 231, 0.6)),
                      )
                    : Border(
                        bottom:
                            BorderSide(width: 0.px, color: Colors.transparent),
                      )),
            child: Row(
              children: [
                Container(
                  // decoration: BoxDecoration(border: Border.all(width: 1)),
                  width: titleWidth ?? 68.px,
                  child: Text.rich(TextSpan(children: [
                    (showStar ?? true)
                        ? TextSpan(
                            text: "*",
                            style: TextStyle(
                                color: Colors.red,
                                fontSize: 16.px,
                                fontWeight: FontWeight.w500),
                          )
                        : TextSpan(
                            text: "",
                            style: TextStyle(
                                color: Colors.red,
                                fontSize: 16.px,
                                fontWeight: FontWeight.w500),
                          ),
                    TextSpan(
                      text: titleName,
                      style: TextStyle(
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  ])),
                ),
                SizedBox(width: 30.px),
                Expanded(
                    child: Stack(
                  children: [
                    Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                              // border: Border.all(width: 1),
                              ),
                          child: isShowPWD == null
                              ? CupertinoTextField.borderless(
                                  decoration:
                                      const BoxDecoration(color: Colors.white),
                                  keyboardType: (textInputType != null)
                                      ? textInputType
                                      : TextInputType.text,
                                  padding: EdgeInsets.zero,
                                  controller: controller,
                                  placeholder: tipName,
                                  enabled:
                                      isChooseDepartment == null ? true : false,
                                  maxLines: twoLineShowDepartmentText ?? false
                                      ? 2
                                      : 1,
                                  style: TextStyle(
                                      fontSize: 16.px,
                                      fontWeight: FontWeight.w400,
                                      color: const Color.fromRGBO(0, 0, 0, 1)),
                                  placeholderStyle: TextStyle(
                                    fontSize: 16.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(0, 0, 0, 0.2),
                                  ),
                                  focusNode: focusScopeNode,
                                  onChanged: (v) {
                                    field.didChange(v);
                                    if (onChange != null) {
                                      onChange(v);
                                    }
                                  },
                                )
                              : CupertinoTextField.borderless(
                                  keyboardType: (textInputType != null)
                                      ? textInputType
                                      : TextInputType.text,
                                  padding: EdgeInsets.zero,
                                  controller: controller,
                                  placeholder: tipName,
                                  obscureText: isShowPWD,
                                  style: TextStyle(
                                      fontSize: 16.px,
                                      fontWeight: FontWeight.w400,
                                      color: const Color.fromRGBO(0, 0, 0, 1)),
                                  placeholderStyle: TextStyle(
                                    fontSize: 16.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(0, 0, 0, 0.2),
                                  ),
                                  onChanged: (v) {
                                    field.didChange(v);
                                    if (onChange != null) {
                                      onChange(v);
                                    }
                                  },
                                ),
                        ),
                      ],
                    ),
                    isChooseDepartment == null
                        ? Container()
                        : Positioned(
                            child: GestureDetector(
                              onTap: () {
                                clickedChooseDepartmentCallBack!();
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    // border: Border.all(width: 1),
                                    color: BDHColor.kClear),
                                height: 50.px,
                                width: 250.px,
                              ),
                            ),
                          ),
                    Positioned(
                        left: 0,
                        bottom: -2,
                        child: //错误提示
                            Visibility(
                          // visible: field.errorText != null &&
                          //     field.errorText!.isNotEmpty,
                          visible: showErrTip ?? false,
                          child: Text(
                            errTipInfo ?? '',
                            textAlign: TextAlign.start,
                            // field.errorText ?? "",
                            style:
                                TextStyle(color: Colors.red, fontSize: 10.px),
                          ),
                        ))
                  ],
                )),
                SizedBox(width: 10.px),
                (rightSubView != null) ? rightSubView : Container(),
              ],
            ),
          );
        });

  @override
  FormFieldState<String> createState() => BdhDigitalTextIntputState();
}

class BdhDigitalTextIntputState extends FormFieldState<String> {
  TextEditingController? _controller;
  TextEditingController? get _effectiveController => widget.controller;
  @override
  BDHDigitalTextInput get widget => super.widget as BDHDigitalTextInput;
  @override
  void initState() {
    super.initState();
    _effectiveController?.addListener(_handleControllerChanged);
  }

  @override
  void dispose() {
    super.dispose();
    _effectiveController?.removeListener(_handleControllerChanged);
    _controller?.dispose();
  }

  _handleControllerChanged() {
    didChange(_effectiveController?.text);
  }
}
