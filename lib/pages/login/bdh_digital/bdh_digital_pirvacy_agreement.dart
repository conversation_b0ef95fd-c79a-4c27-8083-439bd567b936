import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_check.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/gestures.dart';

// ignore: must_be_immutable
class BdhDigitalPirvacyAgreement extends StatefulWidget {
  Function(bool isCheck) isCheckCallBack;
  Color? agreementTextColor;
  String? checkBoxNormalImg;
  String? checkoutBoxSelectedImg;
  BdhDigitalPirvacyAgreement(
      {super.key,
      required this.isCheckCallBack,
      this.agreementTextColor,
      this.checkBoxNormalImg,
      this.checkoutBoxSelectedImg});

  @override
  State<BdhDigitalPirvacyAgreement> createState() =>
      _BdhDigitalPirvacyAgreementState();
}

class _BdhDigitalPirvacyAgreementState
    extends State<BdhDigitalPirvacyAgreement> {
  bool isCheck = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin:
          EdgeInsets.only(left: 16.px, right: 16.px, top: 15.px, bottom: 0.px),
      width: 327.px,
      child: Row(
        children: [
          BdhCircleCheckBox(
            width: 20.px,
            isCircle: false,
            isCheck: isCheck,
            unselectedImg: widget.checkoutBoxSelectedImg ??
                'privacy_agreement_unselected.png',
            selectedImg:
                widget.checkBoxNormalImg ?? 'privacy_agreement_selected.png',
            onClick: (check) {
              setState(() {
                isCheck = check;
              });
              widget.isCheckCallBack(isCheck);
            },
          ),
          SizedBox(width: 5.px),
          Text.rich(TextSpan(children: [
            TextSpan(
                text: "上述为个人敏感信息，您知悉并同意《个人信息授权协\n议》，如拒绝，将无法使用本功能。",
                style: TextStyle(
                  color: const Color.fromRGBO(0, 0, 0, 0.4),
                  fontSize: 12.px,
                  fontWeight: FontWeight.w400,
                )),
            TextSpan(
              recognizer: TapGestureRecognizer()
                ..onTap = () {
                  Navigator.of(context).push(CupertinoPageRoute(
                      builder: (_) => const HtmlPage(
                          title: "查看协议",
                          content: "https://www.bdhic.com/Anticipation.html")));
                },
              text: "查看协议",
              style: TextStyle(
                  color: widget.agreementTextColor ??
                      const Color.fromRGBO(22, 183, 96, 1),
                  fontSize: 12.px),
            ),
            // TextSpan(
            //     text: "和",
            //     style: TextStyle(
            //         color: const Color.fromRGBO(0, 0, 0, 0.4),
            //         fontSize: 12.px)),
            // TextSpan(
            //   recognizer: TapGestureRecognizer()
            //     ..onTap = () {
            //       Navigator.of(context).push(CupertinoPageRoute(
            //           builder: (_) => const HtmlPage(
            //               title: "隐私政策",
            //               content:
            //                   "https://smartagric.bdhic.com/privacy.html")));
            //     },
            //   text: "《隐私政策》",
            //   style: TextStyle(
            //       color: widget.agreementTextColor ??
            //           const Color.fromRGBO(22, 183, 96, 1),
            //       fontSize: 12.px),
            // ),
          ]))
        ],
      ),
    );
  }
}
