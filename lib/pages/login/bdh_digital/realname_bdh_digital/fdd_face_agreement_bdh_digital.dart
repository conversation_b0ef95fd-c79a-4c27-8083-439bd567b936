import 'dart:io';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/realname_bdh_digital/real_name_auth_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/product/landcontract/info/again_real_name_auth_page.dart';
import 'package:bdh_smart_agric_app/pages/product/realname/real_name_auth_page.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:permission_handler/permission_handler.dart';

class FddFaceAgreementBdhDigital extends StatefulWidget {
  const FddFaceAgreementBdhDigital({super.key});

  @override
  State<StatefulWidget> createState() => _FddFaceAgreementBdhDigitalState();
}

class _FddFaceAgreementBdhDigitalState extends State
    with AutoDisposeStateMixin {
  var content = '''
  本协议由深圳法大大网络科技有限公司（下称“法大大”）与数字证书用户（下称“用户”或“您”）之间签署，本协议具有合同效力。法大大作为多家合作CA机构的授权审核机构及用户注册平台的合作方，有权与用户签署本协议。CA机构作为权威、可信、公正的第三方电子认证服务机构，根据国家相关法律、法规，为用户提供合法的数字证书申请、审核、签发和管理等电子认证服务。为明确各方权利和义务，法大大和用户就数字证书的申请和使用等事宜达成以下协议，共同遵守执行。<br>
                第一条定义<br>
                1.数字证书：电子认证服务机构签发的包含数字证书使用者身份信息和公开密钥的电子文件。<br>
                2.CA机构：工业和信息化部批准的电子认证服务机构和国家密码管理局批准的电子政务电子认证服务机构，遵照《中华人民共和国电子签名法》为用户提供数字证书相关的电子认证服务。<br>
                3.法大大：深圳法大大网络科技有限公司，相关CA机构的授权审核机构，作为用户注册平台的合作方，为用户提供数字证书颁发的申请、审核等服务。<br>
                4.用户：数字证书持有人以及申请使用数字证书的主体。<br>
                第二条声明<br>
                1.您应在申请数字证书以前务必仔细阅读本协议，若您不同意本协议的任意内容，或者无法准确理解法大大对条款的解释，请不要点击同意并进行后续操作； 若您点击同意，则表示您对本协议的全部内容已充分阅读并认可和同意遵守，同时，承诺遵守中国法律、法规、规章及其他政府规范性文件的规定， 如有违反而造成任何法律后果，您将以本人名义独立承担所有相应的法律责任。<br>
                2.您点击同意即表示您同意法大大将您的个人身份信息或单位信息传输至法大大与CA机构用于申请数字证书。<br>
                第三条 申请<br>
                1.用户同意通过法大大向法大大合作的CA机构申请并使用该机构颁发给用户的数字证书，承诺在申请证书时提供真实、完整和准确的信息及证明材料。如因故意或过失未提供真实、完整和准确的信息，导致CA机构签发证书错误，造成相关各方损失的，由用户承担相关责任。<br>
                2.法大大作为CA机构的授权审核机构，负责用户的信息录入、身份审核等工作。用户在申请数字证书时应遵照CA机构的规程办理手续。<br>
                3.用户同意法大大在为用户申请数字证书前进行实名认证。并同意法大大将用户的真实姓名、身份证信息、手机号码、银行卡信息，以及面部生物特征信息共享给与法大大合作的实名认证机构，其中包括腾讯云计算(北京)有限责任公司、中国联合网络通信有限公司及其关联公司。<br>
                4.在实名认证过程中用户必须根据法大大要求提供最新、真实、有效及完整的资料。根据实名认证方式的不同，用户所需提供的实名认证资料将有所差异，具体所需资料请以法大大要求为准。实名认证具体操作流程请严格按照法大大实名认证规定的流程履行，否则将影响实名审核的结果。<br>
                5.法大大应积极响应用户发出的证书申请请求，及时通过CA机构为用户签发证书。如果由于设备或网络故障而导致签发数字证书错误、延迟、中断或者无法签发，法大大和CA机构不承担任何赔偿责任。<br>
                6.用户在获得数字证书时应及时验证此证书所匹配的用户信息，如无异议则视为接受证书并承担该证书使用的责任和后果。<br>
                第四条 使用<br>
                1.数字证书用于网络上的用户身份标识、数字签名验证及密钥分配，各应用系统可根据需要对其用途进行定义，但不包括涉及违反国家法律、法规或危害国家安全的用途。<br>
                2.用户应确保其应用系统能为数字证书提供安全的应用环境，若因网络、主机、操作系统或其他软硬件环境等存在安全漏洞，由此导致的安全事故及相关后果，法大大和CA机构不承担责任。<br>
                3.用户应当妥善保管数字证书以及调用数字证书的验证码或密码，不得泄漏或交付他人。如用户保管不善导致数字证书遭盗用、冒用、伪造或者篡改，用户应当自行承担相关责任。<br>
                4.数字证书对应的私钥为用户本身访问和使用，用户对使用数字证书的行为负责。所有使用数字证书在网络上签署电子文件或进行其他活动均视为用户所为，因此而产生的相关后果应当由用户自行承担。<br>
                5.数字证书一律不得转让、转借或转用。因转让、转借或转用而产生的相关后果应当由用户自行承担。<br>
                6.如遇数字证书遗失、被窃，或数字证书私钥泄露，用户应当立即到数字证书颁发的CA机构申请注销证书。件在微信小程序上遮罩无效的问题<br>
                第五条 更新<br>
                1.数字证书的有效期根据数字证书上的记录确定，有效期为1个月，自签发之日起计算，法大大保证您在使用我们的电子合同签约服务时，您的数字证书处于有效期内。<br>
                2.因技术需要，如遇证书中的信息发生变更，用户应及时通过法大大向CA机构申请更新证书。若用户逾期没有更新证书，因此而产生的相关后果应当由用户自行负责。<br>
                第六条 吊销<br>
                1.如遇数字证书私钥泄露丢失、证书中的信息发生重大变更、或用户不希望继续使用数字证书的情况，用户应当立即向CA机构申请吊销证书。吊销手续遵循各注册机构的规定。用户应当承担在证书吊销之前所有因使用数字证书而造成的责任。<br>
                2.如果用户主体资格灭失（如企业注销等），法定代表人或授权代表通过法大大向CA机构请求吊销用户证书，用户主体及相关责任人应当承担其数字证书在吊销前所有使用数字证书而造成的相关后果。<br>
                3.对于下列情形之一，CA机构有权主动吊销所签发的证书：<br>
                (1)用户申请证书时，提供不真实信息；<br>
                (2)证书对应的私钥泄露或出现其他证书的安全性得不到保证的情况；<br>
                (3)用户不能履行或违反了相关法律、法规和本协议所规定的责任和义务；<br>
                (4)法律、法规规定的其他情形。<br>
                第七条 其他<br>
                1.法大大与CA机构不对由于意外事件或其他不可抗力事件而导致暂停或终止全部或部分证书服务承担任何责任。<br>
                2.本协议的解释适用中华人民共和国法律。若发生任何纠纷或争议，首先应友好协商解决，协商不成的，争议方均可提请深圳国际仲裁院按照该会有效的仲裁规则进行仲裁。仲裁裁决是终局的，对双方均具有约束力。<br>
                3.用户确认已经认真阅读并完全理解本协议中的各项规定，用户一旦在本页面点击同意即表明接受本协议的约束，本协议即时生效。<br>
                第八条 腾讯云人脸核身SDK个人信息处理授权书<br>
                您(即授权人)正在使用腾讯云计算(北京)有限责任公司(以下简称“腾讯云”) 提供的人脸核身和证件识别SDK服务，具体以您选择的为准(以下简称“本服务”)。<br>
                为了保障您的合法权益，请您务必事先审慎阅读、充分理解本授权书条款内容。如您不同意本授权书，请勿使用本服务。在您点击同意本授权书后，即表示您已详细阅读本授权书条款，并且在业务办理机构(即为您办理具体业务的机构)应用中调取您的智能终端的摄像头权限后，您同意授权业务办理机构收集您的如下个人信息，并将前述信息分享给腾讯云及为本服务提供必要技术支持的服务商(以下统称“被授权人”)，以便实现您使用本服务的目的:<br>
                (1)人脸核身(包含活体检测与人脸比对)服务:获取并使用您的姓名、身份证号码、人脸图像及视频信息，提供给权威数据库方进行核对，以便实现身份信息的比对，其中人脸图像及视频的采集范围是以智能终端摄像头拍摄的实际区域为准。同时，为识别和排除恶意请求、维护服务安全，还将获取并使用您的操作记录信息以及如下设备信息用于检测认证实时风险，不同版本收集的设备信息有所不同，具体以您使用的终端版本为准：1)安卓版将获取安卓 ID、序列号、内核版本、CPU 信息、机型、系统版本、设备制造商、运营商；2)IOS 版将获取 IDFV、GUID、蜂窝网络 IP、WIFI IP;3)人脸核身 H5 服务将获取操作记录信息。<br>
                (2)证件识别服务:获取并使用您的身份证照片信息、驾驶证/行驶证照片信息、操作记录信息，通过OCR技术提取证件上的相关信息。<br>
                上述信息是实现对应服务目的所必须的信息，如您拒绝提供的，被授权人将无法为您提供本服务，但不影响业务办理机构为您提供其他服务。如您使用不成功，请确保您已经按照要求正确使用本服务，或者向业务办理机构咨询其他办理渠道。<br>
                被授权人仅在为实现本服务所必要的最短时间内保存您的个人信息。为了保障您的个人信息安全，被授权人会在现有技术水平下采取合理必要的措施来保护您的个人信息，采取物理、技术和行政管理安全措施来降低丢失、误用、非授权访问、披露和更改的风险。如发生信息安全事件，被授权人有权根据法律规定及其应急管理制度处理您的个人信息，我们将及时以公告、通过业务办理机构应用站内信等方式告知您。如您希望访问、撤回授权、修改、删除您的个人信息，您可以向业务办理机构提出需求，并提供必要的身份证明。本服务获取和处理的个人信息将存储于中华人民共和国境内，未经您授权同意，被授权人不会向境外传输您的个人信息。<br>
                您理解:本授权书是您向被授权人做出的单方承诺，效力具有独立性。若您与被授权人发生任何纠纷或争议，首先应友好协商解决；协商不成的，您同意将纠纷或争议提交本授权书签订地(即中国广东省深圳市南山区)有管辖权的人民法院管辖。本授权书的成立、生效、履行、解释及纠纷解决等，适用中华人民共和国法律(仅为本授权书目的，不含港澳台地区法律)，不包括冲突法。<br>
                您已阅读并理解本授权书所有内容（特别是加粗字体内容）的意义以及由此产生的法律效力，自愿作出上述授权，本授权书是您真实的意思表示，您同意承担相关责任及后果。<br>
 ''';
  bool isLoding = true;
  dynamic idNumber = '';
  @override
  void initState() {
    super.initState();
    // setState(() {
    //   isLoding = false;
    // });
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   BrnLoadingDialog.show(context,
    //       content: "正在获取信息，请稍等", barrierDismissible: false);
    //   BdhLandResponsitory.getResidentInfo(
    //           cancelToken: useCancelToken(CancelToken()))
    //       .then((res) {
    //     idNumber = res.data?.idNumber;
    //     setState(() {
    //       isLoding = true;
    //     });
    //     BrnLoadingDialog.dismiss(context);
    //   });
    // });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("法大大协议"),
      ),
      body: LayoutBuilder(builder: (ctx, cons) {
        return Column(
          children: [
            Container(
              padding: EdgeInsets.all(10.px),
              width: 375.px,
              height: cons.maxHeight -
                  44.px -
                  15.px -
                  MediaQuery.of(context).padding.bottom,
              child: SingleChildScrollView(
                child: HtmlWidget(
                  content,
                  textStyle: TextStyle(fontSize: 18.px),
                ),
              ),
            ),
            GestureDetector(
              onTap: () {
                PermissionUtil.requestMicroPhonePermission(ctx, "录制音频")
                    .then((res) {
                  if (Platform.isAndroid) {
                    if (res) {
                      //如果有身份证号，就去重新实名认证，没有就去实名认证页面
                      Navigator.of(context)
                          .push(CupertinoPageRoute(builder: (ctx) {
                        return const RealNameAuthBdhDigitalPage(title: "实名认证");
                      }));
                    }
                  } else {
                    if (res) {
                      //如果有身份证号，就去重新实名认证，没有就去实名认证页面
                      Navigator.of(context)
                          .push(CupertinoPageRoute(builder: (ctx) {
                        return const RealNameAuthBdhDigitalPage(title: "实名认证");
                      }));
                    } else {
                      return BrnDialogManager.showConfirmDialog(context,
                          title: "提示",
                          cancel: '取消',
                          confirm: '确定',
                          message: "需要您开启录制音频权限, 是否去开启录制音频权限？", onConfirm: () {
                        Navigator.of(context).pop();
                        openAppSettings();
                      }, onCancel: () {
                        Navigator.of(context).pop();
                      });
                    }
                  }
                });
              },
              child: BDHButtonGreen(
                  width: 345.px, height: 44.px, title: "我已知晓并同意"),
            )
          ],
        );
      }),
    );
  }
}
