import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_ocr_image_picker.dart';
import 'package:bdh_smart_agric_app/components/jh_cascade_tree_picker.dart';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/subview/bdh_digital_identity_card_upload_view.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/subview/bdh_digital_text_intput.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:crypto/crypto.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:logger/logger.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

enum RegistInputType {
  telephone,
  newTelephone,
  oldTelephoe,
  name,
  idCard,
  department,
  account,
  verifyCode,
  simCode,
  pwd,
  pwdConfirm,
}

class RegisterBdhDigitalPage extends StatefulWidget {
  const RegisterBdhDigitalPage({super.key});

  @override
  State<RegisterBdhDigitalPage> createState() => _RegisterBdhDigitalPageState();
}

class _RegisterBdhDigitalPageState extends State<RegisterBdhDigitalPage> {
  TextEditingController telephoneController = TextEditingController();
  TextEditingController nameController = TextEditingController();
  TextEditingController idController = TextEditingController();
  TextEditingController departmentController = TextEditingController();

  TextEditingController accountController = TextEditingController();
  TextEditingController simCodeController = TextEditingController();
  TextEditingController pwdController = TextEditingController();
  TextEditingController pwdConfrimController = TextEditingController();
  final FocusScopeNode focusScopeNode = FocusScopeNode();

// phone: "",
// realName: "",
// idCard: "",
// orgCode: "",
// code: "",
// loginName: "",
// systemCode: this.$store.state.SYSTEM_CODE,
// idCardImg: "",
// passwordEnc: "",
// password: "",
  Map<String, dynamic> form = {'systemCode': kSystemCode};
  GlobalKey<FormState> bdhDigitalRegisterKey = GlobalKey<FormState>();
  bool isShowPWD = true;
  bool isShowPWDConfirmed = true;
  bool twoLineShowDepartmentText = false;
  OrgTreeResult? treeResult;
  Map<dynamic, dynamic>? currentOrg;
  String idCardImg = '';
  String idCard = '';
  int count = 60;
  String btnText = "发送验证码";
  Timer? timer;
  Timer? checkFaceVerifyTimer;
  bool showTelephoneErr = false;
  String errTelephoneInfo = '手机号输入有误';
  bool showNameErr = false;
  String errNameInfo = '姓名不能为空';
  bool showIDCardErr = false;
  String errIDInfo = '身份证号不能为空';
  bool showDepartmentErr = false;
  String errDepartmentInfo = '请选择所在单位';
  bool showAccountErr = false;
  String errAccountInfo = '账号不能为空';
  bool showSIMCodeErr = false;
  String errSImCodeInfo = '验证码不能为空';
  bool showPWDErr = false;
  String errPWDInfo = '请输入密码';
  bool showConfrimPWDErr = false;
  String errConfirmPWDInfo = '请输入密码';

  @override
  void initState() {
    super.initState();
    loadOrg();
  }

  @override
  void dispose() {
    super.dispose();
    checkFaceVerifyTimer?.cancel();
    timer?.cancel();
    telephoneController.dispose();
    nameController.dispose();
    idController.dispose();
    departmentController.dispose();
    accountController.dispose();
    simCodeController.dispose();
    pwdController.dispose();
    pwdConfrimController.dispose();
    focusScopeNode.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: HexColor("#F3F5F9"),
      appBar: AppBar(
        title: const Text('注册'),
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0.px),
        child: Form(
          key: bdhDigitalRegisterKey,
          child: Column(
            children: [
              topCardView(),
              centerCardView(),
              bottomCardView(),
              bottomButtonView()
            ],
          ),
        ),
      ),
    );
  }

//sub view 姓名   身份证号  所在单位  手机号
  Widget topCardView() {
    return Container(
      // height: 200,
      margin: EdgeInsets.only(bottom: 10.px),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        children: [
          //手机号
          BDHDigitalTextInput(
            titleName: '手 机 号',
            tipName: '请输入手机号',
            controller: telephoneController,
            textInputType: TextInputType.number,
            showErrTip: showTelephoneErr,
            errTipInfo: errTelephoneInfo,
            onSaved: (v) {
              form["phone"] = v;
              showErrTipInfo(RegistInputType.telephone, v);
            },
            onChange: (v) {
              form["phone"] = v;
              showErrTipInfo(RegistInputType.telephone, v);
            },
            // validator: (value) {
            //   String errString = '';
            //   if (value == null || value.isEmpty) {
            //     errString = "手机号不能为空";
            //   } else if (!RegUtil.isPhoneNumber(value)) {
            //     errString = "手机号输入有误";
            //   }
            //   if (errString.isEmpty) {
            //     return null;
            //   } else {
            //     return errString;
            //   }
            // },
          ),

          //姓名
          BDHDigitalTextInput(
            titleName: '姓      名',
            tipName: '请输入姓名',
            controller: nameController,
            rightSubView: getOCRNameView(),
            showErrTip: showNameErr,
            errTipInfo: errNameInfo,
            onSaved: (v) {
              form["realName"] = v;
              showErrTipInfo(RegistInputType.name, v);
            },
            onChange: (v) {
              form["realName"] = v;
              showErrTipInfo(RegistInputType.name, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "姓名不能为空";
            //   }
            //   return null;
            // },
          ),

          //身份证号
          BDHDigitalTextInput(
            titleName: '身份证号',
            tipName: '请输入身份证号',
            controller: idController,
            textInputType: TextInputType.number,
            showErrTip: showIDCardErr,
            errTipInfo: errIDInfo,
            onSaved: (v) {
              form["idCard"] = v;
              showErrTipInfo(RegistInputType.idCard, v);
            },
            onChange: (v) {
              form["idCard"] = v;
              showErrTipInfo(RegistInputType.idCard, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "身份证号不能为空";
            //   }
            //   return null;
            // },
          ),

          //所在单位
          BDHDigitalTextInput(
            titleName: '所在单位',
            tipName: '请选择所在单位',
            controller: departmentController,
            rightSubView: getChooseDepartmentView(),
            isChooseDepartment: true,
            twoLineShowDepartmentText: twoLineShowDepartmentText,
            showErrTip: showDepartmentErr,
            errTipInfo: errDepartmentInfo,
            clickedChooseDepartmentCallBack: () {
              Log.i('点击选择部门');
              showBottomMultiSelectPicker(context);
            },
            onSaved: (v) {
              // form["contactsName"] = v;
              showErrTipInfo(
                  RegistInputType.department, departmentController.text);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "所在单位不能为空";
            //   }
            //   return null;
            // },
          ),
        ],
      ),
    );
  }

//subview: 账号  密码  验证码  确认密码
  Widget centerCardView() {
    return Container(
      // height: 200,
      margin: EdgeInsets.only(bottom: 10.px),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        children: [
          //账      号
          BDHDigitalTextInput(
            titleName: '账      号',
            tipName: '请输入账号',
            controller: accountController,
            textInputType: TextInputType.number,
            showErrTip: showAccountErr,
            errTipInfo: errAccountInfo,
            onSaved: (v) {
              form["loginName"] = v;
              showErrTipInfo(RegistInputType.account, v);
            },
            onChange: (v) {
              form["loginName"] = v;
              showErrTipInfo(RegistInputType.account, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "账号不能为空";
            //   }
            //   return null;
            // },
          ),

          //验证码
          BDHDigitalTextInput(
            titleName: '验 证 码',
            tipName: '请输入验证码',
            controller: simCodeController,
            textInputType: TextInputType.number,
            rightSubView: getSimCodeView(),
            showErrTip: showSIMCodeErr,
            errTipInfo: errSImCodeInfo,
            onSaved: (v) {
              form["code"] = v;
              showErrTipInfo(RegistInputType.simCode, v);
            },
            onChange: (v) {
              form["code"] = v;
              showErrTipInfo(RegistInputType.simCode, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "验证码不能为空";
            //   }
            //   return null;
            // },
          ),

          //密 码
          BDHDigitalTextInput(
            titleName: '密      码',
            tipName: '请输入密码',
            controller: pwdController,
            rightSubView: getPWDeyeView(),
            isShowPWD: isShowPWD,
            showErrTip: showPWDErr,
            errTipInfo: errPWDInfo,
            onSaved: (v) {
              form["passwordEnc"] = v;
              showErrTipInfo(RegistInputType.pwd, v);
            },
            onChange: (v) {
              form["passwordEnc"] = v;
              showErrTipInfo(RegistInputType.pwd, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "密码不能为空";
            //   }
            //   return null;
            // },
          ),

          //确认密码
          BDHDigitalTextInput(
            titleName: '确认密码',
            tipName: '请再次输入密码',
            controller: pwdConfrimController,
            rightSubView: getPWDConfirmdeyeView(),
            isShowPWD: isShowPWDConfirmed,
            showErrTip: showConfrimPWDErr,
            errTipInfo: errConfirmPWDInfo,
            focusScopeNode: focusScopeNode,
            onSaved: (v) {
              form["password"] = v;
              showErrTipInfo(RegistInputType.pwdConfirm, v);
            },
            onChange: (v) {
              form["password"] = v;
              showErrTipInfo(RegistInputType.pwdConfirm, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "确认密码不能为空";
            //   }
            //   return null;
            // },
          ),
        ],
      ),
    );
  }

  Widget bottomCardView() {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Container(
        width: 343.px,
        margin: EdgeInsets.only(bottom: 10.px),
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.px),
        ),
        child: Column(
          children: [
            BdhDigitalIdentityCardUploadView(
              netImgUrl: idCardImg,
              mainTitle: '头像面',
              subTitle: '上传您身份证头像面',
              imageName: 'identityCart_front.png',
              initialValue: IDCardItem(nationalEmblem: null, portrait: null),
              onSaved: (v) {
                form["idCardImg"] = v?.portrait?.imgUrl;
                // form["cardBackImg"] = v?.nationalEmblem?.url;
              },
              validator: (value) {
                if (value?.portrait == null) {
                  return "身份证人像不能为空";
                }
                if (value?.nationalEmblem == null) {
                  return "身份证国徽不能为空";
                }
                return null;
              },
              clickedCloseBtn: () {
                form['idCardImg'] = ''; //清空
                setState(() {
                  idCardImg = '';
                });
              },
              clickedOpen: () {
                Log.i('点击打开相册按钮');
                setState(() {
                  focusScopeNode.unfocus();
                  FocusScope.of(context).unfocus();
                });
              },
            ),
          ],
        ),
      ),
    );
  }

//openPWDImg.png
  Widget getPWDeyeView() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isShowPWD = !isShowPWD;
        });
      },
      child: Image.asset(
        width: 24.px,
        height: 24.px,
        ImageHelper.wrapAssets(
            isShowPWD ? "openPWDImg.png" : "colsePWDImg.png"),
      ),
    );
  }

  Widget getPWDConfirmdeyeView() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isShowPWDConfirmed = !isShowPWDConfirmed;
        });
      },
      child: Image.asset(
        width: 24.px,
        height: 24.px,
        ImageHelper.wrapAssets(
            isShowPWDConfirmed ? "openPWDImg.png" : "colsePWDImg.png"),
      ),
    );
  }

//获取单位view
  Widget getChooseDepartmentView() {
    return GestureDetector(
      onTap: () {
        showBottomMultiSelectPicker(context);
      },
      child: Image.asset(
        width: 24.px,
        height: 24.px,
        ImageHelper.wrapAssets("bdh_digitao_arrow_left.png"),
      ),
    );
  }

//身份验证码view
  Widget getOCRNameView() {
    return GestureDetector(
      onTap: () {
        Log.i('点击识别姓名按钮');
        showModalBottomSheet(
            backgroundColor: Colors.transparent,
            useSafeArea: true,
            context: context,
            builder: (ctx) {
              return CameraPhotoSelect(sourceSelect: (e) {
                openImage(e, ImageType.portrait);
              });
            });
      },
      child: Container(
        height: 28.px,
        width: 72.px,
        decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border.all(width: 1, color: const Color.fromRGBO(0, 127, 255, 1)),
          borderRadius: BorderRadius.circular(4.px),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '识别身份证',
              style: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 127, 255, 1)),
            )
          ],
        ),
      ),
    );
  }

//获取验证码view
  Widget getSimCodeView() {
    return GestureDetector(
      onTap: () {
        if (telephoneController.text.isEmpty) {
          showToast('手机号码不能为空');
          return;
        }

        if (!RegUtil.isPhoneNumber(telephoneController.text)) {
          showToast('手机号码有误, 请重新输入');
          return;
        }

        if (count < 60 && count > 0) {
          showToast("请等计时结束再试");
          return;
        }

        TDToast.showLoadingWithoutText(context: context, preventTap: true);
        BdhDigitalService.getRegisterSmsCodeBdhDigital(
            {"phone": telephoneController.text}).then((result) {
          TDToast.dismissLoading();

          if (result.success ?? false) {
            showToast(result.msg ?? "");
            if (count < 60 && count > 0) {
              showToast("请等计时结束再试");
            } else {
              timer = Timer.periodic(const Duration(seconds: 1), (timer) {
                count--;
                if (count <= 0) {
                  timer.cancel();
                }
                setState(() {
                  if (count > 0) {
                    btnText = "请$count秒后再试";
                  } else {
                    btnText = "重新发送";
                    count = 60;
                  }
                });
              });
            }
          } else {
            showToast(result.msg ?? "");
          }
        });
      },
      child: Container(
        padding: EdgeInsets.only(left: 2.px, right: 2.px),
        height: 28.px,
        // width: 72.px,
        decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border.all(width: 1, color: const Color.fromRGBO(0, 127, 255, 1)),
          borderRadius: BorderRadius.circular(
            4.px,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              btnText,
              style: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 127, 255, 1)),
            )
          ],
        ),
      ),
    );
  }

//选择图片
  openImage(ImageSource type, ImageType imageType) async {
    ImagePicker picker = ImagePicker();
    try {
      final XFile? pickedFile =
          await picker.pickImage(source: type, imageQuality: 10);
      if (pickedFile != null) {
        XFile processedImage =
            await RotateImageUtil.rotateImageIfNeeded(File(pickedFile.path));

        Logger().i('processedImage=$processedImage');
        processedImage.readAsBytes().then((bytes) {
          String? mimeType = lookupMimeType(processedImage.name);
          if (mimeType != null) {
            FormData postData = FormData.fromMap({
              "file": MultipartFile.fromBytes(bytes,
                  filename: processedImage.name,
                  contentType: MediaType(
                      mimeType.split("/").first, mimeType.split("/").last))
            });
            TDToast.showLoadingWithoutText(context: context, preventTap: true);
            BdhDigitalService.authIdentityUploadFileBdhDigital(postData)
                .then((value) {
              TDToast.dismissLoading();
              if (value.code == -1) {
                showToast("识别失败请重试");
              } else {
                Log.i(value.data);
                form['idCard'] = value.data?.idCard;
                form['idCardImg'] = value.data?.imgUrl;
                idCard = value.data?.idCard ?? '';
                // idCardImg = value.data?.imgUrl ?? '';
                Log.i("----form ------ $form");
                nameController.text = value.data?.realName! ?? '';
                idController.text = value.data?.idCard ?? '';
                // if (bdhDigitalRegisterKey.currentState!.validate()) {
                //   bdhDigitalRegisterKey.currentState!.save();
                // }
                setState(() {
                  idCardImg = value.data?.imgUrl ?? '';
                });
              }
            });
          } else {
            showToast("未知文件");
          }
        });
      } else {
        showToast('选择图片时出错');
      }
    } catch (err) {
      showToast('选择图片时出错');
    }

    // picker.pickImage(source: type, imageQuality: 10).then((image) async {
    //   // Navigator.of(context).pop();
    //   //图片上传
    //   if (image != null) {
    //     image.readAsBytes().then((bytes) {
    //       String? mimeType = lookupMimeType(image.name);
    //       if (mimeType != null) {
    //         FormData postData = FormData.fromMap({
    //           "file": MultipartFile.fromBytes(bytes,
    //               filename: image.name,
    //               contentType: MediaType(
    //                   mimeType.split("/").first, mimeType.split("/").last))
    //         });
    //         TDToast.showLoadingWithoutText(context: context, preventTap: true);
    //         BdhDigitalService.authIdentityUploadFileBdhDigital(postData)
    //             .then((value) {
    //           TDToast.dismissLoading();
    //           if (value.code == -1) {
    //             showToast("识别失败请重试");
    //           } else {
    //             Log.i(value.data);
    //             form['idCard'] = value.data?.idCard;
    //             form['idCardImg'] = value.data?.imgUrl;
    //             idCard = value.data?.idCard ?? '';
    //             // idCardImg = value.data?.imgUrl ?? '';
    //             Log.i("----form ------ $form");
    //             nameController.text = value.data?.realName! ?? '';
    //             idController.text = value.data?.idCard ?? '';
    //             // if (bdhDigitalRegisterKey.currentState!.validate()) {
    //             //   bdhDigitalRegisterKey.currentState!.save();
    //             // }
    //             setState(() {
    //               idCardImg = value.data?.imgUrl ?? '';
    //             });
    //           }
    //         });
    //       } else {
    //         showToast("未知文件");
    //       }
    //     });
    //   }
    // });
  }

//选择组织
  showBottomMultiSelectPicker(BuildContext context) {
    var tempData = [];
    List<OrgTreeItem> tempList = treeResult!.data ?? [];
    OrgTreeItem item = tempList[0];

    for (var e in item.children ?? []) {
      tempData.add(e.toJson());
    }
    // for (var e in treeResult!.data ?? []) {
    //   tempData.add(e.toJson());
    // }
    JhCascadeTreePicker.show(context,
        data: tempData,
        valueKey: "orgCode",
        labelKey: "orgName",
        childrenKey: "list",
        clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
      List tempList = ress;
      String orgName = '';

      for (int i = 0; i < tempList.length; i++) {
        Map<dynamic, dynamic> item = tempList[i];
        String itemName = item['orgName'];
        if (i > 0) {
          orgName += "/$itemName";
        } else {
          orgName += itemName;
        }
      }
      setState(() {
        orgName.length > 12
            ? twoLineShowDepartmentText = true
            : twoLineShowDepartmentText = false;
        currentOrg = (ress as List).last;
        departmentController.text = orgName;
        form['orgCode'] = currentOrg?['orgCode'];
      });
      showErrTipInfo(RegistInputType.department, departmentController.text);
    });
  }

//登录按钮
  Widget bottomButtonView() {
    return GestureDetector(
      onTap: () {
        Log.i("点击了登录按钮");
        bdhDigitalRegisterKey.currentState!.save();
        if (bdhDigitalRegisterKey.currentState!.validate()) {
          bdhDigitalRegisterKey.currentState!.save();
        }

        if (telephoneController.text.isEmpty ||
            nameController.text.isEmpty ||
            idController.text.isEmpty ||
            departmentController.text.isEmpty ||
            accountController.text.isEmpty ||
            simCodeController.text.isEmpty ||
            pwdController.text.isEmpty ||
            pwdConfrimController.text.isEmpty) {
          return;
        }

        if (pwdController.text != pwdConfrimController.text) {
          showToast('两次密码不一致');
          return;
        }

        if (idCard != '') {
          form['idCard'] = idCard;
        }
        if (idCardImg != '') {
          form['idCardImg'] = idCardImg;
        }

        if (form.containsKey('idCardImg')) {
          String idCardImg = form['idCardImg'];
          if (idCardImg.isEmpty) {
            showToast('请上传身份证照片');
            return;
          }
        } else {
          showToast('请上传身份证照片');
          return;
        }

        //加密
        var password = pwdConfrimController.text;
        var bytes = utf8.encode(password.toUpperCase());
        //第一层MD5
        var firstMD5 = '${md5.convert(bytes)}'.toUpperCase();
        //第二层MD5
        var md5Password = '${md5.convert(utf8.encode(firstMD5))}'.toLowerCase();

        form['password'] = md5Password;

        Log.i('form = $form');
        TDToast.showLoadingWithoutText(context: context, preventTap: true);
        BdhDigitalService.registerBdhDigital(form).then((result) {
          TDToast.dismissLoading();
          Log.i('注册step1$result');
          if (result.success ?? false) {
            NativeUtil.openFdd(result.data?.url);
            Future.delayed(const Duration(seconds: 3), () {
              checkFaceAuthResult(result.data?.staffId ?? 0);
            });
          } else {
            showToast(result.msg ?? "注册失败");
          }
        });
      },
      child: BDHUserLoginButton(
        width: 343.px,
        height: 48.px,
        title: '提交',
        borderRadius: 8.px,
        marginBottom: 16.px,
        marginTop: 20.px,
        bgLinearGradientColors: const [
          Color.fromRGBO(0, 127, 255, 1),
          Color.fromRGBO(61, 156, 255, 1),
        ],
      ),
    );
  }

  //轮询查看人脸结果
  checkFaceAuthResult(int stffId) {
    int counter = 0;
    int maxCount = 30;
    TDToast.showLoading(
        context: context, text: '人脸认证结果查询中...', preventTap: true);
    // TDToast.showLoadingWithoutText(context: context, preventTap: true);
    checkFaceVerifyTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      BdhDigitalService.getFaceResultBdhDigital({'staffId': stffId})
          .then((result) {
        counter++;
        Log.i('注册step1$result');
        if (counter == maxCount) {
          TDToast.dismissLoading();
          showToast('认证超时');
          timer.cancel();
          NativeUtil.closeFdd();
          return;
        }
        if (result.data?.verifiedStatus == 2) {
          TDToast.dismissLoading();
          timer.cancel();
          NativeUtil.closeFdd();
          showToast('申请成功,等待审核');
          Navigator.pop(context);
          return;
        } else if (result.data?.verifiedStatus != 2 &&
            result.data?.verifiedStatus != 1) {
          TDToast.dismissLoading();
          showToast('认证失败');
          NativeUtil.closeFdd();
          timer.cancel();
        } else {
          // timer.cancel();
          // NativeUtil.closeFdd();
          // showToast(result.data?.verifiedStatusDesc ?? '认证失败');
        }
      });
    });
  }

//net: 组织架构
  loadOrg() {
    BdhDigitalService.getCodeList({}).then((res) {
      Log.i('组织架构$res');
      setState(() {
        treeResult = res;
      });
    });
  }

//错误提示信息
  showErrTipInfo(RegistInputType type, dynamic v) {
    //电话号码
    if (type == RegistInputType.telephone) {
      if (v == null || v.isEmpty) {
        setState(() {
          showTelephoneErr = true;
          errTelephoneInfo = '手机号不能为空';
        });
      } else if (!RegUtil.isPhoneNumber(v)) {
        setState(() {
          showTelephoneErr = true;
          errTelephoneInfo = '手机号输入有误';
        });
      } else {
        setState(() {
          showTelephoneErr = false;
          errTelephoneInfo = '';
        });
      }
      return;
    }

    //姓名
    if (type == RegistInputType.name) {
      if (v == null || v.isEmpty) {
        setState(() {
          showNameErr = true;
          errNameInfo = '姓名不能为空';
        });
      } else {
        setState(() {
          showNameErr = false;
          errNameInfo = '';
        });
      }
      return;
    }
    //身份证号码
    if (type == RegistInputType.idCard) {
      if (v == null || v.isEmpty) {
        setState(() {
          showIDCardErr = true;
          errIDInfo = '身份证号不能为空';
        });
      } else if (!RegUtil.isIdCard(v)) {
        setState(() {
          showIDCardErr = true;
          errIDInfo = '请输入正确身份证号';
        });
      } else {
        setState(() {
          showIDCardErr = false;
          errIDInfo = '';
        });
      }
      return;
    }

    //所在单位
    if (type == RegistInputType.department) {
      if (v == null || v.isEmpty) {
        setState(() {
          showDepartmentErr = true;
          errDepartmentInfo = '请选择所在单位';
        });
      } else {
        setState(() {
          showDepartmentErr = false;
          errDepartmentInfo = '';
        });
      }
    }

    //账号
    if (type == RegistInputType.account) {
      if (v == null || v.isEmpty) {
        setState(() {
          showAccountErr = true;
          errAccountInfo = '账号不能为空';
        });
      } else if (!RegUtil.isValidAccountName(v)) {
        setState(() {
          showAccountErr = true;
          errAccountInfo = '请输入8到16位数字与字母组合';
        });
      } else {
        setState(() {
          showAccountErr = false;
          errAccountInfo = '';
        });
      }
      return;
    }

    //验证码
    if (type == RegistInputType.simCode) {
      if (v == null || v.isEmpty) {
        setState(() {
          showSIMCodeErr = true;
          errSImCodeInfo = '验证码不能为空';
        });
      } else {
        setState(() {
          showSIMCodeErr = false;
          errSImCodeInfo = '';
        });
      }
      return;
    }

    //密码
    if (type == RegistInputType.pwd) {
      if (v == null || v.isEmpty) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '密码不能为空';
        });
      } else if (!RegUtil.isValidPWD(v)) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '需含特殊字符[!@#\$%^&*()_?<>{}]';
        });
      } else if (!RegUtil.isValidPWDLength(v)) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '密码不符合规范:长度需为8-18位';
        });
      } else if (!RegUtil.isValidPWDHaveAaZz(v)) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '密码不符合规范:需含有字母[a-zA-Z]';
        });
      } else if (!RegUtil.isValidPWDHaveNumber(v)) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '密码不符合规范:需含有数字[0-9]]';
        });
      }
      // else if (pwdConfrimController.text != pwdController.text) {
      //   setState(() {
      //     showPWDErr = true;
      //     errPWDInfo = '两次密码不一致';
      //   });
      // }
      else {
        setState(() {
          showPWDErr = false;
          errPWDInfo = '';
        });
      }
      return;
    }

    //确认密码
    if (type == RegistInputType.pwdConfirm) {
      if (v == null || v.isEmpty) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '确认密码不能为空';
        });
      } else if (!RegUtil.isValidPWD(v)) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '需含特殊字符[!@#\$%^&*()_?<>{}]';
        });
      } else if (!RegUtil.isValidPWDLength(v)) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '密码不符合规范:长度需为8-18位';
        });
      } else if (!RegUtil.isValidPWDHaveAaZz(v)) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '密码不符合规范:需含有字母[a-zA-Z]';
        });
      } else if (!RegUtil.isValidPWDHaveNumber(v)) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '密码不符合规范:需含有数字[0-9]]';
        });
      } else if (pwdConfrimController.text != pwdController.text) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '两次密码不一致';
        });
      } else {
        setState(() {
          showConfrimPWDErr = false;
          errConfirmPWDInfo = '';
        });
      }
      return;
    }
  }
}
