import 'dart:async';
import 'dart:convert';

import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/model/bdh_login_multi_account_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/user/we_chat_auth/native_paramter_back.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:pinput/pinput.dart';
import 'package:smart_auth/smart_auth.dart';

// ignore: must_be_immutable
class LoginPincodeInputBdhDigital extends StatefulWidget {
  String phoneNumber;
  LoginPincodeInputBdhDigital({super.key, required this.phoneNumber});

  @override
  State<LoginPincodeInputBdhDigital> createState() =>
      _LoginPincodeInputBdhDigitalState();
}

class _LoginPincodeInputBdhDigitalState
    extends State<LoginPincodeInputBdhDigital> {
  Timer? timer;
  int count = 60;
  bool invalidCode = false;
  String errInfo = '验证码有误';
  String tempPhoneNumber = '';
  GlobalKey<_PinputViewState> pinInputKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    tempPhoneNumber = maskPhoneNumber(widget.phoneNumber);
    setState(() {});
    getSmsCode();
  }

  @override
  void dispose() {
    if (timer != null) {
      timer?.cancel();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: HexColor('#F3F3F7'),
        leading: IconButton(
          onPressed: () {
            final childState = pinInputKey.currentState;
            if (childState != null) {
              childState.focusNode.unfocus();
            }
            Future.delayed(const Duration(milliseconds: 300), () {
              Navigator.of(context).pop();
            });
          },
          icon: Image.asset(
              width: 42.px,
              height: 42.px,
              ImageHelper.wrapAssets("backArrowImg.png")),
        ),
      ),
      body: Container(
        width: 375.px,
        decoration: BoxDecoration(color: HexColor('#F3F3F7')),
        child: Column(
          children: [
            Text(
              '请输入短信验证码',
              style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 30.px,
                  fontFamily: 'PingFang SC'),
            ),
            Container(
              padding: EdgeInsets.only(top: 16.px),
              child: RichText(
                text: TextSpan(children: [
                  TextSpan(
                      text: "已向",
                      style: TextStyle(
                        color: const Color.fromRGBO(0, 0, 0, 0.5),
                        fontSize: 12.px,
                        fontWeight: FontWeight.w400,
                      )),
                  TextSpan(
                      text: ' +86 $tempPhoneNumber',
                      style: TextStyle(
                        color: const Color.fromRGBO(7, 44, 29, 1),
                        fontSize: 12.px,
                        fontWeight: FontWeight.w400,
                      )),
                  TextSpan(
                      text: " 发送验证码",
                      style: TextStyle(
                        color: const Color.fromRGBO(0, 0, 0, 0.5),
                        fontSize: 12.px,
                        fontWeight: FontWeight.w400,
                      )),
                ]),
              ),
            ),
            // Text('已向 +86 $tempPhoneNumber发送验证码'),
            const SizedBox(height: 40),
            Stack(
              clipBehavior: Clip.none,
              children: [
                Container(
                  padding: EdgeInsets.only(bottom: 50.px),
                  child: PinputView(
                    key: pinInputKey,
                    pinputCallBack: (code) {
                      smsLogin({
                        "telephone": widget.phoneNumber,
                        "code": code,
                        "loginType": 1,
                        "systemCode": kSystemCode
                      });
                    },
                  ),
                ),
                Positioned(
                    bottom: 0.px,
                    right: 0.px,
                    child: SizedBox(
                      // decoration: BoxDecoration(border: Border.all(width: 1)),
                      height: 30.px,
                      width: 290.px,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          invalidCode
                              ? Text(
                                  errInfo,
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(254, 44, 85, 1),
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14.px,
                                      fontFamily: 'PingFang SC'),
                                )
                              : Container(),
                          Row(
                            children: [
                              count == 0
                                  ? Container()
                                  : Text(
                                      '没有收到?($count) ',
                                      style: TextStyle(
                                          color: const Color.fromRGBO(
                                              153, 153, 153, 1),
                                          fontWeight: FontWeight.w500,
                                          fontSize: 14.px,
                                          fontFamily: 'PingFang SC'),
                                    ),
                              SizedBox(width: 5.px),
                              GestureDetector(
                                onTap: () {
                                  final childState = pinInputKey.currentState;
                                  if (childState != null) {
                                    childState.pinController.clear();
                                  }
                                  if (count <= 0) {
                                    count = 60;
                                  }
                                  setState(() {
                                    invalidCode = false;
                                  });
                                  getSmsCode();
                                },
                                child: Text(
                                  '重新发送',
                                  style: TextStyle(
                                      color:
                                          // const Color.fromRGBO(8, 214, 103, 1),
                                          const Color.fromRGBO(0, 127, 255, 1),
                                      fontWeight: FontWeight.w500,
                                      fontSize: 14.px,
                                      fontFamily: 'PingFang SC'),
                                ),
                              ),
                            ],
                          )
                        ],
                      ),
                    ))
              ],
            )
          ],
        ),
      ),
    );
  }

  String maskPhoneNumber(String phoneNumber) {
    if (phoneNumber.length != 11) {
      throw ArgumentError('Phone number must be 11 digits long');
    }
    // 将第4位到第9位替换为 *
    String maskedNumber =
        phoneNumber.substring(0, 3) + '******' + phoneNumber.substring(9);

    return maskedNumber;
  }

  getSmsCode() async {
    if (count < 60 && count > 0) {
      showToast("请等计时结束再试");
      return;
    }
    timer?.cancel();
    if (RegUtil.isPhoneNumber(widget.phoneNumber)) {
      timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          count--;
        });
        if (count <= 0) {
          count = 60;
          timer.cancel();
        }
      });

      var result = await BDHResponsitory.getSmsCode(
          {"phone": widget.phoneNumber, "template": ""});
      if (result.success ?? false) {
        showToast(result.msg ?? "");
      } else {
        showToast(result.msg ?? "");
        if (timer != null) {
          timer?.cancel();
        }
      }
    } else {
      showToast("请输入正确的手机号");
    }
  }

  smsLogin(Map parmater) async {
    var res = await BDHResponsitory.login(parmater);
    if (res['success'] == true) {
      Map data = res['data'];
      // 多个账号
      if (data.containsKey('code') && data['code'] == '30555') {
        BdhLoginMultiAccountModel model =
            BdhLoginMultiAccountModel.fromJson(res['data']);
        showToast("请选择账号登录");
        Navigator.pushNamed(context, RouteName.loginChooseAccountBdhDigital,
                arguments: model)
            .then((backModel) {
          if (backModel != null) {
            var backTempModel = backModel as Account;
            smsLogin({
              "systemCode": kSystemCode,
              "loginName": backTempModel.loginName, // 友盟需要的token
              "loginType": 3, //票据登录
              "ticket": model.ticket //ios 或 android
            });
          }
        });
      } else {
        // 单个账号
        UserInfo result = UserInfo.fromJson(res);
        result.data!.ifRegistered = "1";
        String? orgCode = result.data?.pluginInfo?.orgInfos?.first.orgCode;
        GlobalServiceView.orgCode = orgCode ?? '';
        StorageManager.storage!.setItem(kUser, jsonEncode(result.toJson()));
        showToast("登录成功");

        NativeParamterBack.uploadTokenToServerWhenLogin();
        if (mounted) {
          // Navigator.of(context).pushReplacementNamed(RouteName.tabMain);
          // Navigator.pushAndRemoveUntil(
          //   context,
          //   CupertinoPageRoute(builder: (context) => const TabMainPage()),
          //   (Route<dynamic> route) => false, // 该条件确保移除所有之前的页面
          // );
          RouteSettings setting = const RouteSettings(name: RouteName.tabMain);
          Navigator.pushAndRemoveUntil(
            context,
            MyRouter.generateRoute(setting)!,
            (Route<dynamic> route) => false, // 该条件确保移除所有之前的页面
          );
        }
      }
    } else {
      String msg = res['msg'] ?? '登录失败';
      setState(() {
        invalidCode = true;
        errInfo = msg.length > 8 ? msg.substring(0, 8) : msg;
      });
      showToast(res['msg'] ?? "登录失败");
    }
  }

  smsLogin1(String code) async {
    var result = await BDHResponsitory.login({
      "telephone": widget.phoneNumber,
      "code": code,
      "loginType": 1,
      "systemCode": "bdh-app"
    });
    if (result.success ?? false) {
      result.data!.ifRegistered = "1";
      StorageManager.storage!.setItem(kUser, jsonEncode(result.toJson()));

      showToast("登录成功");
      NativeParamterBack.uploadTokenToServerWhenLogin();
      if (mounted) {
        Navigator.of(context).pop();
        Navigator.of(context).pushReplacementNamed(RouteName.tabMain);
      }
    } else {
      String msg = result.msg ?? '登录失败';
      setState(() {
        invalidCode = true;
        errInfo = msg.length > 8 ? msg.substring(0, 8) : msg;
      });
      showToast(result.msg ?? "登录失败");
    }
  }
}

// ignore: must_be_immutable
class PinputView extends StatefulWidget {
  Function(String code) pinputCallBack;
  PinputView({Key? key, required this.pinputCallBack}) : super(key: key);

  @override
  State<PinputView> createState() => _PinputViewState();
}

class _PinputViewState extends State<PinputView> {
  late final SmsRetriever smsRetriever;
  // SmartAuth smartAuth = SmartAuth.instance;
  late final TextEditingController pinController;
  late final FocusNode focusNode;
  late final GlobalKey<FormState> formKey;

  @override
  void initState() {
    super.initState();

    formKey = GlobalKey<FormState>();
    pinController = TextEditingController();
    focusNode = FocusNode();

    /// In case you need an SMS autofill feature
    // smsRetriever = SmsRetrieverImpl(smartAuth);
    smsRetriever = SmsRetrieverImpl(
      SmartAuth(),
    );

    Future.delayed(const Duration(milliseconds: 50), () {
      if (mounted) {
        FocusScope.of(context).requestFocus(focusNode);
      }
    });
  }

  @override
  void dispose() {
    pinController.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    const focusedBorderColor = Color.fromRGBO(0, 127, 255, 1);
    const fillColor = Color.fromRGBO(243, 246, 249, 0);
    const borderColor = BDHColor.kClear;
    final defaultPinTheme = PinTheme(
      width: 40.px,
      height: 54.px,
      textStyle: const TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.w400,
        fontFamily: 'bayon',
        color: Color.fromRGBO(0, 0, 0, 1),
      ),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.px),
          border: Border.all(color: borderColor),
          color: Colors.white),
    );

    /// Optionally you can use form to validate the Pinput
    return Form(
      key: formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Directionality(
            // Specify direction if desired
            textDirection: TextDirection.ltr,
            child: Pinput(
              // You can pass your own SmsRetriever implementation based on any package
              // in this example we are using the SmartAuth

              smsRetriever: smsRetriever,
              showCursor: false,
              errorText: '',
              errorTextStyle: TextStyle(
                  color: const Color.fromRGBO(254, 44, 85, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: 14.px,
                  fontFamily: 'PingFang SC'),
              errorBuilder: (errorText, pin) => SizedBox(
                // decoration: BoxDecoration(border: Border.all(width: 1)),
                height: 0.px,
                width: 0.px,
              ),
              pinAnimationType: PinAnimationType.slide,
              // animationDuration: Duration(seconds: 1),
              length: 6,
              controller: pinController,
              focusNode: focusNode,
              defaultPinTheme: defaultPinTheme,
              separatorBuilder: (index) => SizedBox(width: 10.px),
              validator: (value) {
                return '';
                // return value == '2222' ? null : '';
              },
              hapticFeedbackType: HapticFeedbackType.heavyImpact,
              onCompleted: (pin) {
                debugPrint('onCompleted: $pin');
                widget.pinputCallBack(pin);
              },
              onChanged: (value) {
                debugPrint('onChanged: $value');
              },
              cursor: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    margin: const EdgeInsets.only(bottom: 9),
                    width: 22,
                    height: 1,
                    color: Colors.red,
                  ),
                ],
              ),
              focusedPinTheme: defaultPinTheme.copyWith(
                decoration: defaultPinTheme.decoration!.copyWith(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: focusedBorderColor),
                ),
              ),
              submittedPinTheme: defaultPinTheme.copyWith(
                decoration: defaultPinTheme.decoration!.copyWith(
                  color: fillColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: focusedBorderColor),
                ),
              ),
              errorPinTheme: defaultPinTheme.copyBorderWith(
                border: Border.all(color: Colors.redAccent),
              ),
            ),
          ),
          // TextButton(
          //   onPressed: () {
          //     focusNode.unfocus();
          //     formKey.currentState!.validate();
          //   },
          //   child: const Text('Validate'),
          // ),
        ],
      ),
    );
  }
}

class SmsRetrieverImpl implements SmsRetriever {
  const SmsRetrieverImpl(this.smartAuth);

  final SmartAuth smartAuth;

  @override
  Future<void> dispose() {
    return smartAuth.removeSmsListener();
  }

  @override
  Future<String?> getSmsCode() async {
    final signature = await smartAuth.getAppSignature();
    debugPrint('App Signature: $signature');
    final res = await smartAuth.getSmsCode(
      useUserConsentApi: true,
    );
    if (res.succeed && res.codeFound) {
      return res.code!;
    }
    return null;
  }

  @override
  bool get listenForMultipleSms => false;
}


// class SmsRetrieverImpl implements SmsRetriever {
//   const SmsRetrieverImpl(this.smartAuth);

//   final SmartAuth smartAuth;

//   @override
//   Future<void> dispose() {
//     return smartAuth.removeSmsRetrieverApiListener();
//     // return smartAuth.removeSmsListener();
//   }

//   @override
//   Future<String?> getSmsCode() async {
//     final signature = await smartAuth.getAppSignature();
//     debugPrint('App Signature: $signature');
//     final res = await smartAuth.getSmsWithRetrieverApi();
//     if (res.hasData) {
//       debugPrint('smsRetriever: $res');
//       final code = res.requireData.code;

//       /// The code can be null if the SMS was received but
//       /// the code was not extracted from it
//       ///
//       return code;
//     } else {
//       debugPrint('smsRetriever failed: $res');
//     }

//     // final res = await smartAuth.getSmsCode(
//     //   useUserConsentApi: true,
//     // );
//     // if (res.succeed && res.codeFound) {
//     //   return res.code!;
//     // }
//     return null;
//   }

//   @override
//   bool get listenForMultipleSms => false;
// }
