import 'dart:convert';
import 'dart:io';
import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_login_input_view.dart';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/model/bdh_login_multi_account_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/login/privacy_agreement_view.dart';
import 'package:bdh_smart_agric_app/pages/user/we_chat_auth/native_paramter_back.dart';
import 'package:bdh_smart_agric_app/utils/debounce_throttle_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/request/message_new_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_tool.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';
import 'package:encrypt/encrypt.dart' as enc;
import 'package:umeng_common_sdk/umeng_common_sdk.dart';
import 'package:umeng_verify_sdk/umeng_verify_sdk.dart';

class LoginBdhDigitalPage extends StatefulWidget {
  const LoginBdhDigitalPage({super.key});

  @override
  State<StatefulWidget> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginBdhDigitalPage>
    with WidgetsBindingObserver {
  TextEditingController loginNameController = TextEditingController();
  TextEditingController passwordController = TextEditingController();
  bool isShowPassWord = false;
  bool isCheck = false;
  String _token = '';
  bool _canTelePhoneLogin = false;
  // bool _isHaveSimCard = true;
  bool _isHaveCarrierName = false;
  // int loginType = 2; //默认是账号密码登录 1 手机号验证码登录 2 账号密码登录 3 票据(验证码+一键) 6一键登录
  final _debounce = Debouncer(milliseconds: 500);

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    GlobalServiceView.hidenView();
    // nativeCheckIsHaveSimCard();
    // initVerifySDKInfo();
    // checkEnvAvilable();
    // getCurrentCarrierName();
  }

  getCurrentCarrierName() async {
    String? carrierName = await UmengVerifySdk.getCurrentCarrierName();
    Log.i("获取运营商名称carrierName=: $carrierName");
    // showToast('运营商名称$carrierName');
    //unknown
    if (carrierName == "unknown" || (carrierName ?? '').contains("UNKNOW")) {
      setState(() {
        _isHaveCarrierName = false;
      });
    } else {
      setState(() {
        _isHaveCarrierName = true;
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // if (state == AppLifecycleState.resumed) {
    //   Log.i("AppLifecycleState.resumed");
    //   if (Platform.isAndroid) {
    //     UmengVerifySdk.quitLoginPage_android();
    //   }
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(
                      ImageHelper.wrapAssets("loginBDHDigitalBG.png")),
                  fit: BoxFit.cover),
            ),
          ),
          // Column(
          //   children: [
          //     Expanded(flex: 10, child: getCenterInputView()),
          //     Expanded(flex: 3, child: getBottomBtnView()),
          //   ],
          // )
          SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                getCenterInputView(),
                getBottomBtnView(),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget getCenterInputView() {
    return SizedBox(
      width: 375.px,
      child: Column(
        children: [
          SizedBox(height: MediaQuery.of(context).padding.top + 56.px),
          // Image.asset(
          //     width: 112.px, ImageHelper.wrapAssets("welcomeLoginImg.png")),
          // SizedBox(height: 10.px),
          Image.asset(
              width: 200.px,
              height: 93.px,
              ImageHelper.wrapAssets("welcomeLoginBDHDigitalImg.png")),

          //账号
          Container(
            margin: EdgeInsets.only(top: 30.px),
            child: BdhLoginInputView(
                titleName: '账号',
                tipName: '请输入账号',
                borderRadius: BorderRadius.all(Radius.circular(8.px)),
                controller: loginNameController),
          ),

          //密码
          Container(
            margin: EdgeInsets.only(top: 10.px),
            child: BDHLoginInputPWDView(
                titleName: '密码',
                tipName: '请输入密码',
                borderRadius: BorderRadius.all(Radius.circular(8.px)),
                controller: passwordController),
          ),

          //忘记密码
          getForgetPWDView(),

          //隐私协议
          PrivacyAgreementView(
              checkBoxNormalImg: 'privacy_agreement_selected_digital.png',
              agreementTextColor: const Color.fromRGBO(0, 127, 255, 1),
              isCheckCallBack: (res) {
                setState(() {
                  isCheck = res;
                });
              }),

          //登录
          GestureDetector(
            onTap: () {
              if (isCheck == false) {
                showToast("请先阅读并同意用户协议和隐私协议");
                return;
              }
              if (loginNameController.text.isEmpty ||
                  passwordController.text.isEmpty) {
                showToast("用户名或者密码不能为空");
              } else {
                login();
              }
            },
            child: BDHUserLoginButton(
              width: 296.px,
              height: 52.px,
              title: '登录',
              borderRadius: 8.px,
              marginBottom: 16.px,
              bgLinearGradientColors: const [
                Color.fromRGBO(0, 127, 255, 1),
                Color.fromRGBO(61, 156, 255, 1)
              ],
              marginTop: 20.px,
            ),
          ),

          //本机号码一键登录
          _canTelePhoneLogin && _isHaveCarrierName
              ? GestureDetector(
                  onTap: () {
                    if (isCheck == false) {
                      showToast("请先阅读并同意隐私协议");
                      return;
                    }

                    // 加速一键登录授权页弹起
                    _debounce.run(() {
                      UmengVerifySdk.accelerateLoginPageWithTimeout(3)
                          .then((result) {
                        Log.i("Android 登录加速结果 $result");
                        if (Platform.isAndroid) {
                          if (result["code"] == "600009") {
                            //"ret" -> "{"carrierFailedResultData":"","code":"600009","msg":"无法判运营商",
                            //"requestCode":0,"requestId":"a1f41f97-4c91-4405-bcca-61a824986bac",…"
                            setState(() {
                              _canTelePhoneLogin = false;
                              _token = "";
                            });
                            return;
                          }
                          // "code" -> "600000" "msg" -> "获取token成功"
                          UMCustomModel uiConfig = getAndroidUIConfig();
                          UmengVerifySdk.getLoginTokenWithTimeout(3, uiConfig);
                        } else {
                          // "code" -> "600000" "msg" -> "获取token成功"
                          UMCustomModel uiConfigIOS = getIOSUIConfig();
                          UmengVerifySdk.getLoginTokenWithTimeout(
                              3, uiConfigIOS);
                        }
                      });
                    });
                  },
                  child: BDHUserLoginButton(
                    width: 296.px,
                    height: 52.px,
                    title: '本机号码一键登录',
                    borderRadius: 50.px,
                    marginBottom: 16.px,
                  ),
                )
              : Container()
        ],
      ),
    );
  }

//忘记密码
  Widget getForgetPWDView() {
    return Container(
      padding: EdgeInsets.only(left: 40.px, right: 40.px, top: 16.px),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          GestureDetector(
            onTap: () {
              Navigator.of(context)
                  .pushNamed(RouteName.loginVerifyBdhDigitalPage);
            },
            child: Text(
              '验证码登录',
              style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 127, 255, 1)),
            ),
          ),
          GestureDetector(
            onTap: () {
              // Navigator.pushNamed(context, RouteName.forgetPassword);
              Navigator.pushNamed(context, RouteName.forgetPwdBdhDigitalPage);
            },
            child: Text(
              '忘记密码？',
              style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 0, 0, 0.4)),
            ),
          )
        ],
      ),
    );
  }

//底部按钮
  Widget getBottomBtnView() {
    return Container(
        padding: EdgeInsets.only(
            left: 20.px, right: 20.px, bottom: 100.px, top: 100.px),
        width: 375.px,
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(bottom: 10.px),
              height: 22.px,
              width: 300.px,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    height: 1.px,
                    width: 85.px,
                    color: const Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                  Container(
                    padding: EdgeInsets.only(left: 20.px, right: 20.px),
                    child: Text('其他',
                        style: TextStyle(
                            color: const Color.fromRGBO(0, 0, 0, 0.2),
                            fontSize: 14.px,
                            fontWeight: FontWeight.w300)),
                  ),
                  Container(
                    height: 1.px,
                    width: 85.px,
                    color: const Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                //忘记密码
                // GestureDetector(
                //   child: Image.asset(
                //       width: 48.px,
                //       ImageHelper.wrapAssets("forgive_password.png")),
                //   onTap: () {
                //     Navigator.pushNamed(context, RouteName.forgetPassword);
                //   },
                // ),

                //验证码登录
                // GestureDetector(
                //   child: BdHLoginOptionButton(
                //     buttonName: '验证码登录',
                //     imageName: 'verificationCodeLoginImg.png',
                //   ),
                //   onTap: () {
                //     Navigator.of(context)
                //         .pushNamed(RouteName.loginVerifyBdhDigitalPage);
                //   },
                // ),

                //新用户注册
                GestureDetector(
                  child: BdHLoginOptionButton(
                    buttonName: '新用户注册',
                    imageName: 'touristLoginImg.png',
                  ),
                  onTap: () {
                    // Navigator.of(context)
                    //     .pushNamed(RouteName.registerBdhDigitalPage);
                    Navigator.of(context).pushNamed(RouteName.register);
                  },
                ),

                // //手机号登录
                // GestureDetector(
                //   child: BdHLoginOptionButton(
                //     buttonName: '手机号登录',
                //     imageName: 'cellPhoneLoginImg.png',
                //   ),
                //   onTap: () {
                //     Navigator.of(context).pushNamed(RouteName.loginVerify);
                //   },
                // ),

                //游客模式
                // GestureDetector(
                //   child: BdHLoginOptionButton(
                //     buttonName: '游客模式',
                //     imageName: 'touristLoginImg.png',
                //   ),
                //   onTap: () {
                //     //TODO 调用临时账号注册接口,生成游客userInfo
                //     BDHResponsitory.touristLogin().then((result) {
                //       if (result.success ?? false) {
                //         StorageManager.storage!
                //             .setItem(kUser, jsonEncode(result.toJson()));
                //         // showToast("游客登录成功");
                //         if (mounted) {
                //           Navigator.of(context)
                //               .pushReplacementNamed(RouteName.tabMain);
                //         }
                //       } else {
                //         // showToast(result.msg ?? "游客登录失败");
                //       }
                //     });
                //   },
                // ),
              ],
            ),
          ],
        ));
  }

//认证登录
  initVerifySDKInfo() {
    if (Platform.isAndroid) {
      UmengVerifySdk.register_android();
    }
    UmengCommonSdk.initCommon(
        '6722d46c7e5e6a4eeb897f57', 'ios appkey', 'Umeng');
    UmengVerifySdk.setVerifySDKInfo(kAndroidPhoneAuthKey, kIosPhoneAuthKey)
        .then((map) {
      Log.i("初始化initVerifySDKInfo:$map");
    });
    // //检查当前环境是否支持一键登录
    // if (Platform.isAndroid) {
    //   // 检查当前环境是否支持一键登录或号码认证，通过[setTokenResultCallback_android]设置的监听回调结果
    //   UmengVerifySdk.checkEnvAvailable_android(UMEnvCheckType.type_login);
    // } else {
    //   UmengVerifySdk.checkEnvAvailableWithAuthType_ios(
    //           'UMPNSAuthTypeLoginToken')
    //       .then((result) {});
    // }

    //android 监听回调结果
    UmengVerifySdk.setTokenResultCallback_android((result) {
      Log.i("android 监听回调结果 setTokenResultCallback_android:${result}");

      if (result["code"] == "600002") {
        // "code" -> "600002" "msg" -> "授权页已存在，不能再次拉起"
        UmengVerifySdk.getVerifyTokenWithTimeout_android(3);
        UmengVerifySdk.quitLoginPage_android();
        return;
      }

      if (result["code"] == "600015") {
        // "code" -> "600015" "msg" -> "msg" -> "WIFI切换超时"
        setState(() {
          _canTelePhoneLogin = false;
          _token = '';
        });
        UmengVerifySdk.quitLoginPage_android();
        showToast("WIFI切换超时, 不支持当前号码不支持一键登录");
        return;
      }

      if (result["code"] == "600008") {
        // "code" -> "600008" "msg" -> "msg" -> "移动网络未开启"
        setState(() {
          _canTelePhoneLogin = false;
          _token = '';
        });
        UmengVerifySdk.quitLoginPage_android();
        showToast("移动网络未开启");
        return;
      }

      if (result["code"] == "600007") {
        //"code" -> "600007" "msg" -> "SIM卡无法检测"
        setState(() {
          _canTelePhoneLogin = false;
          _token = '';
        });
        UmengVerifySdk.quitLoginPage_android();
        showToast("无SIM卡");
        return;
      }

      if (result["code"] == "600017") {
        //"code" -> "600017"  "msg" -> "AppID Secret解析失败"
        setState(() {
          _canTelePhoneLogin = false;
          _token = '';
        });
        UmengVerifySdk.quitLoginPage_android();
        showToast("AppID Secret解析失败");
        return;
      }

      if (result["code"] == "600001") {
        // "msg" -> "唤起授权页成功" "code" -> "600001"
      }

      if (result["code"] == "600009") {
        //"code" -> "600009" 无法判运营商
        setState(() {
          _canTelePhoneLogin = false;
          _token = '';
        });
        UmengVerifySdk.quitLoginPage_android();
        showToast("无法判运营商");
        return;
      }

      if (result["code"] == "600024") {
        // "code" -> "600024" "msg" -> "终端支持认证"
        setState(() {
          _canTelePhoneLogin = true;
          _token = '';
        });
      }

      if (result["code"] == "600000") {
        // "code" -> "600000" "msg" -> "获取token成功"
        _token = result["token"];
        // 登录成功后，需要调用[quitLoginPage_android]方法去关闭授权页面
        // 注意：如果不调用[quitLoginPage_android]方法，授权页面将不会关闭
        UmengVerifySdk.quitLoginPage_android();
        telephoneLogin({
          "systemCode": kSystemCode,
          "token": _token, // 友盟需要的token
          "loginType": 6,
          "deviceType": "android" //ios 或 android
        });
        //获取到Token > 请求后端接口 校验 Token, 获取电话号码, 注册登录, 跳转首页
      }
    });

    //iOS 监听回调结果
    UmengVerifySdk.getLoginTokenCallback((result) {
      Log.i("iOS 监听回调结果resultDic>>>>:$result");
      Log.i("iOS 监听回调结果token:${result["token"]}");

      if (result["code"] == "600002") {
        // "code" -> "600002" "msg" -> "授权页已存在，不能再次拉起"
        UmengVerifySdk.getVerifyTokenWithTimeout_ios(3);
        return;
      }

      if (result["resultCode"] == "600001") {
        // "msg" -> "唤起授权页成功" "code" -> "600001"
      }

      if (result["resultCode"] == "700003") {
        //"resultCode" -> "700003" "msg" -> "点击了协议CheckBox按钮"
      }

      if (result["resultCode"] == "700002") {
        //"resultCode" -> "700002" "msg" -> "点击了登录按钮"
      }

      if (result["resultCode"] == "700001") {
        //"resultCode" -> "700001" "msg" -> "切换到其他方式"
        UmengVerifySdk.cancelLoginVCAnimated(true);
        Future.delayed(const Duration(milliseconds: 300), () {
          Navigator.of(context).pushNamed(RouteName.loginVerify);
        });
      }

      if (result["resultCode"] == "600000") {
        // "code" -> "600000" "msg" -> "获取token成功"
        //"token" -> "eyJjIjoiRStwd3RJWmRmWDBVb1NNMkY1RzM2WG1SZVN0T2t5enVEcjQ2RlNGMkJWc05Ma1JKTUNER09ONG05MFJOSU5HZjBESVp5bTRMVDNlRVxubnBuUFNrWFJYNkZ2…"
        if (result['token'] != null) {
          bool isTrue = true;
          _token = result["token"];
          UmengVerifySdk.cancelLoginVCAnimated(isTrue);
          telephoneLogin({
            "systemCode": kSystemCode,
            "token": _token, // 友盟需要的token
            "loginType": 6,
            "deviceType": "ios" //ios 或 android
          });
        }
      }
    });

//ios 点击自定view 回调结果
    UmengVerifySdk.getWidgetEventCallback((result) {
      Log.i("iOS 点击自定view 回调结果 :$result");
      //"widgetId" -> "clickedBackButton"
      if (result["widgetId"] == "clickedBackButton") {
        UmengVerifySdk.cancelLoginVCAnimated(true);
      }
    });

//android 点击自定view 回调结果
    UmengVerifySdk.setUIClickCallback_android((result) {
      //{code: 700002, jsonString: {"isChecked":false}}
      Log.i("android 点击自定view 回调结果 :$result");
      if (result["code"] == "700002") {
        if (result["jsonString"] != null) {
          String res = result["jsonString"];
          Map<String, dynamic> map = jsonDecode(res);
          bool isChecked = map["isChecked"];
          if (!isChecked) {
            // showToast("请勾选同意服务条款和隐私政策");
            Log.i("xxxxxxx android 点击自定view 回调结果");
          }
        }
      }

      if (result["code"] == "700001") {
        //"resultCode" -> "700001" "msg" -> "切换到其他方式"
        Future.delayed(const Duration(milliseconds: 300), () {
          Navigator.of(context).pushNamed(RouteName.loginVerify);
        });
      }
    });
  }

  //检查当前环境是否支持
  checkEnvAvilable() {
    if (Platform.isAndroid) {
      // 检查当前环境是否支持一键登录或号码认证，通过[setTokenResultCallback_android]设置的监听回调结果
      UmengVerifySdk.checkEnvAvailable_android(UMEnvCheckType.type_login);
    } else {
      UmengVerifySdk.checkEnvAvailableWithAuthType_ios(
              'UMPNSAuthTypeLoginToken')
          .then((result) {
        Log.i("ios检查当前环境是否支持 回调结果 :$result");
        if (result["resultCode"] == "600007") {
          //"resultCode" -> "600007"  "msg" -> "无SIM卡"
          setState(() {
            _canTelePhoneLogin = false;
            _token = '';
          });
          showToast("无SIM卡");
          return;
        }

        if (result["resultCode"] == "600017") {
          //"code" -> "600017"  "msg" -> "AppID Secret解析失败"
          setState(() {
            _canTelePhoneLogin = false;
            _token = '';
          });
          showToast("AppID Secret解析失败");
          return;
        }

        if (result["resultCode"] == "600009") {
          //"code" -> "600009" 无法判运营商
          setState(() {
            _canTelePhoneLogin = false;
            _token = '';
          });
          showToast("无法判运营商");
          return;
        }

        if (result["resultCode"] == "600008") {
          // "resultCode" -> "600008" "msg" -> "请检查手机蜂窝网络及APP网络使用权限是否开启"
          setState(() {
            _canTelePhoneLogin = false;
            _token = '';
          });
          showToast("请检查手机蜂窝网络及APP网络使用权限是否开启");
          return;
        }

        if (result["resultCode"] == "600024") {
          // "code" -> "600024" "msg" -> "终端支持认证"
          setState(() {
            _canTelePhoneLogin = true;
            _token = '';
          });
        }
      });
    }
  }

//ios uiconfig
  UMCustomModel getIOSUIConfig() {
    UMCustomModel uiConfig = UMCustomModel();
    double w = ScreenTool().screenWidth;
    double h = ScreenTool().screenHeight;
    // uiConfig.contentViewFrame = [-1, -1, w, h];
    uiConfig.prefersStatusBarHidden = true; //状态栏是否隐藏，默认NO
    uiConfig.isAutorotate = false; //不旋转
    uiConfig.backgroundImage = 'phone_login_bg';

    //导航栏
    uiConfig.navIsHidden = true;
    // uiConfig.navTitle = ['  ', Colors.transparent.value, 1];
    // uiConfig.backgroundColor_ios = Colors.transparent.value;
    // uiConfig.navColor = Colors.white.value;

    //号码
    uiConfig.numberColor = Colors.black.value;
    uiConfig.numberFont = 30;

    //登录按钮
    uiConfig.loginBtnBgImgs_ios = [
      "phone_login_btn",
      "phone_login_btn_un",
      "phone_login_btn_h"
    ];

    uiConfig.loginBtnText = ["本机号码一键登录", Colors.white.value, 20];
    double btnW = 296.px;
    double btnH = 52.px;
    uiConfig.loginBtnFrame = [
      (w - btnW) * 0.5,
      (h - btnH) * 0.5 - 60.px,
      -1,
      btnW,
      btnH
    ];

    //隐私政策
    uiConfig.privacyOne = ["用户协议", "https://www.bdhic.com/Anticipation.html"];
    uiConfig.privacyTwo = ["隐私政策", "https://smartagric.bdhic.com/privacy.html"];

    //切换到其他方式
    // uiConfig.changeBtnTitle = ["切换手机号", Colors.grey[400]?.value, 16];
    uiConfig.changeBtnTitle = [
      "切换手机号",
      const Color.fromRGBO(0, 0, 0, 0.6).value,
      16
    ];
    uiConfig.changeBtnIsHidden = false;

    //自定义返回按钮
    UMCustomWidget customButton =
        UMCustomWidget("clickedBackButton", UMCustomWidgetType.button);
    customButton.widgetId = "clickedBackButton";
    customButton.type = UMCustomWidgetType.button;
    customButton.title = "";
    customButton.titleFont = 1;
    customButton.textAlignment = UMCustomWidgetTextAlignmentType.center;
    customButton.left = (w - 45.px).toInt();
    customButton.top = 50;
    customButton.width = 24;
    customButton.height = 24;
    // customButton.backgroundColor = Colors.red.value;
    customButton.titleColor = Colors.transparent.value;
    customButton.isClickEnable = true;
    customButton.btnNormalImageName_ios = "arrow_left";
    customButton.rootViewId = UMRootViewId.body;
    uiConfig.customWidget = [
      customButton.toJsonMap(),
      // customButton1.toJsonMap(),
      // customTextView.toJsonMap()
    ];
    return uiConfig;
  }

//android uiconfig
  UMCustomModel getAndroidUIConfig() {
    UMCustomModel uiConfig = UMCustomModel();
    double w = ScreenTool().screenWidth;
    double h = ScreenTool().screenHeight;
    uiConfig.contentViewFrame = [-1, -1, w, h];
    // uiConfig.prefersStatusBarHidden = true; //状态栏是否隐藏，默认NO
    // uiConfig.alertTitleBarFrame_ios = [80, 100, 260, 500];
    uiConfig.isAutorotate = false; //不旋转
    uiConfig.backgroundImage = 'phone_login_bg';

    //导航栏
    uiConfig.navIsHidden = false;
    uiConfig.navTitle = ['  ', Colors.transparent.value, 16];
    uiConfig.navColor = Colors.transparent.value;
    uiConfig.navBackImage = 'arrow_left';
    uiConfig.navBackButtonFrame = [-1, -1, 24, 24];

    //号码
    uiConfig.numberColor = Colors.black.value;
    uiConfig.numberFont = 30;

    //登录按钮
    uiConfig.loginBtnBgImg_android = "phone_login_btn";

    //uiConfig.animationDuration_ios=0;
    // uiConfig.alertBlurViewColor_ios = Colors.red.value;
    // uiConfig.alertBlurViewAlpha_ios = 0.8;
    // uiConfig.alertTitle_ios = ["aaaabbb", Colors.red.value, 20];
    // uiConfig.navColor = Colors.orange.value;
    uiConfig.loginBtnText = ["", Colors.transparent.value, 1];

    uiConfig.privacyOne = ["用户协议", "https://www.bdhic.com/Anticipation.html"];
    uiConfig.privacyTwo = ["隐私政策", "https://smartagric.bdhic.com/privacy.html"];

    //切换到其他方式
    uiConfig.changeBtnTitle = [
      "切换手机号",
      const Color.fromRGBO(0, 0, 0, 0.6).value,
      16
    ];
    uiConfig.changeBtnIsHidden = false;

    return uiConfig;
  }

//账号密码登录
  login() async {
    var loginName = loginNameController.text;
    var password = passwordController.text;
    var bytes = utf8.encode(password.toUpperCase());
    //第一层MD5
    var firstMD5 = '${md5.convert(bytes)}'.toUpperCase();
    //第二层MD5
    var md5Password = '${md5.convert(utf8.encode(firstMD5))}'.toLowerCase();

    // var enKey =

    var key = enc.Key.fromUtf8('T8d7areaVthcV9ir');
    var iv = enc.IV.fromUtf8('2I5cTwvg9oCzTChP');
    var e = enc.Encrypter(enc.AES(key, mode: enc.AESMode.cbc));
    var enData = e.encrypt(password, iv: iv);
    var password1 = enData.base64;

    var res = await BDHResponsitory.login({
      "loginName": loginName,
      "password": md5Password,
      "password1": password1,
      "systemCode": kSystemCode,
      "loginType": 2, //默认是账号密码登录 1 手机号验证码登录 2 账号密码登录 3 票据(验证码+一键) 6一键登录
    });

    if (res['success'] == true) {
      UserInfo result = UserInfo.fromJson(res);
      result.data!.ifRegistered = "1";
      String? orgCode = result.data?.pluginInfo?.orgInfos?.first.orgCode;
      GlobalServiceView.orgCode = orgCode ?? '';
      StorageManager.storage!.setItem(kUser, jsonEncode(result.toJson()));
      showToast("登录成功");
      getRoleList(); //获取角色列表
      NativeParamterBack.uploadTokenToServerWhenLogin();
      if (mounted) {
        // Navigator.of(context).pushReplacementNamed(RouteName.tabMain);

        RouteSettings setting = const RouteSettings(name: RouteName.tabMain);
        Navigator.pushAndRemoveUntil(
          context,
          MyRouter.generateRoute(setting)!,
          (Route<dynamic> route) => false, // 该条件确保移除所有之前的页面
        );
      }
    } else {
      showToast(res['msg'] ?? "登录失败");
    }

    // if (result.success ?? false) {
    //   result.data!.ifRegistered = "1";
    //   String? orgCode = result.data?.pluginInfo?.orgInfos?.first.orgCode;
    //   GlobalServiceView.orgCode = orgCode ?? '';
    //   StorageManager.storage!.setItem(kUser, jsonEncode(result.toJson()));
    //   showToast("登录成功");
    //   getRoleList(); //获取角色列表
    //   NativeParamterBack.uploadTokenToServerWhenLogin();
    //   if (mounted) {
    //     Navigator.of(context).pushReplacementNamed(RouteName.tabMain);
    //   }
    // } else {
    //   showToast(result.msg ?? "登录失败");
    // }
  }

//一键登录
  telephoneLogin(Map parmater) async {
    var res = await BDHResponsitory.login(parmater);
    Log.i('一键登录 telephoneLogin message$res');
    if (res['success'] == true) {
      Map data = res['data'];
      // 多个账号
      if (data.containsKey('code') && data['code'] == '30555') {
        BdhLoginMultiAccountModel model =
            BdhLoginMultiAccountModel.fromJson(res['data']);
        model.accounts.sort((a, b) {
          if ((a.lastLogin == 1) && (b.lastLogin == 0)) {
            return -1;
          } else if ((b.lastLogin == 1) && (a.lastLogin == 0)) {
            return 1;
          } else {
            return 0;
          }
        });

        // for()
        showToast("请选择账号登录");
        Navigator.pushNamed(context, RouteName.loginChooseAccount,
                arguments: model)
            .then((backModel) {
          if (backModel != null) {
            var backTempModel = backModel as Account;
            telephoneLogin({
              "systemCode": kSystemCode,
              "loginName": backTempModel.loginName, // 友盟需要的token
              "loginType": 3, //票据登录
              "ticket": model.ticket //ios 或 android
            });
          }
        });
      } else {
        // 单个账号
        UserInfo result = UserInfo.fromJson(res);
        result.data!.ifRegistered = "1";
        String? orgCode = result.data?.pluginInfo?.orgInfos?.first.orgCode;
        GlobalServiceView.orgCode = orgCode ?? '';
        StorageManager.storage!.setItem(kUser, jsonEncode(result.toJson()));
        showToast("登录成功");
        getRoleList(); //获取角色列表
        NativeParamterBack.uploadTokenToServerWhenLogin();
        if (mounted) {
          Navigator.of(context).pushReplacementNamed(RouteName.tabMain);
        }
      }
    } else {
      showToast(res['msg'] ?? "一键登录失败");
    }
  }

//一键登录 回调
  chooseLoginNameCallBack(Account model) {}

//获取用户角色信息
  getRoleList() {
    MessageNewResponsitory.getRoleList().then((res) {
      // Log.i('获取角色列表');
      // Log.i(res);
      if (res['code'] == 0 && res['msg'] == 'success') {
        String jsonStr = jsonEncode(res);
        StorageManager.storage!.setItem(kUserRoleList, jsonStr);
      }
    });
  }
}
