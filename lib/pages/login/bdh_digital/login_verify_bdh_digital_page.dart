import 'dart:async';
import 'dart:convert';
import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_login_input_view.dart';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/pages/login/privacy_agreement_view.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

class LoginVerifyBdhDigitalPage extends StatefulWidget {
  const LoginVerifyBdhDigitalPage({super.key});

  @override
  State<LoginVerifyBdhDigitalPage> createState() =>
      _LoginVerifyBdhDigitalPageState();
}

class _LoginVerifyBdhDigitalPageState extends State<LoginVerifyBdhDigitalPage> {
  int count = 60;
  String btnText = "获取验证码";
  bool isFirstClick = true;
  Timer? timer;
  bool isCheck = false;

  TextEditingController phoneController = TextEditingController();
  TextEditingController smsController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(
                      ImageHelper.wrapAssets("loginBDHDigitalBG.png")),
                  fit: BoxFit.cover),
            ),
          ),
          Column(
            children: [
              Expanded(flex: 10, child: getCenterInputView()),
              Expanded(flex: 3, child: getBottomBtnView()),
            ],
          )
        ],
      ),
    );
  }

  Widget getCenterInputView() {
    return SizedBox(
      width: 375.px,
      child: Column(
        children: [
          SizedBox(height: MediaQuery.of(context).padding.top),
          getTopCloseBtn(),

          Image.asset(
              width: 200.px,
              height: 93.px,
              ImageHelper.wrapAssets("welcomeLoginBDHDigitalImg.png")),

          //账号
          Container(
            margin: EdgeInsets.only(top: 30.px),
            child: BdhLoginPhoneNumberInputView(
              tipName: '请输入手机号',
              controller: phoneController,
              borderRadius: BorderRadius.all(Radius.circular(8.px)),
            ),
          ),

          //隐私协议
          PrivacyAgreementView(
              checkBoxNormalImg: 'privacy_agreement_selected_digital.png',
              agreementTextColor: const Color.fromRGBO(0, 127, 255, 1),
              isCheckCallBack: (res) {
                setState(() {
                  isCheck = res;
                });
              }),

          //登录
          GestureDetector(
            onTap: () {
              if (isCheck == false) {
                // showToast("请先阅读并同意隐私协议");
                showToast("请先阅读并同意用户协议和隐私协议");
                return;
              }
              if (RegUtil.isPhoneNumber(phoneController.text)) {
                Navigator.of(context).pushNamed(
                    RouteName.loginPincodeInputBdhDigital,
                    arguments: phoneController.text);
              } else {
                showToast("请输入正确的手机号");
              }
            },
            child: BDHUserLoginButton(
              width: 296.px,
              height: 52.px,
              title: '获取短信验证码',
              borderRadius: 8.px,
              marginBottom: 16.px,
              marginTop: 10.px,
              bgLinearGradientColors: const [
                Color.fromRGBO(0, 127, 255, 1),
                Color.fromRGBO(61, 156, 255, 1)
              ],
            ),
          ),
        ],
      ),
    );
  }

  //关闭按钮
  Widget getTopCloseBtn() {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
      },
      child: Container(
        padding: EdgeInsets.only(bottom: 10.px, top: 15.px),
        child: Row(
          children: [
            SizedBox(width: 20.px),
            Image.asset(
                width: 42.px,
                height: 42.px,
                ImageHelper.wrapAssets("closePageBtnImg.png")),
          ],
        ),
      ),
    );
  }

//底部按钮
  Widget getBottomBtnView() {
    return Container(
        padding: EdgeInsets.only(left: 20.px, right: 20.px),
        width: 375.px,
        child: Column(
          children: [
            Container(
              margin: EdgeInsets.only(bottom: 10.px),
              height: 22.px,
              width: 300.px,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    height: 1.px,
                    width: 85.px,
                    color: const Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                  Container(
                    padding: EdgeInsets.only(left: 20.px, right: 20.px),
                    child: Text('其他登录方式',
                        style: TextStyle(
                            color: const Color.fromRGBO(0, 0, 0, 0.2),
                            fontSize: 14.px,
                            fontWeight: FontWeight.w300)),
                  ),
                  Container(
                    height: 1.px,
                    width: 85.px,
                    color: const Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                ],
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                //手机号登录
                // GestureDetector(
                //   child: BdHLoginOptionButton(
                //     buttonName: '一键登录',
                //     imageName: 'cellPhoneLoginImg.png',
                //   ),
                //   onTap: () {
                //     Navigator.pop(context);
                //     // Navigator.of(context).pushNamed(RouteName.loginVerify);
                //   },
                // ),

                //密码登录
                GestureDetector(
                  child: BdHLoginOptionButton(
                    buttonName: '密码登录',
                    imageName: 'secretLoginImg.png',
                  ),
                  onTap: () {
                    Navigator.pop(context);
                    // Navigator.of(context).pushNamed(RouteName.loginVerify);
                  },
                ),

                //游客模式
                // GestureDetector(
                //   child: BdHLoginOptionButton(
                //     buttonName: '游客模式',
                //     imageName: 'touristLoginImg.png',
                //   ),
                //   onTap: () {
                //     //TODO 调用临时账号注册接口,生成游客userInfo
                //     BDHResponsitory.touristLogin().then((result) {
                //       if (result.success ?? false) {
                //         StorageManager.storage!
                //             .setItem(kUser, jsonEncode(result.toJson()));

                //         // showToast("游客登录成功");
                //         if (mounted) {
                //           Navigator.of(context)
                //               .pushReplacementNamed(RouteName.tabMain);
                //         }
                //       } else {
                //         // showToast(result.msg ?? "游客登录失败");
                //       }
                //     });
                //   },
                // ),
              ],
            ),
          ],
        ));
  }

  // @override
  Widget build1(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: LayoutBuilder(
        builder: (context, cons) {
          return SizedBox(
            width: cons.maxWidth,
            height: cons.maxHeight,
            child: Stack(
              children: [
                Image.asset(
                    width: 375.px, ImageHelper.wrapAssets("report_header.png")),
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Column(
                      children: [
                        SizedBox(
                          height: MediaQuery.of(context).padding.top + 50.px,
                        ),
                        Image.asset(
                            width: 190.px, ImageHelper.wrapAssets("logo.png")),
                        Container(
                          margin: EdgeInsets.only(top: 20.px),
                          padding: EdgeInsets.only(left: 12.px, right: 12.px),
                          width: 327.px,
                          height: 48.px,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(4.px)),
                              border: Border.all(
                                  width: 1.px,
                                  color: const Color.fromRGBO(
                                      226, 235, 231, 0.8))),
                          child: Row(
                            children: [
                              Text(
                                "手机号",
                                style: TextStyle(
                                    fontSize: 16.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(0, 0, 0, 0.2)),
                              ),
                              Container(
                                margin:
                                    EdgeInsets.only(left: 10.px, right: 10.px),
                                width: 1.px,
                                height: 24.px,
                                color: const Color.fromRGBO(226, 235, 231, 0.4),
                              ),
                              SizedBox(
                                width: 220.px,
                                child: CupertinoTextField.borderless(
                                  padding: EdgeInsets.zero,
                                  controller: phoneController,
                                  placeholder: "请输入手机号",
                                  placeholderStyle: TextStyle(
                                      fontSize: 16.px,
                                      fontWeight: FontWeight.w400,
                                      color:
                                          const Color.fromRGBO(0, 0, 0, 0.2)),
                                ),
                              )
                            ],
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: 20.px),
                          padding: EdgeInsets.only(left: 12.px, right: 12.px),
                          width: 327.px,
                          height: 48.px,
                          decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(4.px)),
                              border: Border.all(
                                  width: 1.px,
                                  color: const Color.fromRGBO(
                                      226, 235, 231, 0.8))),
                          child: Row(
                            children: [
                              Text(
                                "验证码",
                                style: TextStyle(
                                    fontSize: 16.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(0, 0, 0, 0.2)),
                              ),
                              Container(
                                margin:
                                    EdgeInsets.only(left: 10.px, right: 10.px),
                                width: 1.px,
                                height: 24.px,
                                color: const Color.fromRGBO(226, 235, 231, 0.4),
                              ),
                              SizedBox(
                                width: 136.px,
                                child: CupertinoTextField.borderless(
                                  padding: EdgeInsets.zero,
                                  controller: smsController,
                                  placeholder: "请输入验证码",
                                  placeholderStyle: TextStyle(
                                      fontSize: 16.px,
                                      fontWeight: FontWeight.w500,
                                      color:
                                          const Color.fromRGBO(0, 0, 0, 0.2)),
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  getSmsCode();
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  width: 94.px,
                                  height: 32.px,
                                  decoration: BoxDecoration(
                                      color:
                                          const Color.fromRGBO(22, 183, 96, 1),
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(2.px))),
                                  child: Text(
                                    btnText,
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 14.px,
                                        fontWeight: FontWeight.w500),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 375.px,
                          margin: EdgeInsets.only(top: 30.px, bottom: 10.px),
                        ),
                        GestureDetector(
                          onTap: () {
                            smsLogin();
                          },
                          child: BDHButtonGreen(
                            width: 327.px,
                            height: 44.px,
                            title: "登录",
                          ),
                        ),
                      ],
                    ),
                    Container(
                      margin: EdgeInsets.only(
                          bottom: MediaQuery.of(context).padding.bottom),
                      width: 249.px,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          GestureDetector(
                            child: Image.asset(
                                width: 48.px,
                                ImageHelper.wrapAssets("forgive_password.png")),
                            onTap: () {
                              Navigator.pushNamed(
                                  context, RouteName.forgetPassword);
                            },
                          ),
                          GestureDetector(
                            child: Image.asset(
                                width: 60.px,
                                ImageHelper.wrapAssets("new_register.png")),
                            onTap: () {
                              Navigator.of(context)
                                  .pushNamed(RouteName.register);
                            },
                          ),
                          GestureDetector(
                            child: Image.asset(
                                width: 48.px,
                                ImageHelper.wrapAssets("password_login.png")),
                            onTap: () {
                              Navigator.of(context).pop();
                            },
                          ),
                        ],
                      ),
                    )
                  ],
                )
              ],
            ),
          );
        },
      ),
    );
  }

  getSmsCode() async {
    if (count < 60 && count > 0) {
      showToast("请等计时结束再试");
      return;
    }
    if (RegUtil.isPhoneNumber(phoneController.text)) {
      var result = await BDHResponsitory.getSmsCode(
          {"phone": phoneController.text, "template": ""});
      if (result.success ?? false) {
        showToast(result.msg ?? "");
        if (count < 60 && count > 0) {
          showToast("请等计时结束再试");
        } else {
          timer = Timer.periodic(const Duration(seconds: 1), (timer) {
            count--;
            if (count <= 0) {
              timer.cancel();
            }
            setState(() {
              if (count > 0) {
                btnText = "请$count秒后再试";
              } else {
                btnText = "重新发送";
                count = 60;
              }
            });
          });
        }
      } else {
        showToast(result.msg ?? "");
      }
    } else {
      showToast("请输入正确的手机号");
    }
  }

  smsLogin() async {
    if (smsController.text.isEmpty) {
      showToast("请输入验证码");
    } else {
      var result = await BDHResponsitory.login({
        "telephone": phoneController.text,
        "code": smsController.text,
        "loginType": 1,
        "systemCode": "bdh-app"
      });
      if (result.success ?? false) {
        result.data!.ifRegistered = "1";
        StorageManager.storage!.setItem(kUser, jsonEncode(result.toJson()));

        showToast("登录成功");
        if (mounted) {
          Navigator.of(context).pop();
          Navigator.of(context).pushReplacementNamed(RouteName.tabMain);
        }
      } else {
        showToast(result.msg ?? "登录失败");
      }
    }
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }
}
