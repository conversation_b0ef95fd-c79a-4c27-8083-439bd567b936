import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/model/bdh_login_multi_account_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

class LoginChooseAccountBdhDigital extends StatefulWidget {
  BdhLoginMultiAccountModel model;
  LoginChooseAccountBdhDigital({
    super.key,
    required this.model,
  });

  @override
  State<LoginChooseAccountBdhDigital> createState() =>
      _LoginChooseAccountBdhDigitalState();
}

class _LoginChooseAccountBdhDigitalState
    extends State<LoginChooseAccountBdhDigital> {
  Account? currentSeletedAccount;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: BoxDecoration(
              image: DecorationImage(
                  image: AssetImage(
                      ImageHelper.wrapAssets("loginBDHDigitalBG.png")),
                  fit: BoxFit.cover),
            ),
          ),
          Column(
            children: [
              Expanded(flex: 2, child: titleView()),
              Expanded(flex: 7, child: chooseAccountView()),
              Expanded(flex: 3, child: getBottomBtnView()),
            ],
          )
        ],
      ),
    );
  }

  //关闭按钮视图
  Widget titleView() {
    return SizedBox(
      width: double.infinity,
      child: Column(
        children: [
          SizedBox(height: MediaQuery.of(context).padding.top + 20.px),
          GestureDetector(
            onTap: () {
              Navigator.pop(context);
            },
            child: BDHCustomAppBarView(
              leftView: BDHBackActionBtn(
                imageName: 'backArrowImg.png',
                clicekdCallBack: () {
                  Navigator.pop(context);
                },
              ),
              centerView: BDHAppBarTitleView(titleName: '选择登录账号'),
            ),
          ),
        ],
      ),
    );
  }

  dealWithClickItem(Account model) {
    for (int i = 0; i < widget.model.accounts.length; i++) {
      Account item = widget.model.accounts[i];
      if (item.loginName == model.loginName) {
        item.isSelected = true;
        currentSeletedAccount = item;
      } else {
        item.isSelected = false;
      }
    }
    setState(() {});
  }

  //选账号视图
  Widget chooseAccountView() {
    return Container(
      width: 375.px,
      height: 100.px,
      padding: EdgeInsets.only(left: 40.px, right: 40.px, bottom: 20.px),
      child: ListView.separated(
        scrollDirection: Axis.vertical,
        itemCount: widget.model.accounts.length,
        itemBuilder: (BuildContext context, int index) {
          Account model = widget.model.accounts[index];
          return GestureDetector(
            onTap: () {
              dealWithClickItem(model);
            },
            child: chooseAccountItemView(model),
          );
        },
        separatorBuilder: (context, index) {
          return SizedBox(height: 20.px);
        },
      ),
    );
  }

  Widget chooseAccountItemView(Account model) {
    return Container(
      padding: EdgeInsets.only(left: 10.px, right: 10.px),
      height: 54.px,
      width: 295.px,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50.px),
        border: model.isSelected ?? true
            ? Border.all(
                color: const Color.fromRGBO(22, 183, 96, 0.8), width: 1.px)
            : Border.all(
                color: const Color.fromRGBO(226, 235, 231, 1), width: 1.px),
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          model.lastLogin == 1
              ? SizedBox(height: 24.px, width: 60.px)
              : Container(),
          Text(model.loginName,
              style: TextStyle(
                  fontSize: 16.px,
                  color: const Color.fromRGBO(0, 0, 0, 0.4),
                  fontWeight: model.isSelected ?? true
                      ? FontWeight.w500
                      : FontWeight.w400)),
          model.lastLogin == 1
              ? Container(
                  height: 24.px,
                  width: 60.px,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(50.px),
                    color: const Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                  child: Center(
                    child: Text(
                      '上次登录',
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: const Color.fromRGBO(0, 0, 0, 0.2)),
                    ),
                  ),
                )
              : Container(),
        ],
      ),
    );
  }

  //确定按钮视图
  Widget getBottomBtnView() {
    return SizedBox(
      width: double.infinity,
      height: 100.px,
      child: Column(
        children: [
          GestureDetector(
            onTap: () {
              if (currentSeletedAccount == null) {
                showToast("请选择登录账号");
                return;
              }
              Navigator.of(context).pop(currentSeletedAccount);
            },
            child: BDHUserLoginButton(
              width: 296.px,
              height: 52.px,
              title: '确定',
              borderRadius: 50.px,
              marginBottom: 16.px,
              marginTop: 10.px,
            ),
          )
        ],
      ),
    );
  }
}

class ChooseLoginAccountModel {
  late String accountName;
  late bool? isSelected;
  ChooseLoginAccountModel(this.accountName, this.isSelected);
}
