class BdhDigitalIdentityCardDataModel {
  bool? success;
  int? code;
  String? msg;
  IdentityData? data;

  BdhDigitalIdentityCardDataModel(
      {this.success, this.code, this.msg, this.data});

  factory BdhDigitalIdentityCardDataModel.fromJson(Map<String, dynamic> json) =>
      BdhDigitalIdentityCardDataModel(
        success: json['success'] as bool?,
        code: json['code'] as int?,
        msg: json['msg'] as String?,
        data: json['data'] == null
            ? null
            : IdentityData.fromJson(json['data'] as Map<String, dynamic>),
      );

  Map<String, dynamic> toJson() => {
        'success': success,
        'code': code,
        'msg': msg,
        'data': data?.toJson(),
      };
}

class IdentityData {
  String? imgUrl;
  String? realName;
  String? idCard;

  IdentityData({this.imgUrl, this.realName, this.idCard});

  factory IdentityData.fromJson(Map<String, dynamic> json) => IdentityData(
        imgUrl: json['imgUrl'] as String?,
        realName: json['realName'] as String?,
        idCard: json['idCard'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'imgUrl': imgUrl,
        'realName': realName,
        'idCard': idCard,
      };
}
