class BdhDigitalFaceVerifyResultModel {
  bool? success;
  int? code;
  String? msg;
  BdhDigitalFaceVerifyData? data;

  BdhDigitalFaceVerifyResultModel(
      {this.success, this.code, this.msg, this.data});

  BdhDigitalFaceVerifyResultModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = json['data'] != null
        ? BdhDigitalFaceVerifyData.fromJson(json['data'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    data['msg'] = msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class BdhDigitalFaceVerifyData {
  String? transactionNo;
  int? verifiedStatus;
  String? verifiedStatusDesc;
  String? data;

  BdhDigitalFaceVerifyData(
      {this.transactionNo, this.verifiedStatus, this.verifiedStatusDesc});
  BdhDigitalFaceVerifyData.fromJson(Map<String, dynamic> json) {
    transactionNo = json['transactionNo'];
    verifiedStatus = json['verifiedStatus'];
    verifiedStatusDesc = json['verifiedStatusDesc'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['transactionNo'] = transactionNo;
    data['verifiedStatus'] = verifiedStatus;
    data['verifiedStatusDesc'] = verifiedStatusDesc;
    // data['data'] = data;
    return data;
  }

// "verifiedStatusDesc" -> "账户未认证"
// "verifiedStatus" -> 1
// "transactionNo" -> "1443de0cf144494b8b41e7285a941f54"
}
