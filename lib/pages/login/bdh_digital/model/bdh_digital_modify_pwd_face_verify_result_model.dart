class BdhDigitalModifyPWDFaceVerifyResultModel {
  bool? success;
  int? code;
  String? msg;
  // PWDFaceVerfifyData? data;
  String? data;

  BdhDigitalModifyPWDFaceVerifyResultModel(
      {this.success, this.code, this.msg, this.data});

  BdhDigitalModifyPWDFaceVerifyResultModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data = json['data'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    data['msg'] = msg;
    return data;
  }
}

// class PWDFaceVerfifyData {
//   String? data;
//   PWDFaceVerfifyData({
//     this.data,
//   });
//   PWDFaceVerfifyData.fromJson(Map<String, dynamic> json) {
//     data = json['data'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = <String, dynamic>{};
//     data['data'] = data;
//     return data;
//   }
// }
