class BdhDigitalUserModel {
  bool? success;
  int? code;
  String? msg;
  BdhDigitalUserData? data;

  BdhDigitalUserModel({this.success, this.code, this.msg, this.data});

  BdhDigitalUserModel.fromJson(Map<String, dynamic> json) {
    success = json['success'];
    code = json['code'];
    msg = json['msg'];
    data =
        json['data'] != null ? BdhDigitalUserData.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['success'] = success;
    data['code'] = code;
    data['msg'] = msg;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class BdhDigitalUserData {
  String? url;
  String? faceUrl;
  int? staffId;
  String? ticket;
  BdhDigitalUserData({this.url, this.staffId});
  BdhDigitalUserData.fromJson(Map<String, dynamic> json) {
    url = json['url'];
    faceUrl = json['faceUrl'];
    staffId = json['staffId'];
    ticket = json['ticket'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['faceUrl'] = faceUrl;
    data['url'] = url;
    data['staffId'] = staffId;
    data['ticket'] = ticket;
    return data;
  }

  // "url" -> "https://kyc1.qcloud.com/api/web/login?orderNo=wz2025042910411280535203&sign=215A283E3A215017082F8A9AB80A28C183312A58&from=browse…"
  // "staffId" -> 39438
}
