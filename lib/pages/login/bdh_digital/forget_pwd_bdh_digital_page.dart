import 'dart:async';
import 'dart:convert';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/manager/storage_manager.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/bdh_digital_pirvacy_agreement.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/forget_pwd_step.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/register_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/login/bdh_digital/subview/bdh_digital_text_intput.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/reg_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_tool.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class ForgetPwdBdhDigitalPage extends StatefulWidget {
  const ForgetPwdBdhDigitalPage({super.key});

  @override
  State<ForgetPwdBdhDigitalPage> createState() =>
      _ForgetPwdBdhDigitalPageState();
}

class _ForgetPwdBdhDigitalPageState extends State<ForgetPwdBdhDigitalPage> {
  Map<String, dynamic> form = {'systemCode': kSystemCode};
  Map<String, dynamic> formPWD = {};
  GlobalKey<FormState> bdhDigitalForgetPWDKey = GlobalKey<FormState>();

  TextEditingController accountController = TextEditingController();
  TextEditingController telephoneController = TextEditingController();
  TextEditingController simCodeController = TextEditingController();

  TextEditingController pwdController = TextEditingController();
  TextEditingController pwdConfrimController = TextEditingController();

  int count = 60;
  String btnText = "获取验证码";
  Timer? timer; // 验证码倒计时
  Timer? checkFaceVerifyTimer;
  int currentStep = 0;
  bool isCheck = false;
  bool isShowPWD = true;
  bool isShowPWDConfirmed = true;
  // BdhDigitalUserModel? digitalUserModel;

  bool showAccountErr = false;
  String errAccountInfo = '账号不能为空';

  bool showTelephoneErr = false;
  String errTelephoneInfo = '手机号输入有误';

  bool showSIMCodeErr = false;
  String errSImCodeInfo = '验证码不能为空';

  bool showPWDErr = false;
  String errPWDInfo = '请输入密码';

  bool showConfrimPWDErr = false;
  String errConfirmPWDInfo = '请输入密码';

  @override
  void dispose() {
    super.dispose();
    checkFaceVerifyTimer?.cancel();
    timer?.cancel();
    accountController.dispose();
    telephoneController.dispose();
    simCodeController.dispose();
    pwdController.dispose();
    pwdConfrimController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        title: const Text('忘记密码'),
      ),
      body: GestureDetector(
        onTap: () {
          TDToast.dismissLoading();
          FocusScope.of(context).unfocus();
        },
        child: Container(
          height: ScreenTool().screenHeight,
          width: 375.px,
          child: Stack(
            children: [
              Container(
                color: HexColor("#F3F5F9"),
                child: Column(
                  children: [
                    ForgetPwdStep(
                      activeStep: currentStep,
                    ),
                    Form(
                        key: bdhDigitalForgetPWDKey,
                        child: getCurrentShowStepView()),
                  ],
                ),
              ),
              Positioned(
                width: 375.px,
                bottom: 40.px,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // currentStep == 1
                    //     ? BdhDigitalPirvacyAgreement(
                    //         checkBoxNormalImg:
                    //             'privacy_agreement_selected_digital.png',
                    //         agreementTextColor:
                    //             const Color.fromRGBO(0, 127, 255, 1),
                    //         isCheckCallBack: (res) {
                    //           setState(() {
                    //             isCheck = res;
                    //           });
                    //         })
                    //     : Container(),
                    bottomButtonView(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getCurrentShowStepView() {
    if (currentStep == 0) {
      return stepOneView();
    } else if (currentStep == 1) {
      return stepTwoView();
    } else if (currentStep == 2) {
      return stepThreeView();
    } else if (currentStep == 3) {
      return stepFourView();
    } else {
      return stepOneView();
    }
  }

//第1步view
  Widget stepOneView() {
    String tipInfo = """
1. 若您忘记证件号码，请联系北大荒信息公司实施人员进行密码重置。
2. 随机短信密码每日最多发送5次。
3. 若连续输入随机码错误3次,则锁定账号无法进行密码重置30分钟后解锁。
""";
    return Column(
      children: [
        stepOneInputCardView(),
        stepOneTipInfoView(tipInfo),
      ],
    );
  }

//第2步view
  Widget stepTwoView() {
    return Column(
      children: [
        SizedBox(height: 30.px),
        Image.asset(
            width: 120.px,
            height: 120.px,
            ImageHelper.wrapAssets('forgetPwdStepTwoIcon.png')),
        SizedBox(height: 20.px),
        stepTwoTipInfoView(),
      ],
    );
  }

//第3步view
  Widget stepThreeView() {
    String tipInfo = """
1. 密码要遵循密码规范
2. 两次密码要输入一致
""";
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        stepThreeInputCardView(),
        stepOneTipInfoView(tipInfo),
      ],
    );
  }

//第4步view
  Widget stepFourView() {
    String tipInfo = """
密码重置成功，返回登录页面
""";
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 120.px),
        Image.asset(
            width: 120.px,
            height: 120.px,
            ImageHelper.wrapAssets('forgetPwdStepFrouImg.png')),
        stepOneTipInfoView(tipInfo),
      ],
    );
  }

//step one
  Widget stepOneInputCardView() {
    return Container(
      margin:
          EdgeInsets.only(bottom: 10.px, left: 16.px, right: 16.px, top: 20.px),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        children: [
          //账      号
          BDHDigitalTextInput(
            showStar: false,
            titleWidth: 90.px,
            titleName: '请输入账号',
            tipName: '请输入账号',
            controller: accountController,
            showErrTip: showAccountErr,
            errTipInfo: errAccountInfo,
            onSaved: (v) {
              form["loginName"] = v;
              showErrTipInfo(RegistInputType.account, v);
            },
            onChange: (v) {
              form["loginName"] = v;
              showErrTipInfo(RegistInputType.account, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "账号不能为空";
            //   }
            //   return null;
            // },
          ),

          //手机号
          BDHDigitalTextInput(
            showStar: false,
            titleWidth: 90.px,
            titleName: '请输入手机号',
            tipName: '请输入手机号',
            controller: telephoneController,
            textInputType: TextInputType.number,
            showErrTip: showTelephoneErr,
            errTipInfo: errTelephoneInfo,
            onSaved: (v) {
              form["phone"] = v;
              showErrTipInfo(RegistInputType.telephone, v);
            },
            onChange: (v) {
              form["phone"] = v;
              showErrTipInfo(RegistInputType.telephone, v);
            },
            // validator: (value) {
            //   String errString = '';
            //   if (value == null || value.isEmpty) {
            //     errString = "手机号不能为空";
            //   } else if (!RegUtil.isPhoneNumber(value)) {
            //     errString = "手机号输入有误";
            //   }
            //   if (errString.isEmpty) {
            //     return null;
            //   } else {
            //     return errString;
            //   }
            // },
          ),

          //验证码
          BDHDigitalTextInput(
            showStar: false,
            showBottomLine: false,
            titleWidth: 90.px,
            titleName: '请填写验证码',
            tipName: '请输入验证码',
            controller: simCodeController,
            textInputType: TextInputType.number,
            rightSubView: getSimCodeView(),
            showErrTip: showSIMCodeErr,
            errTipInfo: errSImCodeInfo,
            onSaved: (v) {
              form["code"] = v;
              showErrTipInfo(RegistInputType.simCode, v);
            },
            onChange: (v) {
              form["code"] = v;
              showErrTipInfo(RegistInputType.simCode, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "验证码不能为空";
            //   }
            //   return null;
            // },
          ),
        ],
      ),
    );
  }

  Widget stepOneTipInfoView(String tipInfo) {
    return Container(
      // height: 200,
      margin: EdgeInsets.only(bottom: 10.px),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            tipInfo,
            style: TextStyle(
                height: 2,
                color: const Color.fromRGBO(0, 0, 0, 0.3),
                fontSize: 13.px,
                fontWeight: FontWeight.w500),
          )
        ],
      ),
    );
  }

  Widget stepTwoTipInfoView() {
    return Container(
      // height: 200,
      width: 343.px,
      margin: EdgeInsets.only(bottom: 10.px),
      // padding: const EdgeInsets.all(16.0),
      padding: EdgeInsets.only(
        top: 20.px,
        left: 40.px,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "请您知悉并同意应用提供者:",
            style: TextStyle(
                height: 2,
                color: const Color.fromRGBO(0, 0, 0, 1),
                fontSize: 14.px,
                fontWeight: FontWeight.w600),
          ),
          Text(
            """
· 调用相机及麦克风权限
· 收集、使用您本人身份相关的人脸图像
· 向合法数据持有者核验您的身份信息
""",
            style: TextStyle(
                height: 2,
                color: const Color.fromRGBO(0, 0, 0, 0.3),
                fontSize: 13.px,
                fontWeight: FontWeight.w500),
          )
        ],
      ),
    );
  }

//step three
  Widget stepThreeInputCardView() {
    return Container(
      // height: 200,
      margin:
          EdgeInsets.only(bottom: 10.px, left: 16.px, right: 16.px, top: 20.px),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        children: [
          //密 码
          BDHDigitalTextInput(
            showStar: false,
            titleWidth: 90.px,
            titleName: '新 密 码',
            tipName: '请输入密码',
            controller: pwdController,
            rightSubView: getPWDeyeView(),
            isShowPWD: isShowPWD,
            showErrTip: showPWDErr,
            errTipInfo: errPWDInfo,
            onSaved: (v) {
              formPWD["passwordEnc"] = v;
              showErrTipInfo(RegistInputType.pwd, v);
            },
            onChange: (v) {
              formPWD["passwordEnc"] = v;
              showErrTipInfo(RegistInputType.pwd, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "密码不能为空";
            //   }
            //   return null;
            // },
          ),

          //确认密码
          BDHDigitalTextInput(
            showStar: false,
            titleWidth: 90.px,
            titleName: '确认密码',
            tipName: '请再次输入密码',
            controller: pwdConfrimController,
            rightSubView: getPWDConfirmdeyeView(),
            isShowPWD: isShowPWDConfirmed,
            showErrTip: showConfrimPWDErr,
            errTipInfo: errConfirmPWDInfo,
            onSaved: (v) {
              formPWD["password"] = v;
              showErrTipInfo(RegistInputType.pwdConfirm, v);
            },
            onChange: (v) {
              formPWD["password"] = v;
              showErrTipInfo(RegistInputType.pwdConfirm, v);
            },
            // validator: (value) {
            //   if (value == null || value.isEmpty) {
            //     return "确认密码不能为空";
            //   }
            //   return null;
            // },
          ),
        ],
      ),
    );
  }

  //获取验证码view
  Widget getSimCodeView() {
    return GestureDetector(
      onTap: () {
        if (telephoneController.text.isEmpty) {
          showToast('手机号码不能为空');
          return;
        }

        if (!RegUtil.isPhoneNumber(telephoneController.text)) {
          showToast('手机号码有误, 请重新输入');
          return;
        }

        if (count < 60 && count > 0) {
          showToast("请等计时结束再试");
          return;
        }

        TDToast.showLoadingWithoutText(context: context, preventTap: true);
        BdhDigitalService.getForgetPWDSmsCodeBDHDigital(
            {"phone": telephoneController.text}).then((result) {
          TDToast.dismissLoading();

          if (result.success ?? false) {
            showToast(result.msg ?? "");
            if (count < 60 && count > 0) {
              showToast("请等计时结束再试");
            } else {
              timer = Timer.periodic(const Duration(seconds: 1), (timer) {
                count--;
                if (count <= 0) {
                  timer.cancel();
                }
                setState(() {
                  if (count > 0) {
                    btnText = "请$count秒后再试";
                  } else {
                    btnText = "重新发送";
                    count = 60;
                  }
                });
              });
            }
          } else {
            showToast(result.msg ?? "");
          }
        });
      },
      child: Container(
        padding: EdgeInsets.only(left: 2.px, right: 2.px),
        height: 28.px,
        // width: 72.px,
        decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border.all(width: 1, color: const Color.fromRGBO(0, 127, 255, 1)),
          borderRadius: BorderRadius.circular(
            4.px,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              btnText,
              style: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(0, 127, 255, 1)),
            )
          ],
        ),
      ),
    );
  }

//openPWDImg.png
  Widget getPWDeyeView() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isShowPWD = !isShowPWD;
        });
      },
      child: Image.asset(
        width: 24.px,
        height: 24.px,
        ImageHelper.wrapAssets(
            isShowPWD ? "openPWDImg.png" : "colsePWDImg.png"),
      ),
    );
  }

  Widget getPWDConfirmdeyeView() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isShowPWDConfirmed = !isShowPWDConfirmed;
        });
      },
      child: Image.asset(
        width: 24.px,
        height: 24.px,
        ImageHelper.wrapAssets(
            isShowPWDConfirmed ? "openPWDImg.png" : "colsePWDImg.png"),
      ),
    );
  }

  // checkSubmitBtnStatus() {
  //   if (openSubmitBtn == false) {
  //     return;
  //   }
  // }

//轮询查看人脸结果
  checkFaceAuthResult(String ticket) {
    int counter = 0;
    int maxCount = 30;
    TDToast.showLoading(
        context: context, text: '人脸认证结果查询中...', preventTap: false);
    // TDToast.showLoadingWithoutText(context: context, preventTap: true);
    checkFaceVerifyTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      BdhDigitalService.getModifyPWDFaceResultBdhDigital(
          {'resetPasswordValidateTicket': ticket}).then((result) {
        counter++;
        Log.i('注册step1$result');
        if (counter == maxCount) {
          TDToast.dismissLoading();
          showToast('认证超时');
          timer.cancel();
          NativeUtil.closeFdd();
          return;
        }
        if (result.data == '2') {
          TDToast.dismissLoading();
          timer.cancel();
          NativeUtil.closeFdd();
          showToast('认证成功');
          setState(() {
            currentStep = 2;
          });
          return;
        } else if (result.data != '2' && result.data != '') {
          TDToast.dismissLoading();
          showToast('认证失败');
          NativeUtil.closeFdd();
          timer.cancel();
        } else {
          // timer.cancel();
          // NativeUtil.closeFdd();
          // showToast(result.data?.verifiedStatusDesc ?? '认证失败');
        }
      });
    });
  }

//登录按钮
  Widget bottomButtonView() {
    if (currentStep == 0) {
      return GestureDetector(
        onTap: () {
          bdhDigitalForgetPWDKey.currentState!.save();
          if (bdhDigitalForgetPWDKey.currentState!.validate()) {
            bdhDigitalForgetPWDKey.currentState!.save();
          }

          if (telephoneController.text.isEmpty ||
              accountController.text.isEmpty ||
              simCodeController.text.isEmpty) {
            return;
          }
          setState(() {
            currentStep = 1;
          });
          Log.i("忘记密码step1, 点击事件, form = $form");
        },
        child: BDHUserLoginButton(
          width: 343.px,
          height: 48.px,
          title: '下一步',
          borderRadius: 8.px,
          // marginBottom: 16.px,
          marginTop: 20.px,
          bgLinearGradientColors: const [
            Color.fromRGBO(0, 127, 255, 1),
            Color.fromRGBO(61, 156, 255, 1),
          ],
        ),
      );
    } else if (currentStep == 1) {
      return GestureDetector(
        onTap: () {
          Log.i("忘记密码step 2 点击");
          TDToast.showLoadingWithoutText(context: context, preventTap: true);
          BdhDigitalService.checkUpdatePasswordBDHDigital(form).then((result) {
            TDToast.dismissLoading();
            Log.i('修改密码step2 $result');
            formPWD['resetPasswordValidateTicket'] = result.data?.ticket ?? '';
            if (result.success ?? false) {
              NativeUtil.openFdd(result.data?.url);
              Future.delayed(const Duration(seconds: 3), () {
                checkFaceAuthResult(result.data?.ticket ?? '');
              });
            } else {
              showToast(result.msg ?? "修改密码失败");
            }
          });
        },
        child: BDHUserLoginButton(
          width: 343.px,
          height: 48.px,
          title: '同意授权并继续',
          borderRadius: 8.px,
          // marginBottom: 16.px,
          marginTop: 20.px,
          bgLinearGradientColors: const [
            Color.fromRGBO(0, 127, 255, 1),
            Color.fromRGBO(61, 156, 255, 1),
          ],
        ),
      );
    } else if (currentStep == 2) {
      return GestureDetector(
        onTap: () {
          Log.i("忘记密码step three 点击");
          // setState(() {
          //   currentStep = 3;
          // });
          bdhDigitalForgetPWDKey.currentState!.save();
          if (bdhDigitalForgetPWDKey.currentState!.validate()) {
            bdhDigitalForgetPWDKey.currentState!.save();
          }

          if (pwdController.text.isEmpty) {
            showToast('请输入密码');
            return;
          }

          if (pwdConfrimController.text.isEmpty) {
            showToast('请输入确认密码');
            return;
          }

          if (pwdController.text != pwdConfrimController.text) {
            showToast('两次密码不一致');
            return;
          }

          var password = pwdConfrimController.text;
          var bytes = utf8.encode(password.toUpperCase());
          //第一层MD5
          var firstMD5 = '${md5.convert(bytes)}'.toUpperCase();
          //第二层MD5
          var md5Password =
              '${md5.convert(utf8.encode(firstMD5))}'.toLowerCase();
          formPWD['password'] = md5Password;
          Log.i('更新密码 formPWD = $formPWD');
          TDToast.showLoadingWithoutText(context: context, preventTap: true);
          BdhDigitalService.updatePasswordBdhDigital(formPWD).then((result) {
            TDToast.dismissLoading();
            Log.i('更新密码step2: $result');
            if (result.success ?? false) {
              showToast('修改密码成功');
              setState(() {
                currentStep = 3;
              });
            } else {
              showToast(result.msg ?? "修改密码失败");
            }
          });
        },
        child: BDHUserLoginButton(
          width: 343.px,
          height: 48.px,
          title: '下一步',
          borderRadius: 8.px,
          marginBottom: 16.px,
          marginTop: 20.px,
          bgLinearGradientColors: const [
            Color.fromRGBO(0, 127, 255, 1),
            Color.fromRGBO(61, 156, 255, 1),
          ],
        ),
      );
    } else if (currentStep == 3) {
      return GestureDetector(
        onTap: () {
          Log.i("忘记密码step 4 点击事件, 完成");
          //修改密码成功 > 退出登录
          TDToast.showLoadingWithoutText(context: context, preventTap: true);
          BDHResponsitory.logout({}).then((res) {
            TDToast.dismissLoading();
            setState(() {
              currentStep = 0;
            });
            StorageManager.storage?.clear();
            if (mounted) {
              Navigator.of(context).pop();
              Navigator.of(context)
                  .popAndPushNamed(RouteName.loginBdhDigitalPage)
                  .then((res) {
                GlobalServiceView.hidenView();
              });
            }
          });
        },
        child: BDHUserLoginButton(
          width: 343.px,
          height: 48.px,
          title: '完成',
          borderRadius: 8.px,
          marginBottom: 16.px,
          marginTop: 20.px,
          bgLinearGradientColors: const [
            Color.fromRGBO(0, 127, 255, 1),
            Color.fromRGBO(61, 156, 255, 1),
          ],
        ),
      );
    } else {
      return GestureDetector(
        child: BDHUserLoginButton(
          width: 343.px,
          height: 48.px,
          title: '完成',
          borderRadius: 8.px,
          marginBottom: 16.px,
          marginTop: 20.px,
          bgLinearGradientColors: const [
            Color.fromRGBO(0, 127, 255, 1),
            Color.fromRGBO(61, 156, 255, 1),
          ],
        ),
      );
    }
  }

//错误提示信息
  showErrTipInfo(RegistInputType type, dynamic v) {
    //电话号码
    if (type == RegistInputType.telephone) {
      if (v == null || v.isEmpty) {
        setState(() {
          showTelephoneErr = true;
          errTelephoneInfo = '手机号不能为空';
        });
      } else if (!RegUtil.isPhoneNumber(v)) {
        setState(() {
          showTelephoneErr = true;
          errTelephoneInfo = '手机号输入有误';
        });
      } else {
        setState(() {
          showTelephoneErr = false;
          errTelephoneInfo = '';
        });
      }
      return;
    }

    //账号
    if (type == RegistInputType.account) {
      if (v == null || v.isEmpty) {
        setState(() {
          showAccountErr = true;
          errAccountInfo = '账号不能为空';
        });
      } else {
        setState(() {
          showAccountErr = false;
          errAccountInfo = '';
        });
      }
      return;
    }

    //验证码
    if (type == RegistInputType.simCode) {
      if (v == null || v.isEmpty) {
        setState(() {
          showSIMCodeErr = true;
          errSImCodeInfo = '验证码不能为空';
        });
      } else {
        setState(() {
          showSIMCodeErr = false;
          errSImCodeInfo = '';
        });
      }
      return;
    }

    //密码
    if (type == RegistInputType.pwd) {
      if (v == null || v.isEmpty) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '密码不能为空';
        });
      } else if (!RegUtil.isValidPWD(v)) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '需含特殊字符[!@#\$%^&*()_?<>{}]';
        });
      } else if (!RegUtil.isValidPWDLength(v)) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '密码不符合规范:长度需为8-18位';
        });
      } else if (!RegUtil.isValidPWDHaveAaZz(v)) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '密码不符合规范:需含有字母[a-zA-Z]';
        });
      } else if (!RegUtil.isValidPWDHaveNumber(v)) {
        setState(() {
          showPWDErr = true;
          errPWDInfo = '密码不符合规范:需含有数字[0-9]]';
        });
      } else {
        setState(() {
          showPWDErr = false;
          errPWDInfo = '';
        });
      }
      return;
    }

    //确认密码
    if (type == RegistInputType.pwdConfirm) {
      if (v == null || v.isEmpty) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '密码不能为空';
        });
      } else if (!RegUtil.isValidPWD(v)) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '需含特殊字符[!@#\$%^&*()_?<>{}]';
        });
      } else if (!RegUtil.isValidPWDLength(v)) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '密码不符合规范:长度需为8-18位';
        });
      } else if (!RegUtil.isValidPWDHaveAaZz(v)) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '密码不符合规范:需含有字母[a-zA-Z]';
        });
      } else if (!RegUtil.isValidPWDHaveNumber(v)) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '密码不符合规范:需含有数字[0-9]]';
        });
      } else if (pwdConfrimController.text != pwdController.text) {
        setState(() {
          showConfrimPWDErr = true;
          errConfirmPWDInfo = '两次密码不一致';
        });
      } else {
        setState(() {
          showConfrimPWDErr = false;
          errConfirmPWDInfo = '';
        });
      }
      return;
    }
  }
}
