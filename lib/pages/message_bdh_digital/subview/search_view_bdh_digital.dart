import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:oktoast/oktoast.dart';

// ignore: must_be_immutable
class SearchViewBdhDigital extends StatefulWidget {
  TextEditingController searchController;
  FocusScopeNode focusScopeNode;
  Function(String content) clickedSeachBtn;
  Function() clickedClearBtn;
  SearchViewBdhDigital({
    super.key,
    required this.searchController,
    required this.focusScopeNode,
    required this.clickedSeachBtn,
    required this.clickedClearBtn,
  });

  @override
  State<SearchViewBdhDigital> createState() => _SearchViewBdhDigitalState();
}

class _SearchViewBdhDigitalState extends State<SearchViewBdhDigital> {
  bool showDeletIcon = false;

  @override
  void initState() {
    super.initState();
    widget.searchController.addListener(() {
      setState(() {
        // showSearchResult = searchController.text.isEmpty ? false : true;
        showDeletIcon = widget.searchController.text.isNotEmpty ? true : false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 12.px, right: 12.px),
      height: 32.px,
      // width: 300.px,
      decoration: BoxDecoration(
        // border: Border.all(width: 1),
        borderRadius: BorderRadius.circular(21),
        color: BDHColor.black03,
      ),
      child: Row(
        children: [
          Image.asset(
            width: 24.px,
            height: 24.px,
            ImageHelper.wrapAssets('search_grey.png'),
          ),
          SizedBox(width: 5.px),
          Expanded(
              child: SearchCustomTextField(
            searchController: widget.searchController,
            clickedKeyBoradSearch: (e) {
              widget.clickedSeachBtn(e);
            },
          )),
          // Expanded(
          //   child: CupertinoTextField.borderless(
          //     style: TextStyle(
          //       fontSize: 13.px,
          //       color: HexColor('#000000'),
          //       fontWeight: FontWeight.w400,
          //     ),
          //     textInputAction: TextInputAction.search,
          //     padding: EdgeInsets.zero,
          //     controller: widget.searchController,
          //     placeholder: "请输入搜索内容",
          //     placeholderStyle: TextStyle(
          //         fontSize: 12.px,
          //         fontWeight: FontWeight.w500,
          //         color: HexColor('#A8B8B1')),
          //     focusNode: widget.focusScopeNode,
          //     // autofocus: true,
          //     // onTapOutside: (e) => {focusNode.unfocus()},
          //     // onChanged: (e) => {},

          //     onEditingComplete: () {
          //       // Logger().i('textfiled 点击--onEditingComplete----');
          //       // setState(() {
          //       //   searchKeyWord = searchController.text;
          //       // });
          //       // getMessageListDate();
          //       widget.focusScopeNode.unfocus();
          //     },
          //   ),
          // ),
          SizedBox(width: 5.px),
          showDeletIcon
              ? GestureDetector(
                  onTap: () {
                    widget.searchController.text = '';
                    widget.clickedClearBtn();
                  },
                  child: Image.asset(
                      width: 16.px,
                      height: 16.px,
                      ImageHelper.wrapAssets('delteIcons.png')),
                )
              : Container(),
          GestureDetector(
            onTap: () {
              if (widget.searchController.text.isEmpty) {
                showToast('请输入搜索内容');
                return;
              }
              widget.clickedSeachBtn(widget.searchController.text);
            },
            child: Container(
              padding: EdgeInsets.only(left: 15.px, right: 5.px),
              child: Text(
                '搜索',
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w600,
                  color: const Color.fromRGBO(0, 127, 255, 1),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class SearchCustomTextField extends StatefulWidget {
  late TextEditingController searchController;
  Function(String content) clickedKeyBoradSearch;

  SearchCustomTextField(
      {super.key,
      required this.searchController,
      required this.clickedKeyBoradSearch});

  @override
  // ignore: library_private_types_in_public_api
  _MyTextFieldState createState() => _MyTextFieldState();
}

class _MyTextFieldState extends State<SearchCustomTextField> {
  final FocusNode _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 确保焦点和键盘同时处理
        if (!_focusNode.hasFocus) {
          _focusNode.requestFocus();
        }
      },
      child: CupertinoTextField.borderless(
        style: TextStyle(
          fontSize: 13.px,
          color: HexColor('#000000'),
          fontWeight: FontWeight.w400,
        ),
        textInputAction: TextInputAction.search,
        padding: EdgeInsets.zero,
        controller: widget.searchController,
        placeholder: "请输入搜索内容",
        placeholderStyle: TextStyle(
            fontSize: 12.px,
            fontWeight: FontWeight.w500,
            color: HexColor('#A8B8B1')),
        focusNode: _focusNode,
        // autofocus: true,
        // onTapOutside: (e) => {focusNode.unfocus()},
        // onChanged: (e) => {},
        onEditingComplete: () {
          if (widget.searchController.text.isEmpty) {
            showToast('请输入搜索内容');
            return;
          }
          widget.clickedKeyBoradSearch(widget.searchController.text);
          _focusNode.unfocus();
        },
      ),
    );
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }
}



/*

// 搜索视图
  Widget searchView() {
    return Container(
      padding: EdgeInsets.only(left: 12.px, right: 12.px),
      height: 32.px,
      // width: 300.px,
      decoration: BoxDecoration(
        // border: Border.all(width: 1),
        borderRadius: BorderRadius.circular(21),
        color: BDHColor.black03,
      ),
      child: Row(
        children: [
          Image.asset(
            width: 24.px,
            height: 24.px,
            ImageHelper.wrapAssets('search_grey.png'),
          ),
          SizedBox(
            width: 5.px,
          ),
          Expanded(
            child: CupertinoTextField.borderless(
              style: TextStyle(
                fontSize: 13.px,
                color: HexColor('#000000'),
                fontWeight: FontWeight.w400,
              ),
              textInputAction: TextInputAction.search,
              padding: EdgeInsets.zero,
              controller: searchController,
              placeholder: "请输入搜索内容",
              placeholderStyle: TextStyle(
                  fontSize: 12.px,
                  fontWeight: FontWeight.w500,
                  color: HexColor('#A8B8B1')),
              focusNode: focusScopeNode,
              // autofocus: true,
              // onTapOutside: (e) => {focusNode.unfocus()},
              // onChanged: (e) => {},
              onEditingComplete: () {
                // Logger().i('textfiled 点击--onEditingComplete----');
                // setState(() {
                //   searchKeyWord = searchController.text;
                // });
                // getMessageListDate();
                focusScopeNode.unfocus();
              },
            ),
          ),
          SizedBox(
            width: 5.px,
          ),
          showDeletIcon
              ? GestureDetector(
                  onTap: () {
                    searchController.text = '';
                  },
                  child: Image.asset(
                      width: 16.px,
                      height: 16.px,
                      ImageHelper.wrapAssets('delteIcons.png')),
                )
              : Container(),
        ],
      ),
    );
  }

 */