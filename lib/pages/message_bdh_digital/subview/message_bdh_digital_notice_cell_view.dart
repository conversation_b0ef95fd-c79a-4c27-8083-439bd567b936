import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/notice_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../utils/cover_tool.dart';

// ignore: must_be_immutable
class MessageBdhDigitalNoticeCellView extends StatefulWidget {
  int msgTabIndex;
  NoticeRecordBdhDigitalModel modelItem;
  Function(NoticeRecordBdhDigitalModel modelItem)? clickeNoticeCellItem;
  MessageBdhDigitalNoticeCellView({
    super.key,
    required this.msgTabIndex,
    required this.modelItem,
    this.clickeNoticeCellItem,
  });

  @override
  State<MessageBdhDigitalNoticeCellView> createState() =>
      _MessageBdhDigitalNoticeCellViewState();
}

class _MessageBdhDigitalNoticeCellViewState
    extends State<MessageBdhDigitalNoticeCellView> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.clickeNoticeCellItem != null) {
          widget.clickeNoticeCellItem!(widget.modelItem);
        }
      },
      child: Container(
        height: 80.px,
        width: 375.px,
        decoration: const BoxDecoration(
          color: Colors.transparent,
        ),
        child: Center(
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 8.px, horizontal: 10.px),
            width: 345.px,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4.px),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: SizedBox(
                    height: 40.px,
                    width: 40.px,
                    child: Image.asset(
                      width: 40.px,
                      height: 40.px,
                      ImageHelper.wrapBdhDigitalImagesAssets(
                          widget.msgTabIndex == 2
                              ? 'notice.png'
                              : 'message.png'),
                    ),
                  ),
                ),
                Expanded(
                  flex: 12,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          SizedBox(
                            width: 160.px,
                            child: Text(
                              widget.modelItem.noticeName ?? '',
                              style: TextStyle(
                                  fontSize: 16.px,
                                  overflow: TextOverflow.ellipsis,
                                  color: HexColor('#151830'),
                                  fontWeight: FontWeight.w500),
                              maxLines: 1,
                            ),
                          ),
                          Text(
                              FormatTool.timeFormat(
                                  timestamp: widget.modelItem.sendTime ?? 0,
                                  formate: 'yyyy/MM/dd HH:mm'),
                              // widget.modelItem.createDate ?? '',
                              style: TextStyle(
                                  overflow: TextOverflow.ellipsis,
                                  fontSize: 12.px,
                                  color: HexColor('#62636e'),
                                  fontWeight: FontWeight.w400)),
                        ],
                      ),
                      SizedBox(height: 5.px),
                      Text(
                        widget.modelItem.noticeContentEnd ?? '',
                        textAlign: TextAlign.left,
                        style: TextStyle(
                            fontSize: 12.px,
                            overflow: TextOverflow.ellipsis,
                            color: HexColor('#62636e'),
                            fontWeight: FontWeight.w400),
                        maxLines: 1,
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
