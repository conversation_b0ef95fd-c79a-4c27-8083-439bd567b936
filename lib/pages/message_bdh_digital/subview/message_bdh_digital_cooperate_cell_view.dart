import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/all_message_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class MessageBdhDigitalCooperateCellView extends StatefulWidget {
  int msgTabIndex;
  EMessage modelItem;
  Function(EMessage modelItem)? clickeCellItem;
  MessageBdhDigitalCooperateCellView({
    super.key,
    required this.msgTabIndex,
    required this.modelItem,
    this.clickeCellItem,
  });

  @override
  State<MessageBdhDigitalCooperateCellView> createState() =>
      _MessageBdhDigitalCooperateCellViewState();
}

class _MessageBdhDigitalCooperateCellViewState
    extends State<MessageBdhDigitalCooperateCellView> {
  String iconPath = ImageHelper.wrapBdhDigitalImagesAssets('oa.png');
  @override
  void initState() {
    super.initState();
    // switch (widget.msgTabIndex) {
    //   case 0:
    //     iconPath = ImageHelper.wrapBdhDigitalImagesAssets('oa.png');
    //     break;
    //   case 1:
    //     iconPath = ImageHelper.wrapBdhDigitalImagesAssets('news.png');
    //     break;
    //   case 2:
    //     iconPath = ImageHelper.wrapBdhDigitalImagesAssets('notice.png');
    //     break;
    //   case 3:
    //     iconPath = ImageHelper.wrapBdhDigitalImagesAssets('message.png');
    //     break;
    //   default:
    //     iconPath = ImageHelper.wrapBdhDigitalImagesAssets('oa.png');
    // }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (widget.clickeCellItem != null) {
          widget.clickeCellItem!(widget.modelItem);
        }
      },
      child: Container(
        height: 80.px,
        width: 375.px,
        decoration: const BoxDecoration(
          color: Colors.transparent,
        ),
        child: Center(
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 8.px, horizontal: 10.px),
            width: 345.px,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4.px),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: SizedBox(
                    height: 40.px,
                    width: 40.px,
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Image.asset(
                          width: 40.px,
                          height: 40.px,
                          // iconPath,
                          ImageHelper.wrapBdhDigitalImagesAssets(
                              widget.msgTabIndex == 0 ? 'oa.png' : 'news.png'),
                          // ImageHelper.wrapBdhDigitalImagesAssets('oa.png'),
                        ),
                        widget.msgTabIndex == 0
                            ? widget.modelItem.isRead ?? false
                                ? Container()
                                : Positioned(
                                    top: -5,
                                    right: 0,
                                    child: Container(
                                      height: 10.px,
                                      width: 10.px,
                                      decoration: BoxDecoration(
                                        color: Colors.red,
                                        borderRadius:
                                            BorderRadius.circular(15.px),
                                      ),
                                    ))
                            : Container()
                      ],
                    ),
                  ),
                ),
                Expanded(
                  flex: 12,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: 140.px,
                            child: Text(
                              widget.modelItem.senderName ?? '',
                              style: TextStyle(
                                  fontSize: 16.px,
                                  overflow: TextOverflow.ellipsis,
                                  color: HexColor('#151830'),
                                  fontWeight: FontWeight.w500),
                              maxLines: 1,
                            ),
                          ),
                          Text(widget.modelItem.createDate ?? '',
                              style: TextStyle(
                                  overflow: TextOverflow.ellipsis,
                                  fontSize: 12.px,
                                  color: HexColor('#62636e'),
                                  fontWeight: FontWeight.w400)),
                        ],
                      ),
                      SizedBox(height: 5.px),
                      Text(
                        widget.modelItem.messageContent ?? '',
                        textAlign: TextAlign.left,
                        style: TextStyle(
                            fontSize: 12.px,
                            overflow: TextOverflow.ellipsis,
                            color: HexColor('#62636e'),
                            fontWeight: FontWeight.w400),
                        maxLines: 1,
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
