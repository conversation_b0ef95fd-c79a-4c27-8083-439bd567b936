class SystemDitailBdhDigitalModel {
  bool? success;
  int? code;
  String? msg;
  Data? data;

  SystemDitailBdhDigitalModel({
    this.success,
    this.code,
    this.msg,
    this.data,
  });

  factory SystemDitailBdhDigitalModel.fromJson(Map<String, dynamic> json) =>
      SystemDitailBdhDigitalModel(
        success: json["success"],
        code: json["code"],
        msg: json["msg"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "code": code,
        "msg": msg,
        "data": data?.toJson(),
      };
}

class Data {
  int? id;
  String? title;
  String? content;
  dynamic appPath;
  dynamic appParam;
  dynamic page;
  int? sendTime;
  String? isRead;

  Data({
    this.id,
    this.title,
    this.content,
    this.appPath,
    this.appParam,
    this.page,
    this.sendTime,
    this.isRead,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        id: json["id"],
        title: json["title"],
        content: json["content"],
        appPath: json["appPath"],
        appParam: json["appParam"],
        page: json["page"],
        sendTime: json["sendTime"],
        isRead: json["isRead"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "content": content,
        "appPath": appPath,
        "appParam": appParam,
        "page": page,
        "sendTime": sendTime,
        "isRead": isRead,
      };
}
