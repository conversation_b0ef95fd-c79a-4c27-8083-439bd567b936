// To parse this JSON data, do
//
//     final noticeBdhDigitalModel = noticeBdhDigitalModelFromJson(jsonString);

// NoticeBdhDigitalModel noticeBdhDigitalModelFromJson(String str) => NoticeBdhDigitalModel.fromJson(json.decode(str));

// String noticeBdhDigitalModelToJson(NoticeBdhDigitalModel data) => json.encode(data.toJson());

import 'package:bdh_smart_agric_app/utils/cover_tool.dart';

class NoticeBdhDigitalModel {
  bool? success;
  int? code;
  String? msg;
  Data? data;

  NoticeBdhDigitalModel({
    this.success,
    this.code,
    this.msg,
    this.data,
  });

  factory NoticeBdhDigitalModel.fromJson(Map<String, dynamic> json) =>
      NoticeBdhDigitalModel(
        success: json["success"],
        code: json["code"],
        msg: json["msg"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "code": code,
        "msg": msg,
        "data": data?.toJson(),
      };
}

class Data {
  List<NoticeRecordBdhDigitalModel>? records;
  int? total;
  int? size;
  int? current;
  // Orders? orders;
  bool? optimizeCountSql;
  bool? hitCount;
  dynamic countId;
  dynamic maxLimit;
  bool? searchCount;
  int? pages;

  Data({
    this.records,
    this.total,
    this.size,
    this.current,
    // this.orders,
    this.optimizeCountSql,
    this.hitCount,
    this.countId,
    this.maxLimit,
    this.searchCount,
    this.pages,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        records: json["records"] == null
            ? []
            : List<NoticeRecordBdhDigitalModel>.from(json["records"]!
                .map((x) => NoticeRecordBdhDigitalModel.fromJson(x))),
        total: json["total"],
        size: json["size"],
        current: json["current"],
        // orders: json["orders"] == null ? null : Orders.fromJson(json["orders"]),
        optimizeCountSql: json["optimizeCountSql"],
        hitCount: json["hitCount"],
        countId: json["countId"],
        maxLimit: json["maxLimit"],
        searchCount: json["searchCount"],
        pages: json["pages"],
      );

  Map<String, dynamic> toJson() => {
        "records": records == null
            ? []
            : List<dynamic>.from(records!.map((x) => x.toJson())),
        "total": total,
        "size": size,
        "current": current,
        // "orders": orders?.toJson(),
        "optimizeCountSql": optimizeCountSql,
        "hitCount": hitCount,
        "countId": countId,
        "maxLimit": maxLimit,
        "searchCount": searchCount,
        "pages": pages,
      };
}

class Orders {
  Orders();

  factory Orders.fromJson(Map<String, dynamic> json) => Orders();

  Map<String, dynamic> toJson() => {};
}

class NoticeRecordBdhDigitalModel {
  int? noticeId;
  int? id;
  String? orgCode;
  String? orgName;
  String? noticeType;
  int? sendTime;
  String? noticeName;
  String? noticeContent;
  String? noticeContentEnd;
  String? isRead;
  String? title;
  String? content;

  NoticeRecordBdhDigitalModel({
    this.noticeId,
    this.id,
    this.orgCode,
    this.orgName,
    this.noticeType,
    this.sendTime,
    this.noticeName,
    this.noticeContent,
    this.noticeContentEnd,
    this.isRead,
    this.title,
    this.content,
  });

  factory NoticeRecordBdhDigitalModel.fromJson(Map<String, dynamic> json) {
    String noticeContentString = '';
    if (json.containsKey('noticeContent')) {
      String tempStr = json["noticeContent"];
      // noticeContentString = tempStr.replaceAll(RegExp(r'/<[^>]+>/g'), '');
      noticeContentString = tempStr.replaceAll(RegExp(r'<p>|</p>'), '');
      RegExp regExp = RegExp(r'>([^<]+)<'); // 匹配 `>` 和 `<` 之间的内容
      Match? match = regExp.firstMatch(tempStr);

      if (match != null) {
        noticeContentString = match.group(1)!.trim(); // group(1) 获取第一个捕获组
        // print(noticeContentString);
      } else {
        // print("未匹配到内容");
      }
    }

    return NoticeRecordBdhDigitalModel(
      noticeId: json["noticeId"],
      id: json["id"],
      orgCode: json["orgCode"],
      orgName: json["orgName"],
      noticeType: json["noticeType"],
      sendTime: json["sendTime"],
      noticeName: json["noticeName"],
      noticeContentEnd: noticeContentString,
      noticeContent: json["noticeContent"],
      isRead: json["isRead"],
      title: json["title"],
      content: json["content"],
    );
  }

  // factory NoticeRecordBdhDigitalModel.fromJson(Map<String, dynamic> json) =>
  //     NoticeRecordBdhDigitalModel(
  //       noticeId: json["noticeId"],
  //       orgCode: json["orgCode"],
  //       orgName: json["orgName"],
  //       noticeType: json["noticeType"],
  //       sendTime: json["sendTime"],
  //       noticeName: json["noticeName"],
  //       noticeContent: json["noticeContent"],
  //       isRead: json["isRead"],
  //     );

  Map<String, dynamic> toJson() => {
        "noticeId": noticeId,
        "id": id,
        "orgCode": orgCode,
        "orgName": orgName,
        "noticeType": noticeType,
        "sendTime": sendTime,
        "noticeName": noticeName,
        "noticeContent": noticeContent,
        "isRead": isRead,
        "title": title,
        "content": content,
      };
}
