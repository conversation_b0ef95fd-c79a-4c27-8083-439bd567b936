import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/all_message_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/notice_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_refresh_list_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';

//业务通知
class NoticeBdhDigitalViewModel extends ViewStateRefreshListModel {
  String isRead = '0'; //0-未读  1-已读  null-全部
  AllMessageBdhDigitalModel? currentModel;

  @override
  Future<List<NoticeRecordBdhDigitalModel>> loadData({
    int? pageNum,
  }) async {
    NoticeBdhDigitalModel model =
        await BdhDigitalService.getNoticeListBdhDigital({
      "page": pageNum,
      "rows": 10,
      "isRead": isRead,
      "systemCode": kSystemCode,
    });

    return model.data?.records ?? [];
  }

  setIsRead(String isReadStr) {
    isRead = isReadStr;
    notifyListeners();
  }
}
