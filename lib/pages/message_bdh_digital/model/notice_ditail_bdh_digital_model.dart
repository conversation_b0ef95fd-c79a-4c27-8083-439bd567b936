// To parse this JSON data, do
//
//     final noticeDitailBdhDigitalModel = noticeDitailBdhDigitalModelFromJson(jsonString);

import 'dart:convert';

// NoticeDitailBdhDigitalModel noticeDitailBdhDigitalModelFromJson(String str) => NoticeDitailBdhDigitalModel.fromJson(json.decode(str));

// String noticeDitailBdhDigitalModelToJson(NoticeDitailBdhDigitalModel data) => json.encode(data.toJson());

class NoticeDitailBdhDigitalModel {
  bool? success;
  int? code;
  String? msg;
  Data? data;

  NoticeDitailBdhDigitalModel({
    this.success,
    this.code,
    this.msg,
    this.data,
  });

  factory NoticeDitailBdhDigitalModel.fromJson(Map<String, dynamic> json) =>
      NoticeDitailBdhDigitalModel(
        success: json["success"],
        code: json["code"],
        msg: json["msg"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "code": code,
        "msg": msg,
        "data": data?.toJson(),
      };
}

class Data {
  int? noticeId;
  String? orgCode;
  String? orgName;
  String? recOrgCode;
  String? recOrgName;
  String? noticeType;
  String? sendType;
  String? sendTime;
  String? noticeName;
  String? noticeContent;
  String? noticeContentEnd;
  String? dataStatus;
  String? releaseMode;
  String? roleType;
  dynamic impBatchId;
  dynamic remark;
  int? createBy;
  int? createTime;
  dynamic updateBy;
  dynamic updateTime;
  dynamic statusCd;
  dynamic params;
  dynamic list;
  // AnnexList? annexList;
  dynamic statsTime;
  dynamic totalCount;
  dynamic noReadCount;
  dynamic readCount;
  dynamic recSystemCode;
  dynamic recSystemName;
  dynamic recRoleId;
  dynamic recRoleName;
  dynamic impBatchDesc;

  Data({
    this.noticeId,
    this.orgCode,
    this.orgName,
    this.recOrgCode,
    this.recOrgName,
    this.noticeType,
    this.sendType,
    this.sendTime,
    this.noticeName,
    this.noticeContent,
    this.noticeContentEnd,
    this.dataStatus,
    this.releaseMode,
    this.roleType,
    this.impBatchId,
    this.remark,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.statusCd,
    this.params,
    this.list,
    // this.annexList,
    this.statsTime,
    this.totalCount,
    this.noReadCount,
    this.readCount,
    this.recSystemCode,
    this.recSystemName,
    this.recRoleId,
    this.recRoleName,
    this.impBatchDesc,
  });

  factory Data.fromJson(Map<String, dynamic> json) {
    String noticeContentString = '';
    if (json.containsKey('noticeContent')) {
      String tempStr = json["noticeContent"];
      // noticeContentString = tempStr.replaceAll(RegExp(r'/<[^>]+>/g'), '');
      // noticeContentString = tempStr.replaceAll(RegExp(r'<p>|</p>'), '');

      RegExp regExp = RegExp(r'>([^<]+)<'); // 匹配 `>` 和 `<` 之间的内容
      Match? match = regExp.firstMatch(tempStr);

      if (match != null) {
        noticeContentString = match.group(1)!.trim(); // group(1) 获取第一个捕获组
        // print(noticeContentString);
      } else {
        // print("未匹配到内容");
      }
    }
    return Data(
      noticeId: json["noticeId"],
      orgCode: json["orgCode"],
      orgName: json["orgName"],
      recOrgCode: json["recOrgCode"],
      recOrgName: json["recOrgName"],
      noticeType: json["noticeType"],
      sendType: json["sendType"],
      sendTime: json["sendTime"],
      noticeName: json["noticeName"],
      noticeContent: json["noticeContent"],
      noticeContentEnd: noticeContentString,
      dataStatus: json["dataStatus"],
      releaseMode: json["releaseMode"],
      roleType: json["roleType"],
      impBatchId: json["impBatchId"],
      remark: json["remark"],
      createBy: json["createBy"],
      createTime: json["createTime"],
      updateBy: json["updateBy"],
      updateTime: json["updateTime"],
      statusCd: json["statusCd"],
      params: json["params"],
      list: json["list"],
      // annexList: json["annexList"] == null
      //     ? null
      //     : AnnexList.fromJson(json["annexList"]),
      statsTime: json["statsTime"],
      totalCount: json["totalCount"],
      noReadCount: json["noReadCount"],
      readCount: json["readCount"],
      recSystemCode: json["recSystemCode"],
      recSystemName: json["recSystemName"],
      recRoleId: json["recRoleId"],
      recRoleName: json["recRoleName"],
      impBatchDesc: json["impBatchDesc"],
    );
  }

  Map<String, dynamic> toJson() => {
        "noticeId": noticeId,
        "orgCode": orgCode,
        "orgName": orgName,
        "recOrgCode": recOrgCode,
        "recOrgName": recOrgName,
        "noticeType": noticeType,
        "sendType": sendType,
        "sendTime": sendTime,
        "noticeName": noticeName,
        "noticeContent": noticeContent,
        "dataStatus": dataStatus,
        "releaseMode": releaseMode,
        "roleType": roleType,
        "impBatchId": impBatchId,
        "remark": remark,
        "createBy": createBy,
        "createTime": createTime,
        "updateBy": updateBy,
        "updateTime": updateTime,
        "statusCd": statusCd,
        "params": params,
        "list": list,
        // "annexList": annexList?.toJson(),
        "statsTime": statsTime,
        "totalCount": totalCount,
        "noReadCount": noReadCount,
        "readCount": readCount,
        "recSystemCode": recSystemCode,
        "recSystemName": recSystemName,
        "recRoleId": recRoleId,
        "recRoleName": recRoleName,
        "impBatchDesc": impBatchDesc,
      };
}

// class AnnexList {
//   AnnexList();
//   factory AnnexList.fromJson(Map<String, dynamic> json) => AnnexList();

//   Map<String, dynamic> toJson() => {};
// }
