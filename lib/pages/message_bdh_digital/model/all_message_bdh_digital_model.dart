// To parse this JSON data, do
//
//     final allMessageBdhDigitalModel = allMessageBdhDigitalModelFromJson(jsonString);

import 'dart:convert';

AllMessageBdhDigitalModel allMessageBdhDigitalModelFromJson(String str) =>
    AllMessageBdhDigitalModel.fromJson(json.decode(str));

String allMessageBdhDigitalModelToJson(AllMessageBdhDigitalModel data) =>
    json.encode(data.toJson());

class AllMessageBdhDigitalModel {
  bool? success;
  int? code;
  String? msg;
  AllMessageBdhDigitalModelData? data;

  AllMessageBdhDigitalModel({
    this.success,
    this.code,
    this.msg,
    this.data,
  });

  factory AllMessageBdhDigitalModel.fromJson(Map<String, dynamic> json) =>
      AllMessageBdhDigitalModel(
        success: json["success"],
        code: json["code"],
        msg: json["msg"],
        data: json["data"] == null
            ? null
            : AllMessageBdhDigitalModelData.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "code": code,
        "msg": msg,
        "data": data?.toJson(),
      };
}

class AllMessageBdhDigitalModelData {
  DataData? data;
  int? pageNo;
  int? pageSize;
  int? dataCount;

  AllMessageBdhDigitalModelData({
    this.data,
    this.pageNo,
    this.pageSize,
    this.dataCount,
  });

  factory AllMessageBdhDigitalModelData.fromJson(Map<String, dynamic> json) =>
      AllMessageBdhDigitalModelData(
        data: json["data"] == null ? null : DataData.fromJson(json["data"]),
        pageNo: json["pageNo"],
        pageSize: json["pageSize"],
        dataCount: json["dataCount"],
      );

  Map<String, dynamic> toJson() => {
        "data": data?.toJson(),
        "pageNo": pageNo,
        "pageSize": pageSize,
        "dataCount": dataCount,
      };
}

class DataData {
  List<EMessage>? cooperateMessage;
  List<EMessage>? noticeMessage;

  DataData({
    this.cooperateMessage,
    this.noticeMessage,
  });

  factory DataData.fromJson(Map<String, dynamic> json) => DataData(
        cooperateMessage: json["cooperateMessage"] == null
            ? []
            : List<EMessage>.from(
                json["cooperateMessage"]!.map((x) => EMessage.fromJson(x))),
        noticeMessage: json["noticeMessage"] == null
            ? []
            : List<EMessage>.from(
                json["noticeMessage"]!.map((x) => EMessage.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "cooperateMessage": cooperateMessage == null
            ? []
            : List<dynamic>.from(cooperateMessage!.map((x) => x.toJson())),
        "noticeMessage": noticeMessage == null
            ? []
            : List<dynamic>.from(noticeMessage!.map((x) => x.toJson())),
      };
}

class EMessage {
  String? messageUrl;
  String? senderName;
  bool? isRead;
  String? messageId;
  String? createDate;
  String? messageContent;

  EMessage({
    this.messageUrl,
    this.senderName,
    this.isRead,
    this.messageId,
    this.createDate,
    this.messageContent,
  });

  factory EMessage.fromJson(Map<String, dynamic> json) => EMessage(
        messageUrl: json["messageUrl"],
        senderName: json["senderName"],
        isRead: json["isRead"],
        messageId: json["messageId"],
        createDate: json["createDate"],
        messageContent: json["messageContent"],
      );

  Map<String, dynamic> toJson() => {
        "messageUrl": messageUrl,
        "senderName": senderName,
        "isRead": isRead,
        "messageId": messageId,
        "createDate": createDate,
        "messageContent": messageContent,
      };
}

class EnumValues<T> {
  Map<String, T> map;
  late Map<T, String> reverseMap;

  EnumValues(this.map);

  Map<T, String> get reverse {
    reverseMap = map.map((k, v) => MapEntry(v, k));
    return reverseMap;
  }
}
