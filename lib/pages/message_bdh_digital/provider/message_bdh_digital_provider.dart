import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/all_message_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/notice_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/pages/message_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:flutter/cupertino.dart';
import 'package:logger/logger.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class MessageBdhDigitalProvider extends ChangeNotifier {
  // factory MessageBdhDigitalProvider() => _instance;
  // MessageBdhDigitalProvider._();
  // static final MessageBdhDigitalProvider _instance =
  //     MessageBdhDigitalProvider._();

  //已读 未读状态处理
  List<MessageBdhDigitalItemModel> _readTabsList = []; //已读未读 tab
  MessageBdhDigitalItemModel? _currentReadTabModel; //当前选中: 已读未读tab item

//消息 tabbar 状态管理
  List<MessageBdhDigitalItemModel> messageTabsList = [];
  MessageBdhDigitalItemModel? _currentMessageTabModel; //当前选中: 消息 item
  int currentMessageIndex = 0;

//net data list ---below ----
  bool isBindOA = false;
  AllMessageBdhDigitalModel? currentMessageModel; // message list data
  AllMessageBdhDigitalModel?
      currentUnReadMessageModel; //  unread message list data

  List<EMessage>? cooperateMessage; //协同办公, 全部
  List<EMessage>? cooperateMessageUnread; //协同办公, 未读
  List<EMessage>? noticeMessage; //新闻公告,全部
  List<EMessage>? noticeMessageUnread; //新闻公告,未读
  List<EMessage>? endShowDataList = []; //最终显示 list : 协同办公 + 新闻公告
  List<EMessage>? endShowNewsDataList = []; //最终显示 list : 新闻公告
  // List<NoticeRecordBdhDigitalModel>? endShowNoticeDataList =
  //     []; //最终显示 list : 业务通知
  // List<NoticeRecordBdhDigitalModel>? endShowSystemDataList =
  //     []; //最终显示 list : 业务消息
//未读消息数量
  int unReadCountCooperate = 0;
  int unReadCountNews = 0;
  int unReadNoticeCount = 0;
  int unReadSystemCount = 0;

  List<dynamic>? showList = [];
  List<dynamic>? showSearchList = [];
  bool _isLoading = false;
  String? _errorMessage;

//协同
  final RefreshController _refreshControllerCooprated =
      RefreshController(initialRefresh: false);
  //新闻
  final RefreshController _refreshControllerNews =
      RefreshController(initialRefresh: false);
//业务通知
  final RefreshController _refreshControllerNotice =
      RefreshController(initialRefresh: false);
// 业务消息
  final RefreshController _refreshControllerSystem =
      RefreshController(initialRefresh: false);

  bool get isLoading => _isLoading;
  MessageBdhDigitalItemModel? get currentReadTabModel => _currentReadTabModel;
  MessageBdhDigitalItemModel? get currentMsgTabModel => _currentMessageTabModel;
  List<MessageBdhDigitalItemModel> get readTabsList => _readTabsList;
  String? get errorMessage => _errorMessage;
  bool get hasError => _errorMessage != null;

  //分页
  RefreshController get refreshControllerCooprated =>
      _refreshControllerCooprated;
  RefreshController get refreshControllerNews => _refreshControllerNews;
  RefreshController get refreshControllerNotice => _refreshControllerNotice;
  RefreshController get refreshControllerSystem => _refreshControllerSystem;
  static const int pageNumFirst = 1;
  static const int pageSize = 10;
  int _currentPageNum = pageNumFirst;

  initData() {
    _readTabsList = [
      MessageBdhDigitalItemModel(name: '未读', isSelected: true, index: 0),
      MessageBdhDigitalItemModel(name: '全部', isSelected: false, index: 1),
    ];
    _setCurrentReadTabModel(_readTabsList.first); //初始化

    //message list
    messageTabsList = [
      MessageBdhDigitalItemModel(name: '协同办公', isSelected: true, index: 0),
      MessageBdhDigitalItemModel(name: '新闻公告', isSelected: false, index: 1),
      MessageBdhDigitalItemModel(name: '业务通知', isSelected: true, index: 2),
      MessageBdhDigitalItemModel(name: '业务消息', isSelected: false, index: 3),
    ];
    _setCurrentMsgTabModel(messageTabsList.first); //初始化
    currentMessageIndex = 0;
  }

//已读 全部 状态处理
  dealWithSelectedReadStatusWithModel(MessageBdhDigitalItemModel model) {
    for (int i = 0; i < readTabsList.length; i++) {
      MessageBdhDigitalItemModel item = readTabsList[i];
      if (item.name == model.name) {
        item.isSelected = true;
        _setCurrentReadTabModel(item);
      } else {
        item.isSelected = false;
      }
    }
    _setLoading(true);
    setEndShowData();
    notifyListeners();
  }

  //tab message list 状态处理
  dealWithSelectedMessageStatusWithIndex(int index) {
    currentMessageIndex = index;
    // for (int i = 0; i < messageTabsList.length; i++) {
    //   MessageBdhDigitalItemModel item = messageTabsList[i];
    //   if (item.index == index) {
    //     item.isSelected = true;
    //     _setCurrentMsgTabModel(item);
    //   } else {
    //     item.isSelected = false;
    //   }
    // }

    if (index == 0 || index == 1) {
      _setLoading(true);
      setEndShowData();
    } else {
      showList?.clear();
      showSearchList?.clear();
      refreshNotice();
    }
    notifyListeners();
  }

  clearData() {
    cooperateMessage?.clear();
    cooperateMessageUnread?.clear();
    noticeMessage?.clear();
    noticeMessageUnread?.clear();
    endShowDataList?.clear();
    endShowNewsDataList?.clear();
    cooperateMessage?.clear();
    showList?.clear();
    showSearchList?.clear();
    notifyListeners();
  }

  clearMessageCount() {
    unReadCountCooperate = 0;
    unReadCountNews = 0;
    unReadNoticeCount = 0;
    unReadSystemCount = 0;
    notifyListeners();
  }

  //清除所有未读消息 step1
  Future<void> changeAllUnReadMessageToRead() async {
    var res = await BdhDigitalService.changeAllUnReadMessageToRead({});
    Logger().i('res = $res');
  }

  //清除所有未读消息 step2
  Future<void> handleClearAllBdhDigital() async {
    var res = await BdhDigitalService.handleClearAllBdhDigital({});
    Logger().i('res = $res');
  }

  //查询是否绑定OA
  Future<bool> getIsBind() async {
    var res = await BdhDigitalService.oaIsBindBdhDigital();
    if (res.code == 0 && res.data == null) {
      isBindOA = false;
    } else {
      isBindOA = true;
    }
    return isBindOA;
  }

  //获取业务通知未读
  Future<void> getUnreadNoticeBdhDigital() async {
    await BdhDigitalService.getUnreadNoticeBdhDigital({
      "systemCode": kSystemCode,
    }).then((res) {
      // Logger().i('res=$res');
      unReadNoticeCount = int.parse(res.data);
      notifyListeners();
    });
  }

  //获取系统通知未读
  Future<void> getUnreadSystemBdhDigital() async {
    await BdhDigitalService.getUnreadSystemBdhDigital({
      "systemCode": kSystemCode,
    }).then((res) {
      // Logger().i('res=$res');
      unReadSystemCount = int.parse(res.data);
      notifyListeners();
    });
  }

//设置为消息已读
  Future<dynamic> changeMessageToReadBdhDigital(String messageId) async {
    var res = await BdhDigitalService.changeMessageToReadBdhDigital(
        {'messageId': messageId});
    return res;
  }

  RefreshController get getCurrentRefreshController {
    if (currentMessageIndex == 0) {
      return _refreshControllerCooprated;
    } else if (currentMessageIndex == 1) {
      return _refreshControllerNews;
    } else if (currentMessageIndex == 2) {
      return _refreshControllerNotice;
    } else if (currentMessageIndex == 3) {
      return _refreshControllerSystem;
    } else {
      return _refreshControllerCooprated;
    }

    // return currentMessageIndex == 2
    //     ? _refreshControllerNotice
    //     : _refreshControllerSystem;
  }

//获取'全部' 数据[1,协同办公, 2.新闻公告  全部数据]
  Future<void> getAllMessageData() async {
    _setLoading(true);
    _clearError();
    try {
      currentMessageModel = await BdhDigitalService.getAllMessagesBdhDigital({
        "pageNo": 1,
        "pageSize": 99999,
      });
      //tabsType = 0(协同办公), buttonType = 全部, : this.messageList.cooperateMessage
      cooperateMessage = currentMessageModel?.data?.data?.cooperateMessage;

      //tabsType = 1(新闻公告), buttonType = 全部, : this.messageList.noticeMessage
      noticeMessage = currentMessageModel?.data?.data?.noticeMessage;
      setEndShowData();
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        getCurrentRefreshController.refreshCompleted();
      });
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

//获取'未读' 数据[1,协同办公, 2.新闻公告, 未读数据]
  Future<void> getUnReadMessageData() async {
    _setLoading(true);
    _clearError();
    try {
      currentUnReadMessageModel =
          await BdhDigitalService.getUnReadMessagesBdhDigital({
        "pageNo": 1,
        "pageSize": 99999,
      });
      //tabsType = 0(协同办公), buttonType = 未读, : this.messageList.cooperateMessage
      cooperateMessageUnread =
          currentUnReadMessageModel?.data?.data?.cooperateMessage;

      //tabsType = 1(新闻公告), buttonType = 未读, : this.messageList.noticeMessage
      noticeMessageUnread =
          currentUnReadMessageModel?.data?.data?.noticeMessage;
      unReadCountCooperate = (cooperateMessageUnread ?? []).length;
      unReadCountNews = (noticeMessageUnread ?? []).length;
      setEndShowData();
      Future.delayed(const Duration(milliseconds: 300)).then((_) {
        getCurrentRefreshController.refreshCompleted();
      });
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  //获取'业务通知' 数据 base api
  Future<List<dynamic>?> getNoticeListBdhDigital({
    int? pageNum,
  }) async {
    _setLoading(true);
    _clearError();
    try {
      NoticeBdhDigitalModel model =
          await BdhDigitalService.getNoticeListBdhDigital({
        "page": pageNum,
        "rows": pageSize,
        "isRead": (currentReadTabModel?.index == 0) ? '0' : '1',
        "systemCode": kSystemCode,
      });
      Logger().i('model = $model');
      // endShowNoticeDataList = model.data?.records;
      // showList = model.data?.records;
      return model.data?.records ?? [];
    } catch (e) {
      _setError(e.toString());
      return null;
    } finally {
      _setLoading(false);
    }
  }

  //获取'业务消息' 数据 base api
  Future<List<dynamic>?> getSystemListBdhDigital({
    int? pageNum,
  }) async {
    _setLoading(true);
    _clearError();
    try {
      NoticeBdhDigitalModel model =
          await BdhDigitalService.getSystemListBdhDigital({
        "page": pageNum,
        "rows": 10,
        "isRead": (currentReadTabModel?.index == 0) ? '0' : '1',
        "systemCode": kSystemCode,
      });
      Logger().i('model = $model');
      return model.data?.records ?? [];
    } catch (e) {
      _setError(e.toString());
      return null;
    } finally {
      _setLoading(false);
    }
  }

  //获取'业务通知' 刷新
  Future<void> refreshNotice() async {
    try {
      _currentPageNum = pageNumFirst;
      var data = currentMessageIndex == 2
          ? await getNoticeListBdhDigital(pageNum: 1)
          : await getSystemListBdhDigital(pageNum: 1);
      if ((data ?? []).isEmpty) {
        showList?.clear();
        showSearchList?.clear();
        getCurrentRefreshController.refreshCompleted(resetFooterState: true);
      } else {
        showList?.clear();
        showList?.addAll(data as Iterable);
        showSearchList?.clear();
        showSearchList?.addAll(data as Iterable);
        notifyListeners();
        // 小于分页的数量,禁止上拉加载更多
        if (data!.length < pageSize) {
          getCurrentRefreshController.loadNoData();
        } else {
          //防止上次上拉加载更多失败,需要重置状态
          getCurrentRefreshController.loadComplete();
        }
      }
      Future.delayed(const Duration(milliseconds: 500)).then((_) {
        getCurrentRefreshController.refreshCompleted();
      });
    } catch (e) {
      _setError(e.toString());
      getCurrentRefreshController.refreshFailed();
    } finally {
      notifyListeners();
    }
  }

  //获取'业务通知' 加载
  Future<void> loadMoreNotice() async {
    try {
      var data = currentMessageIndex == 2
          ? await getNoticeListBdhDigital(pageNum: ++_currentPageNum)
          : await getSystemListBdhDigital(pageNum: ++_currentPageNum);
      // var data = await getNoticeListBdhDigital(pageNum: ++_currentPageNum);
      if ((data ?? []).isEmpty) {
        _currentPageNum--;
        refreshControllerNotice.loadNoData();
        refreshControllerSystem.loadNoData();
      } else {
        showList?.addAll(data as Iterable);
        showSearchList?.addAll(data as Iterable);
        // 小于分页的数量,禁止上拉加载更多
        if (data!.length < pageSize) {
          getCurrentRefreshController.loadNoData();
        } else {
          //防止上次上拉加载更多失败,需要重置状态
          getCurrentRefreshController.loadComplete();
        }
      }
      Future.delayed(const Duration(milliseconds: 500)).then((_) {
        getCurrentRefreshController.refreshCompleted();
      });
    } catch (e) {
      _setError(e.toString());
      refreshControllerNotice.refreshFailed();
      refreshControllerSystem.refreshFailed();
    } finally {
      notifyListeners();
    }
  }

  setEndShowData() {
    switch (currentMessageIndex) {
      case 0:
        //协同办公
        endShowDataList?.clear();
        if (currentReadTabModel?.index == 0) {
          //未读
          endShowDataList?.addAll(cooperateMessageUnread ?? []);
        } else {
          //全部
          endShowDataList?.addAll(cooperateMessage ?? []);
        }
        _setLoading(false);
        notifyListeners();
        break;
      case 1:
        // //新闻公告
        // endShowDataList?.clear();
        // if (currentReadTabModel?.index == 0) {
        //   //未读
        //   endShowDataList?.addAll(noticeMessageUnread ?? []);
        // } else {
        //   //全部
        //   endShowDataList?.addAll(noticeMessage ?? []);
        // }
        //新闻公告
        endShowNewsDataList?.clear();
        if (currentReadTabModel?.index == 0) {
          //未读
          endShowNewsDataList?.addAll(noticeMessageUnread ?? []);
        } else {
          //全部
          endShowNewsDataList?.addAll(noticeMessage ?? []);
        }
        _setLoading(false);
        notifyListeners();
        break;
      case 2:
        //业务通知
        refreshNotice();
        break;
      case 3:
        //业务消息
        refreshNotice();
        break;
      default:
        _setLoading(false);
        endShowNewsDataList = [];
    }
  }

  dealWithSearchReasult(String searchContent) {
    switch (currentMessageIndex) {
      case 0:
        //协同办公
        endShowDataList?.clear();
        if (currentReadTabModel?.index == 0) {
          //未读
          var temp = cooperateMessageUnread?.where((item) {
            return (item.messageContent ?? '').contains(searchContent);
          });
          endShowDataList?.addAll(temp as Iterable<EMessage>);
        } else {
          //全部
          var temp = cooperateMessage?.where((item) {
            return (item.messageContent ?? '').contains(searchContent);
          });
          endShowDataList?.addAll(temp as Iterable<EMessage>);
          // endShowDataList?.addAll(cooperateMessage ?? []);
        }
        _setLoading(false);
        notifyListeners();
        break;
      case 1:
        //新闻公告
        // endShowDataList?.clear();
        // if (currentReadTabModel?.index == 0) {
        //   //未读
        //   var temp = noticeMessageUnread?.where((item) {
        //     return (item.messageContent ?? '').contains(searchContent);
        //   });
        //   endShowDataList?.addAll(temp as Iterable<EMessage>);
        //   // endShowDataList?.addAll(noticeMessageUnread ?? []);
        // } else {
        //   //全部
        //   var temp = noticeMessage?.where((item) {
        //     return (item.messageContent ?? '').contains(searchContent);
        //   });
        //   endShowDataList?.addAll(temp as Iterable<EMessage>);
        //   // endShowDataList?.addAll(noticeMessage ?? []);
        // }

        endShowNewsDataList?.clear();
        if (currentReadTabModel?.index == 0) {
          //未读
          var temp = noticeMessageUnread?.where((item) {
            return (item.messageContent ?? '').contains(searchContent);
          });
          endShowNewsDataList?.addAll(temp as Iterable<EMessage>);
        } else {
          //全部
          var temp = noticeMessage?.where((item) {
            return (item.messageContent ?? '').contains(searchContent);
          });
          endShowNewsDataList?.addAll(temp as Iterable<EMessage>);
        }

        _setLoading(false);
        notifyListeners();
        break;
      case 2:
        //业务通知
        var temp = showSearchList?.where((item) {
          NoticeRecordBdhDigitalModel temItem = item;
          return (temItem.noticeContent ?? '').contains(searchContent);
        });
        showList?.clear();
        showList?.addAll(temp!);
        notifyListeners();
        break;
      case 3:
        //业务消息
        var temp = showSearchList?.where((item) {
          NoticeRecordBdhDigitalModel temItem = item;
          return (temItem.noticeContent ?? '').contains(searchContent);
        });
        showList?.clear();
        showList?.addAll(temp!);
        notifyListeners();
        break;
      default:
        _setLoading(false);
        endShowDataList = [];
        endShowNewsDataList = [];
    }
  }

  // 刷新数据 : 协同办公 + 新闻公告
  Future<void> refreshCooperateAndNewsMessage() async {
    getUnreadNoticeBdhDigital();
    getUnreadSystemBdhDigital();
    getIsBind().then((isBind) {
      if (isBind) {
        getAllMessageData();
        getUnReadMessageData();
      } else {
        cooperateMessageUnread = [];
        noticeMessageUnread = [];
        cooperateMessage = [];
        noticeMessage = [];
      }
    });
  }

  void _setCurrentReadTabModel(MessageBdhDigitalItemModel model) {
    _currentReadTabModel = model;
  }

  void _setCurrentMsgTabModel(MessageBdhDigitalItemModel model) {
    _currentMessageTabModel = model;
  }

  // 私有方法
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
    }
  }

  void _setError(String error) {
    _errorMessage = error;
  }

  void _clearError() {
    _errorMessage = null;
  }
}
