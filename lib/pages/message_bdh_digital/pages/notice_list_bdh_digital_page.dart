import 'dart:core';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/notice_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/pages/notice_system_ditail_page.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/provider/message_bdh_digital_provider.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/subview/message_bdh_digital_notice_cell_view.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

class NoticeListBdhDigitalPage extends StatefulWidget {
  const NoticeListBdhDigitalPage({super.key});

  @override
  State<NoticeListBdhDigitalPage> createState() =>
      _NoticeListBdhDigitalPageState();
}

class _NoticeListBdhDigitalPageState extends State<NoticeListBdhDigitalPage> {
  // final RefreshController _refreshController =
  //     RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    MessageBdhDigitalProvider tempProvider =
        Provider.of<MessageBdhDigitalProvider>(context, listen: false);
    tempProvider.refreshNotice();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MessageBdhDigitalProvider>(
      builder: (context, messageProvider, child) {
        if (messageProvider.isLoading) {
          return const ViewStateBusyWidget();
        }
        if (messageProvider.showList!.isEmpty) {
          return const BdhEmptyView();
        }
        return SmartRefresher(
          enablePullDown: true,
          enablePullUp: true,
          onRefresh: messageProvider.refreshNotice,
          onLoading: messageProvider.loadMoreNotice,
          controller: messageProvider.refreshControllerNotice,
          child: ListView.separated(
            padding: EdgeInsets.only(top: 10.px, bottom: 20.px),
            shrinkWrap: true,
            scrollDirection: Axis.vertical,
            itemCount: (messageProvider.showList ?? []).length,
            itemBuilder: (BuildContext context, int index) {
              NoticeRecordBdhDigitalModel modelItem =
                  (messageProvider.showList ?? [])[index];
              return MessageBdhDigitalNoticeCellView(
                msgTabIndex: 2,
                modelItem: modelItem,
                clickeNoticeCellItem: (e) {
                  Logger().i('-----$e');
                  Navigator.push(
                      context,
                      CupertinoPageRoute(
                          builder: (_) => NoticeSystemDitailPage(
                                messageId: modelItem.noticeId ?? 0,
                                tabType: 2,
                              ))).then((res) {
                    messageProvider.refreshNotice();
                    // MessageBdhDigitalProvider tempProvider =
                    //     Provider.of<MessageBdhDigitalProvider>(context,
                    //         listen: false);
                    // tempProvider.refreshNotice();
                  });
                },
              );
            },
            separatorBuilder: (BuildContext context, int index) {
              return Container(
                color: Colors.transparent,
                height: 10.px,
              );
            },
          ),
        );
      },
    );
  }
}


// class _NoticeListBdhDigitalPageState extends State<NoticeListBdhDigitalPage> {
//   @override
//   Widget build(BuildContext context) {
//     return ProviderWidget(
//       builder: (BuildContext context, ChangeNotifier model, child) {
//         model as NoticeBdhDigitalViewModel;
//         return model.isBusy
//             ? const ViewStateBusyWidget()
//             : model.isEmpty
//                 ? const BdhEmptyView()
//                 : SmartRefresher(
//                     enablePullDown: true,
//                     enablePullUp: true,
//                     onRefresh: model.refresh,
//                     onLoading: model.loadMore,
//                     controller: model.refreshController,
//                     child: ListView.separated(
//                       padding: const EdgeInsets.only(top: 0),
//                       shrinkWrap: true,
//                       scrollDirection: Axis.vertical,
//                       itemCount: model.list.length,
//                       itemBuilder: (BuildContext context, int index) {
//                         NoticeRecordBdhDigitalModel modelItem =
//                             model.list[index];
//                         return MessageBdhDigitalNoticeCellView(
//                           msgTabIndex: 2,
//                           modelItem: modelItem,
//                         );
//                       },
//                       separatorBuilder: (BuildContext context, int index) {
//                         return Container(
//                           color: Colors.transparent,
//                           height: 10.px,
//                         );
//                       },
//                     ),
//                   );
//       },
//       model: NoticeBdhDigitalViewModel(),
//       onModelReady: (ChangeNotifier model) {
//         model as NoticeBdhDigitalViewModel;
//         model.initData();
//       },
//     );
//   }
// }
