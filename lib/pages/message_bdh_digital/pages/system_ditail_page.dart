import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/notice_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/notice_ditail_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/system_ditail_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_tool.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:webview_flutter/webview_flutter.dart';

// ignore: must_be_immutable
class SystemDitailPage extends StatefulWidget {
  int messageId;
  int tabType;
  NoticeRecordBdhDigitalModel modelItem;
  SystemDitailPage({
    super.key,
    required this.modelItem,
    required this.messageId,
    required this.tabType,
  });

  @override
  State<SystemDitailPage> createState() {
    return _SystemDitailPageState();
  }
}

class _SystemDitailPageState extends State<SystemDitailPage> {
  SystemDitailBdhDigitalModel? model;
  bool isLoading = false;
  WebViewController controller = WebViewController();
  double webViewHeight = 0;
  double bottomViewHeight = 60.px;
  String htmlStr = "";

  @override
  void initState() {
    super.initState();
    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel("Resize", onMessageReceived: (message) {
        double height = double.parse(message.message);
        setState(() {
          webViewHeight = height / 10;
        });
      });
    getData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: HexColor("#F3F5F9"),
      // backgroundColor: Colors.red,
      appBar: AppBar(
        title: const Text("详情"),
      ),
      body: SafeArea(child: LayoutBuilder(builder: (context, cons) {
        return isLoading
            ? const ViewStateBusyWidget()
            : Column(
                children: [
                  Container(
                    padding: EdgeInsets.only(
                        left: 5.px, right: 5.px, top: 10.px, bottom: 15.px),
                    margin: EdgeInsets.only(
                        left: 15.px, right: 15.px, top: 10.px, bottom: 15.px),
                    // color: HexColor("#F3F5F9"),
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8.px)),
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          //通知详情 标题
                          Row(
                            children: [
                              Expanded(
                                child: Container(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 15.px),
                                  child: Text(
                                    widget.modelItem.title ?? '',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      color: HexColor("#31394C"),
                                      fontWeight: FontWeight.w600,
                                      fontSize: 18.px,
                                    ),
                                  ),
                                ),
                              )
                            ],
                          ),

                          //通知详情 内容
                          Container(
                            margin: EdgeInsets.only(top: 20.px),
                            padding: EdgeInsets.symmetric(horizontal: 15.px),
                            color: Colors.white,
                            child: Text(
                              widget.modelItem.content ?? '',
                              style: TextStyle(
                                color: HexColor("#000000"),
                                fontWeight: FontWeight.w400,
                                fontSize: 16.px,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
      })),
    );
  }

  getData() {
    setState(() {
      isLoading = true;
    });
    BdhDigitalService.systemDitailInfoBdhDigital(widget.messageId).then((res) {
      Logger().i('res==$res');
      setState(() {
        model = res;
        isLoading = false;
      });
      // getContentWithModel(res);
    });
  }

  getContentWithModel(NoticeDitailBdhDigitalModel model) {
    String html = """
<!DOCTYPE html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            margin: 0;
            padding: 16px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
        }
        
        /* 限制所有图片的最大宽度 */
        img {
            max-width: 100% !important;
            height: auto !important;
            display: block;
            margin: 10px auto;
            border-radius: 4px;
        }
        
        /* 处理可能存在的内联样式 */
        * {
            max-width: 100% !important;
            box-sizing: border-box;
        }
        
        /* 确保容器不会超出屏幕 */
        div, p, span, table {
            max-width: 100%;
            overflow-wrap: break-word;
            word-wrap: break-word;
        }
        
        /* 表格响应式处理 */
        table {
            width: 100%;
            border-collapse: collapse;
            overflow-x: auto;
            display: block;
            white-space: nowrap;
        }
        
        /* 视频和其他媒体元素 */
        video, iframe, embed, object {
            max-width: 100% !important;
            height: auto !important;
        }
        
        h3 {
            text-align: center;
            margin: 10px 0;
            font-size: 1.2em;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h3>${model.data?.noticeName ?? ''}</h3>
    <div class="content">
        ${model.data?.noticeContent ?? ''}
    </div>
</body>
<script>
const resizeObserver = new ResizeObserver(entries => {
    Resize.postMessage(document.documentElement.scrollHeight.toString());
});
resizeObserver.observe(document.body);

// 确保动态加载的图片也能正确调整大小
document.addEventListener('DOMContentLoaded', function() {
    const images = document.querySelectorAll('img');
    images.forEach(img => {
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
        img.style.display = 'block';
        img.style.margin = '10px auto';
    });
});
</script>
</html>
""";

//     String html = """
// <!DOCTYPE html>
// <html>
// <head><meta name="viewport" content="width=device-width, initial-scale=1.0"></head>
// <body>
//  <h3 style="text-align: center;">${model.data?.noticeName ?? ''}</h3>
// ${model.data?.noticeContent ?? ''}
// </body>
// </html>
// <script>
// const resizeObserver = new ResizeObserver(entries =>
// Resize.postMessage(document.documentElement.scrollHeight.toString()) )
// resizeObserver.observe(document.body)
// </script>
// """;

// getVideoSourceMethordOne(html); //方式一
    // getVideoSourceMethordTwo(html); //方式二
    // String baseUrl = "${urlConfig.microfront}";
    // String tempStr = html.replaceAll('src="', 'src="${baseUrl}');
    // Logger().i(tempStr);
    // htmlStr = tempStr;
    controller.loadHtmlString(html);
    setState(() {});
  }

  // setState(() {});
}
