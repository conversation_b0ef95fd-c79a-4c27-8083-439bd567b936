import 'dart:core';

import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/all_message_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/pages/message_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/provider/message_bdh_digital_provider.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/subview/message_bdh_digital_cooperate_cell_view.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page.dart';
import 'package:bdh_smart_agric_app/pages/user/html_page_inappweb.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

// ignore: must_be_immutable
class MessageListBdhDigitalPage extends StatefulWidget {
  MessageBdhDigitalItemModel model;
  MessageListBdhDigitalPage({
    super.key,
    required this.model,
  });

  @override
  State<MessageListBdhDigitalPage> createState() =>
      _MessageListBdhDigitalPageState();
}

class _MessageListBdhDigitalPageState extends State<MessageListBdhDigitalPage>
    with AutomaticKeepAliveClientMixin {
  List<EMessage>? cooperateMessage; //协同办公
  List<EMessage>? noticeMessage; //新闻公告
  bool isBusy = true;
  // final RefreshController _refreshController =
  //     RefreshController(initialRefresh: false);
  AllMessageBdhDigitalModel? dataModel;

  @override
  void initState() {
    super.initState();
    // MessageBdhDigitalProvider tempProvider =
    //     Provider.of<MessageBdhDigitalProvider>(context, listen: false);
    // tempProvider.initData();
    // tempProvider.refreshCooperateAndNewsMessage();
  }

  // goToDitailPage(EMessage modelItem) {
  //   Logger().i('model = ${modelItem.messageUrl ?? ''}');
  // }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Consumer<MessageBdhDigitalProvider>(
      builder: (context, messageProvider, child) {
        if (messageProvider.isLoading) {
          return const ViewStateBusyWidget();
        }
        if (messageProvider.endShowDataList!.isEmpty) {
          return const BdhEmptyView();
        }
        return SmartRefresher(
          enablePullDown: true,
          enablePullUp: false,
          onRefresh: messageProvider.refreshCooperateAndNewsMessage,
          controller: messageProvider.refreshControllerCooprated,
          child: ListView.separated(
            padding: EdgeInsets.only(top: 10.px, bottom: 20.px),
            shrinkWrap: true,
            scrollDirection: Axis.vertical,
            itemCount: (messageProvider.endShowDataList ?? []).length,
            itemBuilder: (BuildContext context, int index) {
              EMessage modelItem =
                  (messageProvider.endShowDataList ?? [])[index];
              return MessageBdhDigitalCooperateCellView(
                msgTabIndex: messageProvider.currentMessageIndex,
                modelItem: modelItem,
                clickeCellItem: (e) {
                  Logger().i('-----$e');
                  messageProvider
                      .changeMessageToReadBdhDigital(modelItem.messageId ?? '')
                      .then((res) {
                    Logger().i('res = $res');
                    Navigator.of(context)
                        .push(CupertinoPageRoute(builder: (ctx) {
                      // return HtmlPage(
                      //     title: e.senderName ?? "",
                      //     content: e.messageUrl ?? '');
                      return HtmlPageInappweb(
                          content: e.messageUrl ?? '',
                          title: e.senderName ?? "");
                    })).then((res) {
                      messageProvider.refreshCooperateAndNewsMessage();
                    });
                  });
                },
              );
            },
            separatorBuilder: (BuildContext context, int index) {
              return Container(
                color: Colors.transparent,
                height: 10.px,
              );
            },
          ),
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;

  // @override
  // bool get wantKeepAlive => true;

  // @override
  // Widget build(BuildContext context) {
  //   return Consumer<MessageBdhDigitalProvider>(
  //     builder: (context, messageProvider, child) {
  //       String? readItemName = messageProvider.currentReadTabModel?.name;
  //       if (messageProvider.isLoading) {
  //         return const ViewStateBusyWidget();
  //       }
  //       if (messageProvider.cooperateMessage!.isEmpty) {
  //         return const BdhEmptyView();
  //       }
  //       return SmartRefresher(
  //         enablePullDown: true,
  //         enablePullUp: false,
  //         onRefresh: messageProvider.refresh,
  //         controller: _refreshController,
  //         child: ListView.separated(
  //           padding: EdgeInsets.only(top: 10.px, bottom: 20.px),
  //           shrinkWrap: true,
  //           scrollDirection: Axis.vertical,
  //           // itemCount: (messageProvider.cooperateMessageUnread ?? []).length,
  //           itemCount: (messageProvider.currentReadTabModel?.name == '未读')
  //               ? (messageProvider.cooperateMessageUnread ?? []).length
  //               : (messageProvider.cooperateMessage ?? []).length,
  //           itemBuilder: (BuildContext context, int index) {
  //             if (messageProvider.currentMessageIndex == 0) {
  //               EMessage modelItem = (readItemName == '未读')
  //                   ? (messageProvider.cooperateMessageUnread ?? [])[index]
  //                   : (messageProvider.cooperateMessage ?? [])[index];
  //               // return Container();
  //               return MessageBdhDigitalCooperateCellView(
  //                 modelItem: modelItem,
  //                 clickeCellItem: (e) {
  //                   Logger().i('-----$e');
  //                 },
  //               );
  //             } else {
  //               return Container();
  //             }
  //           },
  //           separatorBuilder: (BuildContext context, int index) {
  //             return Container(
  //               color: Colors.transparent,
  //               height: 10.px,
  //             );
  //           },
  //         ),
  //       );
  //     },
  //   );
  // }
}
