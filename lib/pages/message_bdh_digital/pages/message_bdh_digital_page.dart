import 'package:bdh_smart_agric_app/pages/home/<USER>/components/underline_indicator.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/underline_indicator_scalefont.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/pages/message_list_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/pages/news_list_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/pages/notice_list_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/pages/system_list_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/provider/message_bdh_digital_provider.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/subview/search_view_bdh_digital.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';

class MessageBdhDigitalPage extends StatefulWidget {
  const MessageBdhDigitalPage({super.key});

  @override
  State<MessageBdhDigitalPage> createState() => _MessageBdhDigitalPageState();
}

class _MessageBdhDigitalPageState extends State<MessageBdhDigitalPage>
    with TickerProviderStateMixin {
  TextEditingController searchController = TextEditingController();
  final FocusScopeNode focusScopeNode = FocusScopeNode();

  // bool showDeletIcon = true;
  TabController? _tabController;
  List<Widget> pageViewList = [];

  @override
  void initState() {
    super.initState();
    MessageBdhDigitalProvider tempProvider =
        Provider.of<MessageBdhDigitalProvider>(context, listen: false);
    _tabController =
        TabController(length: tempProvider.messageTabsList.length, vsync: this);
    for (int i = 0; i < tempProvider.messageTabsList.length; i++) {
      MessageBdhDigitalItemModel item = tempProvider.messageTabsList[i];
      if (i == 0) {
        pageViewList.add(MessageListBdhDigitalPage(model: item));
      } else if (i == 1) {
        pageViewList.add(NewsListBdhDigitalPage(model: item));
      } else if (i == 2) {
        pageViewList.add(const NoticeListBdhDigitalPage());
      } else if (i == 3) {
        pageViewList.add(const SystemListBdhDigitalPage());
      }
    }
    // for (MessageBdhDigitalItemModel item in tempProvider.messageTabsList) {
    //   pageViewList.add(MessageListBdhDigitalPage(
    //     model: item,
    //   ));
    // }
    changeTabAction();
  }

//点击搜索按钮
  void clickedSeachBtn(String conntent) {
    FocusScope.of(context).unfocus();
    MessageBdhDigitalProvider tempProvider =
        Provider.of<MessageBdhDigitalProvider>(context, listen: false);
    Logger().i('点击搜索=$conntent');

    tempProvider.dealWithSearchReasult(conntent);
  }

  void clickedClearBtn() {
    FocusScope.of(context).unfocus();
    MessageBdhDigitalProvider tempProvider =
        Provider.of<MessageBdhDigitalProvider>(context, listen: false);
    tempProvider.setEndShowData();
  }

  changeTabAction() {
    if (_tabController != null) {
      _tabController!.addListener(() {
        int index = _tabController!.index;
        Logger().i('滑动  切换tabbar----------------------$index');
        FocusScope.of(context).unfocus();
        MessageBdhDigitalProvider tempProvider =
            Provider.of<MessageBdhDigitalProvider>(context, listen: false);
        tempProvider.dealWithSelectedMessageStatusWithIndex(index);
      });
    }
  }

  Widget getItem(MessageBdhDigitalItemModel model,
      MessageBdhDigitalProvider providerModel) {
    return GestureDetector(
      onTap: () {
        providerModel.dealWithSelectedReadStatusWithModel(model);
        // dealWithSelectedReadStatusWithModel(model);
        // pageController.animateToPage(model.index,
        //     duration: const Duration(milliseconds: 500),
        //     curve: Curves.easeInOut);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16.px),
        margin: EdgeInsets.only(left: 10.px),
        height: 28.px,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4.px),
          border: model.isSelected
              ? Border.all(
                  width: 0.5, color: const Color.fromRGBO(0, 127, 255, 1))
              : Border.all(
                  width: 0.01, color: const Color.fromRGBO(245, 245, 245, 1)),
          color: model.isSelected
              ? const Color.fromRGBO(0, 127, 255, 0.1)
              : const Color.fromRGBO(245, 245, 245, 1),
        ),
        child: Align(
          alignment: Alignment.center,
          child: Text(
            model.name,
            style: TextStyle(
              color: model.isSelected
                  ? const Color.fromRGBO(0, 127, 255, 1)
                  : const Color.fromRGBO(34, 34, 34, 0.6),
              fontSize: 12.px,
              fontWeight: model.isSelected ? FontWeight.w600 : FontWeight.w400,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: HexColor("#F3F5F9"),
      body: Consumer<MessageBdhDigitalProvider>(
        builder: (context, providerModel, child) {
          return Stack(
            children: [
              Image.asset(
                  width: 375.px,
                  height: 812.px,
                  fit: BoxFit.fitWidth,
                  ImageHelper.wrapAssets("workTabelBG.png")),
              Column(
                children: [
                  //1.搜索视图
                  Container(
                    padding: EdgeInsets.only(top: 55.px, right: 20.px),
                    child: SizedBox(
                      height: 35.px,
                      width: 375.px,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          GestureDetector(
                            onTap: () {
                              Navigator.of(context).pop();
                              FocusScope.of(context).unfocus();
                              providerModel.initData();
                            },
                            child: Container(
                              padding:
                                  EdgeInsets.only(left: 16.px, right: 10.px),
                              child: Image.asset(
                                width: 22.px,
                                height: 22.px,
                                ImageHelper.wrapAssets("backBlackIcon.png"),
                              ),
                            ),
                          ),
                          Expanded(
                              flex: 1,
                              child: SearchViewBdhDigital(
                                searchController: searchController,
                                focusScopeNode: focusScopeNode,
                                clickedSeachBtn: clickedSeachBtn,
                                clickedClearBtn: clickedClearBtn,
                              )),
                        ],
                      ),
                    ),
                  ),

                  // 2.消息列表 + 已读 全部 row
                  Container(
                    width: 375.px,
                    height: 40.px,
                    margin: EdgeInsets.only(top: 10.px, bottom: 10.px),
                    padding: EdgeInsets.symmetric(horizontal: 16.px),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Text(
                              '消息列表',
                              style: TextStyle(
                                color: Colors.black,
                                fontSize: 14.px,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                return BrnDialogManager.showConfirmDialog(
                                    context,
                                    title: "清除所有未读消息",
                                    cancel: '取消',
                                    confirm: '确定',
                                    message: "该操作不可撤回，请确认是否清除", onConfirm: () {
                                  Navigator.of(context).pop();
                                  providerModel
                                      .changeAllUnReadMessageToRead()
                                      .then((res) {
                                    providerModel
                                        .handleClearAllBdhDigital()
                                        .then((res) {
                                      providerModel.clearData();
                                      providerModel.clearMessageCount();
                                      Future.delayed(const Duration(seconds: 3),
                                          () {
                                        providerModel
                                            .refreshCooperateAndNewsMessage();
                                      });
                                    });
                                  });
                                }, onCancel: () {
                                  Navigator.of(context).pop();
                                });
                              },
                              child: Container(
                                padding: EdgeInsets.only(left: 5.px),
                                child: Image.asset(
                                  width: 22.px,
                                  height: 22.px,
                                  ImageHelper.wrapAssets("group.png"),
                                ),
                              ),
                            ),
                          ],
                        ),
                        Row(
                          children: providerModel.readTabsList
                              .map((e) => getItem(e, providerModel))
                              .toList(),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    height: 40.px,
                    child: TabBar(
                      dividerColor: const Color.fromRGBO(226, 235, 231, 0.0),
                      labelColor: const Color.fromRGBO(0, 127, 255, 1),
                      labelPadding: EdgeInsets.only(left: 10.px, right: 10.px),
                      unselectedLabelColor: Colors.black,
                      labelStyle: TextStyle(
                          fontSize: 16.px,
                          fontWeight: FontWeight.w500,
                          color: const Color.fromRGBO(0, 127, 255, 1)),
                      unselectedLabelStyle: TextStyle(
                          fontSize: 16.px,
                          fontWeight: FontWeight.w400,
                          color: HexColor('#000000')),
                      isScrollable: false,
                      indicator: const TCUnderlineTabIndicator(
                          isRound: false,
                          indicatorBottom: 0,
                          indicatorWidth: 30,
                          borderSide: BorderSide(
                            width: 4,
                            color: Color.fromRGBO(0, 127, 255, 1),
                          )),
                      controller: _tabController,
                      tabs: providerModel.messageTabsList.map((model) {
                        return Stack(
                          clipBehavior: Clip.none,
                          children: [
                            TCTabScale(text: model.name),
                            // Positioned.fill(
                            //   child: TCTabScale(text: model.name),
                            // ),
                            getCountView(model, providerModel),
                          ],
                        );
                      }).toList(),
                      // tabs: messageTabsList.map((model) {
                      //   return Stack(
                      //     clipBehavior: Clip.none,
                      //     children: [
                      //       Positioned.fill(
                      //           child: TCTabScale(
                      //         text: model.name,
                      //       )),
                      //     ],
                      //   );
                      // }).toList(),
                      // onTap: (index) {
                      //   bool indexIsChanging =
                      //       _tabController!.indexIsChanging;
                      //   Logger().i(
                      //       '点击 切换tabbar----------------------$index-----$indexIsChanging');
                      //   if (!indexIsChanging) {
                      //   }
                      // },
                    ),
                  ),
                  //tabbarView
                  Expanded(
                      child: TabBarView(
                          controller: _tabController, children: pageViewList))
                ],
              ),
            ],
          );
        },
      ),
    );
  } //end build

  Widget getCountView(MessageBdhDigitalItemModel model,
      MessageBdhDigitalProvider providerModel) {
    int unReadCountCooperate = providerModel.unReadCountCooperate;
    int unReadCountNews = providerModel.unReadCountNews;
    int unReadSystemCount = providerModel.unReadSystemCount;
    int unReadNoticeCount = providerModel.unReadNoticeCount;
    if (model.index == 0) {
      return unReadCountCooperate == 0
          ? Container()
          : Positioned(
              top: -5.px,
              right: -10.px,
              child: Container(
                height: 18.px,
                // width: 20.px,
                padding: EdgeInsets.only(left: 5.px, right: 5.px),
                decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10.px)),
                child: Align(
                  alignment: Alignment.center,
                  child: Text(
                    '$unReadCountCooperate',
                    style: TextStyle(fontSize: 12.px, color: Colors.white),
                  ),
                ),
              ),
            );
    } else if (model.index == 1) {
      return Container();
      // return unReadCountNews == 0
      //     ? Container()
      //     : Positioned(
      //         top: -5.px,
      //         right: -10.px,
      //         child: Container(
      //           height: 18.px,
      //           // width: 20.px,
      //           padding: EdgeInsets.only(left: 5.px, right: 5.px),
      //           decoration: BoxDecoration(
      //               color: Colors.red,
      //               borderRadius: BorderRadius.circular(10.px)),
      //           child: Align(
      //             alignment: Alignment.center,
      //             child: Text(
      //               '$unReadCountNews',
      //               style: TextStyle(fontSize: 12.px, color: Colors.white),
      //             ),
      //           ),
      //         ),
      //       );
    } else if (model.index == 2) {
      return unReadNoticeCount == 0
          ? Container()
          : Positioned(
              top: -5.px,
              right: -10.px,
              child: Container(
                height: 18.px,
                // width: 20.px,
                padding: EdgeInsets.only(left: 5.px, right: 5.px),
                decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10.px)),
                child: Align(
                  alignment: Alignment.center,
                  child: Text(
                    '$unReadNoticeCount',
                    style: TextStyle(fontSize: 12.px, color: Colors.white),
                  ),
                ),
              ),
            );
    } else if (model.index == 3) {
      return unReadSystemCount == 0
          ? Container()
          : Positioned(
              top: -5.px,
              right: -10.px,
              child: Container(
                height: 18.px,
                // width: 20.px,
                padding: EdgeInsets.only(left: 5.px, right: 5.px),
                decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(10.px)),
                child: Align(
                  alignment: Alignment.center,
                  child: Text(
                    '$unReadSystemCount',
                    style: TextStyle(fontSize: 12.px, color: Colors.white),
                  ),
                ),
              ),
            );
    } else {
      return Container();
    }
  }
} //end state

class MessageBdhDigitalItemModel {
  int index;
  String name;
  bool isSelected;
  MessageBdhDigitalItemModel(
      {required this.name, required this.isSelected, required this.index});
}
