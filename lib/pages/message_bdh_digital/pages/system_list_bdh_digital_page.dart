import 'dart:core';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/model/notice_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/pages/system_ditail_page.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/provider/message_bdh_digital_provider.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/subview/message_bdh_digital_notice_cell_view.dart';
import 'package:bdh_smart_agric_app/pages/message_bdh_digital/subview/message_bdh_digital_system_cell_view.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

//业务消息
class SystemListBdhDigitalPage extends StatefulWidget {
  const SystemListBdhDigitalPage({super.key});

  @override
  State<SystemListBdhDigitalPage> createState() =>
      _SystemListBdhDigitalPageState();
}

class _SystemListBdhDigitalPageState extends State<SystemListBdhDigitalPage> {
  // final RefreshController _refreshController =
  //     RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    MessageBdhDigitalProvider tempProvider =
        Provider.of<MessageBdhDigitalProvider>(context, listen: false);
    // tempProvider.getSystemListBdhDigital(pageNum: 1);
    tempProvider.refreshNotice();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MessageBdhDigitalProvider>(
      builder: (context, messageProvider, child) {
        if (messageProvider.isLoading) {
          return const ViewStateBusyWidget();
        }
        if (messageProvider.showList!.isEmpty) {
          return const BdhEmptyView();
        }
        return SmartRefresher(
          enablePullDown: true,
          enablePullUp: true,
          onRefresh: messageProvider.refreshNotice,
          onLoading: messageProvider.loadMoreNotice,
          controller: messageProvider.refreshControllerSystem,
          child: ListView.separated(
            padding: EdgeInsets.only(top: 10.px, bottom: 20.px),
            shrinkWrap: true,
            scrollDirection: Axis.vertical,
            itemCount: (messageProvider.showList ?? []).length,
            itemBuilder: (BuildContext context, int index) {
              NoticeRecordBdhDigitalModel modelItem =
                  (messageProvider.showList ?? [])[index];
              return MessageBdhDigitalSystemCellView(
                //   MessageBdhDigitalNoticeCellView
                msgTabIndex: 3,
                modelItem: modelItem,
                clickeNoticeCellItem: (e) {
                  Logger().i('-----$e');
                  Navigator.push(
                      context,
                      CupertinoPageRoute(
                          builder: (_) => SystemDitailPage(
                                messageId: modelItem.id ?? 0,
                                tabType: 3,
                                modelItem: modelItem,
                              ))).then((res) {
                    messageProvider.refreshNotice();
                    messageProvider.getUnreadSystemBdhDigital();
                  });
                },
              );
            },
            separatorBuilder: (BuildContext context, int index) {
              return Container(
                color: Colors.transparent,
                height: 10.px,
              );
            },
          ),
        );
      },
    );
  }
}
