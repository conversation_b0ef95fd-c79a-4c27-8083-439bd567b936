import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class AuthorizationLatter extends StatefulWidget {
  final LandBaseInfo info;
  const AuthorizationLatter({super.key, required this.info});

  @override
  State<AuthorizationLatter> createState() => _AuthorizationLatterState();
}

class _AuthorizationLatterState extends State<AuthorizationLatter>
    with AutoDisposeStateMixin {
  bool canConfirm = false;

  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();

    _scrollController = useScrollController(ScrollController());
    _scrollController.addListener(() {
      if (_scrollController.position.pixels ==
          _scrollController.position.maxScrollExtent) {
        setState(() {
          canConfirm = true;
        });
      }
    });
  }

  Widget _widgetContent() {
    return SingleChildScrollView(
        controller: _scrollController,
        child: Column(
          children: [
            Text.rich(TextSpan(children: [
              TextSpan(
                  text: "重要提示\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text:
                      "请您在勾选《数字北大荒综合授权书》（以下简称授权书）前，充分理解授权书的条款和内容。您勾选“同意授权”的选项、点击授权书所在页面按钮并进入下一个页面的行为即视为您已经充分理解授权书的内容，并同意接受授权书的约束，授权书即完成在线签署并生效。\n\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text: "授权方：",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text: "${widget.info.name ?? ""} \n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
              TextSpan(
                  text: "身份证号：",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text: "${widget.info.idNumber ?? ""} \n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
              TextSpan(
                  text: "家庭住址：",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text: "${widget.info.address ?? ""} \n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
              TextSpan(
                  text: "工作单位：",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text: "${widget.info.organizationName ?? ""} \n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
              TextSpan(
                  text: "被授权方：",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text: "北大荒信息有限公司 \n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
              TextSpan(
                  text: "公司住所：",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text: "哈尔滨市松北区智谷大街288号深圳(哈尔滨)产业园区科创总部1号楼 \n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "鉴    于 \n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text:
                      "授权方已注册成为被授权方运营的“数字北大荒”农户服务平台的用户。授权方为正常使用“数字北大荒”农户服务平台的服务内容，现向被授权方出具授权书。授权方承诺所注册提供的信息均为真实、有效的信息，如涉及虚假信息，授权方同意被授权方暂停和终止服务，由此产生的责任由授权方承担。\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "授权范围\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "授权方在授权书所列授权期限内，授权被授权方实施如下行为：\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text:
                      "一、收集并存储授权方的姓名、居民身份证登载信息、户籍登载信息、职工身份、职工编号、银行卡号、电话号码、家庭住址、电子邮箱、本人照片、人脸图像，并将上述信息记录在被授权方建立的数据库中。\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text:
                      "二、收集并存储与授权方相关的由农场提供的《土地承包计划》、授权方与农场签署的《土地承包合同》的电子文档及登载信息，包括但不限于：承包方姓名、身份证号码、所承包农用地的基本情况、承包形式、承包期限、承包面积、承包费、地宗号、种植作物、签名（盖章）样式，并将上述信息记录在被授权方建立的数据库中。\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text:
                      "三、如授权方要求使用“数字北大荒”农户服务平台所提供的金融服务（包括：农业种植贷款、农业互助保险），则授权方同意并向被授权方授权：将本授权书“授权范围”项下所登载的一、二中所列信息提供给金融服务的提供方，并将上述信息记录在被授权方建立的数据库中。金融服务的提供方包括：\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "1.中国银行股份有限公司黑龙江省分行；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "2.中国农业银行股份有限公司黑龙江省分行；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "3.中国工商银行股份有限公司黑龙江省分行；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "4.中国建设银行股份有限公司黑龙江省分行；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "5.交通银行股份有限公司黑龙江省分行；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "6.中国邮政储蓄银行股份有限公司黑龙江省分行；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "7.上海浦东发展银行股份有限公司哈尔滨分行；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "8.中信银行股份有限公司哈尔滨分行；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "9.哈尔滨银行股份有限公司；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "10.中国民生银行股份有限公司哈尔滨分行；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "11.龙江银行股份有限公司；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "12.锦州银行股份有限公司；\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "13.黑龙江省农村信用社联合社；\n\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "授权期限\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "本授权书的授权期限为：自授权书生效时起，至贷款结清时止。\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "授权方同意：授权期限届满后，被授权方为统计和运营需要，在保障授权方信息安全的前提下继续保留授权方信息。\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text: "特别提示\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text:
                      "1.本《数字北大荒综合授权书》中所列事项是授权方的真实意思表示，授权方已全部阅读授权书内容、充分理解授权书含义、自愿实施授权并明确知悉授权的法律后果。\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              WidgetSpan(child: SizedBox(width: 24.px)),
              TextSpan(
                  text:
                      "2.如被授权方系依据本授权书所列授权范围或依照法律、法规、政策、政府（机关）行政及司法命令等、基于提供“数字北大荒”农户服务平台相关服务内容之目的等不可归责于被授权方的情形披露、使用、许可第三方使用该等信息，授权方同意享有/承担该等行为所产生的全部后果。授权方承诺不因被授权方在此种情形下披露、使用、许可第三方使用该等信息而向被授权方提出任何形式的权利主张。\n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text: "\n授权方：",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500)),
              TextSpan(
                  text: widget.info.name ?? "",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600)),
            ])),
            Row(
              children: [
                Expanded(
                    child: Text(
                  "$dateStr \n",
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600),
                  textAlign: TextAlign.end,
                ))
              ],
            )
          ],
        ));
  }

  Widget _widgetTitle() {
    return Padding(
      padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "数字北大荒综合授权书",
            strutStyle: StrutStyle(fontSize: 16.px, forceStrutHeight: true),
            style: TextStyle(
                color: const Color.fromRGBO(44, 44, 52, 1),
                fontSize: 16.px,
                fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  Widget _widgetButtons() {
    return Padding(
      padding: EdgeInsets.only(top: 10.px),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 12.px,
          ),
          Expanded(
              child: BdhTextButton(
            text: "同意",
            width: 100.px,
            height: 40.px,
            textSize: 16.px,
            borderRadius: BorderRadius.all(Radius.circular(22.px)),
            backgroundColor: const Color.fromRGBO(30, 192, 106, 1),
            disableBackgroundColor: const Color.fromRGBO(0, 0, 0, 0.2),
            pressedBackgroundColor: const Color.fromRGBO(16, 164, 85, 1),
            foregroundColor: Colors.white,
            disableForegroundColor: Colors.white,
            pressedForegroundColor: Colors.white,
            onPressed: canConfirm
                ? () {
                    Navigator.maybePop(context, true);
                  }
                : null,
          )),
          SizedBox(
            width: 30.px,
          ),
          Expanded(
              child: BdhTextButton(
            text: "不同意",
            width: 100.px,
            height: 40.px,
            textSize: 16.px,
            borderRadius: BorderRadius.all(Radius.circular(22.px)),
            side: BorderSide(
              width: 1.px,
              color: const Color.fromRGBO(30, 192, 106, 1),
            ),
            pressedSide: BorderSide(
              width: 1.px,
              color: const Color.fromRGBO(30, 192, 106, 1),
            ),
            disableSide: BorderSide(
              width: 1.px,
              color: const Color.fromRGBO(1, 1, 1, 0.1),
            ),
            onPressed: () {
              Navigator.maybePop(context, false);
            },
          )),
          SizedBox(
            width: 12.px,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheet(
        enableDrag: false,
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.px)),
        ),
        onClosing: () {},
        builder: (context) {
          return Container(
              constraints: BoxConstraints(maxHeight: 524.px),
              padding:
                  EdgeInsets.only(left: 15.px, right: 15.px, bottom: 10.px),
              child: Column(children: [
                _widgetTitle(),
                Expanded(child: _widgetContent()),
                _widgetButtons()
              ]));
        });
  }

  String get dateStr {
    return DateFormat("yyyy年MM月dd日").format(DateTime.now());
  }
}
