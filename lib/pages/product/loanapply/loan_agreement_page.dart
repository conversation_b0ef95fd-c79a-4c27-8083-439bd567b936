import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

//用户结清协议
class LoanAgreementPage extends StatelessWidget {
  const LoanAgreementPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        appBar: AppBar(
          centerTitle: true,
          elevation: 0,
          title: const Text("用户结清协议"),
        ),
        body: SafeArea(
            top: false,
            child: LayoutBuilder(builder: (context, constraints) {
              return SingleChildScrollView(
                  padding: EdgeInsets.all(12.px),
                  child: SizedBox(
                      width: constraints.maxWidth - 24.px,
                      child: Column(
                        children: [
                          Text.rich(TextSpan(children: [
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "1. 登录数字北大荒平台（简称“农服平台”）用户系具备完全民事行为能力的自然人，因承包农场土地具备资金需求，与农服平台入驻的金融机构建立了客户关系，并从金融机构获取贷款预授信、授信或贷款等金融服务。\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "2. 农服平台是金融机构和用户技术服务提供方，为用户通过“农服系统”进行线上贷款相关业务提供技术服务。\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "3. 用户根据《中华人民共和国民法典》及相关法律、法规的规定，就便于用户与金融机构在农服平台解除双方之间的客户关系锁定事项，遵守本须知。\n\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text: "第一条 操作流程\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w600)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "1. 用户已取得金融机构贷款额度预授信、授信、或贷款后，用户与金融机构建立客户关系，如用户实际未贷款或已足额清偿金融机构贷款，则用户有权通过农服平台APP向金融机构申请在农服平台上解锁与金融机构的客户关系。\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "2. 用户提交申请后，金融机构将在农服系统APP中收到用户上述申请，金融机构在核实用户申请意愿且符合在农服平台上解锁条件后，通过农服平台APP审核功能，金融机构与用户双方在农服平台上解除客户关系锁定。\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "3. 用户已与某家金融机构建立客户关系后，提交解锁申请的同时选择了其他目标金融机构，由目标金融机构上传用户与在先金融机构的还款完成证明和额度结清证明，上传完成后，在先金融机构需要对该用户申请进行审核。审核完成后，农服平台有权利将该用户申请贷款的相关信息直接推送给目标金融机构，用户无需在农服平台上重新向目标金融机构申请贷款，即可与目标金融机构建立贷款申请关系，用户信息将被目标金融机构锁定。\n\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text: "第二条 用户的权利、义务\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w600)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "1. 用户对于其向金融机构提供的贷款偿还信息、贷款额度注销申请等信息负有确保其真实性、准确性、完整性的义务；不得存在虚构事实、隐瞒真相等行为，否则应承担因此给农服平台运营方、金融机构造成的直接和间接损失。\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "2. 用户提交申请后，金融机构有义务在收到用户申请的48小时内完成审核，如审核无误，应及时予以通过。\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "3. 对于用户的申请，金融机构自行承担发放、回收贷款的风险，对于申请客户关系释放的用户还款情况、贷款额度注销申请负有独立的审核义务，并自行承担因其独立判断而产生的法律后果。如用户有疑问可以直接联系金融机构，或通过农服平台客服渠道协助解决疑问。\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "4. 用户有权利在某家金融机构审核完成前随时撤销解锁申请，则用户还保持与在先金融机构的锁定关系。如果某家金融机构审核成功，用户拟向在先金融机构申请贷款，需重新发起贷款申请流程。\n\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text: "第三条 用户责任\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w600)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "1. 用户应确保其提供给目标金融机构及其他需提供给农服平台的信息的真实、准确、完整性，否则应承担因此给守约方造成的直接、间接损失。\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "2. 用户应确保其严格履行本须知项下的义务，如发生违反，应承担因此给相关方造成的直接、间接损失。\n\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text: "第四条 争议解决\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w600)),
                            WidgetSpan(child: SizedBox(width: 24.px)),
                            TextSpan(
                                text:
                                    "用户应遵循本须知内容，与相关主体平等协商解决业务办理过程中发生的争议；如协商不成，任何相关主体均有权向哈尔滨仲裁委员会申请仲裁。\n",
                                style: TextStyle(
                                    color: const Color.fromRGBO(44, 44, 52, 1),
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w500)),
                          ])),
                          _widgetConfirmButton(context)
                        ],
                      )));
            })));
  }

  Widget _widgetConfirmButton(BuildContext context) {
    return BdhTextButton(
      width: 351.px,
      height: 40.px,
      text: '阅读并确定',
      textFontWeight: FontWeight.w500,
      textSize: 15.px,
      borderRadius: BorderRadius.all(Radius.circular(6.px)),
      backgroundColor: const Color.fromRGBO(2, 139, 93, 1),
      disableBackgroundColor: const Color.fromARGB(255, 178, 174, 174),
      pressedBackgroundColor: const Color.fromRGBO(8, 168, 105, 1),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: () {
        Navigator.of(context).pop(true);
      },
    );
  }
}
