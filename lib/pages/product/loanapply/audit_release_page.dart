import 'package:bdh_smart_agric_app/pages/product/loanapply/data_analyst_page.dart';
import 'package:bdh_smart_agric_app/pages/product/loanapply/unlock_audit_page.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../utils/native_util.dart';

//农贷助手
class AuditReleasePage extends StatefulWidget {
  const AuditReleasePage({super.key});

  @override
  State<AuditReleasePage> createState() => _AuditReleasePageState();
}

class _AuditReleasePageState extends State<AuditReleasePage> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Image(image: AssetImage(ImageHelper.wrapAssets("audit_release_bg.png")),),
          Positioned(
              top: 44.px,
              left: 10.px,
              child: const BackButton(color: Colors.black,)
          ),
          Column(
            children: [
              SizedBox(height: 225.px,),
              Container(
                margin: EdgeInsets.only(left:40.px, right: 40.px),
                child: Wrap(
                  direction: Axis.horizontal,
                  spacing: 30.px,
                  runSpacing: 30.px,
                  children: [
                    GestureDetector(
                      onTap: () {
                        NativeUtil.openUni({"path": "/pages/centerInfo/releaseAudit/unlockReview/unlockReview"});
                      },
                      child: Container(
                        width: 48.px,
                        height: 82.px,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image(image: AssetImage(ImageHelper.wrapAssets('info_collection.png')), width: 40.px, height: 40.px,),
                            SizedBox(height: 8.px,),
                            Text("结清审核", style: TextStyle(fontSize: 12.px, color: Colors.black, fontWeight: FontWeight.w400),textAlign: TextAlign.center,),
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        NativeUtil.openUni({"path": "/pages/centerInfo/releaseAudit/proofSettlement/proofSettlement"});
                      },
                      child: Container(
                        width: 48.px,
                        height: 82.px,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image(image: AssetImage(ImageHelper.wrapAssets('apply_record.png')), width: 40.px, height: 40.px,),
                            SizedBox(height: 8.px,),
                            Text("申请记录", style: TextStyle(fontSize: 12.px, color: Colors.black, fontWeight: FontWeight.w400), textAlign: TextAlign.center,),
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                          return UnlockAuditPage();
                        }));
                      },
                      child: Container(
                        width: 48.px,
                        height: 82.px,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image(image: AssetImage(ImageHelper.wrapAssets('intercept_data.png')), width: 40.px, height: 40.px,),
                            SizedBox(height: 8.px,),
                            Text("拦截数据恢复", style: TextStyle(fontSize: 12.px, color: Colors.black, fontWeight: FontWeight.w400), textAlign: TextAlign.center,),
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
                          return DataAnalystPage();
                        }));
                      },
                      child: Container(
                        width: 48.px,
                        height: 82.px,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image(image: AssetImage(ImageHelper.wrapAssets('pre_warning_data_analyst.png')), width: 40.px, height: 40.px,),
                            SizedBox(height: 8.px,),
                            Text("预警数据分析", style: TextStyle(fontSize: 12.px, color: Colors.black, fontWeight: FontWeight.w400), textAlign: TextAlign.center,),
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        NativeUtil.openUni({"path": "/pages/amHouseKeep/loansscale/index"});
                      },
                      child: Container(
                        width: 48.px,
                        height: 82.px,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image(image: AssetImage(ImageHelper.wrapAssets('agric_machine_collect.png')), width: 40.px, height: 40.px,),
                            SizedBox(height: 8.px,),
                            Text("农机贷采集", style: TextStyle(fontSize: 12.px, color: Colors.black, fontWeight: FontWeight.w400),textAlign: TextAlign.center,),
                          ],
                        ),
                      ),
                    ),
                    GestureDetector(
                      onTap: () {
                        NativeUtil.openUni({"path": "/pages/amHouseKeep/loansscale/recordlist"});
                      },
                      child: Container(
                        width: 48.px,
                        height: 82.px,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Image(image: AssetImage(ImageHelper.wrapAssets('agric_machine_loan_record.png')), width: 40.px, height: 40.px,),
                            SizedBox(height: 8.px,),
                            Text("农机贷记录", style: TextStyle(fontSize: 12.px, color: Colors.black, fontWeight: FontWeight.w400),textAlign: TextAlign.center,),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
