import 'dart:io';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/land_base_info_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/product/loanapply/video_player_page.dart';
import 'package:bdh_smart_agric_app/pages/product/loanapply/widget/authorization_latter.dart';
import 'package:bdh_smart_agric_app/pages/product/realname/fdd_contract_page.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/land_intradomain_sign_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:oktoast/oktoast.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../home/<USER>/live_web_page.dart';
import 'loan_apply_bank_list_page.dart';
import 'loan_unlock_apply_page.dart';
import 'loan_unlock_record_page.dart';
import 'model/area_and_pay.dart';
import 'model/loan_record.dart';
import 'model/release_apply_verify.dart';
import 'pufa/constants.dart';
import 'pufa/pufa_main_page.dart';
import 'request/loan_apply_service.dart';
import 'widget/loan_apply_item_widget.dart';

//农贷助手
class LoanApplyPage extends StatefulWidget {
  const LoanApplyPage({super.key});

  @override
  State<LoanApplyPage> createState() => _LoanApplyPageState();
}

class _LoanApplyPageState extends State<LoanApplyPage>
    with AutoDisposeStateMixin {
  late _Controller controller;

  @override
  void initState() {
    super.initState();

    controller = useChangeNotifier(_Controller(context))
      ..addListener(() {
        setState(() {});
      })
      ..getResidentInfo();
  }

  @override
  Widget build(BuildContext context) {
    if (controller.isLoading) {
      return _widgetLoading();
    }
    return _widgetBody();
  }

  Widget _widgetLoading() {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: LayoutBuilder(builder: (context, constraints) {
            return Stack(
                alignment: AlignmentDirectional.topCenter,
                fit: StackFit.expand,
                children: [
                  _widgetBackground(),
                  _widgetAppBar(),
                  const Positioned.fill(
                      child: Center(
                    child: ViewStateBusyWidget(),
                  )),
                ]);
          })),
    );
  }

  Widget _widgetAppBar() {
    return AppBar(
      centerTitle: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: const BackButton(
        color: Colors.white,
      ),
    );
  }

  Widget _widgetBackground() {
    return SizedBox(
        width: 375.px,
        child: Stack(
          children: [
            SvgPicture.asset(
                width: 375.px,
                height: 322.5.px,
                ImageHelper.wrapAssets("finance/bkg_loan_apply.svg")),
            Positioned(
                top: 321.px,
                left: 0,
                right: 0,
                child: SizedBox(
                  height: 10.px,
                  child:
                      const ColoredBox(color: Color.fromRGBO(243, 245, 249, 1)),
                )),
            Positioned(
                top: 82.px,
                left: 175.5.px,
                child: Image.asset(
                    width: 20.5.px,
                    height: 23.5.px,
                    ImageHelper.wrapAssets("finance/bkg_finance_star.png"))),
            Positioned(
                top: 44.px,
                left: 213.px,
                child: Image.asset(
                    width: 25.px,
                    height: 27.px,
                    ImageHelper.wrapAssets("finance/bkg_finance_star2.png"))),
            Positioned(
                top: 69.5.px,
                left: 205.5.px,
                child: Image.asset(
                    width: 149.5.px,
                    height: 131.5.px,
                    ImageHelper.wrapAssets("finance/bkg_finance_icon.png"))),
          ],
        ));
  }

  Widget _widgetSubPanel() {
    return GestureDetector(
        onTap: controller.recordFun,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Text("释放申请记录",
                style: TextStyle(
                    fontSize: 13.px,
                    color: Colors.white,
                    fontWeight: FontWeight.w500)),
            Icon(Icons.arrow_forward_ios, size: 13.px, color: Colors.white)
          ],
        ));
  }

  Widget _widgetBody() {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: LayoutBuilder(builder: (context, constraints) {
            return Stack(
                alignment: AlignmentDirectional.topCenter,
                fit: StackFit.expand,
                children: [
                  _widgetBackground(),
                  _widgetAppBar(),
                  Positioned(
                      top: 132.px, left: 30.px, child: _widgetSubPanel()),
                  Positioned.fill(top: 155.5.px, child: _widgetList()),
                  Positioned(bottom: 12.px, child: _widgetApplyButton())
                ]);
          })),
    );
  }

  Widget _widgetNoMess() {
    return Text(
      "暂无贷款信息，不能申请贷款",
      style: TextStyle(
          color: Colors.red, fontSize: 14.px, fontWeight: FontWeight.w600),
    );
  }

  Widget _widgetList() {
    return Container(
      width: 375.px,
      padding: EdgeInsets.only(left: 12.px, right: 12.px),
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
              child: Column(
            children: [
              if (controller.showMainPanel) ...[
                _widgetMainPanel(),
                SizedBox(
                  height: 12.px,
                ),
              ],
              if (controller.showNoMess) ...[
                _widgetNoMess(),
                SizedBox(
                  height: 12.px,
                ),
              ],
              if (controller.showLoanPanel) ...[
                _widgetLoanPanel(),
                SizedBox(
                  height: 12.px,
                ),
              ],
              if (!controller.isEmpty) ...[
                _widgetListTitle(),
                SizedBox(
                  height: 12.px,
                ),
              ]
            ],
          )),
          if (controller.isEmpty)
            const SliverFillRemaining(
              hasScrollBody: false,
              child: Center(
                child: BdhEmptyView(
                  tipInfo: "未查到申请记录",
                ),
              ),
            ),
          if (!controller.isEmpty) ...[
            SliverList.separated(
                itemCount: controller.itemLength,
                itemBuilder: (context, index) {
                  return LoanApplyItemWidget(
                    loanStatusDict: controller.loanStatusDict ?? [],
                    releaseAuditStatusDict:
                        controller.releaseAuditStatusDict ?? [],
                    item: controller.loadRecordItems![index],
                    onPressed: controller.handlePufa,
                    onPressedUnlock: controller.handleUnlock,
                    onPressedOpenApp: controller.handleOpenBank,
                    onPressedOpenVideo: controller.handleOpenVideo,
                  );
                },
                separatorBuilder: (BuildContext context, int index) {
                  return SizedBox(
                    height: 12.px,
                  );
                }),
            SliverToBoxAdapter(child: _widgetBottom())
          ]
        ],
      ),
    );
  }

  Widget _widgetBottom() {
    return Column(
      children: [
        SizedBox(
          height: 30.px,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
                height: 1.px,
                width: 85.px,
                color: const Color.fromRGBO(0, 0, 0, 0.15)),
            SizedBox(
              width: 16.px,
            ),
            Text("产品说明",
                style: TextStyle(
                    fontSize: 14.px,
                    color: const Color.fromRGBO(44, 44, 52, 0.7),
                    fontWeight: FontWeight.w500)),
            SizedBox(
              width: 16.px,
            ),
            Container(
                height: 1.px,
                width: 85.px,
                color: const Color.fromRGBO(0, 0, 0, 0.15))
          ],
        ),
        SizedBox(
          height: 25.px,
        ),
        Text(tips,
            style: TextStyle(
                fontSize: 11.9.px,
                color: const Color.fromRGBO(44, 44, 52, 0.4),
                fontWeight: FontWeight.w500)),
        SizedBox(
          height: 44.px,
        ),
      ],
    );
  }

  String get tips {
    var productIntroduceItems = controller.productIntroduceItems ?? [];
    String s = "";
    for (int i = 0; i < productIntroduceItems.length; i++) {
      s += "${i + 1}.${productIntroduceItems[i]}\n";
    }
    return s;
  }

  Widget _widgetApplyButton() {
    return BdhTextButton(
      width: 351.px,
      height: 40.px,
      text: '申请贷款',
      textFontWeight: FontWeight.w500,
      textSize: 15.px,
      borderRadius: BorderRadius.all(Radius.circular(6.px)),
      backgroundColor: const Color.fromRGBO(2, 139, 93, 1),
      disableBackgroundColor: const Color.fromARGB(255, 215, 212, 212),
      pressedBackgroundColor: const Color.fromRGBO(8, 168, 105, 1),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: controller.disableButtonEnable ? null : controller.apply,
    );
  }

  Widget _widgetListTitle() {
    return Row(
      children: [
        Text(
          "申请记录",
          style: TextStyle(
              fontSize: 15.px,
              color: Colors.black,
              fontWeight: FontWeight.w600),
        ),
        const Spacer(),
        if (controller.showAllButton)
          GestureDetector(
            onTap: controller.triggerShowAll,
            child: Text(
              controller._showAll ? "收起" : "查看更多",
              style: TextStyle(
                fontSize: 12.px,
                color: const Color.fromRGBO(44, 44, 52, 0.6),
              ),
            ),
          )
      ],
    );
  }

  Widget _widgetMainPanel() {
    return Container(
      width: 351.px,
      padding: EdgeInsets.all(15.px),
      decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [
              Color.fromRGBO(193, 250, 222, 0.8),
              Color.fromRGBO(255, 255, 255, 1),
            ],
            begin: Alignment.topCenter,
            end: Alignment(0, 0),
          ),
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px)),
          border: Border.all(width: 3.px, color: Colors.white)),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                "贷款年度",
                style: TextStyle(
                    fontSize: 18.px,
                    color: Colors.black,
                    fontFamily: "AlimamaShuHeiTi-Bold"),
              ),
              SizedBox(width: 5.px),
              Text(
                "${controller.areaAndPay?.contractorYear ?? ""}",
                style: TextStyle(
                    fontSize: 20.px,
                    color: const Color.fromRGBO(3, 180, 109, 1),
                    fontFamily: "AlimamaShuHeiTi-Bold"),
              ),
              SizedBox(width: 16.px),
            ],
          ),
          SizedBox(
            height: 14.px,
          ),
          Row(
            children: [
              Expanded(
                  child: _widgetMainPanelItem(
                title: '预承包面积',
                subTitle1: '水田面积(亩)',
                subTitle2: '旱田面积(亩)',
                value1: "${controller.areaAndPay?.paddyArea ?? 0}",
                value2: "${controller.areaAndPay?.dryArea ?? 0}",
              )),
              SizedBox(
                width: 9.px,
              ),
              Expanded(
                  child: _widgetMainPanelItem(
                title: '应缴金额',
                subTitle1: '土地承包费(元)',
                subTitle2: '其他收费(元)',
                value1: "${controller.areaAndPay?.landContractFee ?? 0}",
                value2: "${controller.areaAndPay?.otherFee ?? 0}",
              )),
              SizedBox(
                width: 9.px,
              ),
              Expanded(
                  child: _widgetMainPanelItem(
                      title: '欠费金额',
                      subTitle1: '土地承包费(元)',
                      subTitle2: '其他收费(元)',
                      value1:
                          "${controller.areaAndPay?.landContractFeeOwe ?? 0}",
                      value2: "${controller.areaAndPay?.otherFeeOwe ?? 0}")),
            ],
          )
        ],
      ),
    );
  }

  Widget _widgetLoanPanel() {
    return Container(
      width: 351.px,
      padding: EdgeInsets.all(15.px),
      decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [
              Color.fromRGBO(193, 250, 222, 0.8),
              Color.fromRGBO(255, 255, 255, 1),
            ],
            begin: Alignment.topCenter,
            end: Alignment(0, 0),
          ),
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px)),
          border: Border.all(width: 3.px, color: Colors.white)),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                "贷款成功",
                style: TextStyle(
                    fontSize: 18.px,
                    color: Colors.black,
                    fontFamily: "AlimamaShuHeiTi-Bold"),
              ),
            ],
          ),
          SizedBox(
            height: 4.px,
          ),
          Center(
              child: Text("${controller.areaAndPay?.grantMoney ?? "0"}",
                  style: TextStyle(
                    fontSize: 40.px,
                    color: const Color.fromRGBO(3, 180, 109, 1),
                  ))),
          Center(
              child: Text("放款金额(元)",
                  style: TextStyle(
                    fontSize: 14.px,
                    color: Colors.black,
                  ))),
          SizedBox(
            height: 14.px,
          ),
          Row(
            children: [
              Text("申请时间:",
                  style: TextStyle(
                    fontSize: 14.px,
                    color: Colors.black,
                  )),
              Text("${controller.areaAndPay?.applyTime ?? ""}",
                  style: TextStyle(
                    fontSize: 14.px,
                    color: Colors.black,
                  )),
            ],
          ),
          SizedBox(
            height: 4.px,
          ),
          Row(
            children: [
              Text("审批时间:",
                  style: TextStyle(
                    fontSize: 14.px,
                    color: Colors.black,
                  )),
              Text("${controller.areaAndPay?.approveTime ?? ""}",
                  style: TextStyle(
                    fontSize: 14.px,
                    color: Colors.black,
                  )),
            ],
          )
        ],
      ),
    );
  }

  Widget _widgetMainPanelItem({
    required String title,
    required String subTitle1,
    required String subTitle2,
    required String value1,
    required String value2,
  }) {
    return Container(
      width: 101.px,
      height: 148.px,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color.fromRGBO(228, 248, 242, 1),
            Color.fromRGBO(243, 245, 249, 0.5),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.all(Radius.circular(8.px)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 101.px,
            height: 24.px,
            child: Stack(
              children: [
                Positioned(
                    top: 0,
                    left: 9.px,
                    right: 9.px,
                    child: SvgPicture.asset(
                        fit: BoxFit.fitWidth,
                        width: 83.5.px,
                        height: 24.px,
                        ImageHelper.wrapAssets(
                            "finance/bkg_main_panel_item_title.svg"))),
                Center(
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 12.px,
                      color: Colors.white,
                    ),
                  ),
                )
              ],
            ),
          ),
          SizedBox(
            height: 12.px,
          ),
          Row(
            children: [
              SizedBox(
                width: 12.px,
              ),
              Expanded(
                child: Text(
                  subTitle1,
                  style: TextStyle(
                      fontSize: 12.px,
                      color: const Color.fromRGBO(44, 44, 52, 0.7),
                      fontWeight: FontWeight.w400),
                ),
              ),
            ],
          ),
          Row(
            children: [
              SizedBox(
                width: 12.px,
              ),
              Expanded(
                  child: Text(
                value1,
                style: TextStyle(
                    fontSize: 19.px,
                    color: const Color.fromRGBO(44, 44, 52, 1),
                    fontFamily: "BEBAS",
                    fontWeight: FontWeight.w500),
              )),
            ],
          ),
          SizedBox(
            height: 10.px,
          ),
          Row(
            children: [
              SizedBox(
                width: 12.px,
              ),
              Expanded(
                  child: Text(
                subTitle2,
                style: TextStyle(
                    fontSize: 12.px,
                    color: const Color.fromRGBO(44, 44, 52, 0.7),
                    fontWeight: FontWeight.w400),
              )),
            ],
          ),
          Row(
            children: [
              SizedBox(
                width: 12.px,
              ),
              Expanded(
                  child: Text(
                value2,
                style: TextStyle(
                    fontSize: 19.px,
                    color: const Color.fromRGBO(44, 44, 52, 1),
                    fontFamily: "BEBAS",
                    fontWeight: FontWeight.w500),
              )),
              SizedBox(
                width: 12.px,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _Controller extends AutoDisposeChangeNotifier {
  @override
  final BuildContext context;

  LoadingStatus loadingStatus = LoadingStatus.init;

  bool get isLoading =>
      loadingStatus == LoadingStatus.loading ||
      loadingStatus == LoadingStatus.init;

  _Controller(this.context);

  void showTips() {
    showModalBottomSheet<bool>(
        context: context,
        useSafeArea: true,
        isScrollControlled: true,
        isDismissible: false,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: AuthorizationLatter(
              info: residentInfo!,
            ),
          );
        }).then((result) {
      if (!context.mounted) {
        return;
      }
      if (result == true) {
        saveUserAuth();
      } else {
        Navigator.maybePop(context);
      }
    });
  }

  void saveUserAuth() {
    var data = {
      "organizationNo": residentInfo?.organizationNo,
      "organizationName": residentInfo?.organizationName,
      "orgCode": null,
      "orgName": null,
      "manageId": null,
      "manageName": null,
      "workstationId": null,
      "workStationName": null,
      "contractorIdCard": residentInfo?.idNumber, //身份证
      "contractorId": residentInfo?.farmerId, //用户 id
      "phoneNumber": residentInfo?.phone1, //手机号
      "contractorName": residentInfo?.name, //姓名
    };
    LoanApplyService()
        .saveUserAuth(data, cancelToken: createCancelToken())
        .then((result) {
      if (result.success == true) {
        loadData();
      }
    }).onError(handleError);
  }

  LandBaseInfo? residentInfo;

  void getResidentInfo() {
    //查询实名认证信息
    IntradomainSignService.getResidentInfo().then((result) {
      if (result.success == true) {
        residentInfo = result.data;
        LoanApplyService().verifyUserAuth({"idCardNo": residentInfo?.idNumber},
            cancelToken: createCancelToken()).then((result) {
          if (result.success == true) {
            //0 是成功，-2是还没验证
            if (result.data == -2) {
              showTips();
            } else {
              loadData();
            }
          }
        }).onError(handleError);
      } else {
        Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
          return const FddContractPage();
          // return const RealNameArtificialAuthPage();
        }));
      }
    }).onError(handleError);
  }

  List<DictNode>? loanStatusDict;
  List<DictNode>? releaseAuditStatusDict;

  AreaAndPay? areaAndPay;

  List<LoadRecord>? loadRecordItems;
  List<String>? productIntroduceItems;

  bool pufaApply = false;

  bool get isEmpty => loadRecordItems?.isEmpty ?? true;

  bool _showAll = false;

  bool get showAll => _showAll;

  set showAll(bool b) {
    _showAll = b;
    notifyListeners();
  }

  void triggerShowAll() {
    showAll = !_showAll;
  }

  bool get showMainPanel {
    return areaAndPay?.loanStatus != 4;
  }

  bool get showLoanPanel {
    return areaAndPay?.loanStatus == 4;
  }

  bool get showAllButton {
    if ((loadRecordItems?.length ?? 0) > 4) {
      return true;
    }
    return false;
  }

  bool get showNoMess {
    if (showLoanPanel) {
      return false;
    }
    return (areaAndPay?.paddyArea == 0 &&
        areaAndPay?.dryArea == 0 &&
        areaAndPay?.landContractFee == 0);
  }

  bool get disableButtonEnable {
    if (areaAndPay == null || pufaApply) {
      return true;
    }
    return (areaAndPay?.paddyArea == 0 && areaAndPay?.dryArea == 0) ||
        areaAndPay?.isLock == 1 ||
        pufaApply;
  }

  int get itemLength {
    if (_showAll) return loadRecordItems?.length ?? 0;
    if ((loadRecordItems?.length ?? 0) <= 4) {
      return loadRecordItems?.length ?? 0;
    }
    return 4;
  }

  void pufaCheck() {
    LoanApplyService()
        .crdStatusQry(cancelToken: createCancelToken())
        .then((result) {
      if (result.success == true && result.data != null) {
        loadRecordItems ??= [];
        Map<String, Object?> r = {};
        r.addAll(result.data ?? {});
        var loanStatus = 1;
        var crdStatus = result.data["crdStatus"];

        if (crdStatus == "0") {
          //  未结清
          loanStatus = 1;
        } else if (crdStatus == "1") {
          //  已结清
          loanStatus = 5;
        }

        r["bankName"] = '上海浦东发展银行';
        r["year"] = result.data["crdLoadYear"];
        r["applyTime"] = result.data["crdLoadYear"];
        r["loanStatus"] = loanStatus;
        r["bankCode"] = 1;
        pufaApply = (crdStatus == "0");
        loadRecordItems?.add(LoadRecord.fromJson(r));
        loadingStatus = LoadingStatus.success;
        notifyListeners();
      } else {
        loadingStatus = LoadingStatus.success;
        notifyListeners();
      }
    }).onError(handleError);
  }

  void getLoadRecord() {
    loadingStatus = LoadingStatus.loading;
    notifyListeners();
    var orgCode = residentInfo?.organizationNo;
    var idCard = residentInfo?.idNumber;
    var systemId = "systemlandcontract";

    Future.wait([
      LoanApplyService().getAreaAndPay(
          orgCode: orgCode,
          idCard: idCard,
          systemId: systemId,
          cancelToken: createCancelToken()),
      LoanApplyService().getLoanRecord(
          orgCode: orgCode,
          idCard: idCard,
          systemId: systemId,
          cancelToken: createCancelToken()),
    ]).then((list) {
      var result0 = list[0];
      if (result0.success == true) {
        areaAndPay =
            result0.data != null ? AreaAndPay.fromJson(result0.data) : null;
      } else {
        areaAndPay = null;
      }
      var result1 = list[1];
      if (result1.success == true) {
        loadRecordItems = (result1.data as List?)
                ?.map<LoadRecord>((item) => LoadRecord.fromJson(item))
                .toList() ??
            [];
      } else {
        loadRecordItems = [];
      }
      loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(handleError);
  }

  void loadData() {
    loadingStatus = LoadingStatus.loading;
    notifyListeners();
    var orgCode = residentInfo?.organizationNo;
    var idCard = residentInfo?.idNumber;
    var systemId = "systemlandcontract";

    Future.wait([
      LoanApplyService().getDictLoanStatus(cancelToken: createCancelToken()),
      LoanApplyService()
          .getDictReleaseAuditStatus(cancelToken: createCancelToken()),
      LoanApplyService().getAreaAndPay(
          orgCode: orgCode,
          idCard: idCard,
          systemId: systemId,
          cancelToken: createCancelToken()),
      LoanApplyService().getLoanRecord(
          orgCode: orgCode,
          idCard: idCard,
          systemId: systemId,
          cancelToken: createCancelToken()),
      LoanApplyService().getProductIntroduce(
          systemId: systemId, cancelToken: createCancelToken()),
    ]).then((list) {
      var result0 = list[0] as DictList;
      if (result0.success == true) {
        loanStatusDict = result0.data;
      }
      var result1 = list[1] as DictList;
      if (result1.success == true) {
        releaseAuditStatusDict = result1.data;
      }
      var result2 = list[2] as RequestNoData;
      if (result2.success == true) {
        areaAndPay =
            result2.data != null ? AreaAndPay.fromJson(result2.data) : null;
      } else {
        areaAndPay = null;
      }
      var result3 = list[3] as RequestNoData;
      if (result3.success == true) {
        loadRecordItems = (result3.data as List?)
                ?.map<LoadRecord>((item) => LoadRecord.fromJson(item))
                .toList() ??
            [];
      } else {
        loadRecordItems = [];
      }
      var result4 = list[4] as RequestNoData;
      if (result4.success == true) {
        productIntroduceItems = (result4.data as List?)
                ?.map<String>((item) => item.toString())
                .toList() ??
            [];
      } else {
        productIntroduceItems = [];
      }

      if (areaAndPay?.bankCode == '1') {
        pufaCheck();
        return;
      }

      loadingStatus = LoadingStatus.success;
      notifyListeners();
    }).onError(handleError);
  }

  void handleUnlock(LoadRecord record) {
    showLoading(context, content: "正在请求..  ");
    LoanApplyService()
        .releaseApplyVerify({"loanDataId": record.id},
            cancelToken: createCancelToken())
        .then((result) {
          if (!context.mounted) {
            return;
          }
          if (result.code == 0) {
            ReleaseApplyVerify item = ReleaseApplyVerify.fromJson(result.data);
            Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
              return LoanUnlockApplyPage(item: item);
            })).then((res) {
              if (res == true) {
                getLoadRecord();
              }
            });
          }
        })
        .onError(handleError)
        .whenComplete(() {
          if (!context.mounted) {
            return;
          }
          hideLoading(context);
        });
  }

  void handleOpenBank(LoadRecord record) async {
    var pullAppInfo = record.pullAppInfo;
    if (pullAppInfo == null) {
      return;
    }

    if (pullAppInfo.pullApp == 1) {
      if (Platform.isAndroid && pullAppInfo.appMarkAndroid != null) {
        var uri = Uri.parse(pullAppInfo.appMarkAndroid);
        bool canLaunch = await canLaunchUrl(uri);
        if (canLaunch) {
          await launchUrl(uri);
          return;
        } else if (pullAppInfo.pullMarket == 0) {
          try {
            var uri = Uri.parse(pullAppInfo.downUrl);
            bool canLaunch = await canLaunchUrl(uri);
            if (canLaunch) {
              launchUrl(uri);
            } else {
              showToast("打开失败");
            }
          } catch (error, stackTrace) {
            Log.e("", error: error, stackTrace: stackTrace);
            showToast("打开失败");
          }
        } else if (pullAppInfo.pullMarket == 1 &&
            pullAppInfo.marketMarkAndroid != null) {
          try {
            var uri = Uri.parse(pullAppInfo.marketMarkAndroid);
            bool canLaunch = await canLaunchUrl(uri);
            if (canLaunch) {
              launchUrl(uri);
            } else {
              showToast("打开失败");
            }
          } catch (error, stackTrace) {
            Log.e("", error: error, stackTrace: stackTrace);
            showToast("打开失败");
          }
        }
      } else if (Platform.isIOS && pullAppInfo.appMarkIos != null) {
        var uri = Uri.parse(pullAppInfo.appMarkIos);
        bool canLaunch = await canLaunchUrl(uri);
        if (canLaunch) {
          await launchUrl(uri);
          return;
        } else if (pullAppInfo.pullMarket == 0 && pullAppInfo.downUrl != null) {
          try {
            var uri = Uri.parse(pullAppInfo.downUrl);
            bool canLaunch = await canLaunchUrl(uri);
            if (canLaunch) {
              launchUrl(uri);
            } else {
              showToast("打开失败");
            }
          } catch (error, stackTrace) {
            Log.e("", error: error, stackTrace: stackTrace);
            showToast("打开失败");
          }
        } else if (pullAppInfo.pullMarket == 1 &&
            pullAppInfo.marketMarkIos != null) {
          try {
            var uri = Uri.parse(pullAppInfo.marketMarkIos);
            bool canLaunch = await canLaunchUrl(uri);
            if (canLaunch) {
              launchUrl(uri);
            } else {
              showToast("打开失败");
            }
          } catch (error, stackTrace) {
            Log.e("", error: error, stackTrace: stackTrace);
            showToast("打开失败");
          }
        }
      } else {
        showToast("打开失败");
      }
    } else if (pullAppInfo.pullApp == 2 && pullAppInfo.h5Url != null) {
      Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
        return LiveWebPage(
          uri: Uri.parse(pullAppInfo.h5Url!),
        );
      }));
    } else if (pullAppInfo.pullApp == 3) {
      showToast("当前环境不支持微信操作!", duration: const Duration(seconds: 1));
    }
  }

  void handlePufa(LoadRecord item) {
    if (item.bankCode == 1) {
      Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
        return const PufaMainPage();
      }));
    }
  }

  void handleOpenVideo(LoadRecord item) {
    if (item.operationVideoUrl == null) {
      return;
    }
    //String testUrl = "http://vjs.zencdn.net/v/oceans.mp4";
    Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
      return VideoPlayerPage(videoUrl: item.operationVideoUrl);
    }));
  }

  void recordFun() {
    Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
      return const LoanUnlockRecordPage();
    })).then((res) {
      getLoadRecord();
    });
  }

  void apply() {
    if (residentInfo == null || areaAndPay == null) {
      return;
    }
    Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
      return LoanApplyBankListPage(
        residentInfo: residentInfo!,
        areaAndPay: areaAndPay!,
      );
    })).then((res) {
      if (res == true) {
        getLoadRecord();
      }
    });
  }
}
