import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_publicity_list_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:oktoast/oktoast.dart';

import '../../../utils/color_util.dart';
import '../../../utils/event_bus.dart';
import '../../../utils/request/subsidy_service.dart';
import 'entity/subsidy_publicity_entity.dart';

class SubsidyPublicityFeedbackPage extends StatefulWidget {
  final SubsidyPublicityEntity dateItem;

  const SubsidyPublicityFeedbackPage({super.key, required this.dateItem});

  @override
  State<SubsidyPublicityFeedbackPage> createState() =>
      _SubsidyPublicityFeedbackPageState();
}

class _SubsidyPublicityFeedbackPageState
    extends State<SubsidyPublicityFeedbackPage> {
  bool _isLoading = false;
  final FocusNode _focusNode = FocusNode();
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(widget.dateItem.announceTitle ?? "",
              style: const TextStyle(color: Colors.black, fontSize: 16)),
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: _isLoading
            ? const Center(
                child: SpinKitCircle(
                  // color: HexColor('#16B760'),
                  color: Color.fromRGBO(0, 127, 255, 1),
                  size: 60.0,
                ),
              )
            : Column(children: [
                Container(
                    padding:
                        EdgeInsets.only(left: 16.px, right: 16.px, top: 16.px),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            "补贴公示信息反馈信息",
                            style: TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w400,
                                color: Color.fromRGBO(41, 41, 52, 1)),
                          ),
                          const SizedBox(height: 16),
                          Container(
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(8)),
                              child: TextField(
                                controller: _controller,
                                style: const TextStyle(fontSize: 14),
                                minLines: 5,
                                maxLines: 8,
                                decoration: const InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(8)),
                                    borderSide: BorderSide(
                                        color: Color.fromRGBO(243, 245, 249, 1),
                                        width: 1),
                                  ),
                                  hintText: '请输入反馈内容',
                                  hintStyle: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w400,
                                      color: Color.fromRGBO(41, 41, 52, 0.4)),
                                ),
                                onEditingComplete: () {
                                  print('onEditingComplete');
                                },
                                onChanged: (v) {
                                  print('onChanged:' + v);
                                },
                                onSubmitted: (v) {
                                  FocusScope.of(context)
                                      .requestFocus(_focusNode);
                                  print('onSubmitted:' + v);
                                  _controller.clear();
                                },
                              )),
                          Container(
                            margin: EdgeInsets.only(top: 20.px),
                            width: 347.px,
                            height: 45.px,
                            child: ElevatedButton(
                                onPressed: () {
                                  toSubmit();
                                },
                                style: TextButton.styleFrom(
                                  backgroundColor:
                                      const Color.fromRGBO(2, 139, 93, 1),
                                  foregroundColor: Colors.white,
                                  side: const BorderSide(
                                      color: Color.fromRGBO(2, 139, 93, 1),
                                      width: 1),
                                  shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(10))),
                                ),
                                child: Text("提交",
                                    style: TextStyle(
                                        fontSize: 14.px, color: Colors.white))),
                          )
                        ]))
              ]));
  }

  void toSubmit() {
    String announceFeedbackInfo = _controller.text;
    if (announceFeedbackInfo.isEmpty) {
      showToast("请输入反馈内容");
      return;
    }
    showCustomBusyDialog(context, msg: "提交中...");
    Map<String, dynamic> params = {
      "subsidyAnnounceConfigId": widget.dateItem.subsidyAnnounceConfigId,
      "organizationNo": widget.dateItem.organizationNo,
      "announceFeedbackStatus": 2,
      "announceFeedbackInfo": announceFeedbackInfo
    };

    SubsidyService.feedback(params).then((result) {
      Navigator.of(context).pop();
      if (result.success == true) {
        showToast("反馈成功");
        bus.emit("subsidy_publicity_list_refresh");
        Navigator.of(context).popUntil((route) =>
            route is MaterialPageRoute &&
            route.builder(context) is SubsidyPublicityListPage);
      } else {}
    }, onError: (e) {
      Navigator.of(context).pop();
    });
  }

  Future<void> showCustomBusyDialog(BuildContext context,
      {String msg = '上传中...'}) async {
    await showGeneralDialog(
      context: context,
      barrierDismissible: false, // 设置用户是否可以点击遮罩层来关闭对话框
      barrierLabel: msg, // 遮罩层的提示文字
      barrierColor: Colors.black.withOpacity(0.5), // 遮罩层的颜色和透明度
      transitionDuration: const Duration(milliseconds: 150), // 动画时长
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return Center(
          // 对话框内容居中显示
          child: Container(
            alignment: Alignment.center,
            width: 120.px,
            height: 120.px,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.px),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    width: 40.px,
                    height: 40.px,
                    child: const CircularProgressIndicator()),
                SizedBox(height: 16.px),
                Text(msg,
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(44, 44, 52, 0.8)))
              ],
            ),
          ), // 加载指示器
        );
      },
    );
  }
}
