import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity_response_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_publicity_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_publicity_feedback_page.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_publicity_item_widget.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_publicity_list_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';
import 'package:oktoast/oktoast.dart';

import '../../../utils/color_util.dart';
import '../../../utils/event_bus.dart';
import '../../../utils/request/subsidy_service.dart';

class SubsidyPublicityDetailPage extends StatefulWidget {
  final SubsidyPublicityEntity dateItem;

  const SubsidyPublicityDetailPage({super.key, required this.dateItem});

  @override
  State<SubsidyPublicityDetailPage> createState() =>
      _SubsidyPublicityDetailPageState();
}

class _SubsidyPublicityDetailPageState
    extends State<SubsidyPublicityDetailPage> {
  bool _isLoading = false;
  int subsidyType = 0;
  PublicityResponseEntity? record;
  List<dynamic>? dataRecords = [];
  @override
  void initState() {
    super.initState();
    setState(() {
      _isLoading = true;
    });
    subsidyType = widget.dateItem.subsidyClassify!;
    _getDetailList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(widget.dateItem.announceTitle ?? "",
              style: const TextStyle(color: Colors.black, fontSize: 16)),
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: _isLoading
            ? const Center(
                child: SpinKitCircle(
                  // color: HexColor('#16B760'),
                  color: Color.fromRGBO(0, 127, 255, 1),
                  size: 60.0,
                ),
              )
            : Column(children: [
                Container(
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(top: 10.px, right: 15.px, left: 15),
                  padding: EdgeInsets.all(10.px),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(2, 139, 93, 0.2),
                    borderRadius: BorderRadius.circular(5),
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.info,
                        color: const Color.fromRGBO(2, 139, 93, 1),
                        size: 20.px,
                      ),
                      const SizedBox(
                        width: 5,
                      ),
                      const Text(
                        "公示信息",
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w400,
                          color: Color.fromRGBO(2, 139, 93, 1),
                        ),
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      Container(
                        width: 1.px,
                        color: const Color.fromRGBO(2, 139, 93, 1),
                        height: 15.px,
                      ),
                      const SizedBox(
                        width: 8,
                      ),
                      Expanded(
                        // 使用 Expanded 占据剩余空间
                        child: SingleChildScrollView(
                          // 添加 SingleChildScrollView 实现滚动
                          scrollDirection: Axis.horizontal, // 设置水平滚动
                          child: Text(
                            record != null
                                ? "${record?.subsidyYear}${record?.subsidyItemName}"
                                : "",
                            softWrap: false, // 防止文本换行
                            style: const TextStyle(
                                fontSize: 14,
                                color: Color.fromRGBO(41, 41, 52, 0.8),
                                fontWeight: FontWeight.w500),
                          ),
                        ),
                      ),
                      // Text(
                      //   record != null
                      //       ? "${record?.subsidyYear}${record?.subsidyItemName}"
                      //       : "",
                      //   style: const TextStyle(
                      //       fontSize: 14,
                      //       color: Color.fromRGBO(41, 41, 52, 0.8),
                      //       fontWeight: FontWeight.w500),
                      // ),
                    ],
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Expanded(
                    child: SingleChildScrollView(
                  child: Column(
                      children: dataRecords?.isNotEmpty == true
                          ? (dataRecords!
                              .map((item) => SubsidyPublicityItemWidget(
                                    dataItem: item,
                                    subsidyType: subsidyType,
                                    subsidyAnnounceType:
                                        widget.dateItem.subsidyAnnounceType,
                                  ))
                              .toList())
                          : []),
                )),
                widget.dateItem.feedbackStatus == 0
                    ? Container(
                        alignment: Alignment.center,
                        padding: EdgeInsets.only(
                            bottom: 15.px,
                            top: 10.px,
                            right: 15.px,
                            left: 15.px),
                        color: Colors.white,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 148.px,
                              child: ElevatedButton(
                                  onPressed: () {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) =>
                                            SubsidyPublicityFeedbackPage(
                                          dateItem: widget.dateItem,
                                        ),
                                      ),
                                    );
                                  },
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.only(
                                        left: 10.px, right: 10.px),
                                    backgroundColor: Colors.white,
                                    foregroundColor: Colors.white,
                                    side: const BorderSide(
                                        color: Colors.redAccent, width: 1),
                                    shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(10))),
                                  ),
                                  child: Text("公示信息不符反馈",
                                      style: TextStyle(
                                          fontSize: 14.px, color: Colors.red))),
                            ),
                            SizedBox(
                              width: 20.px,
                            ),
                            SizedBox(
                              width: 148.px,
                              child: ElevatedButton(
                                  onPressed: () {
                                    toSureFeedBack();
                                  },
                                  style: TextButton.styleFrom(
                                    padding: EdgeInsets.only(
                                        left: 10.px, right: 10.px),
                                    backgroundColor:
                                        const Color.fromRGBO(2, 139, 93, 1),
                                    foregroundColor: Colors.white,
                                    side: const BorderSide(
                                        color: Color.fromRGBO(2, 139, 93, 1),
                                        width: 1),
                                    shape: const RoundedRectangleBorder(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(10))),
                                  ),
                                  child: Text("公示信息确认无误",
                                      style: TextStyle(
                                          fontSize: 14.px,
                                          color: Colors.white))),
                            ),
                          ],
                        ))
                    : Container(
                        height: 10.px,
                      )
              ]));
  }

  String getDateFormat(int timestamp) {
    // 转换为 DateTime
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    // 格式化
    String formattedTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    return formattedTime;
  }

  void toSureFeedBack() {
    Map<String, dynamic> params = {
      "subsidyAnnounceConfigId": widget.dateItem.subsidyAnnounceConfigId,
      "organizationNo": widget.dateItem.organizationNo,
      "announceFeedbackStatus": 1,
    };
    showCustomBusyDialog(context, msg: "提交中...");
    SubsidyService.feedback(params).then((result) {
      Navigator.of(context).pop();
      if (result.success == true) {
        showToast("提交成功");
        bus.emit("subsidy_publicity_list_refresh");
        Navigator.of(context).popUntil((route) =>
            route is MaterialPageRoute &&
            route.builder(context) is SubsidyPublicityListPage);
      } else {}
    }, onError: (error) {
      Navigator.of(context).pop();
    });
  }

  void _getDetailList() async {
    Map<String, dynamic> param = {
      "subsidyAnnounceConfigId": widget.dateItem.subsidyAnnounceConfigId,
      "organizationNo": widget.dateItem.organizationNo,
      "rows": 20,
      "page": 1
    };
    String url = "";
    print(widget.dateItem.subsidyAnnounceType);
    if (widget.dateItem.subsidyAnnounceType == '2' ||
        widget.dateItem.subsidyAnnounceType == 2) {
      switch (subsidyType) {
        case 1: // 大豆生产者
          url =
              "/subsidy/subsidyAnnounceConfig/app/queryPaymentApplyDetailList";
          break;
        case 2: // 农机购置与应用补贴
        case 3: // 保护性耕作补贴
        case 4: // 农机报废
          url =
              "/subsidy/subsidyAnnounceConfig/app/queryAgmachinePaymentDetailList";
          break;
        case 5: // 粮改饲
          url =
              "/subsidy/subsidyAnnounceConfig/app/queryPaymentApplyDetailList";
          break;
      }
    } else {
      switch (subsidyType) {
        case 1: // 大豆生产者
          url = "/subsidy/subsidyAnnounceConfig/app/queryPaymentDetailList";
          break;
        case 2: // 农机购置与应用补贴
        case 3: // 保护性耕作补贴
        case 4: // 农机报废
          url =
              "/subsidy/subsidyAnnounceConfig/app/queryAgmachinePaymentDetailList";
          break;
        case 5: // 粮改饲
          url = "/subsidy/subsidyAnnounceConfig/app/queryPaymentDetailList";
          break;
      }
    }

    await SubsidyService.queryAnnounceDetailList(param, url: url)
        .then((result) {
      setState(() {
        _isLoading = false;
      });
      if (result.success == true) {
        setState(() {
          record = PublicityResponseEntity.fromJson(result.data);
          dataRecords = record?.details?.records!;
        });
      }
    });
  }

  Future<void> showCustomBusyDialog(BuildContext context,
      {String msg = '上传中...'}) async {
    await showGeneralDialog(
      context: context,
      barrierDismissible: false, // 设置用户是否可以点击遮罩层来关闭对话框
      barrierLabel: msg, // 遮罩层的提示文字
      barrierColor: Colors.black.withOpacity(0.5), // 遮罩层的颜色和透明度
      transitionDuration: const Duration(milliseconds: 150), // 动画时长
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return Center(
          // 对话框内容居中显示
          child: Container(
            alignment: Alignment.center,
            width: 120.px,
            height: 120.px,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.px),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    width: 40.px,
                    height: 40.px,
                    child: const CircularProgressIndicator()),
                SizedBox(height: 16.px),
                Text(msg,
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(44, 44, 52, 0.8)))
              ],
            ),
          ), // 加载指示器
        );
      },
    );
  }
}
