import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/news_of_subsidy_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_news_detail_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../components/bdh_network_image.dart';
import '../../../utils/color_util.dart';
import '../../../utils/image_util.dart';
import '../../../utils/request/subsidy_service.dart';
import '../../message/bdh_empty_View.dart';
import 'entity/new_category_entity.dart';

class SubsidyNewsListPage extends StatefulWidget {
  final NewCategoryEntity newsCategory;
  const SubsidyNewsListPage({super.key, required this.newsCategory});

  @override
  State<SubsidyNewsListPage> createState() => _SubsidyNewsListPageState();
}

class _SubsidyNewsListPageState extends State<SubsidyNewsListPage> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  List<NewsOfSubsidyRecords> newsRecords = [];
  int pageNum = 1;
  int pageSize = 10;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    setState(() {
      _isLoading = true;
    });
    _onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.newsCategory.columnLevelName!),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: Container(
        margin: EdgeInsets.only(left: 10.px, right: 10.px),
        width: 355.px,
        child: newsRecords.isEmpty
            ? Center(
                child: _isLoading
                    ? const SpinKitCircle(
                        // color: HexColor('#16B760'),
                        color: Color.fromRGBO(0, 127, 255, 1),
                        size: 60.0,
                      )
                    : const BdhEmptyView(),
              )
            : SmartRefresher(
                enablePullUp: true,
                onRefresh: _onRefresh,
                onLoading: _onLoadMore,
                controller: _refreshController,
                child: ListView.builder(
                    itemCount: newsRecords.length,
                    itemBuilder: (ctx, idx) {
                      return _buildNewsItem(newsRecords[idx]);
                    }),
              ),
      ),
    );
  }

  Widget _buildNewsItem(NewsOfSubsidyRecords newsRecord) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SubsidyNewsDetailPage(
              newsTitle: newsRecord.title!,
              newsId: newsRecord.releaseInfoId!,
            ),
          ),
        );
      },
      child: Container(
          margin: EdgeInsets.only(top: 10.px),
          padding:
              EdgeInsets.only(right: 5.px, left: 8.px, bottom: 5.px, top: 5.px),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10.px),
          ),
          child: Row(
            children: [
              Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                    Text(
                      newsRecord.title!,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 16.px,
                          fontWeight: FontWeight.bold,
                          color: Colors.black),
                    ),
                    SizedBox(
                      height: 10.px,
                    ),
                    Text(
                      getDateFormat(newsRecord.createTime!),
                      style: TextStyle(
                          fontSize: 12.px,
                          color: const Color.fromRGBO(153, 153, 153, 1)),
                    )
                  ])),
              Container(
                width: 112.px,
                height: 86.px,
                margin: EdgeInsets.only(left: 15.px),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10.px),
                ),
                child: newsRecord.thumbUrl!.isEmpty
                    ? Image.asset(ImageHelper.wrapAssets(
                        newsRecord.columnLevelName == "政策法规"
                            ? 'subsidy/ic_news_zcfg.png'
                            : "subsidy/ic_news_gzdt.png"))
                    : BdhNetworkImage(
                        width: 112.px,
                        height: 86.px,
                        url: newsRecord.thumbUrl!,
                        fit: BoxFit.cover,
                      ),
              )
            ],
          )),
    );
  }

  String getDateFormat(int timestamp) {
    // 转换为 DateTime
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    // 格式化
    String formattedTime = DateFormat('yyyy-MM-dd').format(dateTime);
    return formattedTime;
  }

  void _onRefresh() async {
    pageNum = 1;
    _getNewsRecords(widget.newsCategory);
  }

  void _onLoadMore() async {
    pageNum++;
    _getNewsRecords(widget.newsCategory);
  }

  void _getNewsRecords(NewCategoryEntity newsCategory) async {
    Map<String, Object> params = {
      "systemCode": "systemlandcontract",
      "columnLevelId": newsCategory.columnLevelId!,
      "orgType": 1,
      "page": pageNum
    };
    await SubsidyService.getAppNewsByPage(params).then((result) {
      setState(() {
        _isLoading = false;
      });
      if (result.success == true) {
        setState(() {
          NewsOfSubsidyEntity newsOfSubsidyEntity =
              NewsOfSubsidyEntity.fromJson(result.data);
          if (newsOfSubsidyEntity.records != null) {
            if (pageNum == 1) {
              newsRecords.clear();
              newsRecords = newsOfSubsidyEntity.records!;
              _refreshController.refreshCompleted();
            } else {
              newsRecords.addAll(newsOfSubsidyEntity.records!);
            }
            if (newsOfSubsidyEntity.records!.length < pageSize) {
              _refreshController.loadNoData();
            } else {
              _refreshController.loadComplete();
            }
          }
        });
      }
    });
  }
}
