import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_apply_info_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_file_item_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_item_info_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_list_page.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_sign/subsidy_item_info_widget.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_sign/subsidy_step_widget.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:open_filex/open_filex.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../model/dict_tree_model.dart';
import '../../../../utils/event_bus.dart';
import '../../../../utils/native_util.dart';
import '../../../../utils/request/land_intradomain_sign_service.dart';
import '../../../../utils/request/subsidy_service.dart';
import '../../../../utils/storage_util.dart';
import '../entity/IC_card_list_model.dart';

/// 申请补贴界面

class SubsidySignApplyPage extends StatefulWidget {
  final SubsidyApplyInfoEntity dataItem;

  const SubsidySignApplyPage({super.key, required this.dataItem});

  @override
  State<SubsidySignApplyPage> createState() => _SubsidySignApplyPageState();
}

class _SubsidySignApplyPageState extends State<SubsidySignApplyPage> {
  int _remainingSeconds = 5; // 倒计时总秒数
  Timer? _timer;
  StateSetter? mCustomState;
  bool isShowAgreeBtn = false;
  bool isAgreeProtocol = false;
  int _currentStep = 0;
  List<DictNode> bankNameTypes = [];
  List<DictNode> subsidyCropCodeTypes = [];

  List<SubsidyItemInfoRecords> subsidyItemInfoRecords = [];

  List<SubsidyFileItemEntity> uploadedFileList = [];
  List<IcCardInfoModel> icCardList = [];

  Timer? loopTimer;
  int count = 0;

  String selectBankNumber = "";

  TextEditingController feedbackController = TextEditingController();

  @override
  void initState() {
    super.initState();
    selectBankNumber = widget.dataItem.bankAccount??"";
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      showGeneralDialog(
          context: context,
          barrierDismissible: false,
          pageBuilder: (BuildContext context, Animation<double> animation,
              Animation<double> secondaryAnimation) {
            return StatefulBuilder(builder: (context, state) {
              mCustomState = state;
              return PopScope(
                  canPop: false, child: showConfirmDialog(context, state));
            });
          });
      startCountdown();
    });

    loadData();
  }

  void startCountdown() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingSeconds > 0 && mCustomState != null) {
          mCustomState!(() {
            _remainingSeconds--;
            if (_remainingSeconds == 0) {
              isShowAgreeBtn = true;
              mCustomState!(() {});
              _getBankNameDicts();
              _querySubsidyDetails();
              _querySubsidyUploadedFile();
            }
          });
        } else {
          _timer?.cancel(); // 倒计时结束取消定时器
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("申请补贴"),
        leading: BackButton(
          onPressed: () {
            Navigator.pop(context);
          },
        ),
        actions: [
          GestureDetector(
            onTap: () {
              feedbackController.text = "";
              showGeneralDialog(
                  context: context,
                  barrierDismissible: false,
                  pageBuilder: (BuildContext context, Animation<double> animation,
                      Animation<double> secondaryAnimation) {
                    return StatefulBuilder(builder: (context, state) {
                      mCustomState = state;
                      return PopScope(
                          canPop: false, child: reportDialog(context, state));
                    });
                  });
            },
            child: const Text("反馈  "),
          )
        ],
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: Column(
        children: [
          Container(
            padding: EdgeInsets.only(bottom: 5.px),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(15),
                bottomRight: Radius.circular(15),
              ),
            ),
            child: SubsidyStepWidget(
              stepCode: _currentStep,
              onSelectChanged: (isSelected) {},
            ),
          ),
          _currentStep == 2 ? _buildApplyInfoView() : getStepView(_currentStep),
          _currentStep == 2 ? Container() : const Spacer(),
          Container(
            width: 347.px,
            height: 45.px,
            margin: EdgeInsets.only(bottom: 40.px, left: 16.px, right: 16.px),
            child: ElevatedButton(
                onPressed: () {
                  if (_currentStep == 1) {
                    updateICCard();
                  }
                  setState(() {
                    if (_currentStep < 3) {
                      _currentStep++;
                    } else if (_currentStep == 3) {
                      if (isAgreeProtocol) {
                        _applySubsidy();
                      } else {
                        showToast("请先同意补贴协议");
                      }
                    }
                  });
                },
                style: TextButton.styleFrom(
                  backgroundColor: const Color.fromRGBO(2, 139, 93, 1),
                  foregroundColor: Colors.white,
                  side: const BorderSide(
                      color: Color.fromRGBO(2, 139, 93, 1), width: 1),
                  shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(10))),
                ),
                child: Text(_currentStep == 3 ? "申请补贴" : "下一步",
                    style: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w400,
                        color: Colors.white))),
          ),
        ],
      ),
    );
  }

  Widget getStepView(int stepCode) {
    switch (stepCode) {
      case 0:
        return _buildPersonalInfoView();
      case 1:
        return _buildBankCardInfoView();
      case 3:
        return _buildFileUploadView();
      default:
        return Container();
    }
  }

  /// 显示确认弹窗
  Widget showConfirmDialog(BuildContext context, StateSetter state) {
    return Container(
        width: 300.px,
        height: 1242.px,
        alignment: Alignment.centerLeft,
        margin: EdgeInsets.only(
            top: 90.px, left: 37.5.px, right: 37.5.px, bottom: 70.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.05),
              offset: Offset(0, 2),
              blurRadius: 4,
            ),
          ],
        ),
        child: Container(
            padding: EdgeInsets.only(top: 8.px, bottom: 8.px),
            alignment: Alignment.centerLeft,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(width: 1.px, color: Colors.white),
                color: Colors.white,
                gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [
                      0.0,
                      0.2,
                    ],
                    colors: [
                      Color.fromRGBO(217, 255, 219, 0.4),
                      Color.fromRGBO(47, 217, 160, 0),
                    ])),
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                      padding: EdgeInsets.only(top: 16.px, bottom: 30.px),
                      alignment: Alignment.center,
                      child: Text(
                        "声明",
                        style: TextStyle(
                            decoration: TextDecoration.none,
                            fontSize: 17.px,
                            color: const Color.fromRGBO(41, 41, 52, 1),
                            fontWeight: FontWeight.w600),
                      )),
                  Expanded(
                      child: SingleChildScrollView(
                    child: Container(
                      margin: EdgeInsets.only(left: 24.px, right: 24.px),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "尊敬的农户朋友：",
                            style: TextStyle(
                                fontSize: 12.px,
                                decoration: TextDecoration.none,
                                letterSpacing: 1.2,
                                height: 1.4,
                                color: const Color.fromRGBO(41, 41, 52, 1),
                                fontWeight: FontWeight.w500),
                          ),
                          Container(
                            height: 4.px,
                          ),
                          Text(
                            "  您好！感谢您使用本平台申请农业补贴。为了确保您顺利申请并享受国家惠农政策，请您在填写申请前仔细阅读以下说明：",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(41, 41, 52, 1),
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            "一、农业补贴政策概述",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(41, 41, 52, 1),
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            "  农业补贴是国家为了支持农业生产、保障粮食安全、促进农民增收而实施的一项惠农政策。补贴资金主要用于支持粮食生产、耕地地力保护、农业结构调整、农业资源保护等方面。具体补贴项目、标准、对象等会根据国家政策调整而变化，请您及时关注所在农场发布的最新政策信息。",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(41, 41, 52, 1),
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            "二、申请条件",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(41, 41, 52, 1),
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            "申请人资格: 申请人应为从事农业生产的农户，包括家庭农场、农民合作社、农业企业等新型农业经营主体",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(242, 63, 43, 1),
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            "要求: 申请人应按照所在农场的要求种植相应的农作物，并达到规定的规模。",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(242, 63, 43, 1),
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            "土地要求: 申请人应拥有合法的土地承包经营权同，并按要求进行耕种。",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(242, 63, 43, 1),
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            "其他条件: 部分补贴项目可能对申请人的信用记录、环保要求等方面有特殊规定，请仔细阅读相关政策文件。",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(242, 63, 43, 1),
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            "三、注意事项",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(41, 41, 52, 1),
                                fontWeight: FontWeight.w400),
                          ),
                          Text(
                            "信息真实性: 申请人应确保填写的信息真实、准确、完整，如有虚假信息将取消申请资格。",
                            style: TextStyle(
                                fontSize: 12.px,
                                wordSpacing: 2,
                                height: 1.5,
                                decoration: TextDecoration.none,
                                color: const Color.fromRGBO(41, 41, 52, 1),
                                fontWeight: FontWeight.w400),
                          ),
                        ],
                      ),
                    ),
                  )),
                  Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(
                          bottom: 25.px, top: 20.px, right: 24.px, left: 24.px),
                      height: 40.px,
                      width: 300.px,
                      color: Colors.white,
                      child: Row(
                        children: [
                          SizedBox(
                            width: 118.px,
                            child: ElevatedButton(
                                onPressed: () {
                                  if (isShowAgreeBtn) {
                                    Navigator.of(context).pop();
                                    isAgreeProtocol = true;
                                  }
                                },
                                style: TextButton.styleFrom(
                                  padding:
                                      EdgeInsets.only(left: 8.px, right: 8.px),
                                  backgroundColor: isShowAgreeBtn
                                      ? const Color.fromRGBO(2, 139, 93, 1)
                                      : const Color.fromRGBO(145, 189, 174, 1),
                                  foregroundColor: Colors.white,
                                  side: BorderSide(
                                      color: isShowAgreeBtn
                                          ? const Color.fromRGBO(2, 139, 93, 1)
                                          : const Color.fromRGBO(
                                              145, 189, 174, 1),
                                      width: 1),
                                  shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(10))),
                                ),
                                child: Text(
                                    isShowAgreeBtn
                                        ? "同意"
                                        : "阅读声明($_remainingSeconds)",
                                    style: TextStyle(
                                        fontSize: 14.px, color: Colors.white))),
                          ),
                          SizedBox(
                            width: 14.px,
                          ),
                          SizedBox(
                            width: 118.px,
                            child: ElevatedButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                  Navigator.of(context).pop();
                                },
                                style: TextButton.styleFrom(
                                  backgroundColor: Colors.white,
                                  foregroundColor: Colors.white,
                                  side: const BorderSide(
                                      color: Color.fromRGBO(2, 139, 93, 1),
                                      width: 1),
                                  shape: const RoundedRectangleBorder(
                                      borderRadius: BorderRadius.all(
                                          Radius.circular(10))),
                                ),
                                child: Text("不同意",
                                    style: TextStyle(
                                        fontSize: 14.px,
                                        color: const Color.fromRGBO(
                                            2, 139, 93, 1)))),
                          ),
                        ],
                      ))
                ])));
  }

  Widget reportDialog (BuildContext context, StateSetter state) {
    return Container(
        width: 300.px,
        margin: EdgeInsets.only(
            top: 200.px, left: 37.5.px, right: 37.5.px, bottom: 270.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color.fromRGBO(0, 0, 0, 0.05),
              offset: Offset(0, 2),
              blurRadius: 4,
            ),
          ],
        ),
        child: Container(
            padding: EdgeInsets.only(top: 8.px, bottom: 8.px),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(width: 1.px, color: Colors.white),
                color: Colors.white,
                gradient: const LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    stops: [
                      0.0,
                      0.2,
                    ],
                    colors: [
                      Color.fromRGBO(217, 255, 219, 0.4),
                      Color.fromRGBO(47, 217, 160, 0),
                    ])),
            child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    margin: EdgeInsets.all(25.px),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Material(color: Colors.transparent, child: Text("反馈信息", style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600, fontSize: 17.px),),),
                        SizedBox(height: 10.px,),
                        Container(
                          height: 175.px,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(5.px)),
                            border: Border.all(color: Colors.grey, width: 1.px),
                          ),
                          child: CupertinoTextField.borderless(
                            maxLines: 10,
                            padding: EdgeInsets.zero,
                            controller: feedbackController,
                            placeholder: "请输入反馈内容\n\n\n\n\n\n\n\n\n\n",
                            placeholderStyle: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: const Color.fromRGBO(0, 0, 0, 0.2),
                            ),
                          ),
                        ),
                        SizedBox(height: 10.px,),
                        Row(
                          children: [
                            const Expanded(child: SizedBox()),
                            GestureDetector(
                              onTap: () {
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                width: 100.px,
                                height: 40.px,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.all(Radius.circular(5.px)),
                                  color: HexColor("#CDCCD2"),
                                ),
                                child: Center(
                                  child: Material(color: Colors.transparent, child: Text("取消", style: TextStyle(color: Colors.white, fontWeight: FontWeight.w400, fontSize: 15.px),),),
                                ),
                              ),
                            ),
                            SizedBox(width: 10.px,),
                            GestureDetector(
                              onTap: () {
                                BdhLandResponsitory.subsidyFeedback({
                                  "lcSubsidyApplyId":widget.dataItem.lcSubsidyApplyId,
                                  "announceFeedbackInfo":feedbackController.text,
                                }).then((value) {
                                  if(value.code == 0) {
                                    showToast("反馈成功");
                                    Navigator.of(context).pop();
                                  } else {

                                  }
                                });
                              },
                              child: Container(
                                width: 100.px,
                                height: 40.px,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.all(Radius.circular(5.px)),
                                  color: HexColor("#028B5D"),
                                ),
                                child: Center(
                                  child: Material(color: Colors.transparent, child: Text("反馈", style: TextStyle(color: Colors.white, fontWeight: FontWeight.w400, fontSize: 15.px),),),
                                ),
                              ),
                            ),
                            const Expanded(child: SizedBox()),
                          ],
                        ),
                      ],
                    ),
                  ),
                ]
            )
        )
    );
  }

  /// 个人信息界面
  Widget _buildPersonalInfoView() {
    return Container(
        width: 347.px,
        margin: EdgeInsets.only(top: 15.px),
        padding: EdgeInsets.only(left: 15.px, right: 15.px),
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(5))),
        child:
            Column(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
          Container(
            padding: EdgeInsets.only(top: 10.px, bottom: 10.px),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("姓名",
                    style: TextStyle(
                        fontSize: 14.px, fontWeight: FontWeight.w400)),
                const Spacer(),
                Text(
                  widget.dataItem.farmerName!,
                  style:
                      TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500),
                ),
                SizedBox(
                  width: 8.px,
                ),
              ],
            ),
          ),
          Divider(
            height: 0.5.px,
            color: const Color.fromRGBO(239, 241, 245, 1),
          ),
          Container(
            padding: EdgeInsets.only(top: 10.px, bottom: 10.px),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("身份证",
                    style: TextStyle(
                        fontSize: 14.px, fontWeight: FontWeight.w400)),
                const Spacer(),
                Text(
                  widget.dataItem.farmerIdNumber!,
                  style:
                      TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
          Divider(
            height: 0.5.px,
            color: const Color.fromRGBO(239, 241, 245, 1),
          ),
          Container(
            padding: EdgeInsets.only(top: 10.px, bottom: 10.px),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("所在单位",
                    style: TextStyle(
                        fontSize: 14.px, fontWeight: FontWeight.w400)),
                const Spacer(),
                SizedBox(
                  width: 200.px,
                  child: Text(
                    widget.dataItem.organizationName!,
                    maxLines: 4,
                    overflow: TextOverflow.ellipsis,
                    style:
                        TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          )
        ]));
  }

  Widget _buildBankCardInfoView() {
    return Container(
        width: 347.px,
        height: 209.5.px,
        margin: EdgeInsets.only(top: 15.px),
        padding: EdgeInsets.only(left: 15.px, right: 15.px),
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(5))),
        child:
            Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
          Container(
            padding: EdgeInsets.only(top: 10.px, bottom: 10.px),
            child: Row(
              // crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("银行卡号",
                    style: TextStyle(
                        fontSize: 14.px, fontWeight: FontWeight.w400)),
                const Expanded(child: SizedBox()),
                Container(
                  width: 180.px,
                  height: 30.px,
                  child: DropdownButtonFormField<String>(
                    decoration: InputDecoration(
                      hintText: widget.dataItem.bankAccount,
                      floatingLabelAlignment: FloatingLabelAlignment.start,
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.symmetric(
                          vertical: 0.px, horizontal: 0.px),
                      // suffixIcon: Image.asset(ImageHelper.wrapAssets("right_arrow_grey.png"),height: 22.px, width: 22.px),
                      isDense: true,
                    ),
                    value: widget.dataItem.bankAccount,
                    onChanged: (value) {
                      for (IcCardInfoModel model in icCardList) {
                        if (model.bankAccount.toString() == value) {
                          selectBankNumber = value.toString();
                          widget.dataItem.bankName = model.bankName.toString();
                          widget.dataItem.network = model.network;
                          widget.dataItem.lineNumber = model.lineNumber;
                          break;
                        }
                      }
                      setState(() {

                      });
                    },
                    items:_buildMenuList(icCardList),
                  ),
                ),
              ],
            ),
          ),
          Divider(
            height: 0.5.px,
            color: const Color.fromRGBO(239, 241, 245, 1),
          ),
          Container(
            padding: EdgeInsets.only(top: 10.px, bottom: 10.px),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("开户银行",
                    style: TextStyle(
                        fontSize: 14.px, fontWeight: FontWeight.w400)),
                const Spacer(),
                Text(
                  getBankNameTitle(widget.dataItem.bankName!),
                  style:
                      TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500),
                ),
              ],
            ),
          ),
          Divider(
            height: 0.5.px,
            color: const Color.fromRGBO(239, 241, 245, 1),
          ),
          Container(
            padding: EdgeInsets.only(top: 10.px, bottom: 10.px),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("开户网点",
                    style: TextStyle(
                        fontSize: 14.px, fontWeight: FontWeight.w400)),
                const Spacer(),
                SizedBox(
                  width: 200.px,
                  child: Text(
                    widget.dataItem.network ?? "",
                    maxLines: 1,
                    textAlign: TextAlign.end,
                    overflow: TextOverflow.ellipsis,
                    style:
                        TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ),
          Divider(
            height: 0.5.px,
            color: const Color.fromRGBO(239, 241, 245, 1),
          ),
          Container(
            padding: EdgeInsets.only(top: 10.px, bottom: 10.px),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("银联号",
                    style: TextStyle(
                        fontSize: 14.px, fontWeight: FontWeight.w400)),
                const Spacer(),
                SizedBox(
                  width: 200.px,
                  child: Text(
                    widget.dataItem.lineNumber ?? "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    textAlign: TextAlign.end,
                    style:
                        TextStyle(fontSize: 14.px, fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          )
        ]));
  }

  Widget _buildApplyInfoView() {
    return Expanded(
      child: ListView.builder(
          itemCount: subsidyItemInfoRecords.length,
          itemBuilder: (context, index) => SubsidyItemInfoWidget(
              dataItem: subsidyItemInfoRecords[index],
              applyItem: widget.dataItem)),
    );
  }

  Widget _buildFileUploadView() {
    return Container(
        width: 347.px,
        margin: EdgeInsets.only(top: 15.px),
        padding: EdgeInsets.only(
            left: 16.px, right: 16.px, top: 15.px, bottom: 15.px),
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(5))),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "流转协议",
              style: TextStyle(
                  fontSize: 15.px,
                  color: const Color.fromRGBO(41, 41, 52, 1),
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(
              height: 4.px,
            ),
            Text("请上传30M内的PDF、DOCX、TXT格式文件",
                style: TextStyle(
                    fontSize: 12.px,
                    color: const Color.fromRGBO(41, 41, 52, 0.6),
                    fontWeight: FontWeight.w400)),
            Container(
              width: 315.px,
              height: 50.px,
              margin: EdgeInsets.only(top: 10.px),
              decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage(
                        "assets/images/subsidy/ic_upload_btn_bg.png"),
                    fit: BoxFit.cover),
              ),
              child: TextButton.icon(
                  icon: Image(
                      width: 20.px,
                      height: 20.px,
                      image: const AssetImage(
                          "assets/images/subsidy/ic_upload.png")),
                  onPressed: () {
                    toSelectedFile();
                  },
                  style: TextButton.styleFrom(
                    side: const BorderSide(color: Colors.transparent, width: 0),
                    shape: const RoundedRectangleBorder(
                        side: BorderSide(
                            color: Color.fromRGBO(228, 231, 238, 1), width: 0),
                        borderRadius: BorderRadius.all(Radius.circular(8))),
                  ),
                  label: Text("选择文件",
                      style: TextStyle(
                          fontSize: 14.px,
                          color: const Color.fromRGBO(41, 41, 52, 0.8)))),
            ),
            uploadedFileList.isNotEmpty
                ? Container(
                    margin: EdgeInsets.only(top: 10.px),
                    child: Column(
                        children: uploadedFileList
                            .map((item) => _buildUploadedFileItems(item))
                            .toList()),
                  )
                : Container()
          ],
        ));
  }

  Widget _buildUploadedFileItems(SubsidyFileItemEntity item) {
    return Container(
        padding: EdgeInsets.only(top: 6.px, bottom: 6.px),
        child: Row(children: [
          Image(
              width: 20.px,
              height: 20.px,
              image:
                  const AssetImage("assets/images/subsidy/ic_file_mark.png")),
          GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              toDownloadFile(item);
            },
            child: SizedBox(
              width: 200.px,
              child: Text(item.fileName!,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      fontSize: 14.px,
                      color: const Color.fromRGBO(41, 41, 52, 0.8),
                      fontWeight: FontWeight.w400)),
            ),
          ),
          const Spacer(),
          item.delete == true
              ? GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    toRemoveFile(item);
                  },
                  child: Container(
                    alignment: Alignment.centerRight,
                    width: 40.px,
                    child: Text(
                      "删除",
                      style: TextStyle(
                          fontSize: 12.px,
                          color: const Color.fromRGBO(41, 41, 52, 0.6),
                          fontWeight: FontWeight.w400),
                    ),
                  ),
                )
              : Container()
        ]));
  }

  void _applySubsidy() {
    //需要把新上传的文件传过去，带过来的文件不用传
    showCustomBusyDialog(context, msg: "提交中..");
    bool allFileIdsNotEmpty =
        uploadedFileList.every((item) => item.fileId != null);
    if (uploadedFileList.isEmpty || allFileIdsNotEmpty) {
      Map<String, Object> params = {
        "fddContractNo": widget.dataItem.fddContractNo!,
      };
      poll();
      SubsidyService.toApplySubsidy(params).then((result) {
        Navigator.of(context).pop();
        if (result.success == true) {
          NativeUtil.openFdd(result.data);
          showToast("提交成功");
        } else {
          showToast("提交失败");
        }
      }, onError: (error) {
        Navigator.of(context).pop();
        showToast("提交失败");
      });
    } else {
      String jsonData = jsonEncode(
          uploadedFileList.where((item) => item.fileId == null).map((item) {
        var json = item.toJson();
        json.remove('params');
        json.remove('delete');
        return json;
      }).toList());

      //   final encoder = JsonEncoder.withIndent('  ');
      // print('完整JSON数据:');
      // print(encoder.convert(json.decode(jsonData)));
      SubsidyService.toSaveSubsidyAttachments(jsonData).then((result) {
        if (result.success == true) {
          Map<String, Object> params = {
            "fddContractNo": widget.dataItem.fddContractNo!,
          };
          poll();
          SubsidyService.toApplySubsidy(params).then((result) {
            Navigator.of(context).pop();
            if (result.success == true) {
              NativeUtil.openFdd(result.data);
              showToast("提交成功");
            } else {
              showToast("提交失败");
            }
          }, onError: (error) {
            Navigator.of(context).pop();
            showToast("提交失败");
          });
        } else {
          Navigator.of(context).pop();
          showToast("提交失败");
        }
      }, onError: (error) {
        Navigator.of(context).pop();
        showToast("提交失败");
      });
    }
  }

  /// 轮巡查询签署状态
  poll() {
    var accountId = StorageUtil.userInfo()?.data?.id;
    // currentSignModel.fddContractNo
    var contractNo = widget.dataItem.fddContractNo!;
    loopTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      count = count + 1;
      if (count > 200) {
        timer.cancel();
      }
      IntradomainSignService.contractAuthStatus(contractNo, accountId)
          .then((res) {
        if (res.code == 0 && res.data == '已签') {
          timer.cancel();
          if (Platform.isIOS) {
            Navigator.of(context).pop();
          } else if (Platform.isAndroid) {
            NativeUtil.closeFdd();
          }
          bus.emit("subsidy_list_refresh");
          Navigator.of(context).popUntil((route) =>
              route is MaterialPageRoute &&
              route.builder(context) is SubsidyListPage);
        }
      });
    });
  }

  void toSelectedFile() async {
    var status = await Permission.storage.status;
    if (!status.isGranted) {
      await Permission.storage.request().then((PermissionStatus status) {
        if (status.isGranted) {
          toSelectedFile();
        } else {
          showToast("请允许访问文件权限");
        }
      });
    } else {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        allowMultiple: false,
        type: FileType.custom,
        allowedExtensions: ['txt', 'pdf', 'docx'],
      );
      if (result != null) {
        File file = File(result.files.single.path!);
        if (file.lengthSync() > 30 * 1024 * 1024) {
          showToast("文件大小不能超过30M");
          return;
        }
        showCustomBusyDialog(context);
        toUploadFile(file);
      } else {
        // User canceled the picker
      }
    }
  }

  void toRemoveFile(SubsidyFileItemEntity item) {
    if (item.fileId == null) {
      setState(() {
        uploadedFileList.remove(item);
        showToast("删除成功");
      });
      return;
    }
    var ids = [item.fileId];
    String jsonData = jsonEncode(ids);
    SubsidyService.toSubsidyDelfile(jsonData).then((result) {
      if (result.success == true) {
        showToast("删除成功");
        setState(() {
          uploadedFileList
              .removeWhere((element) => element.fileId == item.fileId);
        });
      }
    });
  }

  void toUploadFile(File file) async {
    // String base64Data = await fileToBase64(file.path);
    String fileName = file.path.split("/").last;
    String? mimeType = lookupMimeType(file.path);
    var formData = FormData.fromMap({
      "file": await MultipartFile.fromFile(file.path,
          filename: fileName,
          contentType: MediaType.parse(mimeType ?? 'application/octet-stream')),
    });

    SubsidyService.toUploadFileData(formData).then((result) {
      Navigator.of(context).pop();
      if (result.success == true) {
        setState(() {
          var attachment = SubsidyFileItemEntity();
          attachment.fileName = fileName;
          attachment.serviceId =
              double.tryParse(widget.dataItem.lcSubsidyApplyId!) ?? 0.0;
          attachment.fileUrl = result.data;
          attachment.delete = true;
          uploadedFileList.add(attachment);
          // _querySubsidyUploadedFile();
        });
      }
    }, onError: (error) {
      Navigator.of(context).pop();
      showToast("文件上传失败");
    });
  }

  // Future<String> fileToBase64(String filePath) async {
  //   File file = File(filePath);
  //   List<int> bytes = await file.readAsBytes();
  //   return base64Encode(bytes);
  // }

  Future<void> showCustomBusyDialog(BuildContext context,
      {String msg = '上传中...'}) async {
    await showGeneralDialog(
      context: context,
      barrierDismissible: false, // 设置用户是否可以点击遮罩层来关闭对话框
      barrierLabel: msg, // 遮罩层的提示文字
      barrierColor: Colors.black.withOpacity(0.5), // 遮罩层的颜色和透明度
      transitionDuration: const Duration(milliseconds: 150), // 动画时长
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return Center(
          // 对话框内容居中显示
          child: Container(
            alignment: Alignment.center,
            width: 120.px,
            height: 120.px,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.px),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    width: 40.px,
                    height: 40.px,
                    child: const CircularProgressIndicator()),
                SizedBox(height: 16.px),
                Text(msg,
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(44, 44, 52, 0.8)))
              ],
            ),
          ), // 加载指示器
        );
      },
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    loopTimer?.cancel();
    super.dispose();
  }

  void _querySubsidyDetails() async {
    Map<String, Object> params = {
      "lcSubsidyApplyId": widget.dataItem.lcSubsidyApplyId!,
    };
    await SubsidyService.querySubsidyDetail(params).then((result) {
      if (mounted) {
        if (result.success == true) {
          SubsidyItemInfoEntity record =
              SubsidyItemInfoEntity.fromJson(result.data);
          setState(() {
            if (record.records != null && record.records!.isNotEmpty) {
              subsidyItemInfoRecords = record.records!;
            }
          });
        }
      }
    });
  }

  void _querySubsidyUploadedFile() async {
    String serviceId = widget.dataItem.lcSubsidyApplyId!;
    Map<String, dynamic> params = {"serviceId": serviceId, "pageNo": 1000};
    await SubsidyService.querySubsidyUploadedFile(params).then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            uploadedFileList.clear();
            List records = result.data["records"];
            if (records.isNotEmpty) {
              List<SubsidyFileItemEntity> uploadedFilesArr = records
                  .map((e) => SubsidyFileItemEntity.fromJson(e))
                  .toList();
              uploadedFileList = uploadedFilesArr;
            }
          });
        }
      }
    });
  }

  void _getBankNameDicts() async {
    await SubsidyService.getDicByKey("bank_name").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            bankNameTypes = result.data!;
          });
        }
      }
    });

    await SubsidyService.getDicByKey("sys_subsidy_crop_code").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            subsidyCropCodeTypes = result.data!;
          });
        }
      }
    });
  }

  String getBankNameTitle(String type) {
    for (var item in bankNameTypes) {
      if (item.code == type) {
        return item.name!;
      }
    }
    return "";
  }

  String getSubsidyCropCodeType(String type) {
    for (var item in subsidyCropCodeTypes) {
      if (item.code == type) {
        return item.name!;
      }
    }
    return "";
  }

  void toDownloadFile(SubsidyFileItemEntity item) {
    showCustomBusyDialog(context, msg: "下载中..");
    if (item.fileUrl != null && item.fileUrl!.isNotEmpty) {
      String savePath = Directory.systemTemp.path + item.fileName!;
      Dio _dio = Dio();
      _dio.download(
        item.fileUrl!,
        savePath,
        onReceiveProgress: (received, total) {
          if (total != -1) {
            print((received / total * 100).toStringAsFixed(0) + "%");
          }
        },
      ).then((value) {
        print(value);
      }).whenComplete(() {
        Navigator.of(context).pop();
        print('下载结束');
        toOpenFile(savePath);
      }).catchError((onError) {
        Navigator.of(context).pop();
        print(onError);
      });
    }
  }

  void toOpenFile(String savePath) async {
    if (savePath.isNotEmpty) {
      var types = {};
      if (Platform.isAndroid) {
        types = {
          ".docx":
              "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          ".pdf": "application/pdf",
          ".txt": "text/plain",
        };
      } else if (Platform.isIOS) {
        types = {
          ".docx": "com.microsoft.word.docx",
          ".pdf": "com.adobe.pdf",
          ".txt": "public.plain-text"
        };
      }
      File file = File(savePath);
      if (file.existsSync()) {
        var status = await Permission.storage.status;
        if (!status.isGranted) {
          await Permission.storage.request().then((PermissionStatus status) {
            if (status.isGranted) {
              OpenFilex.open(savePath, type: types[file.path.split(".").last]);
            } else {
              showToast("请允许访问文件权限");
            }
          });
        } else {
          await OpenFilex.open(savePath,
              type: types[file.path.split(".").last]);
        }
      }
    }
  }

  void loadData() async {
    BdhLandResponsitory.getICCardList({"lcSubsidyApplyId":widget.dataItem.lcSubsidyApplyId}).then((value) {
      if (value.code == 0) {
        icCardList = value.data ?? [];
        setState(() {

        });
      }
    });
  }

  void updateICCard() async {
    BdhLandResponsitory.updateBankCard({
      "bankAccount":selectBankNumber,
      "lcSubsidyApplyId":widget.dataItem.lcSubsidyApplyId,
      "bankName":widget.dataItem.bankName,
      "network":widget.dataItem.network,
      "lineNumber":widget.dataItem.lineNumber,
    });
  }

  List<DropdownMenuItem<String>> _buildMenuList(List<IcCardInfoModel> data) {
    return data.map((IcCardInfoModel value) {
      return DropdownMenuItem(value: value.bankAccount.toString(),  child: Text(value.bankAccount.toString(), style: TextStyle(fontSize: 14.px)));
    }).toList();
  }
}
