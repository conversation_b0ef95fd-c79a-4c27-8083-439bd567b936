import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_publicity_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_publicity_detail_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../utils/color_util.dart';
import '../../../utils/event_bus.dart';
import '../../../utils/request/subsidy_service.dart';
import '../../message/bdh_empty_View.dart';

// subsidyAnnounceType :1-补贴发放,2-补贴申请申请
class SubsidyPublicityListPage extends StatefulWidget {
  const SubsidyPublicityListPage({super.key});

  @override
  State<SubsidyPublicityListPage> createState() =>
      _SubsidyPublicityListPageState();
}

class _SubsidyPublicityListPageState extends State<SubsidyPublicityListPage> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  List<SubsidyPublicityEntity> dataRecords = [];
  int pageNum = 1;
  int pageSize = 10;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    setState(() {
      _isLoading = true;
    });
    _onRefresh();
    bus.on('subsidy_publicity_list_refresh', (e) {
      _onRefresh();
    });
  }

  @override
  void dispose() {
    bus.off("subsidy_publicity_list_refresh");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("补贴公示"),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: Container(
        margin: EdgeInsets.only(left: 10.px, right: 10.px),
        width: 355.px,
        child: dataRecords.isEmpty
            ? Center(
                child: _isLoading
                    ? const SpinKitCircle(
                        // color: HexColor('#16B760'),
                        color: Color.fromRGBO(0, 127, 255, 1),
                        size: 60.0,
                      )
                    : const BdhEmptyView(),
              )
            : SmartRefresher(
                enablePullUp: true,
                onRefresh: _onRefresh,
                onLoading: null,
                controller: _refreshController,
                child: ListView.builder(
                    itemCount: dataRecords.length,
                    itemBuilder: (ctx, idx) {
                      return _buildNewsItem(dataRecords[idx]);
                    }),
              ),
      ),
    );
  }

  Widget _buildNewsItem(SubsidyPublicityEntity item) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => SubsidyPublicityDetailPage(
                dateItem: item,
              ),
            ),
          );
        },
        child: Stack(children: [
          Container(
              margin: EdgeInsets.only(top: 10.px),
              padding: EdgeInsets.only(
                  right: 8.px, left: 10.px, bottom: 10.px, top: 10.px),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10.px),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // subsidyAnnounceType :1-补贴发放,2-补贴申请申请
                  Container(
                    margin: EdgeInsets.only(top: 2.px),
                    child: Image.asset(
                      item.subsidyAnnounceType == "1"
                          ? 'assets/images/subsidy/ff.png'
                          : 'assets/images/subsidy/sq.png',
                      width: 29.px,
                      height: 15.px,
                      fit: BoxFit.contain,
                    ),
                  ),
                  SizedBox(
                    width: 8.px,
                  ),
                  Container(
                      width: 260.px,
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${item.announceTitle} (${item.subsidyItemName!})",
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              style: TextStyle(
                                  fontSize: 16.px,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black),
                            ),
                            SizedBox(
                              height: 8.px,
                            ),
                            Text(
                              item.organizationName!,
                              style: TextStyle(
                                  fontSize: 12.px,
                                  color:
                                      const Color.fromRGBO(153, 153, 153, 1)),
                            )
                          ])),
                ],
              )),
          Positioned(
            top: 10.px,
            right: 0.px,
            child: Image.asset(
              item.feedbackStatus == 0
                  ? 'assets/images/subsidy/wqr.png'
                  : 'assets/images/subsidy/yqr.png',
              width: 44.px,
              height: 18.px,
              fit: BoxFit.contain,
            ),
          )
        ]));
  }

  String getDateFormat(int timestamp) {
    // 转换为 DateTime
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    // 格式化
    String formattedTime = DateFormat('yyyy-MM-dd').format(dateTime);
    return formattedTime;
  }

  void _onRefresh() async {
    _getDataRecords();
  }

  void _onLoadMore() async {
    _getDataRecords();
  }

  void _getDataRecords() async {
    await SubsidyService.queryAnnounceList({}).then((result) {
      setState(() {
        _isLoading = false;
      });
      if (result.success == true) {
        setState(() {
          List<SubsidyPublicityEntity> records = (result.data as List<dynamic>)
              .map((item) => SubsidyPublicityEntity.fromJson(item))
              .toList();
          dataRecords = records;
          _refreshController.refreshCompleted();
          _refreshController.loadComplete();
          _refreshController.loadNoData();
        });
      }
    });
  }
}
