import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:intl/intl.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../../../utils/color_util.dart';
import '../../../utils/request/subsidy_service.dart';
import 'entity/new_detail_entity.dart';

class SubsidyNewsDetailPage extends StatefulWidget {
  final String newsId;

  final String newsTitle;

  const SubsidyNewsDetailPage(
      {super.key, required this.newsId, required this.newsTitle});

  @override
  State<SubsidyNewsDetailPage> createState() => _SubsidyNewsDetailPageState();
}

class _SubsidyNewsDetailPageState extends State<SubsidyNewsDetailPage> {
  NewDetailEntity? newsDetailEntity;
  late final WebViewController _controller;

  @override
  void initState() {
    super.initState();
    _getNewsDetail();

    PlatformWebViewControllerCreationParams params =
        const PlatformWebViewControllerCreationParams();
    final WebViewController controller =
        WebViewController.fromPlatformCreationParams(params);
    // #enddocregion platform_features

    controller
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..addJavaScriptChannel(
        'Toaster',
        onMessageReceived: (JavaScriptMessage message) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(message.message)),
          );
        },
      );
    _controller = controller;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text("详情",
              style: TextStyle(color: Colors.black, fontSize: 16)),
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: Container(
            margin: EdgeInsets.only(
                left: 10.px, right: 10.px, top: 10.px, bottom: 15.px),
            padding: EdgeInsets.all(10.px),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10),
            ),
            child: newsDetailEntity == null
                ? const Center(
                    child: SpinKitCircle(
                      // color: HexColor('#16B760'),
                      color: Color.fromRGBO(0, 127, 255, 1),
                      size: 60.0,
                    ),
                  )
                : Column(children: [
                    Text(
                      newsDetailEntity!.title ?? "",
                      style: const TextStyle(
                          fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Text(
                      getDateFormat(newsDetailEntity!.releaseTime!),
                      style: const TextStyle(
                          fontSize: 14,
                          color: Color.fromRGBO(41, 41, 52, 0.4),
                          fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        child: HtmlWidget(
                          newsDetailEntity!.releaseContent!,
                          textStyle: const TextStyle(
                            fontSize: 15,
                          ),
                        ),
                      ),
                    )
                  ])));
  }

  String getDateFormat(int timestamp) {
    // 转换为 DateTime
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    // 格式化
    String formattedTime = DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
    return formattedTime;
  }

  void _getNewsDetail() async {
    await SubsidyService.getAppNewsDetail(widget.newsId).then((result) {
      if (result.success == true) {
        setState(() {
          newsDetailEntity = NewDetailEntity.fromJson(result.data);
          // if (newsDetailEntity!.releaseContent!.isNotEmpty) {
          //   _controller.loadHtmlString(newsDetailEntity!.releaseContent!);
          // }
        });
      }
    });
  }
}
