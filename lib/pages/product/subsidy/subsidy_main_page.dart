import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/new_category_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/news_of_subsidy_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_of_mine_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_detail_page.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_list_page.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_news_detail_page.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_news_list_page.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_publicity_list_page.dart';
import 'package:bdh_smart_agric_app/pages/version/bdh_newversion_view.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../model/dict_tree_model.dart';
import '../../../utils/request/subsidy_service.dart';
import 'entity/subsidy_publicity_entity.dart';

///
/// 我的补贴 -- 主界面
///
class SubsidyMainPage extends StatefulWidget {
  const SubsidyMainPage({super.key});

  @override
  State<SubsidyMainPage> createState() => _SubsidyMainPageState();
}

class _SubsidyMainPageState extends State<SubsidyMainPage>
    with SingleTickerProviderStateMixin {
  final List<String> _tabs = ["政策法规", "工作状态"];
  late TabController _tabController;
  FixedExtentScrollController? yearSelectionController;
  int _currentYearIndex = -1;
  int _currentSelectedYearIndex = -1;
  String _currentYear = "";
  SubsidyOfMineEntity? subsidyOfMine;
  List<DictNode> subsidyYears = [];
  List<NewCategoryEntity>? subNewCategoryItems = [];
  List<NewsOfSubsidyRecords> newsOfSubsidyRecords = [];
  NewCategoryEntity? currentNewsCategory;
  List<SubsidyPublicityEntity> subsidyPublicityRecords = []; // 补贴公示
  PackageInfo? initPackageInfo;

  CarouselController? carouselController;

  @override
  void initState() {
    super.initState();
    setState(() {
      _currentYear = DateTime.now().year.toString();
    });
    GetCurrentInstallVersion.initPackageInfo().then((res) {
      setState(() {
        initPackageInfo = res;
      });
    });
    carouselController = CarouselController();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _getNewCategoryTypes();
    _getSubsidyYears();
    _getMineSubsidyDetail();
    _queryAnnounceList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(
            height: 344.px,
            width: 375.px,
            child: Stack(children: [
              Positioned(
                  top: 0,
                  left: 0,
                  child: Container(
                      height: 336.px,
                      width: 375.px,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        image: AssetImage(
                          "assets/images/subsidy/ic_subsidy_top_bg.png",
                        ),
                        fit: BoxFit.cover,
                      )))),
              Positioned(
                  top: 42.px,
                  left: 8.px,
                  child: BackButton(
                    color: Colors.white,
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  )),
              Positioned(
                top: 116.px,
                left: 12.px,
                child: TextButton.icon(
                  iconAlignment: IconAlignment.end,
                  onPressed: () {
                    showModalBottomSheet(
                        context: context,
                        elevation: 10,
                        enableDrag: false,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.px)),
                        builder: (BuildContext context) {
                          return showBottomSelectYearsPicker();
                        });
                  },
                  label: Text(
                    "$_currentYear年",
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w400),
                  ),
                  icon: const Icon(
                    Icons.arrow_drop_down,
                    size: 24,
                    color: Colors.white,
                  ),
                ),
              ),
              Positioned(
                  left: 12.px,
                  bottom: 0.px,
                  child: Container(
                    width: 351.px,
                    height: 182.px,
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                            image: AssetImage(
                                "assets/images/subsidy/ic_amount_bg.png"),
                            fit: BoxFit.cover)),
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            height: 16.px,
                          ),
                          Text(
                            "当年补贴总额(元)",
                            style: TextStyle(
                                color: const Color.fromRGBO(41, 41, 52, 0.9),
                                fontSize: 14.px,
                                fontWeight: FontWeight.w500),
                          ),
                          Text(
                            "${subsidyOfMine != null ? subsidyOfMine!.total?.toStringAsFixed(2) : "0.00"}",
                            style: TextStyle(
                                color: const Color.fromRGBO(41, 41, 52, 1),
                                fontSize: 42.px,
                                fontWeight: FontWeight.w500),
                          ),
                          Text(
                            "${subsidyOfMine != null ? subsidyOfMine!.paymentTime : ""}",
                            style: TextStyle(
                                color: const Color.fromRGBO(41, 41, 52, 0.4),
                                fontSize: 14.px,
                                fontWeight: FontWeight.w400),
                          ),
                          SizedBox(
                            height: 10.px,
                          ),
                          SizedBox(
                            width: 200.px,
                            height: 38.px,
                            child: GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) =>
                                        const SubsidyListPage(),
                                  ),
                                );
                              },
                              child: Container(
                                  alignment: Alignment.center,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(19.px),
                                    gradient: const LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        colors: [
                                          Color.fromRGBO(44, 184, 116, 1),
                                          Color.fromRGBO(2, 139, 93, 1)
                                        ]),
                                  ),
                                  child: Text(
                                    "申请补贴",
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 15.px,
                                        fontWeight: FontWeight.w400),
                                  )),
                            ),
                          )
                        ]),
                  ))
            ]),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  SizedBox(
                    height: 10.px,
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () async {
                      var params = {"version": initPackageInfo?.version ?? ''};
                      final result = await SubsidyService.getVersion(params);
                      if (result.success == true) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) =>
                                const SubsidyPublicityListPage(),
                          ),
                        );
                      } else {
                        // 显示版本不匹配提示
                        showToast(result.data.msg);
                      }
                    },
                    child: Container(
                      height: 98.5.px,
                      width: 351.px,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                              image: AssetImage(
                                  "assets/images/subsidy/ic_subsidy_center_bg.png"),
                              fit: BoxFit.cover)),
                      child: Column(children: [
                        SizedBox(
                          height: 49.px,
                        ),
                        Container(
                          width: 327.px,
                          height: 35.px,
                          decoration: const BoxDecoration(
                              image: DecorationImage(
                                  image: AssetImage(
                                      "assets/images/subsidy/ic_notice_item_bg.png"),
                                  fit: BoxFit.cover)),
                          child: subsidyPublicityRecords.isEmpty
                              ? Center(
                                  child: Text("暂无公示",
                                      style: TextStyle(
                                        fontSize: 14.px,
                                        fontWeight: FontWeight.w400,
                                        color: const Color.fromRGBO(
                                            41, 41, 52, 0.4),
                                      )),
                                )
                              : Swiper(
                                  scrollDirection: Axis.vertical,
                                  autoplay: true,
                                  itemBuilder: (context, index) =>
                                      _buildSubsidyPublicityItem(
                                          subsidyPublicityRecords[index]),
                                  itemCount: subsidyPublicityRecords.length,
                                ),
                        )
                      ]),
                    ),
                  ),
                  SizedBox(
                    height: 8.px,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 180.px,
                        child: subNewCategoryItems != null &&
                                subNewCategoryItems!.isNotEmpty
                            ? TabBar(
                                onTap: (index) {
                                  _getNewsByCategoryType(
                                      subNewCategoryItems![index]);
                                },
                                padding: EdgeInsets.symmetric(horizontal: 0.px),
                                labelPadding:
                                    EdgeInsets.symmetric(horizontal: 10.px),
                                dividerColor: Colors.transparent,
                                controller: _tabController,
                                unselectedLabelColor:
                                    const Color.fromRGBO(41, 41, 52, 0.8),
                                labelColor: const Color.fromRGBO(41, 41, 52, 1),
                                indicatorColor:
                                    const Color.fromRGBO(2, 139, 93, 1),
                                indicatorSize: TabBarIndicatorSize.label,
                                indicatorWeight: 1,
                                indicatorPadding:
                                    EdgeInsets.symmetric(horizontal: 12.px),
                                labelStyle: TextStyle(
                                    fontSize: 16.px,
                                    fontWeight: FontWeight.w600,
                                    color: const Color.fromRGBO(41, 41, 52, 1)),
                                unselectedLabelStyle: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    color:
                                        const Color.fromRGBO(41, 41, 52, 0.8)),
                                tabs: subNewCategoryItems!
                                    .map((e) => Tab(
                                          child: Text(e.columnLevelName!),
                                        ))
                                    .toList())
                            : Container(),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SubsidyNewsListPage(
                                newsCategory: currentNewsCategory!,
                              ),
                            ),
                          );
                        },
                        child: const Text(
                          "更多",
                          style:
                              TextStyle(color: Color.fromRGBO(41, 41, 52, 0.4)),
                        ),
                      ),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 8.px),
                    padding: EdgeInsets.only(
                        left: 15.px, right: 12.px, top: 8.px, bottom: 8.px),
                    height: 76.px,
                    width: 351.px,
                    decoration: BoxDecoration(
                      color: const Color.fromRGBO(255, 255, 255, 1),
                      borderRadius: BorderRadius.circular(8.px),
                    ),
                    child: newsOfSubsidyRecords.isNotEmpty
                        ? Column(
                            children: newsOfSubsidyRecords
                                .map((item) => _buildNewsItem(item))
                                .toList(),
                          )
                        : Center(
                            child: Text(
                            "暂无新闻",
                            style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: const Color.fromRGBO(41, 41, 52, 0.4),
                            ),
                          )),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 10.px, left: 12.px),
                    alignment: Alignment.centerLeft,
                    child: Text(
                      "补贴明细",
                      style: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w600,
                        color: const Color.fromRGBO(41, 41, 52, 1),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 8.px,
                  ),
                  subsidyOfMine != null &&
                          subsidyOfMine!.details != null &&
                          subsidyOfMine!.details!.isNotEmpty
                      ? Column(
                          children: subsidyOfMine!.details!
                              .map((item) => _buildSubsidyItem(item))
                              .toList(),
                        )
                      : Container()
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNewsItem(NewsOfSubsidyRecords item) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SubsidyNewsDetailPage(
              newsTitle: item.title!,
              newsId: item.releaseInfoId!,
            ),
          ),
        );
      },
      child: Container(
        height: 30.px,
        alignment: Alignment.centerLeft,
        child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(
                  width: 290.px,
                  child: Text(
                    item.title!,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: 14.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(41, 41, 52, 1)),
                  )),
              const Spacer(),
              Icon(
                Icons.arrow_forward_ios,
                color: const Color.fromRGBO(139, 171, 160, 1),
                size: 16.px,
              ),
            ]),
      ),
    );
  }

  Widget _buildSubsidyPublicityItem(SubsidyPublicityEntity item) {
    return Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
      SizedBox(
        width: 12.px,
      ),
      Text(
        item.subsidyItemName ?? "",
        style: TextStyle(
            color: const Color.fromRGBO(41, 41, 52, 0.8),
            fontSize: 14.px,
            fontWeight: FontWeight.w500),
      ),
      const Spacer(),
      Icon(
        Icons.arrow_forward_ios,
        color: const Color.fromRGBO(139, 171, 160, 1),
        size: 16.px,
      ),
      SizedBox(
        width: 8.px,
      )
    ]);
  }

  Widget _buildSubsidyItem(SubsidyOfMineDetails item) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SubsidyDetailPage(
              dataItem: item,
              year: _currentYear,
            ),
          ),
        );
      },
      child: Container(
        width: 351.px,
        padding: EdgeInsets.all(14.px),
        margin: EdgeInsets.only(bottom: 10.px),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(255, 255, 255, 1),
          borderRadius: BorderRadius.circular(8.px),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 200.px,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.subsidyItemName!,
                      style: TextStyle(
                          fontSize: 15.px,
                          fontWeight: FontWeight.w600,
                          color: const Color.fromRGBO(41, 41, 52, 1)),
                    ),
                    Text(item.organizationName!,
                        maxLines: 4,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 12.px,
                            color: const Color.fromRGBO(41, 41, 52, 0.6))),
                    SizedBox(
                      height: 5.px,
                    ),
                    Text(item.paymentTime!,
                        style: TextStyle(
                            fontSize: 12.px,
                            fontWeight: FontWeight.w400,
                            color: const Color.fromRGBO(41, 41, 52, 0.6)))
                  ]),
            ),
            const Spacer(),
            Text(
              "${item.actualPaymentFee?.toStringAsFixed(0)}",
              style: TextStyle(
                  fontSize: 25.px,
                  fontWeight: FontWeight.w500,
                  color: const Color.fromRGBO(2, 139, 93, 1)),
            )
          ],
        ),
      ),
    );
  }

  Widget showBottomSelectYearsPicker() {
    if (_currentYearIndex == -1) {
      _currentYearIndex =
          subsidyYears.indexWhere((item) => item.name == _currentYear);
      _currentSelectedYearIndex = _currentYearIndex;
      yearSelectionController =
          FixedExtentScrollController(initialItem: _currentYearIndex);
    } else {
      yearSelectionController =
          FixedExtentScrollController(initialItem: _currentYearIndex);
    }
    return BottomSheet(
        enableDrag: false,
        onClosing: () {},
        builder: (BuildContext context) {
          return Container(
              padding: EdgeInsets.only(left: 10.px, right: 10.px, top: 10.px),
              height: 280.px,
              width: 375.px,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "取消",
                              style: TextStyle(
                                  fontSize: 16.px, color: Colors.redAccent),
                            )),
                        Text(
                          "选择年份",
                          style: TextStyle(
                              color: const Color.fromRGBO(44, 44, 52, 1),
                              fontSize: 18.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextButton(
                            onPressed: () {
                              setState(() {
                                _currentYearIndex = _currentSelectedYearIndex;
                                _currentYear =
                                    subsidyYears[_currentYearIndex].name!;
                              });
                              _getMineSubsidyDetail();
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "确定",
                              style: TextStyle(
                                  color: Colors.blueAccent, fontSize: 16.px),
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 220.px,
                      child: CupertinoPicker(
                          scrollController: yearSelectionController,
                          itemExtent: 40.px,
                          squeeze: 1.5,
                          diameterRatio: 1,
                          onSelectedItemChanged: (index) {
                            setState(() {
                              _currentSelectedYearIndex = index;
                            });
                          },
                          children: subsidyYears
                              .map((item) => Center(
                                    child: Text(item.name!),
                                  ))
                              .toList()),
                    )
                  ]));
        });
  }

  Future<void> showCustomBusyDialog(BuildContext context) async {
    await showGeneralDialog(
      context: context,
      barrierDismissible: false, // 设置用户是否可以点击遮罩层来关闭对话框
      barrierLabel: '加载中...', // 遮罩层的提示文字
      barrierColor: Colors.black.withOpacity(0.5), // 遮罩层的颜色和透明度
      transitionDuration: const Duration(milliseconds: 150), // 动画时长
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return Center(
          // 对话框内容居中显示
          child: Container(
            alignment: Alignment.center,
            width: 120.px,
            height: 120.px,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.px),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    width: 40.px,
                    height: 40.px,
                    child: const CircularProgressIndicator()),
                SizedBox(height: 16.px),
                Text("加载中...",
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(44, 44, 52, 0.8)))
              ],
            ),
          ), // 加载指示器
        );
      },
    );
  }

  void _getNewCategoryTypes() async {
    Map<String, Object> params2 = {"systemCode": 'systemlandcontract'};
    await SubsidyService.queryColumnApp(params2).then((result) {
      if (result.success == true) {
        List<NewCategoryEntity> newCategory = (result.data as List<dynamic>)
            .map((item) => NewCategoryEntity.fromJson(item))
            .toList();
        if (newCategory.isNotEmpty) {
          Map<String, Object> params = {
            "columnLevelId": newCategory.first.columnLevelId!,
          };
          SubsidyService.queryColumn2App(params).then((result) {
            if (result.success == true) {
              List<NewCategoryEntity> subNewCategory =
                  (result.data as List<dynamic>)
                      .map((item) => NewCategoryEntity.fromJson(item))
                      .toList();
              setState(() {
                subNewCategoryItems = subNewCategory;
                currentNewsCategory = subNewCategoryItems!.first;
                _getNewsByCategoryType(currentNewsCategory!);
              });
            }
          });
        }
      }
    });
  }

  void _getNewsByCategoryType(NewCategoryEntity item) async {
    Map<String, Object> params = {
      "systemCode": "systemlandcontract",
      "columnLevelId": item.columnLevelId!,
      "orgType": 1
    };
    currentNewsCategory = item;
    showCustomBusyDialog(context);
    await SubsidyService.getAppNewsByPage(params).then((result) {
      Navigator.of(context).pop();
      if (result.success == true) {
        setState(() {
          NewsOfSubsidyEntity newsOfSubsidyEntity =
              NewsOfSubsidyEntity.fromJson(result.data);
          if (newsOfSubsidyEntity.records != null) {
            if (newsOfSubsidyEntity.records!.length > 2) {
              newsOfSubsidyRecords = newsOfSubsidyEntity.records!.sublist(0, 2);
            } else {
              newsOfSubsidyRecords = newsOfSubsidyEntity.records!;
            }
          }
        });
      }
    });
  }

  void _getMineSubsidyDetail() async {
    Map<String, Object> params = {
      "subsidyYear": _currentYear,
    };
    await SubsidyService.mySubsidyPaymentDetail(params).then((result) {
      if (result.success == true) {
        setState(() {
          subsidyOfMine = SubsidyOfMineEntity.fromJson(result.data);
          // SubsidyOfMineDetails item = subsidyOfMine!.details!.first;
          // subsidyOfMine!.details!.add(item);
          // subsidyOfMine!.details!.add(item);
          // subsidyOfMine!.details!.add(item);
        });
      }
    });
  }

  /// 获取补贴公示
  void _queryAnnounceList() async {
    await SubsidyService.queryAnnounceList({}).then((result) {
      if (result.success == true) {
        setState(() {
          List<SubsidyPublicityEntity> records = (result.data as List<dynamic>)
              .map((item) => SubsidyPublicityEntity.fromJson(item))
              .toList();
          subsidyPublicityRecords = records;
        });
      }
    });
  }

  void _getSubsidyYears() async {
    await SubsidyService.getDicByKey("year_cd").then((result) {
      if (result.success == true) {
        setState(() {
          subsidyYears = result.data!;
        });
      }
    });
  }
}
