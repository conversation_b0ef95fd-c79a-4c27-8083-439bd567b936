import 'dart:convert';

import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_apply_info_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_item_widget.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/subsidy_sign/subsidy_sign_apply_page.dart';
import 'package:bdh_smart_agric_app/utils/request/subsidy_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../model/dict_tree_model.dart';
import '../../../utils/color_util.dart';
import '../../../utils/event_bus.dart';
import '../../../utils/native_util.dart';
import '../../message/bdh_empty_View.dart';

class SubsidyListPage extends StatefulWidget {
  const SubsidyListPage({super.key});

  @override
  State<SubsidyListPage> createState() => _SubsidyListPageState();
}

class _SubsidyListPageState extends State<SubsidyListPage> {
  List<SubsidyApplyInfoEntity> dataRecords = [];
  List<DictNode> subsidyTypes = [];

  bool isShowSignBtn = false;
  bool isShowActionBtn = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _getSubsidySignStatus();
    _getSubsidyTypes();
    _getMineOfSubsidy(context);
    bus.on('subsidy_list_refresh', (e) {
      _getMineOfSubsidy(context);
    });
  }

  @override
  void dispose() {
    bus.off('subsidy_list_refresh');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: Container(
            width: 375.px,
            decoration: const BoxDecoration(
                image: DecorationImage(
              image: AssetImage("assets/images/subsidy/ic_subsidy_bg.png"),
              fit: BoxFit.cover,
            )),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 36.px, left: 8.px),
                  child: BackButton(
                    color: Colors.white,
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                ),
                Expanded(
                  child: dataRecords.isEmpty
                      ? Center(
                          child: _isLoading
                              ? const SpinKitCircle(
                                  // color: HexColor('#16B760'),
                                  color: Color.fromRGBO(0, 127, 255, 1),
                                  size: 60.0,
                                )
                              : const BdhEmptyView(),
                        )
                      : Container(
                          margin: EdgeInsets.only(top: 92.px, left: 16.px),
                          child: SingleChildScrollView(
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: dataRecords.map((item) {
                                    return SubsidyItemWidget(
                                        dataItem: item,
                                        subsidyType: getSubsidyTypeTitle(
                                            item.subsidyType!),
                                        onSelectChanged: (isSelected) {
                                          handleItemSelectChanged(
                                              item, isSelected!);
                                        });
                                  }).toList())),
                        ),
                ),
                isShowActionBtn
                    ? Container(
                        padding: EdgeInsets.only(
                            bottom: 30.px,
                            top: 10.px,
                            right: 16.px,
                            left: 16.px),
                        height: 90.px,
                        width: 375.px,
                        color: Colors.white,
                        child: ElevatedButton(
                            onPressed: () {
                              if (isShowSignBtn) {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => SubsidySignApplyPage(
                                      dataItem: dataRecords
                                          .where((it) => it.isSelected == true)
                                          .first,
                                    ),
                                  ),
                                );
                              } else {
                                _getApplyContract(dataRecords
                                    .where((it) => it.isSelected == true)
                                    .first);
                              }
                            },
                            style: TextButton.styleFrom(
                              backgroundColor:
                                  const Color.fromRGBO(2, 139, 93, 1),
                              foregroundColor: Colors.white,
                              side: const BorderSide(
                                  color: Color.fromRGBO(2, 139, 93, 1),
                                  width: 1),
                              shape: const RoundedRectangleBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10))),
                            ),
                            child: Text(isShowSignBtn ? "签  订" : "查看申请书",
                                style: TextStyle(fontSize: 16.px))))
                    : Container()
              ],
            )
            // Column(
            //     crossAxisAlignment: CrossAxisAlignment.center,
            //     mainAxisAlignment: MainAxisAlignment.start,
            //     children: [
            //       Container(
            //         width: 375.px,
            //         decoration: const BoxDecoration(
            //             image: DecorationImage(
            //           image: AssetImage("assets/images/subsidy/ic_subsidy_bg.png"),
            //           fit: BoxFit.cover,
            //         )),
            //         child: SingleChildScrollView(
            //             child: Column(
            //                 crossAxisAlignment: CrossAxisAlignment.center,
            //                 children: dataRecords.map((item) {
            //                   return SubsidyItemWidget(
            //                       dataItem: item,
            //                       subsidyType: getSubsidyTypeTitle(item.subsidyType!),
            //                       onSelectChanged: (isSelected) {
            //                         handleItemSelectChanged(item, isSelected!);
            //                       });
            //                 }).toList())),
            //       ),
            //       // SizedBox(
            //       //     height: 650.px,
            //       //     width: 375.px,
            //       //     child: Stack(fit: StackFit.expand, children: [
            //       //       Positioned(
            //       //           top: 0,
            //       //           left: 0,
            //       //           child: ),
            //       //       Positioned(
            //       //           top: 42.px,
            //       //           left: 8.px,
            //       //           child: BackButton(
            //       //             color: Colors.white,
            //       //             onPressed: () {
            //       //               Navigator.pop(context);
            //       //             },
            //       //           )),
            //       //       Positioned(
            //       //         top: 184.px,
            //       //         left: 16.px,
            //       //         child: SingleChildScrollView(
            //       //             child: Column(
            //       //                 crossAxisAlignment: CrossAxisAlignment.center,
            //       //                 children: dataRecords.map((item) {
            //       //                   return SubsidyItemWidget(
            //       //                       dataItem: item,
            //       //                       subsidyType:
            //       //                           getSubsidyTypeTitle(item.subsidyType!),
            //       //                       onSelectChanged: (isSelected) {
            //       //                         handleItemSelectChanged(item, isSelected!);
            //       //                       });
            //       //                 }).toList())),
            //       //       )
            //       //     ])),
            //       const Spacer(),
            //       isShowActionBtn
            //           ? Container(
            //               padding: EdgeInsets.only(
            //                   bottom: 30.px, top: 10.px, right: 16.px, left: 16.px),
            //               height: 90.px,
            //               width: 375.px,
            //               color: Colors.white,
            //               child: ElevatedButton(
            //                   onPressed: () {
            //                     Navigator.push(
            //                       context,
            //                       MaterialPageRoute(
            //                         builder: (context) => SubsidySignApplyPage(
            //                           dataItem: dataRecords
            //                               .where((it) => it.isSelected == true)
            //                               .first,
            //                         ),
            //                       ),
            //                     );
            //                   },
            //                   style: TextButton.styleFrom(
            //                     backgroundColor: const Color.fromRGBO(2, 139, 93, 1),
            //                     foregroundColor: Colors.white,
            //                     side: const BorderSide(
            //                         color: Color.fromRGBO(2, 139, 93, 1), width: 1),
            //                     shape: const RoundedRectangleBorder(
            //                         borderRadius:
            //                             BorderRadius.all(Radius.circular(10))),
            //                   ),
            //                   child: Text(isShowSignBtn ? "签  订" : "查看申请书",
            //                       style: TextStyle(fontSize: 16.px))))
            //           : Container()
            //     ]),
            ));
  }

  /// 查看申请书
  void _getApplyContract(SubsidyApplyInfoEntity item) {
    Map<String, Object> params = {
      "fddContractNo": item.fddContractNo!,
      "mobileDevice": true
    };
    SubsidyService.toApplyContract(params).then((result) {
      if (result.success == true) {
        if (result.data != null) {
          if (result.data!.length > 0) {
            NativeUtil.openFdd(result.data);
          }
        }
      }
    });
  }

  void _getSubsidyTypes() async {
    await SubsidyService.getDicByKey("sys_subsidy_type").then((result) {
      if (result.success == true) {
        setState(() {
          subsidyTypes = result.data!;
        });
      }
    });
  }

  void _getSubsidySignStatus() async {
    await SubsidyService.getDicByKey("agreement_sign_no").then((result) {
      if (result.success == true) {
        setState(() {});
      }
    });
  }

  String getSubsidyTypeTitle(String type) {
    for (var element in subsidyTypes) {
      if (element.code == type) {
        return element.name!;
      }
    }
    return "";
  }

  void _getMineOfSubsidy(BuildContext context) async {
    setState(() {
      _isLoading = true;
    });
    await SubsidyService.getMySubsidyList({}).then((result) {
      setState(() {
        _isLoading = false;
      });
      if (result.success == true) {
        List<SubsidyApplyInfoEntity> records = (result.data as List<dynamic>)
            .map((item) => SubsidyApplyInfoEntity.fromJson(item))
            .toList();
        setState(() {
          dataRecords = records;
        });
      } else {}
    });
  }

  void handleItemSelectChanged(SubsidyApplyInfoEntity item, bool isSelected) {
    setState(() {
      for (var value in dataRecords) {
        value.isSelected = false;
      }
      item.isSelected = isSelected;
      // dataRecords.where((it) => it == item).forEach((it) {
      //   it.isSelected = true;
      // });
      bool isCancelAll = dataRecords.every((item) => item.isSelected == false);
      if (isCancelAll) {
        isShowActionBtn = false;
      } else {
        isShowActionBtn = true;
      }
      if (item.isSelected == true) {
        if (item.farmerSignNo == "" || item.farmerSignNo == "0") {
          isShowSignBtn = true;
        } else {
          isShowSignBtn = false;
        }
      }
    });
  }

  Future<void> showCustomBusyDialog(BuildContext context) async {
    await showGeneralDialog(
      context: context,
      barrierDismissible: false, // 设置用户是否可以点击遮罩层来关闭对话框
      barrierLabel: '加载中...', // 遮罩层的提示文字
      barrierColor: Colors.black.withOpacity(0.5), // 遮罩层的颜色和透明度
      transitionDuration: const Duration(milliseconds: 150), // 动画时长
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return Center(
          // 对话框内容居中显示
          child: Container(
            alignment: Alignment.center,
            width: 120.px,
            height: 120.px,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.px),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    width: 40.px,
                    height: 40.px,
                    child: const CircularProgressIndicator()),
                SizedBox(height: 16.px),
                Text("加载中...",
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(44, 44, 52, 0.8)))
              ],
            ),
          ), // 加载指示器
        );
      },
    );
  }
}
