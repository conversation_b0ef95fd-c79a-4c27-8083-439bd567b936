import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_detail_item_entity.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../model/dict_tree_model.dart';
import '../../../utils/color_util.dart';
import '../../../utils/request/subsidy_service.dart';
import 'entity/subsidy_of_mine_entity.dart';

class SubsidyDetailPage extends StatefulWidget {
  final SubsidyOfMineDetails dataItem;
  final String year;

  const SubsidyDetailPage(
      {super.key, required this.dataItem, required this.year});

  @override
  State<SubsidyDetailPage> createState() => _SubsidyDetailPageState();
}

class _SubsidyDetailPageState extends State<SubsidyDetailPage> {
  bool _isLoading = false;
  SubsidyDetailItemEntity? dataItem;
  List<SubsidyDetailItemDetails> records = [];

  @override
  void initState() {
    super.initState();
    setState(() {
      _isLoading = true;
    });
    _getDicTypes();
    _getDataRecords();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(widget.dataItem.subsidyItemName ?? "",
              style: const TextStyle(color: Colors.black, fontSize: 16)),
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: Container(
          margin: EdgeInsets.only(left: 16.px, right: 16.px),
          child: Column(children: [
            Container(
                width: 343.px,
                padding: EdgeInsets.only(
                    left: 10.px, right: 10.px, top: 16.px, bottom: 15.px),
                margin: EdgeInsets.only(top: 10.px),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${widget.year}年补贴金额(元)",
                        style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color.fromRGBO(41, 41, 52, 1)),
                      ),
                      SizedBox(height: 2.px),
                      Text(
                          widget.dataItem.actualPaymentFee
                                  ?.toStringAsFixed(0) ??
                              "",
                          style: const TextStyle(
                            fontSize: 28,
                            fontWeight: FontWeight.w500,
                            color: Color.fromRGBO(41, 41, 52, 1),
                          )),
                      SizedBox(height: 16.px),
                      Row(children: [
                        const Text(
                          "工作站",
                          style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: Color.fromRGBO(41, 41, 52, 0.4)),
                        ),
                        const Spacer(),
                        SizedBox(
                          width: 200.px,
                          child: Text(
                            widget.dataItem.organizationName ?? "",
                            maxLines: 4,
                            overflow: TextOverflow.ellipsis,
                            style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w400,
                                color: Color.fromRGBO(41, 41, 52, 1)),
                          ),
                        )
                      ]),
                      SizedBox(height: 8.px),
                      Row(children: [
                        const Text(
                          "发放时间",
                          style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: Color.fromRGBO(41, 41, 52, 0.4)),
                        ),
                        const Spacer(),
                        Text(
                          widget.dataItem.paymentTime ?? "",
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w400,
                              color: Color.fromRGBO(41, 41, 52, 1)),
                        )
                      ])
                    ])),
            SizedBox(height: 20.px),
            Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Container(
                    width: 3.px,
                    height: 16.px,
                    decoration: BoxDecoration(
                      color: const Color.fromRGBO(2, 139, 93, 1),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  SizedBox(width: 6.px),
                  const Text(
                    "补贴明细",
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Color.fromRGBO(41, 41, 52, 1)),
                  ),
                ]),
            SizedBox(height: 5.px),
            Expanded(
                child: _isLoading
                    ? const Center(
                        child: SpinKitCircle(
                          // color: HexColor('#16B760'),
                          color: Color.fromRGBO(0, 127, 255, 1),
                          size: 60.0,
                        ),
                      )
                    : SingleChildScrollView(
                        child: Column(
                          children: records
                              .map((item) => _buildDetailItem(item))
                              .toList(),
                        ),
                      ))
          ]),
        ));
  }

  Widget _buildDetailItem(SubsidyDetailItemDetails item) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 10.px, vertical: 12.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                    getDictNameByType(
                        subsidyTypes, item.subsidyType.toString()),
                    style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color.fromRGBO(41, 41, 52, 1))),
                const Spacer(),
                Text(item.paymentTime ?? "",
                    style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: Color.fromRGBO(41, 41, 52, 0.8))),
              ]),
          SizedBox(height: 15.px),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Column(children: [
              const Text("补贴标准1(元/亩)",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color.fromRGBO(41, 41, 52, 0.4),
                  )),
              SizedBox(
                height: 5.px,
              ),
              Text(
                item.subsidyStandard1.toString(),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color.fromRGBO(41, 41, 52, 1),
                ),
              ),
            ]),
            Column(children: [
              const Text("面积标准1(亩)",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color.fromRGBO(41, 41, 52, 0.4),
                  )),
              SizedBox(
                height: 5.px,
              ),
              Text(
                item.subsidyArea1.toString(),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color.fromRGBO(41, 41, 52, 1),
                ),
              ),
            ]),
            Column(children: [
              const Text("补贴金额1(元)",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color.fromRGBO(41, 41, 52, 0.4),
                  )),
              SizedBox(
                height: 5.px,
              ),
              Text(
                item.subsidyFee1.toString(),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color.fromRGBO(2, 139, 93, 1),
                ),
              ),
            ]),
          ]),
          SizedBox(height: 15.px),
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Column(children: [
              const Text("补贴标准2(元/亩)",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color.fromRGBO(41, 41, 52, 0.4),
                  )),
              SizedBox(
                height: 5.px,
              ),
              Text(
                item.subsidyStandard2.toString(),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color.fromRGBO(41, 41, 52, 1),
                ),
              ),
            ]),
            Column(children: [
              const Text("面积标准2(亩)",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color.fromRGBO(41, 41, 52, 0.4),
                  )),
              SizedBox(
                height: 5.px,
              ),
              Text(
                item.subsidyArea2.toString(),
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w400,
                  color: Color.fromRGBO(41, 41, 52, 1),
                ),
              ),
            ]),
            Column(children: [
              const Text("补贴金额2(元)",
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: Color.fromRGBO(41, 41, 52, 0.4),
                  )),
              SizedBox(
                height: 5.px,
              ),
              Text(
                item.subsidyFee2.toString(),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color.fromRGBO(2, 139, 93, 1),
                ),
              ),
            ]),
          ]),
        ]));
  }

  void _getDataRecords() async {
    Map<String, Object> params = {
      "subsidyYear": widget.year,
      "subsidyConfigId": widget.dataItem.subsidyConfigId.toString(),
      "organizationNo": widget.dataItem.organizationNo ?? ""
    };

    await SubsidyService.mySubsidyPaymentDetailInfo(params).then((result) {
      setState(() {
        _isLoading = false;
      });
      if (result.success == true) {
        SubsidyDetailItemEntity data =
            SubsidyDetailItemEntity.fromJson(result.data);
        setState(() {
          dataItem = data;
          records = data.details ?? [];
        });
      }
    });
  }

  List<DictNode> subsidyTypes = [];
  void _getDicTypes() async {
    //补贴类型
    await SubsidyService.getDicByKey("sys_subsidy_type").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            subsidyTypes = result.data!;
          });
        }
      }
    });
  }

  String getDictNameByType(List<DictNode> dicts, String type) {
    for (var item in dicts) {
      if (item.code == type) {
        return item.name!;
      }
    }
    return "";
  }
}
