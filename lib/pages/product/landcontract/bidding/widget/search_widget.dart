import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class SearchWidget extends StatefulWidget {
  final TextEditingController? controller;
  final String? initialValue;
  final String? placeholder;
  final ValueChanged<String?> onSubmitted;
  final ValueChanged<String?>? onChanged;
  final Color? backgroundColor;
  final Color? inputColor;
  final double? height;
  final EdgeInsets? margin;
  final TextStyle? placeholderStyle;
  final TextStyle? textStyle;
  final double? marginLeft;
  const SearchWidget(
      {super.key,
      required this.onSubmitted,
      this.onChanged,
      this.controller,
      this.initialValue,
      this.placeholder,
      this.backgroundColor,
      this.height,
      this.margin,
      this.placeholderStyle,
      this.textStyle,
      this.inputColor,
      this.marginLeft});

  @override
  State<StatefulWidget> createState() => _SearchWidgetState();
}

class _SearchWidgetState extends State<SearchWidget> {
  TextEditingController? _controller;
  TextEditingController? get _effectiveController =>
      widget.controller ?? _controller;

  bool _showClear = false;
  @override
  void initState() {
    if (widget.controller == null) {
      _controller = TextEditingController(text: widget.initialValue ?? "");
    }

    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
    _controller?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ColoredBox(
        color: widget.backgroundColor ?? const Color.fromRGBO(243, 245, 249, 1),
        child: Container(
            height: widget.height ?? 37.px,
            margin: widget.margin ??
                EdgeInsets.only(
                    top: 6.px, bottom: 6.px, left: 12.px, right: 12.px),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: widget.inputColor ?? Colors.white,
              borderRadius: BorderRadius.circular(18.5.px),
            ),
            child: Center(
                child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  width: widget.marginLeft ?? 18.5.px,
                ),
                Expanded(
                  child: CupertinoTextField.borderless(
                    style: widget.textStyle ??
                        TextStyle(
                          fontSize: 12.px,
                          color: Colors.black,
                          fontWeight: FontWeight.w400,
                        ),
                    textInputAction: TextInputAction.search,
                    padding: EdgeInsets.zero,
                    controller: _effectiveController,
                    placeholder: widget.placeholder ?? "请输入土地名称、水旱田、土地等级",
                    placeholderStyle: widget.placeholderStyle ??
                        TextStyle(
                            fontSize: 12.px,
                            fontWeight: FontWeight.w500,
                            color: const Color.fromRGBO(44, 44, 52, 0.4)),
                    onTap: () {},
                    onChanged: (value) {
                      widget.onChanged?.call(value);
                      setState(() {
                        _showClear = value.isNotEmpty;
                      });
                    },
                    onSubmitted: (value) {
                      widget.onSubmitted.call(value);
                    },
                  ),
                ),
                if (_showClear) ...[
                  GestureDetector(
                      onTap: () {
                        _effectiveController?.clear();
                        setState(() {
                          _showClear = false;
                        });
                        widget.onSubmitted.call(null);
                      },
                      child: Image.asset(
                          width: 16.px,
                          height: 16.px,
                          ImageHelper.wrapAssets('delteIcons.png'))),
                  SizedBox.square(
                    dimension: 5.px,
                  )
                ],
                GestureDetector(
                    onTap: () {
                      widget.onSubmitted.call(_effectiveController?.text ?? "");
                    },
                    child: Image.asset(
                        width: 16.px,
                        height: 16.px,
                        ImageHelper.wrapAssets('search_grey.png'))),
                SizedBox.square(
                  dimension: 10.px,
                )
              ],
            ))));
  }

  @override
  void didUpdateWidget(covariant SearchWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    if (_effectiveController?.value.text != widget.initialValue) {
      _effectiveController?.value =
          TextEditingValue(text: widget.initialValue ?? "");
    }
  }
}
