import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_confire_detail_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/insure_notification_page.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/services/insure_serivce.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../components/bdh_network_image.dart';
import '../../../model/dict_tree_model.dart';
import '../../../utils/color_util.dart';
import '../../message/bdh_empty_View.dart';
import 'entity/insure_contract_item_entity.dart';

class InsureDetailPage extends StatefulWidget {
  final InsureContractItemEntity dataItem;

  const InsureDetailPage({super.key, required this.dataItem});

  @override
  State<InsureDetailPage> createState() => _InsureDetailPageState();
}

class _InsureDetailPageState extends State<InsureDetailPage> {
  bool isAgree = false;

  InsureConfirmDetailEntity? insureInfo;

  List<InsureConfireDetailBindPlotInfos> get bindPlotInfos =>
      insureInfo?.bindPlotInfos ?? [];

  bool _isLoading = false;

  String signatureUrl = '';

  @override
  void initState() {
    super.initState();
    setState(() {
      _isLoading = true;
    });
    _getDicts();
    _getInsureConfirmInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            leading: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: const Icon(Icons.arrow_back_ios_new_outlined,
                    color: Color.fromRGBO(0, 0, 0, 1))),
            title: const Text(
              '投保信息',
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Color.fromRGBO(0, 0, 0, 1)),
            )),
        body: insureInfo == null
            ? Center(
                child: _isLoading
                    ? const SpinKitCircle(
                        // color: HexColor('#16B760'),
                        color: Color.fromRGBO(0, 127, 255, 1),
                        size: 60.0,
                      )
                    : const BdhEmptyView(),
              )
            : SingleChildScrollView(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.px),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: EdgeInsets.symmetric(vertical: 15.px),
                              padding: EdgeInsets.all(15.px),
                              decoration: BoxDecoration(
                                  color: const Color.fromRGBO(255, 255, 255, 1),
                                  borderRadius: BorderRadius.circular(6.px)),
                              child: RichText(
                                  text: TextSpan(
                                      text: '为了维护您的合法权益，请您仔细阅读',
                                      style: TextStyle(
                                          fontSize: 14.px,
                                          fontWeight: FontWeight.w500,
                                          color: const Color.fromRGBO(
                                              21, 24, 48, 0.8)),
                                      children: [
                                    TextSpan(
                                        text: '《北大荒集团种植业保险投保告知书》',
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () async {
                                            var result = await Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    const InsureNotificationPage(),
                                              ),
                                            );
                                            if (result != null &&
                                                result == true) {
                                              setState(() {
                                                isAgree = true;
                                              });
                                            }
                                          },
                                        style: TextStyle(
                                            fontSize: 14.px,
                                            fontWeight: FontWeight.w500,
                                            color: const Color.fromRGBO(
                                                2, 139, 93, 1))),
                                    TextSpan(
                                        text:
                                            '，如对保险存在异议的，请在填写前进行线下咨询，如未咨询，是表示已经对条款内容完全理解并无异议。同时在您完成签字确认后北大荒信息公司会将您的以下信息以及签字传送给阳光农业相互保险公司，用于保险确认以及生成电子投保单。',
                                        style: TextStyle(
                                            fontSize: 14.px,
                                            fontWeight: FontWeight.w400,
                                            color: const Color.fromRGBO(
                                                21, 24, 48, 0.8)))
                                  ])),
                            ),
                            Text("个人信息",
                                style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(
                                        174, 176, 186, 1))),
                            Container(
                                margin: EdgeInsets.symmetric(vertical: 5.px),
                                padding: EdgeInsets.all(15.px),
                                decoration: BoxDecoration(
                                    color:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    borderRadius: BorderRadius.circular(6.px)),
                                child: Column(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(bottom: 15.px),
                                      child: Row(children: [
                                        Text("姓名",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(insureInfo?.personName ?? "",
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 15.px),
                                      child: Row(children: [
                                        Text("电话",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(insureInfo?.telephone ?? "",
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(
                                          top: 15.px, bottom: 15.px),
                                      child: Row(children: [
                                        Text("身份证号",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(insureInfo?.certNo ?? '',
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(
                                          top: 15.px, bottom: 15.px),
                                      child: Row(children: [
                                        Text("身份证起止日期",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(insureInfo!.certBeginDate ?? "",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    21, 24, 48, 1))),
                                        Text(' - ',
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    21, 24, 48, 1))),
                                        Text(insureInfo!.certExpDate ?? "",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    21, 24, 48, 1))),
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(top: 15.px),
                                      child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              width: 158.px,
                                              height: 101.px,
                                              decoration: BoxDecoration(
                                                color: const Color.fromRGBO(
                                                    234, 238, 243, 1),
                                                borderRadius:
                                                    BorderRadius.circular(6.px),
                                              ),
                                              child: BdhNetworkImage(
                                                  width: 158.px,
                                                  height: 101.px,
                                                  url: insureInfo
                                                          ?.certFrontUrl ??
                                                      ""),
                                            ),
                                            Container(
                                              width: 158.px,
                                              height: 101.px,
                                              decoration: BoxDecoration(
                                                color: const Color.fromRGBO(
                                                    234, 238, 243, 1),
                                                borderRadius:
                                                    BorderRadius.circular(6.px),
                                              ),
                                              child: BdhNetworkImage(
                                                  width: 158.px,
                                                  height: 101.px,
                                                  url:
                                                      insureInfo?.certBackUrl ??
                                                          ""),
                                            )
                                          ]),
                                    )
                                  ],
                                )),
                            SizedBox(
                              height: 10.px,
                            ),
                            Text("耕地详情",
                                style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(
                                        174, 176, 186, 1))),
                            Container(
                                margin: EdgeInsets.symmetric(vertical: 5.px),
                                padding: EdgeInsets.only(
                                    top: 15.px,
                                    left: 15.px,
                                    right: 15.px,
                                    bottom: 10.px),
                                decoration: BoxDecoration(
                                    color:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    borderRadius: BorderRadius.circular(12.px)),
                                child: Column(
                                  children: bindPlotInfos
                                      .map((item) => buildPlotItem(item))
                                      .toList(),
                                )),
                            SizedBox(
                              height: 10.px,
                            ),
                            Row(
                              children: [
                                Text("银行卡信息",
                                    style: TextStyle(
                                        fontSize: 14.px,
                                        fontWeight: FontWeight.w400,
                                        color: const Color.fromRGBO(
                                            174, 176, 186, 1))),
                                const Spacer(),
                              ],
                            ),
                            Container(
                                margin: EdgeInsets.symmetric(vertical: 5.px),
                                padding: EdgeInsets.all(15.px),
                                decoration: BoxDecoration(
                                    color:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    borderRadius: BorderRadius.circular(6.px)),
                                child: Column(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(bottom: 15.px),
                                      child: Row(children: [
                                        Text("银行卡号",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(insureInfo?.bankcardNo ?? '',
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 15.px),
                                      child: Row(children: [
                                        Text("卡片类型",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(
                                            getDictNameByType(bankCardTypes,
                                                insureInfo!.bankcardType!),
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(top: 15.px),
                                      child: Row(children: [
                                        Text("银行名称",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(insureInfo!.bankName!,
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    )
                                  ],
                                )),
                            SizedBox(
                              height: 10.px,
                            ),
                            Text("签字",
                                style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(
                                        174, 176, 186, 1))),
                            Container(
                              height: 155.px,
                              width: 351.px,
                              margin: EdgeInsets.symmetric(vertical: 5.px),
                              padding: EdgeInsets.all(15.px),
                              decoration: BoxDecoration(
                                  color: const Color.fromRGBO(255, 255, 255, 1),
                                  borderRadius: BorderRadius.circular(6.px)),
                              child: signatureUrl.isNotEmpty
                                  ? BdhNetworkImage(
                                      url: signatureUrl,
                                      width: 351.px,
                                      height: 155.px,
                                    )
                                  : Container(),
                            ),
                            SizedBox(
                              height: 10.px,
                            ),
                            // Container(
                            //     width: 351.px,
                            //     margin:
                            //         EdgeInsets.only(top: 5.px, bottom: 15.px),
                            //     child: Column(children: [
                            //       Row(
                            //           crossAxisAlignment:
                            //               CrossAxisAlignment.start,
                            //           children: [
                            //             SizedBox(
                            //               width: 30.px,
                            //               child: CupertinoCheckbox(
                            //                   activeColor: const Color.fromRGBO(
                            //                       0, 152, 91, 1),
                            //                   value: isAgree,
                            //                   shape: const StadiumBorder(),
                            //                   onChanged: (s) {
                            //                     setState(() {
                            //                       isAgree = s!;
                            //                     });
                            //                   }),
                            //             ),
                            //             Container(
                            //               width: 320.px,
                            //               margin: EdgeInsets.only(top: 10.px),
                            //               child: RichText(
                            //                   text: TextSpan(
                            //                       text: '我已仔细阅读',
                            //                       style: TextStyle(
                            //                           fontSize: 12.px,
                            //                           fontWeight:
                            //                               FontWeight.w400,
                            //                           color:
                            //                               const Color.fromRGBO(
                            //                                   21, 24, 48, 0.8)),
                            //                       children: [
                            //                     TextSpan(
                            //                         text: '《北大荒集团种植业保险投保告知书》',
                            //                         recognizer:
                            //                             TapGestureRecognizer()
                            //                               ..onTap = () async {
                            //                                 var result =
                            //                                     await Navigator
                            //                                         .push(
                            //                                   context,
                            //                                   MaterialPageRoute(
                            //                                     builder:
                            //                                         (context) =>
                            //                                             const InsureNotificationPage(),
                            //                                   ),
                            //                                 );
                            //                                 if (result !=
                            //                                         null &&
                            //                                     result ==
                            //                                         true) {
                            //                                   setState(() {
                            //                                     isAgree = true;
                            //                                   });
                            //                                 }
                            //                               },
                            //                         style: TextStyle(
                            //                             fontSize: 12.px,
                            //                             fontWeight:
                            //                                 FontWeight.w400,
                            //                             color: const Color
                            //                                 .fromRGBO(
                            //                                 2, 139, 93, 1))),
                            //                     TextSpan(
                            //                         text:
                            //                             '，同意将承包耕地上种植的全部符合承保条的作物参加种植业保险，对投保情况知情认可。',
                            //                         style: TextStyle(
                            //                             fontSize: 12.px,
                            //                             fontWeight:
                            //                                 FontWeight.w400,
                            //                             color: const Color
                            //                                 .fromRGBO(
                            //                                 21, 24, 48, 0.8)))
                            //                   ])),
                            //             )
                            //           ])
                            //     ])),
                          ],
                        ),
                      ),
                    ]),
              ));
  }

  /// 耕地列表
  Widget buildPlotItem(InsureConfireDetailBindPlotInfos item) {
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image(
                  image:
                      const AssetImage("assets/images/insure/ic_plot_tip.png"),
                  height: 20.px,
                  width: 20.px),
              SizedBox(width: 4.px),
              Text(
                "耕地地号 ${item.plotNo}",
                style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w500,
                    color: const Color.fromRGBO(0, 0, 0, 1)),
              )
            ],
          ),
          Container(
              margin: EdgeInsets.only(top: 8.px, bottom: 8.px),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.px),
                gradient: const LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  stops: [
                    0.0,
                    0.9,
                  ],
                  colors: [
                    Color.fromRGBO(238, 255, 248, 1),
                    Color.fromRGBO(248, 255, 252, 1),
                  ],
                ),
              ),
              padding: EdgeInsets.only(top: 8.px, bottom: 8.px),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "耕地类型",
                            style: TextStyle(
                                fontSize: 12.px,
                                fontWeight: FontWeight.w400,
                                color: const Color.fromRGBO(21, 24, 48, 0.6)),
                          ),
                          SizedBox(height: 4.px),
                          Text(getDictNameByType(plowLandTypes, item.landType!),
                              style: TextStyle(
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600,
                                color: const Color.fromRGBO(0, 0, 0, 1),
                              ))
                        ]),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "地块面积(亩)",
                            style: TextStyle(
                                fontSize: 12.px,
                                fontWeight: FontWeight.w400,
                                color: const Color.fromRGBO(21, 24, 48, 0.6)),
                          ),
                          SizedBox(height: 4.px),
                          Text("${item.plotArea}",
                              style: TextStyle(
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600,
                                color: const Color.fromRGBO(0, 0, 0, 1),
                              ))
                        ]),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "作物",
                            style: TextStyle(
                                fontSize: 12.px,
                                fontWeight: FontWeight.w400,
                                color: const Color.fromRGBO(21, 24, 48, 0.6)),
                          ),
                          SizedBox(height: 4.px),
                          Text(getDictNameByType(cropTypes, item.cropCode!),
                              style: TextStyle(
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600,
                                color: const Color.fromRGBO(0, 0, 0, 1),
                              ))
                        ])
                  ]))
        ]);
  }

  /// 银行列表选择

  Future<void> showBusyDialog(BuildContext context,
      {String msg = '上传中...'}) async {
    showGeneralDialog(
      context: context,
      barrierDismissible: false, // 设置用户是否可以点击遮罩层来关闭对话框
      barrierLabel: msg, // 遮罩层的提示文字
      barrierColor: Colors.black.withOpacity(0.5), // 遮罩层的颜色和透明度
      transitionDuration: const Duration(milliseconds: 150), // 动画时长
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return Center(
          // 对话框内容居中显示
          child: Container(
            alignment: Alignment.center,
            width: 120.px,
            height: 120.px,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.px),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    width: 40.px,
                    height: 40.px,
                    child: const CircularProgressIndicator()),
                SizedBox(height: 16.px),
                Text(msg,
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(44, 44, 52, 0.8)))
              ],
            ),
          ), // 加载指示器
        );
      },
    );
  }

  void _getInsureConfirmInfo() {
    Map<String, dynamic> params = {
      "insureConfId": widget.dataItem.insureConfId,
    };
    InsureService.getInsureConfirmInfo(params).then((result) {
      setState(() {
        _isLoading = false;
      });
      if (result.success == true) {
        setState(() {
          InsureConfirmDetailEntity data =
              InsureConfirmDetailEntity.fromJson(result.data);
          insureInfo = data;
          signatureUrl = data.signImgUrl!;
        });
      }
    }, onError: (error) {
      setState(() {
        _isLoading = false;
      });
    });
  }

  List<DictNode> bankNameTypes = [];
  List<DictNode> bankCardTypes = [];
  List<DictNode> cropTypes = [];
  List<DictNode> plowLandTypes = [];
  void _getDicts() async {
    await InsureService.getDicByKey("bank_name").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            bankNameTypes = result.data!;
          });
        }
      }
    });

    await InsureService.getDicByKey("bankcard_type_no").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            bankCardTypes = result.data!;
          });
        }
      }
    });

    await InsureService.getDicByKey("sys_crop").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            cropTypes = result.data!;
          });
        }
      }
    });

    await InsureService.getDicByKey("sys_plowland").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            plowLandTypes = result.data!;
          });
        }
      }
    });
  }

  String getDictNameByType(List<DictNode> dicts, String type) {
    for (var item in dicts) {
      if (item.code == type) {
        return item.name!;
      }
    }
    return "";
  }
}
