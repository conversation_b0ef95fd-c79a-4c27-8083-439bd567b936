import 'dart:convert';

import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_contract_item_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/insure_confirm_page.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/insure_detail_page.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/insure_item_widget.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/services/insure_serivce.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../components/jh_cascade_tree_picker.dart';
import '../../../model/dict_tree_model.dart';
import '../../../model/org_tree_list_model.dart';
import '../../../utils/color_util.dart';
import '../../../utils/event_bus.dart';
import '../../../utils/request/bdh_service.dart';
import '../../message/bdh_empty_View.dart';

/// 阳光农险投保确认页面
class InsureMainPage extends StatefulWidget {
  const InsureMainPage({super.key});

  @override
  State<InsureMainPage> createState() => _InsureMainPageState();
}

class _InsureMainPageState extends State<InsureMainPage> {
  FixedExtentScrollController? yearSelectionController;
  int _currentYearIndex = -1;
  int _currentSelectedYearIndex = -1;
  String _currentYear = "";
  List<DictNode> subsidyYears = [];
  OrgTreeResult? treeResult;
  Map<dynamic, dynamic>? currentOrg;
  bool isShowSignBtn = false;
  bool isShowActionBtn = false;
  bool _isLoading = false;

  List<InsureContractItemEntity> dataRecords = [];

  @override
  void initState() {
    super.initState();
    setState(() {
      _currentYear = DateTime.now().year.toString();
    });
    setState(() {
      _isLoading = true;
    });
    _getDictTypes();
    _getOrgList();
    _getInsureList();
    bus.on('insure_list_refresh', (e) {
      _getInsureList();
    });
  }

  @override
  void dispose() {
    bus.off('insure_list_refresh');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints cons) {
      return Scaffold(
          backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
          body: SizedBox(
            width: cons.maxWidth,
            height: cons.maxHeight,
            child: Stack(children: [
              Positioned(
                child: Container(
                  height: 256.px,
                  width: 375.px,
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                    image:
                        AssetImage("assets/images/insure/ic_insure_top_bg.png"),
                  )),
                ),
              ),
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 36.px, left: 8.px),
                        child: BackButton(
                          color: Colors.white,
                          onPressed: () {
                            Navigator.pop(context);
                          },
                        ),
                      )
                    ]),
              ),
              Positioned(
                  top: 172.px,
                  left: 0,
                  right: 0,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Container(
                                margin: EdgeInsets.only(left: 16.px),
                                child: GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () {
                                    showBottomMultiSelectPicker(context);
                                  },
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      ConstrainedBox(
                                        constraints: BoxConstraints(
                                          maxWidth: 200.px,
                                        ),
                                        child: Text(
                                            currentOrg == null
                                                ? "请选择单位"
                                                : currentOrg?['orgFullName'],
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                                fontSize: 14.px,
                                                color: Colors.white)),
                                      ),
                                      Icon(
                                        Icons.arrow_drop_down,
                                        color: Colors.white,
                                        size: 22.px,
                                      )
                                    ],
                                  ),
                                ),
                              ),
                              const Spacer(),
                              Container(
                                margin: EdgeInsets.only(left: 8.px),
                                child: TextButton(
                                  onPressed: () {
                                    showModalBottomSheet(
                                        context: context,
                                        elevation: 10,
                                        enableDrag: false,
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.px)),
                                        builder: (BuildContext context) {
                                          return showBottomSelectYearsPicker();
                                        });
                                  },
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Text(
                                        "$_currentYear年",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 14.px,
                                            fontWeight: FontWeight.w400),
                                      ),
                                      Icon(
                                        Icons.arrow_drop_down,
                                        size: 22.px,
                                        color: Colors.white,
                                      )
                                    ],
                                  ),
                                ),
                              )
                            ]),
                      ])),
              Positioned(
                  top: 208.px,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Column(
                    children: [
                      Expanded(
                          child: dataRecords.isEmpty
                              ? Center(
                                  child: _isLoading
                                      ? const SpinKitCircle(
                                          // color: HexColor('#16B760'),
                                          color: Color.fromRGBO(0, 127, 255, 1),
                                          size: 60.0,
                                        )
                                      : const BdhEmptyView(),
                                )
                              : ListView.builder(
                                  shrinkWrap: true,
                                  padding:
                                      EdgeInsets.only(top: 0.px, bottom: 0.px),
                                  itemCount: dataRecords.length,
                                  itemBuilder: (ctx, idx) {
                                    return InsureItemWidget(
                                        onSelectChanged: (isSelected) {
                                          handleItemSelectChanged(
                                              dataRecords[idx], isSelected!);
                                        },
                                        dataItem: dataRecords[idx],
                                        type: "投保信息确认单");
                                  })),
                      isShowActionBtn
                          ? SafeArea(
                              top: false,
                              bottom: true,
                              child: Container(
                                  padding: EdgeInsets.only(
                                      bottom: 30.px,
                                      top: 10.px,
                                      right: 16.px,
                                      left: 16.px),
                                  height: 90.px,
                                  width: 375.px,
                                  color: Colors.white,
                                  child: ElevatedButton(
                                      onPressed: () {
                                        if (isShowSignBtn) {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  InsureConfirmPage(
                                                dataItem: dataRecords
                                                    .where((it) =>
                                                        it.isSelected == true)
                                                    .first,
                                              ),
                                            ),
                                          );
                                        } else {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) =>
                                                  InsureDetailPage(
                                                dataItem: dataRecords
                                                    .where((it) =>
                                                        it.isSelected == true)
                                                    .first,
                                              ),
                                            ),
                                          );
                                        }
                                      },
                                      style: TextButton.styleFrom(
                                        backgroundColor:
                                            const Color.fromRGBO(2, 139, 93, 1),
                                        foregroundColor: Colors.white,
                                        side: const BorderSide(
                                            color:
                                                Color.fromRGBO(2, 139, 93, 1),
                                            width: 1),
                                        shape: const RoundedRectangleBorder(
                                            borderRadius: BorderRadius.all(
                                                Radius.circular(10))),
                                      ),
                                      child: Text(
                                          isShowSignBtn ? "投保确认" : "查看投保确认",
                                          style: TextStyle(fontSize: 16.px)))),
                            )
                          : Container()
                    ],
                  ))
            ]),
          ));
    });
  }

  Widget showBottomSelectYearsPicker() {
    if (_currentYearIndex == -1) {
      _currentYearIndex =
          subsidyYears.indexWhere((item) => item.name == _currentYear);
      _currentSelectedYearIndex = _currentYearIndex;
      yearSelectionController =
          FixedExtentScrollController(initialItem: _currentYearIndex);
    } else {
      yearSelectionController =
          FixedExtentScrollController(initialItem: _currentYearIndex);
    }
    return BottomSheet(
        enableDrag: false,
        onClosing: () {},
        builder: (BuildContext context) {
          return Container(
              padding: EdgeInsets.only(left: 10.px, right: 10.px, top: 10.px),
              height: 280.px,
              width: 375.px,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "取消",
                              style: TextStyle(
                                  fontSize: 16.px, color: Colors.redAccent),
                            )),
                        Text(
                          "选择年份",
                          style: TextStyle(
                              color: const Color.fromRGBO(44, 44, 52, 1),
                              fontSize: 18.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextButton(
                            onPressed: () {
                              setState(() {
                                _currentYearIndex = _currentSelectedYearIndex;
                                _currentYear =
                                    subsidyYears[_currentYearIndex].name!;
                              });
                              _getInsureList();
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "确定",
                              style: TextStyle(
                                  color: Colors.blueAccent, fontSize: 16.px),
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 220.px,
                      child: CupertinoPicker(
                          scrollController: yearSelectionController,
                          itemExtent: 40.px,
                          squeeze: 1.5,
                          diameterRatio: 1,
                          onSelectedItemChanged: (index) {
                            setState(() {
                              _currentSelectedYearIndex = index;
                            });
                          },
                          children: subsidyYears
                              .map((item) => Center(
                                    child: Text(item.name!),
                                  ))
                              .toList()),
                    )
                  ]));
        });
  }

  void showBottomMultiSelectPicker(BuildContext context) {
    var tempData = [];
    for (var e in treeResult!.data!.first.children!) {
      tempData.add(e.toJson());
    }
    JhCascadeTreePicker.show(context,
        data: tempData,
        valueKey: "orgCode",
        labelKey: "orgName",
        childrenKey: "list",
        clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
      setState(() {
        currentOrg = (ress as List).last;
        _getInsureList();
      });
    });
  }

  void handleItemSelectChanged(InsureContractItemEntity item, bool isSelected) {
    setState(() {
      for (var value in dataRecords) {
        value.isSelected = false;
      }
      item.isSelected = isSelected;
      // dataRecords.where((it) => it == item).forEach((it) {
      //   it.isSelected = true;
      // });
      bool isCancelAll = dataRecords.every((item) => item.isSelected == false);
      if (isCancelAll) {
        isShowActionBtn = false;
      } else {
        isShowActionBtn = true;
      }
      if (item.isSelected == true) {
        if (item.isConfirm!) {
          isShowSignBtn = false;
        } else {
          isShowSignBtn = true;
        }
      }
    });
  }

  void _getInsureList() async {
    Map<String, dynamic> params = {
      "yearNo": _currentYear,
    };
    if (currentOrg != null) {
      params["orgCode"] = currentOrg?["orgCode"];
    } else {
      params["orgCode"] = "";
    }

    await InsureService.getInsureContractList(params).then((result) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        if (result.success == true) {
          setState(() {
            List<InsureContractItemEntity> records =
                (result.data as List<dynamic>)
                    .map((item) => InsureContractItemEntity.fromJson(item))
                    .toList();
            dataRecords = records;
          });
        }
      }
    });
  }

  void _getDictTypes() async {
    await InsureService.getDicByKey("year_cd").then((result) {
      if (result.success == true) {
        setState(() {
          subsidyYears = result.data!;
        });
      }
    });
  }

  // 加载组织机构
  void _getOrgList() async {
    await InsureService.getOrgList({}).then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            treeResult = result;
          });
        }
      }
    });
  }
}
