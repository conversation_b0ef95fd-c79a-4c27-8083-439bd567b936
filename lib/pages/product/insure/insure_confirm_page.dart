import 'dart:convert';
import 'dart:typed_data';

import 'package:bdh_smart_agric_app/model/bank_model.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_black_land_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_contract_info_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/insure_main_page.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/insure_notification_page.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/insure_sign_name_page.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/services/insure_serivce.dart';
import 'package:bdh_smart_agric_app/utils/collection_extensions.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:http_parser/http_parser.dart';
import 'package:oktoast/oktoast.dart';
import 'package:signature/signature.dart';

import '../../../components/bdh_network_image.dart';
import '../../../model/dict_tree_model.dart';
import '../../../utils/color_util.dart';
import '../../../utils/cover_tool.dart';
import '../../../utils/event_bus.dart';
import '../../message/bdh_empty_View.dart';
import 'entity/insure_contract_item_entity.dart';

class InsureConfirmPage extends StatefulWidget {
  final InsureContractItemEntity dataItem;

  const InsureConfirmPage({super.key, required this.dataItem});

  @override
  State<InsureConfirmPage> createState() => _InsureConfirmPageState();
}

class _InsureConfirmPageState extends State<InsureConfirmPage> {
  bool isAgree = false;

  InsureContractInfoEntity? insureContractInfo;

  InsureContractInfoFarmerFarmer get farmer =>
      insureContractInfo?.farmer?.farmer ?? InsureContractInfoFarmerFarmer();

  String get farmerId => insureContractInfo?.farmer?.idNumber ?? "";

  List<InsureContractInfoPlotList> get bindPlotInfos =>
      insureContractInfo?.plotList ?? [];

  List<InsureContractInfoBankcard> get bankcards =>
      insureContractInfo?.bankcard ?? [];

  InsureContractInfoContract get contract =>
      insureContractInfo?.contract ?? InsureContractInfoContract();

  // InsureContractInfoBankcard get bankcard =>
  //     bankcards.isNotEmpty ? bankcards.first : InsureContractInfoBankcard();

  InsureContractInfoBankcard? bankcard;

  String? _bankType;

  bool _isLoading = false;
  bool _isSignNameFlag = false;

  Uint8List? signatureImage;
  List<Point> signaturePoints = [];
  String signatureUrl = '';

  int _validityType = 1;
  String identityStartDate = "";
  String identityEndDate = "";

  bool isBlackLandLoaded = false;

  @override
  void initState() {
    super.initState();
    // _getInsureConfirmInfo();
    setState(() {
      _isLoading = true;
    });
    _getDicts();
    _getInsureContractInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            leading: IconButton(
                onPressed: () {
                  Navigator.pop(context);
                },
                icon: const Icon(Icons.arrow_back_ios_new_outlined,
                    color: Color.fromRGBO(0, 0, 0, 1))),
            title: const Text(
              '投保确认',
              style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Color.fromRGBO(0, 0, 0, 1)),
            )),
        body: insureContractInfo == null
            ? Center(
                child: _isLoading
                    ? const SpinKitCircle(
                        // color: HexColor('#16B760'),
                        color: Color.fromRGBO(0, 127, 255, 1),
                        size: 60.0,
                      )
                    : const BdhEmptyView(),
              )
            : SingleChildScrollView(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 12.px),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              margin: EdgeInsets.symmetric(vertical: 15.px),
                              padding: EdgeInsets.all(15.px),
                              decoration: BoxDecoration(
                                  color: const Color.fromRGBO(255, 255, 255, 1),
                                  borderRadius: BorderRadius.circular(6.px)),
                              child: RichText(
                                  text: TextSpan(
                                      text: '为了维护您的合法权益，请您仔细阅读',
                                      style: TextStyle(
                                          fontSize: 14.px,
                                          fontWeight: FontWeight.w500,
                                          color: const Color.fromRGBO(
                                              21, 24, 48, 0.8)),
                                      children: [
                                    TextSpan(
                                        text: '《北大荒集团种植业保险投保告知书》',
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () async {
                                            var result = await Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder: (context) =>
                                                    const InsureNotificationPage(),
                                              ),
                                            );
                                            if (result != null &&
                                                result == true) {
                                              setState(() {
                                                isAgree = true;
                                              });
                                            }
                                          },
                                        style: TextStyle(
                                            fontSize: 14.px,
                                            fontWeight: FontWeight.w500,
                                            color: const Color.fromRGBO(
                                                2, 139, 93, 1))),
                                    TextSpan(
                                        text:
                                            '，如对保险存在异议的，请在填写前进行线下咨询，如未咨询，是表示已经对条款内容完全理解并无异议。同时在您完成签字确认后北大荒信息公司会将您的以下信息以及签字传送给阳光农业相互保险公司，用于保险确认以及生成电子投保单。',
                                        style: TextStyle(
                                            fontSize: 14.px,
                                            fontWeight: FontWeight.w400,
                                            color: const Color.fromRGBO(
                                                21, 24, 48, 0.8)))
                                  ])),
                            ),
                            Text("个人信息",
                                style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(
                                        174, 176, 186, 1))),
                            Container(
                                margin: EdgeInsets.symmetric(vertical: 5.px),
                                padding: EdgeInsets.all(15.px),
                                decoration: BoxDecoration(
                                    color:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    borderRadius: BorderRadius.circular(6.px)),
                                child: Column(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(bottom: 15.px),
                                      child: Row(children: [
                                        Text("姓名",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(farmer.name ?? "",
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 15.px),
                                      child: Row(children: [
                                        Text("电话",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(farmer.phone1 ?? "",
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(
                                          top: 15.px, bottom: 15.px),
                                      child: Row(children: [
                                        Text("身份证号",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(farmer.idNumber ?? '',
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(
                                          top: 15.px, bottom: 15.px),
                                      child: Row(children: [
                                        Text("身份证有效期",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Radio(
                                          activeColor: const Color.fromRGBO(
                                              0, 152, 91, 1),
                                          value: 0,
                                          groupValue: _validityType,
                                          onChanged: (value) {
                                            setState(() {
                                              _validityType = value!;
                                              identityEndDate = "长期";
                                            });
                                          },
                                        ),
                                        Text('长期',
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    21, 24, 48, 1))),
                                        SizedBox(width: 10.w),
                                        Radio(
                                          activeColor: const Color.fromRGBO(
                                              0, 152, 91, 1),
                                          value: 1,
                                          groupValue: _validityType,
                                          onChanged: (value) {
                                            setState(() {
                                              _validityType = value!;
                                              identityEndDate = "请选择";
                                            });
                                          },
                                        ),
                                        Text('短期',
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    21, 24, 48, 1))),
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(
                                          top: 15.px, bottom: 15.px),
                                      child: Row(children: [
                                        Text("身份证起止日期",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        TextButton(
                                            style: TextButton.styleFrom(
                                              padding:
                                                  const EdgeInsets.all(2.0),
                                            ),
                                            onPressed: () {
                                              showDatePickerDialog(
                                                  0, identityStartDate);
                                            },
                                            child: Text(identityStartDate,
                                                style: TextStyle(
                                                    fontSize: 16.px,
                                                    fontWeight: FontWeight.w400,
                                                    color: const Color.fromRGBO(
                                                        21, 24, 48, 1)))),
                                        Text('-',
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    21, 24, 48, 1))),
                                        TextButton(
                                            style: TextButton.styleFrom(
                                              padding:
                                                  const EdgeInsets.all(2.0),
                                            ),
                                            onPressed: () {
                                              showDatePickerDialog(
                                                  1, identityEndDate);
                                            },
                                            child: Text(identityEndDate,
                                                style: TextStyle(
                                                    fontSize: 16.px,
                                                    fontWeight: FontWeight.w400,
                                                    color: const Color.fromRGBO(
                                                        21, 24, 48, 1)))),
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(top: 15.px),
                                      child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Container(
                                              width: 158.px,
                                              height: 101.px,
                                              decoration: BoxDecoration(
                                                color: const Color.fromRGBO(
                                                    234, 238, 243, 1),
                                                borderRadius:
                                                    BorderRadius.circular(6.px),
                                              ),
                                              child: BdhNetworkImage(
                                                  width: 158.px,
                                                  height: 101.px,
                                                  url: farmer.idFront ?? ""),
                                            ),
                                            Container(
                                              width: 158.px,
                                              height: 101.px,
                                              decoration: BoxDecoration(
                                                color: const Color.fromRGBO(
                                                    234, 238, 243, 1),
                                                borderRadius:
                                                    BorderRadius.circular(6.px),
                                              ),
                                              child: BdhNetworkImage(
                                                  width: 158.px,
                                                  height: 101.px,
                                                  url: farmer.idBack ?? ""),
                                            )
                                          ]),
                                    )
                                  ],
                                )),
                            SizedBox(
                              height: 10.px,
                            ),
                            Text("耕地详情",
                                style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(
                                        174, 176, 186, 1))),
                            Container(
                                margin: EdgeInsets.symmetric(vertical: 5.px),
                                padding: EdgeInsets.only(
                                    top: 15.px,
                                    left: 15.px,
                                    right: 15.px,
                                    bottom: 10.px),
                                decoration: BoxDecoration(
                                    color:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    borderRadius: BorderRadius.circular(12.px)),
                                child: isBlackLandLoaded
                                    ? Column(
                                        children: bindPlotInfos
                                            .map((item) => buildPlotItem(item))
                                            .toList(),
                                      )
                                    : Container(
                                        padding: EdgeInsets.only(
                                          top: 20.px,
                                          bottom: 20.px,
                                        ),
                                        child: const SpinKitCircle(
                                          // color: HexColor('#16B760'),
                                          color: Color.fromRGBO(0, 127, 255, 1),
                                          size: 60.0,
                                        ),
                                      )),
                            SizedBox(
                              height: 10.px,
                            ),
                            Row(
                              children: [
                                Text("银行卡信息",
                                    style: TextStyle(
                                        fontSize: 14.px,
                                        fontWeight: FontWeight.w400,
                                        color: const Color.fromRGBO(
                                            174, 176, 186, 1))),
                                const Spacer(),
                                GestureDetector(
                                  onTap: () {
                                    _bankType = bankcard?.bankAccountId!;
                                    if (bankcards.isEmpty) {
                                      showToast("无银行卡可选");
                                      return;
                                    }
                                    showModalBottomSheet(
                                        context: context,
                                        elevation: 10,
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.px)),
                                        builder: (BuildContext context) {
                                          return handleShowBankList();
                                        });
                                  },
                                  behavior: HitTestBehavior.opaque,
                                  child: Row(
                                    children: [
                                      ImageIcon(
                                        const AssetImage(
                                            "assets/images/insure/ic_change_card.png"),
                                        size: 16.px,
                                      ),
                                      SizedBox(
                                        width: 2.px,
                                      ),
                                      Text("更换银行卡",
                                          style: TextStyle(
                                              fontSize: 14.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  0, 0, 0, 1))),
                                      SizedBox(
                                        width: 15.px,
                                      )
                                    ],
                                  ),
                                )
                              ],
                            ),
                            Container(
                                margin: EdgeInsets.symmetric(vertical: 5.px),
                                padding: EdgeInsets.all(15.px),
                                decoration: BoxDecoration(
                                    color:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    borderRadius: BorderRadius.circular(6.px)),
                                child: Column(
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(bottom: 15.px),
                                      child: Row(children: [
                                        Text("银行卡号",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(bankcard?.bankAccount ?? '',
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 15.px),
                                      child: Row(children: [
                                        Text("卡片类型",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(
                                            bankcard?.bankcardTypeNo == null
                                                ? ""
                                                : getDictNameByType(
                                                    bankCardTypes,
                                                    bankcard!.bankcardTypeNo!),
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    ),
                                    Divider(
                                      height: 1.px,
                                      color: const Color.fromRGBO(
                                          234, 238, 243, 1),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(top: 15.px),
                                      child: Row(children: [
                                        Text("银行名称",
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    98, 99, 110, 1))),
                                        const Spacer(),
                                        Text(
                                            bankcard?.bankName == null
                                                ? ""
                                                : getDictNameByType(
                                                    bankNameTypes,
                                                    bankcard!.bankName!),
                                            style: TextStyle(
                                              fontSize: 16.px,
                                              fontWeight: FontWeight.w400,
                                              color: const Color.fromRGBO(
                                                  21, 24, 48, 1),
                                            ))
                                      ]),
                                    )
                                  ],
                                )),
                            SizedBox(
                              height: 10.px,
                            ),
                            Text("签字",
                                style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w400,
                                    color: const Color.fromRGBO(
                                        174, 176, 186, 1))),
                            GestureDetector(
                              behavior: HitTestBehavior.opaque,
                              onTap: () async {
                                var result = await Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => InsureSignNamePage(
                                      signName: farmer.name ?? "",
                                    ),
                                  ),
                                );
                                if (result != null) {
                                  setState(() {
                                    signatureImage = result['image'];
                                    signaturePoints = result['points'];
                                    _toUploadSignImage(signatureImage);
                                    _toInsureSignCheck(signaturePoints);
                                  });
                                }
                              },
                              child: Container(
                                height: 155.px,
                                width: 351.px,
                                margin: EdgeInsets.symmetric(vertical: 5.px),
                                padding: EdgeInsets.all(15.px),
                                decoration: BoxDecoration(
                                    color:
                                        const Color.fromRGBO(255, 255, 255, 1),
                                    borderRadius: BorderRadius.circular(6.px)),
                                child: signatureImage == null
                                    ? Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Image(
                                            image: const AssetImage(
                                                "assets/images/insure/ic_sign_n.png"),
                                            width: 59.px,
                                            height: 59.px,
                                          ),
                                          Text("点击签字",
                                              style: TextStyle(
                                                fontSize: 14.px,
                                                fontWeight: FontWeight.w400,
                                                color: const Color.fromRGBO(
                                                    0, 0, 0, 1),
                                              )),
                                        ],
                                      )
                                    : Image.memory(
                                        signatureImage!,
                                        width: 351.px,
                                        height: 155.px,
                                      ),
                              ),
                            ),
                            Container(
                                width: 351.px,
                                margin:
                                    EdgeInsets.only(top: 5.px, bottom: 15.px),
                                child: Column(children: [
                                  Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          width: 30.px,
                                          child: CupertinoCheckbox(
                                              activeColor: const Color.fromRGBO(
                                                  0, 152, 91, 1),
                                              value: isAgree,
                                              shape: const StadiumBorder(),
                                              onChanged: (s) {
                                                setState(() {
                                                  isAgree = s!;
                                                });
                                              }),
                                        ),
                                        Container(
                                          width: 320.px,
                                          margin: EdgeInsets.only(top: 10.px),
                                          child: RichText(
                                              text: TextSpan(
                                                  text: '我已仔细阅读',
                                                  style: TextStyle(
                                                      fontSize: 12.px,
                                                      fontWeight:
                                                          FontWeight.w400,
                                                      color:
                                                          const Color.fromRGBO(
                                                              21, 24, 48, 0.8)),
                                                  children: [
                                                TextSpan(
                                                    text: '《北大荒集团种植业保险投保告知书》',
                                                    recognizer:
                                                        TapGestureRecognizer()
                                                          ..onTap = () async {
                                                            var result =
                                                                await Navigator
                                                                    .push(
                                                              context,
                                                              MaterialPageRoute(
                                                                builder:
                                                                    (context) =>
                                                                        const InsureNotificationPage(),
                                                              ),
                                                            );
                                                            if (result !=
                                                                    null &&
                                                                result ==
                                                                    true) {
                                                              setState(() {
                                                                isAgree = true;
                                                              });
                                                            }
                                                          },
                                                    style: TextStyle(
                                                        fontSize: 12.px,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: const Color
                                                            .fromRGBO(
                                                            2, 139, 93, 1))),
                                                TextSpan(
                                                    text:
                                                        '，同意将承包耕地上种植的全部符合承保条的作物参加种植业保险，对投保情况知情认可。',
                                                    style: TextStyle(
                                                        fontSize: 12.px,
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        color: const Color
                                                            .fromRGBO(
                                                            21, 24, 48, 0.8)))
                                              ])),
                                        )
                                      ])
                                ])),
                          ],
                        ),
                      ),
                      SafeArea(
                        child: Container(
                            padding: EdgeInsets.only(
                                bottom: 30.px,
                                top: 10.px,
                                right: 16.px,
                                left: 16.px),
                            height: 90.px,
                            width: 375.px,
                            color: Colors.white,
                            child: GestureDetector(
                              onTap: () {
                                if (identityEndDate == "请选择" ||
                                    identityStartDate == "请选择") {
                                  showToast("请选择身份证有效期");
                                  return;
                                }
                                if (!_isSignNameFlag) {
                                  showToast('请先签字');
                                  return;
                                }
                                if (isAgree) {
                                  // showGeneralDialog(
                                  //     context: context,
                                  //     pageBuilder: (BuildContext context,
                                  //         Animation<double> animation,
                                  //         Animation<double>
                                  //             secondaryAnimation) {
                                  //       return showConfirmDialog(context);
                                  //     });
                                  BrnDialogManager.showConfirmDialog(context,
                                      message: "您已完成土地承包合同签订是否进行阳光农险投保确认",
                                      cancel: "取消",
                                      confirm: "确认", onConfirm: () {
                                    _toInsureRetainedSign();
                                  }, onCancel: () {
                                    Navigator.of(context).pop();
                                  });
                                } else {
                                  showToast('请先阅读并同意投保告知书');
                                }
                              },
                              behavior: HitTestBehavior.opaque,
                              child: Container(
                                  alignment: Alignment.center,
                                  width: 345.px,
                                  height: 50.px,
                                  decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(6.px)),
                                    gradient: const LinearGradient(
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                        stops: [
                                          0.0,
                                          0.9,
                                        ],
                                        colors: [
                                          Color.fromRGBO(37, 191, 145, 1),
                                          Color.fromRGBO(2, 139, 93, 1),
                                        ]),
                                  ),
                                  child: Text("确认投保",
                                      style: TextStyle(
                                          fontSize: 16.px,
                                          color: Colors.white))),
                            )),
                      )
                    ]),
              ));
  }

  /// 显示确认弹窗
  Widget showConfirmDialog(BuildContext context) {
    return Center(
      child: Container(
          height: 319.5.px,
          width: 250.5.px,
          padding: EdgeInsets.only(
            top: 195.px,
            bottom: 8.px,
          ),
          alignment: Alignment.centerLeft,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              image: const DecorationImage(
                image:
                    AssetImage('assets/images/insure/ic_sign_confirm_bg.png'),
                fit: BoxFit.cover,
              )),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
            Container(
              padding: EdgeInsets.only(
                left: 30.px,
                right: 25.px,
              ),
              child: Text(
                "您已完成土地承包合同签订是否进行阳光农险投保确认？",
                style: TextStyle(
                    decoration: TextDecoration.none,
                    fontSize: 15.px,
                    fontWeight: FontWeight.w400,
                    color: const Color.fromRGBO(0, 0, 0, 1)),
              ),
            ),
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                      alignment: Alignment.center,
                      width: 105.px,
                      height: 40.px,
                      margin: EdgeInsets.only(left: 15.px, top: 25.px),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(6.px)),
                          color: const Color.fromRGBO(236, 239, 247, 1)),
                      child: Text("取消",
                          style: TextStyle(
                            fontSize: 16.px,
                            fontWeight: FontWeight.w500,
                            color: const Color.fromRGBO(21, 24, 48, 0.8),
                            decoration: TextDecoration.none,
                          ))),
                ),
                GestureDetector(
                  onTap: () {
                    _toInsureRetainedSign();
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Container(
                      alignment: Alignment.center,
                      width: 105.px,
                      height: 40.px,
                      margin: EdgeInsets.only(left: 10.px, top: 25.px),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(6.px)),
                        gradient: const LinearGradient(
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                            stops: [
                              0.0,
                              0.9,
                            ],
                            colors: [
                              Color.fromRGBO(37, 191, 145, 1),
                              Color.fromRGBO(2, 139, 93, 1),
                            ]),
                      ),
                      child: Text("确认",
                          style: TextStyle(
                            decoration: TextDecoration.none,
                            fontSize: 16.px,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                          ))),
                )
              ],
            )
          ])),
    );
  }

  /// 耕地列表
  Widget buildPlotItem(InsureContractInfoPlotList item) {
    return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Image(
                  image:
                      const AssetImage("assets/images/insure/ic_plot_tip.png"),
                  height: 20.px,
                  width: 20.px),
              SizedBox(width: 4.px),
              Text(
                "耕地地号 ${item.landNumber}",
                style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w500,
                    color: const Color.fromRGBO(0, 0, 0, 1)),
              )
            ],
          ),
          Container(
              margin: EdgeInsets.only(top: 8.px, bottom: 8.px),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(6.px),
                gradient: const LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  stops: [
                    0.0,
                    0.9,
                  ],
                  colors: [
                    Color.fromRGBO(238, 255, 248, 1),
                    Color.fromRGBO(248, 255, 252, 1),
                  ],
                ),
              ),
              padding: EdgeInsets.only(top: 8.px, bottom: 8.px),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "耕地类型",
                            style: TextStyle(
                                fontSize: 12.px,
                                fontWeight: FontWeight.w400,
                                color: const Color.fromRGBO(21, 24, 48, 0.6)),
                          ),
                          SizedBox(height: 4.px),
                          Text(
                              getDictNameByType(
                                  plowLandTypes, item.plowlandNo!),
                              style: TextStyle(
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600,
                                color: const Color.fromRGBO(0, 0, 0, 1),
                              ))
                        ]),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "地块面积(亩)",
                            style: TextStyle(
                                fontSize: 12.px,
                                fontWeight: FontWeight.w400,
                                color: const Color.fromRGBO(21, 24, 48, 0.6)),
                          ),
                          SizedBox(height: 4.px),
                          Text("${item.area}",
                              style: TextStyle(
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600,
                                color: const Color.fromRGBO(0, 0, 0, 1),
                              ))
                        ]),
                    Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "作物",
                            style: TextStyle(
                                fontSize: 12.px,
                                fontWeight: FontWeight.w400,
                                color: const Color.fromRGBO(21, 24, 48, 0.6)),
                          ),
                          SizedBox(height: 4.px),
                          Text(getDictNameByType(cropTypes, item.cropNo!),
                              style: TextStyle(
                                fontSize: 16.px,
                                fontWeight: FontWeight.w600,
                                color: const Color.fromRGBO(0, 0, 0, 1),
                              ))
                        ])
                  ]))
        ]);
  }

  /// 银行列表选择
  Widget handleShowBankList() {
    return BottomSheet(
        enableDrag: false,
        onClosing: () {},
        builder: (BuildContext context) {
          return Container(
              height: 248.px,
              width: 375.px,
              color: const Color.fromRGBO(255, 255, 255, 1),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.only(left: 9.px, top: 15.px),
                      child: Row(
                        children: [
                          GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            behavior: HitTestBehavior.opaque,
                            child: ImageIcon(
                              const AssetImage(
                                  "assets/images/insure/ic_back.png"),
                              size: 28.px,
                            ),
                          ),
                          SizedBox(width: 86.px),
                          Text(
                            "选择更改银行卡",
                            style: TextStyle(
                                fontSize: 18.px,
                                fontWeight: FontWeight.w400,
                                color: const Color.fromRGBO(0, 0, 0, 1)),
                          ),
                        ],
                      ),
                    ),
                    Expanded(
                        child: SingleChildScrollView(
                            child: Column(
                                children: bankcards
                                    .map((item) => Container(
                                        margin: EdgeInsets.only(top: 5.px),
                                        padding: EdgeInsets.only(
                                            left: 16.px, right: 16.px),
                                        decoration: BoxDecoration(
                                            color: const Color.fromRGBO(
                                                255, 255, 255, 1),
                                            borderRadius:
                                                BorderRadius.circular(6.px)),
                                        child: Column(
                                          children: [
                                            RadioListTile(
                                              activeColor: const Color.fromRGBO(
                                                  2, 139, 93, 1),
                                              contentPadding: EdgeInsets.zero,
                                              dense: true,
                                              secondary: Image(
                                                  width: 25.px,
                                                  height: 25.px,
                                                  image: AssetImage(
                                                      "assets/images/pay/bankLogo/${item.bankName}.png")),
                                              title: Text(
                                                  "${getDictNameByType(bankNameTypes, item.bankName!)}(尾号 ${item.bankAccount!.substring(item.bankAccount!.length - 4)})",
                                                  style: TextStyle(
                                                      color:
                                                          const Color.fromRGBO(
                                                              51, 51, 51, 1),
                                                      fontSize: 16.px,
                                                      fontWeight:
                                                          FontWeight.w400)),
                                              value: item.bankAccountId,
                                              groupValue: _bankType,
                                              onChanged: (type) {
                                                (context as Element)
                                                    .markNeedsBuild();
                                                _bankType = type;
                                                toChangeBank(type);
                                              },
                                              controlAffinity:
                                                  ListTileControlAffinity
                                                      .trailing,
                                            ),
                                            SizedBox(height: 5.px),
                                            Divider(
                                              height: 0.5.px,
                                              indent: 30.px,
                                              color: const Color.fromRGBO(
                                                  234, 238, 243, 1),
                                            )
                                          ],
                                        )))
                                    .toList()))),
                  ]));
        });
  }

  void showDatePickerDialog(type, dateTime) async {
    var time = await _showDatePicker(dateTime);
    if (time != null) {
      int timestamp = (time as DateTime).millisecondsSinceEpoch;
      String times =
          FormatTool.timeFormat(timestamp: timestamp, formate: 'yyyyMMdd');
      setState(() {
        if (type == 0) {
          identityStartDate = times;
        } else if (type == 1) {
          identityEndDate = times;
        }
      });
    }
  }

  Future _showDatePicker(String dateTime) async {
    DateTime initialDate = DateTime.now();
    if (dateTime.isNotEmpty) {
      if (dateTime != "请选择" && dateTime != "长期" && isDateYYYYMMDD(dateTime)) {
        initialDate = DateTime.parse(dateTime);
      }
    }
    var time = await showDatePicker(
      context: context,
      initialEntryMode: DatePickerEntryMode.calendarOnly,
      initialDate: initialDate, // 初始化选中日期
      firstDate: DateTime(1950, 1), // 开始日期
      lastDate: DateTime(2099, 12), // 结束日期
      currentDate: initialDate, // 当前日期
      helpText: "", // 左上方提示
      cancelText: "取消", // 取消按钮文案
      confirmText: "确定", // 确认按钮文案
      initialDatePickerMode: DatePickerMode.day, // 日期选择模式，默认为天数选择
    );
    return time;
  }

  // YYYYMMDD:
  bool isDateYYYYMMDD(String input) {
    RegExp regex = RegExp(r'^\d{4}\d{2}\d{2}$');
    return regex.hasMatch(input);
  }

  Future<void> showBusyDialog(BuildContext context,
      {String msg = '上传中...'}) async {
    showGeneralDialog(
      context: context,
      barrierDismissible: false, // 设置用户是否可以点击遮罩层来关闭对话框
      barrierLabel: msg, // 遮罩层的提示文字
      barrierColor: Colors.black.withOpacity(0.5), // 遮罩层的颜色和透明度
      transitionDuration: const Duration(milliseconds: 150), // 动画时长
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return Center(
          // 对话框内容居中显示
          child: Container(
            alignment: Alignment.center,
            width: 120.px,
            height: 120.px,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.px),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    width: 40.px,
                    height: 40.px,
                    child: const CircularProgressIndicator()),
                SizedBox(height: 16.px),
                Text(msg,
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(44, 44, 52, 0.8)))
              ],
            ),
          ), // 加载指示器
        );
      },
    );
  }

  void _toInsureRetainedSign() {
    if (bankcard == null) {
      showToast("无默认银行卡,请选择银行卡");
      return;
    }

    showBusyDialog(context, msg: "提交中..");
    Map<String, dynamic> personInfo = {
      "orgCode": contract.organizationNo, //contract.organizationNo
      "personName": farmer.name, //farmer.name
      "certBeginDate": identityStartDate, //farmer.validDate ********-********
      "certExpDate":
          identityEndDate == "长期" ? "长期" : identityEndDate, //farmer.validDate
      "certFrontUrl": farmer.idFront, //farmer.idFront
      "certBackUrl": farmer.idBack, //farmer.idBack
      "certNo": farmerId, //farmer.idNumber
      "contactNo": contract.serialNumber, //contract.serialNumber
      "bankCode": bankcard!.bankName, //bankcard.bankName
      "bankName": getDictNameByType(
          bankNameTypes, bankcard!.bankName!), //bankcard.bankName 字典 bank_name
      "bankcardNo": bankcard!.bankAccount, //bankcard.bankAccount
      "bankcardType": bankcard!.bankcardTypeNo, //bankcard.bankcardTypeNo
      "bankcardTypeNm": getDictNameByType(
          bankCardTypes,
          bankcard!
              .bankcardTypeNo!), //bankcard.bankcardTypeNo 字典 bankcard_type_no
      "statYear": contract.yearNo, //contract.yearNo
      "telephone": farmer.phone1, //farmer.phone1
      "signImgUrl": signatureUrl, //签字上传URL
      "contactId": contract.contractId //签字上传UR
    };

    List<Map<String, dynamic>> plotInfos = [];
    for (var item in bindPlotInfos) {
      Map<String, dynamic> plotInfo = {
        "orgCode":
            item.organizationNo, //地块归属组织机构，取值规则：plotList[i].organizationNo
        "cropCode": item.cropNo, //plotList[i].cropNo
        "cropName": item.cropNo, //plotList[i].cropNo 字典 sys_crop
        "landType": item.plowlandNo, //plotList[i].plowlandNo 字典 sys_plowland
        "plotName": item.landNumber, //plotList[i].landNumber
        "plotNo": item.landNumber, //plotList[i].landNumber
        "plotArea": item.area //plotList[i].area
      };
      plotInfos.add(plotInfo);
    }

    Map<String, dynamic> params = {
      "personInfo": personInfo,
      "plotInfos": plotInfos,
    };
    String jsonData = jsonEncode(params);
    InsureService.toInsureRetainedSign(jsonData).then((result) {
      Navigator.of(context).pop();
      if (result.success == true) {
        setState(() {
          bus.emit("insure_list_refresh");
          Navigator.of(context).popUntil((route) =>
              route is MaterialPageRoute &&
              route.builder(context) is InsureMainPage);
        });
      }
    }, onError: (error) {
      Navigator.of(context).pop();
    });
  }

  void _getInsureContractInfo() {
    Map<String, dynamic> params = {
      "contractId": widget.dataItem.contractId,
    };
    InsureService.getInsureContractInfo(params).then((result) {
      setState(() {
        _isLoading = false;
      });
      if (result.success == true) {
        // setState(() {
        InsureContractInfoEntity data =
            InsureContractInfoEntity.fromJson(result.data);
        insureContractInfo = data;
        isBlackLandLoaded = false;
        _getBlackLands();
        if (bankcards.isNotEmpty) {
          bankcard =
              bankcards.firstWhereOrNull((item) => item.isDefault == "1") ??
                  bankcards.first;
        } else {
          bankcard = null;
          showToast("未获取到银行卡信息");
          return;
        }

        if (farmer.validDate != null && farmer.validDate!.isNotEmpty) {
          List<String> identityDateArr = farmer.validDate!.split("-");
          if (identityDateArr.length == 2) {
            identityStartDate = identityDateArr[0];
            identityEndDate = identityDateArr[1];
          } else if (identityDateArr.length == 1) {
            identityStartDate = "请选择";
            identityEndDate = identityDateArr[0];
          }
          if (identityEndDate == "长期") {
            _validityType = 0;
          }
        } else {
          identityStartDate = "请选择";
          identityEndDate = "请选择";
        }
        // });
      }
    }, onError: (error) {
      setState(() {
        _isLoading = false;
      });
    });
  }

  // 获取黑名单地块
  void _getBlackLands() {
    Map<String, dynamic> params = {
      "certNo": farmerId, //身份证号码
      "orgCode": contract.organizationNo, //组织机构编码
      "plotNos": bindPlotInfos.map((item) => item.landNumber).toList() //地块
    };
    String json = jsonEncode(params);
    InsureService.getBlackLands(json).then((result) {
      if (result.success == true) {
        setState(() {
          isBlackLandLoaded = true;
          InsureBlackLandEntity blackLand =
              InsureBlackLandEntity.fromJson(result.data);
          //在isBlack为0时，黑名单地块，需要从地块列表移除
          if (blackLand.isBlack == 0) {
            bindPlotInfos.removeWhere((item) {
              return blackLand.blackPlotNos!.contains(item.landNumber);
            });
          }
        });
      } else {
        setState(() {
          isBlackLandLoaded = true;
        });
      }
    }, onError: (error) {});
  }

  /// 上传签名文件图片
  void _toUploadSignImage(Uint8List? signatureImage) async {
    var formData = FormData.fromMap({
      "file": MultipartFile.fromBytes(signatureImage!.buffer.asUint8List(),
          filename: "signature_${DateTime.now().millisecondsSinceEpoch}.png",
          contentType: MediaType("image", "png")),
    });
    InsureService.uploadFile(formData).then((result) {
      if (result.success == true) {
        setState(() {
          signatureUrl = result.data;
        });
      }
    });
  }

  void _toInsureSignCheck(List<Point> signaturePoints) {
    List<int> points = [];
    for (int i = 0; i < signaturePoints.length; i++) {
      Point point = signaturePoints[i];
      points.add(point.offset.dx.toInt());
      points.add(point.offset.dy.toInt());
    }
    points.add(-1);
    points.add(-1);

    // final points = compute(_getSignaturePoints, signaturePoints);

    Map<String, dynamic> params = {
      "trace": points,
      "name": farmer.name,
    };
    String json = jsonEncode(params);
    showBusyDialog(context, msg: '正在校验签名');
    InsureService.getInsureSignCheck(json).then((result) {
      Navigator.of(context).pop();
      if (result.success == true) {
        showToast(result.msg!);
        setState(() {
          if (result.code == 0) {
            _isSignNameFlag = true;
          }
        });
      } else {
        showToast(result.msg!);
      }
    }, onError: (error) {
      Navigator.of(context).pop();
    });
  }

  List<int> _getSignaturePoints(signaturePoints) {
    List<int> points = [];
    for (int i = 0; i < signaturePoints.length; i++) {
      Point point = signaturePoints[i];
      points.add(point.offset.dx.toInt());
      points.add(point.offset.dy.toInt());
    }
    points.add(-1);
    points.add(-1);
    return points;
  }

  List<DictNode> bankNameTypes = [];
  List<DictNode> bankCardTypes = [];
  List<DictNode> cropTypes = [];
  List<DictNode> plowLandTypes = [];
  void _getDicts() async {
    await InsureService.getDicByKey("bank_name").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            bankNameTypes = result.data!;
          });
        }
      }
    });

    await InsureService.getDicByKey("bankcard_type_no").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            bankCardTypes = result.data!;
          });
        }
      }
    });

    await InsureService.getDicByKey("sys_crop").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            cropTypes = result.data!;
          });
        }
      }
    });

    await InsureService.getDicByKey("sys_plowland").then((result) {
      if (mounted) {
        if (result.success == true) {
          setState(() {
            plowLandTypes = result.data!;
          });
        }
      }
    });
  }

  String getDictNameByType(List<DictNode> dicts, String type) {
    for (var item in dicts) {
      if (item.code == type) {
        return item.name!;
      }
    }
    return "";
  }

  void toChangeBank(String? type) {
    setState(() {
      bankcard =
          bankcards.firstWhere((element) => element.bankAccountId == type);
      farmer.bankcardTypeNo = bankcard?.bankcardTypeNo;
      farmer.bankName = bankcard?.bankName;
      farmer.bankAccount = bankcard?.bankAccount;
    });
    Navigator.of(context).pop();
  }
}
