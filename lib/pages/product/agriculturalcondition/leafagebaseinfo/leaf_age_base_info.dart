import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_view.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/model/leaf_massif_info.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/request/agricultural_condition_service.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import 'leaf_age_base_info_add.dart';
import 'leaf_age_collect_point_list.dart';

class LeafAgeBaseInfoPage extends StatefulWidget {
  const LeafAgeBaseInfoPage({
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _State();
}

class _State extends MixinUseState<LeafAgeBaseInfoPage> {
  late final controller = useController(_Controller(context));
  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("一级采集点"),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: UseBuilder((context) {
        if (controller.isLoading) return _widgetLoading();
        var list = controller.items.value ?? [];

        return Column(
          children: [
            SizedBox(
              height: 14.px,
            ),
            Expanded(
                child: list.isEmpty
                    ? _widgetEmpty()
                    : Container(
                        margin: EdgeInsets.only(left: 14.px, right: 14.px),
                        child: SmartRefresher(
                          enablePullUp: true,
                          onRefresh: controller.refresh,
                          onLoading: controller.loadMore,
                          controller: controller.refreshController,
                          child: ListView.builder(
                              itemCount: controller.items.value?.length ?? 0,
                              itemBuilder: (context, index) {
                                var item = list[index];
                                return ItemWidget(
                                    text: item.massifName ?? "",
                                    onClick: () {
                                      controller.onClickItem(item);
                                    },
                                    isTop: index == 0,
                                    isBottom: index == list.length - 1);
                              }),
                        ))),
            SizedBox(
              height: 14.px,
            ),
            _widgetBottom(),
            SizedBox(
              height: 14.px,
            )
          ],
        );
      }),
    );
  }

  Widget _widgetBottom() {
    return BdhTextButton(
      height: 40.px,
      width: 351.px,
      text: '新增一级采集点',
      textFontWeight: FontWeight.w500,
      textSize: 15.px,
      borderRadius: BorderRadius.all(Radius.circular(6.px)),
      backgroundColor: const Color.fromARGB(240, 94, 139, 245),
      disableBackgroundColor: const Color.fromARGB(255, 224, 223, 223),
      pressedBackgroundColor: const Color.fromARGB(255, 94, 139, 245),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: controller.onClickAdd,
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetEmpty() {
    return const Center(
      child: BdhEmptyView(
        tipInfo: "未查到记录",
      ),
    );
  }
}

class ItemWidget extends StatefulWidget {
  final String text;
  final bool isTop;
  final bool isBottom;
  final VoidCallback onClick;
  const ItemWidget(
      {super.key,
      required this.text,
      this.isTop = false,
      this.isBottom = false,
      required this.onClick});

  @override
  State<ItemWidget> createState() => _ItemWidgetState();
}

class _ItemWidgetState extends State<ItemWidget> {
  bool pressed = false;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: widget.onClick,
        onTapDown: (details) {
          setState(() {
            pressed = true;
          });
        },
        onTapUp: (details) {
          setState(() {
            pressed = false;
          });
        },
        onTapCancel: () {
          setState(() {
            pressed = false;
          });
        },
        child: Container(
          decoration: BoxDecoration(
              color:
                  pressed ? const Color.fromRGBO(1, 1, 1, 0.1) : Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: widget.isTop ? Radius.circular(9.px) : Radius.zero,
                topRight: widget.isTop ? Radius.circular(9.px) : Radius.zero,
                bottomLeft:
                    widget.isBottom ? Radius.circular(9.px) : Radius.zero,
                bottomRight:
                    widget.isBottom ? Radius.circular(9.px) : Radius.zero,
              ),
              border: !widget.isBottom
                  ? Border(
                      bottom: BorderSide(
                          width: 1.px,
                          color: const Color.fromRGBO(226, 235, 231, 0.6)))
                  : null),
          constraints: BoxConstraints(minHeight: 44.px),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                      child: Row(
                    children: [
                      SizedBox(
                        width: 14.px,
                      ),
                      Expanded(
                          child: Text(
                        widget.text,
                        style: TextStyle(
                            fontSize: 14.px, fontWeight: FontWeight.w500),
                      )),
                      SizedBox(
                        width: 14.px,
                      ),
                      Image.asset(
                          width: 6.9.px,
                          height: 11.07.px,
                          ImageHelper.wrapAssets("arrow_right_black.png")),
                      SizedBox(
                        width: 14.px,
                      ),
                    ],
                  ))
                ],
              ),
            ],
          ),
        ));
  }
}

class _Controller extends UseController
    with LoadMoreUseController<LeafMassifInfo> {
  _Controller(super.context);

  void onInit() {
    reload(showLoading: true);
  }

  @override
  int get row => 50;

  @override
  Future reloadFuture(
      {required bool showLoading,
      required bool loadingMore,
      required bool refresh}) {
    var data = {
      "page": page,
      "rows": row,
    };

    return AgriculturalConditionService()
        .massifQueryByPage(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true) {
        total = result.data["total"] ?? 0;

        var loadItems = result.data["records"]
                .map<LeafMassifInfo>((v) => LeafMassifInfo.fromJson(v)) ??
            [];
        var currentItems = items.peek() ?? [];

        if (refresh || showLoading) {
          currentItems.clear();
        }
        currentItems.addAll(loadItems);

        items.value = currentItems;
      }
      loadingStatus.value = LoadingStatus.success;
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        loadingStatus.value = LoadingStatus.success;
      });
    });
  }

  void onClickItem(LeafMassifInfo item) {
    Navigator.of(context).push(CupertinoPageRoute(builder: (context) {
      return LeafAgeBaseInfoListPage(
        params: item,
      );
    })).then((result) {
      if (result == true) {
        onInit();
      }
    });
  }

  void onClickAdd() {
    Navigator.of(context).push(CupertinoPageRoute(builder: (context) {
      return const LeafAgeBaseInfoAddPage();
    })).then((result) {
      if (result == true) {
        onInit();
      }
    });
  }
}
