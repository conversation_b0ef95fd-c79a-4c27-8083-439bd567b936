import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_radio_group_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_searchable_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input_small.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/model/greenhouse.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/model/land.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/model/variety_dict.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/request/agricultural_condition_service.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalrecords/request/agricultural_records_service.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

class LeafAgeBaseInfoAddPage extends StatefulWidget {
  const LeafAgeBaseInfoAddPage({super.key});

  @override
  State<StatefulWidget> createState() => _State();
}

class _State extends MixinUseState<LeafAgeBaseInfoAddPage> {
  late final controller = useController(_Controller(context));
  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("新增"),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: UseBuilder((context) {
        if (controller.isLoading) return _widgetLoading();

        return Column(
          children: [
            SizedBox(
              height: 14.px,
            ),
            widgetContainer(children: [
              widgetInput(
                title: "所在地块",
                placeholder: "请输入所在地块",
                isRequired: true,
                initialValue: controller.landName.value,
                onChange: (v) {
                  controller.landName.value = v;
                },
                showArrow: false,
              ),
              widgetOption(
                  title: "作物名称",
                  placeholder: "请选择作物",
                  isRequired: true,
                  initialValue: controller.raiseCropsNm.value,
                  data: controller.sysCropOption.value ?? [],
                  onChange: (v) {
                    controller.raiseCropsNm.value = v;
                  }),
              widgetOption(
                  title: "品种名称",
                  placeholder: "请选择品种名称",
                  isRequired: true,
                  initialValue: controller.raiseCropsVarietyNm.value,
                  data: controller.cropsVarietyOption.value ?? [],
                  onChange: (v) {
                    controller.raiseCropsVarietyNm.value = v;
                  }),
              BdhRadioGroupPicker(
                iconSize: 18.px,
                fontSize: 14.px,
                minHeight: 44.px,
                titleColor: const Color.fromRGBO(51, 51, 51, 0.4),
                showBottomLine: controller.detectLocation.value == "02" ||
                    controller.detectLocation.value == "01" ||
                    controller.detectLocation.value == null,
                initialValue: controller.detectLocation.value,
                item: FormItem(
                    title: "监测位置",
                    isRequired: true,
                    data: controller.detectLocationOption.value ?? []),
                onChange: (v) {
                  controller.detectLocation.value = v;
                },
              ),
              if (controller.detectLocation.value == "01")
                widgetSearchableOption(
                    title: "关联大棚",
                    placeholder: "请选择大棚",
                    isRequired: true,
                    showBottomLine: false,
                    initialValue: controller.greenhouseName.value,
                    data: controller.greenhouseOption.value ?? [],
                    onChange: (v) {
                      controller.greenhouseName.value = v;
                    }),
              if (controller.detectLocation.value == "02")
                widgetSearchableOption(
                    title: "关联地号",
                    placeholder: "请选择地号",
                    isRequired: true,
                    showBottomLine: false,
                    initialValue: controller.plotName.value,
                    data: controller.landOption.value ?? [],
                    onChange: (v) {
                      controller.plotName.value = v;
                    })
            ]),
            SizedBox(
              height: 14.px,
            ),
            _widgetBottom(),
            SizedBox(
              height: 14.px,
            )
          ],
        );
      }),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      margin: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Column(
        children: children,
      ),
    );
  }

  Widget widgetOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DictNode? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(DictNode?)? onChange,
      bool showArrow = true,
      Widget Function(BuildContext, FormFieldState<DictNode>)? suffixBuilder}) {
    return BdhSingleDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      minHeight: 44.px,
      showArrow: showArrow,
      initialValue: initialValue,
      placeholder: placeholder,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange != null
          ? (v) {
              controller._unfocus();
              onChange.call(v);
            }
          : null,
      canShowPicker: canShowPicker != null
          ? () {
              controller._unfocus();
              return canShowPicker.call();
            }
          : null,
      suffixBuilder: suffixBuilder,
      checkState: true,
    );
  }

  Widget widgetSearchableOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DictNode? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(DictNode?)? onChange,
      bool showArrow = true,
      Widget Function(BuildContext, FormFieldState<DictNode>)? suffixBuilder,
      final Widget Function(BuildContext, int, DictNode, bool,
              void Function(DictNode item, bool checked))?
          pickerItemBuilder}) {
    return BdhSearchableSingleDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      minHeight: 44.px,
      maxHeight: 400.px,
      showArrow: showArrow,
      initialValue: initialValue,
      placeholder: placeholder,
      diameterRatio: 100,
      squeeze: 1,
      itemExtent: 40.px,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange != null
          ? (v) {
              controller._unfocus();
              onChange.call(v);
            }
          : null,
      canShowPicker: canShowPicker != null
          ? () {
              controller._unfocus();
              return canShowPicker.call();
            }
          : null,
      suffixBuilder: suffixBuilder,
      pickerItemBuilder: pickerItemBuilder,
      checkState: true,
    );
  }

  Widget widgetInput(
      {required String title,
      String placeholder = "请输入",
      String? unit,
      String? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      bool Function()? canShowPicker,
      void Function(String?)? onChange,
      bool showArrow = true,
      int? maxLength,
      Widget Function(BuildContext, FormFieldState<String>)?
          rightWidgetBuilder}) {
    return BdhTextInputSmall(
      item:
          FormItem(title: title, isRequired: isRequired, isCanEdit: isCanEdit),
      minHeight: 44.px,
      initialValue: initialValue,
      placeHolder: placeholder,
      textInputType: const TextInputType.numberWithOptions(decimal: true),
      fontSize: 14.px,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange,
      maxLength: maxLength,
      rightWidgetBuilder: rightWidgetBuilder ??
          (unit != null
              ? (context, state) {
                  return Padding(
                      padding: EdgeInsets.only(left: 6.px),
                      child: Text(unit,
                          style: TextStyle(
                            fontSize: 14.px,
                            fontWeight: FontWeight.w400,
                            color: const Color.fromRGBO(44, 44, 52, 1),
                          )));
                }
              : null),
    );
  }

  Widget _widgetBottom() {
    return BdhTextButton(
      height: 40.px,
      width: 351.px,
      text: '保存',
      textFontWeight: FontWeight.w500,
      textSize: 15.px,
      borderRadius: BorderRadius.all(Radius.circular(6.px)),
      backgroundColor: const Color.fromARGB(240, 94, 139, 245),
      disableBackgroundColor: const Color.fromARGB(255, 224, 223, 223),
      pressedBackgroundColor: const Color.fromARGB(255, 94, 139, 245),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: controller.onClickSave,
    );
  }
}

class _Controller extends UseController {
  _Controller(super.context);

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  bool get isLoading =>
      loadingStatus.value == LoadingStatus.loading ||
      loadingStatus.value == LoadingStatus.init;

  late final sysCropOption = useList<DictNode>();

  late final detectLocationOption = useList<DictNode>();

  late final cropsVarietyOption = useList<DictNode>();

  late final greenhouseOption = useList<DictNode>();

  late final landOption = useList<DictNode>();

  late final landName = use<String>(null);
  late final raiseCropsNm = use<DictNode>(null)
    ..onChange = (v) {
      raiseCropsVarietyNm.value = null;
      varietyDict();
    };
  late final raiseCropsVarietyNm = use<DictNode>(null);
  late final detectLocation = use<String>(null)
    ..onChange = (v) {
      greenhouseName.value = null;
      plotName.value = null;
    };
  late final plotName = use<DictNode>(null);
  late final greenhouseName = use<DictNode>(null);

  void varietyDict() {
    var orgCode = StorageUtil.orgCode();
    var raiseCropsCd = raiseCropsNm.value?.code;
    showLoading(context, content: "加载中..  ");
    AgriculturalConditionService()
        .varietyDict(
            data: {'raiseCropsCd': raiseCropsCd, 'orgCode': orgCode},
            cancelToken: createCancelToken())
        .then((result) {
          if (result.success == true &&
              result.code == 0 &&
              result.data! != null) {
            cropsVarietyOption.value = result.data?.map<DictNode>((test) {
                  var item = VarietyDict.fromJson(test);
                  return DictNode(
                      code: item.raiseCropsVarietyCd?.toString() ?? "",
                      name: item.raiseCropsVarietyNm ?? "",
                      data: item);
                }).toList() ??
                [];
          }
        })
        .onError(handleError)
        .whenComplete(() {
          if (!context.mounted) {
            return;
          }
          hideLoading(context);
        });
  }

  void onInit() {
    var future0 = AgriculturalRecordsService()
        .getDict(data: 'sys_crop', cancelToken: createCancelToken());
    var future1 = AgriculturalRecordsService()
        .getDict(data: 'detect_location', cancelToken: createCancelToken());
    var future2 = AgriculturalConditionService().greenhouse(data: {
      "needPagination": false,
      "greenhouseName": '',
      "orgCode": '',
      "page": 1,
      "rows": 10000,
    }, cancelToken: createCancelToken());

    var future3 = AgriculturalConditionService().getland(data: {
      "needPagination": false,
      "plotNo": '',
      "plotName": '',
      "orgCode": '',
      "page": 1,
      "rows": 10000,
    }, cancelToken: createCancelToken());

    loadingStatus.value == LoadingStatus.loading;

    Future.wait([future0, future1, future2, future3]).then((result) {
      var result0 = result[0] as DictList;

      if (result0.code == 0 &&
          result0.success == true &&
          result0.data != null) {
        //sysCropOption.value = result0.data ?? [];
        List<DictNode> d0 = result0.data ?? [];
        d0.sort((a, b) => int.parse(a.code ?? "0") - int.parse(b.code ?? "0"));
        sysCropOption.value = d0;
      }

      var result1 = result[1] as DictList;
      if (result1.code == 0 &&
          result1.success == true &&
          result1.data != null) {
        detectLocationOption.value = result1.data ?? [];
      }

      var result2 = result[2] as RequestNoData;
      if (result2.code == 0 &&
          result2.success == true &&
          result2.data != null) {
        greenhouseOption.value = result2.data["records"]?.map<DictNode>((test) {
              var item = Greenhouse.fromJson(test);
              return DictNode(
                  code: item.greenhouseId?.toString() ?? "",
                  name: item.greenhouseName ?? "",
                  data: item);
            }).toList() ??
            [];
      }

      var result3 = result[3] as RequestNoData;
      if (result3.code == 0 &&
          result3.success == true &&
          result3.data != null) {
        landOption.value =
            result3.data["page"]?["records"]?.map<DictNode>((test) {
                  var item = Land.fromJson(test);
                  return DictNode(
                      code: item.plotNo?.toString() ?? "",
                      name: item.plotName ?? "",
                      data: item);
                }).toList() ??
                [];
      }

      loadingStatus.value = LoadingStatus.success;
    }).onError(handleError);
  }

  void onClickSave() {
    if (landName.value?.isEmpty ?? true) {
      showToast("请输入所在地块");
      return;
    }
    if (raiseCropsNm.value == null) {
      showToast("请选择作物名称");
      return;
    }
    if (raiseCropsVarietyNm.value == null) {
      showToast("请选择品种名称");
      return;
    }
    if (detectLocation.value == null) {
      showToast("请选择监测位置");
      return;
    }

    if (detectLocation.value == "01" && greenhouseName.value == null) {
      showToast("请选择关联大棚");
      return;
    }

    if (detectLocation.value == "02" && plotName.value == null) {
      showToast("请选择关联地号");
      return;
    }

    var data = {
      'plotNo': plotName.value?.code,
      "plotName": plotName.value?.name,
      "landName": landName.value,
      "raiseCropsCd": raiseCropsNm.value?.code,
      "raiseCropsNm": raiseCropsNm.value?.code,
      "raiseCropsVarietyCd": raiseCropsVarietyNm.value?.code,
      "raiseCropsVarietyNm": raiseCropsVarietyNm.value?.name,
      "detectLocation": detectLocation.value,
      "greenhouseId": greenhouseName.value?.code,
      "greenhouseName": greenhouseName.value?.name,
    };

    showLoading(context, content: "正在提交..  ");
    AgriculturalConditionService()
        .massifAdd(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
      if (result.success == true && result.code == 0) {
        showToast("添加成功");
        Navigator.of(context).pop(true);
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        if (!context.mounted) {
          return;
        }
        hideLoading(context);
      });
    });
  }

  void _unfocus() {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }
}
