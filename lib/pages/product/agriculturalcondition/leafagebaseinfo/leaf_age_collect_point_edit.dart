import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_map_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_searchable_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input_small.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/model/leaf_gather_info.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/model/leaf_massif_info.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/request/agricultural_condition_service.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';
import 'package:oktoast/oktoast.dart';

class LeafAgeBaseInfoEditPage extends StatefulWidget {
  final LeafGatherInfo? params;
  final LeafMassifInfo item;
  const LeafAgeBaseInfoEditPage({super.key, this.params, required this.item});

  @override
  State<StatefulWidget> createState() => _State();
}

class _State extends MixinUseState<LeafAgeBaseInfoEditPage> {
  late final controller =
      useController(_Controller(context, widget.params, widget.item));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("采集点编辑"),
      ),
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: UseBuilder((context) {
        if (controller.isLoading) return _widgetLoading();
        return Form(
            key: controller.formKey,
            child: SingleChildScrollView(
                child: Column(
              children: [
                SizedBox(
                  height: 14.px,
                ),
                widgetContainer(children: [
                  widgetText(
                    title: "采集点名称",
                    isRequired: true,
                    initialValue: controller.gatherName.value,
                  ),
                  BdhMapPicker(
                    titleStyle: TextStyle(
                        fontSize: 14.px,
                        color: const Color.fromRGBO(51, 51, 51, 0.4)),
                    placeholderStyle: TextStyle(
                        fontSize: 14.px,
                        color: const Color.fromRGBO(51, 51, 51, 0.4)),
                    textStyle: TextStyle(
                        fontSize: 14.px,
                        color: const Color.fromRGBO(51, 51, 51, 1)),
                    minHeight: 44.px,
                    item: FormItem(title: "采集点经纬度", isRequired: true),
                    initialValue: controller.location.value,
                    checkState: true,
                    autoValidate: true,
                    validator: (v) {
                      if (v == null) {
                        return "请填写采集点经纬度";
                      }

                      return null;
                    },
                    onChange: (v) {
                      controller.location.value = v;
                    },
                  ),
                  widgetDateOption(
                      title: "播种日期",
                      placeholder: "请选择播种日期",
                      isRequired: true,
                      initialValue: controller.sowTime.value,
                      minimumDate: null,
                      maximumDate: null,
                      validator: (v) {
                        if (v == null) {
                          return "请选择播种日期";
                        }
                        if (controller.transplantTime.value != null &&
                            v.isAfter(controller.transplantTime.value!)) {
                          return "播种日期需小于插秧日期";
                        }
                        return null;
                      },
                      onChange: (v) {
                        controller.sowTime.value = v;
                      }),
                  if (controller.isRice) ...[
                    widgetDateOption(
                        title: "插秧日期",
                        placeholder: "请选择插秧日期",
                        isRequired: true,
                        initialValue: controller.transplantTime.value,
                        minimumDate: null,
                        maximumDate: null,
                        validator: (v) {
                          if (v == null) {
                            return "请选择插秧日期";
                          }
                          if (controller.sowTime.value != null &&
                              v.isBefore(controller.sowTime.value!)) {
                            return "插秧日期需大于播种日期";
                          }
                          return null;
                        },
                        onChange: (v) {
                          controller.transplantTime.value = v;
                        }),
                    widgetInput(
                      title: "插秧叶龄",
                      placeholder: "请输入叶龄",
                      isRequired: true,
                      initialValue: controller.leafAge.value,
                      validator: (v) {
                        if (v?.isEmpty ?? true) {
                          return "请输入叶龄";
                        }
                        var reg = RegExp(r"^([1-9]|10|11|12|13|14)(\.\d{1})?$");
                        if (!reg.hasMatch(v!)) {
                          return "插秧叶龄必填，请输入1-14.9之间的数字保留一位小数";
                        }
                        return null;
                      },
                      onChange: (v) {
                        controller.leafAge.value = v;
                      },
                      showArrow: false,
                    ),
                    widgetInput(
                      title: "插秧规格-行距(cm)",
                      placeholder: "请输入插秧规格",
                      isRequired: true,
                      initialValue: controller.specsLength.value,
                      onChange: (v) {
                        controller.specsLength.value = v;
                      },
                      validator: (v) {
                        if (v?.isEmpty ?? true) {
                          return "请输入插秧规格";
                        }
                        var reg = RegExp(r"^[1-9][0-9]?\.[0-9]$|^[1-9][0-9]?$");
                        if (!reg.hasMatch(v!)) {
                          return "请输入1-99.9之间的数字，最多保留1位小数";
                        }
                        return null;
                      },
                      showArrow: false,
                    ),
                    widgetInput(
                      title: "插秧规格-穴距(cm)",
                      placeholder: "请输入插秧规格",
                      isRequired: true,
                      initialValue: controller.specsWidth.value,
                      onChange: (v) {
                        controller.specsWidth.value = v;
                      },
                      validator: (v) {
                        if (v?.isEmpty ?? true) {
                          return "请输入插秧规格";
                        }
                        var reg = RegExp(r"^[1-9][0-9]?\.[0-9]$|^[1-9][0-9]?$");
                        if (!reg.hasMatch(v!)) {
                          return "请输入1-99.9之间的数字，最多保留1位小数";
                        }
                        return null;
                      },
                      showArrow: false,
                    ),
                  ],
                  widgetInput(
                    title: "采集点学位数",
                    placeholder: "请输入穴位数",
                    isRequired: true,
                    isCanEdit: !controller.isEdit,
                    showBottomLine: false,
                    initialValue: controller.holeNo.value,
                    onChange: (v) {
                      controller.holeNo.value = v;
                    },
                    validator: (v) {
                      if (v?.isEmpty ?? true) {
                        return "请输入穴位数";
                      }
                      var reg = RegExp(r"^([1-9]|10)$");
                      if (!reg.hasMatch(v!)) {
                        return "必填且整数大于等于1小于等于10";
                      }
                      return null;
                    },
                    showArrow: false,
                  ),
                ]),
                SizedBox(
                  height: 14.px,
                ),
                _widgetBottom(),
                SizedBox(
                  height: 14.px,
                )
              ],
            )));
      }),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget widgetDateOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DateTime? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      bool Function()? canShowPicker,
      DateTime? minimumDate,
      DateTime? maximumDate,
      String? Function(DateTime?)? validator,
      void Function(DateTime?)? onChange}) {
    return BdhDatePicker(
        item: FormItem(
            title: title, isRequired: isRequired, isCanEdit: isCanEdit),
        initialValue: initialValue,
        placeholder: placeholder,
        textAlign: TextAlign.right,
        titleStyle: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
        placeholderStyle: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
        textStyle: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
        showBottomLine: showBottomLine,
        onChanged: onChange != null
            ? (v) {
                controller._unfocus();
                onChange.call(v);
              }
            : null,
        checkState: true,
        minHeight: 44.px,
        minimumDate: minimumDate,
        maximumDate: maximumDate,
        validator: validator,
        canShowPicker: canShowPicker != null
            ? () {
                controller._unfocus();
                return canShowPicker.call();
              }
            : null);
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      margin: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Column(
        children: children,
      ),
    );
  }

  Widget widgetOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DictNode? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(DictNode?)? onChange,
      bool showArrow = true,
      Widget Function(BuildContext, FormFieldState<DictNode>)? suffixBuilder}) {
    return BdhSingleDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      minHeight: 44.px,
      showArrow: showArrow,
      initialValue: initialValue,
      placeholder: placeholder,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange != null
          ? (v) {
              controller._unfocus();
              onChange.call(v);
            }
          : null,
      canShowPicker: canShowPicker != null
          ? () {
              controller._unfocus();
              return canShowPicker.call();
            }
          : null,
      suffixBuilder: suffixBuilder,
      checkState: true,
    );
  }

  Widget widgetSearchableOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DictNode? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(DictNode?)? onChange,
      bool showArrow = true,
      Widget Function(BuildContext, FormFieldState<DictNode>)? suffixBuilder,
      final Widget Function(BuildContext, int, DictNode, bool,
              void Function(DictNode item, bool checked))?
          pickerItemBuilder}) {
    return BdhSearchableSingleDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      minHeight: 44.px,
      maxHeight: 400.px,
      showArrow: showArrow,
      initialValue: initialValue,
      placeholder: placeholder,
      diameterRatio: 100,
      squeeze: 1,
      itemExtent: 40.px,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange != null
          ? (v) {
              controller._unfocus();
              onChange.call(v);
            }
          : null,
      canShowPicker: canShowPicker != null
          ? () {
              controller._unfocus();
              return canShowPicker.call();
            }
          : null,
      suffixBuilder: suffixBuilder,
      pickerItemBuilder: pickerItemBuilder,
      checkState: true,
    );
  }

  Widget widgetInput(
      {required String title,
      String placeholder = "请输入",
      String? unit,
      String? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      bool Function()? canShowPicker,
      void Function(String?)? onChange,
      String? Function(String?)? validator,
      bool showArrow = true,
      int? maxLength,
      Widget Function(BuildContext, FormFieldState<String>)?
          rightWidgetBuilder}) {
    return BdhTextInputSmall(
      item:
          FormItem(title: title, isRequired: isRequired, isCanEdit: isCanEdit),
      minHeight: 44.px,
      initialValue: initialValue,
      placeHolder: placeholder,
      textInputType: const TextInputType.numberWithOptions(decimal: true),
      fontSize: 14.px,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange,
      maxLength: maxLength,
      validator: validator,
      rightWidgetBuilder: rightWidgetBuilder ??
          (unit != null
              ? (context, state) {
                  return Padding(
                      padding: EdgeInsets.only(left: 6.px),
                      child: Text(unit,
                          style: TextStyle(
                            fontSize: 14.px,
                            fontWeight: FontWeight.w400,
                            color: const Color.fromRGBO(44, 44, 52, 1),
                          )));
                }
              : null),
    );
  }

  Widget widgetText(
      {required String title,
      String? initialValue,
      bool isRequired = false,
      bool showBottomLine = true}) {
    return Container(
        decoration: showBottomLine
            ? BoxDecoration(
                border: Border(
                    bottom: BorderSide(
                        width: 1.px,
                        color: const Color.fromRGBO(226, 235, 231, 0.6))))
            : null,
        constraints: BoxConstraints(minHeight: 44.px),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text.rich(TextSpan(children: [
              if (isRequired == true)
                TextSpan(
                  text: "*",
                  style: TextStyle(
                      color: Colors.red,
                      fontSize: 14.px,
                      fontWeight: FontWeight.w500),
                ),
              TextSpan(
                text: title,
                style: TextStyle(
                    fontSize: 14.px,
                    color: const Color.fromRGBO(51, 51, 51, 0.4)),
              )
            ])),
            SizedBox(
              width: 10.px,
            ),
            Expanded(
              child: Text(
                initialValue ?? "",
                textAlign: TextAlign.right,
                style: TextStyle(
                    fontSize: 14.px,
                    color: const Color.fromRGBO(51, 51, 51, 1)),
              ),
            ),
          ],
        ));
  }

  Widget _widgetBottom() {
    return BdhTextButton(
      height: 40.px,
      width: 351.px,
      text: '保存',
      textFontWeight: FontWeight.w500,
      textSize: 15.px,
      borderRadius: BorderRadius.all(Radius.circular(6.px)),
      backgroundColor: const Color.fromARGB(240, 94, 139, 245),
      disableBackgroundColor: const Color.fromARGB(255, 224, 223, 223),
      pressedBackgroundColor: const Color.fromARGB(255, 94, 139, 245),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: controller.onClickSave,
    );
  }
}

class _Controller extends UseController {
  final LeafMassifInfo item;
  final LeafGatherInfo? params;
  _Controller(super.context, this.params, this.item);

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  bool get isLoading =>
      loadingStatus.value == LoadingStatus.loading ||
      loadingStatus.value == LoadingStatus.init;

  late final gatherName = use<String>(null);
  late final location = use<LatLng>(null);
  late final sowTime = use<DateTime>(null);

  late final transplantTime = use<DateTime>(null);
  late final leafAge = use<String>(null);
  late final specsLength = use<String>(null);
  late final specsWidth = use<String>(null);
  late final holeNo = use<String>(null);

  late final gatherInfoId = use<String>(null);

  final riceList = ['1'];

  bool get isRice {
    return riceList.contains(item.raiseCropsCd) && item.detectLocation == '02';
  }

  bool get isEdit {
    return params != null;
  }

  late final GlobalKey<FormState> formKey = GlobalKey();

  void onInit() {
    if (isEdit) {
      AgriculturalConditionService().collectPointQueryOne(
          data: {"gatherInfoId": params?.gatherInfoId},
          cancelToken: createCancelToken()).then((result) {
        if (result.success == true && result.code == 0) {
          gatherName.value = result.data["gatherName"];
          if (result.data["latitude"] != null &&
              result.data["longitude"] != null) {
            try {
              location.value = LatLng(double.parse(result.data["latitude"]),
                  double.parse(result.data["longitude"]));
            } catch (error, stackTrace) {
              Log.e("error", error: error, stackTrace: stackTrace);
              return null;
            }
          }
          specsLength.value = result.data["specsLength"].toString();
          specsWidth.value = result.data["specsWidth"].toString();
          holeNo.value = result.data["holeNo"]?.toString();
          leafAge.value = result.data["leafAge"]?.toString();
          sowTime.value = result.data["sowTime"] != null
              ? DateFormat("yyyy-MM-dd").parse(result.data["sowTime"])
              : null;
          transplantTime.value = result.data["transplantTime"] != null
              ? DateFormat("yyyy-MM-dd").parse(result.data["transplantTime"])
              : null;
          gatherInfoId.value = result.data["gatherInfoId"]?.toString();

          loadingStatus.value = LoadingStatus.success;
        }
      }).onError(handleError);
    } else {
      AgriculturalConditionService().collectPointQueryMax(
          data: {"gatherName": item.massifName},
          cancelToken: createCancelToken()).then((result) {
        if (result.success == true && result.code == 0) {
          gatherName.value = result.msg;
          loadingStatus.value = LoadingStatus.success;
        }
      }).onError(handleError);
    }
  }

  void onClickSave() {
    bool validate = formKey.currentState?.validate() ?? false;
    Log.d("validate: $validate");

    if (validate) {
      if (location.value == null) {
        showToast("请先获取坐标");
        return;
      }
      if (gatherName.value == null) {
        showToast("请输入采集点名称");
        return;
      }
      if (sowTime.value == null) {
        showToast("请选择插秧日期");
        return;
      }
      if (holeNo.value == null) {
        showToast("请输入穴位数");
        return;
      }

      if (isRice) {
        if (transplantTime.value == null) {
          showToast("请选择播种日期");
          return;
        }

        if (leafAge.value == null) {
          showToast("请输入叶龄");
          return;
        }

        if (specsLength.value == null) {
          showToast("请输入插秧规格");
          return;
        }
        if (specsWidth.value == null) {
          showToast("请输入插秧规格");
          return;
        }
      }

      var data = <String, dynamic>{
        "gatherName": gatherName.value,
        "latitude": location.value?.latitude.toString(),
        "longitude": location.value?.longitude.toString(),
        "sowTime": DateFormat("yyyy-MM-dd").format(sowTime.value!),
        "holeNo": holeNo.value,
      };

      if (isRice) {
        data["specsLength"] = specsLength.value;
        data["specsWidth"] = specsWidth.value;
        data["transplantTime"] =
            DateFormat("yyyy-MM-dd").format(transplantTime.value!);
        data["leafAge"] = leafAge.value;
      }

      late Future<RequestNoData> future;
      if (isEdit) {
        data["gatherInfoId"] = gatherInfoId.value;
        future = AgriculturalConditionService()
            .collectPointUpdate(data: data, cancelToken: createCancelToken());
      } else {
        data["massifInfoId"] = item.massifInfoId;
        future = AgriculturalConditionService()
            .collectPointAdd(data: data, cancelToken: createCancelToken());
      }
      showLoading(context, content: "正在提交..  ");
      future.then((result) {
        if (!context.mounted) {
          return;
        }
        hideLoading(context);
        if (result.success == true && result.code == 0) {
          showToast("提交成功");
          Navigator.of(context).pop(true);
        }
      }).onError((error, stackTrace) {
        handleError(error, stackTrace, errorDo: () {
          if (!context.mounted) {
            return;
          }
          hideLoading(context);
        });
      });
    }
  }

  void _unfocus() {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }
}
