import 'package:bdh_smart_agric_app/pages/product/agriculturalcondition/leafagebaseinfo/leaf_age_base_info.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';

import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:fluwx/fluwx.dart';

import 'model/sub_menu.dart';

//农情服务
class AgriculturalConditionPage extends StatefulWidget {
  final Object? arguments;
  const AgriculturalConditionPage({super.key, this.arguments});

  @override
  State<StatefulWidget> createState() => _State();
}

class _State extends MixinUseState<AgriculturalConditionPage> {
  late final controller = useController(_Controller(context));

  @override
  void initState() {
    super.initState();
    controller.loadData();
  }

  Widget _backgroundWidget() {
    return Stack(
      fit: StackFit.expand,
      children: [
        Image.asset(
            alignment: Alignment.topCenter,
            fit: BoxFit.fitWidth,
            ImageHelper.wrapAssets("agriculturalcondition/backTop.png")),
        Positioned(
          top: 40.px + 30.px,
          left: 30.px,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "农情服务",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 30.px,
                    fontWeight: FontWeight.w700),
              ),
              Text(
                "客户为中心，创新为核心",
                style: TextStyle(
                    color: Colors.white,
                    fontSize: 16.px,
                    fontWeight: FontWeight.w700),
              )
            ],
          ),
        ),
      ],
    );
  }

  //加载中
  Widget _widgetLoading(BuildContext context) {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _bodyWidget() {
    return UseBuilder((context) => Column(
          children: [
            AppBar(
              toolbarHeight: 40.px,
              backgroundColor: Colors.transparent,
              titleSpacing: 0,
              leading: const BackButton(color: Colors.white),
            ),
            SizedBox(
              height: 114.px,
            ),
            if (controller.isLoading) ...[
              SizedBox(
                height: 50.px,
              ),
              _widgetLoading(context)
            ],
            if (controller.menuItems.value?.isNotEmpty ?? false)
              Container(
                width: 351.px,
                decoration: BoxDecoration(
                    color: const Color.fromRGBO(255, 255, 255, 1),
                    borderRadius: BorderRadius.all(Radius.circular(9.px))),
                margin: EdgeInsets.only(bottom: 5.px),
                padding: EdgeInsets.only(
                    top: 15.px, left: 18.px, right: 15.px, bottom: 18.px),
                child: Wrap(
                  runSpacing: 20.px,
                  children: [
                    ...controller.menuItems.value!.map((e) {
                      return MenuItemView(
                        item: e,
                        isGroup: false,
                        onClick: () {
                          controller.onItemClick(e);
                        },
                      );
                    })
                  ],
                ),
              ),
          ],
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
      body: SafeArea(
          top: false,
          child: Stack(
            fit: StackFit.expand,
            children: [_backgroundWidget(), _bodyWidget()],
          )),
    );
  }
}

class MenuItemView extends StatelessWidget {
  final bool isGroup;
  final SubMenu item;
  final VoidCallback onClick;
  const MenuItemView(
      {super.key,
      required this.item,
      this.isGroup = false,
      required this.onClick});

  Widget _widgetIcon() {
    String icon = item.icon ?? "";
    if (item.text == "基本信息维护") {
      icon = "agriculturalcondition/base.png";
    } else if (item.text == "信息采集") {
      icon = "agriculturalcondition/collection.png";
    }
    var imageUrl = ImageHelper.wrapAssets(icon);
    Log.d("imageUrl: $imageUrl");
    return Image.asset(
        fit: BoxFit.contain,
        width: 36.px,
        height: 36.px,
        imageUrl, errorBuilder: (context, error, stackTrace) {
      return SvgPicture.asset(
        ImageHelper.wrapAssets("ic_default.svg"),
        width: 36.px,
        height: 36.px,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onClick,
      child: Container(
        width: 78.75.px,
        color: Colors.transparent,
        child: Column(
          children: [
            _widgetIcon(),
            SizedBox(
              height: 5.px,
            ),
            Text(
              item.text ?? "",
              strutStyle: StrutStyle(fontSize: 12.px, forceStrutHeight: true),
              style: TextStyle(
                  color: const Color.fromRGBO(51, 51, 51, 1),
                  fontSize: 12.px,
                  fontWeight: FontWeight.w600),
            )
          ],
        ),
      ),
    );
  }
}

class MenuItem {
  String icon;
  String title;
  String path;
  MenuItem({required this.icon, required this.title, required this.path});
}

class _Controller extends UseController {
  _Controller(super.context);

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  late final menuItems = use<List<SubMenu>>(null);

  bool get isLoading =>
      loadingStatus.value == LoadingStatus.loading ||
      loadingStatus.value == LoadingStatus.init;

  void onItemClick(SubMenu item) {
    usageClick(item);
    if (item.text == "基本信息维护") {
      Navigator.of(context).push(CupertinoPageRoute(builder: (context) {
        return const LeafAgeBaseInfoPage();
      }));
      return;
    }
    if (item.path!.startsWith("gh_")) {
      Fluwx fluwx = Fluwx();
      fluwx.open(target: MiniProgram(username: item.path!));
    } else if (item.path!.startsWith("pages")) {
      NativeUtil.openUni({"path": item.path});
    } else {
      Navigator.of(context).pushNamed(item.path ?? "无效路径", arguments: item);
    }
  }

  void usageClick(SubMenu item) {
    BdhDigitalService.saveUsageBdhDigital(data: {
      "menuName": item.text, //菜单
      "component": item.path, //页面路径
      "operateType": 'in',
      "parMenuName": item.parentText, //父级菜单
    }, cancelToken: createCancelToken())
        .then((res) {})
        .onError((error, stackTrace) {
      Log.e("saveUsageBdhDigital error", error: error, stackTrace: stackTrace);
    });
  }

  //加载数据
  void loadData() {
    var data = {"parentText": '农业生产:农情服务'};
    BdhDigitalService.getMenuBdhDigital(
            data: data, cancelToken: createCancelToken())
        .then((res) {
      if (res.code == 0 && res.success == true) {
        menuItems.value = res.data["appMenuList"]
                ?.map<SubMenu>((test) => SubMenu.fromJson(test))
                .toList() ??
            [];
        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }
}
