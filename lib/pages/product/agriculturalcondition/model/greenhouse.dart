import 'package:freezed_annotation/freezed_annotation.dart';

part 'greenhouse.freezed.dart';
part 'greenhouse.g.dart';

@freezed
class Greenhouse with _$Greenhouse {
  factory Greenhouse(
      {dynamic planterIccid,
      dynamic greenhouseType,
      num? specsWidth,
      dynamic geom,
      dynamic geoJson,
      int? greenhouseId,
      String? orgCode,
      String? orgName,
      String? greenhouseName,
      String? chargePerson,
      String? chargePhone,
      num? specsLength,
      num? greenhousePlantId,
      String? planterName,
      String? planterPhone,
      dynamic plantTime,
      dynamic varietyCode,
      dynamic varietyName,
      String? dynamicplotNo,
      String? plotName,
      dynamic plantStageCode,
      dynamic plantStageName,
      dynamic stageMode,
      dynamic accumulatedTemperature,
      dynamic leafAgeUrl,
      dynamic isAoTuo,
      dynamic isRoot,
      dynamic leafNum}) = _Greenhouse;

  factory Greenhouse.fromJson(Map<String, dynamic> json) =>
      _$GreenhouseFromJson(json);
}
