import 'package:freezed_annotation/freezed_annotation.dart';

part 'land.freezed.dart';
part 'land.g.dart';

@freezed
class Land with _$Land {
  factory Land(
      {String? blBranchComNo,
      String? cropBreed,
      num? plotId,
      String? plotNo,
      String? plotName,
      dynamic pastPlotName,
      dynamic parentPlotNo,
      String? plotLevel,
      dynamic calArea,
      num? contrArea,
      dynamic businessType,
      String? cropType,
      String? landType,
      dynamic payType,
      dynamic organicType,
      dynamic landGrade,
      dynamic createBy,
      dynamic createTime,
      dynamic updateBy,
      dynamic updateTime,
      dynamic statusCd,
      dynamic greenArea,
      dynamic organicArea,
      dynamic agriProdArea,
      dynamic smartKitchenArea,
      dynamic resArea,
      dynamic riskType,
      dynamic remark,
      dynamic isSameSquareArea,
      dynamic isSdgwzcggt,
      dynamic isBmggt,
      dynamic isQmsff,
      dynamic isWmcjp,
      dynamic isTg,
      dynamic squareNum,
      num? year,
      dynamic tillType,
      dynamic disTillReason,
      dynamic blStationNo,
      String? blPrecinctNo,
      String? blFarmNo,
      dynamic orgCode,
      num? hasGeom,
      num? hasChild,
      String? blFarmName,
      String? blBranchComName,
      String? blPrecinctName,
      dynamic blStationName,
      num? dataStatus,
      dynamic preTwoCropType,
      dynamic preOneCropType,
      dynamic ridgeDistance,
      dynamic userId,
      dynamic name,
      dynamic serialNumber,
      dynamic serialNumberTemp,
      dynamic isGrid,
      dynamic gridNum,
      dynamic aliasName,
      dynamic parentPlotName,
      dynamic plantArea,
      dynamic mainPartType,
      dynamic landTypeGrp,
      dynamic isUnderwrite,
      dynamic permBasicLand,
      dynamic refineLandType}) = _Land;

  factory Land.fromJson(Map<String, dynamic> json) => _$LandFromJson(json);
}
