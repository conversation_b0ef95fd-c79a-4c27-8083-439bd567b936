import 'package:freezed_annotation/freezed_annotation.dart';

import 'leaf_massif_info.dart';

part 'leaf_gather_info.freezed.dart';
part 'leaf_gather_info.g.dart';

@freezed
class LeafGatherInfo with _$LeafGatherInfo {
  factory LeafGatherInfo(
      {num? gatherInfoId,
      num? massifInfoId,
      String? year,
      String? orgCode,
      String? orgName,
      String? gatherName,
      dynamic longtitudeStr,
      String? longitude,
      String? latitude,
      String? sowTime,
      dynamic transplantTime,
      dynamic leafAge,
      dynamic specsLength,
      dynamic specsWidth,
      num? holeNo,
      num? createBy,
      String? createTime,
      num? updateBy,
      String? updateTime,
      dynamic statusCd,
      LeafMassifInfo? leafMassifInfo,
      dynamic gatherInfoIds,
      dynamic massifName,
      dynamic authedOrgNos,
      dynamic gatherType,
      dynamic yieldTraitsType}) = _LeafGatherInfo;

  factory LeafGatherInfo.fromJson(Map<String, dynamic> json) =>
      _$LeafGatherInfoFromJson(json);
}
