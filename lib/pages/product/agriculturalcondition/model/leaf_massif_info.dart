import 'package:freezed_annotation/freezed_annotation.dart';

part 'leaf_massif_info.freezed.dart';
part 'leaf_massif_info.g.dart';

@freezed
class LeafMassifInfo with _$LeafMassifInfo {
  factory LeafMassifInfo({
    int? massifInfoId,
    String? year,
    String? orgCode,
    String? orgName,
    String? raiseCropsCd,
    String? massifName,
    String? landName,
    String? raiseCropsNm,
    int? raiseCropsVarietyCd,
    String? raiseCropsVarietyNm,
    int? createBy,
    String? createTime,
    int? updateBy,
    String? updateTime,
    dynamic statusCd,
    String? detectLocation,
    String? plotOrGreenhouseName,
    String? plotNo,
    String? plotName,
    int? greenhouseId,
    String? greenhouseName,
    dynamic dataSource,
    dynamic level2Num,
    dynamic authedOrgNos,
  }) = _LeafMassifInfo;

  factory LeafMassifInfo.fromJson(Map<String, dynamic> json) =>
      _$LeafMassifInfoFromJson(json);
}
