// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'sub_menu.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SubMenu _$SubMenuFromJson(Map<String, dynamic> json) {
  return _SubMenu.fromJson(json);
}

/// @nodoc
mixin _$SubMenu {
  String? get text => throw _privateConstructorUsedError;
  dynamic get group => throw _privateConstructorUsedError;
  String? get path => throw _privateConstructorUsedError;
  String? get icon => throw _privateConstructorUsedError;
  String? get parentText => throw _privateConstructorUsedError;
  dynamic get others => throw _privateConstructorUsedError;
  bool? get hidden => throw _privateConstructorUsedError;

  /// Serializes this SubMenu to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of SubMenu
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $SubMenuCopyWith<SubMenu> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubMenuCopyWith<$Res> {
  factory $SubMenuCopyWith(SubMenu value, $Res Function(SubMenu) then) =
      _$SubMenuCopyWithImpl<$Res, SubMenu>;
  @useResult
  $Res call(
      {String? text,
      dynamic group,
      String? path,
      String? icon,
      String? parentText,
      dynamic others,
      bool? hidden});
}

/// @nodoc
class _$SubMenuCopyWithImpl<$Res, $Val extends SubMenu>
    implements $SubMenuCopyWith<$Res> {
  _$SubMenuCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of SubMenu
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = freezed,
    Object? group = freezed,
    Object? path = freezed,
    Object? icon = freezed,
    Object? parentText = freezed,
    Object? others = freezed,
    Object? hidden = freezed,
  }) {
    return _then(_value.copyWith(
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      group: freezed == group
          ? _value.group
          : group // ignore: cast_nullable_to_non_nullable
              as dynamic,
      path: freezed == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      parentText: freezed == parentText
          ? _value.parentText
          : parentText // ignore: cast_nullable_to_non_nullable
              as String?,
      others: freezed == others
          ? _value.others
          : others // ignore: cast_nullable_to_non_nullable
              as dynamic,
      hidden: freezed == hidden
          ? _value.hidden
          : hidden // ignore: cast_nullable_to_non_nullable
              as bool?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SubMenuImplCopyWith<$Res> implements $SubMenuCopyWith<$Res> {
  factory _$$SubMenuImplCopyWith(
          _$SubMenuImpl value, $Res Function(_$SubMenuImpl) then) =
      __$$SubMenuImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? text,
      dynamic group,
      String? path,
      String? icon,
      String? parentText,
      dynamic others,
      bool? hidden});
}

/// @nodoc
class __$$SubMenuImplCopyWithImpl<$Res>
    extends _$SubMenuCopyWithImpl<$Res, _$SubMenuImpl>
    implements _$$SubMenuImplCopyWith<$Res> {
  __$$SubMenuImplCopyWithImpl(
      _$SubMenuImpl _value, $Res Function(_$SubMenuImpl) _then)
      : super(_value, _then);

  /// Create a copy of SubMenu
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = freezed,
    Object? group = freezed,
    Object? path = freezed,
    Object? icon = freezed,
    Object? parentText = freezed,
    Object? others = freezed,
    Object? hidden = freezed,
  }) {
    return _then(_$SubMenuImpl(
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      group: freezed == group
          ? _value.group
          : group // ignore: cast_nullable_to_non_nullable
              as dynamic,
      path: freezed == path
          ? _value.path
          : path // ignore: cast_nullable_to_non_nullable
              as String?,
      icon: freezed == icon
          ? _value.icon
          : icon // ignore: cast_nullable_to_non_nullable
              as String?,
      parentText: freezed == parentText
          ? _value.parentText
          : parentText // ignore: cast_nullable_to_non_nullable
              as String?,
      others: freezed == others
          ? _value.others
          : others // ignore: cast_nullable_to_non_nullable
              as dynamic,
      hidden: freezed == hidden
          ? _value.hidden
          : hidden // ignore: cast_nullable_to_non_nullable
              as bool?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SubMenuImpl implements _SubMenu {
  _$SubMenuImpl(
      {this.text,
      this.group,
      this.path,
      this.icon,
      this.parentText,
      this.others,
      this.hidden});

  factory _$SubMenuImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubMenuImplFromJson(json);

  @override
  final String? text;
  @override
  final dynamic group;
  @override
  final String? path;
  @override
  final String? icon;
  @override
  final String? parentText;
  @override
  final dynamic others;
  @override
  final bool? hidden;

  @override
  String toString() {
    return 'SubMenu(text: $text, group: $group, path: $path, icon: $icon, parentText: $parentText, others: $others, hidden: $hidden)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubMenuImpl &&
            (identical(other.text, text) || other.text == text) &&
            const DeepCollectionEquality().equals(other.group, group) &&
            (identical(other.path, path) || other.path == path) &&
            (identical(other.icon, icon) || other.icon == icon) &&
            (identical(other.parentText, parentText) ||
                other.parentText == parentText) &&
            const DeepCollectionEquality().equals(other.others, others) &&
            (identical(other.hidden, hidden) || other.hidden == hidden));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      text,
      const DeepCollectionEquality().hash(group),
      path,
      icon,
      parentText,
      const DeepCollectionEquality().hash(others),
      hidden);

  /// Create a copy of SubMenu
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$SubMenuImplCopyWith<_$SubMenuImpl> get copyWith =>
      __$$SubMenuImplCopyWithImpl<_$SubMenuImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubMenuImplToJson(
      this,
    );
  }
}

abstract class _SubMenu implements SubMenu {
  factory _SubMenu(
      {final String? text,
      final dynamic group,
      final String? path,
      final String? icon,
      final String? parentText,
      final dynamic others,
      final bool? hidden}) = _$SubMenuImpl;

  factory _SubMenu.fromJson(Map<String, dynamic> json) = _$SubMenuImpl.fromJson;

  @override
  String? get text;
  @override
  dynamic get group;
  @override
  String? get path;
  @override
  String? get icon;
  @override
  String? get parentText;
  @override
  dynamic get others;
  @override
  bool? get hidden;

  /// Create a copy of SubMenu
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$SubMenuImplCopyWith<_$SubMenuImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
