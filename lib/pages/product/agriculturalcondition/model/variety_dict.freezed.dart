// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'variety_dict.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

VarietyDict _$VarietyDictFromJson(Map<String, dynamic> json) {
  return _VarietyDict.fromJson(json);
}

/// @nodoc
mixin _$VarietyDict {
  dynamic get growPatternsId => throw _privateConstructorUsedError;
  num? get leafAgeMin => throw _privateConstructorUsedError;
  num? get leafAgeMax => throw _privateConstructorUsedError;
  String? get orgCode => throw _privateConstructorUsedError;
  num? get raiseCropsVarietyCd => throw _privateConstructorUsedError;
  String? get raiseCropsVarietyNm => throw _privateConstructorUsedError;
  num? get raiseLeafMax => throw _privateConstructorUsedError;
  dynamic get temp => throw _privateConstructorUsedError;
  String? get raiseCropsCd => throw _privateConstructorUsedError;
  String? get raiseCropsNm => throw _privateConstructorUsedError;

  /// Serializes this VarietyDict to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of VarietyDict
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $VarietyDictCopyWith<VarietyDict> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $VarietyDictCopyWith<$Res> {
  factory $VarietyDictCopyWith(
          VarietyDict value, $Res Function(VarietyDict) then) =
      _$VarietyDictCopyWithImpl<$Res, VarietyDict>;
  @useResult
  $Res call(
      {dynamic growPatternsId,
      num? leafAgeMin,
      num? leafAgeMax,
      String? orgCode,
      num? raiseCropsVarietyCd,
      String? raiseCropsVarietyNm,
      num? raiseLeafMax,
      dynamic temp,
      String? raiseCropsCd,
      String? raiseCropsNm});
}

/// @nodoc
class _$VarietyDictCopyWithImpl<$Res, $Val extends VarietyDict>
    implements $VarietyDictCopyWith<$Res> {
  _$VarietyDictCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of VarietyDict
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? growPatternsId = freezed,
    Object? leafAgeMin = freezed,
    Object? leafAgeMax = freezed,
    Object? orgCode = freezed,
    Object? raiseCropsVarietyCd = freezed,
    Object? raiseCropsVarietyNm = freezed,
    Object? raiseLeafMax = freezed,
    Object? temp = freezed,
    Object? raiseCropsCd = freezed,
    Object? raiseCropsNm = freezed,
  }) {
    return _then(_value.copyWith(
      growPatternsId: freezed == growPatternsId
          ? _value.growPatternsId
          : growPatternsId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafAgeMin: freezed == leafAgeMin
          ? _value.leafAgeMin
          : leafAgeMin // ignore: cast_nullable_to_non_nullable
              as num?,
      leafAgeMax: freezed == leafAgeMax
          ? _value.leafAgeMax
          : leafAgeMax // ignore: cast_nullable_to_non_nullable
              as num?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsVarietyCd: freezed == raiseCropsVarietyCd
          ? _value.raiseCropsVarietyCd
          : raiseCropsVarietyCd // ignore: cast_nullable_to_non_nullable
              as num?,
      raiseCropsVarietyNm: freezed == raiseCropsVarietyNm
          ? _value.raiseCropsVarietyNm
          : raiseCropsVarietyNm // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseLeafMax: freezed == raiseLeafMax
          ? _value.raiseLeafMax
          : raiseLeafMax // ignore: cast_nullable_to_non_nullable
              as num?,
      temp: freezed == temp
          ? _value.temp
          : temp // ignore: cast_nullable_to_non_nullable
              as dynamic,
      raiseCropsCd: freezed == raiseCropsCd
          ? _value.raiseCropsCd
          : raiseCropsCd // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsNm: freezed == raiseCropsNm
          ? _value.raiseCropsNm
          : raiseCropsNm // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$VarietyDictImplCopyWith<$Res>
    implements $VarietyDictCopyWith<$Res> {
  factory _$$VarietyDictImplCopyWith(
          _$VarietyDictImpl value, $Res Function(_$VarietyDictImpl) then) =
      __$$VarietyDictImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {dynamic growPatternsId,
      num? leafAgeMin,
      num? leafAgeMax,
      String? orgCode,
      num? raiseCropsVarietyCd,
      String? raiseCropsVarietyNm,
      num? raiseLeafMax,
      dynamic temp,
      String? raiseCropsCd,
      String? raiseCropsNm});
}

/// @nodoc
class __$$VarietyDictImplCopyWithImpl<$Res>
    extends _$VarietyDictCopyWithImpl<$Res, _$VarietyDictImpl>
    implements _$$VarietyDictImplCopyWith<$Res> {
  __$$VarietyDictImplCopyWithImpl(
      _$VarietyDictImpl _value, $Res Function(_$VarietyDictImpl) _then)
      : super(_value, _then);

  /// Create a copy of VarietyDict
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? growPatternsId = freezed,
    Object? leafAgeMin = freezed,
    Object? leafAgeMax = freezed,
    Object? orgCode = freezed,
    Object? raiseCropsVarietyCd = freezed,
    Object? raiseCropsVarietyNm = freezed,
    Object? raiseLeafMax = freezed,
    Object? temp = freezed,
    Object? raiseCropsCd = freezed,
    Object? raiseCropsNm = freezed,
  }) {
    return _then(_$VarietyDictImpl(
      growPatternsId: freezed == growPatternsId
          ? _value.growPatternsId
          : growPatternsId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafAgeMin: freezed == leafAgeMin
          ? _value.leafAgeMin
          : leafAgeMin // ignore: cast_nullable_to_non_nullable
              as num?,
      leafAgeMax: freezed == leafAgeMax
          ? _value.leafAgeMax
          : leafAgeMax // ignore: cast_nullable_to_non_nullable
              as num?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsVarietyCd: freezed == raiseCropsVarietyCd
          ? _value.raiseCropsVarietyCd
          : raiseCropsVarietyCd // ignore: cast_nullable_to_non_nullable
              as num?,
      raiseCropsVarietyNm: freezed == raiseCropsVarietyNm
          ? _value.raiseCropsVarietyNm
          : raiseCropsVarietyNm // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseLeafMax: freezed == raiseLeafMax
          ? _value.raiseLeafMax
          : raiseLeafMax // ignore: cast_nullable_to_non_nullable
              as num?,
      temp: freezed == temp
          ? _value.temp
          : temp // ignore: cast_nullable_to_non_nullable
              as dynamic,
      raiseCropsCd: freezed == raiseCropsCd
          ? _value.raiseCropsCd
          : raiseCropsCd // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsNm: freezed == raiseCropsNm
          ? _value.raiseCropsNm
          : raiseCropsNm // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$VarietyDictImpl implements _VarietyDict {
  _$VarietyDictImpl(
      {this.growPatternsId,
      this.leafAgeMin,
      this.leafAgeMax,
      this.orgCode,
      this.raiseCropsVarietyCd,
      this.raiseCropsVarietyNm,
      this.raiseLeafMax,
      this.temp,
      this.raiseCropsCd,
      this.raiseCropsNm});

  factory _$VarietyDictImpl.fromJson(Map<String, dynamic> json) =>
      _$$VarietyDictImplFromJson(json);

  @override
  final dynamic growPatternsId;
  @override
  final num? leafAgeMin;
  @override
  final num? leafAgeMax;
  @override
  final String? orgCode;
  @override
  final num? raiseCropsVarietyCd;
  @override
  final String? raiseCropsVarietyNm;
  @override
  final num? raiseLeafMax;
  @override
  final dynamic temp;
  @override
  final String? raiseCropsCd;
  @override
  final String? raiseCropsNm;

  @override
  String toString() {
    return 'VarietyDict(growPatternsId: $growPatternsId, leafAgeMin: $leafAgeMin, leafAgeMax: $leafAgeMax, orgCode: $orgCode, raiseCropsVarietyCd: $raiseCropsVarietyCd, raiseCropsVarietyNm: $raiseCropsVarietyNm, raiseLeafMax: $raiseLeafMax, temp: $temp, raiseCropsCd: $raiseCropsCd, raiseCropsNm: $raiseCropsNm)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$VarietyDictImpl &&
            const DeepCollectionEquality()
                .equals(other.growPatternsId, growPatternsId) &&
            (identical(other.leafAgeMin, leafAgeMin) ||
                other.leafAgeMin == leafAgeMin) &&
            (identical(other.leafAgeMax, leafAgeMax) ||
                other.leafAgeMax == leafAgeMax) &&
            (identical(other.orgCode, orgCode) || other.orgCode == orgCode) &&
            (identical(other.raiseCropsVarietyCd, raiseCropsVarietyCd) ||
                other.raiseCropsVarietyCd == raiseCropsVarietyCd) &&
            (identical(other.raiseCropsVarietyNm, raiseCropsVarietyNm) ||
                other.raiseCropsVarietyNm == raiseCropsVarietyNm) &&
            (identical(other.raiseLeafMax, raiseLeafMax) ||
                other.raiseLeafMax == raiseLeafMax) &&
            const DeepCollectionEquality().equals(other.temp, temp) &&
            (identical(other.raiseCropsCd, raiseCropsCd) ||
                other.raiseCropsCd == raiseCropsCd) &&
            (identical(other.raiseCropsNm, raiseCropsNm) ||
                other.raiseCropsNm == raiseCropsNm));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      const DeepCollectionEquality().hash(growPatternsId),
      leafAgeMin,
      leafAgeMax,
      orgCode,
      raiseCropsVarietyCd,
      raiseCropsVarietyNm,
      raiseLeafMax,
      const DeepCollectionEquality().hash(temp),
      raiseCropsCd,
      raiseCropsNm);

  /// Create a copy of VarietyDict
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$VarietyDictImplCopyWith<_$VarietyDictImpl> get copyWith =>
      __$$VarietyDictImplCopyWithImpl<_$VarietyDictImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$VarietyDictImplToJson(
      this,
    );
  }
}

abstract class _VarietyDict implements VarietyDict {
  factory _VarietyDict(
      {final dynamic growPatternsId,
      final num? leafAgeMin,
      final num? leafAgeMax,
      final String? orgCode,
      final num? raiseCropsVarietyCd,
      final String? raiseCropsVarietyNm,
      final num? raiseLeafMax,
      final dynamic temp,
      final String? raiseCropsCd,
      final String? raiseCropsNm}) = _$VarietyDictImpl;

  factory _VarietyDict.fromJson(Map<String, dynamic> json) =
      _$VarietyDictImpl.fromJson;

  @override
  dynamic get growPatternsId;
  @override
  num? get leafAgeMin;
  @override
  num? get leafAgeMax;
  @override
  String? get orgCode;
  @override
  num? get raiseCropsVarietyCd;
  @override
  String? get raiseCropsVarietyNm;
  @override
  num? get raiseLeafMax;
  @override
  dynamic get temp;
  @override
  String? get raiseCropsCd;
  @override
  String? get raiseCropsNm;

  /// Create a copy of VarietyDict
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$VarietyDictImplCopyWith<_$VarietyDictImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
