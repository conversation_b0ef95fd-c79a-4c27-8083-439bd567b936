// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sub_menu.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$SubMenuImpl _$$SubMenuImplFromJson(Map<String, dynamic> json) =>
    _$SubMenuImpl(
      text: json['text'] as String?,
      group: json['group'],
      path: json['path'] as String?,
      icon: json['icon'] as String?,
      parentText: json['parentText'] as String?,
      others: json['others'],
      hidden: json['hidden'] as bool?,
    );

Map<String, dynamic> _$$SubMenuImplToJson(_$SubMenuImpl instance) =>
    <String, dynamic>{
      'text': instance.text,
      'group': instance.group,
      'path': instance.path,
      'icon': instance.icon,
      'parentText': instance.parentText,
      'others': instance.others,
      'hidden': instance.hidden,
    };
