// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'greenhouse.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$GreenhouseImpl _$$GreenhouseImplFromJson(Map<String, dynamic> json) =>
    _$GreenhouseImpl(
      planterIccid: json['planterIccid'],
      greenhouseType: json['greenhouseType'],
      specsWidth: json['specsWidth'] as num?,
      geom: json['geom'],
      geoJson: json['geoJson'],
      greenhouseId: (json['greenhouseId'] as num?)?.toInt(),
      orgCode: json['orgCode'] as String?,
      orgName: json['orgName'] as String?,
      greenhouseName: json['greenhouseName'] as String?,
      chargePerson: json['chargePerson'] as String?,
      chargePhone: json['chargePhone'] as String?,
      specsLength: json['specsLength'] as num?,
      greenhousePlantId: json['greenhousePlantId'] as num?,
      planterName: json['planterName'] as String?,
      planterPhone: json['planterPhone'] as String?,
      plantTime: json['plantTime'],
      varietyCode: json['varietyCode'],
      varietyName: json['varietyName'],
      dynamicplotNo: json['dynamicplotNo'] as String?,
      plotName: json['plotName'] as String?,
      plantStageCode: json['plantStageCode'],
      plantStageName: json['plantStageName'],
      stageMode: json['stageMode'],
      accumulatedTemperature: json['accumulatedTemperature'],
      leafAgeUrl: json['leafAgeUrl'],
      isAoTuo: json['isAoTuo'],
      isRoot: json['isRoot'],
      leafNum: json['leafNum'],
    );

Map<String, dynamic> _$$GreenhouseImplToJson(_$GreenhouseImpl instance) =>
    <String, dynamic>{
      'planterIccid': instance.planterIccid,
      'greenhouseType': instance.greenhouseType,
      'specsWidth': instance.specsWidth,
      'geom': instance.geom,
      'geoJson': instance.geoJson,
      'greenhouseId': instance.greenhouseId,
      'orgCode': instance.orgCode,
      'orgName': instance.orgName,
      'greenhouseName': instance.greenhouseName,
      'chargePerson': instance.chargePerson,
      'chargePhone': instance.chargePhone,
      'specsLength': instance.specsLength,
      'greenhousePlantId': instance.greenhousePlantId,
      'planterName': instance.planterName,
      'planterPhone': instance.planterPhone,
      'plantTime': instance.plantTime,
      'varietyCode': instance.varietyCode,
      'varietyName': instance.varietyName,
      'dynamicplotNo': instance.dynamicplotNo,
      'plotName': instance.plotName,
      'plantStageCode': instance.plantStageCode,
      'plantStageName': instance.plantStageName,
      'stageMode': instance.stageMode,
      'accumulatedTemperature': instance.accumulatedTemperature,
      'leafAgeUrl': instance.leafAgeUrl,
      'isAoTuo': instance.isAoTuo,
      'isRoot': instance.isRoot,
      'leafNum': instance.leafNum,
    };
