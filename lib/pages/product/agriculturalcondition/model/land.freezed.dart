// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'land.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Land _$LandFromJson(Map<String, dynamic> json) {
  return _Land.fromJson(json);
}

/// @nodoc
mixin _$Land {
  String? get blBranchComNo => throw _privateConstructorUsedError;
  String? get cropBreed => throw _privateConstructorUsedError;
  num? get plotId => throw _privateConstructorUsedError;
  String? get plotNo => throw _privateConstructorUsedError;
  String? get plotName => throw _privateConstructorUsedError;
  dynamic get pastPlotName => throw _privateConstructorUsedError;
  dynamic get parentPlotNo => throw _privateConstructorUsedError;
  String? get plotLevel => throw _privateConstructorUsedError;
  dynamic get calArea => throw _privateConstructorUsedError;
  num? get contrArea => throw _privateConstructorUsedError;
  dynamic get businessType => throw _privateConstructorUsedError;
  String? get cropType => throw _privateConstructorUsedError;
  String? get landType => throw _privateConstructorUsedError;
  dynamic get payType => throw _privateConstructorUsedError;
  dynamic get organicType => throw _privateConstructorUsedError;
  dynamic get landGrade => throw _privateConstructorUsedError;
  dynamic get createBy => throw _privateConstructorUsedError;
  dynamic get createTime => throw _privateConstructorUsedError;
  dynamic get updateBy => throw _privateConstructorUsedError;
  dynamic get updateTime => throw _privateConstructorUsedError;
  dynamic get statusCd => throw _privateConstructorUsedError;
  dynamic get greenArea => throw _privateConstructorUsedError;
  dynamic get organicArea => throw _privateConstructorUsedError;
  dynamic get agriProdArea => throw _privateConstructorUsedError;
  dynamic get smartKitchenArea => throw _privateConstructorUsedError;
  dynamic get resArea => throw _privateConstructorUsedError;
  dynamic get riskType => throw _privateConstructorUsedError;
  dynamic get remark => throw _privateConstructorUsedError;
  dynamic get isSameSquareArea => throw _privateConstructorUsedError;
  dynamic get isSdgwzcggt => throw _privateConstructorUsedError;
  dynamic get isBmggt => throw _privateConstructorUsedError;
  dynamic get isQmsff => throw _privateConstructorUsedError;
  dynamic get isWmcjp => throw _privateConstructorUsedError;
  dynamic get isTg => throw _privateConstructorUsedError;
  dynamic get squareNum => throw _privateConstructorUsedError;
  num? get year => throw _privateConstructorUsedError;
  dynamic get tillType => throw _privateConstructorUsedError;
  dynamic get disTillReason => throw _privateConstructorUsedError;
  dynamic get blStationNo => throw _privateConstructorUsedError;
  String? get blPrecinctNo => throw _privateConstructorUsedError;
  String? get blFarmNo => throw _privateConstructorUsedError;
  dynamic get orgCode => throw _privateConstructorUsedError;
  num? get hasGeom => throw _privateConstructorUsedError;
  num? get hasChild => throw _privateConstructorUsedError;
  String? get blFarmName => throw _privateConstructorUsedError;
  String? get blBranchComName => throw _privateConstructorUsedError;
  String? get blPrecinctName => throw _privateConstructorUsedError;
  dynamic get blStationName => throw _privateConstructorUsedError;
  num? get dataStatus => throw _privateConstructorUsedError;
  dynamic get preTwoCropType => throw _privateConstructorUsedError;
  dynamic get preOneCropType => throw _privateConstructorUsedError;
  dynamic get ridgeDistance => throw _privateConstructorUsedError;
  dynamic get userId => throw _privateConstructorUsedError;
  dynamic get name => throw _privateConstructorUsedError;
  dynamic get serialNumber => throw _privateConstructorUsedError;
  dynamic get serialNumberTemp => throw _privateConstructorUsedError;
  dynamic get isGrid => throw _privateConstructorUsedError;
  dynamic get gridNum => throw _privateConstructorUsedError;
  dynamic get aliasName => throw _privateConstructorUsedError;
  dynamic get parentPlotName => throw _privateConstructorUsedError;
  dynamic get plantArea => throw _privateConstructorUsedError;
  dynamic get mainPartType => throw _privateConstructorUsedError;
  dynamic get landTypeGrp => throw _privateConstructorUsedError;
  dynamic get isUnderwrite => throw _privateConstructorUsedError;
  dynamic get permBasicLand => throw _privateConstructorUsedError;
  dynamic get refineLandType => throw _privateConstructorUsedError;

  /// Serializes this Land to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Land
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LandCopyWith<Land> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LandCopyWith<$Res> {
  factory $LandCopyWith(Land value, $Res Function(Land) then) =
      _$LandCopyWithImpl<$Res, Land>;
  @useResult
  $Res call(
      {String? blBranchComNo,
      String? cropBreed,
      num? plotId,
      String? plotNo,
      String? plotName,
      dynamic pastPlotName,
      dynamic parentPlotNo,
      String? plotLevel,
      dynamic calArea,
      num? contrArea,
      dynamic businessType,
      String? cropType,
      String? landType,
      dynamic payType,
      dynamic organicType,
      dynamic landGrade,
      dynamic createBy,
      dynamic createTime,
      dynamic updateBy,
      dynamic updateTime,
      dynamic statusCd,
      dynamic greenArea,
      dynamic organicArea,
      dynamic agriProdArea,
      dynamic smartKitchenArea,
      dynamic resArea,
      dynamic riskType,
      dynamic remark,
      dynamic isSameSquareArea,
      dynamic isSdgwzcggt,
      dynamic isBmggt,
      dynamic isQmsff,
      dynamic isWmcjp,
      dynamic isTg,
      dynamic squareNum,
      num? year,
      dynamic tillType,
      dynamic disTillReason,
      dynamic blStationNo,
      String? blPrecinctNo,
      String? blFarmNo,
      dynamic orgCode,
      num? hasGeom,
      num? hasChild,
      String? blFarmName,
      String? blBranchComName,
      String? blPrecinctName,
      dynamic blStationName,
      num? dataStatus,
      dynamic preTwoCropType,
      dynamic preOneCropType,
      dynamic ridgeDistance,
      dynamic userId,
      dynamic name,
      dynamic serialNumber,
      dynamic serialNumberTemp,
      dynamic isGrid,
      dynamic gridNum,
      dynamic aliasName,
      dynamic parentPlotName,
      dynamic plantArea,
      dynamic mainPartType,
      dynamic landTypeGrp,
      dynamic isUnderwrite,
      dynamic permBasicLand,
      dynamic refineLandType});
}

/// @nodoc
class _$LandCopyWithImpl<$Res, $Val extends Land>
    implements $LandCopyWith<$Res> {
  _$LandCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Land
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blBranchComNo = freezed,
    Object? cropBreed = freezed,
    Object? plotId = freezed,
    Object? plotNo = freezed,
    Object? plotName = freezed,
    Object? pastPlotName = freezed,
    Object? parentPlotNo = freezed,
    Object? plotLevel = freezed,
    Object? calArea = freezed,
    Object? contrArea = freezed,
    Object? businessType = freezed,
    Object? cropType = freezed,
    Object? landType = freezed,
    Object? payType = freezed,
    Object? organicType = freezed,
    Object? landGrade = freezed,
    Object? createBy = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statusCd = freezed,
    Object? greenArea = freezed,
    Object? organicArea = freezed,
    Object? agriProdArea = freezed,
    Object? smartKitchenArea = freezed,
    Object? resArea = freezed,
    Object? riskType = freezed,
    Object? remark = freezed,
    Object? isSameSquareArea = freezed,
    Object? isSdgwzcggt = freezed,
    Object? isBmggt = freezed,
    Object? isQmsff = freezed,
    Object? isWmcjp = freezed,
    Object? isTg = freezed,
    Object? squareNum = freezed,
    Object? year = freezed,
    Object? tillType = freezed,
    Object? disTillReason = freezed,
    Object? blStationNo = freezed,
    Object? blPrecinctNo = freezed,
    Object? blFarmNo = freezed,
    Object? orgCode = freezed,
    Object? hasGeom = freezed,
    Object? hasChild = freezed,
    Object? blFarmName = freezed,
    Object? blBranchComName = freezed,
    Object? blPrecinctName = freezed,
    Object? blStationName = freezed,
    Object? dataStatus = freezed,
    Object? preTwoCropType = freezed,
    Object? preOneCropType = freezed,
    Object? ridgeDistance = freezed,
    Object? userId = freezed,
    Object? name = freezed,
    Object? serialNumber = freezed,
    Object? serialNumberTemp = freezed,
    Object? isGrid = freezed,
    Object? gridNum = freezed,
    Object? aliasName = freezed,
    Object? parentPlotName = freezed,
    Object? plantArea = freezed,
    Object? mainPartType = freezed,
    Object? landTypeGrp = freezed,
    Object? isUnderwrite = freezed,
    Object? permBasicLand = freezed,
    Object? refineLandType = freezed,
  }) {
    return _then(_value.copyWith(
      blBranchComNo: freezed == blBranchComNo
          ? _value.blBranchComNo
          : blBranchComNo // ignore: cast_nullable_to_non_nullable
              as String?,
      cropBreed: freezed == cropBreed
          ? _value.cropBreed
          : cropBreed // ignore: cast_nullable_to_non_nullable
              as String?,
      plotId: freezed == plotId
          ? _value.plotId
          : plotId // ignore: cast_nullable_to_non_nullable
              as num?,
      plotNo: freezed == plotNo
          ? _value.plotNo
          : plotNo // ignore: cast_nullable_to_non_nullable
              as String?,
      plotName: freezed == plotName
          ? _value.plotName
          : plotName // ignore: cast_nullable_to_non_nullable
              as String?,
      pastPlotName: freezed == pastPlotName
          ? _value.pastPlotName
          : pastPlotName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      parentPlotNo: freezed == parentPlotNo
          ? _value.parentPlotNo
          : parentPlotNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotLevel: freezed == plotLevel
          ? _value.plotLevel
          : plotLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      calArea: freezed == calArea
          ? _value.calArea
          : calArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      contrArea: freezed == contrArea
          ? _value.contrArea
          : contrArea // ignore: cast_nullable_to_non_nullable
              as num?,
      businessType: freezed == businessType
          ? _value.businessType
          : businessType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      cropType: freezed == cropType
          ? _value.cropType
          : cropType // ignore: cast_nullable_to_non_nullable
              as String?,
      landType: freezed == landType
          ? _value.landType
          : landType // ignore: cast_nullable_to_non_nullable
              as String?,
      payType: freezed == payType
          ? _value.payType
          : payType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      organicType: freezed == organicType
          ? _value.organicType
          : organicType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      landGrade: freezed == landGrade
          ? _value.landGrade
          : landGrade // ignore: cast_nullable_to_non_nullable
              as dynamic,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as dynamic,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as dynamic,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      greenArea: freezed == greenArea
          ? _value.greenArea
          : greenArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      organicArea: freezed == organicArea
          ? _value.organicArea
          : organicArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      agriProdArea: freezed == agriProdArea
          ? _value.agriProdArea
          : agriProdArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      smartKitchenArea: freezed == smartKitchenArea
          ? _value.smartKitchenArea
          : smartKitchenArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      resArea: freezed == resArea
          ? _value.resArea
          : resArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      riskType: freezed == riskType
          ? _value.riskType
          : riskType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSameSquareArea: freezed == isSameSquareArea
          ? _value.isSameSquareArea
          : isSameSquareArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSdgwzcggt: freezed == isSdgwzcggt
          ? _value.isSdgwzcggt
          : isSdgwzcggt // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isBmggt: freezed == isBmggt
          ? _value.isBmggt
          : isBmggt // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isQmsff: freezed == isQmsff
          ? _value.isQmsff
          : isQmsff // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isWmcjp: freezed == isWmcjp
          ? _value.isWmcjp
          : isWmcjp // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isTg: freezed == isTg
          ? _value.isTg
          : isTg // ignore: cast_nullable_to_non_nullable
              as dynamic,
      squareNum: freezed == squareNum
          ? _value.squareNum
          : squareNum // ignore: cast_nullable_to_non_nullable
              as dynamic,
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as num?,
      tillType: freezed == tillType
          ? _value.tillType
          : tillType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      disTillReason: freezed == disTillReason
          ? _value.disTillReason
          : disTillReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      blStationNo: freezed == blStationNo
          ? _value.blStationNo
          : blStationNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      blPrecinctNo: freezed == blPrecinctNo
          ? _value.blPrecinctNo
          : blPrecinctNo // ignore: cast_nullable_to_non_nullable
              as String?,
      blFarmNo: freezed == blFarmNo
          ? _value.blFarmNo
          : blFarmNo // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      hasGeom: freezed == hasGeom
          ? _value.hasGeom
          : hasGeom // ignore: cast_nullable_to_non_nullable
              as num?,
      hasChild: freezed == hasChild
          ? _value.hasChild
          : hasChild // ignore: cast_nullable_to_non_nullable
              as num?,
      blFarmName: freezed == blFarmName
          ? _value.blFarmName
          : blFarmName // ignore: cast_nullable_to_non_nullable
              as String?,
      blBranchComName: freezed == blBranchComName
          ? _value.blBranchComName
          : blBranchComName // ignore: cast_nullable_to_non_nullable
              as String?,
      blPrecinctName: freezed == blPrecinctName
          ? _value.blPrecinctName
          : blPrecinctName // ignore: cast_nullable_to_non_nullable
              as String?,
      blStationName: freezed == blStationName
          ? _value.blStationName
          : blStationName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dataStatus: freezed == dataStatus
          ? _value.dataStatus
          : dataStatus // ignore: cast_nullable_to_non_nullable
              as num?,
      preTwoCropType: freezed == preTwoCropType
          ? _value.preTwoCropType
          : preTwoCropType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      preOneCropType: freezed == preOneCropType
          ? _value.preOneCropType
          : preOneCropType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      ridgeDistance: freezed == ridgeDistance
          ? _value.ridgeDistance
          : ridgeDistance // ignore: cast_nullable_to_non_nullable
              as dynamic,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as dynamic,
      serialNumber: freezed == serialNumber
          ? _value.serialNumber
          : serialNumber // ignore: cast_nullable_to_non_nullable
              as dynamic,
      serialNumberTemp: freezed == serialNumberTemp
          ? _value.serialNumberTemp
          : serialNumberTemp // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isGrid: freezed == isGrid
          ? _value.isGrid
          : isGrid // ignore: cast_nullable_to_non_nullable
              as dynamic,
      gridNum: freezed == gridNum
          ? _value.gridNum
          : gridNum // ignore: cast_nullable_to_non_nullable
              as dynamic,
      aliasName: freezed == aliasName
          ? _value.aliasName
          : aliasName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      parentPlotName: freezed == parentPlotName
          ? _value.parentPlotName
          : parentPlotName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plantArea: freezed == plantArea
          ? _value.plantArea
          : plantArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      mainPartType: freezed == mainPartType
          ? _value.mainPartType
          : mainPartType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      landTypeGrp: freezed == landTypeGrp
          ? _value.landTypeGrp
          : landTypeGrp // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isUnderwrite: freezed == isUnderwrite
          ? _value.isUnderwrite
          : isUnderwrite // ignore: cast_nullable_to_non_nullable
              as dynamic,
      permBasicLand: freezed == permBasicLand
          ? _value.permBasicLand
          : permBasicLand // ignore: cast_nullable_to_non_nullable
              as dynamic,
      refineLandType: freezed == refineLandType
          ? _value.refineLandType
          : refineLandType // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LandImplCopyWith<$Res> implements $LandCopyWith<$Res> {
  factory _$$LandImplCopyWith(
          _$LandImpl value, $Res Function(_$LandImpl) then) =
      __$$LandImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? blBranchComNo,
      String? cropBreed,
      num? plotId,
      String? plotNo,
      String? plotName,
      dynamic pastPlotName,
      dynamic parentPlotNo,
      String? plotLevel,
      dynamic calArea,
      num? contrArea,
      dynamic businessType,
      String? cropType,
      String? landType,
      dynamic payType,
      dynamic organicType,
      dynamic landGrade,
      dynamic createBy,
      dynamic createTime,
      dynamic updateBy,
      dynamic updateTime,
      dynamic statusCd,
      dynamic greenArea,
      dynamic organicArea,
      dynamic agriProdArea,
      dynamic smartKitchenArea,
      dynamic resArea,
      dynamic riskType,
      dynamic remark,
      dynamic isSameSquareArea,
      dynamic isSdgwzcggt,
      dynamic isBmggt,
      dynamic isQmsff,
      dynamic isWmcjp,
      dynamic isTg,
      dynamic squareNum,
      num? year,
      dynamic tillType,
      dynamic disTillReason,
      dynamic blStationNo,
      String? blPrecinctNo,
      String? blFarmNo,
      dynamic orgCode,
      num? hasGeom,
      num? hasChild,
      String? blFarmName,
      String? blBranchComName,
      String? blPrecinctName,
      dynamic blStationName,
      num? dataStatus,
      dynamic preTwoCropType,
      dynamic preOneCropType,
      dynamic ridgeDistance,
      dynamic userId,
      dynamic name,
      dynamic serialNumber,
      dynamic serialNumberTemp,
      dynamic isGrid,
      dynamic gridNum,
      dynamic aliasName,
      dynamic parentPlotName,
      dynamic plantArea,
      dynamic mainPartType,
      dynamic landTypeGrp,
      dynamic isUnderwrite,
      dynamic permBasicLand,
      dynamic refineLandType});
}

/// @nodoc
class __$$LandImplCopyWithImpl<$Res>
    extends _$LandCopyWithImpl<$Res, _$LandImpl>
    implements _$$LandImplCopyWith<$Res> {
  __$$LandImplCopyWithImpl(_$LandImpl _value, $Res Function(_$LandImpl) _then)
      : super(_value, _then);

  /// Create a copy of Land
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? blBranchComNo = freezed,
    Object? cropBreed = freezed,
    Object? plotId = freezed,
    Object? plotNo = freezed,
    Object? plotName = freezed,
    Object? pastPlotName = freezed,
    Object? parentPlotNo = freezed,
    Object? plotLevel = freezed,
    Object? calArea = freezed,
    Object? contrArea = freezed,
    Object? businessType = freezed,
    Object? cropType = freezed,
    Object? landType = freezed,
    Object? payType = freezed,
    Object? organicType = freezed,
    Object? landGrade = freezed,
    Object? createBy = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statusCd = freezed,
    Object? greenArea = freezed,
    Object? organicArea = freezed,
    Object? agriProdArea = freezed,
    Object? smartKitchenArea = freezed,
    Object? resArea = freezed,
    Object? riskType = freezed,
    Object? remark = freezed,
    Object? isSameSquareArea = freezed,
    Object? isSdgwzcggt = freezed,
    Object? isBmggt = freezed,
    Object? isQmsff = freezed,
    Object? isWmcjp = freezed,
    Object? isTg = freezed,
    Object? squareNum = freezed,
    Object? year = freezed,
    Object? tillType = freezed,
    Object? disTillReason = freezed,
    Object? blStationNo = freezed,
    Object? blPrecinctNo = freezed,
    Object? blFarmNo = freezed,
    Object? orgCode = freezed,
    Object? hasGeom = freezed,
    Object? hasChild = freezed,
    Object? blFarmName = freezed,
    Object? blBranchComName = freezed,
    Object? blPrecinctName = freezed,
    Object? blStationName = freezed,
    Object? dataStatus = freezed,
    Object? preTwoCropType = freezed,
    Object? preOneCropType = freezed,
    Object? ridgeDistance = freezed,
    Object? userId = freezed,
    Object? name = freezed,
    Object? serialNumber = freezed,
    Object? serialNumberTemp = freezed,
    Object? isGrid = freezed,
    Object? gridNum = freezed,
    Object? aliasName = freezed,
    Object? parentPlotName = freezed,
    Object? plantArea = freezed,
    Object? mainPartType = freezed,
    Object? landTypeGrp = freezed,
    Object? isUnderwrite = freezed,
    Object? permBasicLand = freezed,
    Object? refineLandType = freezed,
  }) {
    return _then(_$LandImpl(
      blBranchComNo: freezed == blBranchComNo
          ? _value.blBranchComNo
          : blBranchComNo // ignore: cast_nullable_to_non_nullable
              as String?,
      cropBreed: freezed == cropBreed
          ? _value.cropBreed
          : cropBreed // ignore: cast_nullable_to_non_nullable
              as String?,
      plotId: freezed == plotId
          ? _value.plotId
          : plotId // ignore: cast_nullable_to_non_nullable
              as num?,
      plotNo: freezed == plotNo
          ? _value.plotNo
          : plotNo // ignore: cast_nullable_to_non_nullable
              as String?,
      plotName: freezed == plotName
          ? _value.plotName
          : plotName // ignore: cast_nullable_to_non_nullable
              as String?,
      pastPlotName: freezed == pastPlotName
          ? _value.pastPlotName
          : pastPlotName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      parentPlotNo: freezed == parentPlotNo
          ? _value.parentPlotNo
          : parentPlotNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotLevel: freezed == plotLevel
          ? _value.plotLevel
          : plotLevel // ignore: cast_nullable_to_non_nullable
              as String?,
      calArea: freezed == calArea
          ? _value.calArea
          : calArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      contrArea: freezed == contrArea
          ? _value.contrArea
          : contrArea // ignore: cast_nullable_to_non_nullable
              as num?,
      businessType: freezed == businessType
          ? _value.businessType
          : businessType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      cropType: freezed == cropType
          ? _value.cropType
          : cropType // ignore: cast_nullable_to_non_nullable
              as String?,
      landType: freezed == landType
          ? _value.landType
          : landType // ignore: cast_nullable_to_non_nullable
              as String?,
      payType: freezed == payType
          ? _value.payType
          : payType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      organicType: freezed == organicType
          ? _value.organicType
          : organicType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      landGrade: freezed == landGrade
          ? _value.landGrade
          : landGrade // ignore: cast_nullable_to_non_nullable
              as dynamic,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as dynamic,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as dynamic,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      greenArea: freezed == greenArea
          ? _value.greenArea
          : greenArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      organicArea: freezed == organicArea
          ? _value.organicArea
          : organicArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      agriProdArea: freezed == agriProdArea
          ? _value.agriProdArea
          : agriProdArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      smartKitchenArea: freezed == smartKitchenArea
          ? _value.smartKitchenArea
          : smartKitchenArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      resArea: freezed == resArea
          ? _value.resArea
          : resArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      riskType: freezed == riskType
          ? _value.riskType
          : riskType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSameSquareArea: freezed == isSameSquareArea
          ? _value.isSameSquareArea
          : isSameSquareArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSdgwzcggt: freezed == isSdgwzcggt
          ? _value.isSdgwzcggt
          : isSdgwzcggt // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isBmggt: freezed == isBmggt
          ? _value.isBmggt
          : isBmggt // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isQmsff: freezed == isQmsff
          ? _value.isQmsff
          : isQmsff // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isWmcjp: freezed == isWmcjp
          ? _value.isWmcjp
          : isWmcjp // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isTg: freezed == isTg
          ? _value.isTg
          : isTg // ignore: cast_nullable_to_non_nullable
              as dynamic,
      squareNum: freezed == squareNum
          ? _value.squareNum
          : squareNum // ignore: cast_nullable_to_non_nullable
              as dynamic,
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as num?,
      tillType: freezed == tillType
          ? _value.tillType
          : tillType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      disTillReason: freezed == disTillReason
          ? _value.disTillReason
          : disTillReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      blStationNo: freezed == blStationNo
          ? _value.blStationNo
          : blStationNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      blPrecinctNo: freezed == blPrecinctNo
          ? _value.blPrecinctNo
          : blPrecinctNo // ignore: cast_nullable_to_non_nullable
              as String?,
      blFarmNo: freezed == blFarmNo
          ? _value.blFarmNo
          : blFarmNo // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      hasGeom: freezed == hasGeom
          ? _value.hasGeom
          : hasGeom // ignore: cast_nullable_to_non_nullable
              as num?,
      hasChild: freezed == hasChild
          ? _value.hasChild
          : hasChild // ignore: cast_nullable_to_non_nullable
              as num?,
      blFarmName: freezed == blFarmName
          ? _value.blFarmName
          : blFarmName // ignore: cast_nullable_to_non_nullable
              as String?,
      blBranchComName: freezed == blBranchComName
          ? _value.blBranchComName
          : blBranchComName // ignore: cast_nullable_to_non_nullable
              as String?,
      blPrecinctName: freezed == blPrecinctName
          ? _value.blPrecinctName
          : blPrecinctName // ignore: cast_nullable_to_non_nullable
              as String?,
      blStationName: freezed == blStationName
          ? _value.blStationName
          : blStationName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dataStatus: freezed == dataStatus
          ? _value.dataStatus
          : dataStatus // ignore: cast_nullable_to_non_nullable
              as num?,
      preTwoCropType: freezed == preTwoCropType
          ? _value.preTwoCropType
          : preTwoCropType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      preOneCropType: freezed == preOneCropType
          ? _value.preOneCropType
          : preOneCropType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      ridgeDistance: freezed == ridgeDistance
          ? _value.ridgeDistance
          : ridgeDistance // ignore: cast_nullable_to_non_nullable
              as dynamic,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as dynamic,
      serialNumber: freezed == serialNumber
          ? _value.serialNumber
          : serialNumber // ignore: cast_nullable_to_non_nullable
              as dynamic,
      serialNumberTemp: freezed == serialNumberTemp
          ? _value.serialNumberTemp
          : serialNumberTemp // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isGrid: freezed == isGrid
          ? _value.isGrid
          : isGrid // ignore: cast_nullable_to_non_nullable
              as dynamic,
      gridNum: freezed == gridNum
          ? _value.gridNum
          : gridNum // ignore: cast_nullable_to_non_nullable
              as dynamic,
      aliasName: freezed == aliasName
          ? _value.aliasName
          : aliasName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      parentPlotName: freezed == parentPlotName
          ? _value.parentPlotName
          : parentPlotName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plantArea: freezed == plantArea
          ? _value.plantArea
          : plantArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      mainPartType: freezed == mainPartType
          ? _value.mainPartType
          : mainPartType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      landTypeGrp: freezed == landTypeGrp
          ? _value.landTypeGrp
          : landTypeGrp // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isUnderwrite: freezed == isUnderwrite
          ? _value.isUnderwrite
          : isUnderwrite // ignore: cast_nullable_to_non_nullable
              as dynamic,
      permBasicLand: freezed == permBasicLand
          ? _value.permBasicLand
          : permBasicLand // ignore: cast_nullable_to_non_nullable
              as dynamic,
      refineLandType: freezed == refineLandType
          ? _value.refineLandType
          : refineLandType // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LandImpl implements _Land {
  _$LandImpl(
      {this.blBranchComNo,
      this.cropBreed,
      this.plotId,
      this.plotNo,
      this.plotName,
      this.pastPlotName,
      this.parentPlotNo,
      this.plotLevel,
      this.calArea,
      this.contrArea,
      this.businessType,
      this.cropType,
      this.landType,
      this.payType,
      this.organicType,
      this.landGrade,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.statusCd,
      this.greenArea,
      this.organicArea,
      this.agriProdArea,
      this.smartKitchenArea,
      this.resArea,
      this.riskType,
      this.remark,
      this.isSameSquareArea,
      this.isSdgwzcggt,
      this.isBmggt,
      this.isQmsff,
      this.isWmcjp,
      this.isTg,
      this.squareNum,
      this.year,
      this.tillType,
      this.disTillReason,
      this.blStationNo,
      this.blPrecinctNo,
      this.blFarmNo,
      this.orgCode,
      this.hasGeom,
      this.hasChild,
      this.blFarmName,
      this.blBranchComName,
      this.blPrecinctName,
      this.blStationName,
      this.dataStatus,
      this.preTwoCropType,
      this.preOneCropType,
      this.ridgeDistance,
      this.userId,
      this.name,
      this.serialNumber,
      this.serialNumberTemp,
      this.isGrid,
      this.gridNum,
      this.aliasName,
      this.parentPlotName,
      this.plantArea,
      this.mainPartType,
      this.landTypeGrp,
      this.isUnderwrite,
      this.permBasicLand,
      this.refineLandType});

  factory _$LandImpl.fromJson(Map<String, dynamic> json) =>
      _$$LandImplFromJson(json);

  @override
  final String? blBranchComNo;
  @override
  final String? cropBreed;
  @override
  final num? plotId;
  @override
  final String? plotNo;
  @override
  final String? plotName;
  @override
  final dynamic pastPlotName;
  @override
  final dynamic parentPlotNo;
  @override
  final String? plotLevel;
  @override
  final dynamic calArea;
  @override
  final num? contrArea;
  @override
  final dynamic businessType;
  @override
  final String? cropType;
  @override
  final String? landType;
  @override
  final dynamic payType;
  @override
  final dynamic organicType;
  @override
  final dynamic landGrade;
  @override
  final dynamic createBy;
  @override
  final dynamic createTime;
  @override
  final dynamic updateBy;
  @override
  final dynamic updateTime;
  @override
  final dynamic statusCd;
  @override
  final dynamic greenArea;
  @override
  final dynamic organicArea;
  @override
  final dynamic agriProdArea;
  @override
  final dynamic smartKitchenArea;
  @override
  final dynamic resArea;
  @override
  final dynamic riskType;
  @override
  final dynamic remark;
  @override
  final dynamic isSameSquareArea;
  @override
  final dynamic isSdgwzcggt;
  @override
  final dynamic isBmggt;
  @override
  final dynamic isQmsff;
  @override
  final dynamic isWmcjp;
  @override
  final dynamic isTg;
  @override
  final dynamic squareNum;
  @override
  final num? year;
  @override
  final dynamic tillType;
  @override
  final dynamic disTillReason;
  @override
  final dynamic blStationNo;
  @override
  final String? blPrecinctNo;
  @override
  final String? blFarmNo;
  @override
  final dynamic orgCode;
  @override
  final num? hasGeom;
  @override
  final num? hasChild;
  @override
  final String? blFarmName;
  @override
  final String? blBranchComName;
  @override
  final String? blPrecinctName;
  @override
  final dynamic blStationName;
  @override
  final num? dataStatus;
  @override
  final dynamic preTwoCropType;
  @override
  final dynamic preOneCropType;
  @override
  final dynamic ridgeDistance;
  @override
  final dynamic userId;
  @override
  final dynamic name;
  @override
  final dynamic serialNumber;
  @override
  final dynamic serialNumberTemp;
  @override
  final dynamic isGrid;
  @override
  final dynamic gridNum;
  @override
  final dynamic aliasName;
  @override
  final dynamic parentPlotName;
  @override
  final dynamic plantArea;
  @override
  final dynamic mainPartType;
  @override
  final dynamic landTypeGrp;
  @override
  final dynamic isUnderwrite;
  @override
  final dynamic permBasicLand;
  @override
  final dynamic refineLandType;

  @override
  String toString() {
    return 'Land(blBranchComNo: $blBranchComNo, cropBreed: $cropBreed, plotId: $plotId, plotNo: $plotNo, plotName: $plotName, pastPlotName: $pastPlotName, parentPlotNo: $parentPlotNo, plotLevel: $plotLevel, calArea: $calArea, contrArea: $contrArea, businessType: $businessType, cropType: $cropType, landType: $landType, payType: $payType, organicType: $organicType, landGrade: $landGrade, createBy: $createBy, createTime: $createTime, updateBy: $updateBy, updateTime: $updateTime, statusCd: $statusCd, greenArea: $greenArea, organicArea: $organicArea, agriProdArea: $agriProdArea, smartKitchenArea: $smartKitchenArea, resArea: $resArea, riskType: $riskType, remark: $remark, isSameSquareArea: $isSameSquareArea, isSdgwzcggt: $isSdgwzcggt, isBmggt: $isBmggt, isQmsff: $isQmsff, isWmcjp: $isWmcjp, isTg: $isTg, squareNum: $squareNum, year: $year, tillType: $tillType, disTillReason: $disTillReason, blStationNo: $blStationNo, blPrecinctNo: $blPrecinctNo, blFarmNo: $blFarmNo, orgCode: $orgCode, hasGeom: $hasGeom, hasChild: $hasChild, blFarmName: $blFarmName, blBranchComName: $blBranchComName, blPrecinctName: $blPrecinctName, blStationName: $blStationName, dataStatus: $dataStatus, preTwoCropType: $preTwoCropType, preOneCropType: $preOneCropType, ridgeDistance: $ridgeDistance, userId: $userId, name: $name, serialNumber: $serialNumber, serialNumberTemp: $serialNumberTemp, isGrid: $isGrid, gridNum: $gridNum, aliasName: $aliasName, parentPlotName: $parentPlotName, plantArea: $plantArea, mainPartType: $mainPartType, landTypeGrp: $landTypeGrp, isUnderwrite: $isUnderwrite, permBasicLand: $permBasicLand, refineLandType: $refineLandType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LandImpl &&
            (identical(other.blBranchComNo, blBranchComNo) ||
                other.blBranchComNo == blBranchComNo) &&
            (identical(other.cropBreed, cropBreed) ||
                other.cropBreed == cropBreed) &&
            (identical(other.plotId, plotId) || other.plotId == plotId) &&
            (identical(other.plotNo, plotNo) || other.plotNo == plotNo) &&
            (identical(other.plotName, plotName) ||
                other.plotName == plotName) &&
            const DeepCollectionEquality()
                .equals(other.pastPlotName, pastPlotName) &&
            const DeepCollectionEquality()
                .equals(other.parentPlotNo, parentPlotNo) &&
            (identical(other.plotLevel, plotLevel) ||
                other.plotLevel == plotLevel) &&
            const DeepCollectionEquality().equals(other.calArea, calArea) &&
            (identical(other.contrArea, contrArea) ||
                other.contrArea == contrArea) &&
            const DeepCollectionEquality()
                .equals(other.businessType, businessType) &&
            (identical(other.cropType, cropType) ||
                other.cropType == cropType) &&
            (identical(other.landType, landType) ||
                other.landType == landType) &&
            const DeepCollectionEquality().equals(other.payType, payType) &&
            const DeepCollectionEquality()
                .equals(other.organicType, organicType) &&
            const DeepCollectionEquality().equals(other.landGrade, landGrade) &&
            const DeepCollectionEquality().equals(other.createBy, createBy) &&
            const DeepCollectionEquality()
                .equals(other.createTime, createTime) &&
            const DeepCollectionEquality().equals(other.updateBy, updateBy) &&
            const DeepCollectionEquality()
                .equals(other.updateTime, updateTime) &&
            const DeepCollectionEquality().equals(other.statusCd, statusCd) &&
            const DeepCollectionEquality().equals(other.greenArea, greenArea) &&
            const DeepCollectionEquality()
                .equals(other.organicArea, organicArea) &&
            const DeepCollectionEquality()
                .equals(other.agriProdArea, agriProdArea) &&
            const DeepCollectionEquality()
                .equals(other.smartKitchenArea, smartKitchenArea) &&
            const DeepCollectionEquality().equals(other.resArea, resArea) &&
            const DeepCollectionEquality().equals(other.riskType, riskType) &&
            const DeepCollectionEquality().equals(other.remark, remark) &&
            const DeepCollectionEquality()
                .equals(other.isSameSquareArea, isSameSquareArea) &&
            const DeepCollectionEquality()
                .equals(other.isSdgwzcggt, isSdgwzcggt) &&
            const DeepCollectionEquality().equals(other.isBmggt, isBmggt) &&
            const DeepCollectionEquality().equals(other.isQmsff, isQmsff) &&
            const DeepCollectionEquality().equals(other.isWmcjp, isWmcjp) &&
            const DeepCollectionEquality().equals(other.isTg, isTg) &&
            const DeepCollectionEquality().equals(other.squareNum, squareNum) &&
            (identical(other.year, year) || other.year == year) &&
            const DeepCollectionEquality().equals(other.tillType, tillType) &&
            const DeepCollectionEquality()
                .equals(other.disTillReason, disTillReason) &&
            const DeepCollectionEquality()
                .equals(other.blStationNo, blStationNo) &&
            (identical(other.blPrecinctNo, blPrecinctNo) ||
                other.blPrecinctNo == blPrecinctNo) &&
            (identical(other.blFarmNo, blFarmNo) ||
                other.blFarmNo == blFarmNo) &&
            const DeepCollectionEquality().equals(other.orgCode, orgCode) &&
            (identical(other.hasGeom, hasGeom) || other.hasGeom == hasGeom) &&
            (identical(other.hasChild, hasChild) ||
                other.hasChild == hasChild) &&
            (identical(other.blFarmName, blFarmName) ||
                other.blFarmName == blFarmName) &&
            (identical(other.blBranchComName, blBranchComName) ||
                other.blBranchComName == blBranchComName) &&
            (identical(other.blPrecinctName, blPrecinctName) ||
                other.blPrecinctName == blPrecinctName) &&
            const DeepCollectionEquality()
                .equals(other.blStationName, blStationName) &&
            (identical(other.dataStatus, dataStatus) ||
                other.dataStatus == dataStatus) &&
            const DeepCollectionEquality()
                .equals(other.preTwoCropType, preTwoCropType) &&
            const DeepCollectionEquality()
                .equals(other.preOneCropType, preOneCropType) &&
            const DeepCollectionEquality()
                .equals(other.ridgeDistance, ridgeDistance) &&
            const DeepCollectionEquality().equals(other.userId, userId) &&
            const DeepCollectionEquality().equals(other.name, name) &&
            const DeepCollectionEquality()
                .equals(other.serialNumber, serialNumber) &&
            const DeepCollectionEquality()
                .equals(other.serialNumberTemp, serialNumberTemp) &&
            const DeepCollectionEquality().equals(other.isGrid, isGrid) &&
            const DeepCollectionEquality().equals(other.gridNum, gridNum) &&
            const DeepCollectionEquality().equals(other.aliasName, aliasName) &&
            const DeepCollectionEquality()
                .equals(other.parentPlotName, parentPlotName) &&
            const DeepCollectionEquality().equals(other.plantArea, plantArea) &&
            const DeepCollectionEquality()
                .equals(other.mainPartType, mainPartType) &&
            const DeepCollectionEquality()
                .equals(other.landTypeGrp, landTypeGrp) &&
            const DeepCollectionEquality()
                .equals(other.isUnderwrite, isUnderwrite) &&
            const DeepCollectionEquality()
                .equals(other.permBasicLand, permBasicLand) &&
            const DeepCollectionEquality()
                .equals(other.refineLandType, refineLandType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        blBranchComNo,
        cropBreed,
        plotId,
        plotNo,
        plotName,
        const DeepCollectionEquality().hash(pastPlotName),
        const DeepCollectionEquality().hash(parentPlotNo),
        plotLevel,
        const DeepCollectionEquality().hash(calArea),
        contrArea,
        const DeepCollectionEquality().hash(businessType),
        cropType,
        landType,
        const DeepCollectionEquality().hash(payType),
        const DeepCollectionEquality().hash(organicType),
        const DeepCollectionEquality().hash(landGrade),
        const DeepCollectionEquality().hash(createBy),
        const DeepCollectionEquality().hash(createTime),
        const DeepCollectionEquality().hash(updateBy),
        const DeepCollectionEquality().hash(updateTime),
        const DeepCollectionEquality().hash(statusCd),
        const DeepCollectionEquality().hash(greenArea),
        const DeepCollectionEquality().hash(organicArea),
        const DeepCollectionEquality().hash(agriProdArea),
        const DeepCollectionEquality().hash(smartKitchenArea),
        const DeepCollectionEquality().hash(resArea),
        const DeepCollectionEquality().hash(riskType),
        const DeepCollectionEquality().hash(remark),
        const DeepCollectionEquality().hash(isSameSquareArea),
        const DeepCollectionEquality().hash(isSdgwzcggt),
        const DeepCollectionEquality().hash(isBmggt),
        const DeepCollectionEquality().hash(isQmsff),
        const DeepCollectionEquality().hash(isWmcjp),
        const DeepCollectionEquality().hash(isTg),
        const DeepCollectionEquality().hash(squareNum),
        year,
        const DeepCollectionEquality().hash(tillType),
        const DeepCollectionEquality().hash(disTillReason),
        const DeepCollectionEquality().hash(blStationNo),
        blPrecinctNo,
        blFarmNo,
        const DeepCollectionEquality().hash(orgCode),
        hasGeom,
        hasChild,
        blFarmName,
        blBranchComName,
        blPrecinctName,
        const DeepCollectionEquality().hash(blStationName),
        dataStatus,
        const DeepCollectionEquality().hash(preTwoCropType),
        const DeepCollectionEquality().hash(preOneCropType),
        const DeepCollectionEquality().hash(ridgeDistance),
        const DeepCollectionEquality().hash(userId),
        const DeepCollectionEquality().hash(name),
        const DeepCollectionEquality().hash(serialNumber),
        const DeepCollectionEquality().hash(serialNumberTemp),
        const DeepCollectionEquality().hash(isGrid),
        const DeepCollectionEquality().hash(gridNum),
        const DeepCollectionEquality().hash(aliasName),
        const DeepCollectionEquality().hash(parentPlotName),
        const DeepCollectionEquality().hash(plantArea),
        const DeepCollectionEquality().hash(mainPartType),
        const DeepCollectionEquality().hash(landTypeGrp),
        const DeepCollectionEquality().hash(isUnderwrite),
        const DeepCollectionEquality().hash(permBasicLand),
        const DeepCollectionEquality().hash(refineLandType)
      ]);

  /// Create a copy of Land
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LandImplCopyWith<_$LandImpl> get copyWith =>
      __$$LandImplCopyWithImpl<_$LandImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LandImplToJson(
      this,
    );
  }
}

abstract class _Land implements Land {
  factory _Land(
      {final String? blBranchComNo,
      final String? cropBreed,
      final num? plotId,
      final String? plotNo,
      final String? plotName,
      final dynamic pastPlotName,
      final dynamic parentPlotNo,
      final String? plotLevel,
      final dynamic calArea,
      final num? contrArea,
      final dynamic businessType,
      final String? cropType,
      final String? landType,
      final dynamic payType,
      final dynamic organicType,
      final dynamic landGrade,
      final dynamic createBy,
      final dynamic createTime,
      final dynamic updateBy,
      final dynamic updateTime,
      final dynamic statusCd,
      final dynamic greenArea,
      final dynamic organicArea,
      final dynamic agriProdArea,
      final dynamic smartKitchenArea,
      final dynamic resArea,
      final dynamic riskType,
      final dynamic remark,
      final dynamic isSameSquareArea,
      final dynamic isSdgwzcggt,
      final dynamic isBmggt,
      final dynamic isQmsff,
      final dynamic isWmcjp,
      final dynamic isTg,
      final dynamic squareNum,
      final num? year,
      final dynamic tillType,
      final dynamic disTillReason,
      final dynamic blStationNo,
      final String? blPrecinctNo,
      final String? blFarmNo,
      final dynamic orgCode,
      final num? hasGeom,
      final num? hasChild,
      final String? blFarmName,
      final String? blBranchComName,
      final String? blPrecinctName,
      final dynamic blStationName,
      final num? dataStatus,
      final dynamic preTwoCropType,
      final dynamic preOneCropType,
      final dynamic ridgeDistance,
      final dynamic userId,
      final dynamic name,
      final dynamic serialNumber,
      final dynamic serialNumberTemp,
      final dynamic isGrid,
      final dynamic gridNum,
      final dynamic aliasName,
      final dynamic parentPlotName,
      final dynamic plantArea,
      final dynamic mainPartType,
      final dynamic landTypeGrp,
      final dynamic isUnderwrite,
      final dynamic permBasicLand,
      final dynamic refineLandType}) = _$LandImpl;

  factory _Land.fromJson(Map<String, dynamic> json) = _$LandImpl.fromJson;

  @override
  String? get blBranchComNo;
  @override
  String? get cropBreed;
  @override
  num? get plotId;
  @override
  String? get plotNo;
  @override
  String? get plotName;
  @override
  dynamic get pastPlotName;
  @override
  dynamic get parentPlotNo;
  @override
  String? get plotLevel;
  @override
  dynamic get calArea;
  @override
  num? get contrArea;
  @override
  dynamic get businessType;
  @override
  String? get cropType;
  @override
  String? get landType;
  @override
  dynamic get payType;
  @override
  dynamic get organicType;
  @override
  dynamic get landGrade;
  @override
  dynamic get createBy;
  @override
  dynamic get createTime;
  @override
  dynamic get updateBy;
  @override
  dynamic get updateTime;
  @override
  dynamic get statusCd;
  @override
  dynamic get greenArea;
  @override
  dynamic get organicArea;
  @override
  dynamic get agriProdArea;
  @override
  dynamic get smartKitchenArea;
  @override
  dynamic get resArea;
  @override
  dynamic get riskType;
  @override
  dynamic get remark;
  @override
  dynamic get isSameSquareArea;
  @override
  dynamic get isSdgwzcggt;
  @override
  dynamic get isBmggt;
  @override
  dynamic get isQmsff;
  @override
  dynamic get isWmcjp;
  @override
  dynamic get isTg;
  @override
  dynamic get squareNum;
  @override
  num? get year;
  @override
  dynamic get tillType;
  @override
  dynamic get disTillReason;
  @override
  dynamic get blStationNo;
  @override
  String? get blPrecinctNo;
  @override
  String? get blFarmNo;
  @override
  dynamic get orgCode;
  @override
  num? get hasGeom;
  @override
  num? get hasChild;
  @override
  String? get blFarmName;
  @override
  String? get blBranchComName;
  @override
  String? get blPrecinctName;
  @override
  dynamic get blStationName;
  @override
  num? get dataStatus;
  @override
  dynamic get preTwoCropType;
  @override
  dynamic get preOneCropType;
  @override
  dynamic get ridgeDistance;
  @override
  dynamic get userId;
  @override
  dynamic get name;
  @override
  dynamic get serialNumber;
  @override
  dynamic get serialNumberTemp;
  @override
  dynamic get isGrid;
  @override
  dynamic get gridNum;
  @override
  dynamic get aliasName;
  @override
  dynamic get parentPlotName;
  @override
  dynamic get plantArea;
  @override
  dynamic get mainPartType;
  @override
  dynamic get landTypeGrp;
  @override
  dynamic get isUnderwrite;
  @override
  dynamic get permBasicLand;
  @override
  dynamic get refineLandType;

  /// Create a copy of Land
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LandImplCopyWith<_$LandImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
