// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'greenhouse.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Greenhouse _$GreenhouseFromJson(Map<String, dynamic> json) {
  return _Greenhouse.fromJson(json);
}

/// @nodoc
mixin _$Greenhouse {
  dynamic get planterIccid => throw _privateConstructorUsedError;
  dynamic get greenhouseType => throw _privateConstructorUsedError;
  num? get specsWidth => throw _privateConstructorUsedError;
  dynamic get geom => throw _privateConstructorUsedError;
  dynamic get geoJson => throw _privateConstructorUsedError;
  int? get greenhouseId => throw _privateConstructorUsedError;
  String? get orgCode => throw _privateConstructorUsedError;
  String? get orgName => throw _privateConstructorUsedError;
  String? get greenhouseName => throw _privateConstructorUsedError;
  String? get chargePerson => throw _privateConstructorUsedError;
  String? get chargePhone => throw _privateConstructorUsedError;
  num? get specsLength => throw _privateConstructorUsedError;
  num? get greenhousePlantId => throw _privateConstructorUsedError;
  String? get planterName => throw _privateConstructorUsedError;
  String? get planterPhone => throw _privateConstructorUsedError;
  dynamic get plantTime => throw _privateConstructorUsedError;
  dynamic get varietyCode => throw _privateConstructorUsedError;
  dynamic get varietyName => throw _privateConstructorUsedError;
  String? get dynamicplotNo => throw _privateConstructorUsedError;
  String? get plotName => throw _privateConstructorUsedError;
  dynamic get plantStageCode => throw _privateConstructorUsedError;
  dynamic get plantStageName => throw _privateConstructorUsedError;
  dynamic get stageMode => throw _privateConstructorUsedError;
  dynamic get accumulatedTemperature => throw _privateConstructorUsedError;
  dynamic get leafAgeUrl => throw _privateConstructorUsedError;
  dynamic get isAoTuo => throw _privateConstructorUsedError;
  dynamic get isRoot => throw _privateConstructorUsedError;
  dynamic get leafNum => throw _privateConstructorUsedError;

  /// Serializes this Greenhouse to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of Greenhouse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $GreenhouseCopyWith<Greenhouse> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GreenhouseCopyWith<$Res> {
  factory $GreenhouseCopyWith(
          Greenhouse value, $Res Function(Greenhouse) then) =
      _$GreenhouseCopyWithImpl<$Res, Greenhouse>;
  @useResult
  $Res call(
      {dynamic planterIccid,
      dynamic greenhouseType,
      num? specsWidth,
      dynamic geom,
      dynamic geoJson,
      int? greenhouseId,
      String? orgCode,
      String? orgName,
      String? greenhouseName,
      String? chargePerson,
      String? chargePhone,
      num? specsLength,
      num? greenhousePlantId,
      String? planterName,
      String? planterPhone,
      dynamic plantTime,
      dynamic varietyCode,
      dynamic varietyName,
      String? dynamicplotNo,
      String? plotName,
      dynamic plantStageCode,
      dynamic plantStageName,
      dynamic stageMode,
      dynamic accumulatedTemperature,
      dynamic leafAgeUrl,
      dynamic isAoTuo,
      dynamic isRoot,
      dynamic leafNum});
}

/// @nodoc
class _$GreenhouseCopyWithImpl<$Res, $Val extends Greenhouse>
    implements $GreenhouseCopyWith<$Res> {
  _$GreenhouseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of Greenhouse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? planterIccid = freezed,
    Object? greenhouseType = freezed,
    Object? specsWidth = freezed,
    Object? geom = freezed,
    Object? geoJson = freezed,
    Object? greenhouseId = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? greenhouseName = freezed,
    Object? chargePerson = freezed,
    Object? chargePhone = freezed,
    Object? specsLength = freezed,
    Object? greenhousePlantId = freezed,
    Object? planterName = freezed,
    Object? planterPhone = freezed,
    Object? plantTime = freezed,
    Object? varietyCode = freezed,
    Object? varietyName = freezed,
    Object? dynamicplotNo = freezed,
    Object? plotName = freezed,
    Object? plantStageCode = freezed,
    Object? plantStageName = freezed,
    Object? stageMode = freezed,
    Object? accumulatedTemperature = freezed,
    Object? leafAgeUrl = freezed,
    Object? isAoTuo = freezed,
    Object? isRoot = freezed,
    Object? leafNum = freezed,
  }) {
    return _then(_value.copyWith(
      planterIccid: freezed == planterIccid
          ? _value.planterIccid
          : planterIccid // ignore: cast_nullable_to_non_nullable
              as dynamic,
      greenhouseType: freezed == greenhouseType
          ? _value.greenhouseType
          : greenhouseType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      specsWidth: freezed == specsWidth
          ? _value.specsWidth
          : specsWidth // ignore: cast_nullable_to_non_nullable
              as num?,
      geom: freezed == geom
          ? _value.geom
          : geom // ignore: cast_nullable_to_non_nullable
              as dynamic,
      geoJson: freezed == geoJson
          ? _value.geoJson
          : geoJson // ignore: cast_nullable_to_non_nullable
              as dynamic,
      greenhouseId: freezed == greenhouseId
          ? _value.greenhouseId
          : greenhouseId // ignore: cast_nullable_to_non_nullable
              as int?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      greenhouseName: freezed == greenhouseName
          ? _value.greenhouseName
          : greenhouseName // ignore: cast_nullable_to_non_nullable
              as String?,
      chargePerson: freezed == chargePerson
          ? _value.chargePerson
          : chargePerson // ignore: cast_nullable_to_non_nullable
              as String?,
      chargePhone: freezed == chargePhone
          ? _value.chargePhone
          : chargePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      specsLength: freezed == specsLength
          ? _value.specsLength
          : specsLength // ignore: cast_nullable_to_non_nullable
              as num?,
      greenhousePlantId: freezed == greenhousePlantId
          ? _value.greenhousePlantId
          : greenhousePlantId // ignore: cast_nullable_to_non_nullable
              as num?,
      planterName: freezed == planterName
          ? _value.planterName
          : planterName // ignore: cast_nullable_to_non_nullable
              as String?,
      planterPhone: freezed == planterPhone
          ? _value.planterPhone
          : planterPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      plantTime: freezed == plantTime
          ? _value.plantTime
          : plantTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      varietyCode: freezed == varietyCode
          ? _value.varietyCode
          : varietyCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      varietyName: freezed == varietyName
          ? _value.varietyName
          : varietyName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dynamicplotNo: freezed == dynamicplotNo
          ? _value.dynamicplotNo
          : dynamicplotNo // ignore: cast_nullable_to_non_nullable
              as String?,
      plotName: freezed == plotName
          ? _value.plotName
          : plotName // ignore: cast_nullable_to_non_nullable
              as String?,
      plantStageCode: freezed == plantStageCode
          ? _value.plantStageCode
          : plantStageCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plantStageName: freezed == plantStageName
          ? _value.plantStageName
          : plantStageName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      stageMode: freezed == stageMode
          ? _value.stageMode
          : stageMode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      accumulatedTemperature: freezed == accumulatedTemperature
          ? _value.accumulatedTemperature
          : accumulatedTemperature // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafAgeUrl: freezed == leafAgeUrl
          ? _value.leafAgeUrl
          : leafAgeUrl // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isAoTuo: freezed == isAoTuo
          ? _value.isAoTuo
          : isAoTuo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isRoot: freezed == isRoot
          ? _value.isRoot
          : isRoot // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafNum: freezed == leafNum
          ? _value.leafNum
          : leafNum // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GreenhouseImplCopyWith<$Res>
    implements $GreenhouseCopyWith<$Res> {
  factory _$$GreenhouseImplCopyWith(
          _$GreenhouseImpl value, $Res Function(_$GreenhouseImpl) then) =
      __$$GreenhouseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {dynamic planterIccid,
      dynamic greenhouseType,
      num? specsWidth,
      dynamic geom,
      dynamic geoJson,
      int? greenhouseId,
      String? orgCode,
      String? orgName,
      String? greenhouseName,
      String? chargePerson,
      String? chargePhone,
      num? specsLength,
      num? greenhousePlantId,
      String? planterName,
      String? planterPhone,
      dynamic plantTime,
      dynamic varietyCode,
      dynamic varietyName,
      String? dynamicplotNo,
      String? plotName,
      dynamic plantStageCode,
      dynamic plantStageName,
      dynamic stageMode,
      dynamic accumulatedTemperature,
      dynamic leafAgeUrl,
      dynamic isAoTuo,
      dynamic isRoot,
      dynamic leafNum});
}

/// @nodoc
class __$$GreenhouseImplCopyWithImpl<$Res>
    extends _$GreenhouseCopyWithImpl<$Res, _$GreenhouseImpl>
    implements _$$GreenhouseImplCopyWith<$Res> {
  __$$GreenhouseImplCopyWithImpl(
      _$GreenhouseImpl _value, $Res Function(_$GreenhouseImpl) _then)
      : super(_value, _then);

  /// Create a copy of Greenhouse
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? planterIccid = freezed,
    Object? greenhouseType = freezed,
    Object? specsWidth = freezed,
    Object? geom = freezed,
    Object? geoJson = freezed,
    Object? greenhouseId = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? greenhouseName = freezed,
    Object? chargePerson = freezed,
    Object? chargePhone = freezed,
    Object? specsLength = freezed,
    Object? greenhousePlantId = freezed,
    Object? planterName = freezed,
    Object? planterPhone = freezed,
    Object? plantTime = freezed,
    Object? varietyCode = freezed,
    Object? varietyName = freezed,
    Object? dynamicplotNo = freezed,
    Object? plotName = freezed,
    Object? plantStageCode = freezed,
    Object? plantStageName = freezed,
    Object? stageMode = freezed,
    Object? accumulatedTemperature = freezed,
    Object? leafAgeUrl = freezed,
    Object? isAoTuo = freezed,
    Object? isRoot = freezed,
    Object? leafNum = freezed,
  }) {
    return _then(_$GreenhouseImpl(
      planterIccid: freezed == planterIccid
          ? _value.planterIccid
          : planterIccid // ignore: cast_nullable_to_non_nullable
              as dynamic,
      greenhouseType: freezed == greenhouseType
          ? _value.greenhouseType
          : greenhouseType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      specsWidth: freezed == specsWidth
          ? _value.specsWidth
          : specsWidth // ignore: cast_nullable_to_non_nullable
              as num?,
      geom: freezed == geom
          ? _value.geom
          : geom // ignore: cast_nullable_to_non_nullable
              as dynamic,
      geoJson: freezed == geoJson
          ? _value.geoJson
          : geoJson // ignore: cast_nullable_to_non_nullable
              as dynamic,
      greenhouseId: freezed == greenhouseId
          ? _value.greenhouseId
          : greenhouseId // ignore: cast_nullable_to_non_nullable
              as int?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      greenhouseName: freezed == greenhouseName
          ? _value.greenhouseName
          : greenhouseName // ignore: cast_nullable_to_non_nullable
              as String?,
      chargePerson: freezed == chargePerson
          ? _value.chargePerson
          : chargePerson // ignore: cast_nullable_to_non_nullable
              as String?,
      chargePhone: freezed == chargePhone
          ? _value.chargePhone
          : chargePhone // ignore: cast_nullable_to_non_nullable
              as String?,
      specsLength: freezed == specsLength
          ? _value.specsLength
          : specsLength // ignore: cast_nullable_to_non_nullable
              as num?,
      greenhousePlantId: freezed == greenhousePlantId
          ? _value.greenhousePlantId
          : greenhousePlantId // ignore: cast_nullable_to_non_nullable
              as num?,
      planterName: freezed == planterName
          ? _value.planterName
          : planterName // ignore: cast_nullable_to_non_nullable
              as String?,
      planterPhone: freezed == planterPhone
          ? _value.planterPhone
          : planterPhone // ignore: cast_nullable_to_non_nullable
              as String?,
      plantTime: freezed == plantTime
          ? _value.plantTime
          : plantTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      varietyCode: freezed == varietyCode
          ? _value.varietyCode
          : varietyCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      varietyName: freezed == varietyName
          ? _value.varietyName
          : varietyName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dynamicplotNo: freezed == dynamicplotNo
          ? _value.dynamicplotNo
          : dynamicplotNo // ignore: cast_nullable_to_non_nullable
              as String?,
      plotName: freezed == plotName
          ? _value.plotName
          : plotName // ignore: cast_nullable_to_non_nullable
              as String?,
      plantStageCode: freezed == plantStageCode
          ? _value.plantStageCode
          : plantStageCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plantStageName: freezed == plantStageName
          ? _value.plantStageName
          : plantStageName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      stageMode: freezed == stageMode
          ? _value.stageMode
          : stageMode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      accumulatedTemperature: freezed == accumulatedTemperature
          ? _value.accumulatedTemperature
          : accumulatedTemperature // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafAgeUrl: freezed == leafAgeUrl
          ? _value.leafAgeUrl
          : leafAgeUrl // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isAoTuo: freezed == isAoTuo
          ? _value.isAoTuo
          : isAoTuo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isRoot: freezed == isRoot
          ? _value.isRoot
          : isRoot // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafNum: freezed == leafNum
          ? _value.leafNum
          : leafNum // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GreenhouseImpl implements _Greenhouse {
  _$GreenhouseImpl(
      {this.planterIccid,
      this.greenhouseType,
      this.specsWidth,
      this.geom,
      this.geoJson,
      this.greenhouseId,
      this.orgCode,
      this.orgName,
      this.greenhouseName,
      this.chargePerson,
      this.chargePhone,
      this.specsLength,
      this.greenhousePlantId,
      this.planterName,
      this.planterPhone,
      this.plantTime,
      this.varietyCode,
      this.varietyName,
      this.dynamicplotNo,
      this.plotName,
      this.plantStageCode,
      this.plantStageName,
      this.stageMode,
      this.accumulatedTemperature,
      this.leafAgeUrl,
      this.isAoTuo,
      this.isRoot,
      this.leafNum});

  factory _$GreenhouseImpl.fromJson(Map<String, dynamic> json) =>
      _$$GreenhouseImplFromJson(json);

  @override
  final dynamic planterIccid;
  @override
  final dynamic greenhouseType;
  @override
  final num? specsWidth;
  @override
  final dynamic geom;
  @override
  final dynamic geoJson;
  @override
  final int? greenhouseId;
  @override
  final String? orgCode;
  @override
  final String? orgName;
  @override
  final String? greenhouseName;
  @override
  final String? chargePerson;
  @override
  final String? chargePhone;
  @override
  final num? specsLength;
  @override
  final num? greenhousePlantId;
  @override
  final String? planterName;
  @override
  final String? planterPhone;
  @override
  final dynamic plantTime;
  @override
  final dynamic varietyCode;
  @override
  final dynamic varietyName;
  @override
  final String? dynamicplotNo;
  @override
  final String? plotName;
  @override
  final dynamic plantStageCode;
  @override
  final dynamic plantStageName;
  @override
  final dynamic stageMode;
  @override
  final dynamic accumulatedTemperature;
  @override
  final dynamic leafAgeUrl;
  @override
  final dynamic isAoTuo;
  @override
  final dynamic isRoot;
  @override
  final dynamic leafNum;

  @override
  String toString() {
    return 'Greenhouse(planterIccid: $planterIccid, greenhouseType: $greenhouseType, specsWidth: $specsWidth, geom: $geom, geoJson: $geoJson, greenhouseId: $greenhouseId, orgCode: $orgCode, orgName: $orgName, greenhouseName: $greenhouseName, chargePerson: $chargePerson, chargePhone: $chargePhone, specsLength: $specsLength, greenhousePlantId: $greenhousePlantId, planterName: $planterName, planterPhone: $planterPhone, plantTime: $plantTime, varietyCode: $varietyCode, varietyName: $varietyName, dynamicplotNo: $dynamicplotNo, plotName: $plotName, plantStageCode: $plantStageCode, plantStageName: $plantStageName, stageMode: $stageMode, accumulatedTemperature: $accumulatedTemperature, leafAgeUrl: $leafAgeUrl, isAoTuo: $isAoTuo, isRoot: $isRoot, leafNum: $leafNum)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GreenhouseImpl &&
            const DeepCollectionEquality()
                .equals(other.planterIccid, planterIccid) &&
            const DeepCollectionEquality()
                .equals(other.greenhouseType, greenhouseType) &&
            (identical(other.specsWidth, specsWidth) ||
                other.specsWidth == specsWidth) &&
            const DeepCollectionEquality().equals(other.geom, geom) &&
            const DeepCollectionEquality().equals(other.geoJson, geoJson) &&
            (identical(other.greenhouseId, greenhouseId) ||
                other.greenhouseId == greenhouseId) &&
            (identical(other.orgCode, orgCode) || other.orgCode == orgCode) &&
            (identical(other.orgName, orgName) || other.orgName == orgName) &&
            (identical(other.greenhouseName, greenhouseName) ||
                other.greenhouseName == greenhouseName) &&
            (identical(other.chargePerson, chargePerson) ||
                other.chargePerson == chargePerson) &&
            (identical(other.chargePhone, chargePhone) ||
                other.chargePhone == chargePhone) &&
            (identical(other.specsLength, specsLength) ||
                other.specsLength == specsLength) &&
            (identical(other.greenhousePlantId, greenhousePlantId) ||
                other.greenhousePlantId == greenhousePlantId) &&
            (identical(other.planterName, planterName) ||
                other.planterName == planterName) &&
            (identical(other.planterPhone, planterPhone) ||
                other.planterPhone == planterPhone) &&
            const DeepCollectionEquality().equals(other.plantTime, plantTime) &&
            const DeepCollectionEquality()
                .equals(other.varietyCode, varietyCode) &&
            const DeepCollectionEquality()
                .equals(other.varietyName, varietyName) &&
            (identical(other.dynamicplotNo, dynamicplotNo) ||
                other.dynamicplotNo == dynamicplotNo) &&
            (identical(other.plotName, plotName) ||
                other.plotName == plotName) &&
            const DeepCollectionEquality()
                .equals(other.plantStageCode, plantStageCode) &&
            const DeepCollectionEquality()
                .equals(other.plantStageName, plantStageName) &&
            const DeepCollectionEquality().equals(other.stageMode, stageMode) &&
            const DeepCollectionEquality()
                .equals(other.accumulatedTemperature, accumulatedTemperature) &&
            const DeepCollectionEquality()
                .equals(other.leafAgeUrl, leafAgeUrl) &&
            const DeepCollectionEquality().equals(other.isAoTuo, isAoTuo) &&
            const DeepCollectionEquality().equals(other.isRoot, isRoot) &&
            const DeepCollectionEquality().equals(other.leafNum, leafNum));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        const DeepCollectionEquality().hash(planterIccid),
        const DeepCollectionEquality().hash(greenhouseType),
        specsWidth,
        const DeepCollectionEquality().hash(geom),
        const DeepCollectionEquality().hash(geoJson),
        greenhouseId,
        orgCode,
        orgName,
        greenhouseName,
        chargePerson,
        chargePhone,
        specsLength,
        greenhousePlantId,
        planterName,
        planterPhone,
        const DeepCollectionEquality().hash(plantTime),
        const DeepCollectionEquality().hash(varietyCode),
        const DeepCollectionEquality().hash(varietyName),
        dynamicplotNo,
        plotName,
        const DeepCollectionEquality().hash(plantStageCode),
        const DeepCollectionEquality().hash(plantStageName),
        const DeepCollectionEquality().hash(stageMode),
        const DeepCollectionEquality().hash(accumulatedTemperature),
        const DeepCollectionEquality().hash(leafAgeUrl),
        const DeepCollectionEquality().hash(isAoTuo),
        const DeepCollectionEquality().hash(isRoot),
        const DeepCollectionEquality().hash(leafNum)
      ]);

  /// Create a copy of Greenhouse
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$GreenhouseImplCopyWith<_$GreenhouseImpl> get copyWith =>
      __$$GreenhouseImplCopyWithImpl<_$GreenhouseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GreenhouseImplToJson(
      this,
    );
  }
}

abstract class _Greenhouse implements Greenhouse {
  factory _Greenhouse(
      {final dynamic planterIccid,
      final dynamic greenhouseType,
      final num? specsWidth,
      final dynamic geom,
      final dynamic geoJson,
      final int? greenhouseId,
      final String? orgCode,
      final String? orgName,
      final String? greenhouseName,
      final String? chargePerson,
      final String? chargePhone,
      final num? specsLength,
      final num? greenhousePlantId,
      final String? planterName,
      final String? planterPhone,
      final dynamic plantTime,
      final dynamic varietyCode,
      final dynamic varietyName,
      final String? dynamicplotNo,
      final String? plotName,
      final dynamic plantStageCode,
      final dynamic plantStageName,
      final dynamic stageMode,
      final dynamic accumulatedTemperature,
      final dynamic leafAgeUrl,
      final dynamic isAoTuo,
      final dynamic isRoot,
      final dynamic leafNum}) = _$GreenhouseImpl;

  factory _Greenhouse.fromJson(Map<String, dynamic> json) =
      _$GreenhouseImpl.fromJson;

  @override
  dynamic get planterIccid;
  @override
  dynamic get greenhouseType;
  @override
  num? get specsWidth;
  @override
  dynamic get geom;
  @override
  dynamic get geoJson;
  @override
  int? get greenhouseId;
  @override
  String? get orgCode;
  @override
  String? get orgName;
  @override
  String? get greenhouseName;
  @override
  String? get chargePerson;
  @override
  String? get chargePhone;
  @override
  num? get specsLength;
  @override
  num? get greenhousePlantId;
  @override
  String? get planterName;
  @override
  String? get planterPhone;
  @override
  dynamic get plantTime;
  @override
  dynamic get varietyCode;
  @override
  dynamic get varietyName;
  @override
  String? get dynamicplotNo;
  @override
  String? get plotName;
  @override
  dynamic get plantStageCode;
  @override
  dynamic get plantStageName;
  @override
  dynamic get stageMode;
  @override
  dynamic get accumulatedTemperature;
  @override
  dynamic get leafAgeUrl;
  @override
  dynamic get isAoTuo;
  @override
  dynamic get isRoot;
  @override
  dynamic get leafNum;

  /// Create a copy of Greenhouse
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$GreenhouseImplCopyWith<_$GreenhouseImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
