// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'leaf_gather_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LeafGatherInfo _$LeafGatherInfoFromJson(Map<String, dynamic> json) {
  return _LeafGatherInfo.fromJson(json);
}

/// @nodoc
mixin _$LeafGatherInfo {
  num? get gatherInfoId => throw _privateConstructorUsedError;
  num? get massifInfoId => throw _privateConstructorUsedError;
  String? get year => throw _privateConstructorUsedError;
  String? get orgCode => throw _privateConstructorUsedError;
  String? get orgName => throw _privateConstructorUsedError;
  String? get gatherName => throw _privateConstructorUsedError;
  dynamic get longtitudeStr => throw _privateConstructorUsedError;
  String? get longitude => throw _privateConstructorUsedError;
  String? get latitude => throw _privateConstructorUsedError;
  String? get sowTime => throw _privateConstructorUsedError;
  dynamic get transplantTime => throw _privateConstructorUsedError;
  dynamic get leafAge => throw _privateConstructorUsedError;
  dynamic get specsLength => throw _privateConstructorUsedError;
  dynamic get specsWidth => throw _privateConstructorUsedError;
  num? get holeNo => throw _privateConstructorUsedError;
  num? get createBy => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  num? get updateBy => throw _privateConstructorUsedError;
  String? get updateTime => throw _privateConstructorUsedError;
  dynamic get statusCd => throw _privateConstructorUsedError;
  LeafMassifInfo? get leafMassifInfo => throw _privateConstructorUsedError;
  dynamic get gatherInfoIds => throw _privateConstructorUsedError;
  dynamic get massifName => throw _privateConstructorUsedError;
  dynamic get authedOrgNos => throw _privateConstructorUsedError;
  dynamic get gatherType => throw _privateConstructorUsedError;
  dynamic get yieldTraitsType => throw _privateConstructorUsedError;

  /// Serializes this LeafGatherInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LeafGatherInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LeafGatherInfoCopyWith<LeafGatherInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LeafGatherInfoCopyWith<$Res> {
  factory $LeafGatherInfoCopyWith(
          LeafGatherInfo value, $Res Function(LeafGatherInfo) then) =
      _$LeafGatherInfoCopyWithImpl<$Res, LeafGatherInfo>;
  @useResult
  $Res call(
      {num? gatherInfoId,
      num? massifInfoId,
      String? year,
      String? orgCode,
      String? orgName,
      String? gatherName,
      dynamic longtitudeStr,
      String? longitude,
      String? latitude,
      String? sowTime,
      dynamic transplantTime,
      dynamic leafAge,
      dynamic specsLength,
      dynamic specsWidth,
      num? holeNo,
      num? createBy,
      String? createTime,
      num? updateBy,
      String? updateTime,
      dynamic statusCd,
      LeafMassifInfo? leafMassifInfo,
      dynamic gatherInfoIds,
      dynamic massifName,
      dynamic authedOrgNos,
      dynamic gatherType,
      dynamic yieldTraitsType});

  $LeafMassifInfoCopyWith<$Res>? get leafMassifInfo;
}

/// @nodoc
class _$LeafGatherInfoCopyWithImpl<$Res, $Val extends LeafGatherInfo>
    implements $LeafGatherInfoCopyWith<$Res> {
  _$LeafGatherInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LeafGatherInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gatherInfoId = freezed,
    Object? massifInfoId = freezed,
    Object? year = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? gatherName = freezed,
    Object? longtitudeStr = freezed,
    Object? longitude = freezed,
    Object? latitude = freezed,
    Object? sowTime = freezed,
    Object? transplantTime = freezed,
    Object? leafAge = freezed,
    Object? specsLength = freezed,
    Object? specsWidth = freezed,
    Object? holeNo = freezed,
    Object? createBy = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statusCd = freezed,
    Object? leafMassifInfo = freezed,
    Object? gatherInfoIds = freezed,
    Object? massifName = freezed,
    Object? authedOrgNos = freezed,
    Object? gatherType = freezed,
    Object? yieldTraitsType = freezed,
  }) {
    return _then(_value.copyWith(
      gatherInfoId: freezed == gatherInfoId
          ? _value.gatherInfoId
          : gatherInfoId // ignore: cast_nullable_to_non_nullable
              as num?,
      massifInfoId: freezed == massifInfoId
          ? _value.massifInfoId
          : massifInfoId // ignore: cast_nullable_to_non_nullable
              as num?,
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      gatherName: freezed == gatherName
          ? _value.gatherName
          : gatherName // ignore: cast_nullable_to_non_nullable
              as String?,
      longtitudeStr: freezed == longtitudeStr
          ? _value.longtitudeStr
          : longtitudeStr // ignore: cast_nullable_to_non_nullable
              as dynamic,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as String?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as String?,
      sowTime: freezed == sowTime
          ? _value.sowTime
          : sowTime // ignore: cast_nullable_to_non_nullable
              as String?,
      transplantTime: freezed == transplantTime
          ? _value.transplantTime
          : transplantTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafAge: freezed == leafAge
          ? _value.leafAge
          : leafAge // ignore: cast_nullable_to_non_nullable
              as dynamic,
      specsLength: freezed == specsLength
          ? _value.specsLength
          : specsLength // ignore: cast_nullable_to_non_nullable
              as dynamic,
      specsWidth: freezed == specsWidth
          ? _value.specsWidth
          : specsWidth // ignore: cast_nullable_to_non_nullable
              as dynamic,
      holeNo: freezed == holeNo
          ? _value.holeNo
          : holeNo // ignore: cast_nullable_to_non_nullable
              as num?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as num?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as num?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafMassifInfo: freezed == leafMassifInfo
          ? _value.leafMassifInfo
          : leafMassifInfo // ignore: cast_nullable_to_non_nullable
              as LeafMassifInfo?,
      gatherInfoIds: freezed == gatherInfoIds
          ? _value.gatherInfoIds
          : gatherInfoIds // ignore: cast_nullable_to_non_nullable
              as dynamic,
      massifName: freezed == massifName
          ? _value.massifName
          : massifName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      authedOrgNos: freezed == authedOrgNos
          ? _value.authedOrgNos
          : authedOrgNos // ignore: cast_nullable_to_non_nullable
              as dynamic,
      gatherType: freezed == gatherType
          ? _value.gatherType
          : gatherType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      yieldTraitsType: freezed == yieldTraitsType
          ? _value.yieldTraitsType
          : yieldTraitsType // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }

  /// Create a copy of LeafGatherInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $LeafMassifInfoCopyWith<$Res>? get leafMassifInfo {
    if (_value.leafMassifInfo == null) {
      return null;
    }

    return $LeafMassifInfoCopyWith<$Res>(_value.leafMassifInfo!, (value) {
      return _then(_value.copyWith(leafMassifInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$LeafGatherInfoImplCopyWith<$Res>
    implements $LeafGatherInfoCopyWith<$Res> {
  factory _$$LeafGatherInfoImplCopyWith(_$LeafGatherInfoImpl value,
          $Res Function(_$LeafGatherInfoImpl) then) =
      __$$LeafGatherInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {num? gatherInfoId,
      num? massifInfoId,
      String? year,
      String? orgCode,
      String? orgName,
      String? gatherName,
      dynamic longtitudeStr,
      String? longitude,
      String? latitude,
      String? sowTime,
      dynamic transplantTime,
      dynamic leafAge,
      dynamic specsLength,
      dynamic specsWidth,
      num? holeNo,
      num? createBy,
      String? createTime,
      num? updateBy,
      String? updateTime,
      dynamic statusCd,
      LeafMassifInfo? leafMassifInfo,
      dynamic gatherInfoIds,
      dynamic massifName,
      dynamic authedOrgNos,
      dynamic gatherType,
      dynamic yieldTraitsType});

  @override
  $LeafMassifInfoCopyWith<$Res>? get leafMassifInfo;
}

/// @nodoc
class __$$LeafGatherInfoImplCopyWithImpl<$Res>
    extends _$LeafGatherInfoCopyWithImpl<$Res, _$LeafGatherInfoImpl>
    implements _$$LeafGatherInfoImplCopyWith<$Res> {
  __$$LeafGatherInfoImplCopyWithImpl(
      _$LeafGatherInfoImpl _value, $Res Function(_$LeafGatherInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of LeafGatherInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? gatherInfoId = freezed,
    Object? massifInfoId = freezed,
    Object? year = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? gatherName = freezed,
    Object? longtitudeStr = freezed,
    Object? longitude = freezed,
    Object? latitude = freezed,
    Object? sowTime = freezed,
    Object? transplantTime = freezed,
    Object? leafAge = freezed,
    Object? specsLength = freezed,
    Object? specsWidth = freezed,
    Object? holeNo = freezed,
    Object? createBy = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statusCd = freezed,
    Object? leafMassifInfo = freezed,
    Object? gatherInfoIds = freezed,
    Object? massifName = freezed,
    Object? authedOrgNos = freezed,
    Object? gatherType = freezed,
    Object? yieldTraitsType = freezed,
  }) {
    return _then(_$LeafGatherInfoImpl(
      gatherInfoId: freezed == gatherInfoId
          ? _value.gatherInfoId
          : gatherInfoId // ignore: cast_nullable_to_non_nullable
              as num?,
      massifInfoId: freezed == massifInfoId
          ? _value.massifInfoId
          : massifInfoId // ignore: cast_nullable_to_non_nullable
              as num?,
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      gatherName: freezed == gatherName
          ? _value.gatherName
          : gatherName // ignore: cast_nullable_to_non_nullable
              as String?,
      longtitudeStr: freezed == longtitudeStr
          ? _value.longtitudeStr
          : longtitudeStr // ignore: cast_nullable_to_non_nullable
              as dynamic,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as String?,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as String?,
      sowTime: freezed == sowTime
          ? _value.sowTime
          : sowTime // ignore: cast_nullable_to_non_nullable
              as String?,
      transplantTime: freezed == transplantTime
          ? _value.transplantTime
          : transplantTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafAge: freezed == leafAge
          ? _value.leafAge
          : leafAge // ignore: cast_nullable_to_non_nullable
              as dynamic,
      specsLength: freezed == specsLength
          ? _value.specsLength
          : specsLength // ignore: cast_nullable_to_non_nullable
              as dynamic,
      specsWidth: freezed == specsWidth
          ? _value.specsWidth
          : specsWidth // ignore: cast_nullable_to_non_nullable
              as dynamic,
      holeNo: freezed == holeNo
          ? _value.holeNo
          : holeNo // ignore: cast_nullable_to_non_nullable
              as num?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as num?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as num?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      leafMassifInfo: freezed == leafMassifInfo
          ? _value.leafMassifInfo
          : leafMassifInfo // ignore: cast_nullable_to_non_nullable
              as LeafMassifInfo?,
      gatherInfoIds: freezed == gatherInfoIds
          ? _value.gatherInfoIds
          : gatherInfoIds // ignore: cast_nullable_to_non_nullable
              as dynamic,
      massifName: freezed == massifName
          ? _value.massifName
          : massifName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      authedOrgNos: freezed == authedOrgNos
          ? _value.authedOrgNos
          : authedOrgNos // ignore: cast_nullable_to_non_nullable
              as dynamic,
      gatherType: freezed == gatherType
          ? _value.gatherType
          : gatherType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      yieldTraitsType: freezed == yieldTraitsType
          ? _value.yieldTraitsType
          : yieldTraitsType // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LeafGatherInfoImpl implements _LeafGatherInfo {
  _$LeafGatherInfoImpl(
      {this.gatherInfoId,
      this.massifInfoId,
      this.year,
      this.orgCode,
      this.orgName,
      this.gatherName,
      this.longtitudeStr,
      this.longitude,
      this.latitude,
      this.sowTime,
      this.transplantTime,
      this.leafAge,
      this.specsLength,
      this.specsWidth,
      this.holeNo,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.statusCd,
      this.leafMassifInfo,
      this.gatherInfoIds,
      this.massifName,
      this.authedOrgNos,
      this.gatherType,
      this.yieldTraitsType});

  factory _$LeafGatherInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$LeafGatherInfoImplFromJson(json);

  @override
  final num? gatherInfoId;
  @override
  final num? massifInfoId;
  @override
  final String? year;
  @override
  final String? orgCode;
  @override
  final String? orgName;
  @override
  final String? gatherName;
  @override
  final dynamic longtitudeStr;
  @override
  final String? longitude;
  @override
  final String? latitude;
  @override
  final String? sowTime;
  @override
  final dynamic transplantTime;
  @override
  final dynamic leafAge;
  @override
  final dynamic specsLength;
  @override
  final dynamic specsWidth;
  @override
  final num? holeNo;
  @override
  final num? createBy;
  @override
  final String? createTime;
  @override
  final num? updateBy;
  @override
  final String? updateTime;
  @override
  final dynamic statusCd;
  @override
  final LeafMassifInfo? leafMassifInfo;
  @override
  final dynamic gatherInfoIds;
  @override
  final dynamic massifName;
  @override
  final dynamic authedOrgNos;
  @override
  final dynamic gatherType;
  @override
  final dynamic yieldTraitsType;

  @override
  String toString() {
    return 'LeafGatherInfo(gatherInfoId: $gatherInfoId, massifInfoId: $massifInfoId, year: $year, orgCode: $orgCode, orgName: $orgName, gatherName: $gatherName, longtitudeStr: $longtitudeStr, longitude: $longitude, latitude: $latitude, sowTime: $sowTime, transplantTime: $transplantTime, leafAge: $leafAge, specsLength: $specsLength, specsWidth: $specsWidth, holeNo: $holeNo, createBy: $createBy, createTime: $createTime, updateBy: $updateBy, updateTime: $updateTime, statusCd: $statusCd, leafMassifInfo: $leafMassifInfo, gatherInfoIds: $gatherInfoIds, massifName: $massifName, authedOrgNos: $authedOrgNos, gatherType: $gatherType, yieldTraitsType: $yieldTraitsType)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LeafGatherInfoImpl &&
            (identical(other.gatherInfoId, gatherInfoId) ||
                other.gatherInfoId == gatherInfoId) &&
            (identical(other.massifInfoId, massifInfoId) ||
                other.massifInfoId == massifInfoId) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.orgCode, orgCode) || other.orgCode == orgCode) &&
            (identical(other.orgName, orgName) || other.orgName == orgName) &&
            (identical(other.gatherName, gatherName) ||
                other.gatherName == gatherName) &&
            const DeepCollectionEquality()
                .equals(other.longtitudeStr, longtitudeStr) &&
            (identical(other.longitude, longitude) ||
                other.longitude == longitude) &&
            (identical(other.latitude, latitude) ||
                other.latitude == latitude) &&
            (identical(other.sowTime, sowTime) || other.sowTime == sowTime) &&
            const DeepCollectionEquality()
                .equals(other.transplantTime, transplantTime) &&
            const DeepCollectionEquality().equals(other.leafAge, leafAge) &&
            const DeepCollectionEquality()
                .equals(other.specsLength, specsLength) &&
            const DeepCollectionEquality()
                .equals(other.specsWidth, specsWidth) &&
            (identical(other.holeNo, holeNo) || other.holeNo == holeNo) &&
            (identical(other.createBy, createBy) ||
                other.createBy == createBy) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateBy, updateBy) ||
                other.updateBy == updateBy) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            const DeepCollectionEquality().equals(other.statusCd, statusCd) &&
            (identical(other.leafMassifInfo, leafMassifInfo) ||
                other.leafMassifInfo == leafMassifInfo) &&
            const DeepCollectionEquality()
                .equals(other.gatherInfoIds, gatherInfoIds) &&
            const DeepCollectionEquality()
                .equals(other.massifName, massifName) &&
            const DeepCollectionEquality()
                .equals(other.authedOrgNos, authedOrgNos) &&
            const DeepCollectionEquality()
                .equals(other.gatherType, gatherType) &&
            const DeepCollectionEquality()
                .equals(other.yieldTraitsType, yieldTraitsType));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        gatherInfoId,
        massifInfoId,
        year,
        orgCode,
        orgName,
        gatherName,
        const DeepCollectionEquality().hash(longtitudeStr),
        longitude,
        latitude,
        sowTime,
        const DeepCollectionEquality().hash(transplantTime),
        const DeepCollectionEquality().hash(leafAge),
        const DeepCollectionEquality().hash(specsLength),
        const DeepCollectionEquality().hash(specsWidth),
        holeNo,
        createBy,
        createTime,
        updateBy,
        updateTime,
        const DeepCollectionEquality().hash(statusCd),
        leafMassifInfo,
        const DeepCollectionEquality().hash(gatherInfoIds),
        const DeepCollectionEquality().hash(massifName),
        const DeepCollectionEquality().hash(authedOrgNos),
        const DeepCollectionEquality().hash(gatherType),
        const DeepCollectionEquality().hash(yieldTraitsType)
      ]);

  /// Create a copy of LeafGatherInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LeafGatherInfoImplCopyWith<_$LeafGatherInfoImpl> get copyWith =>
      __$$LeafGatherInfoImplCopyWithImpl<_$LeafGatherInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LeafGatherInfoImplToJson(
      this,
    );
  }
}

abstract class _LeafGatherInfo implements LeafGatherInfo {
  factory _LeafGatherInfo(
      {final num? gatherInfoId,
      final num? massifInfoId,
      final String? year,
      final String? orgCode,
      final String? orgName,
      final String? gatherName,
      final dynamic longtitudeStr,
      final String? longitude,
      final String? latitude,
      final String? sowTime,
      final dynamic transplantTime,
      final dynamic leafAge,
      final dynamic specsLength,
      final dynamic specsWidth,
      final num? holeNo,
      final num? createBy,
      final String? createTime,
      final num? updateBy,
      final String? updateTime,
      final dynamic statusCd,
      final LeafMassifInfo? leafMassifInfo,
      final dynamic gatherInfoIds,
      final dynamic massifName,
      final dynamic authedOrgNos,
      final dynamic gatherType,
      final dynamic yieldTraitsType}) = _$LeafGatherInfoImpl;

  factory _LeafGatherInfo.fromJson(Map<String, dynamic> json) =
      _$LeafGatherInfoImpl.fromJson;

  @override
  num? get gatherInfoId;
  @override
  num? get massifInfoId;
  @override
  String? get year;
  @override
  String? get orgCode;
  @override
  String? get orgName;
  @override
  String? get gatherName;
  @override
  dynamic get longtitudeStr;
  @override
  String? get longitude;
  @override
  String? get latitude;
  @override
  String? get sowTime;
  @override
  dynamic get transplantTime;
  @override
  dynamic get leafAge;
  @override
  dynamic get specsLength;
  @override
  dynamic get specsWidth;
  @override
  num? get holeNo;
  @override
  num? get createBy;
  @override
  String? get createTime;
  @override
  num? get updateBy;
  @override
  String? get updateTime;
  @override
  dynamic get statusCd;
  @override
  LeafMassifInfo? get leafMassifInfo;
  @override
  dynamic get gatherInfoIds;
  @override
  dynamic get massifName;
  @override
  dynamic get authedOrgNos;
  @override
  dynamic get gatherType;
  @override
  dynamic get yieldTraitsType;

  /// Create a copy of LeafGatherInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LeafGatherInfoImplCopyWith<_$LeafGatherInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
