// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'leaf_massif_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LeafMassifInfo _$LeafMassifInfoFromJson(Map<String, dynamic> json) {
  return _LeafMassifInfo.fromJson(json);
}

/// @nodoc
mixin _$LeafMassifInfo {
  int? get massifInfoId => throw _privateConstructorUsedError;
  String? get year => throw _privateConstructorUsedError;
  String? get orgCode => throw _privateConstructorUsedError;
  String? get orgName => throw _privateConstructorUsedError;
  String? get raiseCropsCd => throw _privateConstructorUsedError;
  String? get massifName => throw _privateConstructorUsedError;
  String? get landName => throw _privateConstructorUsedError;
  String? get raiseCropsNm => throw _privateConstructorUsedError;
  int? get raiseCropsVarietyCd => throw _privateConstructorUsedError;
  String? get raiseCropsVarietyNm => throw _privateConstructorUsedError;
  int? get createBy => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  int? get updateBy => throw _privateConstructorUsedError;
  String? get updateTime => throw _privateConstructorUsedError;
  dynamic get statusCd => throw _privateConstructorUsedError;
  String? get detectLocation => throw _privateConstructorUsedError;
  String? get plotOrGreenhouseName => throw _privateConstructorUsedError;
  String? get plotNo => throw _privateConstructorUsedError;
  String? get plotName => throw _privateConstructorUsedError;
  int? get greenhouseId => throw _privateConstructorUsedError;
  String? get greenhouseName => throw _privateConstructorUsedError;
  dynamic get dataSource => throw _privateConstructorUsedError;
  dynamic get level2Num => throw _privateConstructorUsedError;
  dynamic get authedOrgNos => throw _privateConstructorUsedError;

  /// Serializes this LeafMassifInfo to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LeafMassifInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LeafMassifInfoCopyWith<LeafMassifInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LeafMassifInfoCopyWith<$Res> {
  factory $LeafMassifInfoCopyWith(
          LeafMassifInfo value, $Res Function(LeafMassifInfo) then) =
      _$LeafMassifInfoCopyWithImpl<$Res, LeafMassifInfo>;
  @useResult
  $Res call(
      {int? massifInfoId,
      String? year,
      String? orgCode,
      String? orgName,
      String? raiseCropsCd,
      String? massifName,
      String? landName,
      String? raiseCropsNm,
      int? raiseCropsVarietyCd,
      String? raiseCropsVarietyNm,
      int? createBy,
      String? createTime,
      int? updateBy,
      String? updateTime,
      dynamic statusCd,
      String? detectLocation,
      String? plotOrGreenhouseName,
      String? plotNo,
      String? plotName,
      int? greenhouseId,
      String? greenhouseName,
      dynamic dataSource,
      dynamic level2Num,
      dynamic authedOrgNos});
}

/// @nodoc
class _$LeafMassifInfoCopyWithImpl<$Res, $Val extends LeafMassifInfo>
    implements $LeafMassifInfoCopyWith<$Res> {
  _$LeafMassifInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LeafMassifInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? massifInfoId = freezed,
    Object? year = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? raiseCropsCd = freezed,
    Object? massifName = freezed,
    Object? landName = freezed,
    Object? raiseCropsNm = freezed,
    Object? raiseCropsVarietyCd = freezed,
    Object? raiseCropsVarietyNm = freezed,
    Object? createBy = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statusCd = freezed,
    Object? detectLocation = freezed,
    Object? plotOrGreenhouseName = freezed,
    Object? plotNo = freezed,
    Object? plotName = freezed,
    Object? greenhouseId = freezed,
    Object? greenhouseName = freezed,
    Object? dataSource = freezed,
    Object? level2Num = freezed,
    Object? authedOrgNos = freezed,
  }) {
    return _then(_value.copyWith(
      massifInfoId: freezed == massifInfoId
          ? _value.massifInfoId
          : massifInfoId // ignore: cast_nullable_to_non_nullable
              as int?,
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsCd: freezed == raiseCropsCd
          ? _value.raiseCropsCd
          : raiseCropsCd // ignore: cast_nullable_to_non_nullable
              as String?,
      massifName: freezed == massifName
          ? _value.massifName
          : massifName // ignore: cast_nullable_to_non_nullable
              as String?,
      landName: freezed == landName
          ? _value.landName
          : landName // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsNm: freezed == raiseCropsNm
          ? _value.raiseCropsNm
          : raiseCropsNm // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsVarietyCd: freezed == raiseCropsVarietyCd
          ? _value.raiseCropsVarietyCd
          : raiseCropsVarietyCd // ignore: cast_nullable_to_non_nullable
              as int?,
      raiseCropsVarietyNm: freezed == raiseCropsVarietyNm
          ? _value.raiseCropsVarietyNm
          : raiseCropsVarietyNm // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      detectLocation: freezed == detectLocation
          ? _value.detectLocation
          : detectLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      plotOrGreenhouseName: freezed == plotOrGreenhouseName
          ? _value.plotOrGreenhouseName
          : plotOrGreenhouseName // ignore: cast_nullable_to_non_nullable
              as String?,
      plotNo: freezed == plotNo
          ? _value.plotNo
          : plotNo // ignore: cast_nullable_to_non_nullable
              as String?,
      plotName: freezed == plotName
          ? _value.plotName
          : plotName // ignore: cast_nullable_to_non_nullable
              as String?,
      greenhouseId: freezed == greenhouseId
          ? _value.greenhouseId
          : greenhouseId // ignore: cast_nullable_to_non_nullable
              as int?,
      greenhouseName: freezed == greenhouseName
          ? _value.greenhouseName
          : greenhouseName // ignore: cast_nullable_to_non_nullable
              as String?,
      dataSource: freezed == dataSource
          ? _value.dataSource
          : dataSource // ignore: cast_nullable_to_non_nullable
              as dynamic,
      level2Num: freezed == level2Num
          ? _value.level2Num
          : level2Num // ignore: cast_nullable_to_non_nullable
              as dynamic,
      authedOrgNos: freezed == authedOrgNos
          ? _value.authedOrgNos
          : authedOrgNos // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LeafMassifInfoImplCopyWith<$Res>
    implements $LeafMassifInfoCopyWith<$Res> {
  factory _$$LeafMassifInfoImplCopyWith(_$LeafMassifInfoImpl value,
          $Res Function(_$LeafMassifInfoImpl) then) =
      __$$LeafMassifInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? massifInfoId,
      String? year,
      String? orgCode,
      String? orgName,
      String? raiseCropsCd,
      String? massifName,
      String? landName,
      String? raiseCropsNm,
      int? raiseCropsVarietyCd,
      String? raiseCropsVarietyNm,
      int? createBy,
      String? createTime,
      int? updateBy,
      String? updateTime,
      dynamic statusCd,
      String? detectLocation,
      String? plotOrGreenhouseName,
      String? plotNo,
      String? plotName,
      int? greenhouseId,
      String? greenhouseName,
      dynamic dataSource,
      dynamic level2Num,
      dynamic authedOrgNos});
}

/// @nodoc
class __$$LeafMassifInfoImplCopyWithImpl<$Res>
    extends _$LeafMassifInfoCopyWithImpl<$Res, _$LeafMassifInfoImpl>
    implements _$$LeafMassifInfoImplCopyWith<$Res> {
  __$$LeafMassifInfoImplCopyWithImpl(
      _$LeafMassifInfoImpl _value, $Res Function(_$LeafMassifInfoImpl) _then)
      : super(_value, _then);

  /// Create a copy of LeafMassifInfo
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? massifInfoId = freezed,
    Object? year = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? raiseCropsCd = freezed,
    Object? massifName = freezed,
    Object? landName = freezed,
    Object? raiseCropsNm = freezed,
    Object? raiseCropsVarietyCd = freezed,
    Object? raiseCropsVarietyNm = freezed,
    Object? createBy = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statusCd = freezed,
    Object? detectLocation = freezed,
    Object? plotOrGreenhouseName = freezed,
    Object? plotNo = freezed,
    Object? plotName = freezed,
    Object? greenhouseId = freezed,
    Object? greenhouseName = freezed,
    Object? dataSource = freezed,
    Object? level2Num = freezed,
    Object? authedOrgNos = freezed,
  }) {
    return _then(_$LeafMassifInfoImpl(
      massifInfoId: freezed == massifInfoId
          ? _value.massifInfoId
          : massifInfoId // ignore: cast_nullable_to_non_nullable
              as int?,
      year: freezed == year
          ? _value.year
          : year // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsCd: freezed == raiseCropsCd
          ? _value.raiseCropsCd
          : raiseCropsCd // ignore: cast_nullable_to_non_nullable
              as String?,
      massifName: freezed == massifName
          ? _value.massifName
          : massifName // ignore: cast_nullable_to_non_nullable
              as String?,
      landName: freezed == landName
          ? _value.landName
          : landName // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsNm: freezed == raiseCropsNm
          ? _value.raiseCropsNm
          : raiseCropsNm // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsVarietyCd: freezed == raiseCropsVarietyCd
          ? _value.raiseCropsVarietyCd
          : raiseCropsVarietyCd // ignore: cast_nullable_to_non_nullable
              as int?,
      raiseCropsVarietyNm: freezed == raiseCropsVarietyNm
          ? _value.raiseCropsVarietyNm
          : raiseCropsVarietyNm // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      detectLocation: freezed == detectLocation
          ? _value.detectLocation
          : detectLocation // ignore: cast_nullable_to_non_nullable
              as String?,
      plotOrGreenhouseName: freezed == plotOrGreenhouseName
          ? _value.plotOrGreenhouseName
          : plotOrGreenhouseName // ignore: cast_nullable_to_non_nullable
              as String?,
      plotNo: freezed == plotNo
          ? _value.plotNo
          : plotNo // ignore: cast_nullable_to_non_nullable
              as String?,
      plotName: freezed == plotName
          ? _value.plotName
          : plotName // ignore: cast_nullable_to_non_nullable
              as String?,
      greenhouseId: freezed == greenhouseId
          ? _value.greenhouseId
          : greenhouseId // ignore: cast_nullable_to_non_nullable
              as int?,
      greenhouseName: freezed == greenhouseName
          ? _value.greenhouseName
          : greenhouseName // ignore: cast_nullable_to_non_nullable
              as String?,
      dataSource: freezed == dataSource
          ? _value.dataSource
          : dataSource // ignore: cast_nullable_to_non_nullable
              as dynamic,
      level2Num: freezed == level2Num
          ? _value.level2Num
          : level2Num // ignore: cast_nullable_to_non_nullable
              as dynamic,
      authedOrgNos: freezed == authedOrgNos
          ? _value.authedOrgNos
          : authedOrgNos // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LeafMassifInfoImpl implements _LeafMassifInfo {
  _$LeafMassifInfoImpl(
      {this.massifInfoId,
      this.year,
      this.orgCode,
      this.orgName,
      this.raiseCropsCd,
      this.massifName,
      this.landName,
      this.raiseCropsNm,
      this.raiseCropsVarietyCd,
      this.raiseCropsVarietyNm,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.statusCd,
      this.detectLocation,
      this.plotOrGreenhouseName,
      this.plotNo,
      this.plotName,
      this.greenhouseId,
      this.greenhouseName,
      this.dataSource,
      this.level2Num,
      this.authedOrgNos});

  factory _$LeafMassifInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$LeafMassifInfoImplFromJson(json);

  @override
  final int? massifInfoId;
  @override
  final String? year;
  @override
  final String? orgCode;
  @override
  final String? orgName;
  @override
  final String? raiseCropsCd;
  @override
  final String? massifName;
  @override
  final String? landName;
  @override
  final String? raiseCropsNm;
  @override
  final int? raiseCropsVarietyCd;
  @override
  final String? raiseCropsVarietyNm;
  @override
  final int? createBy;
  @override
  final String? createTime;
  @override
  final int? updateBy;
  @override
  final String? updateTime;
  @override
  final dynamic statusCd;
  @override
  final String? detectLocation;
  @override
  final String? plotOrGreenhouseName;
  @override
  final String? plotNo;
  @override
  final String? plotName;
  @override
  final int? greenhouseId;
  @override
  final String? greenhouseName;
  @override
  final dynamic dataSource;
  @override
  final dynamic level2Num;
  @override
  final dynamic authedOrgNos;

  @override
  String toString() {
    return 'LeafMassifInfo(massifInfoId: $massifInfoId, year: $year, orgCode: $orgCode, orgName: $orgName, raiseCropsCd: $raiseCropsCd, massifName: $massifName, landName: $landName, raiseCropsNm: $raiseCropsNm, raiseCropsVarietyCd: $raiseCropsVarietyCd, raiseCropsVarietyNm: $raiseCropsVarietyNm, createBy: $createBy, createTime: $createTime, updateBy: $updateBy, updateTime: $updateTime, statusCd: $statusCd, detectLocation: $detectLocation, plotOrGreenhouseName: $plotOrGreenhouseName, plotNo: $plotNo, plotName: $plotName, greenhouseId: $greenhouseId, greenhouseName: $greenhouseName, dataSource: $dataSource, level2Num: $level2Num, authedOrgNos: $authedOrgNos)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LeafMassifInfoImpl &&
            (identical(other.massifInfoId, massifInfoId) ||
                other.massifInfoId == massifInfoId) &&
            (identical(other.year, year) || other.year == year) &&
            (identical(other.orgCode, orgCode) || other.orgCode == orgCode) &&
            (identical(other.orgName, orgName) || other.orgName == orgName) &&
            (identical(other.raiseCropsCd, raiseCropsCd) ||
                other.raiseCropsCd == raiseCropsCd) &&
            (identical(other.massifName, massifName) ||
                other.massifName == massifName) &&
            (identical(other.landName, landName) ||
                other.landName == landName) &&
            (identical(other.raiseCropsNm, raiseCropsNm) ||
                other.raiseCropsNm == raiseCropsNm) &&
            (identical(other.raiseCropsVarietyCd, raiseCropsVarietyCd) ||
                other.raiseCropsVarietyCd == raiseCropsVarietyCd) &&
            (identical(other.raiseCropsVarietyNm, raiseCropsVarietyNm) ||
                other.raiseCropsVarietyNm == raiseCropsVarietyNm) &&
            (identical(other.createBy, createBy) ||
                other.createBy == createBy) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateBy, updateBy) ||
                other.updateBy == updateBy) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            const DeepCollectionEquality().equals(other.statusCd, statusCd) &&
            (identical(other.detectLocation, detectLocation) ||
                other.detectLocation == detectLocation) &&
            (identical(other.plotOrGreenhouseName, plotOrGreenhouseName) ||
                other.plotOrGreenhouseName == plotOrGreenhouseName) &&
            (identical(other.plotNo, plotNo) || other.plotNo == plotNo) &&
            (identical(other.plotName, plotName) ||
                other.plotName == plotName) &&
            (identical(other.greenhouseId, greenhouseId) ||
                other.greenhouseId == greenhouseId) &&
            (identical(other.greenhouseName, greenhouseName) ||
                other.greenhouseName == greenhouseName) &&
            const DeepCollectionEquality()
                .equals(other.dataSource, dataSource) &&
            const DeepCollectionEquality().equals(other.level2Num, level2Num) &&
            const DeepCollectionEquality()
                .equals(other.authedOrgNos, authedOrgNos));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        massifInfoId,
        year,
        orgCode,
        orgName,
        raiseCropsCd,
        massifName,
        landName,
        raiseCropsNm,
        raiseCropsVarietyCd,
        raiseCropsVarietyNm,
        createBy,
        createTime,
        updateBy,
        updateTime,
        const DeepCollectionEquality().hash(statusCd),
        detectLocation,
        plotOrGreenhouseName,
        plotNo,
        plotName,
        greenhouseId,
        greenhouseName,
        const DeepCollectionEquality().hash(dataSource),
        const DeepCollectionEquality().hash(level2Num),
        const DeepCollectionEquality().hash(authedOrgNos)
      ]);

  /// Create a copy of LeafMassifInfo
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LeafMassifInfoImplCopyWith<_$LeafMassifInfoImpl> get copyWith =>
      __$$LeafMassifInfoImplCopyWithImpl<_$LeafMassifInfoImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LeafMassifInfoImplToJson(
      this,
    );
  }
}

abstract class _LeafMassifInfo implements LeafMassifInfo {
  factory _LeafMassifInfo(
      {final int? massifInfoId,
      final String? year,
      final String? orgCode,
      final String? orgName,
      final String? raiseCropsCd,
      final String? massifName,
      final String? landName,
      final String? raiseCropsNm,
      final int? raiseCropsVarietyCd,
      final String? raiseCropsVarietyNm,
      final int? createBy,
      final String? createTime,
      final int? updateBy,
      final String? updateTime,
      final dynamic statusCd,
      final String? detectLocation,
      final String? plotOrGreenhouseName,
      final String? plotNo,
      final String? plotName,
      final int? greenhouseId,
      final String? greenhouseName,
      final dynamic dataSource,
      final dynamic level2Num,
      final dynamic authedOrgNos}) = _$LeafMassifInfoImpl;

  factory _LeafMassifInfo.fromJson(Map<String, dynamic> json) =
      _$LeafMassifInfoImpl.fromJson;

  @override
  int? get massifInfoId;
  @override
  String? get year;
  @override
  String? get orgCode;
  @override
  String? get orgName;
  @override
  String? get raiseCropsCd;
  @override
  String? get massifName;
  @override
  String? get landName;
  @override
  String? get raiseCropsNm;
  @override
  int? get raiseCropsVarietyCd;
  @override
  String? get raiseCropsVarietyNm;
  @override
  int? get createBy;
  @override
  String? get createTime;
  @override
  int? get updateBy;
  @override
  String? get updateTime;
  @override
  dynamic get statusCd;
  @override
  String? get detectLocation;
  @override
  String? get plotOrGreenhouseName;
  @override
  String? get plotNo;
  @override
  String? get plotName;
  @override
  int? get greenhouseId;
  @override
  String? get greenhouseName;
  @override
  dynamic get dataSource;
  @override
  dynamic get level2Num;
  @override
  dynamic get authedOrgNos;

  /// Create a copy of LeafMassifInfo
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LeafMassifInfoImplCopyWith<_$LeafMassifInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
