// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leaf_gather_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LeafGatherInfoImpl _$$LeafGatherInfoImplFromJson(Map<String, dynamic> json) =>
    _$LeafGatherInfoImpl(
      gatherInfoId: json['gatherInfoId'] as num?,
      massifInfoId: json['massifInfoId'] as num?,
      year: json['year'] as String?,
      orgCode: json['orgCode'] as String?,
      orgName: json['orgName'] as String?,
      gatherName: json['gatherName'] as String?,
      longtitudeStr: json['longtitudeStr'],
      longitude: json['longitude'] as String?,
      latitude: json['latitude'] as String?,
      sowTime: json['sowTime'] as String?,
      transplantTime: json['transplantTime'],
      leafAge: json['leafAge'],
      specsLength: json['specsLength'],
      specsWidth: json['specsWidth'],
      holeNo: json['holeNo'] as num?,
      createBy: json['createBy'] as num?,
      createTime: json['createTime'] as String?,
      updateBy: json['updateBy'] as num?,
      updateTime: json['updateTime'] as String?,
      statusCd: json['statusCd'],
      leafMassifInfo: json['leafMassifInfo'] == null
          ? null
          : LeafMassifInfo.fromJson(
              json['leafMassifInfo'] as Map<String, dynamic>),
      gatherInfoIds: json['gatherInfoIds'],
      massifName: json['massifName'],
      authedOrgNos: json['authedOrgNos'],
      gatherType: json['gatherType'],
      yieldTraitsType: json['yieldTraitsType'],
    );

Map<String, dynamic> _$$LeafGatherInfoImplToJson(
        _$LeafGatherInfoImpl instance) =>
    <String, dynamic>{
      'gatherInfoId': instance.gatherInfoId,
      'massifInfoId': instance.massifInfoId,
      'year': instance.year,
      'orgCode': instance.orgCode,
      'orgName': instance.orgName,
      'gatherName': instance.gatherName,
      'longtitudeStr': instance.longtitudeStr,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'sowTime': instance.sowTime,
      'transplantTime': instance.transplantTime,
      'leafAge': instance.leafAge,
      'specsLength': instance.specsLength,
      'specsWidth': instance.specsWidth,
      'holeNo': instance.holeNo,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'statusCd': instance.statusCd,
      'leafMassifInfo': instance.leafMassifInfo,
      'gatherInfoIds': instance.gatherInfoIds,
      'massifName': instance.massifName,
      'authedOrgNos': instance.authedOrgNos,
      'gatherType': instance.gatherType,
      'yieldTraitsType': instance.yieldTraitsType,
    };
