import 'package:freezed_annotation/freezed_annotation.dart';

part 'sub_menu.freezed.dart';
part 'sub_menu.g.dart';

@freezed
class SubMenu with _$SubMenu {
  factory SubMenu({
    String? text,
    dynamic group,
    String? path,
    String? icon,
    String? parentText,
    dynamic others,
    bool? hidden,
  }) = _SubMenu;

  factory SubMenu.fromJson(Map<String, dynamic> json) =>
      _$SubMenuFromJson(json);
}
