// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'land.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LandImpl _$$LandImplFromJson(Map<String, dynamic> json) => _$LandImpl(
      blBranchComNo: json['blBranchComNo'] as String?,
      cropBreed: json['cropBreed'] as String?,
      plotId: json['plotId'] as num?,
      plotNo: json['plotNo'] as String?,
      plotName: json['plotName'] as String?,
      pastPlotName: json['pastPlotName'],
      parentPlotNo: json['parentPlotNo'],
      plotLevel: json['plotLevel'] as String?,
      calArea: json['calArea'],
      contrArea: json['contrArea'] as num?,
      businessType: json['businessType'],
      cropType: json['cropType'] as String?,
      landType: json['landType'] as String?,
      payType: json['payType'],
      organicType: json['organicType'],
      landGrade: json['landGrade'],
      createBy: json['createBy'],
      createTime: json['createTime'],
      updateBy: json['updateBy'],
      updateTime: json['updateTime'],
      statusCd: json['statusCd'],
      greenArea: json['greenArea'],
      organicArea: json['organicArea'],
      agriProdArea: json['agriProdArea'],
      smartKitchenArea: json['smartKitchenArea'],
      resArea: json['resArea'],
      riskType: json['riskType'],
      remark: json['remark'],
      isSameSquareArea: json['isSameSquareArea'],
      isSdgwzcggt: json['isSdgwzcggt'],
      isBmggt: json['isBmggt'],
      isQmsff: json['isQmsff'],
      isWmcjp: json['isWmcjp'],
      isTg: json['isTg'],
      squareNum: json['squareNum'],
      year: json['year'] as num?,
      tillType: json['tillType'],
      disTillReason: json['disTillReason'],
      blStationNo: json['blStationNo'],
      blPrecinctNo: json['blPrecinctNo'] as String?,
      blFarmNo: json['blFarmNo'] as String?,
      orgCode: json['orgCode'],
      hasGeom: json['hasGeom'] as num?,
      hasChild: json['hasChild'] as num?,
      blFarmName: json['blFarmName'] as String?,
      blBranchComName: json['blBranchComName'] as String?,
      blPrecinctName: json['blPrecinctName'] as String?,
      blStationName: json['blStationName'],
      dataStatus: json['dataStatus'] as num?,
      preTwoCropType: json['preTwoCropType'],
      preOneCropType: json['preOneCropType'],
      ridgeDistance: json['ridgeDistance'],
      userId: json['userId'],
      name: json['name'],
      serialNumber: json['serialNumber'],
      serialNumberTemp: json['serialNumberTemp'],
      isGrid: json['isGrid'],
      gridNum: json['gridNum'],
      aliasName: json['aliasName'],
      parentPlotName: json['parentPlotName'],
      plantArea: json['plantArea'],
      mainPartType: json['mainPartType'],
      landTypeGrp: json['landTypeGrp'],
      isUnderwrite: json['isUnderwrite'],
      permBasicLand: json['permBasicLand'],
      refineLandType: json['refineLandType'],
    );

Map<String, dynamic> _$$LandImplToJson(_$LandImpl instance) =>
    <String, dynamic>{
      'blBranchComNo': instance.blBranchComNo,
      'cropBreed': instance.cropBreed,
      'plotId': instance.plotId,
      'plotNo': instance.plotNo,
      'plotName': instance.plotName,
      'pastPlotName': instance.pastPlotName,
      'parentPlotNo': instance.parentPlotNo,
      'plotLevel': instance.plotLevel,
      'calArea': instance.calArea,
      'contrArea': instance.contrArea,
      'businessType': instance.businessType,
      'cropType': instance.cropType,
      'landType': instance.landType,
      'payType': instance.payType,
      'organicType': instance.organicType,
      'landGrade': instance.landGrade,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'statusCd': instance.statusCd,
      'greenArea': instance.greenArea,
      'organicArea': instance.organicArea,
      'agriProdArea': instance.agriProdArea,
      'smartKitchenArea': instance.smartKitchenArea,
      'resArea': instance.resArea,
      'riskType': instance.riskType,
      'remark': instance.remark,
      'isSameSquareArea': instance.isSameSquareArea,
      'isSdgwzcggt': instance.isSdgwzcggt,
      'isBmggt': instance.isBmggt,
      'isQmsff': instance.isQmsff,
      'isWmcjp': instance.isWmcjp,
      'isTg': instance.isTg,
      'squareNum': instance.squareNum,
      'year': instance.year,
      'tillType': instance.tillType,
      'disTillReason': instance.disTillReason,
      'blStationNo': instance.blStationNo,
      'blPrecinctNo': instance.blPrecinctNo,
      'blFarmNo': instance.blFarmNo,
      'orgCode': instance.orgCode,
      'hasGeom': instance.hasGeom,
      'hasChild': instance.hasChild,
      'blFarmName': instance.blFarmName,
      'blBranchComName': instance.blBranchComName,
      'blPrecinctName': instance.blPrecinctName,
      'blStationName': instance.blStationName,
      'dataStatus': instance.dataStatus,
      'preTwoCropType': instance.preTwoCropType,
      'preOneCropType': instance.preOneCropType,
      'ridgeDistance': instance.ridgeDistance,
      'userId': instance.userId,
      'name': instance.name,
      'serialNumber': instance.serialNumber,
      'serialNumberTemp': instance.serialNumberTemp,
      'isGrid': instance.isGrid,
      'gridNum': instance.gridNum,
      'aliasName': instance.aliasName,
      'parentPlotName': instance.parentPlotName,
      'plantArea': instance.plantArea,
      'mainPartType': instance.mainPartType,
      'landTypeGrp': instance.landTypeGrp,
      'isUnderwrite': instance.isUnderwrite,
      'permBasicLand': instance.permBasicLand,
      'refineLandType': instance.refineLandType,
    };
