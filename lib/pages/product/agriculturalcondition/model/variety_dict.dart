import 'package:freezed_annotation/freezed_annotation.dart';

part 'variety_dict.freezed.dart';
part 'variety_dict.g.dart';

@freezed
class VarietyDict with _$VarietyDict {
  factory VarietyDict(
      {dynamic growPatternsId,
      num? leafAgeMin,
      num? leafAgeMax,
      String? orgCode,
      num? raiseCropsVarietyCd,
      String? raiseCropsVarietyNm,
      num? raiseLeafMax,
      dynamic temp,
      String? raiseCropsCd,
      String? raiseCropsNm}) = _VarietyDict;

  factory VarietyDict.fromJson(Map<String, dynamic> json) =>
      _$VarietyDictFromJson(json);
}
