// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'leaf_massif_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LeafMassifInfoImpl _$$LeafMassifInfoImplFromJson(Map<String, dynamic> json) =>
    _$LeafMassifInfoImpl(
      massifInfoId: (json['massifInfoId'] as num?)?.toInt(),
      year: json['year'] as String?,
      orgCode: json['orgCode'] as String?,
      orgName: json['orgName'] as String?,
      raiseCropsCd: json['raiseCropsCd'] as String?,
      massifName: json['massifName'] as String?,
      landName: json['landName'] as String?,
      raiseCropsNm: json['raiseCropsNm'] as String?,
      raiseCropsVarietyCd: (json['raiseCropsVarietyCd'] as num?)?.toInt(),
      raiseCropsVarietyNm: json['raiseCropsVarietyNm'] as String?,
      createBy: (json['createBy'] as num?)?.toInt(),
      createTime: json['createTime'] as String?,
      updateBy: (json['updateBy'] as num?)?.toInt(),
      updateTime: json['updateTime'] as String?,
      statusCd: json['statusCd'],
      detectLocation: json['detectLocation'] as String?,
      plotOrGreenhouseName: json['plotOrGreenhouseName'] as String?,
      plotNo: json['plotNo'] as String?,
      plotName: json['plotName'] as String?,
      greenhouseId: (json['greenhouseId'] as num?)?.toInt(),
      greenhouseName: json['greenhouseName'] as String?,
      dataSource: json['dataSource'],
      level2Num: json['level2Num'],
      authedOrgNos: json['authedOrgNos'],
    );

Map<String, dynamic> _$$LeafMassifInfoImplToJson(
        _$LeafMassifInfoImpl instance) =>
    <String, dynamic>{
      'massifInfoId': instance.massifInfoId,
      'year': instance.year,
      'orgCode': instance.orgCode,
      'orgName': instance.orgName,
      'raiseCropsCd': instance.raiseCropsCd,
      'massifName': instance.massifName,
      'landName': instance.landName,
      'raiseCropsNm': instance.raiseCropsNm,
      'raiseCropsVarietyCd': instance.raiseCropsVarietyCd,
      'raiseCropsVarietyNm': instance.raiseCropsVarietyNm,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'statusCd': instance.statusCd,
      'detectLocation': instance.detectLocation,
      'plotOrGreenhouseName': instance.plotOrGreenhouseName,
      'plotNo': instance.plotNo,
      'plotName': instance.plotName,
      'greenhouseId': instance.greenhouseId,
      'greenhouseName': instance.greenhouseName,
      'dataSource': instance.dataSource,
      'level2Num': instance.level2Num,
      'authedOrgNos': instance.authedOrgNos,
    };
