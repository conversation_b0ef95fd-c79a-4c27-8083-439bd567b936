// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'variety_dict.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$VarietyDictImpl _$$VarietyDictImplFromJson(Map<String, dynamic> json) =>
    _$VarietyDictImpl(
      growPatternsId: json['growPatternsId'],
      leafAgeMin: json['leafAgeMin'] as num?,
      leafAgeMax: json['leafAgeMax'] as num?,
      orgCode: json['orgCode'] as String?,
      raiseCropsVarietyCd: json['raiseCropsVarietyCd'] as num?,
      raiseCropsVarietyNm: json['raiseCropsVarietyNm'] as String?,
      raiseLeafMax: json['raiseLeafMax'] as num?,
      temp: json['temp'],
      raiseCropsCd: json['raiseCropsCd'] as String?,
      raiseCropsNm: json['raiseCropsNm'] as String?,
    );

Map<String, dynamic> _$$VarietyDictImplToJson(_$VarietyDictImpl instance) =>
    <String, dynamic>{
      'growPatternsId': instance.growPatternsId,
      'leafAgeMin': instance.leafAgeMin,
      'leafAgeMax': instance.leafAgeMax,
      'orgCode': instance.orgCode,
      'raiseCropsVarietyCd': instance.raiseCropsVarietyCd,
      'raiseCropsVarietyNm': instance.raiseCropsVarietyNm,
      'raiseLeafMax': instance.raiseLeafMax,
      'temp': instance.temp,
      'raiseCropsCd': instance.raiseCropsCd,
      'raiseCropsNm': instance.raiseCropsNm,
    };
