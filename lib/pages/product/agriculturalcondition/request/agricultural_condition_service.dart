import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

class AgriculturalConditionService {
  const AgriculturalConditionService._();

  static const AgriculturalConditionService _instance =
      AgriculturalConditionService._();

  factory AgriculturalConditionService() => _instance;

  //品种作物信息维护
  Future<RequestNoData> massifQueryByPage(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-massif-info/queryByPage",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> massifAdd(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-massif-info/add", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  //采集点信息维护
  Future<RequestNoData> collectPointQueryByPage(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-gather-info/queryByPage",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> collectPointQueryMax(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-gather-info/getId", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> collectPointAdd(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-gather-info/add", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> collectPointQueryOne(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-gather-info/getOne", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> collectPointUpdate(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-gather-info/update", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> commitCache(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-gather-detail/save", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> reportHistory(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-gather-detail/hispage",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> historyCollectPointQueryOne(
      {Object? id, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-gather-detail/detail/$id",
            data: null, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> saveCollectPoint(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-gather-detail/saveDetail",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> saveHole(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/gather-hole-detail/saveHole",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> maxAgeDetail(
      {Object? gatherinfoId,
      Object? holeName,
      required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/gather-hole-detail/maxAgeDetail/$gatherinfoId/$holeName",
            data: null, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> uploadBybase64(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/file/uploadBase64", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> varietyDict(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leafage/index/varietyDict",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> greenhouse(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-massif-info/greenhouse/page",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> getland(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/leaf-massif-info/land/page",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

// 字典
  Future<RequestNoData> getDicts(
      {Object? data, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/client/dict/list/$data", data: null, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

// 生育期
  Future<RequestNoData> listSubDict(
      {Object? dictKey, Object? code, required CancelToken cancelToken}) {
    return leafAgeHttp
        .post("/client/dict/listSubDict/$dictKey/$code",
            data: null, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }
}
