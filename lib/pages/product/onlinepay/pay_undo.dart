import 'package:bdh_smart_agric_app/pages/product/onlinepay/payment_detail.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/request/online_pay_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

import '../../../components/jh_cascade_tree_picker.dart';
import '../../../model/org_tree_list_model.dart';
import '../../../utils/color_util.dart';

class UnPaymentPage extends StatefulWidget {
  final List<dynamic> selectYears;
  final OrgTreeResult? orgTree;

  const UnPaymentPage({super.key, required this.selectYears, this.orgTree});

  @override
  State<UnPaymentPage> createState() => _UnPaymentPageState();
}

class _UnPaymentPageState extends State<UnPaymentPage>
    with AutomaticKeepAliveClientMixin {
  final Color _themeColor = const Color.fromRGBO(0, 152, 91, 1);
  Map<dynamic, dynamic>? currentOrg;
  FixedExtentScrollController? yearSelectionController;
  int _currentYearIndex = -1;
  int _currentSelectedYearIndex = -1;
  String _currentYear = "";

  dynamic unPaySubjectSummaryRecord;

  List<dynamic> unPaymentList = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    var now = DateTime.now();
    _currentYear = now.year.toString();
    // getSelectYears();
    // loadOrg();
    loadUnPaySubjectSummary();
    bus.on("returnContractfun", (v) {
      setState(() {
        loadUnPaySubjectSummary();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Material(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 375.px,
            height: 285.px,
            padding: EdgeInsets.only(top: 40.px),
            decoration: const BoxDecoration(
                image: DecorationImage(
                    image: AssetImage("assets/images/pay/pay_head.png"),
                    fit: BoxFit.cover)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BackButton(
                  color: Colors.white,
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      margin: EdgeInsets.only(left: 25.px),
                      child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  "未支付(元)",
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 14.px,
                                      fontWeight: FontWeight.w400),
                                ),
                                GestureDetector(
                                  behavior: HitTestBehavior.opaque,
                                  onTap: () {
                                    showModalBottomSheet(
                                        context: context,
                                        elevation: 10,
                                        enableDrag: false,
                                        shape: RoundedRectangleBorder(
                                            borderRadius:
                                                BorderRadius.circular(8.px)),
                                        builder: (BuildContext context) {
                                          return showBottomSelectYearsPicker();
                                        });
                                  },
                                  child: Container(
                                    width: 62.px,
                                    height: 19.px,
                                    margin: EdgeInsets.only(left: 5.px),
                                    padding: EdgeInsets.only(
                                        left: 6.px, right: 2.px),
                                    decoration: BoxDecoration(
                                        color:
                                            const Color.fromRGBO(0, 152, 91, 1),
                                        borderRadius:
                                            BorderRadius.circular(16.px)),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(_currentYear,
                                            style: TextStyle(
                                                fontSize: 12.px,
                                                color: Colors.white)),
                                        Icon(
                                          Icons.arrow_drop_down,
                                          color: Colors.white,
                                          size: 16.px,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              margin: EdgeInsets.only(top: 12.px),
                              child: Text(
                                unPaySubjectSummaryRecord == null
                                    ? '0.00'
                                    : '${unPaySubjectSummaryRecord['unpayedAmountTotal']?.toStringAsFixed(2)}',
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 38.px,
                                    fontFamily: "bayon",
                                    letterSpacing: 1,
                                    fontWeight: FontWeight.bold),
                              ),
                            )
                          ]),
                    ),
                    Container(
                      height: 19.px,
                      margin: EdgeInsets.only(right: 23.px),
                      child: GestureDetector(
                        behavior: HitTestBehavior.opaque,
                        onTap: () {
                          showBottomMultiSelectPicker(context);
                        },
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                                currentOrg == null
                                    ? "请选择单位"
                                    : currentOrg?['orgName'],
                                style: TextStyle(
                                    fontSize: 12.px, color: Colors.white)),
                            Icon(
                              Icons.arrow_drop_down,
                              color: Colors.white,
                              size: 16.px,
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                Container(
                  margin: EdgeInsets.only(left: 12.px),
                  width: 351.px,
                  height: 107.px,
                  decoration: const BoxDecoration(
                      image: DecorationImage(
                          image:
                              AssetImage("assets/images/pay/pay_card_bg.png"),
                          fit: BoxFit.cover)),
                  child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "应支付(元)",
                              style: TextStyle(
                                  color: const Color.fromRGBO(44, 44, 52, 1),
                                  fontSize: 14.px),
                            ),
                            SizedBox(height: 8.px),
                            Text(
                              unPaySubjectSummaryRecord == null
                                  ? '0.00'
                                  : '${unPaySubjectSummaryRecord['planAmountTotal'].toStringAsFixed(2)}',
                              style: TextStyle(
                                  color: _themeColor,
                                  fontFamily: "bayon",
                                  letterSpacing: 1,
                                  fontSize: 28.px),
                            )
                          ],
                        ),
                        VerticalDivider(
                            width: 1.px,
                            color: const Color.fromRGBO(227, 229, 234, 1),
                            thickness: 1,
                            indent: 26.px,
                            endIndent: 27.5.px),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "已支付(元)",
                              style: TextStyle(
                                  color: const Color.fromRGBO(44, 44, 52, 1),
                                  fontSize: 14.px),
                            ),
                            SizedBox(height: 8.px),
                            Text(
                              unPaySubjectSummaryRecord == null
                                  ? '0.00'
                                  : '${unPaySubjectSummaryRecord['amountTotal'].toStringAsFixed(2)}',
                              style: TextStyle(
                                  color: _themeColor,
                                  fontFamily: "bayon",
                                  letterSpacing: 1,
                                  fontSize: 28.px),
                            )
                          ],
                        )
                      ]),
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(left: 12.px, top: 16.px, right: 12.px),
            child: Column(
              children: [
                Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: 10.px),
                        width: 4.px,
                        height: 16.px,
                        decoration: BoxDecoration(
                            color: _themeColor,
                            borderRadius:
                                BorderRadius.all(Radius.circular(4.px))),
                      ),
                      Text("待支付明细",
                          style: TextStyle(
                              color: const Color.fromRGBO(44, 44, 52, 1),
                              fontWeight: FontWeight.w500,
                              fontSize: 16.px)),
                    ]),
                SizedBox(height: 16.px),
                unPaymentList.isEmpty
                    ? SizedBox(
                        height: 200.px,
                        child: Center(
                          child: _isLoading
                              ? const SpinKitCircle(
                                  // color: HexColor('#16B760'),
                                  color: Color.fromRGBO(0, 127, 255, 1),
                                  size: 50.0,
                                )
                              : Text(
                                  "暂无数据",
                                  style: TextStyle(
                                      color: Colors.grey, fontSize: 16.px),
                                ),
                        ),
                      )
                    : SingleChildScrollView(
                        child: Column(
                          children: unPaymentList
                              .map((item) => GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () {
                                      Navigator.of(context).push(
                                          CupertinoPageRoute(builder: (ctx) {
                                        return PaymentDetailPage(
                                            year: _currentYear,
                                            payItemInfo: item);
                                      }));
                                    },
                                    child: _buildToBePayItem(
                                        item['organizationName'],
                                        "待支付合计",
                                        item['unpayedAmount']
                                            .toStringAsFixed(2)),
                                  ))
                              .toList(),
                        ),
                      )
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildToBePayItem(String title, String subTitle, String price) {
    return Container(
      width: 351.px,
      margin: EdgeInsets.only(bottom: 10.px),
      padding:
          EdgeInsets.only(left: 16.px, top: 18.px, right: 12.px, bottom: 19.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(6.px))),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                    fontSize: 16.px,
                    color: const Color.fromRGBO(44, 44, 52, 1)),
              ),
              SizedBox(height: 6.px),
              Text(
                subTitle,
                style: TextStyle(
                    fontSize: 12.px,
                    color: const Color.fromRGBO(124, 124, 128, 1)),
              )
            ],
          ),
          const Spacer(),
          Row(
            children: [
              Text(
                price,
                style: TextStyle(
                    fontFamily: "bayon",
                    letterSpacing: 1,
                    color: _themeColor,
                    fontSize: 24.px),
              ),
              SizedBox(width: 12.px),
              const Icon(
                Icons.arrow_forward_ios,
                color: Color.fromRGBO(196, 196, 196, 1.0),
                size: 16,
              )
            ],
          )
        ],
      ),
    );
  }

  Widget showBottomSelectYearsPicker() {
    if (_currentYearIndex == -1) {
      _currentYearIndex =
          widget.selectYears.indexWhere((item) => item["name"] == _currentYear);
      yearSelectionController =
          FixedExtentScrollController(initialItem: _currentYearIndex);
    } else {
      yearSelectionController =
          FixedExtentScrollController(initialItem: _currentYearIndex);
    }
    return BottomSheet(
        enableDrag: false,
        onClosing: () {},
        builder: (BuildContext context) {
          return Container(
              padding: EdgeInsets.only(left: 10.px, right: 10.px, top: 10.px),
              height: 280.px,
              width: 375.px,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "取消",
                              style: TextStyle(
                                  fontSize: 16.px, color: Colors.redAccent),
                            )),
                        Text(
                          "选择年份",
                          style: TextStyle(
                              color: const Color.fromRGBO(44, 44, 52, 1),
                              fontSize: 18.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextButton(
                            onPressed: () {
                              setState(() {
                                _currentYearIndex = _currentSelectedYearIndex;
                                _currentYear = widget
                                    .selectYears[_currentYearIndex]['name'];
                              });
                              loadUnPaySubjectSummary();
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "确定",
                              style: TextStyle(
                                  color: Colors.blueAccent, fontSize: 16.px),
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 220.px,
                      child: CupertinoPicker(
                          scrollController: yearSelectionController,
                          itemExtent: 40.px,
                          squeeze: 1.5,
                          diameterRatio: 1,
                          onSelectedItemChanged: (index) {
                            setState(() {
                              _currentSelectedYearIndex = index;
                            });
                          },
                          children: widget.selectYears
                              .map((item) => Center(
                                    child: Text(item['name']),
                                  ))
                              .toList()),
                    )
                  ]));
        });
  }

  showBottomMultiSelectPicker(BuildContext context) {
    var tempData = [];
    for (var e in widget.orgTree!.data!) {
      tempData.add(e.toJson());
    }
    JhCascadeTreePicker.show(context,
        data: tempData,
        valueKey: "orgCode",
        labelKey: "orgName",
        childrenKey: "list",
        clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
      setState(() {
        currentOrg = (ress as List).last;
        loadUnPaySubjectSummary();
      });
    });
  }

  void loadUnPaySubjectSummary() async {
    setState(() {
      _isLoading = true;
    });
    var params = {
      "yearNo": _currentYear,
      "organizationNo": currentOrg?["orgCode"],
    };
    OnlinePayResponsitory.queryUnpaySubjectSummary(params).then((res) {
      if (res.success!) {
        setState(() {
          _isLoading = false;
          unPaySubjectSummaryRecord = res.data;
          unPaymentList = unPaySubjectSummaryRecord['list'];
        });
      }
    }, onError: (err) {
      setState(() {
        _isLoading = false;
      });
    });
  }

  @override
  void didUpdateWidget(covariant UnPaymentPage oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.orgTree != widget.orgTree ||
        oldWidget.selectYears != widget.selectYears) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;
}
