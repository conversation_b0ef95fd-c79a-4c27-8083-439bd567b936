import 'package:bdh_smart_agric_app/model/dia_apply_filed_model.dart';
import 'package:bdh_smart_agric_app/model/dia_authent_model.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

//基本田认证详情
class DiaAuthentDetail extends StatefulWidget {
  final DiaAuthentRecords params;
  const DiaAuthentDetail({super.key, required this.params});

  @override
  State<DiaAuthentDetail> createState() => _DiaAuthentDetailState();
}

class _DiaAuthentDetailState extends State<DiaAuthentDetail> {
  // 步骤数据
  late List<TDStepsItemData> stepsData;
  //与户主关系
  List<DictNode> householderRelationDict = [];
  bool isLoading = false;
  int activeIndex = 0;
  String householderRelationName = '';
  @override
  void initState() {
    super.initState();
    stepsData = [
      TDStepsItemData(title: "提交申请", content: widget.params.createTime ?? ''),
      TDStepsItemData(
          title: '审核时间',
          content:
              (widget.params.auditAFlag == 1 ? widget.params.auditATime : '') ??
                  ''),
    ];
    activeIndex = widget.params.auditAFlag == 1 ? 1 : 0;
    loadDics();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: HexColor('#F5F5F5'),
        appBar: AppBar(
          title: const Text("基本田资格认证"),
        ),
        body: isLoading
            ? const ViewStateBusyWidget()
            : SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 15.px),
                child: Center(
                    child: Column(
                  children: [
                    SizedBox(height: 16.px),
                    Row(
                      children: [
                        Container(
                          width: 3.px,
                          height: 12.px,
                          decoration: BoxDecoration(
                            color: Color.fromRGBO(
                                50, 109, 255, 1.0), // 正确应用 RGBA 颜色
                            borderRadius:
                                BorderRadius.circular(2.px), // 可选：添加圆角
                          ),
                        ),
                        SizedBox(width: 8.px),
                        Text(
                             "第${widget.params.validateNum ?? '0'}次资格认证",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              fontFamily: 'PingFang SC',
                              color: Color.fromRGBO(41, 41, 52, 1)),
                        )
                      ],
                    ),
                    Container(
                      width: 347.px,
                      margin: EdgeInsets.only(top: 15.px),
                      padding: EdgeInsets.only(left: 15.px, right: 15.px),
                      decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(5))),
                      child: Column(
                        children: [
                          StaticDiaplay(
                              "选择年度", (widget.params.yearNo?.toString() ?? ''),
                              isDivider: true),
                          StaticDiaplay("姓名", widget.params.name ?? '',
                              isDivider: true),
                          StaticDiaplay(
                              "身份证号", maskIdCard(widget.params.idNumber ?? ''),
                              isDivider: true),
                          StaticDiaplay(
                              "资格所在单位", widget.params.organizationName ?? '',
                              isDivider: false),
                        ],
                      ),
                    ),
                    Container(
                      width: 347.px,
                      margin: EdgeInsets.only(top: 15.px),
                      padding: EdgeInsets.only(
                          left: 25.px, right: 25.px, top: 25.px, bottom: 25.px),
                      decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(5))),
                      child: TDSteps(
                        steps: stepsData,
                        direction: TDStepsDirection.vertical,
                        activeIndex: activeIndex,
                      ),
                    ),
                  ],
                )),
              ));
  }

  String maskIdCard(String idNumber) {
    if (idNumber.length < 14) return idNumber;
    return idNumber.substring(0, 6) +
        '*****' +
        idNumber.substring(idNumber.length - 4);
  }

  void loadDics() {
    setState(() {
      isLoading = true;
      householderRelationName = '';
    });
    Future.wait([
      BdhDigitalService.getDicByKey("relations"),
      // BdhDigitalService.getDicByKey("year_cd"),
    ]).then((list) {
      householderRelationDict = (list[0] as DictList).data ?? [];
      setState(() {
        isLoading = false;
        final matchedList = householderRelationDict.where((node) {
          final parsedCode = num.tryParse(node.code ?? '');

          return parsedCode == widget.params.householderRelation;
        }).toList();
        householderRelationName =
            matchedList.isNotEmpty ? matchedList[0].name ?? '' : '';
      });
    });
  }
}

//静态展示
Widget StaticDiaplay(String name, String? value, {required bool isDivider}) {
  return Column(children: [
    SizedBox(
      height: 50.px,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 标签部分（固定宽度）
            SizedBox(
              width: 100.px,
              child: Text(
                name,
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'PingFang SC',
                ),
              ),
            ),

            // 值部分（自动换行 + 最大宽度限制）
            SizedBox(
              width: 200.px, // 固定最大宽度（根据需求调整）
              child: Text(
                value ?? '',
                maxLines: null, // 允许无限换行
                overflow: TextOverflow.visible, // 不截断
                textAlign: TextAlign.right, // 右对齐
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'PingFang SC',
                ),
              ),
            ),

            SizedBox(width: 8.px),
          ],
        ),
      ),
    ),

    // 分隔线
    if (isDivider)
      Divider(
        height: 0.5.px,
        color: const Color.fromRGBO(239, 241, 245, 1),
      ),
  ]);
}
