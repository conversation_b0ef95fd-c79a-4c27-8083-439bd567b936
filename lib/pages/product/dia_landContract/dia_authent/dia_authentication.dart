import 'dart:convert';
import 'dart:math';

import 'package:bdh_smart_agric_app/model/dia_apply_filed_model.dart';
import 'package:bdh_smart_agric_app/model/dia_authent_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/underline_indicator.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/dia_apply/dia_apply_detail.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/dia_apply_success.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/dia_authent/dia_authent_detail.dart';
import 'package:bdh_smart_agric_app/pages/product/landcontract/baseland/bdh_base_land_video_record.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_land.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/utils/request/land_intradomain_sign_service.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:flutter/services.dart';
import 'package:oktoast/oktoast.dart';
import 'dart:async';

import 'package:tdesign_flutter/tdesign_flutter.dart';

// 数字北大荒 - 基本田资格认证
class DiaAuthentication extends StatefulWidget {
  const DiaAuthentication({super.key});

  @override
  State<DiaAuthentication> createState() => _DiaAuthenticationState();
}

class _DiaAuthenticationState extends State<DiaAuthentication>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String yearNo = DateTime.now().year.toString(); // 默认选择当前年份
  int _selectedIndex = -1; // 选中的项目索引，-1表示未选择
  List<DictNode> yearDics = []; // 年度字典
  List<DictNode> auditAFlagDics = []; // 审核状态字典
  bool isLoading = false;

  // 分页相关变量
  int _unauditedCurrentPage = 1; // 未审核当前页码
  int _auditedCurrentPage = 1; // 已审核当前页码
  final int _pageSize = 10; // 每页数据条数
  bool _unauditedHasMore = true; // 未审核是否有更多数据
  bool _auditedHasMore = true; // 已审核是否有更多数据
  bool _isUnauditedLoading = false; // 未审核数据加载状态
  bool _isAuditedLoading = false; // 已审核数据加载状态

  // 滚动控制器
  final ScrollController _unauditedScrollController = ScrollController();
  final ScrollController _auditedScrollController = ScrollController();

  // 添加退回原因的控制器
  final TextEditingController _rejectReasonController = TextEditingController();

  // 搜索控制器
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // 搜索关键词
  String farmerNameOrCard = '';
  Timer? _debounce;

  // 未审核数据
  List<DiaAuthentRecords> _unauditedData = [];

  // 已审核数据
  List<DiaAuthentRecords> _auditedData = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 添加滚动监听器
    _unauditedScrollController.addListener(_unauditedScrollListener);
    _auditedScrollController.addListener(_auditedScrollListener);

    // 添加Tab切换监听
    _tabController.addListener(_handleTabChange);
    _initData();
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _rejectReasonController.dispose();
    _unauditedScrollController.removeListener(_unauditedScrollListener);
    _auditedScrollController.removeListener(_auditedScrollListener);
    _unauditedScrollController.dispose();
    _auditedScrollController.dispose();
    _debounce?.cancel();
    TDToast.dismissLoading();
    super.dispose();
  }

  // Tab切换处理
  void _handleTabChange() {
    if (!_tabController.indexIsChanging) {
      FocusManager.instance.primaryFocus?.unfocus();
      setState(() {
        farmerNameOrCard = '';
        _searchController.clear();
        _resetAndRefetch();
        // if (_tabController.indexIsChanging) {
        //   // 切换到未审核Tab
        //   if (_tabController.index == 0 && _unauditedData.isEmpty) {
        //     _fetchData(refresh: true, type: 'noApply');
        //   }
        //   // 切换到已审核Tab
        //   else if (_tabController.index == 1 && _auditedData.isEmpty) {
        //     _fetchData(refresh: true, type: 'applyFor');
        //   }
        // }
      });
    }
  }

  // 未审核数据滚动监听
  void _unauditedScrollListener() {
    if (_unauditedScrollController.position.pixels >=
            _unauditedScrollController.position.maxScrollExtent - 50 &&
        !_isUnauditedLoading &&
        _unauditedHasMore) {
      _fetchData(type: 'noApply');
    }
  }

  // 已审核数据滚动监听
  void _auditedScrollListener() {
    if (_auditedScrollController.position.pixels >=
            _auditedScrollController.position.maxScrollExtent - 50 &&
        !_isAuditedLoading &&
        _auditedHasMore) {
      _fetchData(type: 'applyFor');
    }
  }

  // 初始化数据
  void _initData() async {
    setState(() {
      isLoading = true;
    });

    try {
      // 获取年度字典
      Future.wait([
        BdhDigitalService.getDicByKey("year_cd"),
        BdhDigitalService.getDicByKey("aproval_status_no"),
      ]).then((list) {
        print(list[1]);
        if (list[0] != null && list[0].data != null) {
          yearDics = list[0].data!;
          if (yearDics.isNotEmpty) {
            // 设置默认选中当前年份
            String currentYear = DateTime.now().year.toString();
            DictNode? currentYearNode = yearDics.firstWhere(
              (node) => node.code == currentYear,
              orElse: () => yearDics.first,
            );
            yearNo = currentYearNode.code ?? currentYear;
          }
        }
        auditAFlagDics = list[1].data!;
        _fetchData(refresh: true, type: 'noApply');
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      print('初始化数据失败: $e');
    }
  }

// 获取未审核数据 和已审核数据
  Future<void> _fetchData({bool refresh = false, type = 'noApply'}) async {
    if (type == 'noApply') {
      if (_isUnauditedLoading) return;

      setState(() {
        _isUnauditedLoading = true;
      });

      if (refresh) {
        _unauditedData = [];
        _unauditedCurrentPage = 1;
        _unauditedHasMore = true;
      }
    } else {
      if (_isAuditedLoading) return;

      setState(() {
        _isAuditedLoading = true;
      });

      if (refresh) {
        _auditedData = [];
        _auditedCurrentPage = 1;
        _auditedHasMore = true;
      }
    }
    try {
      var response = await BdhDigitalLand.appSzValidateList({
        "yearNo": yearNo,
        "farmerNameOrCard": farmerNameOrCard,
        "page": type == 'noApply' ? _unauditedCurrentPage : _auditedCurrentPage,
        "rows": _pageSize,
        "auditAFlag": type == 'noApply' ? 0 : 1,
      });
      if (response.code != 0) {
        setState(() {
          if (type == 'noApply') {
            _isUnauditedLoading = false;
          } else {
            _isAuditedLoading = false;
          }
        });
        showToast(response.msg ?? '');
        return;
      }
      List<DiaAuthentRecords>? newData = response.data?.records;
      if (newData != null && newData.isNotEmpty) {
        for (var record in newData) {
          if (record.auditAFlag != null) {
            for (var dict in auditAFlagDics) {
              if (dict.code == record.auditAFlag.toString()) {
                record.auditAFlagName = dict.name;
                break;
              }
            }
          }
        }
      }

      setState(() {
        if (type == 'noApply') {
          if (refresh) {
            _unauditedData = newData!;
          } else {
            _unauditedData.addAll(newData!);
          }
          _unauditedCurrentPage++;
          _unauditedHasMore =
              _unauditedCurrentPage <= (response.data?.pages ?? 0);

          _isUnauditedLoading = false;
        } else {
          if (refresh) {
            _auditedData = newData!;
          } else {
            _auditedData.addAll(newData!);
          }
          _auditedCurrentPage++;
          _auditedHasMore = _auditedCurrentPage <= (response.data?.pages ?? 0);
          _isAuditedLoading = false;
        }
      });
    } catch (e) {
      setState(() {
        if (type == 'noApply') {
          _isUnauditedLoading = false;
        } else {
          _isAuditedLoading = false;
        }
      });
    }
  }

  //手动触发搜索
  void _performSearch() {
    String value = _searchController.text.trim();
    if (farmerNameOrCard != value) {
      setState(() {
        farmerNameOrCard = value;
        // 重置页码和数据
        _resetAndRefetch();
      });
    } else {
      _resetAndRefetch();
    }
  }

  // 重置页码和数据并重新获取
  void _resetAndRefetch() {
    _selectedIndex = -1;
    setState(() {
      isLoading = true;
    });
    // 重置滚动位置到顶部
    if (_tabController.index == 0) {
      if (_unauditedScrollController.hasClients) {
        _unauditedScrollController.animateTo(
          0,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
      _fetchData(refresh: true, type: 'noApply').then((_) {
        // 数据加载完成后，重置 loading 状态
        setState(() {
          isLoading = false;
        });
      }).catchError((error) {
        // 处理错误
        isLoading = false;
      });
    } else {
      if (_auditedScrollController.hasClients) {
        _auditedScrollController.animateTo(
          0,
          duration: Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
      _fetchData(refresh: true, type: 'applyFor').then((_) {
        // 数据加载完成后，重置 loading 状态
        setState(() {
          isLoading = false;
        });
      }).catchError((error) {
        // 处理错误
        isLoading = false;
      });
    }
  }

  // 构建搜索区域
  Widget _buildSearchSection() {
    return Container(
      color: HexColor('#F5F5F5'),
      padding: EdgeInsets.all(15.px),
      child: Container(
        height: 40.px,
        decoration: BoxDecoration(
          color: HexColor('#FFFFFF'),
          borderRadius: BorderRadius.circular(6.px), // 修改圆角为 6px
        ),
        child: CupertinoTextField.borderless(
          controller: _searchController,
          focusNode: _searchFocusNode,
          placeholder: "请输入姓名/身份证号",
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[\u4e00-\u9fa50-9xX]')),
          ],
          style: TextStyle(
            fontSize: 14.px,
            color: HexColor('#333333'),
          ),
          placeholderStyle: TextStyle(
            fontSize: 14.px,
            color: HexColor('#999999'),
          ),
          padding: EdgeInsets.symmetric(horizontal: 12.px),
          textInputAction: TextInputAction.search,
          onEditingComplete: () {
            _performSearch();
            FocusScope.of(context).unfocus(); // 收起键盘
          },
          suffix: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 搜索图标放在这里，并绑定点击事件
              GestureDetector(
                onTap: () {
                  _performSearch();
                  FocusScope.of(context).unfocus(); // 收起键盘
                },
                child: Icon(
                  Icons.search,
                  color: HexColor('#999999'),
                  size: 20.px,
                ),
              ),
              SizedBox(width: 8.px),
              // 原来的清除按钮
              if (_searchController.text.isNotEmpty)
                GestureDetector(
                  onTap: () {
                    farmerNameOrCard = '';
                    _searchController.clear();
                    _performSearch();
                  },
                  child: Icon(
                    Icons.clear,
                    color: HexColor('#999999'),
                    size: 20.px,
                  ),
                ),
            ],
          ),
          // 去掉 prefix 中的搜索图标
        ),
      ),
    );
  }

  // 构建TabBar
  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        dividerColor: Colors.transparent,
        labelColor: Color.fromRGBO(42, 108, 255, 1),
        unselectedLabelColor: Color.fromRGBO(41, 41, 52, 1),
        indicatorColor: Colors.transparent,
        indicator: TCUnderlineTabIndicator(
          borderSide: BorderSide(
            width: 3.px,
            color: Color.fromRGBO(42, 108, 255, 1),
          ),
          insets: EdgeInsets.zero,
          indicatorWidth: 20.px,
          isRound: true,
        ),
        labelStyle: TextStyle(
          fontSize: 15.px,
          fontWeight: FontWeight.w600,
          fontFamily: 'PingFang SC',
        ),
        unselectedLabelStyle: TextStyle(
          fontSize: 15.px,
          fontWeight: FontWeight.w400,
          fontFamily: 'PingFang SC',
        ),
        tabs: [
          Tab(text: '未审核'),
          Tab(text: '已审核'),
        ],
      ),
    );
  }

  // 构建未审核列表
  Widget _buildUnauditedList() {
    return Container(
      color: HexColor('#F5F5F5'),
      child: _unauditedData.isEmpty && !_isUnauditedLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.inbox_outlined,
                    size: 64.px,
                    color: HexColor('#CCCCCC'),
                  ),
                  SizedBox(height: 16.px),
                  Text(
                    '暂无未审核数据',
                    style: TextStyle(
                      fontSize: 16.px,
                      color: HexColor('#999999'),
                    ),
                  ),
                ],
              ),
            )
          : Stack(
              children: [
                ListView.builder(
                  controller: _unauditedScrollController,
                  padding: EdgeInsets.all(16.px)
                      .copyWith(bottom: 100.px), // 底部留出空间给按钮
                  itemCount:
                      _unauditedData.length + (_unauditedHasMore ? 1 : 0),
                  itemBuilder: (context, index) {
                    if (index == _unauditedData.length) {
                      return _buildLoadingIndicator();
                    }
                    return _buildUnauditedItem(_unauditedData[index], index);
                  },
                ),
                // 底部按钮
                if (_selectedIndex != -1)
                  Positioned(
                      left: 0.px,
                      right: 0.px,
                      bottom: 0.px,
                      child: Container(
                        padding: EdgeInsets.all(16.px),
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        child: Row(
                          children: [
                            // Expanded(
                            //   child: OutlinedButton(
                            //     onPressed: () {
                            //       // 退回操作
                            //       _showRejectDialog();
                            //     },
                            //     style: OutlinedButton.styleFrom(
                            //       side: BorderSide(color: HexColor('#1976D2')),
                            //       shape: RoundedRectangleBorder(
                            //         borderRadius: BorderRadius.circular(4.px),
                            //       ),
                            //       padding: EdgeInsets.symmetric(vertical: 12.px),
                            //       backgroundColor: HexColor('#FFFFFF'),
                            //     ),
                            //     child: Text(
                            //       '退回',
                            //       style: TextStyle(
                            //         fontSize: 16.px,
                            //         color: HexColor('#1976D2'),
                            //       ),
                            //     ),
                            //   ),
                            // ),
                            // SizedBox(width: 12.px),
                            Expanded(
                              child: ElevatedButton(
                                onPressed: () {
                                  // 审核操作
                                  operate('toExamine');
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: HexColor('#1976D2'),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(4.px),
                                  ),
                                  padding:
                                      EdgeInsets.symmetric(vertical: 12.px),
                                ),
                                child: Text(
                                  '审核',
                                  style: TextStyle(
                                    fontSize: 16.px,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      )),
              ],
            ),
    );
  }

  // 构建已审核列表
  Widget _buildAuditedList() {
    return Container(
      color: HexColor('#F5F5F5'),
      child: _auditedData.isEmpty && !_isAuditedLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.inbox_outlined,
                    size: 64.px,
                    color: HexColor('#CCCCCC'),
                  ),
                  SizedBox(height: 16.px),
                  Text(
                    '暂无已审核数据',
                    style: TextStyle(
                      fontSize: 16.px,
                      color: HexColor('#999999'),
                    ),
                  ),
                ],
              ),
            )
          : ListView.builder(
              controller: _auditedScrollController,
              padding: EdgeInsets.all(16.px),
              itemCount: _auditedData.length + (_auditedHasMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == _auditedData.length) {
                  return _buildLoadingIndicator();
                }
                return _buildAuditedItem(_auditedData[index]);
              },
            ),
    );
  }

  // 构建加载指示器
  Widget _buildLoadingIndicator() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.px),
      alignment: Alignment.center,
      child: CircularProgressIndicator(),
    );
  }

  // 构建未审核列表项
  Widget _buildUnauditedItem(DiaAuthentRecords item, int index) {
    bool isSelected = _selectedIndex == index;

    return Container(
      margin: EdgeInsets.only(bottom: 10.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.px),
      ),
      child: InkWell(
          onTap: () {
            setState(() {
              _selectedIndex = isSelected ? -1 : index;
              FocusScope.of(context).unfocus();
            });
          },
          borderRadius: BorderRadius.circular(8.px),
          child: Stack(children: [
            Positioned(
              left: 0,
              top: 0,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8.px, vertical: 4.px),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(42, 108, 255, 0.15),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8.px),
                    bottomRight: Radius.circular(3.px),
                  ),
                ),
                child: Text(
                  '第${item.validateNum}次资格认证',
                  style: TextStyle(
                    fontSize: 10.px,
                    fontFamily: 'PingFang SC',
                    color: Color.fromRGBO(42, 108, 255, 1),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.all(16.px),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 12.px),
                  Row(
                    children: [
                      Container(
                        width: 20.px,
                        height: 20.px,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected
                                ? HexColor('#1976D2')
                                : HexColor('#CCCCCC'),
                            width: 2.px,
                          ),
                          color: isSelected
                              ? HexColor('#1976D2')
                              : Colors.transparent,
                        ),
                        child: isSelected
                            ? Icon(
                                Icons.check,
                                size: 14.px,
                                color: Colors.white,
                              )
                            : null,
                      ),
                      SizedBox(width: 12.px),
                      Text(
                        item.name ?? '',
                        style: TextStyle(
                          fontSize: 16.px,
                          fontWeight: FontWeight.w400,
                          color: Color.fromRGBO(41, 41, 52, 1),
                          fontFamily: 'PingFang SC',
                        ),
                      ),
                      SizedBox(width: 10.px),
                      Text('|',
                          style: TextStyle(
                              fontSize: 16.px,
                              color: Color.fromRGBO(219, 219, 221, 1))),
                      SizedBox(width: 10.px),
                      Text(
                        maskIdCard(item.idNumber ?? ''),
                        style: TextStyle(
                          fontSize: 16.px,
                          fontWeight: FontWeight.w400,
                          color: Color.fromRGBO(41, 41, 52, 1),
                          fontFamily: 'PingFang SC',
                        ),
                      ),
                      Spacer(),
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.px, vertical: 4.px),
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(255, 80, 0, 0.1),
                          borderRadius: BorderRadius.circular(3.px),
                        ),
                        child: Text(
                          item.auditAFlagName ?? '未审核',
                          style: TextStyle(
                            fontSize: 12.px,
                            color: Color.fromRGBO(255, 80, 0, 1),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 6.px),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.organizationName ?? '',
                        style: TextStyle(
                          fontSize: 14.px,
                          color: HexColor('#666666'),
                        ),
                      ),
                      SizedBox(height: 6.px),
                      Text(
                        item.createTime?.split(' ').first ?? '',
                        style: TextStyle(
                          fontSize: 14.px,
                          color: HexColor('#999999'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ])),
    );
  }

  // 构建已审核列表项（未来用）
  Widget _buildAuditedItem(DiaAuthentRecords item) {
    return InkWell(
      onTap: () {
        setState(() {
          FocusManager.instance.primaryFocus?.unfocus();
          Navigator.of(context)
              .push(CupertinoPageRoute(builder: (BuildContext context) {
            return DiaAuthentDetail(params: item);
          }));
        });
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 10.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8.px),
        ),
        child: Stack(
          children: [
            // 第二次资格认证文本，放在Stack顶层，紧贴左上角
            Positioned(
              left: 0,
              top: 0,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 8.px, vertical: 4.px),
                decoration: BoxDecoration(
                  color: Color.fromRGBO(42, 108, 255, 0.15),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8.px),
                    bottomRight: Radius.circular(3.px),
                  ),
                ),
                child: Text(
                  '第${item.validateNum}次资格认证',
                  style: TextStyle(
                    fontSize: 10.px,
                    fontFamily: 'PingFang SC',
                    color: Color.fromRGBO(42, 108, 255, 1),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            // 主要内容
            Padding(
              padding: EdgeInsets.only(
                  left: 16.px, right: 16.px, bottom: 16.px, top: 30.px),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        item.name ?? '',
                        style: TextStyle(
                          fontSize: 16.px,
                          fontWeight: FontWeight.w600,
                          color: HexColor('#333333'),
                        ),
                      ),
                      SizedBox(width: 10.px),
                      Text('|',
                          style: TextStyle(
                              fontSize: 16.px,
                              color: Color.fromRGBO(219, 219, 221, 1))),
                      SizedBox(width: 10.px),
                      Text(
                        maskIdCard(item.idNumber ?? ''),
                        style: TextStyle(
                          fontSize: 14.px,
                          color: HexColor('#666666'),
                        ),
                      ),
                      Spacer(),
                      Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 8.px, vertical: 4.px),
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(50, 109, 255, 0.1),
                          borderRadius: BorderRadius.circular(3.px),
                        ),
                        child: Text(
                          item.auditAFlagName ?? '已审核',
                          style: TextStyle(
                              fontSize: 12.px,
                              color: Color.fromRGBO(50, 109, 255, 1),
                              fontWeight: FontWeight.w500,
                              fontFamily: 'PingFang SC'),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 6.px),
                  Column(
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          item.organizationName ?? '',
                          style: TextStyle(
                            fontSize: 14.px,
                            color: HexColor('#666666'),
                          ),
                        ),
                      ),
                      SizedBox(height: 6.px),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            '认证日期: ${item.createTime?.split(' ').first ?? ''}',
                            style: TextStyle(
                              fontSize: 13.px,
                              color: HexColor('#999999'),
                            ),
                          ),
                          Text(
                            '审核日期: ${item.auditATime?.split(' ').first ?? ''}',
                            style: TextStyle(
                              fontSize: 13.px,
                              color: HexColor('#999999'),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  // 根据当前项的isExpanded状态显示或隐藏详细信息
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: HexColor('#F5F5F5'),
      appBar: AppBar(
        title: Text(
          '基本田资格认证',
          style: TextStyle(
            fontSize: 18.px,
            fontWeight: FontWeight.w600,
            color: Colors.black,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        foregroundColor: Colors.black,
        actions: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.px),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                GestureDetector(
                    onTap: () {
                      FocusManager.instance.primaryFocus?.unfocus();
                      // 显示年份选择器
                      showModalBottomSheet(
                        context: context,
                        builder: (context) {
                          return Container(
                            height: 300.px,
                            child: Column(
                              children: [
                                Container(
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 16.px, vertical: 12.px),
                                  decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                      color: HexColor('#EEEEEE'),
                                    )),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      TextButton(
                                        onPressed: () {
                                          Navigator.pop(context);
                                        },
                                        child: Text('取消'),
                                      ),
                                      Expanded(
                                          child: Center(
                                        child: Text(
                                          '选择年份',
                                          style: TextStyle(
                                            fontSize: 16.px,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      )),
                                      Opacity(
                                        opacity: 0,
                                        child: TextButton(
                                          onPressed: null,
                                          child: Text(''), // 这个不会显示
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Expanded(
                                  child: ListView.builder(
                                    itemCount: yearDics.length,
                                    itemBuilder: (context, index) {
                                      final year = yearDics[index];
                                      final isSelected = year.code == yearNo;
                                      return ListTile(
                                        title: Center(
                                          child: Text(
                                            year.name ?? '',
                                            style: TextStyle(
                                              color: isSelected
                                                  ? HexColor('#1976D2')
                                                  : HexColor('#333333'),
                                              fontWeight: isSelected
                                                  ? FontWeight.w600
                                                  : FontWeight.normal,
                                            ),
                                          ),
                                        ),
                                        onTap: () {
                                          setState(() {
                                            yearNo = year.code!;
                                            FocusScope.of(context).unfocus();
                                          });
                                          Navigator.pop(context);
                                          // 年份变更后重置页码和数据
                                          _resetAndRefetch();
                                        },
                                      );
                                    },
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                    child: Row(
                      children: [
                        Text(
                          '${yearNo}年',
                          style: TextStyle(
                              fontSize: 16.px,
                              color: Colors.black,
                              fontFamily: 'PingFang SC'),
                        ),
                        SizedBox(width: 4.px),
                        Icon(
                          Icons.arrow_drop_down,
                          color: Colors.black,
                        ),
                      ],
                    )),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab栏
          _buildTabBar(),
          // 搜索框放在Tab栏下面
          _buildSearchSection(),
          // Tab内容
          isLoading
              ? const Center(child: CircularProgressIndicator())
              : Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    physics: const NeverScrollableScrollPhysics(),
                    children: [
                      _buildUnauditedList(),
                      _buildAuditedList(),
                    ],
                  ),
                ),
        ],
      ),
    );
  }

  String maskIdCard(String idNumber) {
    if (idNumber.length < 14) return idNumber;
    return idNumber.substring(0, 6) +
        '*****' +
        idNumber.substring(idNumber.length - 4);
  }

  //后续操作
  // ... existing code ...
  void operate(String type) {
    FocusManager.instance.primaryFocus?.unfocus();
    final item = _unauditedData[_selectedIndex];
    var name = item.name;
    BrnDialogManager.showConfirmDialog(context,
        barrierDismissible: false, //让弹框不关闭
        title: "提示",
        cancel: '取消',
        confirm: '确定',
        message: "是否进行农户刷脸操作", onConfirm: () async {
      Navigator.of(context).pop();
      TDToast.showLoadingWithoutText(context: context, preventTap: true);
      try {
        final Map<String, dynamic> paramsItem = {
          ...item.toJson(), // 假设 item 有 toJson() 方法
          "appszFaceUserType": 1
        };
        final res =
            await BdhDigitalLand.appSzStartRationPlanValidate(paramsItem);
        if (res.success == false) {
          TDToast.dismissLoading();
          showToast(res.msg ?? '操作失败', duration: Duration(seconds: 3));
          return;
        } else {
          var responseData = res.data as Map<String, dynamic>;
          var param = {
            "yearNo": item.yearNo,
            "rationServiceType": 4,
            ...responseData
          };
          final faceRes = await NativeUtil.openTxFace(param);
          var faceJson = jsonDecode(faceRes);
          if (faceJson["code"] == "0") {
            //判断是否需要管理人员刷脸
            final configRes = await BdhDigitalLand.getRationVerifyConfig(item);
            if (configRes.success == false) {
              showToast(configRes.msg ?? '操作失败',
                  duration: Duration(seconds: 3));
              TDToast.dismissLoading();
              return;
            } else {
              if (configRes.data['appszAuthAdminFaceFlag'] == 1) {
                //走农户保存
                var faceParams = {
                  "rationServiceType": 4,
                  "lcRationValidateId": item.lcRationValidateId,
                  "yearNo": item.yearNo,
                  "farmerIdNumber": item.idNumber,
                  "farmerName": item.name,
                  "organizationNo": item.organizationNo,
                  "organizationName": item.organizationName,
                  "appszFaceUserType": 1, //1农户 2 管理员
                  ...responseData,
                };
                final addRes =
                    await BdhDigitalLand.appSzSaveRationFaceDetailValidate(
                        faceParams);
                if (addRes.success == false) {
                  showToast(addRes.msg ?? '操作失败',
                      duration: Duration(seconds: 3));
                  TDToast.dismissLoading();
                  return;
                } else {
                  TDToast.dismissLoading();
                  //管理人员刷脸
                  adminfun(item, name);
                }
              } else {
                //农户保存
                var faceParams = {
                  "rationServiceType": 4,
                  "lcRationValidateId": item.lcRationValidateId,
                  "yearNo": item.yearNo,
                  "farmerIdNumber": item.idNumber,
                  "farmerName": item.name,
                  "organizationNo": item.organizationNo,
                  "organizationName": item.organizationName,
                  "appszFaceUserType": 1,
                  "appszFaceFinishStatus": 1, //最终状态 1
                  ...responseData,
                };
                final addRes =
                    await BdhDigitalLand.appSzSaveRationFaceDetailValidate(
                        faceParams);
                if (addRes.success == false) {
                  showToast(addRes.msg ?? '操作失败',
                      duration: Duration(seconds: 3));
                  TDToast.dismissLoading();
                  return;
                } else {
                  //跳转
                  TDToast.dismissLoading();
                  _searchController.clear();
                  farmerNameOrCard = '';
                  _selectedIndex = -1;
                  _rejectReasonController.clear();
                  _performSearch();
                  Map<String, dynamic> routeParam = {
                    "name": name,
                    "type": '基本田资格认证',
                  };
                  Navigator.of(context)
                      .push(CupertinoPageRoute(builder: (BuildContext context) {
                    return DiaApplySuccess(
                      params: routeParam,
                    );
                  }));
                }
              }
            }
          } else if (faceJson["code"] == "1") {
            TDToast.dismissLoading();
            showToast(faceJson["msg"], duration: Duration(seconds: 3));
          }
        }
      } catch (e) {
        TDToast.dismissLoading(); // 捕获异常时关闭 loading
        showToast('加载数据失败，请稍后重试', duration: Duration(seconds: 3)); // 统一错误提示
      }
    }, onCancel: () {
      Navigator.of(context).pop();
    });
  }

//管理员审核
  void adminfun(DiaAuthentRecords item, String? name) {
    BrnDialogManager.showConfirmDialog(context,
        barrierDismissible: false, //让弹框不关闭
        title: "提示",
        cancel: '取消',
        confirm: '确定',
        message: "是否进行管理员刷脸操作", onConfirm: () async {
      Navigator.of(context).pop();
      TDToast.showLoadingWithoutText(context: context, preventTap: true);
      try {
        final Map<String, dynamic> paramsItemAdmin = {
          ...item.toJson(),
          "appszFaceUserType": 2,
        };
        final res =
            await BdhDigitalLand.appSzStartRationPlanValidate(paramsItemAdmin);
        if (res.success == false) {
          TDToast.dismissLoading();
          showToast(res.msg ?? '操作失败', duration: Duration(seconds: 3));
          return;
        } else {
          var responseData = res.data as Map<String, dynamic>;
          var param = {
            "yearNo": item.yearNo,
            "rationServiceType": 4,
            ...responseData
          };
          final faceRes = await NativeUtil.openTxFace(param);
          var faceJson = jsonDecode(faceRes);
          if (faceJson["code"] == "0") {
            var faceParams = {
              "rationServiceType": 4,
              "lcRationValidateId": item.lcRationValidateId,
              "yearNo": item.yearNo,
              "farmerIdNumber": item.idNumber,
              "farmerName": item.name,
              "organizationNo": item.organizationNo,
              "organizationName": item.organizationName,
              "appszFaceUserType": 2,
              "appszFaceFinishStatus": 1,
              ...responseData,
            };
            final addRes =
                await BdhDigitalLand.appSzSaveRationFaceDetailValidate(
                    faceParams);
            if (addRes.success == false) {
              showToast(addRes.msg ?? '操作失败', duration: Duration(seconds: 3));
              TDToast.dismissLoading();
              return;
            } else {
              TDToast.dismissLoading();
              _searchController.clear();
              farmerNameOrCard = '';
              _selectedIndex = -1;
              _rejectReasonController.clear();
              _performSearch();
              Map<String, dynamic> routeParam = {
                "name": name,
                "type": '基本田资格认证',
              };
              Navigator.of(context)
                  .push(CupertinoPageRoute(builder: (BuildContext context) {
                return DiaApplySuccess(
                  params: routeParam,
                );
              }));
            }
          } else if (faceJson["code"] == "1") {
            TDToast.dismissLoading();
            showToast(faceJson["msg"], duration: Duration(seconds: 3));
          }
        }
      } catch (e) {
        TDToast.dismissLoading(); // 捕获异常时关闭 loading
        showToast('加载数据失败，请稍后重试', duration: Duration(seconds: 3)); // 统一错误提示
      }
    }, onCancel: () {
      Navigator.of(context).pop();
    });
  }
}
