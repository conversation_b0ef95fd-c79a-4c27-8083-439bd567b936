import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_land.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

import 'package:flutter_svg/svg.dart';
import 'package:oktoast/oktoast.dart';

class DiaLandContract extends StatefulWidget {
  const DiaLandContract({super.key});

  @override
  State<DiaLandContract> createState() => _DiaLandContractState();
}

class _DiaLandContractState extends State<DiaLandContract> {
  bool isLoading = false;
  List<MenuConfigItem>? menuConfigItem;

  void initState() {
    super.initState();
    getData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Positioned.fill(
            child: Align(
              alignment: Alignment.topCenter, // 设置顶部对齐
              child: Image.asset(
                'assets/images/diaLand/wavebac.png',
                fit: BoxFit.cover, // 图片按高度缩放，保持顶部对齐
                width: MediaQuery.of(context).size.width,
                height: 323.px,
              ),
            ),
          ),
          // 内容区
          SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部自定义AppBar
                Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    children: [
                      IconButton(
                        icon: const Icon(Icons.arrow_back_ios,
                            color: Colors.white),
                        onPressed: () => Navigator.of(context).pop(),
                      ),
                      const Expanded(
                        child: Center(
                          child: Text(
                            '土地承包',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 40), // 占位，保持标题居中
                    ],
                  ),
                ),
                SizedBox(height: 16.px),
                // 大标题和副标题
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 24.px),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '土地承包',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 28.px,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'PingFang SC',
                        ),
                      ),
                      SizedBox(height: 8.px),
                      Text(
                        '客户为中心，创新为核心',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 16.px,
                          fontFamily: 'PingFang SC',
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 32.px),
                // 卡片区
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.px),
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16.px),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.08),
                          blurRadius: 16,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '我的常用功能',
                            style: TextStyle(
                              fontSize: 16.px,
                              fontWeight: FontWeight.bold,
                              color: Color.fromRGBO(0, 0, 0, 0.85),
                              fontFamily: 'PingFang SC',
                            ),
                          ),
                          const SizedBox(height: 16),
                          // 功能按钮区
                          isLoading
                              ? const ViewStateBusyWidget()
                              : GridView.count(
                                  crossAxisCount: 4,
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  mainAxisSpacing: 0,
                                  crossAxisSpacing: 0,
                                  childAspectRatio: 0.85,
                                  children: [
                                    ...(menuConfigItem?.map((item) => _FuncItem(
                                              authCode: item.authCode ?? '',
                                              icon: item.icon ?? "",
                                              label: item.authName ?? "",
                                              url: item.url ?? "",
                                            )) ??
                                        []),
                                  ],
                                ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void getData() {
    setState(() {
      isLoading = true;
    });
    BDHResponsitory.getSubMenuConfig("diaLandContract").then((res) {
      if ((res.data ?? []).isNotEmpty) {
        final List<MenuConfigItem> children =
            List.from(res.data?.first.children ?? []);
        children.sort((a, b) => (a.orderNum ?? 0).compareTo(b.orderNum ?? 0));
        children.add(MenuConfigItem(
          authCode: 'chargeInfo',
          authName: '签字',
          icon: 'chargeInfo',
          url: 'chargeInfo',
          orderNum: 999,
        ));
        children.add(MenuConfigItem(
          authCode: 'chargeInfoPhoto',
          authName: '拍照',
          icon: 'chargeInfoPhoto',
          url: 'chargeInfoPhoto',
          orderNum: 999,
        ));
        children.add(MenuConfigItem(
          authCode: 'examine',
          authName: '预收承包费计划',
          icon: 'examine',
          url: 'examine',
          orderNum: 999,
        ));

        setState(() {
          menuConfigItem = children;
          isLoading = false;
        });
      } else {
        setState(() {
          menuConfigItem = [];
          isLoading = false;
        });
      }
    });
  }
}

// 功能按钮组件
class _FuncItem extends StatelessWidget {
  final String icon;
  final String label;
  final String url;
  final String authCode;
  const _FuncItem(
      {required this.icon,
      required this.label,
      required this.url,
      required this.authCode});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (authCode == 'chargeInfo' || authCode == 'chargeInfoPhoto') {
          //签字 //拍照
          BdhDigitalLand.secondaryMenu({}).then((res) {
            if (res.success == true) {
              Navigator.of(context).pushNamed(url, arguments: res.data);
              // NativeUtil.openUni({"path": url});
            } else {
              showToast(res.msg ?? '请先在pc端发送请求');
            }
          });
        } else if (authCode == 'baselandAuth') {
          var accountId = StorageUtil.userInfo()?.data?.id;

          BdhLandResponsitory.getRealNameStatus(accountId!).then((res) {
            if (res.data == null && res.code == 0) {
              showToast('请先进行实名认证');
            } else {
              //审批人注册
              NativeUtil.openUni({"path": url});
            }
          });
        } else if (authCode == 'examine') {
          //预收承包费计划审核
          BdhDigitalLand.appGetCurrentAuditUser({}).then((res) {
            if (res.success == true) {
              Navigator.of(context).pushNamed(url, arguments: res.data);
              // NativeUtil.openUni({"path": url});
            } else {
              showToast(res.msg ?? '请联系管理员');
            }
          });
        } else {
          Navigator.of(context).pushNamed(url);
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Image.asset(
              width: 28.px, height: 28.px, "assets/images/diaLand/$icon.png"),
          SizedBox(height: 8.px),
          Text(
            label,
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 12.px,
                color: Color.fromRGBO(0, 0, 0, 0.8),
                fontFamily: 'PingFang SC'),
          ),
        ],
      ),
    );
  }
}
