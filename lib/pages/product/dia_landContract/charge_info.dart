import 'package:bdh_smart_agric_app/pages/product/dia_landContract/land_sign.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ChargeInfo extends StatefulWidget {
  const ChargeInfo({super.key});

  @override
  State<ChargeInfo> createState() => _ChargeInfoState();
}

class _ChargeInfoState extends State<ChargeInfo> {
  @override
  Widget build(BuildContext context) {
    final res =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;

    final yearNo = res?['yearNo']?.toString() ?? '无数据';
    final chargeMoudleName = res?['chargeMoudleName']?.toString() ?? '无数据';
    final chargeCategoryTypeName =
        res?['chargeCategoryTypeName']?.toString() ?? '无数据';
    final farmerBankAccount = res?['farmerBankAccount']?.toString() ?? '无数据';
    final farmerName = res?['farmerName']?.toString() ?? '无数据';
    final farmerIdNumber = res?['farmerIdNumber']?.toString() ?? '无数据';
    final sumAmount = res?['sumAmount']?.toString() ?? '无数据';
    final sumKilo = res?['sumKilo']?.toString() ?? '无数据';
    final commission = res?['commission']?.toString() ?? '无数据';

    return Scaffold(
      appBar: AppBar(title: const Text('信息确认')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          InfoRow(label: '年度', value: yearNo),
          InfoRow(label: '收费类型', value: chargeMoudleName),
          InfoRow(label: '收费项目', value: chargeCategoryTypeName),
          InfoRow(label: '收费卡号', value: farmerBankAccount),
          InfoRow(label: '交款人', value: farmerName),
          InfoRow(label: '身份证号', value: farmerIdNumber),
          InfoRow(label: '总收费(元)', value: sumAmount),
          InfoRow(label: '总实物(公斤)', value: sumKilo),
          InfoRow(label: '手续费', value: commission),
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        color: Colors.white,
        child: ElevatedButton(
          onPressed: () {
            // 按钮点击逻辑
            _onConfirm(context, res);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color.fromARGB(255, 60, 158, 76),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('确认并签字'),
        ),
      ),
    );
  }

  void _onConfirm(BuildContext context, res) {
    Navigator.of(context).push(CupertinoPageRoute(builder: (ctx) {
      return landSign(
          isReplenish: true,
          tempKey: (res?['tempKey'] as int?)?.toString() ?? '',
          farmerId: res['farmerId'],
          photoPath: res['photoPath'] ?? '');
    }));
  }
}

class InfoRow extends StatelessWidget {
  final String label;
  final String value;
  final EdgeInsetsGeometry padding;

  const InfoRow({
    super.key,
    required this.label,
    required this.value,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          const Divider(
            height: 24,
            thickness: 1,
            color: Colors.grey,
          ),
        ],
      ),
    );
  }
}
