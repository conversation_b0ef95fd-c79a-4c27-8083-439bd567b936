import 'package:bdh_smart_agric_app/model/examine_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_land.dart';
import 'package:flutter/material.dart';

class ExaminePro extends ChangeNotifier {
  bool _isLoading = false;
  bool _hasMore = true;
  String? _errorMessage;
  List<LandContractRecord> listData = [];

  static const int pageNumFirst = 1;
  static const int pageSize = 5;
  int _currentPageNum = pageNumFirst;

  String _someFilter = '';

  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;
  String? get errorMessage => _errorMessage;
  String get someFilter => _someFilter;

  void setSomeFilter(String filter) {
    _someFilter = filter;
    notifyListeners();
  }

  Future<List<LandContractRecord>?> loadData({int? pageNum}) async {
    _setLoading(true);
    var response = await BdhDigitalLand.appQueryByPage({
      "rows": pageSize,
      "page": pageNum,
      "organizationNo": "8616200101",
      "yearNo": "2025"
    });
    return response.data?.records as List<LandContractRecord>?;
  }

  Future<void> onRefresh() async {
    _currentPageNum = 1;
    var data = await loadData(pageNum: _currentPageNum);
    _setHasMore(true);
    listData.clear();
    if (data != null) {
      listData.addAll(data);
    }
  }

  // 加载更多
  Future<void> loadMore() async {
    if (!_hasMore || _isLoading) return;
    try {
      _currentPageNum += 1;
      var data = await loadData(pageNum: _currentPageNum);
      if ((data ?? []).isEmpty) {
        _currentPageNum--;
        _setHasMore(false);
      } else {
        listData.addAll(data!);
        _setHasMore(true);
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  // 重置
  void reset() {
    _currentPageNum = pageNumFirst;
    listData.clear();
  }

  void _setLoading(bool isLoading) {
    if (_isLoading != isLoading) {
      _isLoading = isLoading;
    }
  }

  void _setHasMore(bool hasMore) {
    _hasMore = hasMore;
  }

  void _setError(String error) {
    _errorMessage = error;
  }

  void _clearError() {
    _errorMessage = null;
  }
}
