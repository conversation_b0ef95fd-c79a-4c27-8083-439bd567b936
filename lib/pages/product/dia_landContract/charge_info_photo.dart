import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_vertify_image_picker.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/land_sign.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_land.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

class ChargeInfoPhoto extends StatefulWidget {
  const ChargeInfoPhoto({super.key});
  @override
  State<ChargeInfoPhoto> createState() => _ChargeInfoPhotoState();
}

class _ChargeInfoPhotoState extends State<ChargeInfoPhoto> {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  @override
  var peoplePhotoPath = '';
  Widget build(BuildContext context) {
    final res =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;

    final yearNo = res?['yearNo']?.toString() ?? '无数据';
    final chargeMoudleName = res?['chargeMoudleName']?.toString() ?? '无数据';
    final chargeCategoryTypeName =
        res?['chargeCategoryTypeName']?.toString() ?? '无数据';
    final farmerBankAccount = res?['farmerBankAccount']?.toString() ?? '无数据';
    final farmerName = res?['farmerName']?.toString() ?? '无数据';
    final farmerIdNumber = res?['farmerIdNumber']?.toString() ?? '无数据';
    final sumAmount = res?['sumAmount']?.toString() ?? '无数据';
    final sumKilo = res?['sumKilo']?.toString() ?? '无数据';
    final commission = res?['commission']?.toString() ?? '无数据';

    return Scaffold(
      appBar: AppBar(title: const Text('拍照')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          InfoRow(label: '年度', value: yearNo),
          InfoRow(label: '收费类型', value: chargeMoudleName),
          InfoRow(label: '收费项目', value: chargeCategoryTypeName),
          InfoRow(label: '收费卡号', value: farmerBankAccount),
          InfoRow(label: '交款人', value: farmerName),
          InfoRow(label: '身份证号', value: farmerIdNumber),
          InfoRow(label: '总收费(元)', value: sumAmount),
          InfoRow(label: '总实物(公斤)', value: sumKilo),
          InfoRow(label: '手续费', value: commission),
          Text('交款人照片'),
          Form(
            key: key,
            child: BdhVertifyImagePicker(
              item: FormItem(title: '交款人照片'),
              initialValue: peoplePhotoPath != null
                  ? BDHFile(url: peoplePhotoPath)
                  : null,
              onSaved: (v) {
                peoplePhotoPath = v!.url!;
              },
              validator: (v) {
                if (v == null) {
                  return "照片不能为空";
                }
                return null;
              },
            ),
          )
        ],
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        color: Colors.white,
        child: ElevatedButton(
          onPressed: () {
            // 按钮点击逻辑
            _onConfirm(context, res);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color.fromARGB(255, 60, 158, 76),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('提交照片'),
        ),
      ),
    );
  }

  void _onConfirm(BuildContext context, res) {
    key.currentState!.save();
    if (peoplePhotoPath.isEmpty) {
      showToast("请上传照片");
      return;
    }
    Map<String, dynamic> params = {
      "farmerId": res['farmerId'],
      "photoPath": peoplePhotoPath
    };

    BdhDigitalLand.saveBillSignData(params).then((result) {
      if (result.success!) {
        Navigator.of(context).pop();
        showToast("签名保存成功");
      } else {
        showToast(result.msg!);
      }
    });
  }
}

class InfoRow extends StatelessWidget {
  final String label;
  final String value;
  final EdgeInsetsGeometry padding;

  const InfoRow({
    super.key,
    required this.label,
    required this.value,
    this.padding = const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          const Divider(
            height: 24,
            thickness: 1,
            color: Colors.grey,
          ),
        ],
      ),
    );
  }
}
