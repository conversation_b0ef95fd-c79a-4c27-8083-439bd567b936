import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/examine_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/examinePro.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class Examine extends StatefulWidget {
  const Examine({super.key});

  @override
  State<Examine> createState() => _ExamineState();
}

class _ExamineState extends State<Examine> {
  bool isAllSelected = false; // 全选状态
  bool isDetailExpanded = false; // 控制详情是否展开
  String orgName = '请选择所在单位';
  String organizationNo = '';
 List<OrgTreeResult> orgTreeData = [];
    List<Map> _cascaderOrgData = [];

  DictNode? year;
  List<DictNode> yearDics = [];
  late RefreshController _refreshController;
  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: false);
    // 初始加载数据
    Future.wait([
      LandResponsitory.getDicByKey("year_cd"),
      BdhLandResponsitory.getOrgData()
    ]).then((list) {
      yearDics = (list[0] as DictList).data ?? [];
      year = yearDics.isNotEmpty ? yearDics.first : DictNode(name: '请选择年份');
      final orgResult = list[1] as OrgTreeResult;
      orgTreeData = orgResult.data?.map((e) => OrgTreeItem.fromJson(e))?.toList() ?? [];
      _cascaderOrgData = _convertToTDCascaderData(orgTreeData);
          // orgTreeData = (list[1] as OrgTreeResult)
          //     .map((e) => OrgTreeItem.fromJson(e))
          //     .toList();
          // _cascaderOrgData = _convertToTDCascaderData(orgTreeData);

    });
    final provider = context.read<ExaminePro>();
    // provider.onRefresh();
  }

  @override
  void dispose() {
    _refreshController.dispose(); // 释放资源
    super.dispose();
  }

  void _onRefresh() async {
    final provider = context.read<ExaminePro>();
    await provider.onRefresh();
    _refreshController.refreshCompleted();
    if (provider.hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  // 上拉加载回调
  void _onLoading() async {
    try {
      final provider = context.read<ExaminePro>();
      await provider.loadMore();
      if (provider.hasMore) {
        _refreshController.loadComplete();
      } else {
        _refreshController.loadNoData();
      }
    } catch (e) {
      _refreshController.loadFailed();
    }
  }

  @override
  Widget build(BuildContext context) {
    final res =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return Scaffold(
      appBar: AppBar(
        title: Text('预收承包费计划审核'),
      ),
      body: // 列表部分
          Consumer<ExaminePro>(
        builder: (context, provider, child) {
          return Container(
            padding: EdgeInsets.all(16.px),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(12.px)),
                color: Color.fromRGBO(243, 245, 249, 1)),
            child: Column(
              children: [
                // 您的其他UI组件，如筛选器、搜索框等
                Row(
                  children: [
                    //年份
                    GestureDetector(
                      onTap: () {
                        TDPicker.showMultiPicker(context, onConfirm: (data) {
                          // print(data);
                          setState(() {
                            year = yearDics[data.first];
                          });
                          Navigator.of(context).pop();
                        }, data: [
                          yearDics.map((e) {
                            return e.name!;
                          }).toList()
                        ]);
                      },
                      child: Container(
                        padding: EdgeInsets.only(left: 10.px, right: 10.px),
                        height: 24.px,
                        decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.all(Radius.circular(16.px)),
                            color: const Color.fromARGB(255, 255, 255, 255)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              (year?.name ?? '请选择') + '年',
                              style: TextStyle(
                                  fontSize: 14.px, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(
                              width: 10.px,
                            ),
                            Image.asset(
                                width: 11.px,
                                height: 11.px,
                                ImageHelper.wrapAssets("arrow_down_line.png"))
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 10.px),
                    //组织机构
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          if (orgTreeData?.isNotEmpty == true) {
                            // 转换数据格式为TDCascader需要的格式
                            TDCascader.showMultiCascader(
                              context,
                              title: '选择组织机构',
                              data: _cascaderOrgData,
                              initialData: organizationNo.isNotEmpty ? organizationNo : null,
                              theme: 'step',
                              onChange: (List<MultiCascaderListModel> selectData) {
                                if (selectData.isNotEmpty) {
                                  setState(() {
                                    var result = [];
                                    var len = selectData.length;
                                    organizationNo = selectData[len - 1].value!;
                                    selectData.forEach((element) {
                                      result.add(element.label ?? '');
                                    });
                                    orgName = result.join(' - ');
                                  });
                                  
                                  // // 检查原始数据中对应的节点是否为叶子节点
                                  // final selectedValue = selectData.last.value;
                                  // if (_isLeafNode(cascaderData, selectedValue)) {
                                  //   Navigator.of(context).pop();
                                  // }
                                }
                              },
                              onClose: () {
                                Navigator.of(context).pop();
                              },
                            );
    //                         TDCascader.showMultiCascader(
    //   context,
    //   title: '选择单位名称',
    //   data: cascaderData,
    //   initialData: _selectedOrg,
    //   theme: 'step',
    //   onChange: (List<MultiCascaderListModel> selectData) {
    //     if (selectData.isNotEmpty) {
    //       setState(() {
    //         var result = [];
    //         var len = selectData.length;
    //         _selectedOrg = selectData[len - 1].value!;
    //         selectData.forEach((element) {
    //           result.add(element.label ?? '');
    //         });
    //         _selectedOrgName = result.join('/');
    //       });
    //       Logger().i('选择的组织: $_selectedOrg, 名称: $_selectedOrgName');
    //     }
    //   },
    //   onClose: () {
    //     Navigator.of(context).pop();
    //   },
    // );
                          }
                        },
                        child: Container(
                          padding: EdgeInsets.only(left: 10.px, right: 10.px),
                          height: 24.px,
                          decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(16.px)),
                              color: const Color.fromARGB(255, 255, 255, 255)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Flexible(
                                child: Text(
                                  orgName,
                                  style: TextStyle(
                                      fontSize: 14.px,
                                      fontWeight: FontWeight.w500),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              SizedBox(
                                width: 10.px,
                              ),
                              Icon(
                                TDIcons.chevron_down,
                                size: 16.px,
                                color: TDTheme.of(context).fontGyColor4,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 10.px),
                    GestureDetector(
                      onTap: () async {
                        final provider = context.read<ExaminePro>();
                        await provider.onRefresh();
                        setState(() {
                          // 刷新UI状态
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.only(left: 10.px, right: 10.px),
                        height: 24.px,
                        decoration: BoxDecoration(
                          borderRadius:
                              BorderRadius.all(Radius.circular(16.px)),
                          color: Colors.white, // 显式设置白色背景
                          border: Border.all(
                            color: const Color(0xFF3C9CFF), // 设置指定边框颜色
                            width: 1.0,
                          ),
                        ),
                        child: Text('筛选'),
                      ),
                    )
                  ],
                ),
                SizedBox(height: 20.px),
                // 列表部分
                Expanded(
                  child: provider.isLoading && provider.listData.isEmpty
                      ? const Center(child: CircularProgressIndicator()) // 加载中
                      : provider.listData.isEmpty
                          ? const Center(child: Text('暂无数据')) // 空数据
                          : SmartRefresher(
                              enablePullDown: true,
                              enablePullUp: true,
                              header: const WaterDropHeader(),
                              footer: CustomFooter(
                                builder: (context, mode) {
                                  Widget body;
                                  if (mode == LoadStatus.idle) {
                                    body = const Text("上拉加载更多");
                                  } else if (mode == LoadStatus.loading) {
                                    body = const CircularProgressIndicator();
                                  } else if (mode == LoadStatus.failed) {
                                    body = const Text("加载失败，点击重试");
                                  } else if (mode == LoadStatus.canLoading) {
                                    body = const Text("释放加载更多");
                                  } else {
                                    body = const Text("没有更多数据了");
                                  }
                                  return Container(
                                    height: 55,
                                    child: Center(child: body),
                                  );
                                },
                              ),
                              controller: _refreshController,
                              onRefresh: _onRefresh,
                              onLoading: _onLoading,
                              child: ListView.builder(
                                itemCount: provider.listData.length,
                                itemBuilder: (context, index) {
                                  final item = provider.listData[index];
                                  // 构建列表项
                                  return _ExamineItemWidget(
                                    item: item,
                                    parentContext: context,
                                  );
                                },
                              ),
                            ),
                ),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: Consumer<ExaminePro>(
        builder: (context, provider, child) {
          // 检查是否所有项目都被选中
          bool allSelected = provider.listData.isNotEmpty && 
                            provider.listData.every((item) => item.isCheck);
          
          return BottomAppBar(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Radio<bool>(
                      value: true,
                      toggleable: true,
                      groupValue: allSelected ? true : null,
                      onChanged: (value) {
                        setState(() {
                          // 设置全选或全不选
                          bool newValue = value ?? false;
                          for (var item in provider.listData) {
                            item.isCheck = newValue;
                          }
                          isAllSelected = newValue;
                        });
                      },
                    ),
                    Text('全选'),
                  ],
                ),
                Row(
                  // 右侧按钮组
                  children: [
                    TextButton(
                      onPressed: provider.listData.any((item) => item.isCheck) ? () {} : null,
                      child: Text('审核通过'),
                    ),
                    SizedBox(width: 16), // 按钮间距
                    TextButton(
                      onPressed: provider.listData.any((item) => item.isCheck) ? () {} : null,
                      child: Text('审核拒绝'),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // 将 OrgTreeItem 列表转换为 TDCascader 需要的数据格式
  List<Map<String, dynamic>> _convertToTDCascaderData(List<OrgTreeItem> orgList) {
    return orgList.map((item) {
      Map<String, dynamic> cascaderItem = {
        'label': item.orgName ?? '',
        'value': item.orgCode ?? '',
      };
      
      // 递归处理子节点
      if (item.children != null && item.children!.isNotEmpty) {
        cascaderItem['children'] = _convertToTDCascaderData(item.children!);
      }
      
      return cascaderItem;
    }).toList();
  }

  // 检查指定值是否为叶子节点（没有子节点）
  bool _isLeafNode(List<Map<String, dynamic>> data, String? value) {
    return _findNodeIsLeaf(data, value) ?? false;
  }

  // 辅助方法：查找节点并返回是否为叶子节点，如果没找到返回null
  bool? _findNodeIsLeaf(List<Map<String, dynamic>> data, String? value) {
    for (var item in data) {
      if (item['value'] == value) {
        // 找到匹配的节点，检查是否为叶子节点
        return item['children'] == null || (item['children'] as List).isEmpty;
      }
      // 如果当前节点有子节点，递归搜索
      if (item['children'] != null && (item['children'] as List).isNotEmpty) {
        bool? result = _findNodeIsLeaf(item['children'], value);
        if (result != null) return result; // 在子树中找到了节点，返回结果
      }
    }
    return null; // 没有找到匹配的节点
  }
}

// 使用StatefulWidget来保持每个卡片的展开状态
class _ExamineItemWidget extends StatefulWidget {
  final LandContractRecord item;
  final BuildContext parentContext;
  
  const _ExamineItemWidget({Key? key, required this.item, required this.parentContext}) : super(key: key);
  
  @override
  _ExamineItemWidgetState createState() => _ExamineItemWidgetState();
}

class _ExamineItemWidgetState extends State<_ExamineItemWidget> {
  bool isExpanded = false;
  
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.px),
      padding: EdgeInsets.all(16.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 4.px,
            offset: Offset(0, 2.px),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部行：单选框、组织名称、农户名称和类型标签
          Row(
            children: [
              Radio<bool>(
                value: true,
                toggleable: true,
                groupValue: widget.item.isCheck ? true : null,
                onChanged: (value) {
                  // 使用父级的setState来更新选中状态，但不影响展开状态
                  final examineState = widget.parentContext.findAncestorStateOfType<_ExamineState>();
                  if (examineState != null) {
                    examineState.setState(() {
                      widget.item.isCheck = value ?? false;
                      
                      // 检查是否需要更新全选状态
                      final provider = Provider.of<ExaminePro>(widget.parentContext, listen: false);
                      bool allSelected = provider.listData.every((item) => item.isCheck);
                      examineState.isAllSelected = allSelected;
                    });
                  }
                },
              ),
              Text(
                widget.item.organizationName ?? '',
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Spacer(),
              Text(
                widget.item.farmerName ?? '',
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: 4.px),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 4.px, vertical: 2.px),
                decoration: BoxDecoration(
                  color: widget.item.contractSignType == 20 
                      ? Color(0xFF5D96FF) 
                      : Color(0xFF61CDB5),
                  borderRadius: BorderRadius.circular(2.px),
                ),
                child: Text(
                  widget.item.contractSignType == 20 ? '企' : '个',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12.px,
                  ),
                ),
              )
            ],
          ),
          
          // 分割线
          Divider(height: 20.px),
          
          // 基本信息行 - 应收总金额
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '应收总金额',
                style: TextStyle(
                  fontSize: 12.px,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                '¥${widget.item.totalFee?.toStringAsFixed(2) ?? '0.00'}',
                style: TextStyle(
                  fontSize: 16.px,
                  fontWeight: FontWeight.bold,
                  color: Colors.red,
                ),
              ),
            ],
          ),
          
          SizedBox(height: 8.px),
          
          // 总面积
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '总面积',
                style: TextStyle(
                  fontSize: 12.px,
                  color: Colors.grey[600],
                ),
              ),
              Text(
                '${widget.item.area?.toStringAsFixed(2) ?? '0.00'} 亩',
                style: TextStyle(
                  fontSize: 16.px,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          // 展开后显示的详细信息
          if (isExpanded) ...[
            SizedBox(height: 16.px),
            Divider(height: 1.px),
            SizedBox(height: 16.px),
            
            Container(
              padding: EdgeInsets.all(12.px),
              decoration: BoxDecoration(
                color: Color.fromRGBO(243, 245, 249, 0.7),
                borderRadius: BorderRadius.circular(8.px),
              ),
              child: Column(
                children: [
                  // 水田信息
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '水田面积',
                        style: TextStyle(
                          fontSize: 12.px,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '${widget.item.paddyArea?.toStringAsFixed(2) ?? '0.00'} 亩',
                        style: TextStyle(
                          fontSize: 14.px,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.px),
                  
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '水田金额',
                        style: TextStyle(
                          fontSize: 12.px,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '¥${widget.item.paddyTotalFee?.toStringAsFixed(2) ?? '0.00'}',
                        style: TextStyle(
                          fontSize: 14.px,
                          fontWeight: FontWeight.w500,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 16.px),
                  
                  // 旱地信息
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '旱地面积',
                        style: TextStyle(
                          fontSize: 12.px,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '${widget.item.uplandArea?.toStringAsFixed(2) ?? '0.00'} 亩',
                        style: TextStyle(
                          fontSize: 14.px,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.px),
                  
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '旱地金额',
                        style: TextStyle(
                          fontSize: 12.px,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '¥${widget.item.uplandTotalFee?.toStringAsFixed(2) ?? '0.00'}',
                        style: TextStyle(
                          fontSize: 14.px,
                          fontWeight: FontWeight.w500,
                          color: Colors.red,
                        ),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 16.px),
                  
                  // 其他信息
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '实物折算',
                        style: TextStyle(
                          fontSize: 12.px,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '${widget.item.rentInKindTotal?.toStringAsFixed(2) ?? '0.00'} 斤',
                        style: TextStyle(
                          fontSize: 14.px,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 12.px),
                  
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '耕地数量',
                        style: TextStyle(
                          fontSize: 12.px,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        '${widget.item.plowlandNo ?? '0'} 块',
                        style: TextStyle(
                          fontSize: 14.px,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
          
          // 底部展开/收起按钮
          SizedBox(height: 16.px),
          Center(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  isExpanded = !isExpanded;
                });
              },
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16.px, vertical: 6.px),
                decoration: BoxDecoration(
                  color: Color(0xFFF5F5F5),
                  borderRadius: BorderRadius.circular(16.px),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      isExpanded ? '收起详情' : '查看详情',
                      style: TextStyle(
                        fontSize: 12.px,
                        color: Color(0xFF666666),
                      ),
                    ),
                    SizedBox(width: 4.px),
                    Icon(
                      isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                      size: 16.px,
                      color: Color(0xFF666666),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}