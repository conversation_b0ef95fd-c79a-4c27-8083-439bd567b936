import 'package:bdh_smart_agric_app/components/jh_cascade_tree_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/examine_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/product/dia_landContract/examinePro.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class Examine extends StatefulWidget {
  const Examine({super.key});

  @override
  State<Examine> createState() => _ExamineState();
}

class _ExamineState extends State<Examine> {
  bool isAllSelected = false; // 单选状态
  bool isDetailExpanded = false; // 控制详情是否展开
  String orgName = '请选择所在单位';
  String organizationNo = '';
  late OrgTreeResult orgTreeResult;

  DictNode? year;
  List<DictNode> yearDics = [];
  late RefreshController _refreshController;
  @override
  void initState() {
    super.initState();
    _refreshController = RefreshController(initialRefresh: false);
    // 初始加载数据
    Future.wait([
      LandResponsitory.getDicByKey("year_cd"),
      BdhLandResponsitory.getOrgData()
    ]).then((list) {
      yearDics = (list[0] as DictList).data ?? [];
      year = yearDics.isNotEmpty ? yearDics.first : DictNode(name: '请选择年份');
      orgTreeResult = list[1] is OrgTreeResult
          ? list[1] as OrgTreeResult
          : OrgTreeResult(data: []);
    });
    final provider = context.read<ExaminePro>();
    provider.onRefresh();
  }

  @override
  void dispose() {
    _refreshController.dispose(); // 释放资源
    super.dispose();
  }

  void _onRefresh() async {
    final provider = context.read<ExaminePro>();
    await provider.onRefresh();
    _refreshController.refreshCompleted();
    if (provider.hasMore) {
      _refreshController.loadComplete();
    } else {
      _refreshController.loadNoData();
    }
  }

  // 上拉加载回调
  void _onLoading() async {
    try {
      final provider = context.read<ExaminePro>();
      await provider.loadMore();
      if (provider.hasMore) {
        _refreshController.loadComplete();
      } else {
        _refreshController.loadNoData();
      }
    } catch (e) {
      _refreshController.loadFailed();
    }
  }

  @override
  Widget build(BuildContext context) {
    final res =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    return Scaffold(
      appBar: AppBar(
        title: Text('预收承包费计划审核'),
      ),
      body: // 列表部分
          Consumer<ExaminePro>(
        builder: (context, provider, child) {
          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(12.px)),
                color: Color.fromRGBO(243, 245, 249, 1)),
            child: Column(
              children: [
                // 您的其他UI组件，如筛选器、搜索框等
                Row(
                  children: [
                    //年份
                    GestureDetector(
                      onTap: () {
                        TDPicker.showMultiPicker(context, onConfirm: (data) {
                          // print(data);
                          setState(() {
                            year = yearDics[data.first];
                          });
                          Navigator.of(context).pop();
                        }, data: [
                          yearDics.map((e) {
                            return e.name!;
                          }).toList()
                        ]);
                      },
                      child: Container(
                        padding: EdgeInsets.only(left: 10.px, right: 10.px),
                        height: 24.px,
                        decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.all(Radius.circular(16.px)),
                            color: const Color.fromARGB(255, 255, 255, 255)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              (year?.name ?? '请选择') + '年',
                              style: TextStyle(
                                  fontSize: 14.px, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(
                              width: 10.px,
                            ),
                            Image.asset(
                                width: 11.px,
                                height: 11.px,
                                ImageHelper.wrapAssets("arrow_down_line.png"))
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 10.px),
                    //组织机构
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          if (orgTreeResult.data?.isNotEmpty == true) {
                            // 将 OrgTreeItem 列表转换为 Map 列表，并修正 children 字段名
                            List<Map<String, dynamic>> orgMapList =
                                _convertOrgTreeToMap(orgTreeResult.data!);
                            JhCascadeTreePicker.show(
                              context,
                              data: orgMapList,
                              labelKey: 'orgName',
                              valueKey: 'orgCode',
                              childrenKey: 'children',
                              title: '选择组织机构',
                              tabText: '请选择',
                              ensureCallBack: (selectItem, selectArr) {
                                setState(() {
                                  if (selectItem is Map<String, dynamic> &&
                                      selectArr is List &&
                                      selectArr.isNotEmpty) {
                                    // 拼接所有层级名称
                                    final names = selectArr.map<String>((item) {
                                      if (item is Map<String, dynamic>) {
                                        return item['orgName'] ?? '';
                                      }
                                      return '';
                                    }).toList();

                                    // 过滤空值后拼接
                                    orgName = names
                                        .where((name) => name.isNotEmpty)
                                        .join(' - ');
                                    organizationNo =
                                        selectItem['orgCode'] ?? '';
                                  } else {
                                    orgName = '请选择所在单位';
                                    organizationNo = '';
                                  }
                                });
                              },
                            );
                          }
                        },
                        child: Container(
                          padding: EdgeInsets.only(left: 10.px, right: 10.px),
                          height: 24.px,
                          decoration: BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(16.px)),
                              color: const Color.fromARGB(255, 255, 255, 255)),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                orgName,
                                style: TextStyle(
                                    fontSize: 14.px,
                                    fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                width: 10.px,
                              ),
                              Image.asset(
                                  width: 11.px,
                                  height: 11.px,
                                  ImageHelper.wrapAssets("arrow_down_line.png"))
                            ],
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 10.px),
                    GestureDetector(
                      onTap: () {
                        setState(() async {
                          final provider = context.read<ExaminePro>();
                          await provider.onRefresh();
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.only(left: 10.px, right: 10.px),
                        height: 24.px,
                        decoration: BoxDecoration(
                          borderRadius:
                              BorderRadius.all(Radius.circular(16.px)),
                          color: Colors.white, // 显式设置白色背景
                          border: Border.all(
                            color: const Color(0xFF3C9CFF), // 设置指定边框颜色
                            width: 1.0,
                          ),
                        ),
                        child: Text('筛选'),
                      ),
                    )
                  ],
                ),
                // 列表部分
                Expanded(
                  child: provider.isLoading && provider.listData.isEmpty
                      ? const Center(child: CircularProgressIndicator()) // 加载中
                      : provider.listData.isEmpty
                          ? const Center(child: Text('暂无数据')) // 空数据
                          : SmartRefresher(
                              enablePullDown: true,
                              enablePullUp: true,
                              header: const WaterDropHeader(),
                              footer: CustomFooter(
                                builder: (context, mode) {
                                  Widget body;
                                  if (mode == LoadStatus.idle) {
                                    body = const Text("上拉加载更多");
                                  } else if (mode == LoadStatus.loading) {
                                    body = const CircularProgressIndicator();
                                  } else if (mode == LoadStatus.failed) {
                                    body = const Text("加载失败，点击重试");
                                  } else if (mode == LoadStatus.canLoading) {
                                    body = const Text("释放加载更多");
                                  } else {
                                    body = const Text("没有更多数据了");
                                  }
                                  return Container(
                                    height: 55,
                                    child: Center(child: body),
                                  );
                                },
                              ),
                              controller: _refreshController,
                              onRefresh: _onRefresh,
                              onLoading: _onLoading,
                              child: ListView.builder(
                                itemCount: provider.listData.length,
                                itemBuilder: (context, index) {
                                  final item = provider.listData[index];
                                  // 构建列表项
                                  return Padding(
                                      padding: EdgeInsets.only(top: 16.px),
                                      child: ExamineItem(item: item));
                                },
                              ),
                            ),
                ),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: BottomAppBar(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Radio<bool>(
                  value: true,
                  toggleable: true,
                  groupValue: isAllSelected,
                  onChanged: (value) {
                    setState(() {
                      isAllSelected = value ?? false;
                    });
                  },
                ),
                Text('全选'),
              ],
            ),
            Row(
              // 右侧按钮组
              children: [
                TextButton(
                  onPressed: () {},
                  child: Text('审核通过'),
                ),
                SizedBox(width: 16), // 按钮间距
                TextButton(
                  onPressed: () {},
                  child: Text('审核拒绝'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 将 OrgTreeItem 列表转换为 Map 列表，并修正 children 字段名
  List<Map<String, dynamic>> _convertOrgTreeToMap(List<OrgTreeItem> orgList) {
    return orgList.map((item) {
      Map<String, dynamic> map = item.toJson();
      // 将 'list' 字段重命名为 'children'
      if (map.containsKey('list')) {
        map['children'] = map['list'];
        map.remove('list');
      }
      // 递归处理子节点
      if (item.children != null && item.children!.isNotEmpty) {
        map['children'] = _convertOrgTreeToMap(item.children!);
      }
      return map;
    }).toList();
  }

  Widget ExamineItem({required LandContractRecord item}) {
    bool isExpanded = false;
    return Container(
      padding:
          EdgeInsets.only(left: 16.px, right: 16.px, top: 16.px, bottom: 16.px),
      decoration: BoxDecoration(
        color: Color.fromARGB(255, 255, 255, 255),
      ),
      child: Column(
        children: [
          // 主要信息行
          Row(
            children: [
              Radio<bool>(
                value: true,
                toggleable: true,
                groupValue: item.isCheck,
                onChanged: (value) {
                  setState(() {
                    item.isCheck = value ?? false;
                  });
                },
              ),
              Text(item.organizationName ?? ''),
              Spacer(),
              Text(item.farmerName ?? ''),
              Container(width: 2.px),
              Container(
                decoration: BoxDecoration(
                  color: item.contractSignType == 20
                      ? Color(0xFF5D96FF)
                      : Color(0x61CDB5),
                ),
                child: Text(item.contractSignType == 20 ? '企' : '个' ?? ''),
              )
            ],
          ),
          // 展开/收起按钮
          GestureDetector(
            onTap: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Padding(
              padding: EdgeInsets.only(top: 8.px),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(isExpanded ? '收起详情' : '查看详情'),
                  Icon(
                    isExpanded ? Icons.expand_less : Icons.expand_more,
                    size: 16.px,
                  ),
                ],
              ),
            ),
          ),
          // 详细信息（可展开）
          if (isExpanded)
            Container(
              margin: EdgeInsets.only(top: 8.px),
              padding: EdgeInsets.all(12.px),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8.px),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 应收总金额
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('应收总金额(元)'),
                      Text('${item.totalFee?.toStringAsFixed(2)}'),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 总面积
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('总面积(亩)'),
                      Text('${item.area?.toStringAsFixed(2)}'),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 年份
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('年份'),
                      Text('${item.yearNo}'),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 所在单位
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('所在单位'),
                      Text(item.organizationName ?? ''),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 农户姓名
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('农户姓名'),
                      Text(item.farmerName ?? ''),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 农户身份证号
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('农户身份证号'),
                      Text(item.farmerIdNumber ?? ''),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 水田金额
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('水田金额(元)'),
                      Text('${item.paddyTotalFee?.toStringAsFixed(2)}'),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 水田面积
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('水田面积(亩)'),
                      Text('${item.paddyArea?.toStringAsFixed(2)}'),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 水田实物
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('水田实物(公斤)'),
                      Text('${item.rentInKindTotal?.toStringAsFixed(2)}'),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 旱田金额
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('旱田金额(元)'),
                      Text('${item.uplandTotalFee?.toStringAsFixed(2)}'),
                    ],
                  ),
                  SizedBox(height: 8.px),
                  // 旱田面积
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('旱田面积(亩)'),
                      Text('${item.uplandArea?.toStringAsFixed(2)}'),
                    ],
                  ),
                ],
              ),
            ),
          Divider(),
        ],
      ),
    );
  }
}
