import 'package:bdh_smart_agric_app/model/dia_apply_filed_model.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

//基本田申请详情
class DiaApplyDetail extends StatefulWidget {
  final Records params;
  const DiaApplyDetail({super.key, required this.params});

  @override
  State<DiaApplyDetail> createState() => _DiaApplyDetailState();
}

class _DiaApplyDetailState extends State<DiaApplyDetail> {
  // 步骤数据
  late List<TDStepsItemData> stepsData;
  //与户主关系
  List<DictNode> householderRelationDict = [];
  bool isLoading = false;
  int activeIndex = 0;
  String householderRelationName = '';
  @override
  void initState() {
    super.initState();
    final auditTimes = [
      widget.params.auditATime,
      widget.params.auditBTime,
      widget.params.auditCTime,
      widget.params.auditDTime,
      widget.params.auditETime,
      widget.params.auditFTime,
      widget.params.auditGTime,
      widget.params.auditHTime,
    ];
    activeIndex = 0;
    for (final time in auditTimes) {
      if (time != null && time.isNotEmpty) {
        activeIndex++;
      } else {
        break;
      }
    }
    stepsData = [
      TDStepsItemData(title: "提交申请", content: widget.params.createTime ?? ''),
    ];
    int auditLevel = widget.params.auditLevel?.toInt() ?? 0;
    for (int i = 0; i <= auditLevel; i++) {
      final level = i + 1; // 审核级别从1开始
      final flag = i == 0
          ? widget.params.auditAFlag
          : i == 1
              ? widget.params.auditBFlag
              : i == 2
                  ? widget.params.auditCFlag
                  : i == 3
                      ? widget.params.auditDFlag
                      : i == 4
                          ? widget.params.auditEFlag
                          : i == 5
                              ? widget.params.auditFFlag
                              : i == 6
                                  ? widget.params.auditGFlag
                                  : widget.params.auditHFlag;

      final time = i == 0
          ? widget.params.auditATime
          : i == 1
              ? widget.params.auditBTime
              : i == 2
                  ? widget.params.auditCTime
                  : i == 3
                      ? widget.params.auditDTime
                      : i == 4
                          ? widget.params.auditETime
                          : i == 5
                              ? widget.params.auditFTime
                              : i == 6
                                  ? widget.params.auditGTime
                                  : widget.params.auditHTime;

      stepsData.add(TDStepsItemData(
          title: "${level}级${flag == 1 ? '审核' : '未审核'}",
          content: flag == 1 ? (time ?? '') : ''));
    }
    loadDics();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: HexColor('#F5F5F5'),
        appBar: AppBar(
          title: const Text("基本田申请"),
        ),
        body: isLoading
            ? const ViewStateBusyWidget()
            : SingleChildScrollView(
                padding: EdgeInsets.symmetric(horizontal: 15.px),
                child: Center(
                    child: Column(
                  children: [
                    Container(
                      width: 347.px,
                      margin: EdgeInsets.only(top: 15.px),
                      padding: EdgeInsets.only(left: 15.px, right: 15.px),
                      decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(5))),
                      child: Column(
                        children: [
                          StaticDiaplay("姓名", widget.params.name ?? '',
                              isDivider: true),
                          StaticDiaplay(
                              "身份证号", maskIdCard(widget.params.idNumber ?? ''),
                              isDivider: false),
                        ],
                      ),
                    ),
                    Container(
                      width: 347.px,
                      margin: EdgeInsets.only(top: 15.px),
                      padding: EdgeInsets.only(left: 15.px, right: 15.px),
                      decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(5))),
                      child: Column(
                        children: [
                          StaticDiaplay(
                              "选择年度", (widget.params.yearNo?.toString() ?? ''),
                              isDivider: true),
                          StaticDiaplay("与户主关系", householderRelationName ?? '',
                              isDivider: true),
                          StaticDiaplay("银行卡号",
                              maskBankCard(widget.params.bankAccount ?? ''),
                              isDivider: true),
                          StaticDiaplay(
                              "资格所在单位", widget.params.organizationName ?? '',
                              isDivider: true),
                          StaticDiaplay("申请日期",
                              widget.params.createTime?.split(' ').first ?? '',
                              isDivider: true),
                          StaticDiaplay("申请状态", widget.params.auditAFlagName,
                              isDivider: false),
                        ],
                      ),
                    ),
                    // 添加步骤条Card
                    Container(
                      width: 347.px,
                      margin: EdgeInsets.only(top: 15.px),
                      padding: EdgeInsets.only(
                          left: 25.px, right: 25.px, top: 25.px, bottom: 25.px),
                      decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(5))),
                      child: TDSteps(
                        steps: stepsData,
                        direction: TDStepsDirection.vertical,
                        activeIndex: activeIndex,
                      ),
                    ),
                  ],
                )),
              ));
  }

  String maskIdCard(String idNumber) {
    if (idNumber.length < 14) return idNumber;
    return idNumber.substring(0, 6) +
        '*****' +
        idNumber.substring(idNumber.length - 4);
  }

//银行卡脱敏
  String maskBankCard(String cardNum) {
    if (cardNum.length >= 16) {
      return cardNum.replaceRange(8, 16, "**** ****");
    } else if (cardNum.length > 8) {
      // 隐藏中间部分，只显示前4位和后4位
      String prefix = cardNum.substring(0, 4);
      String suffix = cardNum.substring(cardNum.length - 4);
      return "$prefix **** $suffix";
    } else {
      // 长度太短，直接返回原卡号
      return cardNum;
    }
  }

  void loadDics() {
    setState(() {
      isLoading = true;
      householderRelationName = '';
    });
    Future.wait([
      BdhDigitalService.getDicByKey("relations"),
      // BdhDigitalService.getDicByKey("year_cd"),
    ]).then((list) {
      householderRelationDict = (list[0] as DictList).data ?? [];
      setState(() {
        isLoading = false;
        final matchedList = householderRelationDict.where((node) {
          final parsedCode = num.tryParse(node.code ?? '');

          return parsedCode == widget.params.householderRelation;
        }).toList();
        householderRelationName =
            matchedList.isNotEmpty ? matchedList[0].name ?? '' : '';
      });
    });
  }
}

//静态展示
Widget StaticDiaplay(String name, String? value, {required bool isDivider}) {
  return Column(children: [
    SizedBox(
      height: 50.px,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 标签部分（固定宽度）
            SizedBox(
              width: 100.px,
              child: Text(
                name,
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                  fontFamily: 'PingFang SC',
                ),
              ),
            ),

            // 值部分（自动换行 + 最大宽度限制）
            SizedBox(
              width: 200.px, // 固定最大宽度（根据需求调整）
              child: Text(
                value ?? '',
                maxLines: null, // 允许无限换行
                overflow: TextOverflow.visible, // 不截断
                textAlign: TextAlign.right, // 右对齐
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w600,
                  fontFamily: 'PingFang SC',
                ),
              ),
            ),

            SizedBox(width: 8.px),
          ],
        ),
      ),
    ),

    // 分隔线
    if (isDivider)
      Divider(
        height: 0.5.px,
        color: const Color.fromRGBO(239, 241, 245, 1),
      ),
  ]);
}
