import 'dart:convert';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_ocr_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_sms.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input.dart';
import 'package:bdh_smart_agric_app/const/string_const.dart';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/user_info_model.dart';
import 'package:bdh_smart_agric_app/utils/encrypt_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class RealNameArtificialAuthPage extends StatefulWidget {
  const RealNameArtificialAuthPage({
    super.key,
  });

  @override
  State<StatefulWidget> createState() => _RealNameArtificialAuthPageState();
}

class _RealNameArtificialAuthPageState
    extends State<RealNameArtificialAuthPage> {
  GlobalKey<FormState> key = GlobalKey<FormState>();
  Map<String, dynamic> form = {};
  TextEditingController textEditingController1 = TextEditingController();
  TextEditingController textEditingController2 = TextEditingController();
  TextEditingController phoneController = TextEditingController();

  List<DictNode> sexDics = [];
  List<DictNode> nations = [];
  int fingerCount = 0;

  @override
  void initState() {
    super.initState();
    loadFingers();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color.fromRGBO(239, 241, 245, 1),
        appBar: AppBar(
          title: const Text("人工审核"),
        ),
        body: LayoutBuilder(builder: (ctx, cons) {
          return Column(
            children: [
              SizedBox(
                width: 375.px,
              ),
              SizedBox(
                height: cons.maxHeight -
                    MediaQuery.of(context).padding.bottom -
                    45.px -
                    15.px,
                child: SingleChildScrollView(
                  child: Form(
                      autovalidateMode: AutovalidateMode.always,
                      key: key,
                      child: Column(
                        children: [
                          Container(
                              margin: EdgeInsets.only(top: 20.px),
                              padding: EdgeInsets.all(16.px),
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(8.px))),
                              width: 347.px,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "本人五官清晰可见，并比出数字$fingerCount的手势",
                                    style: TextStyle(
                                        fontSize: 14.px,
                                        fontWeight: FontWeight.w500),
                                  ),
                                  SizedBox(
                                    height: 10.px,
                                  ),
                                  BdhOcrImagePicker(
                                    pickerType: PickerType.camera,
                                    item: FormItem(),
                                    icon: "artficial.jpg",
                                    onUpload: (v) {},
                                    onSaved: (v) {
                                      form["gesturePhotoPath"] = v?.url;
                                    },
                                    validator: (v) {
                                      if (v == null) {
                                        return "请拍摄手势照片!";
                                      }
                                      return null;
                                    },
                                  )
                                ],
                              )),
                          Container(
                            margin: EdgeInsets.only(top: 20.px),
                            padding: EdgeInsets.only(left: 10.px, right: 10.px),
                            decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius:
                                    BorderRadius.all(Radius.circular(8.px))),
                            width: 347.px,
                            child: Column(
                              children: [
                                BdhTextInput(
                                  controller: textEditingController1,
                                  item: FormItem(title: "姓名", isRequired: true),
                                  onSaved: (v) {
                                    form["name"] = v;
                                  },
                                  validator: (v) {
                                    if (v == null || v.isEmpty) {
                                      return "姓名不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhTextInput(
                                  controller: textEditingController2,
                                  item:
                                      FormItem(title: "身份证", isRequired: true),
                                  onSaved: (v) {
                                    form["identNo"] = v;
                                  },
                                  validator: (v) {
                                    if (v == null || v.isEmpty) {
                                      return "身份证号码不能为空";
                                    }
                                    return null;
                                  },
                                ),
                                BdhSms(
                                  initialValue: SmsItem(
                                      StorageUtil.userInfo()?.data?.telephone ??
                                          "",
                                      ""),
                                  item:
                                      FormItem(title: "手机号码", isRequired: true),
                                  onSaved: (v) {
                                    form["phoneNo"] = v?.phone;
                                    form["smsVerificationCode"] = v?.code;
                                  },
                                  validator: (v) {
                                    if (v?.phone == null ||
                                        v?.phone == "" ||
                                        v?.code == "" ||
                                        v?.code == null) {
                                      return "手机号码和验证码不能为空";
                                    }
                                    return null;
                                  },
                                )
                              ],
                            ),
                          ),
                          Container(
                            margin: EdgeInsets.only(top: 15.px, bottom: 15.px),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "拍摄要求须知",
                                  style: TextStyle(
                                      fontSize: 12.px,
                                      fontWeight: FontWeight.w600,
                                      color:
                                          const Color.fromRGBO(44, 44, 52, 1)),
                                ),
                                SizedBox(
                                  height: 10.px,
                                ),
                                Image.asset(
                                    width: 349.5.px,
                                    height: 58.px,
                                    ImageHelper.wrapAssets(
                                        "photo_standard.png"))
                              ],
                            ),
                          )
                        ],
                      )),
                ),
              ),
              GestureDetector(
                onTap: () async {
                  if (key.currentState!.validate()) {
                    key.currentState!.save();

                    if (kDebugMode) {
                      print(jsonEncode(form));
                    }
                    form['businessType'] = 5;

                    form["systemId"] = kSystemCode;
                    var encryptAccountId = EncryptUtil.rsaEncryptLong(
                        "${form["identNo"]}~${form["accountId"]}~${DateTime.now().millisecondsSinceEpoch}");
                    var encryptGesturePhotoPath = EncryptUtil.rsaEncryptLong(
                        "${form["identNo"]}~${form["gesturePhotoPath"]}~${DateTime.now().millisecondsSinceEpoch}");
                    var encryptIdentFrontPath = EncryptUtil.rsaEncryptLong(
                        "${form["identNo"]}~${form["identFrontPath"]}~${DateTime.now().millisecondsSinceEpoch}");
                    form["accountId"] = encryptAccountId;
                    form["gesturePhotoPath"] = encryptGesturePhotoPath;
                    form["identFrontPath"] = encryptIdentFrontPath;
                    TDToast.showLoadingWithoutText(context: context);
                    BdhLandResponsitory.uploadGesturePhoto(form).then((res) {
                      TDToast.dismissLoading();
                      if (res.code == 0) {
                        Navigator.of(context).popUntil((e) {
                          return e.settings.name == RouteName.tabMain;
                        });
                        // Navigator.of(context).pop();
                      } else if (res.code == -1) {
                        showToast(res.msg ?? '操作失败请重试');
                      }
                    });
                  }
                },
                child:
                    BDHButtonGreen(width: 347.px, height: 45.px, title: "下一步"),
              )
            ],
          );
        }));
  }

  loadFingers() {
    UserInfo? userInfo = StorageUtil.userInfo();
    BdhLandResponsitory.getGestureNumber({
      "accountId": userInfo?.data?.id,
      "businessType": 5,
    }).then((res) {
      setState(() {
        fingerCount = res.data["gesturPhotoNum"];
        form["gesturPhotoNum"] = fingerCount;
        form["accountId"] = StorageUtil.userInfo()?.data?.id;
        form["identFrontPath"] = res.data["identFrontPath"];
        textEditingController1.text = res.data["name"];
        textEditingController2.text = res.data["idCard"];
      });
    });
  }
}
