/*
 * <AUTHOR>
 * @description: 已提交列表
 * @date 2025/06/16 08:52:43
*/
import 'package:flutter/material.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/database/index.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/utils/request/three_censuses_page_service.dart';
import 'package:logger/logger.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class ThreeSubmittedDatabaseListPage extends StatefulWidget {
  const ThreeSubmittedDatabaseListPage({super.key});

  @override
  State<ThreeSubmittedDatabaseListPage> createState() => _ThreeSubmittedDatabaseListPageState();
}

class _ThreeSubmittedDatabaseListPageState extends State<ThreeSubmittedDatabaseListPage> with WidgetsBindingObserver {
  final SamplingTaskService _service = SamplingTaskService();
  
  List<SamplingTaskLocal> _allTasks = [];
  List<SamplingTaskLocal> _filteredTasks = [];
  bool _isLoading = true;
  
  // 选择相关
  final Set<int> _selectedTaskIds = <int>{};
  bool get _hasSelection => _selectedTaskIds.isNotEmpty;
  
  // 过滤相关
  String _searchKeyword = '';

  // 字典数据
  List<DictNode> _landUseTypeDics = [];
  List<DictNode> _samplingTypeDics = [];
  List<DictNode> _pointTypeDics = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadDictionaries();
    _loadSubmittedTasks();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用从后台回到前台时刷新数据
    if (state == AppLifecycleState.resumed) {
      _loadSubmittedTasks();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 每次页面依赖变化时刷新数据（包括从其他页面返回）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadSubmittedTasks();
    });
  }

  Future<void> _loadDictionaries() async {
    try {
      // 并行加载所有字典数据
      final futures = [
        ThreeCensusesPageService.getDicByKey("soil_land_use_type"),
        ThreeCensusesPageService.getDicByKey("soil_sampling_type"),
        ThreeCensusesPageService.getDicByKey("soil_point_type"),
      ];

      final results = await Future.wait(futures);

      setState(() {
        _landUseTypeDics = results[0].data ?? [];
        _samplingTypeDics = results[1].data ?? [];
        _pointTypeDics = results[2].data ?? [];
      });
    } catch (e) {
      Logger().e('加载字典数据失败: $e');
    }
  }

  Future<void> _loadSubmittedTasks() async {
    try {
      if (mounted) {
        setState(() {
          _isLoading = true;
        });
      }

      // 获取已提交状态的任务 (samplingStatus = "1")
      final tasks = await _service.getTasksByStatus(1);
      
      if (mounted) {
        setState(() {
          _allTasks = tasks;
          _applyFilters();
        });
      }

      Logger().i('已提交任务列表加载完成，共 ${tasks.length} 条');
      
      // 打印前几个任务的状态信息用于调试
      for (int i = 0; i < tasks.length && i < 3; i++) {
        final task = tasks[i];
        Logger().i('已提交任务${i+1}: taskId=${task.taskId}, samplingStatus=${task.samplingStatus}, submitStatus=${task.submitStatus}');
      }
    } catch (e) {
      Logger().e('加载已提交任务列表失败: $e');
      if (mounted) {
        TDToast.showText('加载失败: $e', context: context);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _applyFilters() {
    _filteredTasks = _allTasks.where((task) {
      // 关键词过滤
      if (_searchKeyword.isNotEmpty) {
        final keyword = _searchKeyword.toLowerCase();
        if (!(task.pointCode?.toLowerCase().contains(keyword) ?? false) &&
            !(task.taskCode?.toLowerCase().contains(keyword) ?? false) &&
            !(task.orgName?.toLowerCase().contains(keyword) ?? false)) {
          return false;
        }
      }

      return true;
    }).toList();

    // 按创建时间倒序排列
    _filteredTasks.sort((a, b) => (b.createdAt).compareTo(a.createdAt));
  }

  void _onSearchChanged(String keyword) {
    setState(() {
      _searchKeyword = keyword;
      _applyFilters();
    });
  }

  void _toggleTaskSelection(int taskId) {
    setState(() {
      if (_selectedTaskIds.contains(taskId)) {
        _selectedTaskIds.remove(taskId);
      } else {
        _selectedTaskIds.add(taskId);
      }
    });
  }

  void _selectAll() {
    setState(() {
      if (_selectedTaskIds.length == _filteredTasks.length) {
        _selectedTaskIds.clear();
      } else {
        _selectedTaskIds.addAll(_filteredTasks.map((task) => task.taskId!));
      }
    });
  }

  // 获取字典标签
  String _getDictLabel(String? code, List<DictNode> options) {
    if (code == null) return '-';
    for (var option in options) {
      if (option.code == code) {
        return option.name ?? code;
      }
    }
    return code;
  }

  // 批量删除已提交任务
  Future<void> _deleteSelectedTasks() async {
    if (!_hasSelection) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.px),
        ),
        elevation: 8,
        title: Row(
          children: [
            Container(
              padding: EdgeInsets.all(8.px),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8.px),
              ),
              child: Icon(
                Icons.delete_forever_outlined,
                color: Colors.red,
                size: 24.px,
              ),
            ),
            SizedBox(width: 12.px),
            const Text('确认删除'),
          ],
        ),
        titleTextStyle: TextStyle(
          fontSize: 18.px,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '确定要删除选中的 ${_selectedTaskIds.length} 个已提交任务吗？',
              style: TextStyle(
                fontSize: 16.px,
                color: const Color(0xFF333333),
              ),
            ),
            SizedBox(height: 12.px),
            Container(
              padding: EdgeInsets.all(12.px),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(8.px),
                border: Border.all(color: Colors.red[200]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: Colors.red[600],
                    size: 16.px,
                  ),
                  SizedBox(width: 8.px),
                  Expanded(
                    child: Text(
                      '删除后数据将无法恢复，请谨慎操作',
                      style: TextStyle(
                        fontSize: 12.px,
                        color: Colors.red[800],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF666666),
              padding: EdgeInsets.symmetric(horizontal: 24.px, vertical: 12.px),
            ),
            child: Text(
              '取消',
              style: TextStyle(
                fontSize: 16.px,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(width: 8.px),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 24.px, vertical: 12.px),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.px),
              ),
              elevation: 0,
            ),
            child: Text(
              '确认删除',
              style: TextStyle(
                fontSize: 16.px,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      // 批量删除
      for (final taskId in _selectedTaskIds) {
        await _service.deleteTask(taskId);
      }

      TDToast.showText('已删除 ${_selectedTaskIds.length} 个任务', context: context);
      _selectedTaskIds.clear();
      _loadSubmittedTasks();
    } catch (e) {
      Logger().e('删除任务失败: $e');
      TDToast.showText('删除失败: $e', context: context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: GestureDetector(
          onTap: () => Navigator.pop(context),
          child: Icon(
            Icons.arrow_back_ios,
            color: const Color(0xFF333333),
            size: 20.px,
          ),
        ),
        title: Text(
          '已提交列表',
          style: TextStyle(
            fontSize: 18.px,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF333333),
          ),
        ),
        centerTitle: true,
      ),
      body: Column(
        children: [
          // 搜索框
          Container(
            color: Colors.white,
            padding: EdgeInsets.all(16.px),
            child: TextField(
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: '搜索样点编号',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.px),
                  borderSide: BorderSide(color: Colors.grey[300]!),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8.px),
                  borderSide: const BorderSide(color: Color(0xFF007AFF)),
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: 16.px, vertical: 12.px),
              ),
            ),
          ),

          // 全选和统计信息
          if (_filteredTasks.isNotEmpty)
            Container(
              color: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 16.px, vertical: 8.px),
              child: Row(
                children: [
                  Checkbox(
                    value: _selectedTaskIds.length == _filteredTasks.length && _filteredTasks.isNotEmpty,
                    onChanged: (value) => _selectAll(),
                    activeColor: const Color(0xFF007AFF),
                  ),
                  Text(
                    '全选',
                    style: TextStyle(
                      fontSize: 14.px,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '已选择 ${_selectedTaskIds.length} / ${_filteredTasks.length}',
                    style: TextStyle(
                      fontSize: 14.px,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          
          // 列表内容
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredTasks.isEmpty
                    ? _buildEmptyState()
                    : RefreshIndicator(
                        onRefresh: _loadSubmittedTasks,
                        child: ListView.builder(
                          padding: EdgeInsets.all(16.px),
                          itemCount: _filteredTasks.length,
                          itemBuilder: (context, index) {
                            final task = _filteredTasks[index];
                            return _buildTaskCard(task);
                          },
                        ),
                      ),
          ),
          
          // 底部操作栏 - 只有删除功能
          if (_hasSelection)
            _buildBottomActionBar(),
        ],
      ),
    );
  }

  Widget _buildTaskCard(SamplingTaskLocal task) {
    final isSelected = _selectedTaskIds.contains(task.taskId);
    
    return Container(
      margin: EdgeInsets.only(bottom: 12.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.px),
        border: isSelected ? Border.all(color: const Color(0xFF007AFF), width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12.px),
          onTap: () => _toggleTaskSelection(task.taskId),
          child: Padding(
            padding: EdgeInsets.all(16.px),
            child: Row(
              children: [
                // 选择框
                Checkbox(
                  value: isSelected,
                  onChanged: (value) => _toggleTaskSelection(task.taskId),
                  activeColor: const Color(0xFF007AFF),
                ),
                
                SizedBox(width: 12.px),
                
                // 任务信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            task.pointCode,
                            style: TextStyle(
                              fontSize: 16.px,
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF333333),
                            ),
                          ),
                          SizedBox(height: 8.px),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8.px, vertical: 4.px),
                            decoration: BoxDecoration(
                              color: Colors.green,
                              borderRadius: BorderRadius.circular(4.px),
                            ),
                            child: Text(
                              '已提交',
                              style: TextStyle(
                                fontSize: 12.px,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // 土地利用类型和组织信息
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8.px, vertical: 4.px),
                            decoration: BoxDecoration(
                              color: const Color(0xFF007AFF).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(4.px),
                            ),
                            child: Text(
                              _getDictLabel(task.pointType, _pointTypeDics),
                              style: TextStyle(
                                fontSize: 12.px,
                                color: const Color(0xFF007AFF),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          SizedBox(width: 8.px),
                          Text(
                            task.orgName ?? '-',
                            style: TextStyle(
                              fontSize: 12.px,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8.px),
                      

                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64.px,
            color: Colors.grey[400],
          ),
          SizedBox(height: 16.px),
          Text(
            '暂无已提交任务',
            style: TextStyle(
              fontSize: 16.px,
              color: Colors.grey[600],
            ),
          ),
          SizedBox(height: 8.px),
          Text(
            '已提交的任务会显示在这里',
            style: TextStyle(
              fontSize: 14.px,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.all(16.px),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      // child: Row(
      //   children: [
      //     Expanded(
      //       child: ElevatedButton.icon(
      //         onPressed: _deleteSelectedTasks,
      //         style: ElevatedButton.styleFrom(
      //           backgroundColor: Colors.red,
      //           foregroundColor: Colors.white,
      //           padding: EdgeInsets.symmetric(vertical: 12.px),
      //           shape: RoundedRectangleBorder(
      //             borderRadius: BorderRadius.circular(8.px),
      //           ),
      //           elevation: 0,
      //         ),
      //         icon: Icon(Icons.delete_forever, size: 20.px),
      //         label: Text(
      //           '删除选中 (${_selectedTaskIds.length})',
      //           style: TextStyle(
      //             fontSize: 16.px,
      //             fontWeight: FontWeight.w600,
      //           ),
      //         ),
      //       ),
      //     ),
      //   ],
      // ),
    );
  }
} 