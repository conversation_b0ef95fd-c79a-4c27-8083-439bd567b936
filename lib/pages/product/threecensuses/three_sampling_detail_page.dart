/*
 * <AUTHOR>
 * @description: 采样详情
 * @date 2025/06/16 08:47:13
*/
import 'dart:async';
import 'package:bdh_smart_agric_app/components/form/bdh_media_picker.dart';
import 'package:bdh_smart_agric_app/database/index.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/three_pointquery_info.dart';
import 'package:bdh_smart_agric_app/utils/request/three_censuses_page_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/tile_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:amap_flutter_location/amap_location_option.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:latlong2/latlong.dart';
import 'package:logger/logger.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class ThreeSamplingDetailPage extends StatefulWidget {
  final int taskId;

  const ThreeSamplingDetailPage({
    super.key,
    required this.taskId,
  });

  @override
  State<ThreeSamplingDetailPage> createState() =>
      _ThreeSamplingDetailPageState();
}

class _ThreeSamplingDetailPageState extends State<ThreeSamplingDetailPage> {
  bool _isLoading = true;
  ThreePointqueryInfo? _detailData;
  final MapController _mapController = MapController();
  List<Marker> _markers = [];
  List<CircleMarker> _circles = []; // 用于显示采样点范围
  final SamplingTaskService _service = SamplingTaskService();

  // 字典数据
  List<DictNode> _landUseTypeDics = [];
  List<DictNode> _samplingTypeDics = [];
  List<DictNode> _yesNoDics = [];
  List<DictNode> _pointTypeDics = [];
  List<DictNode> _sampleTypeDict = [];

  // 表单数据
  String? _selectedLandUseType;
  String? _selectedSamplingType;
  int? _selectedSalineAlkaliFlag;
  String? _selectedPointType;

  // 四个方向的照片
  List<MediaItem> _eastImages = []; // 东
  List<MediaItem> _southImages = []; // 南
  List<MediaItem> _westImages = []; // 西
  List<MediaItem> _northImages = []; // 北
  List<MediaItem> _otherImages = []; // 其他

  // 是否可编辑（根据采样状态判断）
  bool get _isEditable => _detailData?.data?.samplingStatus != "1";

  // 样品袋数据
  List<Map<String, dynamic>> _sampleBags = [];

  // 当前获取的位置信息
  double? _currentSurveyLongitude;
  double? _currentSurveyLatitude;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  // 串行初始化数据，确保字典数据先加载完成
  Future<void> _initializeData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // 先加载字典数据
      await _loadDictionaries();
      // 再加载详情数据
      await _loadDetailData();

      // 字典数据加载完成后，重新渲染界面以正确显示样品类型名称
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      Logger().e('初始化数据失败: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    // 释放样品袋中的TextEditingController资源
    for (final sampleBag in _sampleBags) {
      sampleBag['weightController']?.dispose();
    }
    super.dispose();
  }

  Future<void> _loadDictionaries() async {
    try {
      // 并行加载所有字典数据
      final futures = [
        ThreeCensusesPageService.getDicByKey("soil_land_use_type"),
        ThreeCensusesPageService.getDicByKey("soil_sampling_type"),
        ThreeCensusesPageService.getDicByKey("soil_yes_no"),
        ThreeCensusesPageService.getDicByKey("soil_point_type"),
        ThreeCensusesPageService.getDicByKey("soil_sample_type"),
      ];

      final results = await Future.wait(futures);

      _landUseTypeDics = results[0].data ?? [];
      _samplingTypeDics = results[1].data ?? [];
      _yesNoDics = results[2].data ?? [];
      _pointTypeDics = results[3].data ?? [];
      _sampleTypeDict = results[4].data ?? [];

      Logger().i('字典数据加载完成: ${_sampleTypeDict.length} 个样品类型');

      // 打印样品类型字典内容，用于调试
      for (var dict in _sampleTypeDict) {
        Logger().i('样品类型: ${dict.code} - ${dict.name}');
      }
    } catch (e) {
      Logger().e('加载字典数据失败: $e');
    }
  }

  Future<void> _loadDetailData() async {
    try {
      // 首先尝试从本地数据库加载数据
      final localData = await _service.getTaskDetail(widget.taskId);

      if (localData != null) {
        // 使用本地数据
        Logger().i('从本地数据库加载任务详情: ${widget.taskId}');
        _loadFromLocalData(localData);
      } else {
        // 本地没有数据，从远程API加载
        Logger().i('本地数据不存在，从远程API加载: ${widget.taskId}');
        await _loadFromRemoteAPI();
      }
    } catch (e) {
      Logger().e('加载详情数据失败: $e');
    }
  }

  // 从远程API加载数据
  Future<void> _loadFromRemoteAPI() async {
    var response = await ThreeCensusesPageService.queryByIdInfo({
      "taskId": widget.taskId,
    });

    Logger().i('获取远程详情数据: $response');

    if (response.success == true && response.data != null) {
      _detailData = response;
      _initFormData();
      _generateMapMarker();
      _classifyImages();
    }
  }

  // 从本地数据库数据初始化页面
  void _loadFromLocalData(Map<String, dynamic> localData) {
    final task = localData['task'] as SamplingTaskLocal;
    final images = localData['images'] as List<SamplingTaskImageLocal>;
    final samples = localData['samples'] as List<SamplingTaskSampleLocal>;

    // 将本地数据转换为与远程API相同的格式，以便复用现有的UI逻辑
    final mockRemoteData =
        _convertLocalDataToRemoteFormat(task, images, samples);

    _detailData = mockRemoteData;
    _initFormDataFromLocal(task);
    _generateMapMarker();
    _classifyImagesFromLocal(images);
    _initSampleBagsFromLocal(samples);
  }

  // 将本地数据转换为远程API格式
  ThreePointqueryInfo _convertLocalDataToRemoteFormat(
    SamplingTaskLocal task,
    List<SamplingTaskImageLocal> images,
    List<SamplingTaskSampleLocal> samples,
  ) {
    // 构造与远程API相同的数据结构
    final data = Data(
      taskId: task.taskId,
      taskCode: task.taskCode,
      pointCode: task.pointCode,
      samplingStatus: task.samplingStatus,
      orgName: task.orgName,
      orgCode: task.orgCode,
      longitude: task.longitude,
      latitude: task.latitude,
      landUseType: task.landUseType,
      salineAlkaliFlag: task.salineAlkaliFlag?.toDouble(),
      samplingType: task.samplingType,
      pointType: task.pointType,
      surveyLongitude: task.surveyLongitude,
      surveyLatitude: task.surveyLatitude,
      correct: task.correct,
      samplingPointRange: task.samplingPointRange,
      samplingTaskImage: images
          .map((img) => SamplingTaskImage(
                taskImageId: img.taskImageId,
                imageUrl: img.imageUrl,
                imageType: img.imageType,
                imageTime: img.imageTime,
                imageAngle: img.imageAngle,
                taskId: img.taskId,
              ))
          .toList(),
      samplingTaskSample: samples
          .map((sample) => SamplingTaskSample(
                taskSampleId: sample.taskSampleId,
                sampleCode: sample.sampleCode,
                sampleType: sample.sampleType,
                sampleWeight: sample.sampleWeight,
                taskId: sample.taskId,
              ))
          .toList(),
    );

    return ThreePointqueryInfo(
      success: true,
      data: data,
      msg: '本地数据加载成功',
    );
  }

  // 从本地任务数据初始化表单
  void _initFormDataFromLocal(SamplingTaskLocal task) {
    _selectedLandUseType = task.landUseType;
    _selectedSamplingType = task.samplingType;
    _selectedSalineAlkaliFlag = task.salineAlkaliFlag;
    _selectedPointType = task.pointType;
    _currentSurveyLongitude = task.surveyLongitude;
    _currentSurveyLatitude = task.surveyLatitude;

    Logger().i(
        '从本地数据初始化表单字段: 土地利用类型=$_selectedLandUseType, 采样类型=$_selectedSamplingType, 盐碱地标志=$_selectedSalineAlkaliFlag, 样点类型=$_selectedPointType');
  }

  // 从本地图片数据分类照片
  void _classifyImagesFromLocal(List<SamplingTaskImageLocal> images) {
    _eastImages.clear();
    _southImages.clear();
    _westImages.clear();
    _northImages.clear();
    _otherImages.clear();

    for (var img in images) {
      MediaItem mediaItem = MediaItem(
        url: img.imageUrl,
        type: img.imageUrl.endsWith('.mp4') ? MediaType.video : MediaType.image,
      );

      switch (img.imageType) {
        case 'E':
          _eastImages.add(mediaItem);
          break;
        case 'S':
          _southImages.add(mediaItem);
          break;
        case 'W':
          _westImages.add(mediaItem);
          break;
        case 'N':
          _northImages.add(mediaItem);
          break;
        case 'O':
        default:
          _otherImages.add(mediaItem);
          break;
      }
    }
  }

  // 从本地样品数据初始化样品袋
  void _initSampleBagsFromLocal(List<SamplingTaskSampleLocal> samples) {
    _sampleBags.clear();
    for (int i = 0; i < samples.length; i++) {
      final sample = samples[i];
      _sampleBags.add({
        'id': sample.taskSampleId,
        'sampleCode': sample.sampleCode,
        'sampleType': sample.sampleType,
        'sampleWeight': sample.sampleWeight.toString(),
        'weightController':
            TextEditingController(text: sample.sampleWeight.toString()),
      });
    }
  }

  // 初始化表单数据
  void _initFormData() {
    if (_detailData?.data != null) {
      final data = _detailData!.data!;
      _selectedLandUseType = data.landUseType;
      _selectedSamplingType = data.samplingType;
      _selectedSalineAlkaliFlag = data.salineAlkaliFlag?.toInt();
      _selectedPointType = data.pointType;
      _initSampleBags();

      Logger().i(
          '初始化表单数据完成 - 土地利用类型: $_selectedLandUseType, 采样类型: $_selectedSamplingType, 盐碱地标志: $_selectedSalineAlkaliFlag, 样点类型: $_selectedPointType');
      Logger().i(
          '字典数据 - 土地利用类型字典: ${_landUseTypeDics.length}项, 采样类型字典: ${_samplingTypeDics.length}项, 样点类型字典: ${_pointTypeDics.length}项');
    }
  }

  // 初始化样品袋数据
  void _initSampleBags() {
    _sampleBags.clear();
    if (_detailData?.data?.samplingTaskSample != null) {
      for (int i = 0; i < _detailData!.data!.samplingTaskSample!.length; i++) {
        final sample = _detailData!.data!.samplingTaskSample![i];
        // 使用字典数据的第一个作为默认值
        String defaultSampleType = '1';
        if (_sampleTypeDict.isNotEmpty) {
          defaultSampleType = _sampleTypeDict.first.code ?? '1';
        }

        _sampleBags.add({
          'id': sample.taskSampleId,
          'sampleCode': _generateSampleCode(i + 1),
          'sampleType': sample.sampleType ?? defaultSampleType,
          'sampleWeight': sample.sampleWeight?.toString() ?? '',
          'weightController': TextEditingController(
              text: sample.sampleWeight?.toString() ?? ''),
        });
      }
    }
  }

  // 生成采土袋编号
  String _generateSampleCode(int index) {
    final taskCode = _detailData?.data?.taskCode ?? '';
    return '$taskCode-${index.toString().padLeft(2, '0')}';
  }

  // 分类照片按方向
  void _classifyImages() {
    if (_detailData?.data?.samplingTaskImage != null) {
      _eastImages.clear();
      _southImages.clear();
      _westImages.clear();
      _northImages.clear();
      _otherImages.clear();

      for (var img in _detailData!.data!.samplingTaskImage!) {
        MediaItem mediaItem = MediaItem(
          url: img.imageUrl ?? '',
          type: img.imageUrl?.endsWith('.mp4') == true
              ? MediaType.video
              : MediaType.image,
        );

        switch (img.imageType) {
          case 'E':
            _eastImages.add(mediaItem);
            break;
          case 'S':
            _southImages.add(mediaItem);
            break;
          case 'W':
            _westImages.add(mediaItem);
            break;
          case 'N':
            _northImages.add(mediaItem);
            break;
          case 'O':
          default:
            _otherImages.add(mediaItem);
            break;
        }
      }
    }
  }

  void _generateMapMarker() {
    _markers.clear();
    _circles.clear();

    if (_detailData?.data?.latitude != null &&
        _detailData?.data?.longitude != null) {
      try {
        final double lat = double.parse(_detailData!.data!.latitude.toString());
        final double lng =
            double.parse(_detailData!.data!.longitude.toString());

        // 橙色标记点 - 原始经度纬度
        _markers.add(
          Marker(
            width: 36.px,
            height: 36.px,
            point: LatLng(lat, lng),
            child: Container(
              decoration: BoxDecoration(
                color: const Color(0xFFFF8A00), // 橙色
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: Icon(
                Icons.location_on,
                color: Colors.white,
                size: 20.px,
              ),
            ),
          ),
        );

        // 添加采样点范围圈（蓝色透明度0.3）
        final samplingRange = _detailData!.data!.samplingPointRange?.toDouble() ?? 0.0;
        if (samplingRange > 0) {
          _circles.add(
            CircleMarker(
              point: LatLng(lat, lng),
              radius: samplingRange, // 单位是米
              useRadiusInMeter: true,
              color: Colors.blue.withOpacity(0.3),
              borderColor: Colors.blue.withOpacity(0.7),
              borderStrokeWidth: 2,
            ),
          );
        }

        // 蓝色标记点 - 调查经度纬度（如果存在）
        if (_currentSurveyLongitude != null && _currentSurveyLatitude != null) {
          _markers.add(
            Marker(
              width: 36.px,
              height: 36.px,
              point: LatLng(_currentSurveyLatitude!, _currentSurveyLongitude!),
              child: Container(
                decoration: BoxDecoration(
                  color: const Color(0xFF007AFF), // 蓝色
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 2),
                ),
                child: Icon(
                  Icons.my_location,
                  color: Colors.white,
                  size: 20.px,
                ),
              ),
            ),
          );
        }
      } catch (e) {
        Logger().e('坐标解析错误: $e');
      }
    }
  }

  LatLng _getMapCenter() {
    if (_detailData?.data?.latitude != null &&
        _detailData?.data?.longitude != null) {
      try {
        final double lat = double.parse(_detailData!.data!.latitude.toString());
        final double lng =
            double.parse(_detailData!.data!.longitude.toString());
        return LatLng(lat, lng);
      } catch (e) {
        Logger().e('坐标解析错误: $e');
      }
    }
    return const LatLng(47.05, 132.76);
  }

  // 获取选中的标签文本
  String? _getSelectedLabel(String? value, List<DictNode> options) {
    if (value == null) return null;
    for (var option in options) {
      if (option.code == value) {
        return option.name;
      }
    }
    return value;
  }

  // 显示下拉选择器
  void _showDropdownPicker(
    String title,
    String? selectedValue,
    List<DictNode> options,
    Function(String?) onChanged,
  ) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 300.px,
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(16.px),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('取消'),
                    ),
                    Text(
                      '选择$title',
                      style: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('确定'),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: options.length,
                  itemBuilder: (context, index) {
                    var option = options[index];
                    String? value = option.code;
                    String? label = option.name;
                    return ListTile(
                      title: Text(label ?? ''),
                      trailing: selectedValue == value
                          ? Icon(Icons.check, color: Color(0xFF007AFF))
                          : null,
                      onTap: () {
                        onChanged(value);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 显示是否选择器
  void _showYesNoDropdownPicker(
    String title,
    int? selectedValue,
    Function(int?) onChanged,
  ) {
    final options = [
      {'value': 1, 'label': '是'},
      {'value': 0, 'label': '否'},
    ];

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 200.px,
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(16.px),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('取消'),
                    ),
                    Text(
                      '选择$title',
                      style: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('确定'),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: options.length,
                  itemBuilder: (context, index) {
                    var option = options[index];
                    int value = option['value'] as int;
                    String label = option['label'] as String;
                    return ListTile(
                      title: Text(label),
                      trailing: selectedValue == value
                          ? Icon(Icons.check, color: Color(0xFF007AFF))
                          : null,
                      onTap: () {
                        onChanged(value);
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 保存数据到本地数据库
  Future<void> _saveToLocal() async {
    try {
      // 验证采样袋数据
      if (!_validateSampleBags()) {
        return;
      }

      final service = SamplingTaskService();

      // 检查任务是否存在，如果不存在则先保存完整数据
      final isExists = await service.isTaskExists(widget.taskId);
      if (!isExists) {
        // 如果任务不存在，从当前详情数据创建完整的本地数据
        if (_detailData?.data != null) {
          final currentData = _detailData!.data!;
          final remoteData = {
            'taskId': widget.taskId,
            'taskCode': currentData.taskCode ?? '',
            'pointCode': currentData.pointCode ?? '',
            'samplingStatus': '8', // 保存后设置为未提交状态
            'orgName': currentData.orgName ?? '',
            'orgCode': currentData.orgCode ?? '',
            'longitude': currentData.longitude ?? '',
            'latitude': currentData.latitude ?? '',
            'landUseType': _selectedLandUseType,
            'salineAlkaliFlag': _selectedSalineAlkaliFlag,
            'samplingType': _selectedSamplingType,
            'pointType': _selectedPointType,
            'surveyLongitude':
                _currentSurveyLongitude ?? currentData.surveyLongitude,
            'surveyLatitude':
                _currentSurveyLatitude ?? currentData.surveyLatitude,
            'correct': currentData.correct,
            'submitStatus': 0, // 设置为未提交
            'samplingTaskImage': _buildImageDataForSave(),
            'samplingTaskSample': _buildSampleDataForSave(),
          };

          await service.saveRemoteDataToLocal(remoteData);
        }
      } else {
        // 任务已存在，更新表单数据和状态
        final formData = {
          'landUseType': _selectedLandUseType,
          'samplingType': _selectedSamplingType,
          'salineAlkaliFlag': _selectedSalineAlkaliFlag,
          'pointType': _selectedPointType,
          'surveyLongitude': _currentSurveyLongitude,
          'surveyLatitude': _currentSurveyLatitude,
          // 移除这里的 samplingStatus 设置，避免与后面的 updateTaskToUnsubmitted 冲突
        };

        await service.updateTaskFormData(widget.taskId, formData);

        // 保存图片数据（如果有新增或修改）
        await _saveImageData(service);

        // 保存样品数据
        await _saveSampleData(service);

        // 更新任务状态为未提交状态（同时设置 samplingStatus = '8' 和 submitStatus = 0）
        await service.updateTaskToUnsubmitted(widget.taskId);
      }

      Logger().i('保存采样数据到本地数据库成功: ${widget.taskId}');
      TDToast.showText('采样数据已保存，状态已更新为未提交', context: context);

      // 返回true表示保存成功，通知上级页面刷新
      Navigator.pop(context, true);
    } catch (e) {
      Logger().e('保存采样数据到本地数据库失败: $e');
      TDToast.showText('保存失败: $e', context: context);
    }
  }

  // 保存图片数据
  Future<void> _saveImageData(SamplingTaskService service) async {
    try {
      // 直接按任务ID删除所有现有图片，确保数据按任务编号匹配
      await service.deleteImagesByTaskId(widget.taskId);

      final List<Map<String, String>> allImages = [];

      // 收集所有方向的图片，每个方向只取第一张（最新的）
      if (_eastImages.isNotEmpty) {
        allImages.add({'url': _eastImages.first.url, 'type': 'E'});
      }
      if (_southImages.isNotEmpty) {
        allImages.add({'url': _southImages.first.url, 'type': 'S'});
      }
      if (_westImages.isNotEmpty) {
        allImages.add({'url': _westImages.first.url, 'type': 'W'});
      }
      if (_northImages.isNotEmpty) {
        allImages.add({'url': _northImages.first.url, 'type': 'N'});
      }
      if (_otherImages.isNotEmpty) {
        allImages.add({'url': _otherImages.first.url, 'type': 'O'});
      }

      // 如果有图片数据则批量保存
      if (allImages.isNotEmpty) {
        await service.addTaskImages(widget.taskId, allImages);
      }

      Logger()
          .i('任务 ${widget.taskId} 图片数据保存完成: ${allImages.length} 张 (每个方向最多1张)');

      // 打印每个方向的图片信息
      for (final image in allImages) {
        Logger()
            .i('任务 ${widget.taskId} 保存${image['type']}方向图片: ${image['url']}');
      }
    } catch (e) {
      Logger().e('保存图片数据失败: $e');
      rethrow;
    }
  }

  // 保存样品数据
  Future<void> _saveSampleData(SamplingTaskService service) async {
    try {
      // 直接按任务ID删除所有现有样品，确保数据按任务编号匹配
      await service.deleteSamplesByTaskId(widget.taskId);

      // 保存样品袋数据
      for (int i = 0; i < _sampleBags.length; i++) {
        final sampleBag = _sampleBags[i];
        await service.addTaskSample(
          widget.taskId,
          sampleBag['sampleCode'],
          sampleBag['sampleType'],
          double.tryParse(sampleBag['sampleWeight']) ?? 0.0,
        );
      }

      Logger().i('任务 ${widget.taskId} 样品数据保存完成: ${_sampleBags.length} 个');
    } catch (e) {
      Logger().e('保存样品数据失败: $e');
      rethrow;
    }
  }

  // 构建图片数据用于保存
  List<Map<String, dynamic>> _buildImageDataForSave() {
    final List<Map<String, dynamic>> imageData = [];

    // 收集所有方向的图片，每个方向只取第一张（与 _saveImageData 保持一致）
    int imageId = DateTime.now().millisecondsSinceEpoch;

    // 东方向 - 只取第一张
    if (_eastImages.isNotEmpty) {
      imageData.add({
        'taskImageId': imageId++,
        'imageUrl': _eastImages.first.url,
        'imageType': 'E',
        'imageTime': DateTime.now().millisecondsSinceEpoch,
        'taskId': widget.taskId,
      });
    }

    // 南方向 - 只取第一张
    if (_southImages.isNotEmpty) {
      imageData.add({
        'taskImageId': imageId++,
        'imageUrl': _southImages.first.url,
        'imageType': 'S',
        'imageTime': DateTime.now().millisecondsSinceEpoch,
        'taskId': widget.taskId,
      });
    }

    // 西方向 - 只取第一张
    if (_westImages.isNotEmpty) {
      imageData.add({
        'taskImageId': imageId++,
        'imageUrl': _westImages.first.url,
        'imageType': 'W',
        'imageTime': DateTime.now().millisecondsSinceEpoch,
        'taskId': widget.taskId,
      });
    }

    // 北方向 - 只取第一张
    if (_northImages.isNotEmpty) {
      imageData.add({
        'taskImageId': imageId++,
        'imageUrl': _northImages.first.url,
        'imageType': 'N',
        'imageTime': DateTime.now().millisecondsSinceEpoch,
        'taskId': widget.taskId,
      });
    }

    // 其他方向 - 只取第一张
    if (_otherImages.isNotEmpty) {
      imageData.add({
        'taskImageId': imageId++,
        'imageUrl': _otherImages.first.url,
        'imageType': 'O',
        'imageTime': DateTime.now().millisecondsSinceEpoch,
        'taskId': widget.taskId,
      });
    }

    return imageData;
  }

  // 构建样品数据用于保存
  List<Map<String, dynamic>> _buildSampleDataForSave() {
    final List<Map<String, dynamic>> sampleData = [];

    // 从样品袋数据构建
    for (int i = 0; i < _sampleBags.length; i++) {
      final sampleBag = _sampleBags[i];
      sampleData.add({
        'taskSampleId':
            sampleBag['id'] ?? DateTime.now().millisecondsSinceEpoch + i,
        'sampleCode': sampleBag['sampleCode'],
        'sampleType': sampleBag['sampleType'],
        'sampleWeight': double.tryParse(sampleBag['sampleWeight']) ?? 0.0,
        'taskId': widget.taskId,
      });
    }

    return sampleData;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          child: Icon(
            Icons.arrow_back_ios,
            color: const Color(0xFF333333),
            size: 20.px,
          ),
        ),
        title: Text(
          '采样点详情',
          style: TextStyle(
            fontSize: 18.px,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF333333),
          ),
        ),
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _detailData?.data == null
              ? const Center(child: Text('暂无数据'))
              : Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.all(16.px),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 基本信息卡片
                            _buildInfoCard(),
                            SizedBox(height: 16.px),

                            // 地图
                            _buildMapSection(),
                            SizedBox(height: 16.px),

                            // 四个方向的照片
                            _buildDirectionalPhotosSection(),
                            SizedBox(height: 16.px),

                            // 样品袋信息
                            _buildSampleBagsSection(),
                          ],
                        ),
                      ),
                    ),

                    // 底部保存按钮（仅在可编辑状态下显示）
                    if (_isEditable)
                      Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(16.px),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 4,
                              offset: const Offset(0, -2),
                            ),
                          ],
                        ),
                        child: ElevatedButton(
                          onPressed: _saveToLocal,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF007AFF),
                            padding: EdgeInsets.symmetric(vertical: 12.px),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.px),
                            ),
                          ),
                          child: Text(
                            '保存',
                            style: TextStyle(
                              fontSize: 16.px,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
    );
  }

  Widget _buildInfoCard() {
    final data = _detailData!.data!;
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.px),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '基本信息',
            style: TextStyle(
              fontSize: 18.px,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.px),
          _buildInfoRow('采样点编号', data.pointCode ?? '-'),
          _buildInfoRow('组织名称', data.orgName ?? '-'),
          _buildInfoRow('任务编号', data.taskCode ?? '-'),
          _buildInfoRow('采样状态', data.samplingStatus == "1" ? '已采样' : '未采样'),

          // 可编辑的字段
          _buildEditableDropdown(
              '土地利用类型', _selectedLandUseType, _landUseTypeDics, (value) {
            setState(() {
              _selectedLandUseType = value;
            });
          }),
          _buildEditableDropdown(
              '采样类型', _selectedSamplingType, _samplingTypeDics, (value) {
            setState(() {
              _selectedSamplingType = value;
            });
          }),
          _buildEditableDropdown('样点类型', _selectedPointType, _pointTypeDics,
              (value) {
            setState(() {
              _selectedPointType = value;
            });
          }),
          _buildEditableYesNoDropdown('是否盐碱地', _selectedSalineAlkaliFlag,
              (value) {
            setState(() {
              _selectedSalineAlkaliFlag = value;
            });
          }),

          _buildInfoRow('经度', data.longitude ?? '-'),
          _buildInfoRow('纬度', data.latitude ?? '-'),
          // 显示调查位置（优先显示当前获取的位置）
          _buildInfoRow('调查经度', _currentSurveyLongitude?.toString() ?? '-'),
          _buildInfoRow('调查纬度', _currentSurveyLatitude?.toString() ?? '-'),

          // 获取当前位置按钮
          if (_isEditable) ...[
            SizedBox(height: 16.px),
            Container(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _getCurrentLocation,
                style: OutlinedButton.styleFrom(
                  foregroundColor: const Color(0xFF007AFF),
                  side: const BorderSide(color: Color(0xFF007AFF)),
                  padding: EdgeInsets.symmetric(vertical: 12.px),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.px),
                  ),
                ),
                icon: Icon(Icons.my_location, size: 20.px),
                label: Text(
                  '获取当前位置',
                  style: TextStyle(
                    fontSize: 16.px,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.px),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.px,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.px,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.px,
                color: const Color(0xFF333333),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditableDropdown(
    String label,
    String? selectedValue,
    List<DictNode> options,
    Function(String?) onChanged,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.px),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.px,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.px,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: _isEditable
                ? GestureDetector(
                    onTap: () => _showDropdownPicker(
                        label, selectedValue, options, onChanged),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 12.px, vertical: 8.px),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.px),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              _getSelectedLabel(selectedValue, options) ??
                                  '请选择',
                              style: TextStyle(
                                fontSize: 14.px,
                                color: selectedValue != null
                                    ? const Color(0xFF333333)
                                    : Colors.grey[400],
                              ),
                            ),
                          ),
                          Icon(Icons.keyboard_arrow_down,
                              size: 20.px, color: Colors.grey[400]),
                        ],
                      ),
                    ),
                  )
                : Text(
                    _getSelectedLabel(selectedValue, options) ?? '-',
                    style: TextStyle(
                      fontSize: 14.px,
                      color: const Color(0xFF333333),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditableYesNoDropdown(
    String label,
    int? selectedValue,
    Function(int?) onChanged,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.px),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.px,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.px,
                color: Colors.grey[600],
              ),
            ),
          ),
          Expanded(
            child: _isEditable
                ? GestureDetector(
                    onTap: () => _showYesNoDropdownPicker(
                        label, selectedValue, onChanged),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 12.px, vertical: 8.px),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[300]!),
                        borderRadius: BorderRadius.circular(4.px),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              selectedValue == null
                                  ? '请选择'
                                  : (selectedValue == 1 ? '是' : '否'),
                              style: TextStyle(
                                fontSize: 14.px,
                                color: selectedValue != null
                                    ? const Color(0xFF333333)
                                    : Colors.grey[400],
                              ),
                            ),
                          ),
                          Icon(Icons.keyboard_arrow_down,
                              size: 20.px, color: Colors.grey[400]),
                        ],
                      ),
                    ),
                  )
                : Text(
                    selectedValue == null
                        ? '-'
                        : (selectedValue == 1 ? '是' : '否'),
                    style: TextStyle(
                      fontSize: 14.px,
                      color: const Color(0xFF333333),
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapSection() {
    return Container(
      height: 200.px,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.px),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.px),
        child: FlutterMap(
          mapController: _mapController,
          options: MapOptions(
            initialZoom: 15,
            initialCenter: _getMapCenter(),
          ),
          children: [
            TileLayerUtil.tileLayer(TianDiTuType.bdh),
            TileLayerUtil.tileLayer(TianDiTuType.cia),
            CircleLayer(circles: _circles),
            MarkerLayer(markers: _markers),
          ],
        ),
      ),
    );
  }

  Widget _buildDirectionalPhotosSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.px),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '采样照片',
            style: TextStyle(
              fontSize: 18.px,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.px),

          // 东
          _buildDirectionalPhotoRow('东', _eastImages, 'E'),
          SizedBox(height: 12.px),

          // 南
          _buildDirectionalPhotoRow('南', _southImages, 'S'),
          SizedBox(height: 12.px),

          // 西
          _buildDirectionalPhotoRow('西', _westImages, 'W'),
          SizedBox(height: 12.px),

          // 北
          _buildDirectionalPhotoRow('北', _northImages, 'N'),
          SizedBox(height: 12.px),

          // 其他
          _buildDirectionalPhotoRow('其他', _otherImages, 'O'),
        ],
      ),
    );
  }

  Widget _buildDirectionalPhotoRow(
      String direction, List<MediaItem> images, String imageType) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$direction方向',
          style: TextStyle(
            fontSize: 14.px,
            fontWeight: FontWeight.w500,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(height: 8.px),
        // 使用Key来强制重建组件，避免热重启问题
        BdhMediaPicker(
          key: ValueKey(
              '${direction}_media_picker_${widget.taskId}_${images.length}'),
          item: FormItem(title: "$direction方向照片"),
          showTitle: false,
          maxCount: 1,
          initialValue: images,
          allowVideo: true,
          allowImage: true,
          autoUpload: false,
          // 默认为true，自动上传到服务器
          onChange: _isEditable
              ? (urls) {
                  Logger().i('$direction方向照片onChange回调: $urls');
                  if (urls != null) {
                    // 打印详细的文件信息
                    for (int i = 0; i < urls.length; i++) {
                      final url = urls[i];
                      final isNetworkUrl = url.startsWith('http');
                      Logger().i('$direction方向第${i + 1}个文件:');
                      Logger().i('  - URL: $url');
                      Logger().i('  - 类型: ${isNetworkUrl ? "网络图片" : "本地文件"}');
                      Logger().i('  - 是否为视频: ${url.endsWith('.mp4')}');

                      if (!isNetworkUrl) {
                        Logger().i('  - 本地文件路径可用于后续上传: $url');
                      }
                    }

                    setState(() {
                      // 只清空当前方向的图片，避免重复
                      switch (imageType) {
                        case 'E':
                          _eastImages.clear();
                          _eastImages.addAll(urls.map((url) => MediaItem(
                                url: url,
                                type: url.endsWith('.mp4')
                                    ? MediaType.video
                                    : MediaType.image,
                              )));
                          break;
                        case 'S':
                          _southImages.clear();
                          _southImages.addAll(urls.map((url) => MediaItem(
                                url: url,
                                type: url.endsWith('.mp4')
                                    ? MediaType.video
                                    : MediaType.image,
                              )));
                          break;
                        case 'W':
                          _westImages.clear();
                          _westImages.addAll(urls.map((url) => MediaItem(
                                url: url,
                                type: url.endsWith('.mp4')
                                    ? MediaType.video
                                    : MediaType.image,
                              )));
                          break;
                        case 'N':
                          _northImages.clear();
                          _northImages.addAll(urls.map((url) => MediaItem(
                                url: url,
                                type: url.endsWith('.mp4')
                                    ? MediaType.video
                                    : MediaType.image,
                              )));
                          break;
                        case 'O':
                        default:
                          _otherImages.clear();
                          _otherImages.addAll(urls.map((url) => MediaItem(
                                url: url,
                                type: url.endsWith('.mp4')
                                    ? MediaType.video
                                    : MediaType.image,
                              )));
                          break;
                      }
                    });
                  }
                }
              : null,
        ),
      ],
    );
  }

  Widget _buildSampleBagsSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.px),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '样品袋信息',
                style: TextStyle(
                  fontSize: 18.px,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF333333),
                ),
              ),
              if (_isEditable)
                GestureDetector(
                  onTap: _addSampleBag,
                  child: Container(
                    width: 32.px,
                    height: 32.px,
                    decoration: BoxDecoration(
                      color: const Color(0xFF007AFF),
                      borderRadius: BorderRadius.circular(16.px),
                    ),
                    child: Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 20.px,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 16.px),
          if (_sampleBags.isNotEmpty)
            ..._sampleBags
                .asMap()
                .entries
                .map((entry) => _buildSampleBagCard(entry.key, entry.value))
          else
            Container(
              height: 80.px,
              alignment: Alignment.center,
              child: Text(
                '暂无样品袋信息',
                style: TextStyle(
                  fontSize: 14.px,
                  color: Colors.grey[600],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSampleBagCard(int index, Map<String, dynamic> sampleBag) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.px),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Dismissible(
        key: Key('sample_bag_${sampleBag['id']}_$index'),
        direction:
            _isEditable ? DismissDirection.endToStart : DismissDirection.none,
        confirmDismiss: (direction) async {
          if (!_isEditable) return false;
          return await _confirmDeleteSampleBag(index);
        },
        onDismissed: (direction) {
          _deleteSampleBag(index);
        },
        background: Container(
          alignment: Alignment.centerRight,
          padding: EdgeInsets.only(right: 16.px),
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(8.px),
          ),
          child: Icon(
            Icons.delete,
            color: Colors.white,
            size: 24.px,
          ),
        ),
        child: GestureDetector(
          onLongPress:
              _isEditable ? () => _showDeleteSampleBagDialog(index) : null,
          child: Container(
            padding: EdgeInsets.all(12.px),
            child: Column(
              children: [
                // 采土袋编号
                Row(
                  children: [
                    SizedBox(
                      width: 80.px,
                      child: Text(
                        '采土袋编号',
                        style: TextStyle(
                          fontSize: 14.px,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Text(
                        sampleBag['sampleCode'],
                        style: TextStyle(
                          fontSize: 14.px,
                          color: const Color(0xFF333333),
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 8.px),

                // 样品类型
                Row(
                  children: [
                    SizedBox(
                      width: 80.px,
                      child: Text(
                        '样品类型',
                        style: TextStyle(
                          fontSize: 14.px,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    Expanded(
                      child: _isEditable
                          ? GestureDetector(
                              onTap: () => _showSampleTypeSelector(index),
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 8.px, vertical: 6.px),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey[300]!),
                                  borderRadius: BorderRadius.circular(4.px),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      _getSampleTypeText(
                                          sampleBag['sampleType']),
                                      style: TextStyle(
                                        fontSize: 14.px,
                                        color: const Color(0xFF333333),
                                      ),
                                    ),
                                    Icon(Icons.keyboard_arrow_down,
                                        size: 16.px, color: Colors.grey[400]),
                                  ],
                                ),
                              ),
                            )
                          : Text(
                              _getSampleTypeText(sampleBag['sampleType']),
                              style: TextStyle(
                                fontSize: 14.px,
                                color: const Color(0xFF333333),
                              ),
                            ),
                    ),
                  ],
                ),
                SizedBox(height: 8.px),

                // 重量
                Row(
                  children: [
                    SizedBox(
                      width: 80.px,
                      child: Text(
                        '重量 (g)',
                        style: TextStyle(
                          fontSize: 14.px,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    Expanded(
                      child: _isEditable
                          ? TextField(
                              controller: sampleBag['weightController'],
                              keyboardType: TextInputType.number,
                              decoration: InputDecoration(
                                hintText: '请输入',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4.px),
                                  borderSide:
                                      BorderSide(color: Colors.grey[300]!),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(4.px),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF007AFF)),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 8.px, vertical: 6.px),
                              ),
                              style: TextStyle(fontSize: 14.px),
                              onChanged: (value) {
                                sampleBag['sampleWeight'] = value;
                              },
                            )
                          : Text(
                              sampleBag['sampleWeight'].isEmpty
                                  ? '-'
                                  : '${sampleBag['sampleWeight']}g',
                              style: TextStyle(
                                fontSize: 14.px,
                                color: const Color(0xFF333333),
                              ),
                            ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 添加样品袋
  void _addSampleBag() {
    if (!_isEditable) return;

    setState(() {
      final newIndex = _sampleBags.length + 1;
      // 使用字典数据的第一个作为默认值
      String defaultSampleType = '1';
      if (_sampleTypeDict.isNotEmpty) {
        defaultSampleType = _sampleTypeDict.first.code ?? '1';
      }

      _sampleBags.add({
        'id': DateTime.now().millisecondsSinceEpoch,
        'sampleCode': _generateSampleCode(newIndex),
        'sampleType': defaultSampleType,
        'sampleWeight': '',
        'weightController': TextEditingController(),
      });
    });
  }

  // 删除样品袋
  void _deleteSampleBag(int index) {
    if (!_isEditable || index < 0 || index >= _sampleBags.length) return;

    setState(() {
      // 释放TextEditingController资源
      _sampleBags[index]['weightController']?.dispose();
      _sampleBags.removeAt(index);

      // 重新生成所有样品袋编号
      for (int i = 0; i < _sampleBags.length; i++) {
        _sampleBags[i]['sampleCode'] = _generateSampleCode(i + 1);
      }
    });
  }

  // 显示删除确认对话框
  Future<void> _showDeleteSampleBagDialog(int index) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4.px),
        ),
        title: const Text('确认删除'),
        content: Text('确定要删除采土袋 "${_sampleBags[index]['sampleCode']}" 吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('删除', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      _deleteSampleBag(index);
    }
  }

  // 确认左滑删除
  Future<bool> _confirmDeleteSampleBag(int index) async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            backgroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4.px),
            ),
            title: const Text('确认删除'),
            content: Text('确定要删除采土袋 "${_sampleBags[index]['sampleCode']}" 吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: const Text('删除', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
        ) ??
        false;
  }

  // 显示样品类型选择器
  void _showSampleTypeSelector(int index) {
    if (!_isEditable) return;

    // 使用字典数据
    if (_sampleTypeDict.isEmpty) {
      Logger().w('样品类型字典数据为空，无法显示选择器');
      return;
    }

    final options = _sampleTypeDict
        .map((dict) => {
              'value': dict.code ?? '',
              'label': dict.name ?? '',
            })
        .toList();

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return Container(
          height: 200.px,
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(16.px),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('取消'),
                    ),
                    Text(
                      '选择样品类型',
                      style: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: Text('确定'),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: options.length,
                  itemBuilder: (context, i) {
                    var option = options[i];
                    String value = option['value']!;
                    String label = option['label']!;
                    return ListTile(
                      title: Text(label),
                      trailing: _sampleBags[index]['sampleType'] == value
                          ? Icon(Icons.check, color: Color(0xFF007AFF))
                          : null,
                      onTap: () {
                        setState(() {
                          _sampleBags[index]['sampleType'] = value;
                        });
                        Navigator.pop(context);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  String _getSampleTypeText(String? type) {
    if (type == null || type.isEmpty) return '-';

    // 首先尝试使用字典数据
    if (_sampleTypeDict.isNotEmpty) {
      final dictItem = _sampleTypeDict.firstWhere(
        (item) => item.code == type,
        orElse: () => DictNode(code: null, name: null),
      );

      if (dictItem.name != null && dictItem.name!.isNotEmpty) {
        return dictItem.name!;
      }
    }

    return type;
  }

  // 验证采样袋数据和必填字段
  bool _validateSampleBags() {
    // 验证调查经纬度
    if (_currentSurveyLongitude == null || _currentSurveyLatitude == null) {
      _showValidationDialog('调查经纬度是必填项，请点击"获取当前位置"按钮获取位置信息');
      return false;
    }

    // 验证采样袋
    if (_sampleBags.isEmpty) {
      _showValidationDialog('采样袋信息是必填项，请至少添加一个采样袋');
      return false;
    }

    for (int i = 0; i < _sampleBags.length; i++) {
      final sampleBag = _sampleBags[i];

      // 检查重量是否为空或无效
      final weightText = sampleBag['sampleWeight'] as String;
      if (weightText.isEmpty) {
        _showValidationDialog('第${i + 1}个采样袋的重量不能为空，这是必填项');
        return false;
      }

      final weight = double.tryParse(weightText);
      if (weight == null || weight <= 0) {
        _showValidationDialog('第${i + 1}个采样袋的重量必须为有效的正数（大于0）');
        return false;
      }
    }

    return true;
  }

  // 显示验证错误对话框
  void _showValidationDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.px),
        ),
        title: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.orange,
              size: 24.px,
            ),
            SizedBox(width: 8.px),
            const Text('验证失败'),
          ],
        ),
        titleTextStyle: TextStyle(
          fontSize: 18.px,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
        content: Text(
          message,
          style: TextStyle(
            fontSize: 16.px,
            color: const Color(0xFF333333),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF007AFF),
              padding: EdgeInsets.symmetric(horizontal: 24.px, vertical: 12.px),
            ),
            child: Text(
              '知道了',
              style: TextStyle(
                fontSize: 16.px,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 获取当前位置并更新surveyLongitude和surveyLatitude
  Future<void> _getCurrentLocation() async {
    try {
      TDToast.showText('正在获取当前位置...', context: context);

      // 检查定位权限
      var status = await Permission.location.status;
      if (status.isDenied) {
        status = await Permission.location.request();
        if (status.isDenied) {
          TDToast.showText('定位权限被拒绝，无法获取位置', context: context);
          return;
        }
      }

      if (status.isPermanentlyDenied) {
        TDToast.showText('定位权限被永久拒绝，请在设置中开启', context: context);
        return;
      }

      // 设置高德地图API Key
      AMapFlutterLocation.setApiKey(
          "cb174d5f4d268a8a72cf54b7b6c724f2", "47ac324f0fda3f062e8cf85d90ae7a8d");
      AMapFlutterLocation.updatePrivacyShow(true, true);
      AMapFlutterLocation.updatePrivacyAgree(true);

      // 创建定位插件实例
      AMapFlutterLocation locationPlugin = AMapFlutterLocation();

      // 设置定位参数
      AMapLocationOption locationOption = AMapLocationOption();
      locationOption.onceLocation = true; // 单次定位
      locationOption.needAddress = false; // 不需要地址信息
      locationOption.geoLanguage = GeoLanguage.DEFAULT;
      locationOption.locationMode = AMapLocationMode.Hight_Accuracy; // 高精度模式
      locationOption.desiredAccuracy = DesiredAccuracy.Best; // 最高精度
      locationOption.locationInterval = 2000; // 定位间隔

      locationPlugin.setLocationOption(locationOption);

      // 设置超时
      bool locationReceived = false;
      Timer? timeoutTimer = Timer(const Duration(seconds: 10), () {
        if (!locationReceived) {
          locationReceived = true;
          locationPlugin.stopLocation();
          TDToast.showText('获取位置超时，请检查GPS信号', context: context);
        }
      });

      // 监听定位结果
      locationPlugin.onLocationChanged().listen((Map<String, Object> result) async {
        if (locationReceived) return;
        locationReceived = true;
                 timeoutTimer.cancel();
        
                 try {
           locationPlugin.stopLocation();
          
          if (result['errorCode'] != null) {
            String errorCode = result['errorCode'].toString();
            Logger().e('定位错误代码: $errorCode');
            TDToast.showText('定位失败，错误代码: $errorCode', context: context);
            return;
          }

          // 解析经纬度
          double? latitude;
          double? longitude;
          
          if (result["latitude"] != null && result["longitude"] != null) {
            // Android和iOS的数据类型可能不同
            latitude = double.tryParse(result["latitude"].toString());
            longitude = double.tryParse(result["longitude"].toString());
          }

          if (latitude != null && longitude != null) {
            // 更新调查经纬度（使用原始GPS坐标）
            _currentSurveyLongitude = double.parse(longitude.toStringAsFixed(6));
            _currentSurveyLatitude = double.parse(latitude.toStringAsFixed(6));

            // 更新UI并重新生成地图标记
            setState(() {
              _generateMapMarker(); // 重新生成地图标记，包括蓝色调查位置点
            });

            TDToast.showText('位置获取成功', context: context);
            Logger().i('GPS位置获取成功: 经度=$_currentSurveyLongitude, 纬度=$_currentSurveyLatitude');
          } else {
            throw Exception('无法解析位置数据');
          }
        } catch (e) {
          Logger().e('处理定位结果失败: $e');
          TDToast.showText('处理位置数据失败', context: context);
        }
      });

      // 开始定位
      locationPlugin.startLocation();

    } catch (e) {
      Logger().e('获取当前位置失败: $e');
      String errorMessage = '获取位置失败';
      
      if (e.toString().contains('timeout') || e.toString().contains('超时')) {
        errorMessage = '获取位置超时，请检查GPS信号';
      } else if (e.toString().contains('permission') || e.toString().contains('权限')) {
        errorMessage = '定位权限被拒绝，请允许定位权限';
      } else if (e.toString().contains('service') || e.toString().contains('服务')) {
        errorMessage = '定位服务不可用，请检查设置';
      }
      
      TDToast.showText(errorMessage, context: context);
    }
  }
}
