import 'dart:convert';
import 'dart:typed_data';

import 'package:bdh_smart_agric_app/components/bdh_weather_address_view.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/SessionEntity.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/chat_message_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/history_message_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/question_dictionary_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/question_record_entity.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:oktoast/oktoast.dart';

import '../../../components/bdh_network_image.dart';
import '../../../const/url_config_const.dart';
import '../../../utils/request/chat_repository.dart';

class ChatMainPage extends StatefulWidget {
  const ChatMainPage({super.key});

  @override
  State<ChatMainPage> createState() => _ChatMainPageState();
}

class _ChatMainPageState extends State<ChatMainPage>
    with WidgetsBindingObserver {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  final TextEditingController _textEditingController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();

  List<QuestionRecordEntity> questionRecordList = [];
  List<QuestionDictionaryEntity> questionDictionaryList = [];

  String currentResult = "";

  List<SessionEntity> sessionList = [];
  bool isKeyborderShow = false;
  bool isInputWord = false;
  String currentTaskId = "";
  String currentConversationId = ""; //当前会话ID 首次为空, 二次问题填入
  String currentQuestion = ""; //当前问题

  bool isShowNewSession = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    queryQuestionDictionary();
    getHistoryRecord();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    WidgetsBinding.instance.removeObserver(this);
    stopCurrentSession();
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final Size size = WidgetsBinding.instance.window.physicalSize;
    final double scale = WidgetsBinding.instance.window.devicePixelRatio;
    final insets = WidgetsBinding.instance.window.viewInsets;
    final bool showKeyboard = insets.bottom > 0; // 判断底部insets是否大于0来判断软键盘是否弹出
    if (showKeyboard != isKeyborderShow) {
      setState(() {
        isKeyborderShow = showKeyboard;
        if (isKeyborderShow) {
          // _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
          //   _scrollController.position.extentTotal,
          //   duration: Duration(milliseconds: 200), // 持续时间可以根据需要调整
          //   curve: Curves.easeInOut, // 动画曲线，可以根据需要调整
          // );
          if (sessionList.isNotEmpty) {
            // 延时执行滚动到底部操作
            Future.delayed(const Duration(milliseconds: 100), () {
              _scrollController
                  .jumpTo(_scrollController.position.maxScrollExtent);
            });
          }
        }
      });
      print('Keyboard is now ${isKeyborderShow ? 'visible' : 'hidden'}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: Drawer(
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        width: 248.px,
        elevation: 3,
        child: _buildLeftDrawer(),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: isShowNewSession
          ? Padding(
              padding: EdgeInsets.only(bottom: 64.px),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  startNewChatMessage();
                },
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(255, 255, 255, 0.8),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: const Color.fromRGBO(255, 255, 255, 1),
                      width: 1,
                    ),
                  ),
                  width: 132.px,
                  height: 32.px,
                  child: const Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        ImageIcon(
                          AssetImage('assets/images/chat/ic_new_chat.png'),
                          size: 24,
                          color: Color.fromRGBO(75, 98, 130, 1),
                        ),
                        SizedBox(width: 5),
                        Text(
                          "开启新对话",
                          style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Color.fromRGBO(111, 126, 152, 1)),
                        ),
                      ]),
                ),
              ),
            )
          : Padding(
              padding: EdgeInsets.only(bottom: 64.px),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/images/chat/ic_deepseek.png',
                    color: const Color.fromRGBO(0, 0, 0, 0.4),
                    width: 24.px,
                    height: 24.px,
                  ),
                  Text(
                    " Deepseek",
                    style: TextStyle(
                        fontSize: 14.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(0, 0, 0, 0.5)),
                  ),
                ],
              ),
            ),
      body: Container(
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/images/chat/bg_chat.png'),
              fit: BoxFit.cover,
            ),
          ),
          child: Column(children: [
            SizedBox(
              height: 48.px,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 20.px,
                ),
                GestureDetector(
                  onTap: () {
                    _scaffoldKey.currentState?.openDrawer();
                    Future.delayed(const Duration(milliseconds: 100), () {
                      FocusScope.of(context).unfocus();
                    });
                  },
                  behavior: HitTestBehavior.opaque,
                  child: const ImageIcon(
                    AssetImage('assets/images/chat/ic_draw_left.png'),
                    size: 24,
                    color: Colors.black,
                  ),
                ),
                SizedBox(width: 10.px),
                const Text('北大荒Chat',
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black)),
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Icon(
                    Icons.close,
                    size: 24.px,
                    color: const Color.fromRGBO(7, 44, 29, 1),
                  ),
                ),
                SizedBox(width: 10.px),
              ],
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  if (isKeyborderShow) {
                    FocusScope.of(context).requestFocus(FocusNode());
                  }
                },
                child: Container(
                  padding: EdgeInsets.only(bottom: 20.px, top: 20.px),
                  child: Column(
                    children: [
                      Container(
                        padding: EdgeInsets.only(
                            left: 12.px, right: 12.px, bottom: 0.px),
                        child: questionDictionaryList.isNotEmpty
                            ? BdhWeatherAddressView(
                                onlyShowAddress: sessionList.isNotEmpty,
                              )
                            : Container(),
                      ),
                      Expanded(
                          child: sessionList.isEmpty
                              ? ListView(
                                  padding: EdgeInsets.only(top: 0.px),
                                  children: [
                                    _buildTipView(),
                                  ],
                                )
                              : Stack(
                                  children: [
                                    ListView.builder(
                                        padding: EdgeInsets.only(bottom: 20.px),
                                        controller: _scrollController,
                                        itemCount: sessionList.length,
                                        itemBuilder: (context, index) =>
                                            _buildSessionItem(
                                                sessionList[index])),
                                    const Positioned(
                                      bottom: -18,
                                      left: 0,
                                      right: 0,
                                      child: Image(
                                        fit: BoxFit.cover,
                                        image: AssetImage(
                                            'assets/images/chat/ic_messge_list_mb.png'),
                                      ),
                                    )
                                  ],
                                )),
                      Container(
                        padding: EdgeInsets.only(left: 10.px, right: 10.px),
                        margin: EdgeInsets.only(top: 16.px),
                        constraints: BoxConstraints(minHeight: 44.px),
                        child: TextField(
                          controller: _textEditingController,
                          maxLines: 4,
                          minLines: 1,
                          onChanged: (value) {
                            setState(() {
                              if (value.isEmpty) {
                                isInputWord = false;
                              } else {
                                isInputWord = true;
                              }
                            });
                          },
                          onSubmitted: (value) {
                            if (value.isEmpty) {
                              showToast("请输入要想要问的问题");
                              return;
                            }
                            queryChatMessagesResult(value);
                          },
                          decoration: InputDecoration(
                            contentPadding: EdgeInsets.only(
                                left: 16.px,
                                right: 8.px,
                                top: 4.px,
                                bottom: 4.px),
                            filled: true,
                            fillColor: const Color.fromRGBO(255, 255, 255, 0.8),
                            hintText: '有什么问题尽管问我',
                            hintStyle: TextStyle(
                                color: const Color.fromRGBO(111, 126, 152, 1),
                                fontSize: 15.px,
                                fontWeight: FontWeight.w400),
                            // prefixIcon: Transform.scale(
                            //   scale: 0.65,
                            //   child: Image.asset(
                            //     'assets/images/chat/ic_mic.png',
                            //   ),
                            // ),
                            suffixIcon: IconButton(
                              onPressed: isInputWord
                                  ? () {
                                      queryChatMessagesResult(
                                          _textEditingController.text);
                                      FocusScope.of(context).unfocus();
                                    }
                                  : null,
                              icon: ImageIcon(
                                AssetImage(isInputWord
                                    ? 'assets/images/chat/ic_send_file_f.png'
                                    : 'assets/images/chat/ic_send_file.png'),
                                color: isInputWord
                                    ? Color.fromRGBO(30, 192, 106, 1)
                                    : Color.fromRGBO(0, 0, 0, 1),
                                size: 28.px,
                              ),
                            ),
                            border: const OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(35)),
                              borderSide:
                                  BorderSide(color: Colors.white, width: 1),
                            ),
                            enabledBorder: const OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(35)),
                              borderSide:
                                  BorderSide(color: Colors.white, width: 1),
                            ),
                            focusedBorder: const OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(35)),
                              borderSide:
                                  BorderSide(color: Colors.white, width: 1),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )
          ])),
    );
  }

  Widget _buildSessionItem(SessionEntity item) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.px, top: 16.px),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(mainAxisAlignment: MainAxisAlignment.end, children: [
            Container(
                alignment: Alignment.topRight,
                margin: EdgeInsets.only(left: 12.px, right: 12.px),
                padding: EdgeInsets.all(12.px),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(30, 192, 106, 1),
                  border: Border.all(
                      color: const Color.fromRGBO(255, 255, 255, 0.2),
                      width: 1.px),
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(4)),
                ),
                child: Text(item.question,
                    style: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w600,
                        color: Colors.white))),
          ]),
          SizedBox(height: 16.px),
          Container(
              margin: EdgeInsets.only(left: 12.px, right: 12.px),
              padding: EdgeInsets.all(12.px),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                    color: const Color.fromRGBO(255, 255, 255, 0.2),
                    width: 1.px),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                    bottomLeft: Radius.circular(4),
                    bottomRight: Radius.circular(20)),
              ),
              child: item.thinkContent.isEmpty
                  ? SizedBox(
                      width: 20.px,
                      height: 20.px,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation(
                            Color.fromRGBO(16, 46, 83, 1)),
                      ),
                    )
                  // : Text(item.answer,
                  //     textAlign: TextAlign.justify,
                  //     style: TextStyle(
                  //         height: 2.px,
                  //         letterSpacing: -0.5,
                  //         fontSize: 16.px,
                  //         fontWeight: FontWeight.w400,
                  //         color: const Color.fromRGBO(16, 46, 83, 1)))
                  : Column(
                      children: [
                        Text(
                          item.thinkContent,
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 14.px,
                          ),
                        ),
                        MarkdownBody(data: item.answer),
                      ],
                    )),
        ],
      ),
    );
  }

  // 进入提示
  Widget _buildTipView() {
    return Column(
      children: [
        questionDictionaryList.isNotEmpty
            ? Container(
                width: 351.px,
                margin:
                    EdgeInsets.only(bottom: 20.px, left: 12.px, right: 12.px),
                padding: EdgeInsets.only(
                    left: 16.px, right: 10.px, top: 20.px, bottom: 16.px),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(255, 255, 255, 0.6),
                  borderRadius: const BorderRadius.all(Radius.circular(20)),
                  border: Border.all(
                      color: const Color.fromRGBO(255, 255, 255, 0.2),
                      width: 1.px),
                ),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ShaderMask(
                        shaderCallback: (Rect bounds) {
                          return const LinearGradient(
                            colors: [
                              Color.fromRGBO(8, 212, 79, 1),
                              Color.fromRGBO(9, 228, 232, 1)
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ).createShader(Offset.zero & bounds.size);
                        },
                        blendMode: BlendMode.srcATop,
                        child: Text(
                          "Hi，我是北大荒Chat",
                          style: TextStyle(
                            fontSize: 16.px,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                      SizedBox(height: 8.px),
                      Text(
                        "我是北大荒集团训练的农业专家,已接入DeepSeek可以随时回答你的问题~",
                        style: TextStyle(
                            letterSpacing: -0.2,
                            fontSize: 13.px,
                            color: const Color.fromRGBO(111, 126, 152, 1)),
                      ),
                      Column(
                        children: questionDictionaryList.isNotEmpty
                            ? questionDictionaryList
                                .map((item) => _buildQuestionDictionary(item))
                                .toList()
                            : [],
                      )
                    ]))
            : Container(),
        SizedBox(height: 8.px),
      ],
    );
  }

  // build 提示问题字典项
  Widget _buildQuestionDictionary(QuestionDictionaryEntity item) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        queryChatMessagesResult(item.question!);
      },
      child: Container(
        padding: EdgeInsets.all(12.px),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(255, 255, 255, 0.9),
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          border: Border.all(
              color: const Color.fromRGBO(255, 255, 255, 1), width: 0.5.px),
        ),
        margin: EdgeInsets.only(top: 16.px),
        child: Row(children: [
          Text(item.question!,
              style: TextStyle(
                fontSize: 13.px,
                fontWeight: FontWeight.w600,
              )),
          const Spacer(),
          BdhNetworkImage(
            url: '${urlConfig.microfront}${item.image}',
            width: 36.px,
            height: 36.px,
          ),
        ]),
      ),
    );
  }

  // build 左侧抽屉
  Widget _buildLeftDrawer() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 36.px),
      width: 248.px,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(width: 14.px),
              Text("历史记录",
                  style: TextStyle(
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600,
                      color: const Color.fromRGBO(51, 51, 51, 0.6))),
              // GestureDetector(
              //   behavior: HitTestBehavior.opaque,
              //   onTap: () {
              //     _scaffoldKey.currentState?.closeDrawer();
              //     startNewChatMessage();
              //   },
              //   child: Container(
              //     decoration: BoxDecoration(
              //       color: const Color.fromRGBO(255, 255, 255, 0.8),
              //       borderRadius: BorderRadius.circular(16),
              //       border: Border.all(
              //         color: const Color.fromRGBO(255, 255, 255, 1),
              //         width: 1,
              //       ),
              //     ),
              //     width: 132.px,
              //     height: 32.px,
              //     child: const Row(
              //         mainAxisAlignment: MainAxisAlignment.center,
              //         crossAxisAlignment: CrossAxisAlignment.center,
              //         children: [
              //           ImageIcon(
              //             AssetImage('assets/images/chat/ic_new_chat.png'),
              //             size: 24,
              //             color: Color.fromRGBO(30, 192, 106, 1),
              //           ),
              //           SizedBox(width: 2),
              //           Text(
              //             "开启新对话",
              //             style: TextStyle(
              //                 fontSize: 14,
              //                 fontWeight: FontWeight.w500,
              //                 color: Color.fromRGBO(30, 192, 106, 1)),
              //           ),
              //         ]),
              //   ),
              // )
            ],
          ),
          SizedBox(height: 4.px),
          Expanded(
            child: questionRecordList.isEmpty
                ? Center(
                    child: Text("暂无历史记录",
                        style: TextStyle(
                            fontSize: 14.px,
                            fontWeight: FontWeight.w600,
                            color: const Color.fromRGBO(51, 51, 51, 0.6))),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    padding: EdgeInsets.only(top: 10.px),
                    itemCount: questionRecordList.length,
                    itemBuilder: (context, index) =>
                        _buildHistoryRecord(questionRecordList[index])),
          ),
          Container(
            padding: EdgeInsets.only(bottom: 16.px),
            alignment: Alignment.center,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                _scaffoldKey.currentState?.closeDrawer();
                startNewChatMessage();
              },
              child: Container(
                margin: EdgeInsets.only(top: 10.px),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(255, 255, 255, 0.8),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color.fromRGBO(255, 255, 255, 1),
                    width: 1,
                  ),
                ),
                width: 132.px,
                height: 32.px,
                child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ImageIcon(
                        AssetImage('assets/images/chat/ic_new_chat.png'),
                        size: 24,
                        color: Color.fromRGBO(30, 192, 106, 1),
                      ),
                      SizedBox(width: 2),
                      Text(
                        "开启新对话",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color.fromRGBO(30, 192, 106, 1)),
                      ),
                    ]),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildHistoryRecord(QuestionRecordEntity item) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          _scaffoldKey.currentState?.closeDrawer();
          getHistoryMessage(item.id!);
        },
        child: Container(
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left: 7.px, right: 30.px),
          child: Container(
            padding: EdgeInsets.only(
                left: 7.px, right: 7.px, bottom: 12.px, top: 12.px),
            decoration: isCurrentSession(item.id)
                ? BoxDecoration(
                    color: const Color.fromRGBO(30, 192, 106, 0.2),
                    borderRadius: BorderRadius.circular(16),
                  )
                : null,
            child: Text(
              item.name ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w600,
                  color: isCurrentSession(item.id)
                      ? const Color.fromRGBO(30, 192, 106, 1)
                      : const Color.fromRGBO(51, 51, 51, 1)),
            ),
          ),
        ));
  }

  bool isCurrentSession(String? conversationId) {
    return currentConversationId == conversationId;
  }

  void startNewChatMessage() {
    setState(() {
      isShowNewSession = false;
      currentConversationId = "";
      _textEditingController.text = "";
      sessionList.clear();
      isInputWord = false;
      isGetResult = false;
    });
    stopCurrentSession();
  }

  bool isGetResult = false;
  void queryChatMessagesResult(String question) async {
    if (question.isEmpty) {
      showToast("请输入要想要问的问题");
      return;
    }
    FocusScope.of(context).unfocus();
    SessionEntity sessionEntity = SessionEntity();
    setState(() {
      currentQuestion = question;
      sessionEntity.question = question;
      _textEditingController.text = "";
      isInputWord = false;
      sessionList.add(sessionEntity);
    });
    isGetResult = false;
    ChatRepository.queryChatMessages(question, currentConversationId)
        .then((response) async {
      // 用于每个阶段的对话结果
      // 处理流式响应
      String? answer = "";
      sessionEntity.isThinkEnd = false;
      response.data.stream.listen((data) {
        Uint8List dataUint8List = Uint8List.fromList(data);
        String str = String.fromCharCodes(dataUint8List);
        print(str);
        if (str.contains("event: ping")) {
          return;
        }
        if (str.contains("</think>")) {
          sessionEntity.isThinkEnd = true;
          return;
        }
        List<String> jsonData = str.split('data: ');
        // 移除空字符串
        jsonData = jsonData.where((element) => element.isNotEmpty).toList();

        for (var element in jsonData) {
          ChatMessageEntity message =
              ChatMessageEntity.fromJson(jsonDecode(element));
          if (message.event == "message") {
            answer = message.answer;
            currentTaskId = message.taskId!;
            currentConversationId = message.conversationId!;
            if (sessionEntity.conversation_id.isEmpty) {
              sessionEntity.conversation_id = currentConversationId;
              setState(() {
                isShowNewSession = true;
              });
              getHistoryRecord();
            }

            print("内容:${answer}");
            print("会话ID:${currentConversationId}");
            print("任务ID:${currentTaskId}");

            if (mounted) {
              setState(() {
                if (!sessionEntity.isThinkEnd) {
                  if(answer!.contains("</")){
                    return;
                  }
                  sessionEntity.thinkContent =
                      sessionEntity.thinkContent + answer!;
                  print("  sessionEntity.thinkContent:${sessionEntity.thinkContent}");
                } else {
                  if(answer!.contains("</")){
                    return;
                  }
                  sessionEntity.answer = sessionEntity.answer + answer!;
                }

                if (sessionList.isNotEmpty) {
                  _scrollController.animateTo(
                    _scrollController.position.maxScrollExtent,
                    duration: Duration(milliseconds: 300), // 持续时间可以根据需要调整
                    curve: Curves.easeOut, // 动画曲线，可以根据需要调整
                  );
                }
              });
            }
          } else if (message.event == "message_end") {
            print("Chat Message End ..........");
          }
        }
      }, onDone: () {
        print("done");
        if (mounted) {
          _textEditingController.text = "";
          getHistoryRecord();
        }
      });
    });
  }

  void getHistoryRecord() {
    ChatRepository.queryHistorySession().then((response) {
      if (response.data["data"] != null) {
        questionRecordList.clear();
        for (var item in response.data["data"]) {
          QuestionRecordEntity questionRecordEntity =
              QuestionRecordEntity.fromJson(item);
          if (questionRecordEntity.name == "New conversation") {
            questionRecordEntity.name = currentQuestion;
          }
          setState(() {
            questionRecordList.add(questionRecordEntity);
          });
        }
      }
    });
  }

  void getHistoryMessage(String conversationId) {
    stopCurrentSession();
    ChatRepository.queryHistoryMessage(conversationId).then((response) {
      if (response.data["data"] != null) {
        sessionList.clear();
        for (var item in response.data["data"]) {
          HistoryMessageEntity historyMessageEntity =
              HistoryMessageEntity.fromJson(item);
          setState(() {
            SessionEntity sessionEntity = SessionEntity();
            sessionEntity.question = historyMessageEntity.query!;
            currentConversationId = historyMessageEntity.conversationId!;
            String answer = historyMessageEntity.answer!;
            if (answer.contains("</think>")) {
              sessionEntity.thinkContent =
                  answer.substring(0, answer.indexOf("</think>"));
              sessionEntity.answer =
                  answer.substring(answer.indexOf("</think>"));
            }
            // if (answer.contains("</think>")) {
            //   int startIndex = answer.indexOf("</think>");
            //   if (startIndex != -1) {
            //     answer = answer.substring(startIndex + 8);
            //     if (answer.startsWith("\n\n")) {
            //       answer = answer.replaceFirst("\n\n", "");
            //     }
            //   }
            // }

            // sessionEntity.answer = answer;
            sessionList.add(sessionEntity);
          });
        }
        setState(() {
          isShowNewSession = true;
        });
      }
    });
  }

  void queryQuestionDictionary() {
    questionDictionaryList.clear();
    ChatRepository.queryQuestionDictionary().then((response) {
      if (response.data["data"] != null) {
        for (var item in response.data["data"]) {
          QuestionDictionaryEntity questionDictionaryEntity =
              QuestionDictionaryEntity.fromJson(item);
          setState(() {
            questionDictionaryList.add(questionDictionaryEntity);
          });
        }
      }
    });
  }

  void stopCurrentSession() {
    if (currentTaskId.isNotEmpty) {
      // 停止消息响应
      ChatRepository.stopQuestionMessage(currentTaskId).then((response) {
        print("取消问题任务响应:" + jsonEncode(response.data));
        // setState(() {
        //   _textEditingController.text = "";
        //   sessionList.clear();
        //   isInputWord = false;
        //   isGetResult = false;
        // });
      });
    }
  }
}
