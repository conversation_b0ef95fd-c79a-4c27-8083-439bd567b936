/*
 * <AUTHOR>
 * @description: 地块画像
 * @date 2025/04/01 09:40:30
*/

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

import '../../../utils/image_util.dart';
import '../../../utils/request/my_field_page_service.dart';

class MyFieldParcelImage extends StatefulWidget {
  final String plotNo;
  final String year;
  final String orgFullName;
  final String cropBreedName;
  final String cropTypeName;
  final String contrArea;

  const MyFieldParcelImage(
      {super.key,
      required this.plotNo,
      required this.year,
      required this.orgFullName,
      required this.cropBreedName,
      required this.cropTypeName,
      required this.contrArea});

  @override
  State<MyFieldParcelImage> createState() => _MyFieldParcelImageState();
}

class _MyFieldParcelImageState extends State<MyFieldParcelImage> {
  Map<String, dynamic>? plotData;

  @override
  void initState() {
    super.initState();
    getPlotInfo();
  }

  // 获取地块画像数据
  Future<void> getPlotInfo() async {
    MyFieldPageService.getMyFieldPlotInfo(
        {"plotNo": widget.plotNo, "year": widget.year}).then((res) {
      setState(() {
        plotData = res['data'];
      });
    }).catchError((err) {
      Logger().e('获取地块画像数据失败------------------');
      Logger().e(err);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(ImageHelper.wrapAssets('my_field_soil_bg.png')),
            fit: BoxFit.cover,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部导航栏
              _buildAppBar(),
              // 地块信息
              Expanded(
                child:
                    // plotData == null
                    //     ? const Center(child: CircularProgressIndicator())
                    //     :
                    SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.all(16.px),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 顶部地块信息卡片
                        _buildTopInfoCard(),
                        SizedBox(height: 16.px),

                        // 地块类型和种植作物
                        _buildSimpleInfoRow(
                            '地块类型',
                            plotData?["base"]?["resPlot"]?["landTypeName"] ??
                                '未知'),
                        SizedBox(height: 6.px),
                        _buildSimpleInfoRow(
                            '种植作物', widget.cropTypeName ?? '未知'),
                        SizedBox(height: 16.px),

                        // 土壤信息卡片
                        _buildInfoCard(
                          '土壤信息',
                          [
                            _buildInfoRow(
                                '土壤类型',
                                plotData?["base"]?["attr"]?["soilType1"] ??
                                    '未知'),
                            _buildInfoRow('耕层厚度',
                                plotData?["base"]?["attr"]?["thick"] ?? '未知'),
                            _buildInfoRow(
                                'ph值',
                                plotData?["base"]?["attr"]?["phValue"]
                                        ?.toString() ??
                                    '未知'),
                            _buildInfoRow(
                                '有机质',
                                plotData?["base"]?["attr"]?["organicMatter"] ??
                                    '未知'),
                            _buildInfoRow(
                                '碱解氮',
                                plotData?["base"]?["attr"]?["alkaliN"]
                                        ?.toString() ??
                                    '未知'),
                            _buildInfoRow(
                                '有效磷',
                                plotData?["base"]?["attr"]?["availableP"]
                                        ?.toString() ??
                                    '未知'),
                            _buildInfoRow(
                                '速效钾',
                                plotData?["base"]?["attr"]?["availableK"]
                                        ?.toString() ??
                                    '未知'),
                          ],
                        ),
                        SizedBox(height: 16.px),

                        // 气象信息卡片
                        _buildInfoCard(
                          '气象信息',
                          [
                            _buildInfoRow('常年活动积温',
                                '${plotData?["weather"]?["activeTemperature"] ?? '未知'}℃'),
                            _buildInfoRow('平均温度',
                                '${plotData?["weather"]?["avgTemperature"] ?? '未知'}℃'),
                            _buildInfoRow('无霜期',
                                '${plotData?["weather"]?["frostFreePeriod"] ?? '未知'}天'),
                            _buildInfoRow('历年灾害发生情况',
                                '${plotData?["weather"]?["disasterDays"] ?? '未知'}天'),
                            SizedBox(height: 8.px),
                            _buildWeatherGrid(),
                          ],
                        ),
                        SizedBox(height: 16.px),

                        // 粮食产量信息卡片
                        _buildInfoCard(
                          '粮食产量信息',
                          [
                            _buildInfoRow(
                                '前一年地块${plotData?["yield"]?["raiseCropsOne"] ?? ''}产量',
                                '${plotData?["yield"]?["yieldOne"] ?? '未知'}kg/亩'),
                            _buildInfoRow(
                                '前两年地块${plotData?["yield"]?["raiseCropsTwo"] ?? ''}产量',
                                '${plotData?["yield"]?["yieldTwo"] ?? '未知'}kg/亩'),
                            _buildInfoRow(
                                '前三年地块${plotData?["yield"]?["raiseCropsThree"] ?? ''}产量',
                                '${plotData?["yield"]?["yieldThree"] ?? '未知'}kg/亩'),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Image.asset(
          ImageHelper.wrapAssets('field_left.png'),
          width: 24.px,
          height: 24.px,
          color: Colors.black,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        '地块画像',
        style: TextStyle(
          color: Colors.black,
          fontSize: 16.px,
          fontWeight: FontWeight.w500,
        ),
      ),
      // 右上角 ai 分析按钮
      // actions: [
      //   InkWell(
      //       onTap: () {},
      //       child: Container(
      //         margin: EdgeInsets.only(right: 16.px),
      //         padding: EdgeInsets.symmetric(horizontal: 8.px, vertical: 4.px),
      //         width: 85.px,
      //         height: 36.px,
      //         decoration: BoxDecoration(
      //           border: Border.all(
      //             color: Color.fromRGBO(184, 245, 215, 1),
      //             width: 1.px,
      //           ),
      //           color: const Color.fromRGBO(225, 255, 255, 0.4),
      //           borderRadius: BorderRadius.circular(18.px),
      //         ),
      //         child: Stack(alignment: Alignment.center, children: [
      //           Positioned(
      //             right: 46.px,
      //             child: Image.asset(
      //               ImageHelper.wrapAssets('my_field_ai.png'),
      //               width: 22.px,
      //               height: 22.px,
      //               fit: BoxFit.fitWidth,
      //             ),
      //           ),
      //           Positioned(
      //               right: 0.px,
      //               child: Text(
      //                 'AI分析',
      //                 style: TextStyle(
      //                   color: Color.fromRGBO(42, 110, 89, 1),
      //                   fontSize: 14.px,
      //                   fontWeight: FontWeight.w400,
      //                 ),
      //               ))
      //         ]),
      //       ))
      // ],
    );
  }

  Widget _buildTopInfoCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.px),
      decoration: BoxDecoration(
        color: const Color(0xFF1AAA6E),
        borderRadius: BorderRadius.circular(12.px),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.orgFullName,
            overflow: TextOverflow.ellipsis,
            maxLines: 1,
            style: TextStyle(
              fontSize: 16.px,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 8.px),
          Row(
            children: [
              Container(
                padding:
                    EdgeInsets.symmetric(horizontal: 3.5.px, vertical: 2.px),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.px),
                  color: const Color.fromRGBO(2, 139, 93, 1),
                ),
                child: Text(
                  '地块编码',
                  style: TextStyle(
                    fontSize: 14.px,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
              ),
              SizedBox(
                width: 8.px,
              ),
              Text(
                widget.plotNo,
                style: TextStyle(
                  fontSize: 14.px,
                  color: Colors.white.withOpacity(0.9),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.px),
          Container(
            padding: EdgeInsets.all(12.px),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8.px),
              border: Border.all(
                color: Colors.white.withOpacity(0.2),
                width: 1.px,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildTopInfoItem('品种', widget.cropBreedName),
                Container(
                  width: 1.px,
                  height: 32.px,
                  color: Colors.white.withOpacity(0.2),
                ),
                _buildTopInfoItem('田面坡度',
                    '${plotData?["base"]?["attr"]["landGrad"] ?? '未知'}°'),
                Container(
                  width: 1.px,
                  height: 32.px,
                  color: Colors.white.withOpacity(0.2),
                ),
                _buildTopInfoItem('耕地面积', '${widget.contrArea}亩'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopInfoItem(String label, String value) {
    Logger().d('_buildTopInfoItem label: $label, value: $value');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 14.px,
            color: Colors.white.withOpacity(0.9),
          ),
        ),
        SizedBox(height: 4.px),
        Text(
          value.isEmpty ? '无$label信息' : value,
          style: TextStyle(
            fontSize: 16.px,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildSimpleInfoRow(String label, String value) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(vertical: 16.px, horizontal: 16.px),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.px,
              color: const Color(0xFF666666),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.px,
              color: const Color(0xFF333333),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(16.px),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.9),
        borderRadius: BorderRadius.circular(12.px),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 3.px,
                height: 16.px,
                decoration: BoxDecoration(
                  color: const Color(0xFF1AAA6E),
                  borderRadius: BorderRadius.circular(1.5.px),
                ),
              ),
              SizedBox(width: 8.px),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16.px,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF333333),
                ),
              ),
            ],
          ),
          SizedBox(height: 12.px),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, dynamic value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6.px),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.px,
              color: const Color(0xFF666666),
            ),
          ),
          Text(
            value.toString(),
            style: TextStyle(
              fontSize: 14.px,
              color: const Color(0xFF333333),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeatherGrid() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: _buildWeatherItem('霜冻',
                    '${plotData?["weather"]?["disasterSdDay"] ?? '未知'}天')),
            SizedBox(width: 16.px),
            Expanded(
                child: _buildWeatherItem('冰雹',
                    '${plotData?["weather"]?["disasterBbDay"] ?? '未知'}天')),
          ],
        ),
        SizedBox(height: 8.px),
        Row(
          children: [
            Expanded(
                child: _buildWeatherItem('暴雪',
                    '${plotData?["weather"]?["disasterXzDay"] ?? '未知'}天')),
            SizedBox(width: 16.px),
            Expanded(
                child: _buildWeatherItem('寒潮',
                    '${plotData?["weather"]?["disasterHcDay"] ?? '未知'}天')),
          ],
        ),
        SizedBox(height: 8.px),
        Row(
          children: [
            Expanded(
                child: _buildWeatherItem('大风',
                    '${plotData?["weather"]?["disasterDfDay"] ?? '未知'}天')),
            SizedBox(width: 16.px),
            Expanded(
                child: _buildWeatherItem('暴雨',
                    '${plotData?["weather"]?["disasterByDay"] ?? '未知'}天')),
          ],
        ),
        SizedBox(height: 8.px),
        Row(
          children: [
            Expanded(
                child: _buildWeatherItem('干旱',
                    '${plotData?["weather"]?["disasterGhDay"] ?? '未知'}天')),
            SizedBox(width: 16.px),
            Expanded(
                child: _buildWeatherItem('高温',
                    '${plotData?["weather"]?["disasterGwDay"] ?? '未知'}天')),
          ],
        ),
      ],
    );
  }

  Widget _buildWeatherItem(String label, String value) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8.px),
      decoration: BoxDecoration(
        color: const Color(0xFFF5F5F5),
        borderRadius: BorderRadius.circular(4.px),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 14.px,
              color: const Color(0xFF666666),
            ),
          ),
          SizedBox(width: 8.px),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.px,
              color: const Color(0xFF333333),
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
