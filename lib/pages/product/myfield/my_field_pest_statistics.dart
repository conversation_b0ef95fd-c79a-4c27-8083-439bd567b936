/*
 * <AUTHOR>
 * @description: 虫害统计
 * @date 2025/03/26 09:57:45 
*/

import 'dart:convert';

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_echarts/flutter_echarts.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import '../../../utils/image_util.dart';
import '../../../utils/request/my_field_page_service.dart';
import './insect_progress_indicator.dart';

class MyFieldPestStatistics extends StatefulWidget {
  final String deviceId;
  final String deviceName;

  const MyFieldPestStatistics({
    super.key,
    required this.deviceId,
    required this.deviceName,
  });

  @override
  State<MyFieldPestStatistics> createState() => _MyFieldPestStatisticsState();
}

class _MyFieldPestStatisticsState extends State<MyFieldPestStatistics> {
  // 当前选中的tab
  int selectedTab = 0; // 0: 照片, 1: 分析

  // 时间范围
  DateTime? startTime;
  DateTime? endTime;

  // 照片 虫子种类
  String? insectTypePhone;

  // 照片列表数据
  List<Map<String, dynamic>> photoList = [];
  int currentPage = 1;
  final int pageSize = 10;
  bool hasMore = true;
  bool isLoading = false;

  // 分析数据
  Map<String, dynamic> analysisData = {};
  Map<String, dynamic> insectAnalysisData = {};

  // 虫子类型列表
  List<String> insectTypes = [];
  String? selectedInsectType;

  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    loadData();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (hasMore && !isLoading && selectedTab == 0) {
        loadPhotoData();
      }
    }
  }

  Future<void> loadData() async {
    if (selectedTab == 0) {
      await loadPhotoData();
    } else {
      Logger().i('加载分析---------数据');
      await Future.wait([
        loadAnalysisData(),
        loadInsectAnalysisData(),
      ]);
    }
  }

  Future<void> loadPhotoData() async {
    if (!hasMore || isLoading) return;

    setState(() => isLoading = false);
    Logger().i(startTime);
    try {
      final response = await MyFieldPageService.getMyFieldPestStatisByPhoto({
        "deviceId": widget.deviceId,
        // "startTime": startTime?.millisecondsSinceEpoch,
        // "endTime": endTime?.millisecondsSinceEpoch,
        "startTime": startTime != null
            ? DateFormat('yyyy-MM-dd').format(startTime!)
            : null,
        "endTime":
            endTime != null ? DateFormat('yyyy-MM-dd').format(endTime!) : null,
        "page": currentPage,
        "rows": pageSize,
        "insectType": insectTypePhone
      });

      if (response['success'] == true) {
        final data = response['data'] ?? [];
        final List<dynamic> records = data['records'] ?? [];

        setState(() {
          if (currentPage == 1) {
            photoList = List<Map<String, dynamic>>.from(records);
          } else {
            photoList.addAll(List<Map<String, dynamic>>.from(records));
          }

          hasMore = photoList.length < (data['total'] ?? 0);
          currentPage++;
          isLoading = false;
        });
      }
    } catch (e) {
      Logger().e('加载照片数据失败: $e');
      setState(() => isLoading = false);
    }
  }

  Future<void> loadAnalysisData() async {
    try {
      final response = await MyFieldPageService.getMyFieldPestStatisByAnalysis({
        "deviceId": widget.deviceId,
        "startTime": startTime != null
            ? DateFormat('yyyy-MM-dd').format(startTime!)
            : null,
        "endTime":
            endTime != null ? DateFormat('yyyy-MM-dd').format(endTime!) : null,
      });
      Logger().i('加载分析数据: $response');
      if (response['success'] == true) {
        setState(() {
          analysisData = response['data'];
        });
      }
    } catch (e) {
      Logger().e('加载分析数据失败: $e');
    }
  }

  Future<void> loadInsectAnalysisData() async {
    try {
      final response =
          await MyFieldPageService.getMyFieldPestStatisInsectAnalysis({
        "deviceId": widget.deviceId,
        "startTime": startTime != null
            ? DateFormat('yyyy-MM-dd').format(startTime!)
            : null,
        "endTime":
            endTime != null ? DateFormat('yyyy-MM-dd').format(endTime!) : null,
        "insectType": selectedInsectType,
      });

      if (response['success'] == true) {
        setState(() {
          insectAnalysisData = response['data'];
        });
      }
    } catch (e) {
      Logger().e('加载虫害分析数据失败: $e');
    }
  }

  Future<void> loadInsectTypes() async {
    try {
      final response = await MyFieldPageService.getMyFieldInsectType({
        "page": 1,
        "rows": 999,
      });
      Logger().i('加载虫子类型: $response');
      if (response['success'] == true) {
        setState(() {
          insectTypes = List<String>.from(
            (response['data']['records'] as List)
                .map((record) => record['insectType']),
          );
        });
      }
    } catch (e) {
      Logger().e('加载虫子类型失败: $e');
    }
  }

  void _showDateRangePicker() async {
    final size = MediaQuery.of(context).size;
    TDCalendarPopup(
      context,
      visible: true,
      child: TDCalendar(
        title: '请选择日期区间',
        minDate: DateTime.now()
            .subtract(const Duration(days: 365))
            .millisecondsSinceEpoch,
        maxDate: DateTime.now()
            .add(const Duration(days: 365))
            .millisecondsSinceEpoch,
        type: CalendarType.range,
        value: [
          DateTime.now().millisecondsSinceEpoch,
          DateTime.now().add(const Duration(days: 6)).millisecondsSinceEpoch,
        ],
        height: size.height * 0.6 + 176,
      ),
      onConfirm: (value) {
        Logger().i('选择日期区间: $value');
        if (value is List && value.length == 2) {
          setState(() {
            startTime = DateTime.fromMillisecondsSinceEpoch(value[0] as int);
            endTime = DateTime.fromMillisecondsSinceEpoch(value[1] as int);
            currentPage = 1;
            hasMore = true;
          });
        } else {
          Logger().e('无效的日期区间值: $value');
        }
      },
    );
  }

  void _showInsectTypePicker() async {
    if (insectTypes.isEmpty) {
      await loadInsectTypes();
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            backgroundColor: Colors.white,
            elevation: 0,
            leading: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
              onPressed: () => Navigator.pop(context),
            ),
            title: Text(
              '选择虫子种类',
              style: TextStyle(
                color: Colors.black,
                fontSize: 16.px,
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
            ],
          ),
          body: ListView.builder(
            itemCount: insectTypes.length,
            itemBuilder: (context, index) {
              final type = insectTypes[index];
              return ListTile(
                title: Text(type),
                trailing: selectedTab == 0
                    ? insectTypePhone == type
                        ? const Icon(Icons.check_circle,
                            color: Color(0xFF16B760))
                        : null
                    : selectedInsectType == type
                        ? const Icon(Icons.check_circle,
                            color: Color(0xFF16B760))
                        : null,
                onTap: () {
                  setState(() {
                    if (selectedTab == 0) {
                      insectTypePhone = type;
                    } else {
                      selectedInsectType = type;
                    }
                  });
                  Navigator.pop(context);
                  if (selectedTab == 0) {
                    loadPhotoData();
                  } else {
                    Future.wait([
                      loadAnalysisData(),
                      loadInsectAnalysisData(),
                    ]);
                  }
                },
              );
            },
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          '害虫统计',
          style: TextStyle(
            color: Colors.black,
            fontSize: 16.px,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      body: Column(
        children: [
          // Tab切换
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(vertical: 12.px),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildTab('照片', 0),
                SizedBox(width: 48.px),
                _buildTab('分析', 1),
              ],
            ),
          ),

          // 时间选择
          Container(
            child: Container(
                margin: EdgeInsets.all(12.px),
                padding:
                    EdgeInsets.symmetric(horizontal: 16.px, vertical: 12.px),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.px),
                ),
                child: Padding(
                    padding:
                        EdgeInsets.symmetric(horizontal: 0.px, vertical: 8.px),
                    child: Column(
                      children: [
                        InkWell(
                          onTap: _showDateRangePicker,
                          child: Container(
                            decoration: BoxDecoration(
                              color: const Color(0xFFF5F5F5),
                              borderRadius: BorderRadius.circular(8.px),
                            ),
                            height: 48.px,
                            padding: EdgeInsets.symmetric(
                                horizontal: 16.px, vertical: 12.px),
                            child: Row(
                              children: [
                                Image.asset(
                                  ImageHelper.wrapAssets(
                                      'my_field_pest_title_time.png'),
                                  width: 20.px,
                                  height: 20.px,
                                ),
                                SizedBox(width: 8.px),
                                Expanded(
                                  child: Text(
                                    startTime != null && endTime != null
                                        ? '${DateFormat('yyyy-MM-dd').format(startTime!)} 至 ${DateFormat('yyyy-MM-dd').format(endTime!)}'
                                        : '请选择开始-结束日期',
                                    style: TextStyle(
                                      color: Colors.black54,
                                      fontSize: 14.px,
                                    ),
                                  ),
                                ),
                                Icon(Icons.arrow_forward_ios,
                                    size: 16.px, color: Colors.black54),
                              ],
                            ),
                          ),
                        ),
                        selectedTab == 1
                            ? Container()
                            : InkWell(
                                onTap: _showInsectTypePicker,
                                child: Container(
                                  margin: EdgeInsets.only(top: 8.px),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFFF5F5F5),
                                    borderRadius: BorderRadius.circular(8.px),
                                  ),
                                  height: 48.px,
                                  padding: EdgeInsets.symmetric(
                                      horizontal: 16.px, vertical: 12.px),
                                  child: Row(
                                    children: [
                                      Image.asset(
                                        ImageHelper.wrapAssets(
                                            'my_field_pest_type.png'),
                                        width: 20.px,
                                        height: 20.px,
                                      ),
                                      SizedBox(width: 8.px),
                                      Expanded(
                                        child: Text(
                                          insectTypePhone != null
                                              ? insectTypePhone.toString()
                                              : '请选择虫子种类',
                                          style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 14.px,
                                          ),
                                        ),
                                      ),
                                      Icon(Icons.arrow_forward_ios,
                                          size: 16.px, color: Colors.black54),
                                    ],
                                  ),
                                ),
                              ),
                        Container(
                          margin: EdgeInsets.only(top: 8.px),
                          child: TDButton(
                            onTap: () async {
                              if (selectedTab == 0) {
                                photoList.clear();
                              } else {
                                analysisData.clear();
                              }
                              currentPage = 1;
                              hasMore = true;
                              Logger().i('查询');
                              loadData();
                            },
                            text: '查询',
                            icon: TDIcons.search,
                            size: TDButtonSize.large,
                            width: double.infinity,
                            style: TDButtonStyle(
                                backgroundColor: const Color(0xFF16B760),
                                textColor: const Color(0xFFFFFFFF),
                                radius: BorderRadius.circular(8.px)),
                          ),
                        )
                      ],
                    ))),
          ),

          // 主要内容区域
          Expanded(
            child: selectedTab == 0 ? _buildPhotoList() : _buildAnalysis(),
          ),
        ],
      ),
    );
  }

  Widget _buildTab(String title, int index) {
    final isSelected = selectedTab == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedTab = index;
          if (index == 1) {
            loadAnalysisData();
            loadInsectAnalysisData();
          }
        });
      },
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              color: isSelected ? const Color(0xFF16B760) : Colors.black,
              fontSize: 16.px,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          SizedBox(height: 4.px),
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: 20.px,
            height: 2.px,
            decoration: BoxDecoration(
              color: isSelected ? const Color(0xFF16B760) : Colors.transparent,
              borderRadius: BorderRadius.circular(1.px),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoList() {
    if (photoList.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            color: Colors.black54,
            fontSize: 14.px,
          ),
        ),
      );
    }

    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.only(left: 12.px, right: 12.px, bottom: 12.px),
      itemCount: photoList.length + 1,
      itemBuilder: (context, index) {
        if (index == photoList.length) {
          return _buildLoadingIndicator();
        }

        final item = photoList[index];
        final createTime =
            DateTime.fromMillisecondsSinceEpoch(item['createTime'] ?? 0);
        final insects = item['insectSituation'] as List? ?? [];

        return Container(
          margin: EdgeInsets.only(bottom: 12.px),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.px),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 日期显示栏
              Stack(
                children: [
                  Image.asset(
                    ImageHelper.wrapAssets(
                        'my_field_pest_statistics_time_bg.png'),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 16.px, top: 18.px),
                    child: Text(
                      DateFormat('yyyy/MM/dd').format(createTime),
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
                ],
              ),

              // 照片和虫害信息
              InkWell(
                onTap: () {
                  if (item['analysisImage'] != null ||
                      item['srcImage'] != null) {
                    showDialog(
                      context: context,
                      builder: (context) => Dialog.fullscreen(
                        child: Stack(
                          children: [
                            InteractiveViewer(
                              child: Image.network(
                                item['analysisImage'] ?? item['srcImage'],
                                fit: BoxFit.contain,
                                errorBuilder: (context, error, stackTrace) {
                                  return Center(
                                    child: Icon(
                                      Icons.image_not_supported,
                                      color: Colors.grey,
                                      size: 48.px,
                                    ),
                                  );
                                },
                              ),
                            ),
                            Positioned(
                              top: 32.px,
                              right: 32.px,
                              child: IconButton(
                                icon: const Icon(Icons.close,
                                    color: Colors.white),
                                onPressed: () => Navigator.pop(context),
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('暂无图片数据')),
                    );
                  }
                },
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Stack(
                      children: [
                        // 左侧图片
                        Padding(
                          padding: EdgeInsets.only(left: 12.px),
                          child: Expanded(
                            flex: 1,
                            child: ClipRRect(
                              borderRadius: BorderRadius.only(
                                bottomLeft: Radius.circular(8.px),
                              ),
                              child: item['analysisImage'] != null ||
                                      item['srcImage'] != null
                                  ? Image.network(
                                      item['analysisImage'] ?? item['srcImage'],
                                      height: 148.px,
                                      width: 148.px,
                                    )
                                  : Container(),
                            ),
                          ),
                        ),
                        // if (captureTime.isNotEmpty)
                        Positioned(
                          right: 2.px,
                          bottom: 2.px,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8.px, vertical: 4.px),
                            margin: EdgeInsets.all(4.px),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(2.px),
                            ),
                            child: Text(
                              item['createTime'] != null
                                  ? DateFormat('yyyy/MM/dd HH:mm:ss')
                                      .format(
                                          DateTime.fromMillisecondsSinceEpoch(
                                              item['createTime']))
                                      .toString()
                                  : '无时间数据',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12.px,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // 右侧虫害列表
                    Expanded(
                      flex: 1,
                      child: Container(
                        height: 200.px,
                        padding: EdgeInsets.all(12.px),
                        child: insects.isEmpty
                            ? Center(
                                child: Text(
                                  '暂无虫害数据',
                                  style: TextStyle(
                                    color: Colors.black54,
                                    fontSize: 14.px,
                                  ),
                                ),
                              )
                            : ListView.builder(
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount:
                                    insects.length > 4 ? 4 : insects.length,
                                itemBuilder: (context, i) {
                                  final insect = insects[i];
                                  return Container(
                                    margin: EdgeInsets.only(bottom: 8.px),
                                    child: Row(
                                      children: [
                                        Container(
                                          width: 4.px,
                                          height: 16.px,
                                          decoration: BoxDecoration(
                                            color: const Color(0xFF16B760),
                                            borderRadius:
                                                BorderRadius.circular(2.px),
                                          ),
                                        ),
                                        SizedBox(width: 8.px),
                                        Expanded(
                                          child: Text(
                                            '${insect['insectName']}',
                                            overflow: TextOverflow.ellipsis,
                                            style: TextStyle(
                                              color: Colors.black87,
                                              fontSize: 14.px,
                                            ),
                                          ),
                                        ),
                                        Text(
                                          '${insect['insectCount']}头',
                                          style: TextStyle(
                                            color: const Color(0xFF16B760),
                                            fontSize: 14.px,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildLoadingIndicator() {
    if (!hasMore) return const SizedBox.shrink();
    return Container(
      padding: EdgeInsets.symmetric(vertical: 16.px),
      alignment: Alignment.center,
      child: CircularProgressIndicator(
        strokeWidth: 2.px,
      ),
    );
  }

  Widget _buildAnalysis() {
    final rankingData = analysisData['paiming'] as List? ?? [];

    final data = analysisData['meiri'] ?? {};
    final categories = data['categories'] ?? []; // 时间
    final seriesData = data['series'] ?? []; // 数据

    // 提取legendData
    final legendData =
        (seriesData as List).map((item) => item['name']).toList();
    Logger().i('legendData: $legendData');
    // 确保series数据格式正确
    final series = seriesData.map((item) {
      return {
        'name': item['name'],
        'type': 'line',
        'smooth': true,
        'data': item['data'],
        'symbolSize': 6,
        'lineStyle': {'width': 2},
        'itemStyle': {'borderWidth': 2}
      };
    }).toList();

    final option = '''
  {
    "color": ["#20BD8E", "#288BFF", "#FF8F3E", "#9B55FF", "#FF5555"],
    "animation": false,
    "title": {},
    "tooltip": {
      "trigger": "axis",
      "confine": true,
      "formatter": function(params) {
        var result = params[0].axisValue + '<br/>';
        params.forEach(function(item) {
          var color = item.color;
          result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + color + '"></span>';
          result += item.name + ': ' + item.data + '头<br/>';
        });
        return result;
      }
    },
    "legend": {
      "data": ${jsonEncode(legendData)},
      "top": 0,
      "right": 0,
      "type": "scroll",
      "pageIconSize": [20, 18]
    },
    "grid": {
      "left": "3%",
      "right": "6%",
      "bottom": "3%",
      "top": "40px",
      "containLabel": true
    },
    "toolbox": {
      "feature": {
        "saveAsImage": {
          "show": false
        }
      }
    },
    "xAxis": {
      "type": "category",
      "boundaryGap": false,
      "data": ${jsonEncode(categories)}
    },
    "yAxis": {
      "type": "value",
      "minInterval": 1,
    },
    "series": ${jsonEncode(series)}
  }
  ''';

    Logger().i('ECharts option: $option');

    return SingleChildScrollView(
      padding: EdgeInsets.all(12.px),
      child: Column(
        children: [
          // 害虫排名
          analysisData.isNotEmpty
              ? Column(
                  children: [
                    _buildTitle('害虫排名'),
                    _buildRankingList(),
                  ],
                )
              : const SizedBox.shrink(),
          SizedBox(height: 16.px),

          // 每日虫害
          _buildTitle('每日虫害'),

          Container(
            margin: EdgeInsets.only(top: 12.px),
            padding: EdgeInsets.all(12.px),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8.px),
            ),
            child: SizedBox(
              height: 300.px,
              child: seriesData.isNotEmpty
                  ? Echarts(
                      option: option,
                    )
                  : Center(
                      child: Text(
                        '暂无数据',
                        style: TextStyle(
                          color: Colors.black54,
                          fontSize: 14.px,
                        ),
                      ),
                    ),
            ),
          ),

          SizedBox(height: 16.px),

          // 虫害分析
          _buildInsectAnalysis(),
        ],
      ),
    );
  }

  Widget _buildTitle(String title) {
    return Stack(
      children: [
        Image.asset(
          ImageHelper.wrapAssets('my_field_pest_title_bg.png'),
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Image.asset(
              ImageHelper.wrapAssets('my_field_pest_title_circle.png'),
              width: 16.px,
              height: 16.px,
            ),
            SizedBox(width: 8.px),
            Text(
              title,
              style: TextStyle(
                color: Colors.black,
                fontSize: 14.px,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRankingList() {
    final rankingData = analysisData['paiming'] as List? ?? [];

    return Container(
      padding: const EdgeInsets.only(top: 0, left: 12, right: 12, bottom: 12),
      // 修改内边距
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 16.px),
          ...rankingData
              .map((item) => Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(bottom: 12.px),
                        child: Row(
                          children: [
                            Expanded(
                              child: Text(
                                item['name'] ?? '',
                                style: TextStyle(
                                  color: Colors.black87,
                                  fontSize: 14.px,
                                ),
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 8.px, vertical: 2.px),
                              decoration: BoxDecoration(
                                color: const Color(0xFF16B760).withOpacity(0.1),
                                borderRadius: BorderRadius.circular(4.px),
                              ),
                              child: Text(
                                '${item['value'] ?? 0}头',
                                style: TextStyle(
                                  color: const Color(0xFF16B760),
                                  fontSize: 12.px,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      CombinedProgressBar(
                        progress: item['value'] / rankingData[0]['value'],
                      ),
                    ],
                  ))
              .toList(),
        ],
      ),
    );
  }

  Widget _buildInsectAnalysis() {
    return Container(
      padding: EdgeInsets.all(16.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '水稻虫害',
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 16.px,
                  fontWeight: FontWeight.bold,
                ),
              ),
              InkWell(
                onTap: _showInsectTypePicker,
                child: Row(
                  children: [
                    Text(
                      selectedInsectType ?? '选择其他虫子',
                      style: TextStyle(
                        color: Colors.black54,
                        fontSize: 14.px,
                      ),
                    ),
                    Icon(
                      Icons.arrow_forward_ios,
                      size: 16.px,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 16.px),
          _buildAnalysisItem('始见期', insectAnalysisData['seetime'], 'See times'),
          _buildAnalysisItem(
              '高峰期',
              '${insectAnalysisData['fast']} ${insectAnalysisData['fastCount']}头',
              'Fastgium'),
          _buildAnalysisItem('终见期', insectAnalysisData['end'], 'End in period'),
        ],
      ),
    );
  }

  Widget _buildAnalysisItem(String label, String? value, String name) {
    return Container(
        margin: EdgeInsets.only(bottom: 12.px),
        child: Stack(
          children: [
            Image.asset(
              ImageHelper.wrapAssets('my_field_pest_statistics_title_bg.png'),
            ),
            Padding(
              padding: EdgeInsets.only(left: 8.px, top: 8.px, right: 8.px),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          label,
                          style: TextStyle(
                              color: Colors.black54,
                              fontSize: 18.px,
                              fontWeight: FontWeight.w500),
                        ),
                        Text(
                          name,
                          style: TextStyle(
                            color: const Color(0x33776273),
                            fontSize: 14.px,
                          ),
                        ),
                      ]),
                  Text(
                    value ?? '-',
                    style: TextStyle(
                      color: const Color.fromRGBO(32, 189, 142, 1),
                      fontSize: 14.px,
                    ),
                  ),
                ],
              ),
            )
          ],
        ));
  }
}
