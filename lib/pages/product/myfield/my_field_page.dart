/*
 * @Author: qikai
 * @LastEditors: qikai
 * @Date: 2025-03-17 15:08:50
 * @LastEditTime: 2025-04-11 15:50:51
 * @description:
 */
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/request/my_field_page_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:convert';
import 'dart:typed_data';

import '../../../utils/event_bus.dart';
import '../../../utils/gps/gps_receiver.dart';
import '../../../utils/permission_util.dart';
import 'my_field_map_page.dart';
import 'package:bdh_smart_agric_app/components/bdh_dropdown_menu.dart';

class Crop {
  final String raiseCropsCd;
  final String raiseCropsNm;

  Crop({
    required this.raiseCropsCd,
    required this.raiseCropsNm,
  });

  factory Crop.fromJson(Map<String, dynamic> json) {
    return Crop(
      raiseCropsCd: json['raiseCropsCd']?.toString() ?? '',
      raiseCropsNm: json['raiseCropsNm']?.toString() ?? '',
    );
  }
}

class PlotModel {
  final String year;
  final String? plotBase64;
  final String lon;
  final String lat;
  final String cropType;
  final String plotName;
  final String cropTypeName;
  final String cropBreedName;
  final String orgFullName;
  final String growPatternsId;
  final double contrArea;
  final int? orderDesc;
  final String plotNo;
  final String geomJson;
  final String linkCode;
  final String orgCode;

  PlotModel({
    required this.year,
    this.plotBase64,
    required this.lon,
    required this.lat,
    required this.cropType,
    required this.plotName,
    required this.cropTypeName,
    required this.growPatternsId,
    required this.cropBreedName,
    required this.orgFullName,
    required this.geomJson,
    required this.linkCode,
    required this.orgCode,
    required this.contrArea,
    this.orderDesc,
    required this.plotNo,
  });

  factory PlotModel.fromJson(Map<String, dynamic> json) {
    return PlotModel(
      year: json['year']?.toString() ?? '',
      plotBase64: json['plotBase64'],
      lon: json['lon']?.toString() ?? '',
      lat: json['lat']?.toString() ?? '',
      cropType: json['cropType']?.toString() ?? '',
      plotName: json['plotName']?.toString() ?? '',
      cropTypeName: json['cropTypeName']?.toString() ?? '',
      cropBreedName: json['cropBreedName']?.toString() ?? '',
      orgFullName: json['orgFullName']?.toString() ?? '',
      contrArea: json['contrArea'] is num
          ? (json['contrArea'] as num).toDouble()
          : 0.0,
      orderDesc: json['orderDesc'] as int?,
      plotNo: json['plotNo']?.toString() ?? '',
      geomJson: json['geomJson']?.toString() ?? '',
      linkCode: json['linkCode']?.toString() ?? '',
      orgCode: json['orgCode']?.toString() ?? '',
      growPatternsId: json['growPatternsId']?.toString() ?? '',
    );
  }
}

class MyFieldPage extends StatefulWidget {
  const MyFieldPage({super.key});

  @override
  State<MyFieldPage> createState() => _MyFieldPageState();
}

class _MyFieldPageState extends State<MyFieldPage> {
  static const _defaultCropCode = '88';
  static const _defaultSortValue = '04';

  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<PlotModel> plots = [];
  int currentPage = 1;
  final int pageSize = 10;
  bool isLoading = false;
  bool hasMore = true;
  List<DictNode> yearDics = [];
  List<Crop> crops = [
    Crop(raiseCropsCd: _defaultCropCode, raiseCropsNm: '全部作物')
  ];

  // 选中的值标记
  String selectedYear = DateTime.now().year.toString();
  String selectedSort = _defaultSortValue;
  String selectedCrop = _defaultCropCode;

  final Map<String, dynamic>? param = {
    "year": DateTime.now().year.toString(),
    "staffId": StorageUtil.userInfo()?.data?.staffId,
    "lon": null,
    "lat": null,
    "cropType": null,
    "plotName": null,
    "orderDesc": null
  };

  @override
  void initState() {
    super.initState();
    _loadData();
    _scrollController.addListener(_onScroll);
  }

  void _setupLocationListener() {
    // 先移除可能存在的监听，避免重复注册
    bus.off("location");

    // 注册新的监听
    bus.on("location", (dynamic data) {
      try {
        // 确保数据是LocationResult类型
        if (data is LocationResult) {
          final locationResult = data;
          Logger().i(
              "接收到位置数据：经度=${locationResult.longitude}，纬度=${locationResult.latitude}");

          // 更新状态
          setState(() {
            // 处理可能的空值，并确保类型正确
            param?['lon'] = locationResult.longitude ?? 0;
            param?['lat'] = locationResult.latitude ?? 0;
          });

          // 成功获取后，重新加载地块列表
          if (param?['lon'] != null && param?['lat'] != null) {
            _loadData();
          }
        } else {
          Logger().e("位置数据类型错误: $data");
        }
      } catch (e) {
        Logger().e("处理位置数据异常: $e");
      }
    });

    // 在初始化时尝试获取一次位置
    try {
      GpsReceiver.getInstance().start(true);
    } catch (e) {
      Logger().e("启动位置服务失败: $e");
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (!isLoading && hasMore) {
        _loadMoreData();
      }
    }
  }

  // 搜索地块
  void _searchPlot(String keyword) {
    setState(() {
      isLoading = true;
    });
    param?['plotName'] = keyword.isNotEmpty ? keyword : null;
    _fetchPlotList();
  }

  // 加载数据
  void _loadData() {
    setState(() {
      isLoading = true;
    });
    _onGetList();
    _onGetCropsList();
    _loadYearDictionary();
  }

  void _loadYearDictionary() {
    MyFieldPageService.getDicByKey("year_cd").then((result) {
      Logger().i('年份------------------');
      setState(() {
        yearDics = result.data ?? [];
        isLoading = false;
      });
    }).catchError((error) {
      setState(() {
        isLoading = false;
      });
    });
  }

  void _onGetCropsList() {
    MyFieldPageService.getMyFieldCropsList({
      "staffId": StorageUtil.userInfo()?.data?.staffId,
      "year": param?['year'],
    }).then((res) {
      setState(() {
        if (res['data'] != null && res['data'] is List) {
          crops = (res['data'] as List)
              .map((item) => Crop.fromJson(item))
              .cast<Crop>()
              .toList();
          crops.add(Crop(raiseCropsCd: _defaultCropCode, raiseCropsNm: '全部作物'));
        } else {
          crops = [Crop(raiseCropsCd: _defaultCropCode, raiseCropsNm: '全部作物')];
        }
        isLoading = false;
      });
    }).catchError((error) {
      setState(() {
        isLoading = false;
      });
    });
  }

  void _onGetList() {
    Logger().i(param);
    _fetchPlotList();
  }

  void _fetchPlotList() {
    MyFieldPageService.getMyFieldPlotList(param).then((res) {
      Logger().i('获取地块列表------------------');
      Logger().i(res);
      setState(() {
        if (res['data'] != null && res['data'] is List) {
          plots = (res['data'] as List)
              .map((item) => PlotModel.fromJson(item))
              .toList();
        } else {
          plots = [];
        }
        isLoading = false;
        hasMore = plots.isNotEmpty;
      });
    }).catchError((error) {
      setState(() {
        isLoading = false;
      });
    });
  }

  // 加载更多数据
  Future<void> _loadMoreData() async {
    if (isLoading) return;

    setState(() {
      isLoading = true;
      currentPage++;
    });

    // 模拟网络请求延迟
    await Future.delayed(const Duration(seconds: 1));

    if (currentPage < 3) {
      // 真实场景应在这里请求带有分页参数的下一页数据
    } else {
      setState(() {
        hasMore = false;
      });
    }

    setState(() {
      isLoading = false;
    });
  }

  void _onPlotTap(PlotModel plot) {
    // 跳转到地块详情页面
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MyFieldMapPage(
          plotNo: plot.plotNo,
          year: plot.year,
          plotName: plot.plotName,
          cropTypeName: plot.cropTypeName,
          cropBreedName: plot.cropBreedName,
          contrArea: plot.contrArea.toString(),
          orgFullName: plot.orgFullName,
          growPatternsId: plot.growPatternsId ?? '',
          geomJson: plot.geomJson,
          linkCode: plot.linkCode,
          orgCode: plot.orgCode,
          cropType: plot.cropType,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            color: const Color(0xFFF1F3F8),
          ),
          // 背景图片
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Image.asset(
              ImageHelper.wrapAssets('field_list_top.png'),
              width: 375.px,
              fit: BoxFit.cover,
            ),
          ),
          // 主体内容
          Column(
            children: [
              _buildSearchBar(),
              _buildFilterDropdowns(),
              Expanded(
                child: _buildPlotList(),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.fromLTRB(16, 48, 16, 8),
      color: Colors.transparent,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '请输入地块名称',
          hintStyle: const TextStyle(color: Color(0xFF999999)),
          prefixIcon: const Icon(Icons.search, color: Color(0xFF999999)),
          filled: true,
          fillColor: const Color(0xFFF0F9F4),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(24),
            borderSide: BorderSide.none,
          ),
          contentPadding:
              const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          suffixIcon: IconButton(
            icon: const Icon(Icons.clear, color: Color(0xFF999999)),
            onPressed: () {
              _searchController.clear();
              param?['plotName'] = null;
              _loadData(); // 清空搜索条件，重新加载所有数据
            },
          ),
        ),
        onSubmitted: (value) {
          _searchPlot(value); // 提交搜索
        },
      ),
    );
  }

  Widget _buildFilterDropdowns() {
    return BdhDropdownMenu(
      direction: BdhDropdownMenuDirection.down,
      onMenuOpened: (value) {
        Logger().i('打开第$value个菜单');
      },
      onMenuClosed: (value) {
        Logger().i('关闭第$value个菜单');
      },
      items: [
        BdhDropdownItem(
          label: _getSelectedYearLabel(),
          options: yearDics.map((dictNode) {
            return BdhDropdownItemOption(
              label: dictNode.name!,
              value: dictNode.code!,
              selected: dictNode.code == selectedYear,
            );
          }).toList(),
          onChange: (value) {
            setState(() {
              selectedYear = value[0];
              param?['year'] = value[0];
            });
            _onGetList();
            _onGetCropsList();
            Logger().i('选择年份：$value');
          },
        ),
        BdhDropdownItem(
          label: _getSelectedSortLabel(),
          options: [
            BdhDropdownItemOption(
                label: '综合排序',
                value: _defaultSortValue,
                selected: selectedSort == _defaultSortValue),
            BdhDropdownItemOption(
                label: '时间优先', value: '00', selected: selectedSort == '00'),
            BdhDropdownItemOption(
                label: '面积最大', value: '01', selected: selectedSort == '01'),
            BdhDropdownItemOption(
                label: '面积最小', value: '02', selected: selectedSort == '02'),
            BdhDropdownItemOption(
                label: '离我最近', value: '03', selected: selectedSort == '03'),
          ],
          onChange: (value) {
            setState(() {
              selectedSort = value[0];
              param?['orderDesc'] =
                  value[0] == _defaultSortValue ? null : value[0];
            });
            if (selectedSort == "03") {
              PermissionUtil.requestLocationPermission(
                      context, "以便为您提供所处位置的地块信息")
                  .then((res) {
                if (res == true) {
                  _setupLocationListener();
                  GpsReceiver receiver = GpsReceiver.getInstance();
                  receiver.start(true);
                } else {
                  return BrnDialogManager.showConfirmDialog(context,
                      title: "提示",
                      cancel: '取消',
                      confirm: '确定',
                      message: "需要您开启定位权限, 是否去开启定位权限？", onConfirm: () {
                    Navigator.of(context).pop();
                    openAppSettings();
                  }, onCancel: () {
                    Navigator.of(context).pop();
                  });
                }
              });
            }
            _onGetList();
            Logger().i('选择排序：$value');
          },
        ),
        BdhDropdownItem(
          label: _getSelectedCropLabel(),
          options: crops.map((cropsItem) {
            return BdhDropdownItemOption(
              label: cropsItem.raiseCropsNm,
              value: cropsItem.raiseCropsCd,
              selected: cropsItem.raiseCropsCd == selectedCrop,
            );
          }).toList(),
          onChange: (value) {
            setState(() {
              selectedCrop = value[0];
              param?['cropType'] =
                  value[0] == _defaultCropCode ? null : value[0];
            });
            _onGetList();
            Logger().i('选择作物：$value');
          },
        ),
      ],
    );
  }

  String _getSelectedYearLabel() {
    final selected = yearDics.firstWhere(
      (element) => element.code == selectedYear,
      orElse: () => DictNode(name: DateTime.now().year.toString()),
    );
    return selected.name ?? DateTime.now().year.toString();
  }

  String _getSelectedSortLabel() {
    switch (selectedSort) {
      case '00':
        return '时间优先';
      case '01':
        return '面积最大';
      case '02':
        return '面积最小';
      case '03':
        return '离我最近';
      default:
        return '综合排序';
    }
  }

  String _getSelectedCropLabel() {
    final selected = crops.firstWhere(
      (element) => element.raiseCropsCd == selectedCrop,
      orElse: () => Crop(raiseCropsCd: _defaultCropCode, raiseCropsNm: '全部作物'),
    );
    return selected.raiseCropsNm;
  }

  Widget _buildPlotList() {
    // if (isLoading && plots.isEmpty) {
    //   return const Center(child: CircularProgressIndicator());
    // }

    if (plots.isEmpty) {
      return const Center(child: Text('暂无地块数据'));
    }

    return GridView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.6,
        crossAxisSpacing: 10,
        mainAxisSpacing: 10,
      ),
      itemCount: plots.length + (isLoading || hasMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index < plots.length) {
          return _buildPlotCard(plots[index]);
        } else {
          return isLoading
              ? const Center(child: CircularProgressIndicator())
              : const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildPlotCard(PlotModel plot) {
    return GestureDetector(
      onTap: () => _onPlotTap(plot),
      child: Container(
        padding: const EdgeInsets.all(3),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(4),
                width: double.infinity,
                decoration: const BoxDecoration(
                  color: Color(0xFFF0F9F4),
                  borderRadius: BorderRadius.vertical(top: Radius.circular(4)),
                ),
                child: _buildPlotImage(plot),
              ),
            ),
            _buildPlotInfo(plot),
          ],
        ),
      ),
    );
  }

  Widget _buildPlotImage(PlotModel plot) {
    return plot.plotBase64 != null && plot.plotBase64!.isNotEmpty
        ? Image.memory(
            _decodeBase64Image(plot.plotBase64!),
            fit: BoxFit.cover,
            width: 169.px,
            height: 245.px,
            errorBuilder: (context, error, stackTrace) => const Center(
              child: Icon(Icons.grass, size: 48, color: Colors.green),
            ),
          )
        : const Center(
            child: Icon(Icons.grass, size: 48, color: Colors.green),
          );
  }

  Widget _buildPlotInfo(PlotModel plot) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            plot.plotName,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            '${plot.cropTypeName}${plot.cropBreedName.isNotEmpty ? " ${plot.cropBreedName}" : ""}',
            style: TextStyle(color: Colors.grey[600]),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Text(
                '${plot.contrArea} 亩',
                style: const TextStyle(
                  color: Colors.green,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              Text(
                '${plot.year}年',
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Container(
            padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              color: Colors.grey[100],
            ),
            child: Text(
              plot.orgFullName,
              style: TextStyle(color: Colors.grey[600]),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          )
        ],
      ),
    );
  }

  // 解码Base64图片
  Uint8List _decodeBase64Image(String base64String) {
    try {
      String processedInput = base64String;
      // 检查字符串是否包含 data:image 前缀，如果有则需要处理
      if (base64String.contains("data:image")) {
        processedInput = base64String.split(",")[1];
      }
      return base64Decode(processedInput);
    } catch (e) {
      Logger().e('解码Base64图片失败: $e');
      // 返回一个1x1像素的透明图片
      return Uint8List.fromList([0, 0, 0, 0]);
    }
  }
}
