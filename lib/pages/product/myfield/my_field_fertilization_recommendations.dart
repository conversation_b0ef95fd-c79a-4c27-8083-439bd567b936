/*
 * <AUTHOR>
 * @description: 施肥建议卡片
 * @date 2025/04/09 09:24:37
*/

import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/request/my_field_page_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_tool.dart';
import 'package:bdh_smart_agric_app/utils/tile_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:logger/logger.dart';

import '../../../utils/image_util.dart';

class MyFieldFertilizationRecommendations extends StatefulWidget {
  final String plotNo;
  final String year;
  final String orgFullName;
  final String cropBreedName;
  final String cropTypeName;
  final String prodProcessName;
  final String title;
  final String contrArea;
  final String cropType;

  const MyFieldFertilizationRecommendations(
      {super.key,
      required this.plotNo,
      required this.year,
      required this.orgFullName,
      required this.cropBreedName,
      required this.cropTypeName,
      required this.prodProcessName,
      required this.title,
      required this.cropType,
      required this.contrArea});

  @override
  State<MyFieldFertilizationRecommendations> createState() =>
      _MyFieldFertilizationRecommendationsState();
}

class _MyFieldFertilizationRecommendationsState
    extends State<MyFieldFertilizationRecommendations>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  Map<String, dynamic>? _fertData;
  List<dynamic>? _selectedFert;

  bool isBase = true;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _fetchFertilizationData();
    loadData();
  }

  loadData() async {
    var res = {};
    if (isBase) {
      res = await MyFieldPageService.recJsonByPlotNoDry(
          {"plotNo": widget.plotNo, "statYear": widget.year});
    } else {
      res = await MyFieldPageService.addJsonByPlotNoDry(
          {"plotNo": widget.plotNo, "statYear": widget.year});
    }

    if (res['code'] == 0) {
      Logger().i(res);

      var data = res['data'];
      var geoJson = data['geoJSON'];
      colorMap.clear();
      int index = 0;
      for (String key in data['spColors'].keys) {
        String k = double.parse(key).toString();
        if (index >= allColors.length) {
          index = 0;
        }
        colorMap[k] = allColors[index];
        index++;
      }

      // _loadRemoteSensingData();
      _parseSimpleGeoJson(geoJson);
    } else {
      colorMap.clear();
      setState(() {});
    }
  }

  final List<String> allColors = [
    "#9abaef", // 浅蓝色
    "#ef9a9a", // 浅红色
    "#a5d6a7", // 浅绿色
    "#ffcc80", // 橙色
    "#ce93d8", // 紫色
    "#80deea", // 青色
    "#fff59d", // 黄色
    "#bcaaa4", // 棕色
    "#90caf9", // 天蓝色
    "#f48fb1", // 粉红色
    "#81c784", // 深绿色
    "#ffb74d", // 深橙色
    "#b39ddb", // 深紫色
    "#4db6ac", // 蓝绿色
    "#ffd54f", // 金色
    "#a1887f", // 深棕色
    "#64b5f6", // 深天蓝
    "#f06292", // 深粉色
    "#66bb6a", // 翠绿色
    "#ffa726", // 橘色
    "#9575cd", // 靛蓝色
    "#26a69a", // 青绿色
    "#ffc107", // 琥珀色
    "#8d6e63", // 褐色
    "#42a5f5", // 亮蓝色
    "#ec407a", // 玫瑰红
    "#4caf50", // 森林绿
    "#ff9800", // 深橘色
    "#7e57c2", // 深靛蓝
    "#009688", // 墨绿色
    "#ffb300", // 深金色
    "#795548", // 深褐色
    "#2196f3", // 蓝色
    "#e91e63", // 洋红色
    "#388e3c", // 深森林绿
    "#f57c00", // 暗橙色
    "#673ab7", // 紫罗兰
    "#00796b", // 深青色
    "#ffa000", // 琥珀金
    "#5d4037", // 深棕褐色
  ];
  Map<String, dynamic> colorMap = {}; //色值
  List<Polygon> _polygons = [];
  final List<LatLng> points = []; // 用于存储点坐标的列表
  final MapController _mapController = MapController();
  var flags = InteractiveFlag.all - InteractiveFlag.rotate; // 禁用地图旋转
  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _parseSimpleGeoJson(Map geoJsonMap) {
    try {
      var features = geoJsonMap['features'] as List;
      List<List<LatLng>> allPolygonPoints = [];
      List<String> labelList = [];
      // List<String> nameList = [];
      List<String> colorList = [];
      int sIndex = 0;
      // 遍历所有 features
      for (var feature in features) {
        sIndex++;
        List coordinates = feature['geometry']['coordinates'] as List;
        String type = feature['geometry']['type'];
        // var color = feature['properties']['color'];
        String value =
            double.parse(feature['properties']['VALUE'].toString()).toString();
        // String name = feature['properties']['NAME'].toString();
        // name = name.substring(11);

        // 处理每个 feature 的坐标
        if (coordinates.length == 1) {
          // 单一图层情况
          var polygonCoords = [];
          if (coordinates[0].length == 1) {
            polygonCoords = coordinates[0][0];
          } else {
            polygonCoords = coordinates[0];
          }
          List<LatLng> polygonPoints = _extractPoints(polygonCoords);
          if (polygonPoints.isNotEmpty) {
            allPolygonPoints.add(polygonPoints);
            if (colorMap[value.toString()] != null) {
              colorList.add(colorMap[value.toString()]);
              labelList.add(value.toString());
              // nameList.add(name);
            }
          }
        } else if (coordinates.length > 1) {
          debugPrint('----value-----: ${coordinates.length}');
          // 多图层情况
          for (int i = 0; i < coordinates.length; i++) {
            var currentCoord = coordinates[i];
            List polygonCoords;

            // final polygonCoords = coordinates[i][0]; // 每个图层取第一个子元素
            if (currentCoord.length == 1) {
              // 如果是 [[[x,y], [x,y]]] 的结构
              polygonCoords = currentCoord[0];
            } else if (currentCoord[0] is List && currentCoord[0][0] is List) {
              // 如果是 [[[[x,y], [x,y]]]] 的结构
              polygonCoords = currentCoord[0];
            } else {
              // 如果是 [[x,y], [x,y]] 的结构
              polygonCoords = currentCoord;
            }
            List<LatLng> polygonPoints = _extractPoints(polygonCoords);
            if (polygonPoints.isNotEmpty) {
              allPolygonPoints.add(polygonPoints);
              if (colorMap[value.toString()] != null) {
                colorList.add(colorMap[value.toString()]);
                labelList.add(value.toString());
                // nameList.add(name);
              }
            }
          }
        }
      }

      if (allPolygonPoints.isEmpty) {
        throw Exception('无有效坐标点');
      }

      // 生成包含所有点的总边界
      List<LatLng> allPoints =
          allPolygonPoints.expand((points) => points).toList();

      // 创建不同颜色的多边形列表
      List<Polygon> polygons = [];

      for (int i = 0; i < allPolygonPoints.length; i++) {
        Color? fillColor;
        if (i < colorList.length) {
          // fillColor = HexColor(colorList[i - 1]);
          fillColor = HexColor(colorList[i]);
        }
        // Logger().i(nameList[i]);
        if (fillColor != null) {
          Color borderColor = Colors.white; //fillColor.withOpacity(0.8);
// nameList[i] == "8" ||

          // if (labelList[i] == "55.0") {}

          polygons.add(Polygon(
            points: allPolygonPoints[i],
            color:
                fillColor, //  nameList[i] == "8-1-6" ? Colors.green : fillColor,
            borderColor: borderColor,
            borderStrokeWidth: 1,
            label:
                labelList[i], // nameList[i] == "8-1-6" ? "8-1-6" : nameList[i],
            labelStyle: TextStyle(
              color: Colors.black,
              fontSize: 8.px,
              fontWeight: FontWeight.normal,
            ),
          ));
        }
      }

      setState(() {
        _polygons = polygons;
        points.clear();
        points.addAll(allPoints);
      });
      Future.delayed(const Duration(milliseconds: 100), () {
        _fitBounds();
      });
    } catch (e, stack) {
      debugPrint('GeoJSON处理失败: $e\n$stack');
    }
  }

  // 从坐标数组中提取点
  List<LatLng> _extractPoints(List polygonCoords) {
    List<LatLng> result = [];

    for (var coord in polygonCoords) {
      if (coord is List && coord.length >= 2) {
        try {
          // debugPrint('处理坐标: $coord');
          // 确保坐标值是数字类型
          var x = coord[0];
          var y = coord[1];

          // debugPrint(
          // '原始坐标值 - x: $x (${x.runtimeType}), y: $y (${y.runtimeType})');

          // 处理可能的字符串类型
          if (x is String) {
            x = x.trim();
          }
          if (y is String) {
            y = y.trim();
          }

          // 转换为double
          final double xValue =
              x is num ? x.toDouble() : double.parse(x.toString());
          final double yValue =
              y is num ? y.toDouble() : double.parse(y.toString());

          // debugPrint('转换后坐标值 - x: $xValue, y: $yValue');

          final point = LatLng(yValue, xValue);
          result.add(point);
        } catch (e, stack) {
          debugPrint('坐标转换错误: $e');
          // debugPrint('错误堆栈: $stack');
          // debugPrint('问题坐标: $coord');
          continue;
        }
      }
    }

    return result;
  }

  // 获取施肥建议卡数据
  Future<void> _fetchFertilizationData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await MyFieldPageService.getMyFieldSoilFertAdviceCard(
          {"plotNo": widget.plotNo, "cropType": widget.cropType});
      if (response['success'] == true &&
          response['data'] != null &&
          response['data'].isNotEmpty) {
        setState(() {
          _fertData = response['data'].firstWhere(
              (item) => item['raiseCrop'] == widget.cropType,
              orElse: () => null);
          Logger().i('_fertData: $_fertData');
          _selectedFert = _fertData?['adviseCardFertsApp'];
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _selectedFert = null;
          _fertData = null;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          color: const Color.fromRGBO(240, 243, 248, 1),
          borderRadius: BorderRadius.circular(10.px),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAppBar(),

            // 当无数据时显示提示信息
            // if (_fertData == null && colorMap.keys.isEmpty)
            //   Expanded(
            //     child: _buildFertilizationNodata(),
            //   )
            // // 有数据时显示内容
            // else
            Expanded(
              child: Column(
                children: [
                  // 基本信息
                  _fertData == null ? Container() : _buildBasicInfo(),
                  // 标签页
                  Container(
                    margin:
                        EdgeInsets.only(top: 12.px, left: 12.px, right: 12.px),
                    decoration: BoxDecoration(
                      color: const Color(0xFFEDF8F0),
                      border: Border(
                          top: BorderSide(
                              color: Colors.grey.shade200, width: 0.5)),
                    ),
                    child: TabBar(
                      controller: _tabController,
                      labelColor: Colors.green,
                      unselectedLabelColor: Colors.black,
                      indicatorColor: Colors.green,
                      indicatorSize: TabBarIndicatorSize.label,
                      indicatorWeight: 3.px,
                      dividerColor: Colors.transparent,
                      tabs: const [
                        Tab(text: '推荐施肥'),
                        Tab(text: '养分情况'),
                        Tab(text: '处方图'),
                      ],
                    ),
                  ),

                  // 标签页内容
                  Expanded(
                    child: Container(
                      height: 500.px,
                      margin: EdgeInsets.only(
                          left: 12.px, right: 12.px, bottom: 12.px),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10.px)),
                      child: TabBarView(
                        physics: const NeverScrollableScrollPhysics(),
                        controller: _tabController,
                        children: [
                          _fertData == null
                              ? _buildFertilizationNodata()
                              : _buildFertilizationRecommendation(),
                          _fertData == null
                              ? _buildFertilizationNodata()
                              : _buildNutrientInfo(),
                          _buildMap()
                        ],
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFertilizationNodata() {
    return Container(
      margin: EdgeInsets.all(12.px),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Text(
        '暂无数据',
        style: TextStyle(
          decoration: TextDecoration.none,
          fontSize: 14.px,
          fontWeight: FontWeight.w400,
          color: const Color.fromRGBO(0, 0, 0, 0.4),
        ),
      ),
    );
  }

  Widget _buildMap() {
    return Column(
      children: [
        _selectCard(),
        if (colorMap.keys.isNotEmpty)
          Padding(
            padding: EdgeInsets.all(12.px),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 240.px,
                  child: Stack(
                    children: [
                      FlutterMap(
                        mapController: _mapController,
                        options: MapOptions(
                          onTap: (_, __) {
                            debugPrint('点击了地图');
                          },
                          onMapReady: () {
                            _fitBounds();
                          },
                          minZoom: 3,
                          maxZoom: 19,
                          keepAlive: true,
                          initialZoom: 15,
                          interactionOptions: InteractionOptions(
                            flags: flags,
                          ),
                        ),
                        children: [
                          TileLayerUtil.tileLayer(TianDiTuType.cia),
                          TileLayerUtil.tileLayer(TianDiTuType.vec),
                          TileLayerUtil.tileLayer(TianDiTuType.img),
                          PolygonLayer(
                            polygons: _polygons,
                          ),
                        ],
                      ),
                      _buildLocationButton(),
                    ],
                  ),
                ),
                SizedBox(height: 12.px),
                Wrap(
                  direction: Axis.horizontal,
                  spacing: 8.px,
                  runSpacing: 8.px,
                  children: colorMap.keys.map((String e) {
                    return Container(
                      margin: EdgeInsets.only(right: 12.px),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 13.px,
                            height: 13.px,
                            decoration: BoxDecoration(
                              color: HexColor(colorMap[e]),
                            ),
                          ),
                          SizedBox(width: 4.px),
                          Text(e,
                              style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 14.px,
                                  fontWeight: FontWeight.normal)),
                        ],
                      ),
                    );
                  }).toList(),
                )
              ],
            ),
          ),
      ],
    );
  }

  Widget _selectCard() {
    return Padding(
      padding: EdgeInsets.only(left: 12.px, right: 12.px, top: 12.px),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                isBase = true;
                loadData();
              });
            },
            child: Container(
              alignment: Alignment.center,
              width: 50.px,
              height: 34.px,
              decoration: BoxDecoration(
                color: isBase
                    ? const Color.fromARGB(255, 9, 171, 230)
                    : const Color.fromARGB(255, 186, 186, 186),
                borderRadius: BorderRadius.circular(8.px),
              ),
              child: Text('基肥',
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 14.px,
                      fontWeight: FontWeight.w500)),
            ),
          ),
          SizedBox(width: 12.px),
          GestureDetector(
              onTap: () {
                setState(() {
                  isBase = false;
                  loadData();
                });
              },
              child: Container(
                alignment: Alignment.center,
                width: 50.px,
                height: 34.px,
                decoration: BoxDecoration(
                  color: !isBase
                      ? const Color.fromARGB(255, 9, 171, 230)
                      : const Color.fromARGB(255, 186, 186, 186),
                  borderRadius: BorderRadius.circular(8.px),
                ),
                child: Text('追肥',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500)),
              ))
        ],
      ),
    );
  }

  Widget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      centerTitle: true,
      leading: IconButton(
        icon: Image.asset(
          ImageHelper.wrapAssets('field_left.png'),
          width: 24.px,
          height: 24.px,
          color: Colors.black,
        ),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        '施肥建议卡',
        style: TextStyle(
          decoration: TextDecoration.none,
          color: Colors.black,
          fontSize: 16.px,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // 添加一个定位按钮
  Widget _buildLocationButton() {
    return Positioned(
      right: 16.px,
      bottom: 16.px,
      child: GestureDetector(
        onTap: () {
          if (points.isNotEmpty) {
            // 重新计算边界并定位
            final bounds = LatLngBounds.fromPoints(points);
            _mapController.fitCamera(
              CameraFit.bounds(
                bounds: bounds,
                padding: EdgeInsets.all(40.px),
                maxZoom: 18,
              ),
            );
          }
        },
        child: Container(
          width: 40.px,
          height: 40.px,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.px),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Icon(
            Icons.my_location,
            size: 24.px,
            color: Colors.green,
          ),
        ),
      ),
    );
  }

  void _fitBounds() {
    try {
      if (points.isEmpty) return;

      final bounds = LatLngBounds.fromPoints(points);

      // 修改缩放配置
      _mapController.fitCamera(
        CameraFit.bounds(
          bounds: bounds,
          padding: EdgeInsets.all(40.px),
          maxZoom: 18,
          minZoom: 3, // 添加最小缩放级别
        ),
      );

      // 如果上面的方法失败，尝试直接设置中心点和缩放级别
      if (_mapController.camera.zoom < 3) {
        _mapController.move(
          bounds.center, // 使用边界的中心点
          10, // 设置一个合适的缩放级别
        );
      }
    } catch (e) {
      debugPrint('自动缩放失败: $e');

      // 发生错误时的备选方案
      // if (points.isNotEmpty) {
      //   // 使用第一个点作为中心点
      //   _mapController.move(
      //     points.first,
      //     10, // 使用固定的缩放级别
      //   );
      // }
    }
  }

  // 基本信息区域
  Widget _buildBasicInfo() {
    return Container(
        margin: EdgeInsets.all(12.px),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.px),
          color: Colors.white,
        ),
        child: Column(
          children: [
            // 基本信息区域
            Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 8.px, horizontal: 10.px),
              decoration: BoxDecoration(
                border: Border(
                  left: BorderSide(color: Colors.green, width: 3.px),
                ),
              ),
              child: Text(
                '基本情况',
                textAlign: TextAlign.left,
                style: TextStyle(
                  decoration: TextDecoration.none,
                  fontSize: 16.px,
                  fontWeight: FontWeight.bold,
                  color: const Color.fromRGBO(51, 51, 51, 1),
                ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.px),
                color: Colors.white,
              ),
              child: Padding(
                padding: EdgeInsets.all(16.px),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildInfoRow('统一编号', _fertData!['uniteNo'] ?? ''),
                    Divider(height: 25.px, color: Colors.grey.shade200),
                    _buildInfoRow('地块编号', _fertData!['plotNo'] ?? ''),
                    Divider(height: 25.px, color: Colors.grey.shade200),
                    _buildInfoRow('地块面积', '${_fertData!['contrArea'] ?? 0}亩'),
                    Divider(height: 25.px, color: Colors.grey.shade200),
                    _buildInfoRow(
                        '目标产量', '${_fertData!['targetYield'] ?? 0}kg/亩'),
                  ],
                ),
              ),
            )
          ],
        ));
  }

  // 基本信息项
  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            decoration: TextDecoration.none,
            fontSize: 14.px,
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: TextStyle(
            decoration: TextDecoration.none,
            fontSize: 14.px,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  // 推荐施肥选项卡内容
  Widget _buildFertilizationRecommendation() {
    if (_selectedFert == null || _selectedFert!.isEmpty) {
      return const Center(child: Text('暂无推荐施肥数据'));
    }

    // 创建表头
    List<Widget> headerWidgets = [
      _buildTableHeaderCell('肥料名称'),
    ];
    //水稻
    if (widget.cropType == "1") {
      headerWidgets.add(_buildTableHeaderCell('基肥\n(kg/亩)'));
      headerWidgets.add(_buildTableHeaderCell('糵肥\n(kg/亩)'));
      headerWidgets.add(_buildTableHeaderCell('穗肥\n(kg/亩)'));
    }
    //玉米
    else if (widget.cropType == "2") {
      headerWidgets.add(_buildTableHeaderCell('基肥\n(kg/亩)'));
      headerWidgets.add(_buildTableHeaderCell('追肥\n(kg/亩)'));
    } else if (widget.cropType == "3") {
      //大豆
      headerWidgets.add(_buildTableHeaderCell('基肥\n(kg/亩)'));
    }

    headerWidgets.add(_buildTableHeaderCell('亩施肥量\n(kg/亩)'));
    headerWidgets.add(_buildTableHeaderCell('总施肥量\n(kg)'));

    return SingleChildScrollView(
      hitTestBehavior: HitTestBehavior.translucent,
      child: Column(
        children: [
          SingleChildScrollView(
            hitTestBehavior: HitTestBehavior.translucent,
            scrollDirection: Axis.horizontal,
            child: Column(
              children: [
                // 表头
                Container(
                  width: double.parse((95 * headerWidgets.length).toString()),
                  color: Colors.white,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: headerWidgets,
                  ),
                ),

                // 循环显示每个配方
                ..._selectedFert!.map((currentFert) {
                  final items = currentFert['item'] as List? ?? [];

                  return Column(
                    children: [
                      Container(
                        width: double.parse(
                            (95 * headerWidgets.length).toString()),
                        margin: EdgeInsets.only(bottom: 8.px),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(8.px),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 每个配方的肥料数据行
                            ...items.map((item) {
                              String effectContent =
                                  item['effectContent'] ?? '';
                              return Container(
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom:
                                        BorderSide(color: Colors.grey.shade200),
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    _buildTableDataCell(
                                      item['fertName'] != null
                                          ? '${effectContent.isNotEmpty ? effectContent : ""}${item['fertName']}'
                                          : '',
                                      color: const Color(0xFF00A853),
                                    ),
                                    getBuildTableDataCellByCropType(item),
                                    _buildTableDataCell(
                                      item['fertPerMu']?.toString() ?? '0',
                                    ),
                                    _buildTableDataCell(
                                      item['fertTotalAmount']?.toString() ??
                                          '0',
                                    ),
                                  ],
                                ),
                              );
                            }),

                            // SizedBox(
                            //   height: 20.px,
                            // ),
                          ],
                        ),
                      ),
                      // 每个配方下方显示配方信息
                      (currentFert['formulaFertName'] ?? '').isNotEmpty
                          ? Container(
                              clipBehavior: Clip.antiAlias,
                              width: double.parse(
                                  (95 * headerWidgets.length).toString()),
                              // margin: EdgeInsets.only(bottom: 8.px),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8.px),
                              ), // 6个单元格的总宽度
                              child: Column(
                                children: [
                                  formulaRow(
                                      currentFert['formulaFertName'] ?? ''),
                                  amountRow(currentFert['formulaFertSum']
                                          ?.toString() ??
                                      '0'),
                                ],
                              ),
                            )
                          : Container(),
                    ],
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget getBuildTableDataCellByCropType(item) {
    if (widget.cropType == "1") {
      return Row(
        children: [
          _buildTableDataCell(
            item['recommFertBase']?.toString() ?? '-',
          ),
          _buildTableDataCell(
            item['recommFertTill']?.toString() ?? '-',
          ),
          _buildTableDataCell(
            item['recommFertSpike']?.toString() ?? '-',
          ),
        ],
      );
    } else if (widget.cropType == "2") {
      return Row(
        children: [
          _buildTableDataCell(
            item['recommFertBase']?.toString() ?? '-',
          ),
          _buildTableDataCell(
            item['recommFertAppend']?.toString() ?? '-',
          ),
        ],
      );
    } else if (widget.cropType == "3") {
      return Row(
        children: [
          _buildTableDataCell(
            item['recommFertBase']?.toString() ?? '-',
          ),
        ],
      );
    } else {
      return Container();
    }
  }

  // 基肥配方肥
  Widget formulaRow(String value) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.px),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          SizedBox(width: 16.px),
          Text(
            '基肥配方肥',
            style: TextStyle(
              decoration: TextDecoration.none,
              fontSize: 14.px,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF00A853),
            ),
          ),
          SizedBox(width: 30.px),
          Expanded(
            child: Text(
              value,
              textAlign: TextAlign.left,
              style: TextStyle(
                decoration: TextDecoration.none,
                fontSize: 14.px,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 施肥量
  Widget amountRow(String value) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.px),
      margin: EdgeInsets.only(bottom: 24.px),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          SizedBox(width: 16.px),
          Text(
            '施肥量',
            style: TextStyle(
              decoration: TextDecoration.none,
              fontSize: 14.px,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF00A853),
            ),
          ),
          SizedBox(width: 56.px),
          Expanded(
            child: Text(
              '${value}kg/亩',
              textAlign: TextAlign.left,
              style: TextStyle(
                decoration: TextDecoration.none,
                fontSize: 14.px,
                color: Colors.black,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 养分情况选项卡内容
  Widget _buildNutrientInfo() {
    if (_fertData == null) {
      return const Center(child: Text('暂无养分信息'));
    }

    return Column(
      children: [
        // 养分表头
        Container(
          padding: EdgeInsets.symmetric(vertical: 12.px),
          decoration: const BoxDecoration(
            color: Colors.white,
          ),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  '监测项目',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    decoration: TextDecoration.none,
                    fontSize: 14.px,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF00A853),
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '速效养分',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    decoration: TextDecoration.none,
                    fontSize: 14.px,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '应施肥纯量',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    decoration: TextDecoration.none,
                    fontSize: 14.px,
                    fontWeight: FontWeight.w500,
                    color: Colors.black,
                  ),
                ),
              ),
            ],
          ),
        ),

        Expanded(
          child: SingleChildScrollView(
              child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8.px),
            ),
            child: Column(
              children: [
                // 氮
                _buildNutrientRow(
                    '氮(mg/kg)',
                    _fertData!['nutrientN']?.toString() ?? '-',
                    _fertData!['pureFertN']?.toString() ?? '-'),

                // 磷
                _buildNutrientRow(
                    '磷(mg/kg)',
                    _fertData!['nutrientP']?.toString() ?? '-',
                    _fertData!['pureFertP']?.toString() ?? '-'),

                // 钾
                _buildNutrientRow(
                    '钾(mg/kg)',
                    _fertData!['nutrientK']?.toString() ?? '-',
                    _fertData!['pureFertK']?.toString() ?? '-'),

                // 合计
                _buildNutrientRow(
                    '合计',
                    _fertData!['nutrientSum']?.toString() ?? '-',
                    _fertData!['pureFertN'] != null &&
                            _fertData!['pureFertP'] != null &&
                            _fertData!['pureFertK'] != null
                        ? (_fertData!['pureFertN'] +
                                _fertData!['pureFertP'] +
                                _fertData!['pureFertK'])
                            .toStringAsFixed(1)
                        : '-'),

                // pH值
                _buildNutrientRow(
                    'pH值', _fertData!['nutrientPh']?.toString() ?? '-', '-'),

                // 有机质
                _buildNutrientRow('有机质(g/kg)',
                    _fertData!['nutrientOrganic']?.toString() ?? '-', '-'),
              ],
            ),
          )),
        ),
      ],
    );
  }

  // 养分情况行
  Widget _buildNutrientRow(String label, String value1, String value2) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12.px),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                decoration: TextDecoration.none,
                fontSize: 14.px,
                color: Colors.black87,
              ),
              maxLines: 1,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value1,
              textAlign: TextAlign.center,
              style: TextStyle(
                decoration: TextDecoration.none,
                fontSize: 14.px,
                color: Colors.black87,
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              value2,
              textAlign: TextAlign.center,
              style: TextStyle(
                decoration: TextDecoration.none,
                fontSize: 14.px,
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 表格头部单元格
  Widget _buildTableHeaderCell(String text) {
    return Container(
      height: 70.px,
      padding: EdgeInsets.symmetric(horizontal: 10.px, vertical: 12.px),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade200),
        ),
      ),
      alignment: Alignment.center,
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: TextStyle(
          decoration: TextDecoration.none,
          fontSize: 14.px,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  // 表格数据单元格
  Widget _buildTableDataCell(String text, {Color? color}) {
    return Container(
      width: 90.px,
      padding: EdgeInsets.symmetric(horizontal: 10.px, vertical: 12.px),
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: TextStyle(
          decoration: TextDecoration.none,
          fontSize: 14.px,
          color: color ?? Colors.black87,
        ),
      ),
    );
  }
}
