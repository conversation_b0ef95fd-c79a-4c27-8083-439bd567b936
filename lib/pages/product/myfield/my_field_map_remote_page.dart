import 'dart:convert';
import 'dart:math';
import 'package:bdh_smart_agric_app/pages/product/myfield/video/rtsp_player_page.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/tile_util.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';

import '../../../model/my_field_device_list_model.dart';
import '../../../model/my_field_map_ai_model.dart';
import '../../../model/myfield_monitorcatalogtree_model.dart';
import '../../../model/weather_day_info_model.dart';
import '../../../model/weather_real_time_model.dart';
import '../../../utils/request/my_field_page_service.dart';

//遥感
class MyFieldMapRemotePage extends StatefulWidget {
  final String plotNo;
  final String year;
  final String plotName;
  final String cropBreedName; // 作物品种名称
  final String cropTypeName; // 作物
  final String contrArea;
  final String orgFullName;
  final String growPatternsId; // 种植方案 id
  final String geomJson;
  final String linkCode;
  final String orgCode;
  final String cropType;

  // final String orgCode;
  // final String id;

  const MyFieldMapRemotePage({
    super.key,
    required this.plotNo,
    required this.year,
    required this.plotName,
    required this.cropBreedName,
    required this.cropTypeName,
    required this.contrArea,
    required this.orgFullName,
    required this.growPatternsId, // 种植方案 id
    required this.geomJson, // 添加geomJson参数
    required this.linkCode, // 添加geomJson参数
    required this.orgCode,
    required this.cropType,
    // required this.orgCode,
    // required this.id,
  });

  @override
  State<MyFieldMapRemotePage> createState() => _MyFieldMapPageState();
}

class _MyFieldMapPageState extends State<MyFieldMapRemotePage>
    with TickerProviderStateMixin {
  final MapController _mapController = MapController();
  List<Polygon> _polygons = [];
  List<Marker> locationMarkers = [];
  List<Marker> deviceMarkers = [];
  var bounds = LatLngBounds(const LatLng(44, 121), const LatLng(52, 134));
  var flags = InteractiveFlag.all - InteractiveFlag.rotate; // 禁用地图旋转
  bool isExpanded = false;
  double bottomPanelHeight = 0.3; // 底部面板高度比例，初始为屏幕高度的30%
  List<MyPlotDeviceModel> deviceList = [];
  final List<LatLng> points = []; // 用于存储点坐标的列表
  List<dynamic> remindList = []; // AI 提醒列表
  WeatherDayData? weatherDayData;
  List<MonitorcatalogtreeItemData>? monitorcatalogtreeItemDatas;

  Map<String, dynamic>? monitorcatalogtreeItemMaps;
  late AnimationController _controller;
  late Animation<int> _charCountAnimation;

  // 种植方案数据
  List<dynamic> plantingPlanData = [];
  WeatherRealData? weatherRealData;

  int selectAiIndex = 0; //选择ai 地块编号

  int selectYear = 0;
  List<String> yearTypes = ["2025", "2024", "2023", "2022"];

  int selectCrop = 1;
  List<String> cropTypes = [];

  //右上角筛选
  List<String> selectRights = [];

  Map<int, List<Children>> cropTypesChildrens = {}; //准备期 播种期，下面对应的高呈等;

  int selectRightType = 1;

  List<String> codes = []; //为了请求地块图片

  List<String> imageLabels = [""]; //为了请求ai

  List<String> bottomItems = []; //底部地块

  List<dynamic> leftChildren = []; //左下角图层 湿度， 较湿，颜色内容等

  late String bbox = ""; //图层定位
  late List<String> bboxs = []; //图层定位
  late String layers = ""; //图层
  late String styles = ""; //样式
  late String regionId = ""; //样式id
  late String aides = ""; //ai回答
  late AiData aiData;

  var screenWidth;
  var screenHight;

  @override
  void initState() {
    super.initState();
    _loadData(isInit: true);
    plantingPlanData = [];
  }

  void _loadData({bool isInit = false}) async {
    await getPlantingPlan();
    // await getDeviceList();
    await monitorCatalogTree(isInit);
  }

  Future<void> getPlantingPlan() async {
    if (widget.growPatternsId.isEmpty) {
      return;
    }
    MyFieldPageService.getMyFieldPlantingPlan({
      "landNo": widget.plotNo,
      "statYear": widget.year,
      "growPatternsId": widget.growPatternsId
    }).then((res) {
      Logger().i(res);
      setState(() {
        plantingPlanData = res['data'];
      });
    });
  }

  Future<void> monitorCatalogTree(bool isInit) async {
    cropTypesChildrens.clear();
    selectRights.clear();
    MyFieldPageService.monitorCatalogTree({
      "orgCode": 86,
      "year": yearTypes[selectYear],
      "code": "MY_LAND",
      "tags": null
    }).then((res) {
      //       String cropCode = "";
      // if (res.data != null) {
      //   for (MonitorcatalogtreeItemData item in res.data! as List) {
      //     if (item.children != null) {
      //       for (Children child in item.children!) {
      //         if (child.label!.contains(widget.cropTypeName)) {

      //           cropCode = child.code ?? "";
      //           break;
      //         }
      //       }
      //     }
      //   }
      // }
      monitorcatalogtreeItemDatas = res.data;
      if (monitorcatalogtreeItemDatas!.isEmpty) {
        showToast("暂无数据");
        selectCrop = 0;
        selectRights.clear();
        codes.clear();
        selectAiIndex = 0;
        selectRightType = 0;
        leftChildren.clear();
        bottomItems.clear();
        setState(() {});
        return;
      }

      for (var m in monitorcatalogtreeItemDatas!) {
        if (!cropTypes.contains(m.label)) {
          cropTypes.add(m.label ?? "");
        }
        cropTypesChildrens[monitorcatalogtreeItemDatas!.indexOf(m)] =
            m.children!;
      }

      codes.clear();
      //右上角 筛选
      List<Children>? rightChildrens = cropTypesChildrens[selectCrop];
      for (var children in rightChildrens!) {
        selectRights.add(children.label!);
        codes.add(children.code!);
      }
      nodeMonitorMapData(isInit);
    });
  }

  //请求图片
  Future<void> nodeMonitorMapData(bool isInit) async {
    if (codes.isEmpty) {
      return;
    }
    MyFieldPageService.nodeMonitorMapData({
      "code": codes[selectRightType],
      "year": yearTypes[selectYear],
      "orgCode": "86",
      "specialCode": "MY_LAND",
      "plotNo": widget.plotNo
    }).then((res) {
      monitorcatalogtreeItemMaps = res["data"];
      if (isInit) {
        processRsFramesList(monitorcatalogtreeItemMaps?["rsFrames"]);
        selectAiIndex = bottomItems.length - 1;
      }
      bbox =
          "${monitorcatalogtreeItemMaps?["rsFrames"][selectAiIndex]["bbox"][0]},${monitorcatalogtreeItemMaps!["rsFrames"][selectAiIndex]["bbox"][1]}";
      bboxs = bbox.split(",");

      //取图片id
      String selectId =
          monitorcatalogtreeItemMaps?["rsFrames"][selectAiIndex]["id"];

      //用于取左下角浮层id
      String? customLegendId = monitorcatalogtreeItemMaps?["rsFrames"]
              [selectAiIndex]["customLegendId"]
          .toString();

      print('selectId 的值: $selectId');

      String url = monitorcatalogtreeItemMaps?["mapStyle"]["sources"][selectId]
          ["tiles"][0];
      //每次切换之后清空
      leftChildren.clear();
      //左下角图层切换对应的数据
      if (monitorcatalogtreeItemMaps?["customLegends"][customLegendId] !=
          null) {
        leftChildren = monitorcatalogtreeItemMaps?["customLegends"]
            [customLegendId]["children"];
      }
      //取出对应的地块图层参数
      Uri uri = Uri.parse(url);
      print('uri 的值: $url');
      // 提取 layers 和 styles 参数的值
      layers = uri.queryParameters['layers'] ?? "";
      styles = uri.queryParameters['styles'] ?? "";
      regionId = uri.queryParameters['regionId'] ?? "";

      // 输出提取的值
      print('layers 的值: $layers');
      print('regionId的值:$regionId');
      print('styles 的值: $styles');

      //
      String imageLabel =
          monitorcatalogtreeItemMaps?["rsFrames"][selectAiIndex]["label"];
      processRsFramesList(monitorcatalogtreeItemMaps?["rsFrames"]);

      setState(() {});
      _byLabel(imageLabel);
    });
  }

  Future<void> _byLabel(String imageLabel) async {
    MyFieldPageService.byLabel(
        {"imageLabel": imageLabel, "plotNo": widget.plotNo}).then((res) {
      setState(() {
        if (res.success != true) {
          aides = "";
        } else {
          AiData aiData = res.data!;
          aides = aiData.doc!;
          final textLength = aides.length;
          _controller = AnimationController(
            duration: Duration(
                milliseconds:
                    (textLength * 120).clamp(1, double.infinity).toInt()),
            vsync: this,
          );
          // 创建字符数动画
          _charCountAnimation = IntTween(
            begin: 0,
            end: textLength, // 使用实际文本长度
          ).animate(_controller);
          _controller.forward();
        }
      });
    });
  }

  void processRsFramesList(List rsFramesList) {
    bottomItems.clear();
    List<DateTime> monitorTimes = []; // 用于存储转换后的 DateTime 对象

    for (int i = 0; i < rsFramesList.length; i++) {
      var item = rsFramesList[i];
      final monitorTimeStr = item["monitorTimeStr"] as String?;
      if (monitorTimeStr != null && !bottomItems.contains(monitorTimeStr)) {
        bottomItems.add(monitorTimeStr);
        try {
          // 将字符串转换为 DateTime 对象
          monitorTimes.add(DateTime.parse(monitorTimeStr));
        } catch (e) {
          print("日期解析错误: $e, 字符串: $monitorTimeStr");
          // 如果解析失败，可以考虑跳过或者使用默认的排序方式
        }
      }
    }

    // 使用 DateTime 对象对 bottomItems 进行排序
    bottomItems.sort((a, b) {
      try {
        final dateA = DateTime.parse(a);
        final dateB = DateTime.parse(b);
        // 降序排序：最新的日期在前面
        return dateB.compareTo(dateA);
      } catch (e) {
        print("日期解析错误: $e, 字符串A: $a, 字符串B: $b");
        // 如果解析失败，保持原有的顺序，或者根据你的需求进行处理
        return 0;
      }
    });

    // 现在 bottomItems 中的 monitorTimeStr 已经按照日期最新排序了
  }

  @override
  Widget build(BuildContext context) {
    print('bboxs 的值222: $bboxs');
    screenWidth = MediaQuery.of(context).size.width;
    screenHight = MediaQuery.of(context).size.height;
    return Scaffold(
      body: Stack(
        children: [
          // 地图部分
          FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              // initialCameraFit: CameraFit.bounds(
              //   bounds: bounds,
              //   padding: EdgeInsets.all(50.px),
              //   maxZoom: 18, // 设置最大缩放级别为18
              // ),
              interactionOptions: InteractionOptions(
                flags: flags,
              ),
            ),
            children: [
              TileLayerUtil.tileLayer(TianDiTuType.bdh),
              TileLayerUtil.tileLayer(TianDiTuType.cia),
              PolygonLayer(polygons: _polygons),
            ],
          ),
          _yaoganMap(),

          // 头部返回按钮和标题
          Positioned(
            top: MediaQuery.of(context).padding.top + 10.px,
            left: 16.px,
            right: 16.px,
            child: Row(
              children: [
                InkWell(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: EdgeInsets.all(8.px),
                    // decoration: BoxDecoration(
                    //   color: Colors.black.withOpacity(0.5),
                    //   borderRadius: BorderRadius.circular(20.px),
                    // ),
                    child: Image.asset(
                      ImageHelper.wrapAssets('field_left.png'),
                      color: Colors.white, // 如果需要改变图片颜色，可以使用 color 属性
                      width: 24.px,
                      height: 24.px,
                    ),
                  ),
                ),
                SizedBox(width: 12.px),
                Expanded(
                  child: Text(
                    widget.orgFullName,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18.px,
                      fontWeight: FontWeight.bold,
                      shadows: const [
                        Shadow(
                          blurRadius: 3.0,
                          color: Colors.black,
                          offset: Offset(1.6, 1.6),
                        ),
                      ],
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          // 遥感提示信息
          selectRights.isNotEmpty
              ? Positioned(
                  top: MediaQuery.of(context).padding.top + 50.px,
                  left: 16.px,
                  right: 16.px,
                  child: Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 12.px, vertical: 4.px),
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        transform:
                            GradientRotation(4.683 * pi), // 268.31° = 4.683弧度
                        colors: [
                          Color.fromRGBO(47, 217, 214, 0),
                          Color.fromRGBO(5, 215, 231, 0.3),
                        ],
                        stops: [0.0143, 0.6491],
                      ),
                      borderRadius: BorderRadius.circular(4.px),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 4.px, vertical: 2.px),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              transform: GradientRotation(4.683 * pi),
                              // 268.31° = 4.683弧度
                              colors: [
                                Color.fromRGBO(47, 217, 214, 0.4),
                                Color.fromRGBO(5, 215, 231, 0.4),
                              ],
                              stops: [0.0143, 0.6491],
                            ),
                            borderRadius: BorderRadius.circular(2.px),
                            border: Border.all(
                                color: const Color.fromRGBO(47, 217, 214, 0.5),
                                width: 1),
                          ),
                          child: Text(
                            '提示',
                            style:
                                TextStyle(color: Colors.white, fontSize: 12.px),
                          ),
                        ),
                        SizedBox(width: 2.px),
                        Expanded(
                          child: Text(
                            '正在显示${selectRights[selectRightType]}信息，数据采集于${bottomItems.isNotEmpty ? bottomItems[selectAiIndex] : ""}',
                            style:
                                TextStyle(color: Colors.white, fontSize: 12.px),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              : Container(),
          //筛选
          Positioned(
            top: MediaQuery.of(context).padding.top + 10.px + 80,
            left: 16.px,
            right: 16.px,
            child: Row(
              children: [
                GestureDetector(
                  child: getYearSelectBuild(),
                  onTap: () {
                    _showYearPicker();
                  },
                ),
                SizedBox(
                  width: 22.h,
                ),
                GestureDetector(
                  child: getCropSelectBuild(),
                  onTap: () {
                    _showCropPicker();
                  },
                ),
                Expanded(
                  child: Container(),
                ),
                GestureDetector(
                  child: getRightSelectBuild(),
                  onTap: () {
                    _showRightsPicker();
                  },
                ),
              ],
            ),
          ),
          //地块信息
          Positioned(
            top: MediaQuery.of(context).padding.top + 120.px,
            left: 16.px,
            child: Container(
              padding: EdgeInsets.all(12.px),
              // decoration: BoxDecoration(
              //   color: Colors.black.withOpacity(0.2),
              //   borderRadius: BorderRadius.circular(2.px),
              // ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  widget.growPatternsId.isNotEmpty
                      ? Text(
                          widget.plotName,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.px,
                            shadows: const [
                              Shadow(
                                blurRadius: 1.0,
                                color: Colors.black,
                                offset: Offset(1.6, 1.6),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox(),
                  SizedBox(height: 4.px),
                  widget.growPatternsId.isNotEmpty
                      ? Text(
                          '${widget.contrArea} 亩 ${widget.cropTypeName}',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.px,
                            shadows: const [
                              Shadow(
                                blurRadius: 1.0,
                                color: Colors.black,
                                offset: Offset(1.6, 1.6),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox(),
                  SizedBox(height: 4.px),
                  widget.growPatternsId.isNotEmpty
                      ? Text(
                          widget.cropBreedName,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.px,
                            shadows: const [
                              Shadow(
                                blurRadius: 1.0,
                                color: Colors.black,
                                offset: Offset(1.6, 1.6),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox(),
                ],
              ),
            ),
          ),
          aides.isNotEmpty
              ? Positioned(
                  left: 12,
                  right: 12,
                  bottom: screenHight / 3.8,
                  child: aiBuildWidget(),
                )
              : Container(),
          _getAiDesWidget(),
          //图表
          leftChildren.isNotEmpty
              ? Positioned(
                  left: 16.px,
                  bottom: aides.isEmpty
                      ? 120.px
                      : screenHight / 2.83, // 放在底部遥感日期列表上方
                  child: Container(
                    padding: const EdgeInsets.only(
                        left: 8, right: 8, top: 16, bottom: 16),
                    decoration: const BoxDecoration(
                      color: Color(0xcc1d2c32),
                      borderRadius: BorderRadius.all(Radius.circular(8)),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Row(
                          children: [
                            selectRights.isNotEmpty
                                ? Text(
                                    selectRights[selectRightType] ?? '图例',
                                    style: TextStyle(
                                      color: const Color(0xFFDBFDFF),
                                      fontSize: 14.px,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  )
                                : Container(),
                            SizedBox(width: 8.px),
                          ],
                        ),
                        if (leftChildren.isNotEmpty) SizedBox(height: 6.px),
                        ..._buildLegendItems(),
                      ],
                    ),
                  ),
                )
              : Container(),
          _buildBottomType()
        ],
      ),
    );
  }

  Widget _yaoganMap() {
    print("object>>$bboxs>>$layers>>$styles>>$regionId");
    return (monitorcatalogtreeItemDatas == null ||
            monitorcatalogtreeItemDatas!.isEmpty)
        ? Container()
        : bboxs.isNotEmpty
            ? FlutterMap(
                options: MapOptions(
                    initialZoom: 15, // 设置初始缩放级别为18
                    initialCenter:
                        LatLng(double.parse(bboxs[1]), double.parse(bboxs[0]))),
                children: [
                  TileLayerUtil.tileLayer(TianDiTuType.bdh),
                  TileLayerUtil.tileLayer(TianDiTuType.cia),
                  TileLayer(
                    // 设置瓦片大小为512（根据你的URL中的width/height参数）
                    tileSize: 512,
                    wmsOptions: WMSTileLayerOptions(
                        // 基础URL（不包含参数）
                        baseUrl:
                            'https://mapservice.bdhic.com/geospatial-api/openapi/commonHttpService/proxy/clipWmsByRegion?',
                        // 图层名称（从URL的layers参数解析）
                        layers: [layers],
                        // 样式（从URL的styles参数解析）
                        styles: [styles],
                        // WMS版本
                        version: '1.1.1',
                        otherParameters: {
                          'regionId': regionId,
                          'regionType': "1"
                        }
                        // 坐标系（对应URL中的srs=EPSG:3857）
                        ),
                    // 额外参数（包括URL中的transparent、format等）
                    additionalOptions: const {
                      'format': 'image/png',
                      'transparent': 'true',
                    },
                    // 自定义参数（regionId和regionType）

                    // 用户代理标识（避免被服务器拦截）
                  ),
                ],
              )
            : Container();
  }

  Widget _getAiDesWidget() {
    return aides.isNotEmpty
        ? Positioned(
            left: 12,
            right: 12,
            bottom: screenHight / 8,
            child: Container(
                padding: EdgeInsets.all(12.h),
                width: screenWidth,
                height: 150.0,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16.0),
                  gradient: const LinearGradient(
                    begin: Alignment.centerLeft, // 对应 angle: 0
                    end: Alignment.centerRight,
                    colors: [Color(0xFFEDFFFE), Color(0xFFFFFFFF)],
                  ),
                ),
                child: AnimatedBuilder(
                  // 替换原来的Text组件
                  animation: _charCountAnimation,
                  builder: (context, child) {
                    final displayText = aides.substring(
                        0, _charCountAnimation.value.clamp(0, aides.length));
                    return SingleChildScrollView(
                      child: Container(
                        margin: EdgeInsets.only(top: 12.px),
                        child: Text(
                          displayText,
                          style: TextStyle(
                            color: Colors.black,
                            fontSize: 14.px,
                            fontWeight: FontWeight.w400,
                            height: 1.5,
                          ),
                        ),
                      ),
                    );
                  },
                )),
          )
        : Container();
  }

  List<Widget> _buildLegendItems() {
    List<Widget> items = [];
    for (var item in leftChildren) {
      if (item['label'] != null &&
          item['color'] != null &&
          item['color'].isNotEmpty) {
        String colorStr = item['color'][0];
        Color color = _hexToColor(colorStr);
        items.add(
          Padding(
            padding: EdgeInsets.symmetric(vertical: 2.px),
            child: Row(
              children: [
                Container(
                  width: 12.px,
                  height: 12.px,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(2.px),
                  ),
                ),
                SizedBox(width: 4.px),
                Text(
                  item['label'],
                  style: TextStyle(
                    color: const Color(0xFFDBFDFF),
                    fontSize: 10.px,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    return items;
  }

  // 16进制颜色转换为Flutter Color
  Color _hexToColor(String hexString) {
    try {
      hexString = hexString.replaceAll('#', '');
      if (hexString.length == 6) {
        hexString = 'FF$hexString';
      }
      return Color(int.parse(hexString, radix: 16));
    } catch (e) {
      return Colors.grey;
    }
  }

  Widget aiBuildWidget() {
    return Stack(
      children: [
        Container(
          width: screenWidth,
          height: 62.0,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20.0),
            gradient: const LinearGradient(
              begin: Alignment.topCenter, // 对应 angle: -90
              end: Alignment.bottomCenter,
              colors: [Color(0xFF68FCda), Color(0xFF76FD04)],
            ),
          ),
        ),
        Positioned(
          left: 24.h,
          top: 8.h,
          child: Image.asset(
            "assets/images/icon_ai_think_logo.png",
            height: 13,
            fit: BoxFit.cover,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomType() {
    return Container(
      margin: EdgeInsets.only(top: screenHight - 80, right: 12.h, left: 12.h),
      height: 50.0, // 可以根据需要调整高度
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: bottomItems.length,
        itemBuilder: (context, index) {
          // 计算倒序索引
          final isSelected = selectAiIndex == index;
          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: GestureDetector(
              onTap: () {
                selectAiIndex = index;
                //切换图片
                // List<Children>? rightChildrens = cropTypesChildrens[selectCrop];
                // for (var children in rightChildrens!) {
                //   selectRights.add(children.label!);
                //   codes.add(children.code!);
                // }
                _loadData();
                // 在这里处理点击选中的逻辑，例如回调给父组件
                // print('选中了: ${widget.items[index]}');
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                decoration: BoxDecoration(
                  color: isSelected ? Colors.green : Colors.grey[300],
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Center(
                  child: Text(
                    bottomItems[index],
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.black,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> getDeviceList() async {
    Logger().i(widget.plotNo);
    MyFieldPageService.getMyFieldDeviceList({"plotNo": widget.plotNo})
        .then((res) {
      _parseSimpleGeoJson(widget.geomJson);

      setState(() {
        if (res.success != null) {
          // deviceList = (res['data'] as List)
          //     .map((item) => MyPlotDeviceModel.fromJson(item))
          //     .toList();
          deviceList = res.data!;
          // 生成设备标记
        } else {
          deviceList = [];
        }
        // isLoading = false;
      });
    }).catchError((error) {
      print("error>>$error");
      // setState(() {
      //   // isLoading = false;
      //
      // });
    });
  }

  void _parseSimpleGeoJson(String geoJsonStr) {
    try {
      final parsed = jsonDecode(geoJsonStr) as Map<String, dynamic>;
      final coordinates = parsed['coordinates'] as List;

      // 存储所有多边形的点集合
      List<List<LatLng>> allPolygonPoints = [];

      // 根据coordinates数组长度进行不同处理
      if (coordinates.length == 1) {
        // 单一图层情况
        var polygonCoords = [];
        if (coordinates[0].length == 1) {
          polygonCoords = coordinates[0][0];
        } else {
          polygonCoords = coordinates[0];
        }
        List<LatLng> polygonPoints = _extractPoints(polygonCoords);
        if (polygonPoints.isNotEmpty) {
          allPolygonPoints.add(polygonPoints);
        }
      } else if (coordinates.length > 1) {
        // 多图层情况
        for (int i = 0; i < coordinates.length; i++) {
          final polygonCoords = coordinates[i][0]; // 每个图层取第一个子元素
          List<LatLng> polygonPoints = _extractPoints(polygonCoords);
          if (polygonPoints.isNotEmpty) {
            allPolygonPoints.add(polygonPoints);
          }
        }
      }

      if (allPolygonPoints.isEmpty) {
        throw Exception('无有效坐标点');
      }

      // 生成包含所有点的总边界
      List<LatLng> allPoints =
          allPolygonPoints.expand((points) => points).toList();

      // 创建不同颜色的多边形列表
      List<Polygon> polygons = [];
      List<Color> colors = [
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        // Colors.blue.withOpacity(0.4),
        // Colors.red.withOpacity(0.4),
        // Colors.orange.withOpacity(0.4),
        // Colors.purple.withOpacity(0.4)
      ];

      for (int i = 0; i < allPolygonPoints.length; i++) {
        Color fillColor = colors[i % colors.length];
        Color borderColor = fillColor.withOpacity(0.5);

        polygons.add(Polygon(
          points: allPolygonPoints[i],
          color: fillColor,
          borderColor: borderColor,
          borderStrokeWidth: 2,
        ));
      }

      setState(() {
        _polygons = polygons;
        points.clear();
        points.addAll(allPoints);
      });

      // 如果已经有设备标记，则更新地图边界
      if (deviceMarkers.isNotEmpty) {
        // List<LatLng> devicePoints =
        // deviceMarkers.map((marker) => marker.point).toList();
        // _updateMapBounds(devicePoints);
      } else {
        // 否则仅适配地块边界
        // final bounds = LatLngBounds.fromPoints(allPoints);
        // _mapController.fitCamera(
        //   CameraFit.bounds(
        //     bounds: bounds,
        //     padding: EdgeInsets.all(40.px),
        //     maxZoom: 18, // 设置最大缩放级别为18
        //   ),
        // );
      }
    } catch (e, stack) {
      debugPrint('GeoJSON处理失败: $e\n$stack');
    }
  }

  void _updateMapBounds(List<LatLng> devicePoints) {
    if (devicePoints.isEmpty && points.isEmpty) {
      return;
    }
    List<LatLng> allPoints = [...points];
    allPoints.addAll(devicePoints);
    if (allPoints.isNotEmpty) {
      final bounds = LatLngBounds.fromPoints(allPoints);
      // 调整地图视图以显示所有点
      _mapController.fitCamera(
        CameraFit.bounds(
          bounds: bounds,
          padding: EdgeInsets.all(40.px),
          maxZoom: 18, // 设置最大缩放级别为18
        ),
      );
    }
  }

// 从坐标数组中提取点
  List<LatLng> _extractPoints(List polygonCoords) {
    List<LatLng> result = [];

    for (var coord in polygonCoords) {
      if (coord is List && coord.length >= 2) {
        final x = (coord[0] as num).toDouble();
        final y = (coord[1] as num).toDouble();
        final point = _convertPoint(x, y);
        result.add(point);
      }
    }

    return result;
  }

  // 修正后的坐标转换方法
  LatLng _convertPoint(double x, double y) {
    // Web墨卡托(EPSG:3857)到WGS84(EPSG:4326)的转换
    const earthRadius = 6378137.0; // 地球半径，单位米

    // 经度转换
    final lng = (x / earthRadius) * 180.0 / pi;

    // 纬度转换，需要处理边界情况
    var lat = (atan(exp(y / earthRadius)) * 2 - pi / 2) * 180.0 / pi;

    // 边界检查
    if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
      debugPrint('转换后坐标越界: ($lng, $lat)');
      return const LatLng(0, 0);
    }

    return LatLng(lat, lng);
  }

  Widget getYearSelectBuild() {
    return Container(
      padding: EdgeInsets.all(8.h),
      decoration: const BoxDecoration(
        color: Color(0xcc1d2c32),
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: Row(
        children: [
          Text(
            yearTypes[selectYear],
            style: const TextStyle(color: Color(0xFFDBFDFF)),
          ),
          SizedBox(
            width: 12.h,
          ),
          Image.asset(
            "assets/images/tally_up_down.png",
            width: 6.w,
            height: 6.w,
            color: Colors.grey,
          )
        ],
      ),
    );
  }

  Widget getCropSelectBuild() {
    return Container(
      padding: EdgeInsets.all(8.h),
      decoration: const BoxDecoration(
        color: Color(0xcc1d2c32),
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: Row(
        children: [
          Text(
            cropTypes.isNotEmpty ? cropTypes[selectCrop] : "",
            style: const TextStyle(color: Color(0xFFDBFDFF)),
          ),
          SizedBox(
            width: 12.h,
          ),
          Image.asset(
            "assets/images/tally_up_down.png",
            width: 6.w,
            height: 6.w,
            color: Colors.grey,
          )
        ],
      ),
    );
  }

  Widget getRightSelectBuild() {
    return Container(
      padding: EdgeInsets.all(8.h),
      decoration: const BoxDecoration(
        color: Color(0xcc1d2c32),
        borderRadius: BorderRadius.all(Radius.circular(8)),
      ),
      child: Row(
        children: [
          Image.asset(
            "assets/images/icon_right_select_logo.png",
            width: 20.w,
            height: 20.w,
            color: Colors.grey,
          )
        ],
      ),
    );
  }

  void _showYearPicker() {
    BrnMultiDataPicker(
      context: context,
      title: '年份',
      //修改选中字体粗细
      themeData: BrnPickerConfig(
          itemTextSelectedStyle: BrnTextStyle(fontWeight: FontWeight.w500)),
      delegate: HomeDropTypeRowDelegate(
          firstSelectedIndex: selectYear, list: yearTypes),
      confirmClick: (list) {
        selectYear = list[0];
        selectCrop = 0;
        cropTypes.clear();
        selectRights.clear();
        codes.clear();
        selectAiIndex = 0;
        selectRightType = 0;
        aides = "";
        _loadData();
      },
    ).show();
  }

  void _showCropPicker() {
    BrnMultiDataPicker(
      context: context,
      title: '',
      //修改选中字体粗细
      themeData: BrnPickerConfig(
          itemTextSelectedStyle: BrnTextStyle(fontWeight: FontWeight.w500)),
      delegate: HomeDropTypeRowDelegate(
          firstSelectedIndex: selectCrop, list: cropTypes),
      confirmClick: (list) {
        selectCrop = list[0];
        selectRights.clear();
        codes.clear();
        selectAiIndex = 0;
        selectRightType = 0;
        aides = "";
        _loadData();
      },
    ).show();
  }

  void _showRightsPicker() {
    BrnMultiDataPicker(
      context: context,
      title: '',
      //修改选中字体粗细
      themeData: BrnPickerConfig(
          itemTextSelectedStyle: BrnTextStyle(fontWeight: FontWeight.w500)),
      delegate: HomeDropTypeRowDelegate(
          firstSelectedIndex: selectRightType, list: selectRights),
      confirmClick: (list) {
        selectRightType = list[0];
        selectAiIndex = 0;
        aides = "";
        _loadData();
        print("object");
      },
    ).show();
  }

  @override
  void dispose() {
    _controller.dispose(); // 释放控制器
    super.dispose();
  }
}
