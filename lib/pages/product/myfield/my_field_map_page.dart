import 'dart:convert';
import 'dart:math';

import 'package:bdh_smart_agric_app/components/map_location_plugin.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/%20widget/my_field_grid_alert.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/%20widget/my_field_grid_item.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/fieldpatrol/extension.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/fieldpatrol/field_patrol_add_page.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/my_paddyfield_fertilization_recommendations.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/video/video_online_page.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/weather/real_time_data_page.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/tile_util.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'package:logger/logger.dart';

import '../../../model/fild_map_list_model.dart';
import '../../../model/fild_patrol_list_model.dart';
import '../../../model/my_field_device_list_model.dart';
import '../../../model/my_field_page_model.dart';
import '../../../model/myfield_monitorcatalogtree_model.dart';
import '../../../model/weather_day_info_model.dart';
import '../../../model/weather_real_time_model.dart';
import '../../../utils/enum_util.dart';
import '../../../utils/event_bus.dart';
import '../../../utils/request/field_record_service.dart';
import '../../../utils/request/my_field_page_service.dart';
import '../../../utils/request/my_weather_page_service.dart';
import 'fieldpatrol/field_patrol_edit_page.dart';
import 'fieldpatrol/field_patrol_home_list_page.dart';
import 'my_field_fertilization_recommendations.dart';
import 'my_field_map_remote_page.dart';
import 'my_field_nutrient_info.dart';
import 'my_field_parcel_image.dart';
import 'my_field_pest.dart';
import 'my_field_record_agricultural_activities.dart';
import 'my_field_soil_ai_remind.dart';
import 'my_field_soil_ai_remind_info.dart';
import 'my_field_soil_equipment.dart';
import 'my_field_soil_sexual_msg_info.dart';
import 'my_field_soil_spore_info.dart';
import 'myfieldchat/my_field_chat.dart';
import 'myplanting_plan_list.dart';

// 添加 HexColor 工具类
extension HexColor on Color {
  /// 字符串转Color
  static Color fromHex(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length == 6 || hexString.length == 7) buffer.write('ff');
    buffer.write(hexString.replaceFirst('#', ''));
    return Color(int.parse(buffer.toString(), radix: 16));
  }

  /// Color转字符串
  String toHex({bool leadingHashSign = true}) => '${leadingHashSign ? '#' : ''}'
      '${alpha.toRadixString(16).padLeft(2, '0')}'
      '${red.toRadixString(16).padLeft(2, '0')}'
      '${green.toRadixString(16).padLeft(2, '0')}'
      '${blue.toRadixString(16).padLeft(2, '0')}';
}

class MyFieldMapPage extends StatefulWidget {
  final String plotNo;
  final String year;
  final String plotName;
  final String cropBreedName; // 作物品种名称
  final String cropTypeName; // 作物
  final String contrArea;
  final String orgFullName;
  final String growPatternsId; // 种植方案 id
  final String geomJson;
  final String linkCode;
  final String orgCode;
  final String cropType;

  // final String orgCode;
  // final String id;

  const MyFieldMapPage({
    super.key,
    required this.plotNo,
    required this.year,
    required this.plotName,
    required this.cropBreedName,
    required this.cropTypeName,
    required this.contrArea,
    required this.orgFullName,
    required this.growPatternsId, // 种植方案 id
    required this.geomJson, // 添加geomJson参数
    required this.linkCode, // 添加geomJson参数
    required this.orgCode,
    required this.cropType,
    // required this.orgCode,
    // required this.id,
  });

  @override
  State<MyFieldMapPage> createState() => _MyFieldMapPageState();
}

class _MyFieldMapPageState extends State<MyFieldMapPage> {
  final MapController _mapController = MapController();

  List<Polygon> _polygons = [];
  List<Marker> locationMarkers = [];
  List<Marker> deviceMarkers = [];
  List<Marker> _fildPatrolsMarkers = [];
  var bounds = LatLngBounds(const LatLng(44, 121), const LatLng(52, 134));
  var flags = InteractiveFlag.all - InteractiveFlag.rotate; // 禁用地图旋转
  bool isExpanded = false;
  double bottomPanelHeight = 0.3; // 底部面板高度比例，初始为屏幕高度的30%
  bool _remoteSensingEnabled = false;
  bool _showDevices = false; // 控制设备图层显示状态
  bool _showFildPatrols = false; // 是否显示巡田
  final Set<String> _selectedButtons = {};

  // 遥感图层相关变量
  Map<String, dynamic>? _remoteSensingData;
  List<Map<String, dynamic>> _rsFramesList = [];
  final List<Widget> _remoteSensingLayers = [];
  int _currentRsLayerIndex = 0;
  Map<String, dynamic>? _currentLegend;

  Map<String, dynamic>? currentData;
  final ValueNotifier<String> _selectedPlanName = ValueNotifier("");
//记录点击的种植方案
  final int _selectedPlanId = 0;
  //是否点击地图清屏
  bool isActionMap = false;
  // 更多服务
  final List _moreServices = [
    {
      "title": "地块画像",
      "icon": "my_field_map_more_dikuai.png",
    },
    {
      "title": "遥感服务",
      "icon": "my_field_map_more_yaogan.png",
    },
    {
      "title": "施肥建议",
      "icon": "my_field_map_more_shifei.png",
    },
    {
      "title": "农事提醒",
      "icon": "my_field_map_more_nongshi.png",
    },
    {
      "title": "巡田情况",
      "icon": "my_field_map_more_xuntian.png",
    },
  ];
  // 遥感图层参数
  String _remoteLayers = "";
  String _remoteStyles = "";
  String _remoteRegionId = "";
  String _remoteBbox = "";
  List<String> _remoteBboxs = [];

  MonitorcatalogtreeItemData? remoteSensing;
  List<MyPlotDeviceModel> deviceList = [];
  final List<LatLng> points = []; // 用于存储点坐标的列表
  List<dynamic> remindList = []; // AI 提醒列表
  bool _showAiRemind = true; // 控制AI提醒是否显示
  bool _isBottomMenuExpanded = false;
  List<FidMapItmData> _listFildPatrols = []; //巡田记录列表
  WeatherDayData? weatherDayData;
  String json = '''[ {
            "deviceId": "1080010124071694",
            "productId": "78d5cb2197d09224b78d0ac439dc2997",
            "nodeId": null,
            "deviceName": "田间监控设备10",
            "timeout": null,
            "description": null,
            "gatewayId": null,
            "nodeType": null,
            "status": "ONLINE",
            "version": null,
            "onlineTime": null,
            "offlineTime": null,
            "orgCode": "8601040104",
            "longitude": "132.75978199534754",
            "latitude": "47.04569009240436",
            "addressInfo": null,
            "addressType": null,
            "projectCode": null,
            "last_msg_time": null,
            "sourceType": null,
            "sourceName": null,
            "deviceAlias": null,
            "isFault": null,
            "insType": "1",
            "debugType": "1",
            "insLoc": null,
            "addDeviceType": null,
            "insPoint": null,
            "insPicUrl1": "https://app.bdhic.com/highresource/bdh-pro/bdh-highresource-equipment/a7e8145559fc4ac0b37702d668678e071724120518012.jpg",
            "insPicUrl2": "https://app.bdhic.com/highresource",
            "insPicUrl3": "https://app.bdhic.com/highresource",
            "firstCategory": "1",
            "secondCategory": "3",
            "iotSubType": "108001",
            "isStatus": 1,
            "remark": null,
            "createBy": 100516750,
            "createName": null,
            "createTime": 1724135376950,
            "updateBy": null,
            "updateTime": 1731560832835,
            "statusCd": 1,
            "params": null,
            "updateMin": null,
            "orgCodeBind": null,
            "orgNameBind": null,
            "streamType": 0,
            "iotDeviceVideoId": null,
            "videoDeviceCode": "1080010124071694",
            "rtspUrl": null,
            "rid": null
        },{
            "deviceId": "1030020124060024",
            "productId": "f24202a624857da3e17a21d9286ee690",
            "nodeId": "1030020124060024",
            "deviceName": "1030020124060024",
            "timeout": null,
            "description": null,
            "gatewayId": null,
            "nodeType": "ENDPOINT",
            "status": "ONLINE",
            "version": null,
            "onlineTime": 1745191415182,
            "offlineTime": 1744215466694,
            "orgCode": "8604110107",
            "longitude": "132.75641005232552",
            "latitude": "47.04523564269001",
            "addressInfo": null,
            "addressType": null,
            "projectCode": "bdh-weather-platform",
            "last_msg_time": null,
            "sourceType": null,
            "sourceName": null,
            "deviceAlias": "1030020124060024",
            "isFault": "0",
            "insType": "1",
            "debugType": "1",
            "insLoc": null,
            "addDeviceType": null,
            "insPoint": null,
            "insPicUrl1": "https://app.bdhic.com/highresource/bdh-pro/bdh-highresource-equipment/40dbeb4833a748f6b52de3d5b7ab5e331726295084067.jpg",
            "insPicUrl2": "https://app.bdhic.com/highresource",
            "insPicUrl3": "https://app.bdhic.com/highresource",
            "firstCategory": "1",
            "secondCategory": "103002",
            "iotSubType": "103002",
            "isStatus": 1,
            "remark": null,
            "createBy": 100516750,
            "createName": null,
            "createTime": 1718695164059,
            "updateBy": null,
            "updateTime": 1745191415184,
            "statusCd": 1,
            "params": null,
            "updateMin": null,
            "orgCodeBind": null,
            "orgNameBind": null,
            "streamType": 0,
            "iotDeviceVideoId": null,
            "videoDeviceCode": null,
            "rtspUrl": null,
            "rid": 28907
        },
        {
            "deviceId": "1030050124070144",
            "productId": "1a668ee6ae375914f84ef206ce8a5a61",
            "nodeId": "1030050124070144",
            "deviceName": "智能性诱监测设备02",
            "timeout": null,
            "description": null,
            "gatewayId": null,
            "nodeType": "ENDPOINT",
            "status": "ONLINE",
            "version": null,
            "onlineTime": 1745194950746,
            "offlineTime": 1744216184159,
            "orgCode": "8606050103",
            "longitude": "132.7570911263554",
            "latitude": "47.05293694874633",
            "addressInfo": null,
            "addressType": null,
            "projectCode": "bdh-weather-platform",
            "last_msg_time": null,
            "sourceType": null,
            "sourceName": null,
            "deviceAlias": "智能性诱监测设备02",
            "isFault": null,
            "insType": "1",
            "debugType": "1",
            "insLoc": null,
            "addDeviceType": null,
            "insPoint": null,
            "insPicUrl1": "https://app.bdhic.com/highresource/bdh-pro/bdh-highresource-equipment/288287fe2e054715b185cff4fbc8c7ac1722571979273.jpg",
            "insPicUrl2": "https://app.bdhic.com/highresource",
            "insPicUrl3": "https://app.bdhic.com/highresource",
            "firstCategory": "1",
            "secondCategory": "103001",
            "iotSubType": "103005",
            "isStatus": 1,
            "remark": null,
            "createBy": 100516750,
            "createName": null,
            "createTime": 1719976363416,
            "updateBy": null,
            "updateTime": 1745194950759,
            "statusCd": 1,
            "params": null,
            "updateMin": null,
            "orgCodeBind": null,
            "orgNameBind": null,
            "streamType": 0,
            "iotDeviceVideoId": null,
            "videoDeviceCode": null,
            "rtspUrl": null,
            "rid": 35334
        },
        {
            "deviceId": "1010100124070338",
            "productId": "6c49514673d17f07c0569e63c5aac0dc",
            "nodeId": "1010100124070338",
            "deviceName": "8-4养分02",
            "timeout": null,
            "description": null,
            "gatewayId": null,
            "nodeType": "ENDPOINT",
            "status": "ONLINE",
            "version": null,
            "onlineTime": 1745200817001,
            "offlineTime": 1745186416781,
            "orgCode": "860604",
            "longitude": "132.76000193814218",
            "latitude": "47.048918252742794",
            "addressInfo": null,
            "addressType": null,
            "projectCode": "bdh-weather-platform",
            "last_msg_time": null,
            "sourceType": null,
            "sourceName": null,
            "deviceAlias": "8-4养分02",
            "isFault": null,
            "insType": "1",
            "debugType": "1",
            "insLoc": null,
            "addDeviceType": null,
            "insPoint": null,
            "insPicUrl1": "https://app.bdhic.com/highresource/bdh-pro/bdh-highresource-equipment/7581d295bd794b64aff22dc33e8a76621722926485314.jpg",
            "insPicUrl2": "https://app.bdhic.com/highresource",
            "insPicUrl3": "https://app.bdhic.com/highresource",
            "firstCategory": "1",
            "secondCategory": "5",
            "iotSubType": "101010",
            "isStatus": 1,
            "remark": null,
            "createBy": 100516750,
            "createName": null,
            "createTime": 1722414704867,
            "updateBy": null,
            "updateTime": 1745200817002,
            "statusCd": 1,
            "params": null,
            "updateMin": null,
            "orgCodeBind": null,
            "orgNameBind": null,
            "streamType": 0,
            "iotDeviceVideoId": null,
            "videoDeviceCode": null,
            "rtspUrl": null,
            "rid": 46192
        }]''';

  // 设备类型对应的图标
  final Map<String, String> deviceTypeIcons = {
    "101001": "qixiang.png",
    "101002": "shangqing.png",
    "103001": "chongqing.png",
    "103002": "baozi.png",
    "108001": "shexiangtou.png",
    "108003": "shexiangtou.png",
    "101010": "yangfen.png",
    "103005": "xingyou.png"
  };

  // 种植方案数据
  List<dynamic> plantingPlanData = [];
  WeatherRealData? weatherRealData;

  late ScrollController _scrollController;
  bool _hasScrolledToToday = false;
  @override
  void initState() {
    super.initState();
    _loadData();
    bus.on("update", (dynamic data) {
      queryFarmerByAll();
    });
    plantingPlanData = [];

    // 自动加载遥感数据
    _loadRemoteSensingData();
    _scrollController = ScrollController();

    // 监听滚动状态，防止多次滚动
    _scrollController.addListener(() {
      // 获取当前滚动位置
      double pixels = _scrollController.position.pixels;
      // 计算每个item的总宽度（item宽度 + 间距）
      double itemWidth = (250 * 0.7).px + 10.px; // item宽度 + 左右间距
      // 计算最左边可见的item索引
      int leftmostVisibleIndex = (pixels / itemWidth).floor();
      debugPrint('最左边可见的是第 $leftmostVisibleIndex 个item');

      if (leftmostVisibleIndex >= 0 &&
          leftmostVisibleIndex < plantingPlanData.length) {
        _selectedPlanName.value = plantingPlanData[leftmostVisibleIndex]
                ['prodProcessName'] ??
            "未知农事活动";
      }
    });

    currentData = getDateParts();
  }

  Map<String, String> getDateParts() {
    final now = DateTime.now();
    return {
      'year': now.year.toString(),
      'month': now.month.toString().padLeft(2, '0'),
      'day': now.day.toString().padLeft(2, '0'),
    };
  }

// 查找今天对应的条目索引
  int? _findTodayItemIndex() {
    final today = DateTime.now();
    final todayStr =
        '${today.month.toString().padLeft(2, '0')}${today.day.toString().padLeft(2, '0')}';

    for (int planIndex = 0; planIndex < plantingPlanData.length; planIndex++) {
      // 每个方案的标题项（增加索引）

      final activity = plantingPlanData[planIndex];
      final startDate = activity['planStartDate'];
      final endDate = activity['planEndDate'];

      if (startDate != null &&
          endDate != null &&
          startDate.length == 4 &&
          endDate.length == 4) {
        // 转换日期并比较
        final startMonth = int.parse(startDate.substring(0, 2));
        final startDay = int.parse(startDate.substring(2, 4));
        final endMonth = int.parse(endDate.substring(0, 2));
        final endDay = int.parse(endDate.substring(2, 4));

        final startDateTime = DateTime(today.year, startMonth, startDay);
        final endDateTime = DateTime(today.year, endMonth, endDay);
        final todayDateTime = DateTime(today.year, today.month, today.day);

        if ((startDateTime.isBefore(todayDateTime) ||
                startDateTime.isAtSameMomentAs(todayDateTime)) &&
            (endDateTime.isAfter(todayDateTime) ||
                endDateTime.isAtSameMomentAs(todayDateTime))) {
          return planIndex; // 找到匹配项，返回当前总索引
        }
      }
    }
    return null; // 未找到匹配项
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 滚动到今天对应的条目
  void _scrollToToday() {
    final targetIndex = _findTodayItemIndex();
    if (targetIndex != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollController
            .jumpTo(((250 * 0.7).px + 10.px) * targetIndex + 16.px);
        // _scrollController.animateTo(
        //   ((250 * 0.7).px + 10.px) * targetIndex + 16.px, // 假设每个条目的高度约为100像素
        //   duration: const Duration(milliseconds: 500),
        //   curve: Curves.easeInOut,
        // );
        _hasScrolledToToday = true;
      });
    }
  }

  void _loadData() async {
    await getDeviceList();
    await queryFarmerByAll();
    await queryWeaInfo();
    await getMyFieldSoilRemindList();
    await getPlantingPlan();
    await _loadRemoteSensingData();
  }

  // 加载遥感数据
  Future<void> _loadRemoteSensingData() async {
    var res = await MyFieldPageService.monitorCatalogTree({
      "orgCode": 86,
      "year": DateTime.now().year,
      "code": "MY_LAND",
      "tags": null
    });
    Logger().i(res);
    String cropCode = "";
    if (res.data != null) {
      for (MonitorcatalogtreeItemData item in res.data! as List) {
        if (item.children != null) {
          for (Children child in item.children!) {
            if (child.label!.contains(widget.cropTypeName)) {
              remoteSensing = item;
              cropCode = child.code ?? "";
              break;
            }
          }
        }
      }
    }

    MyFieldPageService.nodeMonitorMapData({
      "code": cropCode,
      "year": DateTime.now().year,
      "orgCode": "86",
      "specialCode": "MY_LAND",
      "plotNo": widget.plotNo
    }).then(
      (res) {
        setState(() {
          // 获取结果数据
          _remoteSensingData = res["data"];
          Logger().i(_remoteSensingData);
          // 提取rsFrames列表
          if (_remoteSensingData != null &&
              _remoteSensingData!["rsFrames"] != null) {
            _rsFramesList = List<Map<String, dynamic>>.from(
                _remoteSensingData!["rsFrames"]);
            // 如果存在图层，设置当前图例
            if (_rsFramesList.isNotEmpty) {
              //显示最新的
              _currentRsLayerIndex = _rsFramesList.length - 1;
              String selectId =
                  _remoteSensingData!["rsFrames"][_currentRsLayerIndex]["id"];

              // 获取URL中的参数
              String url = _remoteSensingData!["mapStyle"]["sources"][selectId]
                  ["tiles"][0];
              Uri uri = Uri.parse(url);

              // 提取参数
              _remoteLayers = uri.queryParameters['layers'] ?? "";
              _remoteStyles = uri.queryParameters['styles'] ?? "";
              _remoteRegionId = uri.queryParameters['regionId'] ?? "";

              // 获取边界框
              _remoteBbox =
                  "${_remoteSensingData!["rsFrames"][_currentRsLayerIndex]["bbox"][0]},${_remoteSensingData!["rsFrames"][_currentRsLayerIndex]["bbox"][1]}";
              _remoteBboxs = _remoteBbox.split(",");

              // 设置图例信息
              String customLegendId =
                  _rsFramesList[_currentRsLayerIndex]["customLegendUID"];
              if (_remoteSensingData!["customLegends"] != null &&
                  _remoteSensingData!["customLegends"][customLegendId] !=
                      null) {
                _currentLegend =
                    _remoteSensingData!["customLegends"][customLegendId];
              }

              // 启用遥感显示
              _remoteSensingEnabled = true;
            }
          }
        });
      },
    );
  }

  // 切换遥感图层
  void _switchRsLayer(int index) {
    if (index < 0 || index >= _rsFramesList.length) return;

    setState(() {
      _currentRsLayerIndex = index;

      // 更新当前显示的图层信息
      if (_remoteSensingData != null && _rsFramesList.isNotEmpty) {
        String selectId = _rsFramesList[_currentRsLayerIndex]["id"];

        // 获取URL中的参数
        String url =
            _remoteSensingData!["mapStyle"]["sources"][selectId]["tiles"][0];
        Uri uri = Uri.parse(url);

        // 提取参数
        _remoteLayers = uri.queryParameters['layers'] ?? "";
        _remoteStyles = uri.queryParameters['styles'] ?? "";
        _remoteRegionId = uri.queryParameters['regionId'] ?? "";

        // 获取边界框
        _remoteBbox =
            "${_rsFramesList[_currentRsLayerIndex]["bbox"][0]},${_rsFramesList[_currentRsLayerIndex]["bbox"][1]}";
        _remoteBboxs = _remoteBbox.split(",");

        // 更新图例
        String customLegendId =
            _rsFramesList[_currentRsLayerIndex]["customLegendUID"];
        if (_remoteSensingData!["customLegends"] != null &&
            _remoteSensingData!["customLegends"][customLegendId] != null) {
          _currentLegend = _remoteSensingData!["customLegends"][customLegendId];
        }
      }
    });
  }

  // 辅助方法：构建图例项目
  List<Widget> _buildLegendItems() {
    List<Widget> items = [];

    if (_currentLegend != null && _currentLegend!['children'] != null) {
      for (var item in _currentLegend!['children']) {
        String label = item['label'] ?? '';
        List<dynamic> colorList = item['color'] ?? ['#ffffff'];
        Color color = colorList.isNotEmpty
            ? HexColor.fromHex(colorList[0])
            : Colors.white;

        items.add(
          Padding(
            padding: EdgeInsets.symmetric(vertical: 4.px),
            child: Row(
              children: [
                Container(
                  width: 16.px,
                  height: 16.px,
                  decoration: BoxDecoration(
                    color: color,
                    borderRadius: BorderRadius.circular(2.px),
                  ),
                ),
                SizedBox(width: 8.px),
                Text(
                  label,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.9),
                    fontSize: 10.px,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    }

    return items;
  }

  Future<void> getPlantingPlan() async {
    if (widget.growPatternsId.isEmpty) {
      return;
    }
    MyFieldPageService.getMyFieldPlantingPlan({
      "landNo": widget.plotNo,
      "statYear": widget.year,
      "growPatternsId": widget.growPatternsId
    }).then((res) {
      Logger().i(res);
      setState(() {
        List mapList = res['data'];
        plantingPlanData.clear();
        for (var item in mapList) {
          if (item['children'] != null) {
            plantingPlanData.addAll(item['children']);
          }
        }
        if (plantingPlanData.isNotEmpty) {
          _selectedPlanName.value =
              plantingPlanData[0]['prodProcessName'] ?? "未知农事活动";
          _scrollToToday();
        }
      });
    });
  }

  Future<void> getDeviceList() async {
    Logger().i(widget.plotNo);
    MyFieldPageService.getMyFieldDeviceList({"plotNo": widget.plotNo})
        .then((res) {
      Logger().i(widget.geomJson);
      _parseSimpleGeoJson(widget.geomJson);

      setState(() {
        if (res.success != null) {
          // deviceList = (res['data'] as List)
          //     .map((item) => MyPlotDeviceModel.fromJson(item))
          //     .toList();
          deviceList = res.data!;
          List<dynamic> parsedJson = jsonDecode(json);

          for (var deviceJson in parsedJson) {
            MyPlotDeviceModel model = MyPlotDeviceModel.fromJson(deviceJson);

            deviceList.add(MyPlotDeviceModel.fromJson(deviceJson));
          }
          print("deviceList>>${deviceList.length}");
          // 生成设备标记
          _generateDeviceMarkers();
          //当日天气
        } else {
          deviceList = [];
        }
        // isLoading = false;
      });
    }).catchError((error) {
      print("error>>$error");
      // setState(() {
      //   // isLoading = false;
      //
      // });
    });
  }

  //
  Future<void> queryFarmerByAll() async {
    Logger().i(widget.plotNo);
    await Future.delayed(const Duration(milliseconds: 200));
    var result = await FieldRecordService.queryFarmerByAll(
        {"landNo": widget.plotNo, "statYear": widget.year});
    if (mounted) {
      setState(() {
        _listFildPatrols = result.data!;
        if (_listFildPatrols.isNotEmpty) {
          _generateFildPatrolsMarkers();
          // _showFildPatrols=true;
        } else {
          // _showFildPatrols=false;
        }
      });
    }
  }

  Future<void> queryWeaInfo() async {
    Logger().i(widget.plotNo);
    String orgcode;
    if (widget.orgCode.length > 10) {
      orgcode = widget.orgCode.substring(0, 10);
    } else {
      orgcode = widget.orgCode;
    }
    var result = await MyWeatherPageService.queryWeaInfo({"orgCode": orgcode});
    if (result.success != null) {
      if (result.data != null) {
        weatherDayData = result.data!;
        setState(() {});
      }
    }
  }

  Future<void> getMyFieldSoilRemindList() async {
    MyFieldPageService.getMyFieldSoilRemind({
      "plotNo": widget.plotNo,
      // "statYear": widget.year,
      "page": 1,
      "rows": 1
    }).then((res) {
      setState(() {
        remindList = res['data']?['records'];
      });
    });
  }

  void _updateMapBounds(List<LatLng> devicePoints) {
    if (devicePoints.isEmpty && points.isEmpty) {
      return;
    }
    List<LatLng> allPoints = [...points];
    allPoints.addAll(devicePoints);
    if (allPoints.isNotEmpty) {
      final bounds = LatLngBounds.fromPoints(allPoints);
      // 调整地图视图以显示所有点
      _mapController.fitCamera(
        CameraFit.bounds(
          bounds: bounds,
          padding: EdgeInsets.all(40.px),
          maxZoom: 18, // 设置最大缩放级别为18
        ),
      );
    }
  }

  void _parseSimpleGeoJson(String geoJsonStr) {
    try {
      final parsed = jsonDecode(geoJsonStr) as Map<String, dynamic>;
      final coordinates = parsed['coordinates'] as List;

      // 存储所有多边形的点集合
      List<List<LatLng>> allPolygonPoints = [];

      // 根据coordinates数组长度进行不同处理
      if (coordinates.length == 1) {
        // 单一图层情况
        var polygonCoords = [];
        if (coordinates[0].length == 1) {
          polygonCoords = coordinates[0][0];
        } else {
          polygonCoords = coordinates[0];
        }
        List<LatLng> polygonPoints = _extractPoints(polygonCoords);
        if (polygonPoints.isNotEmpty) {
          allPolygonPoints.add(polygonPoints);
        }
      } else if (coordinates.length > 1) {
        // 多图层情况
        for (int i = 0; i < coordinates.length; i++) {
          final polygonCoords = coordinates[i][0]; // 每个图层取第一个子元素
          List<LatLng> polygonPoints = _extractPoints(polygonCoords);
          if (polygonPoints.isNotEmpty) {
            allPolygonPoints.add(polygonPoints);
          }
        }
      }

      if (allPolygonPoints.isEmpty) {
        throw Exception('无有效坐标点');
      }

      // 生成包含所有点的总边界
      List<LatLng> allPoints =
          allPolygonPoints.expand((points) => points).toList();

      // 创建不同颜色的多边形列表
      List<Polygon> polygons = [];
      List<Color> colors = [
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        // Colors.blue.withOpacity(0.4),
        // Colors.red.withOpacity(0.4),
        // Colors.orange.withOpacity(0.4),
        // Colors.purple.withOpacity(0.4)
      ];

      for (int i = 0; i < allPolygonPoints.length; i++) {
        Color fillColor = colors[i % colors.length];
        Color borderColor = fillColor.withOpacity(0.5);

        polygons.add(Polygon(
          points: allPolygonPoints[i],
          color: fillColor,
          borderColor: borderColor,
          borderStrokeWidth: 2,
        ));
      }

      setState(() {
        _polygons = polygons;
        points.clear();
        points.addAll(allPoints);
      });

      // 如果已经有设备标记，则更新地图边界
      if (deviceMarkers.isNotEmpty) {
        List<LatLng> devicePoints =
            deviceMarkers.map((marker) => marker.point).toList();
        _updateMapBounds(devicePoints);
      } else {
        // 否则仅适配地块边界
        final bounds = LatLngBounds.fromPoints(allPoints);
        _mapController.fitCamera(
          CameraFit.bounds(
            bounds: bounds,
            padding: EdgeInsets.all(40.px),
            maxZoom: 18, // 设置最大缩放级别为18
          ),
        );
      }
    } catch (e, stack) {
      debugPrint('GeoJSON处理失败: $e\n$stack');
    }
  }

  // 从坐标数组中提取点
  List<LatLng> _extractPoints(List polygonCoords) {
    List<LatLng> result = [];

    for (var coord in polygonCoords) {
      debugPrint(
          'coord-----: ${(coord is List && coord.length >= 2)}---${coord.length}');
      if (coord is List && coord.length >= 2) {
        final x = (coord[0] as num).toDouble();
        final y = (coord[1] as num).toDouble();
        final point = _convertPoint(x, y);
        result.add(point);
      }
    }

    return result;
  }

  // 修正后的坐标转换方法
  LatLng _convertPoint(double x, double y) {
    // Web墨卡托(EPSG:3857)到WGS84(EPSG:4326)的转换
    const earthRadius = 6378137.0; // 地球半径，单位米

    // 经度转换
    final lng = (x / earthRadius) * 180.0 / pi;

    // 纬度转换，需要处理边界情况
    var lat = (atan(exp(y / earthRadius)) * 2 - pi / 2) * 180.0 / pi;

    // 边界检查
    if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
      debugPrint('转换后坐标越界: ($lng, $lat)');
      return const LatLng(0, 0);
    }

    return LatLng(lat, lng);
  }

  // 根据设备信息生成标记
  void _generateDeviceMarkers() {
    List<Marker> markers = [];
    List<LatLng> devicePoints = [];

    for (var device in deviceList) {
      if (device.latitude != null && device.longitude != null) {
        try {
          final double lat = double.parse(device.latitude!);
          final double lng = double.parse(device.longitude!);

          String iconName = "default_device.png"; // 默认图标
          if (device.iotSubType != null &&
              deviceTypeIcons.containsKey(device.iotSubType)) {
            iconName = deviceTypeIcons[device.iotSubType]!;
          } else {
            // 根据设备类型设置不同图标
            switch (device.iotSubType) {
              case "101001":
                iconName = "qixiang.png";
                break;
              case "101002":
                iconName = "shangqing.png";
                break;
              case "103001":
                iconName = "chongqing.png";
                break;
              case "103002":
                iconName = "baozi.png";
                break;
              case "108001":
                iconName = "shexiangtou.png";
              case "108003":
                iconName = "shexiangtou.png";
                break;
              case "101010":
                iconName = "yangfen.png";
                break;
              case "103005":
                iconName = "xingyou.png";
                break;
              default:
                iconName = "default_device.png";
            }
          }
          LatLng latLng;

          latLng = LatLng(lat, lng);

          // 添加设备位置到点集合
          devicePoints.add(latLng);

          print("LatLng>>${lat}lng>>${lng}LatLng.$latLng");

          markers.add(
            Marker(
              width: 36.px,
              height: 36.px,
              point: latLng,
              child: GestureDetector(
                onTap: () {
                  _handleDeviceTap(device);
                },
                child: Image.asset(
                  ImageHelper.wrapAssets(iconName),
                  width: 36.px,
                  height: 36.px,
                ),
              ),
            ),
          );
        } catch (e) {
          Logger().e('设备坐标解析错误: ${e.toString()}');
        }
      }
    }

    setState(() {
      deviceMarkers = markers;
    });

    // 更新地图视图，确保既包含地块又包含设备
    if (points.isNotEmpty) {
      _updateMapBounds(devicePoints);
    }
  }

  // 根据巡田经纬度生成图片list
  void _generateFildPatrolsMarkers() {
    List<Marker> markers = [];

    for (var device in _listFildPatrols) {
      if (device.latitude != null && device.longitude != null) {
        try {
          final double lat = double.parse(device.latitude!);
          final double lng = double.parse(device.longitude!);
          markers.add(
            Marker(
              width: 36.px,
              height: 36.px,
              point: LatLng(lat, lng),
              child: GestureDetector(
                onTap: () async {
                  // _handleDeviceTap(device);
                  final newData = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => FieldPatrolHomeListPage(
                        plotNo: widget.plotNo ?? "",
                        year: widget.year ?? "",
                        orgCode: widget.orgCode,
                        linkCode: widget.linkCode,
                        longitude: device.latitude,
                        latitude: device.longitude,
                      ),
                    ),
                  );
                  if (newData != null) {
                    await queryFarmerByAll();
                  }
                },
                child: Image.asset(
                  "assets/images/icon_xuntian.png",
                  width: 36.px,
                  height: 36.px,
                ),
              ),
            ),
          );
        } catch (e) {
          Logger().e('设备坐标解析错误: ${e.toString()}');
        }
      }
    }

    setState(() {
      _fildPatrolsMarkers = markers;
    });
  }

  // 处理设备点击事件
  void _handleDeviceTap(MyPlotDeviceModel device) {
    Logger().i('点击了设备: ${device.iotSubType}');
    Logger().i('点击了设备deviceId: ${device.deviceId}');
    Logger().i('orgCode: ${device.orgCode}');
    // 根据设备类型跳转到不同的详情页
    if (device.iotSubType == "101002") {
      // 土壤设备跳转到土壤设备详情页
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MyFieldSoilEquipment(
            status: device.status ?? "OFFLINE",
            deviceId: device.deviceId ?? "",
            deviceName: device.deviceName ?? "未命名设备",
            insPicUrl1: device.insPicUrl1 ?? "",
            orgCode: device.orgCode ?? "",
          ),
        ),
      );
    } else if (device.iotSubType == "101001") {
      //天气页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => RealTimeDataPage(
              deviceId: device.deviceId ?? "", orgCode: device.orgCode ?? ""),
        ),
      );
    } else if (device.iotSubType == "103001") {
      // 虫害监测设备跳转到虫害统计页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MyFieldPest(
            status: device.status ?? "OFFLINE",
            deviceId: device.deviceId ?? "",
            deviceName: device.deviceName ?? "未命名设备",
            insPicUrl1: device.insPicUrl1 ?? "",
          ),
        ),
      );
    } else if (device.iotSubType == "108003") {
      // 实时监测设备全景
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoOnlinePage(
            iotSubType: device.iotSubType ?? "",
            status: device.status ?? "OFFLINE",
            deviceId: device.deviceId ?? "",
            deviceName: device.deviceName ?? "未命名设备",
            insPicUrl1: device.insPicUrl1 ?? "",
          ),
        ),
      );
    } else if (device.iotSubType == "108001") {
      // 实时监测设备跳转
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoOnlinePage(
            iotSubType: device.iotSubType ?? "",
            status: device.status ?? "OFFLINE",
            deviceId: device.deviceId ?? "",
            deviceName: device.deviceName ?? "未命名设备",
            insPicUrl1: device.insPicUrl1 ?? "",
          ),
        ),
      );
    } else if (device.iotSubType == "101010") {
      // 养分监测设备跳转到养分详情页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MyFieldNutrientInfo(
            status: device.status ?? "OFFLINE",
            deviceId: device.deviceId ?? "",
            deviceName: device.deviceName ?? "未命名设备",
            insPicUrl1: device.insPicUrl1 ?? "",
            orgCode: device.orgCode ?? "",
          ),
        ),
      );
    } else if (device.iotSubType == "103002") {
      // 跳转孢子设备页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MyFieldSoilSporeInfo(
            status: device.status ?? "OFFLINE",
            deviceId: device.deviceId ?? "",
            deviceName: device.deviceName ?? "未命名设备",
            insPicUrl1: device.insPicUrl1 ?? "",
            orgCode: device.orgCode ?? "",
          ),
        ),
      );
    } else if (device.iotSubType == "103005") {
      // 跳转孢子设备页面
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => MyFieldSoilSexualMsgInfo(
            status: device.status ?? "OFFLINE",
            deviceId: device.deviceId ?? "",
            deviceName: device.deviceName ?? "未命名设备",
            insPicUrl1: device.insPicUrl1 ?? "",
            orgCode: device.orgCode ?? "",
          ),
        ),
      );
    } else {
      // 其他设备类型显示简单的设备详情对话框
      _showDeviceDetail(device);
    }
  }

  // 显示设备详情
  void _showDeviceDetail(MyPlotDeviceModel device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(device.deviceName ?? "未命名设备"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text("设备ID: ${device.deviceId ?? '-'}"),
            SizedBox(height: 4.px),
            Text("状态: ${device.status == 'ONLINE' ? '在线' : '离线'}"),
            SizedBox(height: 4.px),
            if (device.iotSubType != null) Text("设备类型: ${device.iotSubType}"),
            SizedBox(height: 4.px),
            Text("经度: ${device.longitude ?? '-'}"),
            SizedBox(height: 4.px),
            Text("纬度: ${device.latitude ?? '-'}"),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text("关闭"),
          ),
        ],
      ),
    );
  }

  _getFlutterMapByRemote() {
    if (_remoteBbox.isNotEmpty) {
      return FlutterMap(
        mapController: _mapController,
        options: MapOptions(
          onTap: (_, __) {
            debugPrint('点击了地图');
            setState(() {
              isActionMap = !isActionMap;
              bottomPanelHeight = isActionMap ? 0 : 0.7;
            });
          },
          initialZoom: 15, // 设置初始缩放级别为18
          initialCenter: LatLng(
              double.parse(_remoteBboxs[1]), double.parse(_remoteBboxs[0])),

          interactionOptions: InteractionOptions(
            flags: flags,
          ),
        ),
        children: [
          TileLayerUtil.tileLayer(TianDiTuType.bdh),
          TileLayerUtil.tileLayer(TianDiTuType.cia),
          PolygonLayer(polygons: _polygons),
          // 添加遥感图层
          if (_remoteSensingEnabled && _remoteBboxs.isNotEmpty)
            TileLayer(
              tileSize: 512,
              wmsOptions: WMSTileLayerOptions(
                  baseUrl:
                      'https://mapservice.bdhic.com/geospatial-api/openapi/commonHttpService/proxy/clipWmsByRegion?',
                  layers: [_remoteLayers],
                  styles: [_remoteStyles],
                  version: '1.1.1',
                  otherParameters: {
                    'regionId': _remoteRegionId,
                    'regionType': "1"
                  }),
              additionalOptions: const {
                'format': 'image/png',
                'transparent': 'true',
              },
            ),
          // 条件性显示遥感图层
          if (_remoteSensingEnabled &&
              _remoteSensingLayers.isNotEmpty &&
              _currentRsLayerIndex < _remoteSensingLayers.length)
            _remoteSensingLayers[_currentRsLayerIndex],

          if (_showDevices) MarkerLayer(markers: deviceMarkers),
          if (_showFildPatrols && _listFildPatrols.isNotEmpty)
            MarkerLayer(markers: _fildPatrolsMarkers),
          // 位置标记图层
          MarkerLayer(markers: locationMarkers),

          // 定位按钮
          // MapLocationButton(
          //   markerCallback: (marker) {
          //     setState(() {
          //       locationMarkers = [marker];
          //     });
          //   },
          //   xAlign: 0.9,
          //   yAlign: 0.5,
          // ),
        ],
      );
    } else {
      return Container();
    }
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;

    return Scaffold(
      body: Stack(
        children: [
          // 地图部分
          _getFlutterMapByRemote(),
          // 头部返回按钮和标题
          Positioned(
            top: MediaQuery.of(context).padding.top + 10.px,
            left: 16.px,
            right: 16.px,
            child: Row(
              children: [
                InkWell(
                  onTap: () => Navigator.of(context).pop(),
                  child: Container(
                    padding: EdgeInsets.all(8.px),
                    // decoration: BoxDecoration(
                    //   color: Colors.black.withOpacity(0.5),
                    //   borderRadius: BorderRadius.circular(20.px),
                    // ),
                    child: Image.asset(
                      ImageHelper.wrapAssets('field_left.png'),
                      color: Colors.white, // 如果需要改变图片颜色，可以使用 color 属性
                      width: 24.px,
                      height: 24.px,
                    ),
                  ),
                ),
                SizedBox(width: 12.px),
                Expanded(
                  child: Text(
                    widget.orgFullName,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18.px,
                      fontWeight: FontWeight.bold,
                      shadows: const [
                        Shadow(
                          blurRadius: 3.0,
                          color: Colors.black,
                          offset: Offset(1.6, 1.6),
                        ),
                      ],
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),

          // 顶部提示信息
          // Positioned(
          //   top: MediaQuery.of(context).padding.top + 50.px,
          //   left: 16.px,
          //   right: 16.px,
          //   child: Container(
          //     padding: EdgeInsets.symmetric(horizontal: 12.px, vertical: 4.px),
          //     decoration: BoxDecoration(
          //       gradient: const LinearGradient(
          //         transform: GradientRotation(4.683 * pi), // 268.31° = 4.683弧度
          //         colors: [
          //           Color.fromRGBO(47, 217, 214, 0),
          //           Color.fromRGBO(5, 215, 231, 0.3),
          //         ],
          //         stops: [0.0143, 0.6491],
          //       ),
          //       borderRadius: BorderRadius.circular(4.px),
          //     ),
          //     child: Row(
          //       mainAxisSize: MainAxisSize.min,
          //       children: [
          //         Container(
          //           padding:
          //               EdgeInsets.symmetric(horizontal: 4.px, vertical: 2.px),
          //           decoration: BoxDecoration(
          //             gradient: const LinearGradient(
          //               transform: GradientRotation(4.683 * pi),
          //               // 268.31° = 4.683弧度
          //               colors: [
          //                 Color.fromRGBO(47, 217, 214, 0.4),
          //                 Color.fromRGBO(5, 215, 231, 0.4),
          //               ],
          //               stops: [0.0143, 0.6491],
          //             ),
          //             borderRadius: BorderRadius.circular(2.px),
          //             border: Border.all(
          //                 color: const Color.fromRGBO(47, 217, 214, 0.5),
          //                 width: 1),
          //           ),
          //           child: Text(
          //             '提示',
          //             style: TextStyle(color: Colors.white, fontSize: 12.px),
          //           ),
          //         ),
          //         SizedBox(width: 2.px),
          //         Text(
          //           '目前正处于播种期，当前为您展示地表温度遥感分析',
          //           style: TextStyle(color: Colors.white, fontSize: 12.px),
          //         ),
          //       ],
          //     ),
          //   ),
          // ),

          // 遥感提示信息
          if (_remoteSensingEnabled)
            Positioned(
              top: MediaQuery.of(context).padding.top + 50.px,
              left: 16.px,
              right: 16.px,
              child: Container(
                padding:
                    EdgeInsets.symmetric(horizontal: 12.px, vertical: 4.px),
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    transform: GradientRotation(4.683 * pi),
                    // 268.31° = 4.683弧度
                    colors: [
                      Color.fromRGBO(47, 217, 214, 0),
                      Color.fromRGBO(5, 215, 231, 0.3),
                    ],
                    stops: [0.0143, 0.6491],
                  ),
                  borderRadius: BorderRadius.circular(4.px),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 4.px, vertical: 2.px),
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          transform: GradientRotation(4.683 * pi),
                          // 268.31° = 4.683弧度
                          colors: [
                            Color.fromRGBO(47, 217, 214, 0.4),
                            Color.fromRGBO(5, 215, 231, 0.4),
                          ],
                          stops: [0.0143, 0.6491],
                        ),
                        borderRadius: BorderRadius.circular(2.px),
                        border: Border.all(
                            color: const Color.fromRGBO(47, 217, 214, 0.5),
                            width: 1),
                      ),
                      child: Text(
                        '提示',
                        style: TextStyle(color: Colors.white, fontSize: 12.px),
                      ),
                    ),
                    SizedBox(width: 2.px),
                    Expanded(
                      child: Text(
                        '正在显示土壤墒情信息，数据采集于${_rsFramesList.isNotEmpty ? _rsFramesList[_currentRsLayerIndex]['monitorTimeStr'] : ""}',
                        style: TextStyle(color: Colors.white, fontSize: 12.px),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // 地块基本信息
          Positioned(
            top: MediaQuery.of(context).padding.top + 90.px,
            left: 16.px,
            child: Container(
              padding: EdgeInsets.all(12.px),
              // decoration: BoxDecoration(
              //   color: Colors.black.withOpacity(0.2),
              //   borderRadius: BorderRadius.circular(2.px),
              // ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  widget.growPatternsId.isNotEmpty
                      ? Text(
                          widget.plotName,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.px,
                            shadows: const [
                              Shadow(
                                blurRadius: 1.0,
                                color: Colors.black,
                                offset: Offset(1.6, 1.6),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox(),
                  SizedBox(height: 4.px),
                  widget.growPatternsId.isNotEmpty
                      ? Text(
                          '${widget.contrArea} 亩 ${widget.cropTypeName}',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.px,
                            shadows: const [
                              Shadow(
                                blurRadius: 1.0,
                                color: Colors.black,
                                offset: Offset(1.6, 1.6),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox(),
                  SizedBox(height: 4.px),
                  widget.growPatternsId.isNotEmpty
                      ? Text(
                          widget.cropBreedName,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14.px,
                            shadows: const [
                              Shadow(
                                blurRadius: 1.0,
                                color: Colors.black,
                                offset: Offset(1.6, 1.6),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox(),
                ],
              ),
            ),
          ),
          // 右侧功能按钮组
          Positioned(
            right: 16.px,
            top: MediaQuery.of(context).padding.top + 90.px,
            child: Column(
              children: [
                Container(
                  width: 48.px,
                  padding: EdgeInsets.all(2.px),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(29, 44, 50, 0.8),
                    borderRadius: BorderRadius.circular(8.px),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(
                            horizontal: 4.px, vertical: 4.px),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Transform.scale(
                              scale: 0.7, // 调整开关尺寸
                              child: Switch(
                                value: _remoteSensingEnabled,
                                onChanged: (bool value) {
                                  _toggleRemoteSensing(value);
                                },
                                trackOutlineColor:
                                    WidgetStateProperty.all(Colors.transparent),
                                activeColor:
                                    const Color.fromRGBO(22, 183, 96, 1),
                                // 激活颜色
                                activeTrackColor:
                                    const Color.fromRGBO(22, 183, 96, 0.4),
                                inactiveThumbColor: Colors.grey[300],
                                inactiveTrackColor:
                                    Colors.grey[500]!.withOpacity(0.1),
                                materialTapTargetSize:
                                    MaterialTapTargetSize.shrinkWrap,
                              ),
                            ),
                            SizedBox(width: 4.px),
                            Text(
                              '遥感',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 12.px,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                SizedBox(height: 8.px), // 模块间距
                // 第一组：巡田和设备
                Container(
                  // padding: EdgeInsets.all(4.px),
                  padding:
                      EdgeInsets.symmetric(vertical: 2.px, horizontal: 4.px),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(29, 44, 50, 0.8),
                    borderRadius: BorderRadius.circular(8.px),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      _buildFunctionButton(
                        color: const Color.fromRGBO(22, 183, 96, 1),
                        icon: 'field_xuntian.png',
                        label: '巡田',
                        onTap: () {
                          setState(() {
                            _showFildPatrols = !_showFildPatrols;
                            if (points.isNotEmpty && _showFildPatrols) {
                              List<LatLng> devicePoints = deviceMarkers
                                  .map((marker) => marker.point)
                                  .toList();
                              _updateMapBounds(devicePoints);
                            }
                          });
                        },
                      ),
                      SizedBox(height: 4.px),
                      _buildFunctionButton(
                        color: const Color.fromRGBO(22, 183, 96, 1),
                        icon: 'field_shebei.png',
                        label: '设备',
                        onTap: () {
                          setState(() {
                            _showDevices = !_showDevices;
                            if (points.isNotEmpty && _showDevices) {
                              List<LatLng> devicePoints = deviceMarkers
                                  .map((marker) => marker.point)
                                  .toList();
                              _updateMapBounds(devicePoints);
                            }
                            // 切换按钮状态
                            if (_showDevices) {
                              _selectedButtons.add('设备');
                            } else {
                              _selectedButtons.remove('设备');
                            }
                          });
                        },
                      ),
                    ],
                  ),
                ),

                // 第二组：AI助手（单独样式）
                SizedBox(height: 12.px), // 模块间距
                Container(
                  padding:
                      EdgeInsets.symmetric(vertical: 2.px, horizontal: 4.px),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(29, 44, 50, 0.8),
                    borderRadius: BorderRadius.circular(8.px),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildFunctionButton(
                        color: const Color.fromRGBO(22, 183, 96, 1),
                        icon: 'field_ai.png',
                        label: '助手',
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                            builder: (context) => const MyFieldChatMainPage(),
                          ));
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // 左下角图例
          if (_remoteSensingEnabled && _currentLegend != null)
            Positioned(
              left: 16.px,
              top: remindList.isNotEmpty
                  ? screenHeight / 2.6
                  : screenHeight / 2.3,
              child: Container(
                padding: EdgeInsets.all(12.px),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(0, 0, 0, 0.4),
                  borderRadius: BorderRadius.circular(8.px),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.white.withOpacity(0.1),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Text(
                          _currentLegend!['label'] ?? '图例',
                          style: TextStyle(
                            fontSize: 12.px,
                            fontWeight: FontWeight.bold,
                            color: Colors.white.withOpacity(0.9),
                          ),
                        ),
                        SizedBox(width: 8.px),
                      ],
                    ),
                    if (_rsFramesList.isNotEmpty)
                      // Padding(
                      //   padding: EdgeInsets.only(top: 4.px),
                      //   child: Text(
                      //     _rsFramesList[_currentRsLayerIndex]['monitorTimeStr'],
                      //     style: TextStyle(
                      //       fontSize: 10.px,
                      //       color: Colors.white.withOpacity(0.9),
                      //     ),
                      //   ),
                      // ),
                      SizedBox(height: 6.px),
                    ..._buildLegendItems(),
                  ],
                ),
              ),
            ),

          // 底部信息面板
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Column(
              children: [
                // 定位按钮
                Align(
                  alignment: Alignment.centerRight,
                  child: Container(
                    padding: EdgeInsets.all(8.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.px),
                    ),
                    margin: EdgeInsets.only(right: 16.px, bottom: 6.px),
                    child: InkWell(
                      onTap: () {
                        if (points.isNotEmpty) {
                          List<LatLng> devicePoints = deviceMarkers
                              .map((marker) => marker.point)
                              .toList();
                          _updateMapBounds(devicePoints);
                        }
                      },
                      child: Container(
                        width: 30.px,
                        height: 30.px,
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(20.px),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              blurRadius: 5,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(Icons.my_location,
                            color: const Color.fromRGBO(22, 183, 96, 0.6),
                            size: 28.px,
                            shadows: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.7),
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ]),
                      ),
                    ),
                  ),
                ),
                // AI提醒
                InkWell(
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (context) => MyFieldSoilAiRemindInfo(
                        pushMsg: remindList[0]["pushMsg"],
                        sendTime: remindList[0]["sendTime"],
                        argiRemindName: remindList[0]["argiRemindName"],
                      ),
                    ));
                  },
                  child: _showAiRemind && remindList.isNotEmpty
                      ? Stack(
                          children: [
                            Image.asset(
                              ImageHelper.wrapAssets('my_field_map_ai_bg.png'),
                              width: 350.px,
                              height: 40.px,
                            ),
                            SizedBox(
                              width: 340.px,
                              child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Image.asset(
                                      ImageHelper.wrapAssets('my_field_ai.png'),
                                      width: 40.px,
                                      height: 40.px,
                                    ),
                                    remindList.isNotEmpty
                                        ? SizedBox(
                                            width: 270.px,
                                            child: Text(
                                              remindList[0]["pushMsg"] ??
                                                  "暂无提醒信息",
                                              style: TextStyle(
                                                color: const Color.fromRGBO(
                                                    29, 44, 50, 1),
                                                fontSize: 12.px,
                                                fontWeight: FontWeight.w500,
                                                // 超出宽度显示省略号
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          )
                                        : Container(),

                                    // 关闭
                                    InkWell(
                                      onTap: () {
                                        setState(() {
                                          _showAiRemind = false;
                                        });
                                      },
                                      child: Icon(
                                        Icons.close,
                                        color:
                                            const Color.fromRGBO(29, 44, 50, 1),
                                        size: 20.px,
                                        weight: 500,
                                      ),
                                    )

                                    // Text(
                                    //   '更多',
                                    //   style: TextStyle(
                                    //     color: Colors.white,
                                    //     fontSize: 12.px,
                                    //     fontWeight: FontWeight.w500,
                                    //   )
                                    // )
                                  ]),
                            )
                          ],
                        )
                      : Container(),
                ),

                // GestureDetector(
                //   onVerticalDragUpdate: (details) {
                //     if (details.primaryDelta! < 0) {
                //       // 上滑，展开面板
                //       setState(() {
                //         bottomPanelHeight = 0.7; // 展开至70%
                //       });
                //     } else if (details.primaryDelta! > 0) {
                //       // 下滑，收起面板
                //       setState(() {
                //         bottomPanelHeight = 0.3; // 收起至30%
                //       });
                //     }
                //   },
                //   child: ,
                // )
                AnimatedContainer(
                  duration: const Duration(milliseconds: 400),
                  height: screenHeight * bottomPanelHeight,
                  margin: EdgeInsets.only(top: 16.px),
                  decoration: BoxDecoration(
                    color: const Color(0xF0F3F8FF),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(20.px),
                      topRight: Radius.circular(20.px),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 10,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: NotificationListener<ScrollNotification>(
                    onNotification: (scrollNotification) {
                      if (scrollNotification is ScrollUpdateNotification) {
                        // 检查是否是底部面板的滚动
                        final currentWidget =
                            scrollNotification.context?.widget;
                        // debugPrint("当前滚动的 widget: $currentWidget");

                        // 判断滚动方向
                        if (scrollNotification.metrics.axis == Axis.vertical) {
                          // 垂直滚动（底部面板）
                          double currentPosition =
                              scrollNotification.metrics.pixels;
                          if (currentPosition <= -40 &&
                              bottomPanelHeight == 0.7) {
                            setState(() {
                              bottomPanelHeight = 0.3;
                            });
                          }
                          if (currentPosition >= 20 &&
                              bottomPanelHeight == 0.3) {
                            setState(() {
                              bottomPanelHeight = 0.7;
                            });
                          }
                        } else if (scrollNotification.metrics.axis ==
                            Axis.horizontal) {
                          // 水平滚动（种植方案）
                          // debugPrint("种植方案横向滚动");
                          if (scrollNotification.metrics.pixels <=
                              scrollNotification.metrics.minScrollExtent) {
                            // debugPrint("到达最左边");
                          }
                          if (scrollNotification.metrics.pixels >=
                              scrollNotification.metrics.maxScrollExtent) {
                            // debugPrint("到达最右边");
                          }
                        }
                      }
                      return true;
                    },
                    child: SingleChildScrollView(
                      hitTestBehavior: HitTestBehavior.opaque,
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: ScrollConfiguration(
                        behavior: ScrollConfiguration.of(context).copyWith(
                          dragDevices: {
                            PointerDeviceKind.touch,
                            PointerDeviceKind.mouse,
                          },
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // 拖动条
                            GestureDetector(
                              onTap: () {
                                setState(() {
                                  bottomPanelHeight =
                                      bottomPanelHeight == 0.3 ? 0.7 : 0.3;
                                });
                              },
                              child: Center(
                                child: Container(
                                  margin: EdgeInsets.only(
                                      top: 10.px, bottom: 10.px),
                                  width: 40.px,
                                  height: 4.px,
                                  decoration: BoxDecoration(
                                    color: Colors.grey[300],
                                    borderRadius: BorderRadius.circular(2.px),
                                  ),
                                ),
                              ),
                            ),

                            // 天气信息

                            GestureDetector(
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 16.px, vertical: 10.px),
                                child: Row(
                                  children: [
                                    weatherDayData != null
                                        ? Image.asset(
                                            weatherCategory
                                                .fromText(
                                                    weatherDayData!.weather ??
                                                        "")
                                                .getWeatherImage(),
                                            height: 28,
                                            width: 28,
                                          )
                                        : Container(),
                                    // const Icon(Icons.wb_sunny, color: Colors.orange),
                                    SizedBox(width: 8.px),
                                    Text(
                                      '今日天气 ',
                                      style: TextStyle(
                                        fontSize: 16.px,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                    SizedBox(width: 4.px),
                                    weatherDayData != null
                                        ? Row(
                                            children: [
                                              Text(
                                                '${weatherDayData!.weather} ${weatherDayData!.windDirDesc}风 ${weatherDayData!.windLvl}级',
                                                style: TextStyle(
                                                  fontSize: 16.px,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                            ],
                                          )
                                        : Container(),
                                    const Spacer(),
                                  ],
                                ),
                              ),
                              onTap: () {
                                for (var device in deviceList) {
                                  if (device.iotSubType == "101001") {
                                    Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                        builder: (context) => RealTimeDataPage(
                                            deviceId: device.deviceId ?? "",
                                            orgCode: device.orgCode ?? ""),
                                      ),
                                    );
                                  }
                                }
                              },
                            ),

                            // 种植方案
                            // Padding(
                            //   padding: EdgeInsets.symmetric(
                            //       horizontal: 16.px, vertical: 8.px),
                            //   child: Text(
                            //     '种植方案',
                            //     style: TextStyle(
                            //       fontSize: 18.px,
                            //       fontWeight: FontWeight.bold,
                            //     ),
                            //   ),
                            // ),
                            widget.growPatternsId.isEmpty
                                ? Container(
                                    margin: EdgeInsets.only(top: 24.px),
                                    padding: EdgeInsets.all(16.px),
                                    child: Center(
                                      child: Text(
                                        '暂无种植方案',
                                        style: TextStyle(
                                          fontSize: 16.px,
                                          color: Colors.grey,
                                        ),
                                      ),
                                    ))
                                : Stack(children: [
                                    SizedBox(
                                      height: 250.px,
                                      child: CustomScrollView(
                                        controller: _scrollController,
                                        key: const PageStorageKey(
                                            'bottomCustom'),
                                        scrollDirection: Axis.horizontal,
                                        slivers: <Widget>[
                                          SliverToBoxAdapter(
                                            child: SizedBox(width: 16.px),
                                          ),
                                          SliverGrid(
                                              delegate:
                                                  SliverChildBuilderDelegate(
                                                (context, index) {
                                                  String dateRange = '';
                                                  Map acti =
                                                      plantingPlanData[index];
                                                  if (acti['planStartDate'] !=
                                                          null &&
                                                      acti['planEndDate'] !=
                                                          null) {
                                                    // 将MMDD格式转换为M月D日
                                                    String startDate =
                                                        acti['planStartDate'];
                                                    String endDate =
                                                        acti['planEndDate'];

                                                    if (startDate.length == 4 &&
                                                        endDate.length == 4) {
                                                      String startMonth =
                                                          startDate
                                                              .substring(0, 2)
                                                              .replaceFirst(
                                                                  RegExp('^0'),
                                                                  '');
                                                      String startDay =
                                                          startDate
                                                              .substring(2, 4)
                                                              .replaceFirst(
                                                                  RegExp('^0'),
                                                                  '');
                                                      String endMonth = endDate
                                                          .substring(0, 2)
                                                          .replaceFirst(
                                                              RegExp('^0'), '');
                                                      String endDay = endDate
                                                          .substring(2, 4)
                                                          .replaceFirst(
                                                              RegExp('^0'), '');

                                                      dateRange =
                                                          '$startMonth月$startDay日-$endMonth月$endDay日';
                                                    } else {
                                                      dateRange =
                                                          '$startDate-$endDate';
                                                    }
                                                  }
                                                  // _selectedPlanName
                                                  //     .value = acti[
                                                  //         'linkName'] ??
                                                  //     "未知农事活动";
                                                  return MyFieldGridItem(
                                                    title: acti['linkName'] ??
                                                        "未知农事活动",
                                                    date: dateRange,
                                                    description:
                                                        acti['standard'] ??
                                                            "无详细标准",
                                                    isShowTitle: false,
                                                    isSelected: currentData![
                                                                'year'] ==
                                                            widget.year &&
                                                        (int.parse(
                                                                "${currentData!['month']}${currentData!['day']}") >=
                                                            int.parse(acti[
                                                                'planStartDate'])) &&
                                                        int.parse(
                                                                "${currentData!['month']}${currentData!['day']}") <=
                                                            int.parse(acti[
                                                                'planEndDate']),
                                                    onTap: () {
                                                      MyFieldGridAlert.showAlert(
                                                          context,
                                                          acti['linkName'] ??
                                                              "未知农事活动",
                                                          acti['standard'] ??
                                                              "无详细标准",
                                                          dateRange);
                                                      // setState(() {
                                                      //   _selectedPlanId =
                                                      //       acti[
                                                      //           'patternsLinkId'];
                                                      // });
                                                    },
                                                  );
                                                },
                                                childCount:
                                                    plantingPlanData.length,
                                              ),
                                              gridDelegate:
                                                  const SliverGridDelegateWithFixedCrossAxisCount(
                                                crossAxisCount: 1, // 只显示1行
                                                mainAxisSpacing: 10.0, // 水平间距
                                                childAspectRatio:
                                                    250 / 176, // 宽高比
                                              )),
                                          SliverToBoxAdapter(
                                            child: SizedBox(width: 16.px),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Positioned(
                                        right: 0,
                                        top: 40.px,
                                        child: IgnorePointer(
                                          child: Container(
                                            decoration: BoxDecoration(
                                              gradient: LinearGradient(
                                                begin: Alignment.centerRight,
                                                end: Alignment.centerLeft,
                                                colors: [
                                                  HexColor.fromHex("#F0F3F8")
                                                      .withOpacity(1),
                                                  HexColor.fromHex("#F0F3F8")
                                                      .withOpacity(0),
                                                ],
                                              ),
                                            ),
                                            height: 210.px,
                                            width: 60.px,
                                          ),
                                        )),
                                    Positioned(
                                        left: 16.px,
                                        top: 0.px,
                                        child: ValueListenableBuilder(
                                          valueListenable: _selectedPlanName,
                                          builder: (context, value, child) {
                                            return Text(
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                              softWrap: true,
                                              strutStyle: const StrutStyle(
                                                height: 1.7,
                                              ),
                                              "种植方案-$value",
                                              style: TextStyle(
                                                fontSize: 18.px,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            );
                                          },
                                        ))
                                  ]),
                            Padding(
                              padding: EdgeInsets.only(left: 16.px, top: 30.px),
                              child: SizedBox(
                                height: 30.px,
                                child: Text(
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  softWrap: true,
                                  strutStyle: const StrutStyle(
                                    height: 1.7,
                                  ),
                                  "更多服务",
                                  style: TextStyle(
                                    fontSize: 18.px,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 16.px,
                            ),
                            Padding(
                              padding:
                                  EdgeInsets.only(left: 16.px, right: 16.px),
                              child: SingleChildScrollView(
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: _moreServices.map((item) {
                                    return GestureDetector(
                                      onTap: () {
                                        _moreServiceTap(item['title'],
                                            prodProcessName:
                                                item['prodProcessName']);
                                      },
                                      child: Container(
                                        color: Colors.transparent,
                                        height: 100.px,
                                        width: 60.px,
                                        child: Column(
                                          children: [
                                            Image.asset(
                                              ImageHelper.wrapAssets(
                                                  item['icon']),
                                              width: 28.px,
                                              height: 28.px,
                                            ),
                                            SizedBox(height: 10.px),
                                            Text(item['title'],
                                                style: TextStyle(
                                                  fontSize: 12.px,
                                                  fontWeight: FontWeight.w400,
                                                )),
                                          ],
                                        ),
                                      ),
                                    );
                                  }).toList(),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // _buildBottomButtons(),

          // 底部日期选择器
          // _buildBottomType(),

          // 功能按钮
          // _buildBottomButtons(),
        ],
      ),
    );
  }

//更多服务点击
  void _moreServiceTap(String title, {String? prodProcessName}) async {
    switch (title) {
      case '地块画像':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MyFieldParcelImage(
              plotNo: widget.plotNo ?? "",
              year: widget.year ?? "",
              orgFullName: widget.orgFullName ?? "",
              cropBreedName: widget.cropBreedName ?? "",
              contrArea: widget.contrArea ?? "",
              cropTypeName: widget.cropTypeName ?? "",
            ),
          ),
        );
        break;
      case '遥感服务':
        // TODO: 实现遥感监测功能
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MyFieldMapRemotePage(
              plotNo: widget.plotNo,
              year: widget.year,
              plotName: widget.plotName,
              cropTypeName: widget.cropTypeName,
              cropBreedName: widget.cropBreedName,
              contrArea: widget.contrArea.toString(),
              orgFullName: widget.orgFullName,
              growPatternsId: widget.growPatternsId ?? '',
              geomJson: widget.geomJson,
              linkCode: widget.linkCode,
              orgCode: widget.orgCode,
              cropType: widget.cropType,
            ),
          ),
        );
        break;
      case '农事提醒':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => MyFieldSoilAiRemind(
              plotNo: widget.plotNo ?? "",
              year: widget.year ?? "",
            ),
          ),
        );

        break;
      case '巡田情况':
        // TODO: 实现设备监测功能
        final newData = await Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => FieldPatrolHomeListPage(
              plotNo: widget.plotNo ?? "",
              year: widget.year ?? "",
              orgCode: widget.orgCode,
              linkCode: widget.linkCode,
              longitude: "",
              latitude: "",
            ),
          ),
        );
        if (newData != null) {
          await queryFarmerByAll();
        }
        break;
      case '施肥建议':
        {
          widget.cropType == "1"
              ? Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        MyPaddyfieldFertilizationRecommendations(
                      plotNo: widget.plotNo, //"806010100GH00286"
                      statYear: widget.year,
                      cropType: widget.cropType,
                    ),
                  ),
                )
              : Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => MyFieldFertilizationRecommendations(
                            plotNo: widget.plotNo ?? "",
                            year: widget.year ?? "",
                            orgFullName: widget.orgFullName ?? "",
                            cropBreedName: widget.cropBreedName ?? "",
                            contrArea: widget.contrArea ?? "",
                            cropTypeName: widget.cropTypeName ?? "",
                            cropType: widget.cropType ?? "",
                            title: title ?? "",
                            prodProcessName: prodProcessName ?? "",
                          )));
        }

        break;
    }
  }

  // Future<void> _realTimeDataquery() async {
  //   String? deviceId="";
  //   for (var device in deviceList) {
  //     if(device.iotSubType == "101001"){
  //       deviceId=device.deviceId;
  //     }
  //   }
  //   MyWeatherPageService.realTimeDataquery({"deviceId":deviceId})
  //       .then((res) {
  //     if (res['data'] != null) {
  //       weatherRealData = WeatherRealData.fromJson(res['data']);
  //       setState(() {
  //
  //       });
  //     } else {}
  //     // isLoading = false;
  //   }).catchError((error) {
  //     setState(() {
  //       // isLoading = false;
  //     });
  //   });
  // }

  void _updateDirection(Offset delta) {
    final dx = delta.dx;
    final dy = delta.dy;

    // 判断是水平方向还是垂直方向的移动
    if (dx.abs() > dy.abs()) {
      // 水平方向移动
      if (dx > 0) {
      } else {}
    } else {
      // 垂直方向移动
      if (dy > 0) {
        //下
        setState(() {
          bottomPanelHeight = 0.3;
        });
      } else {
        //上
        setState(() {
          bottomPanelHeight = 0.6;
        });
      }
    }
  }

  Widget _buildFunctionButton({
    required Color color,
    required String icon,
    required String label,
    required VoidCallback onTap,
  }) {
    bool isSelected = _selectedButtons.contains(label); // 通过按钮标签判断选中状态

    return InkWell(
      onTap: () {
        if (label != '助手') {
          setState(() {
            if (isSelected) {
              _selectedButtons.remove(label);
            } else {
              _selectedButtons.add(label);
            }
          });
        }
        onTap();
      },
      child: Container(
        width: 40.px,
        height: 59.px,
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent, // 切换颜色逻辑
          borderRadius: BorderRadius.circular(8.px),
          border:
              isSelected ? Border.all(color: color, width: 1) : null, // 可选边框效果
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              ImageHelper.wrapAssets(icon),
              color: isSelected ? Colors.white : Colors.white.withOpacity(0.8),
              // 图标颜色微调
              width: 24.px,
              height: 24.px,
            ),
            SizedBox(height: 4.px),
            Text(
              label,
              style: TextStyle(
                color:
                    isSelected ? Colors.white : Colors.white.withOpacity(0.9),
                fontSize: 12.px,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 功能按钮
  Widget _buildBottomButtons() {
    return Positioned(
      bottom: 10.px,
      right: 10.px,
      child: Column(
        children: [
          // 展开的功能按钮列表
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isBottomMenuExpanded ? 330.px : 0,
            child: SingleChildScrollView(
              physics: const NeverScrollableScrollPhysics(),
              child: Column(
                children: [
                  _buildFunctionItem('地块画像', 'my_field_map_bottom_dikuai.png'),
                  _buildFunctionItem('遥感监测', 'my_field_map_bottom_yaogan.png'),
                  _buildFunctionItem('农事提醒', 'my_field_map_bottom_nongshi.png'),
                  _buildFunctionItem('巡田情况', 'my_field_map_bottom_shebei.png'),
                ],
              ),
            ),
          ),
          // 展开/收起按钮
          SizedBox(
            height: 70.px,
            width: 70.px,
            child: InkWell(
              onTap: () {
                setState(() {
                  _isBottomMenuExpanded = !_isBottomMenuExpanded;
                });
              },
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Image.asset(
                    ImageHelper.wrapAssets(_isBottomMenuExpanded
                        ? 'my_field_map_bottom_close.png'
                        : 'my_field_map_bottom_open.png'),
                    fit: BoxFit.cover,
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (!_isBottomMenuExpanded)
                        Image.asset(
                          ImageHelper.wrapAssets(
                              'my_field_map_bottom_function.png'),
                          width: 28.px,
                          height: 28.px,
                        ),
                      if (!_isBottomMenuExpanded)
                        Text(
                          '功能',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10.px,
                            fontWeight: FontWeight.w400,
                          ),
                        )
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionItem(String title, String iconName) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.px),
      child: InkWell(
        onTap: () async {
          // 处理各功能按钮的点击事件
          switch (title) {
            case '地块画像':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MyFieldParcelImage(
                    plotNo: widget.plotNo ?? "",
                    year: widget.year ?? "",
                    orgFullName: widget.orgFullName ?? "",
                    cropBreedName: widget.cropBreedName ?? "",
                    contrArea: widget.contrArea ?? "",
                    cropTypeName: widget.cropTypeName ?? "",
                  ),
                ),
              );
              break;
            case '遥感监测':
              // TODO: 实现遥感监测功能
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MyFieldMapRemotePage(
                    plotNo: widget.plotNo,
                    year: widget.year,
                    plotName: widget.plotName,
                    cropTypeName: widget.cropTypeName,
                    cropBreedName: widget.cropBreedName,
                    contrArea: widget.contrArea.toString(),
                    orgFullName: widget.orgFullName,
                    growPatternsId: widget.growPatternsId ?? '',
                    geomJson: widget.geomJson,
                    linkCode: widget.linkCode,
                    orgCode: widget.orgCode,
                    cropType: widget.cropType,
                  ),
                ),
              );
              break;
            case '农事提醒':
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => MyFieldSoilAiRemind(
                    plotNo: widget.plotNo ?? "",
                    year: widget.year ?? "",
                  ),
                ),
              );

              break;
            case '巡田情况':
              // TODO: 实现设备监测功能
              final newData = await Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FieldPatrolHomeListPage(
                    plotNo: widget.plotNo ?? "",
                    year: widget.year ?? "",
                    orgCode: widget.orgCode,
                    linkCode: widget.linkCode,
                    longitude: "",
                    latitude: "",
                  ),
                ),
              );
              if (newData != null) {
                await queryFarmerByAll();
              }
              break;
          }
        },
        child: Container(
          width: 70.px,
          height: 70.px,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.white, width: 2),
            borderRadius: BorderRadius.circular(35.px),
            image: DecorationImage(
              image: AssetImage(
                  ImageHelper.wrapAssets('my_field_map_bottom_bg.png')),
              fit: BoxFit.cover,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image.asset(
                ImageHelper.wrapAssets(iconName),
                width: 28.px,
                height: 28.px,
              ),
              SizedBox(height: 4.px),
              Text(
                title,
                style: TextStyle(
                  color: Colors.black,
                  fontSize: 10.px,
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFarmingActivity(String title, String date, String description,
      String prodProcessName, plan,
      {String? isSeedLand, String? patternsLinkId, String? isFertilize}) {
    return Container(
      padding: EdgeInsets.all(16.px),
      margin: EdgeInsets.only(bottom: 12.px),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8.px),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 16.px,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                date,
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14.px,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.px),
          Text(
            description,
            style: TextStyle(
              color: Colors.grey[800],
              fontSize: 14.px,
            ),
          ),
          SizedBox(height: 12.px),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              isFertilize == "1"
                  ? _buildActionButton(
                      '施肥建议卡', Colors.green, title, prodProcessName)
                  : Container(),
              // _buildActionButton('结束农事', Colors.green,title, prodProcessName),
              // _buildActionButton('记录我的农事', Colors.green,title, prodProcessName,isSeedLand: isSeedLand,patternsLinkId:patternsLinkId),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(
      String text, Color color, String title, String prodProcessName,
      {String? isSeedLand, String? patternsLinkId}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.px, vertical: 6.px),
      decoration: BoxDecoration(
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(16.px),
      ),
      child: InkWell(
        onTap: () {
          if (text == '记录我的农事') {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => MyFieldRecordAgriculturalActivities(
                          plotNo: widget.plotNo ?? "",
                          year: widget.year ?? "",
                          orgFullName: widget.orgFullName ?? "",
                          cropBreedName: widget.cropBreedName ?? "",
                          contrArea: widget.contrArea ?? "",
                          cropTypeName: widget.cropTypeName ?? "",
                          cropType: widget.cropType ?? "",
                          orgCode: widget.orgCode ?? "",
                          title: title ?? "",
                          prodProcessName: prodProcessName ?? "",
                          isSeedLand: isSeedLand ?? "",
                          patternsLinkId: patternsLinkId ?? "",
                        )));
          }
          if (text == '施肥建议卡') {
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => MyFieldFertilizationRecommendations(
                          plotNo: widget.plotNo ?? "",
                          year: widget.year ?? "",
                          orgFullName: widget.orgFullName ?? "",
                          cropBreedName: widget.cropBreedName ?? "",
                          contrArea: widget.contrArea ?? "",
                          cropTypeName: widget.cropTypeName ?? "",
                          cropType: widget.cropType ?? "",
                          title: title ?? "",
                          prodProcessName: prodProcessName ?? "",
                        )));
          }
          // 处理按钮的点击事件
        },
        child: Text(
          text,
          style: TextStyle(
            color: color,
            fontSize: 12.px,
          ),
        ),
      ),
    );
  }

  // 切换遥感状态
  void _toggleRemoteSensing(bool enabled) {
    print("切换遥感状态: $enabled");
    if (enabled) {
      if (_remoteSensingLayers.isEmpty) {
        print("遥感图层为空，正在加载数据...");
        _loadRemoteSensingData();
      } else {
        print("已有${_remoteSensingLayers.length}个遥感图层");
      }
    }

    setState(() {
      _remoteSensingEnabled = enabled;
    });

    print("遥感状态更新为: $enabled, 图层数量: ${_remoteSensingLayers.length}");
    if (_remoteSensingLayers.isNotEmpty) {
      print(
          "当前选中图层: $_currentRsLayerIndex, 日期: ${_rsFramesList.isNotEmpty ? _rsFramesList[_currentRsLayerIndex]['monitorTimeStr'] : 'unknown'}");
    }
    // _scrollToToday();
  }

  // 底部遥感日期选择器
  Widget _buildBottomType() {
    if (!_remoteSensingEnabled || _rsFramesList.isEmpty) {
      return Container();
    }

    var screenHeight = MediaQuery.of(context).size.height;
    return Positioned(
      bottom: 80.px,
      left: 0,
      right: 0,
      child: Container(
        height: 60.px,
        padding: EdgeInsets.symmetric(vertical: 4.px),
        color: Colors.black.withOpacity(0.5),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.symmetric(horizontal: 16.px),
          itemCount: _rsFramesList.length,
          itemBuilder: (context, index) {
            var frame = _rsFramesList[index];
            String timeStr = frame['monitorTimeStr'] ?? '';
            bool isSelected = _currentRsLayerIndex == index;

            return GestureDetector(
              onTap: () => _switchRsLayer(index),
              child: Container(
                width: 100.px,
                margin: EdgeInsets.symmetric(horizontal: 4.px, vertical: 4.px),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color.fromRGBO(22, 183, 96, 0.9)
                      : Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(8.px),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.calendar_today_outlined,
                      size: 16.px,
                      color: isSelected
                          ? Colors.white
                          : const Color.fromRGBO(22, 183, 96, 1),
                    ),
                    SizedBox(height: 2.px),
                    Text(
                      timeStr,
                      style: TextStyle(
                        fontSize: 12.px,
                        fontWeight:
                            isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected ? Colors.white : Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
