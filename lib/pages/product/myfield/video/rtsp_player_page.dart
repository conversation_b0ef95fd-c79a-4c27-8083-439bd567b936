import 'dart:convert';

import 'package:bdh_smart_agric_app/pages/product/myfield/video/pic_diff_list_item_page.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/video/video_online_item_event_detail_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_vlc_player/flutter_vlc_player.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';

import '../../../../assets/app_colors.dart';
import '../../../../model/video_pager_model.dart';
import '../../../../utils/request/my_video_page_service.dart';
import '../../../../model/video_info_live_model.dart';
import 'fullscreen_videopage.dart';

class RTSPPlayerPage extends StatefulWidget {
  final String deviceId;
  final String iotSubType;

  const RTSPPlayerPage({
    Key? key,
    required this.iotSubType,
    required this.deviceId,
  }) : super(key: key);

  @override
  _RTSPPlayerPageState createState() => _RTSPPlayerPageState();
}

class _RTSPPlayerPageState extends State<RTSPPlayerPage> {
  VlcPlayerController? _videoPlayerController;
  VideoLiveData? videoLiveData;
  bool _isFullscreen = false; // 添加全屏状态
  List<VideoPageData> videoPagesdatas = [];
  final groupedEvents = <String, List<VideoPageData>>{};

  int _currentPage = 1; // 当前页码
  int _pageSize = 10; // 每页显示数量
  int _totalItems = 0; // 总条数
  int _totalPages = 0; // 总页数
  bool _isLoading = false; // 是否正在加载数据
  bool isClickOpen = false;

  @override
  void initState() {
    super.initState();
    _initVideo("1");
  }

  _initVideo(streamType) {
    _initVideoPlayer(streamType: streamType);
    if (widget.iotSubType == "108003") {
    }
    _initMockVideoEvents();
  }

  // 初始化视频播放器
  Future<void> _initVideoPlayer({required String streamType}) async {
    try {
      final res = await MyVideoPageService.wetIotDeviceInfoLive(
          {"deviceId": widget.deviceId, "streamType": streamType});
      if (res['data'] != null) {
        videoLiveData = VideoLiveData.fromJson(res['data']);
        Logger().e('videoLiveData: ${videoLiveData!.data}');
        _videoPlayerController = VlcPlayerController.network(
          videoLiveData!.data ?? "",
          hwAcc: HwAcc.auto,
          autoPlay: true,
          options: VlcPlayerOptions(
            advanced: VlcAdvancedOptions([
              VlcAdvancedOptions.networkCaching(2000),
            ]),
            http: VlcHttpOptions([
              VlcHttpOptions.httpReconnect(true),
            ]),
            rtp: VlcRtpOptions([
              VlcRtpOptions.rtpOverRtsp(true),
            ]),
            // extras: ['--verbose=2'],
          ),
        );
        setState(() {});
      }
    } catch (error) {
      Logger().e('初始化视频播放器出错: $error');
      setState(() {});
    }
  }

  //获取视频渠道
  // Future<void> _videoChannel() async {
  //   try {
  //     final res =
  //         await MyVideoPageService.videoChannel({"deviceId": widget.deviceId});
  //     if (res['data'] != null) {
  //       channelName = res['data']["data"][0]["channelNo"];
  //       Logger().d('channelName111: $channelName');
  //       setState(() {});
  //     }
  //   } catch (error) {
  //     Logger().e('初始化视频播放器出错: $error');
  //     setState(() {});
  //   }
  // }

  // 初始化模拟视频事件数据
  Future<void> _initMockVideoEvents() async {
    try {
      final res = await MyVideoPageService.videoEvent({
        "deviceId": widget.deviceId,
        "page": _currentPage,
        "rows": _pageSize,
      });
      if (res.success!=null) {
        if (res.data!= null) {
          videoPagesdatas = res.data!;
          _groupEventsByDate();
          setState(() {
          });
        }
      }
    } catch (error) {
      setState(() {
        _isLoading = false; // 加载出错
      });
    }
  }

  // 按日期分组事件
  void _groupEventsByDate() {
    for (final event in videoPagesdatas) {
      final date = event.createTime!.split(' ')[0];
      groupedEvents.putIfAbsent(date, () => []).add(event);
    }
    print("groupedEvents>>"+groupedEvents.length.toString());
  }

  @override
  void dispose() {
    if (_videoPlayerController != null) {
      _videoPlayerController!.stopRendererScanning();
      _videoPlayerController!.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Center(
          child: Container(
            margin: EdgeInsets.only(top: 12.h),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.0),
            ),
            alignment: Alignment.center,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildVideoPlayer(),
                SizedBox(height: 10.h),
                _buildImageComparisonButton(),
              ],
            ),
          ),
        ),
        SizedBox(
          height: 12.h,
        ),
        Expanded(
          child: _getVideoPage(),
        )
      ],
    );
  }

  // 构建视频播放器
  Widget _buildVideoPlayer() {
    return Stack(
      children: [
        Center(
            child: _videoPlayerController != null
                ? VlcPlayer(
                    controller: _videoPlayerController!,
                    aspectRatio: 16 / 9,
                  )
                : Center(
                    child: Text("视频加载中...."),
                  )),
        Positioned(
          right: 0,
          top: 0,
          child: GestureDetector(
            onTap: () {
              // if (_videoPlayerController != null) {
              //   _videoPlayerController!.pause();
              // }
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FullScreenVideoPage(
                    deviceId: widget.deviceId,
                  ),
                ),
              );
            },
            child: Container(
              margin: EdgeInsets.all(12.h),
              alignment: Alignment.center,
              child: GestureDetector(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      videoTypes[select],
                      style: TextStyle(color: Colors.white),
                    ),
                    SizedBox(
                      width: 4.w,
                    ),
                    Image.asset(
                      isClickOpen
                          ? "assets/images/tally_up_bg.png"
                          : "assets/images/tally_up_down.png",
                      width: 6.w,
                      height: 6.w,
                      color: Colors.grey,
                    )
                  ],
                ),
                onTap: () {
                  _showZWPicker();
                },
              ),
            ),
          ),
        ),
        Positioned(
          right: 0,
          bottom: 0,
          child: GestureDetector(
            onTap: () {
              if (_videoPlayerController != null) {
                _videoPlayerController!.pause();
              }
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FullScreenVideoPage(
                    deviceId: widget.deviceId,
                  ),
                ),
              );
            },
            child: Container(
              margin: EdgeInsets.all(12.h),
              alignment: Alignment.center,
              width: 36,
              height: 36,
              child: Image.asset("assets/images/icon_fangfa.png"),
            ),
          ),
        ),
      ],
    );
  }

  // 构建图片对比按钮
  Widget _buildImageComparisonButton() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => PicDiffListItemPage(
              deviceId: widget.deviceId ?? "",
            ),
          ),
        );
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
            "assets/images/icon_pic_diff.png",
            width: 24,
            height: 24,
          ),
          SizedBox(width: 8.w),
          const Text("图片对比"),
        ],
      ),
    );
  }

// 构建视频事件列表
  Widget _getVideoPage() {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        if (notification is ScrollEndNotification &&
            notification.metrics.extentAfter == 0) {
          _loadMoreData(); // 加载更多数据
          return true;
        }
        return false;
      },
      child: Column(
        children: [
          Expanded(
            child: ListView.builder(
                    itemCount: groupedEvents.length,
                    itemBuilder: (context, index) {
                      final date = groupedEvents.keys.elementAt(index);
                      final eventsOnDate = groupedEvents[date]!;

                      return Container(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 10),
                              child: Text(
                                date,
                                style: const TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            MediaQuery.removePadding(
                              context: context,
                              removeTop: true,
                              child: ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: eventsOnDate.length,
                                itemBuilder: (context, eventIndex) {
                                  final event = eventsOnDate[eventIndex];
                                  // 将时间戳转换为 DateTime 对象
                                  DateTime dateTime =
                                      DateTime.fromMillisecondsSinceEpoch(
                                          int.parse(eventsOnDate[eventIndex]
                                              .eventTime!
                                              .toString()));

                                  // 格式化时间为 hh:mm 格式
                                  String formattedTime =
                                      DateFormat('HH:mm').format(dateTime);

                                  return _getItemVieoType(formattedTime, event);
                                },
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
          ),
          if (_isLoading && _currentPage < _totalPages)
            const Padding(
              padding: EdgeInsets.all(8.0),
              child: Center(child: CircularProgressIndicator()), // 显示加载更多指示器
            ),
        ],
      ),
    );
  }

  Widget _getItemVieoType(String formattedTime, VideoPageData event) {
    return GestureDetector(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            formattedTime,
            style: const TextStyle(fontSize: 13),
          ),
          const SizedBox(
            width: 10,
          ),
          Expanded(
              child: Container(
                  margin: EdgeInsets.only(bottom: 12.h),
                  padding: EdgeInsets.all(12.h),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  alignment: Alignment.center,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(4.0), // 设置圆角半径
                        child: Image.network(
                          json.decode(event.procJson ?? "")["data"]["url"]?? event.eventPicUrl,
                          width: 90,
                          height: 50,
                          fit: BoxFit.cover,
                        ),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          _getTypeText(event),
                        ],
                      ),
                    ],
                  )))
        ],
      ),
      onTap: () {
        jumpByType(event);
      },
    );
  }

  Widget _getTypeText(VideoPageData event) {
    if (int.parse(event.eventType ?? "0") >= 4) {
      //生育期
      return Text('${json.decode(event.procJson ?? "")["data"]["stage"]}');
    } else {
      return Text(' ${json.decode(event.procJson ?? "")["data"]["category"]}');
    }
  }

  jumpByType(VideoPageData event) {
    if (int.parse(event.eventType ?? "0") >= 4) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoOnlineItemEventDetailPage(
            videoUrl: event.aftVideoUrl ?? "",
            pic:json.decode(event.procJson ?? "")["data"]["url"]?? event.eventPicUrl,
            eventType: event.eventType ?? "",
            eventText: json.decode(event.procJson ?? "")["data"]["stage"] ?? "",
          ),
        ),
      );
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => VideoOnlineItemEventDetailPage(
            videoUrl: event.aftVideoUrl ?? "",
            pic: event.eventPicUrl ?? "",
            eventType: event.eventType ?? "",
            eventText:
                json.decode(event.procJson ?? "")["data"]["category"] ?? "",
          ),
        ),
      );
    }
  }

  // 加载更多数据
  void _loadMoreData() {
    _currentPage++;
    _initMockVideoEvents();
  }

  int select = 0;
  List<String> videoTypes = ["子码流", "主码流"];

  void _showZWPicker() {
    BrnMultiDataPicker(
      context: context,
      title: '码流',
      //修改选中字体粗细
      themeData: BrnPickerConfig(
          itemTextSelectedStyle: BrnTextStyle(fontWeight: FontWeight.w500)),
      delegate:
          HomeDropTypeRowDelegate(firstSelectedIndex: select, list: videoTypes),
      confirmClick: (list) {
        if (_videoPlayerController != null) {
          _videoPlayerController!.pause();
          _videoPlayerController!.stopRendererScanning();
          _videoPlayerController!.dispose();
          _videoPlayerController = null;
        }
        select = list[0];
        Future.delayed(const Duration(milliseconds: 500), () {
          // 调整延迟时间，例如 300ms
          if(select==0){
            _initVideo("1");
          }else{
            _initVideo("0");
          }
        });
      },
    ).show();
  }
}

class HomeDropTypeRowDelegate implements BrnMultiDataPickerDelegate {
  int firstSelectedIndex = 0;
  List<String> list = [];

  HomeDropTypeRowDelegate({this.firstSelectedIndex = 0, required this.list});

  @override
  int numberOfComponent() {
    return 1;
  }

  @override
  int numberOfRowsInComponent(int component) {
    return list.length;
  }

  @override
  String titleForRowInComponent(int component, int index) {
    return list[index];
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  selectRowInComponent(int component, int row) {
    firstSelectedIndex = row;
  }

  @override
  int initSelectedRowForComponent(int component) {
    if (0 == component) {
      return firstSelectedIndex;
    }
    return 0;
  }
}
