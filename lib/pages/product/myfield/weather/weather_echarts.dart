import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_echarts/flutter_echarts.dart';

class WeatherEcharts extends StatefulWidget {
  final List<dynamic> data;
  final List<dynamic> time;
  final String depth;
  final String desTitle;
  final String temperatureName;
  final String humidityName;

  const WeatherEcharts({
    super.key,
    required this.data,
    required this.time,
    required this.desTitle,
    required this.depth,
    required this.temperatureName,
    required this.humidityName,
  });

  @override
  State<WeatherEcharts> createState() => _SoilEquipmentEchartsState();
}

class _SoilEquipmentEchartsState extends State<WeatherEcharts> {
  @override
  Widget build(BuildContext context) {
    final option = '''
     {
  "color": ["#288BFF", "#20BD8E"],
  "animation": false,
  "title": {},
  "tooltip": {
    "trigger": "axis",
    "formatter": function(params) {
      var result = params[0].name + '<br/>';
      params.forEach(function(item) {
        result += item.seriesName + ': ' + item.value + (item.seriesName.includes('温度') ? ' °C' : '') + '<br/>';
      });
      return result;
    }
  },
  "legend": {
    "data": ["${widget.depth}${widget.temperatureName}", "${widget.depth}${widget.humidityName}"],
    "top": 0
  },
  "grid": {
    "left": "10%",
    "right": "6%",
    "containLabel": true
  },
  "toolbox": {
    "feature": {
      "saveAsImage": {
        "show": false
      }
    }
  },
  "xAxis": {
    "type": "category",
    "boundaryGap": false,
    "axisLabel": {
      "show": true,
      "interval": 'auto',
      "rotate": ${widget.time.length > 3 ? 45 : 0}
    },
    "data": ${jsonEncode(widget.time)}
  },
  "yAxis": [
    {
      "type": "value",
      "name": "${widget.desTitle}",
      "position": "left",
      "nameLocation": "end",
      "nameGap": 20,
      "nameRotate": 0,
      "interval": 100
    },
    {
      "type": "value",
      "name": "",
      "position": ""
    }
  ],
  "series": [
    {
      "name": "${widget.depth}${widget.temperatureName}",
      "type": "line", // 第一个系列保持 type 为 line
      "data": ${jsonEncode(widget.data[0])},
      "smooth": true,
      "yAxisIndex": 0,
      "areaStyle": {} // 添加 areaStyle，使其显示为面积图
    },
    {
      "name": "${widget.depth}${widget.humidityName}",
      "type": "line", // 第二个系列保持 type 为 line
      "data": ${jsonEncode(widget.data[1])},
      "smooth": false,
      "yAxisIndex": 1
    }
  ],
  "dataZoom": [
    {
      "type": "slider",
      "xAxisIndex": 0
    }
  ]
}
    ''';

    return Container(
      height: 230,
      width: 600,
      child: Echarts(
        option: option,
      ),
    );
  }
}