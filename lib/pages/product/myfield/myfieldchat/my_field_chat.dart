/*
 * <AUTHOR>
 * @description: 我的田 AI 助手
 * @date 2025/04/2 10:21:03
*/
import 'dart:convert';
import 'dart:typed_data';
import 'dart:async';
import 'dart:io';
import 'package:bruno/bruno.dart';
import 'package:logger/logger.dart';
import 'package:record/record.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:oktoast/oktoast.dart';

import '../../../../utils/permission_util.dart';
import '../../../../utils/request/chat_repository.dart';
import '../../../../components/bdh_network_image.dart';
import '../../../../const/url_config_const.dart';
import 'MyFieldSessionEntity.dart';
import 'my_field_chat_message_entity.dart';
import 'my_field_chat_weather_address.dart';
import 'my_field_history_message_entity.dart';
import 'my_field_question_dictionary_entity.dart';
import 'my_field_question_record_entity.dart';

class MyFieldChatMainPage extends StatefulWidget {
  const MyFieldChatMainPage({super.key});

  @override
  State<MyFieldChatMainPage> createState() => _MyFieldChatMainPageState();
}

class AppColors {
  static const green = Color.fromRGBO(30, 192, 106, 1);
  static const lightGreen = Color.fromRGBO(30, 192, 106, 0.2);
  static const white = Colors.white;
  static const whiteTransparent = Color.fromRGBO(255, 255, 255, 0.8);
  static const textPrimary = Color.fromRGBO(16, 46, 83, 1);
  static const textSecondary = Color.fromRGBO(111, 126, 152, 1);
  static const black = Color.fromRGBO(0, 0, 0, 1);
  static const red = Colors.red;
}

// 边框样式
class AppStyles {
  static BoxDecoration roundedContainer({
    Color color = AppColors.whiteTransparent,
    double radius = 35,
    Color borderColor = AppColors.white,
    double borderWidth = 1,
  }) {
    return BoxDecoration(
      color: color,
      borderRadius: BorderRadius.circular(radius),
      border: Border.all(color: borderColor, width: borderWidth),
    );
  }
  
  static TextStyle messageText({
    Color color = AppColors.textPrimary,
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.w400,
  }) {
    return TextStyle(
      fontSize: fontSize.px,
      fontWeight: fontWeight,
      color: color,
    );
  }
}

class _MyFieldChatMainPageState extends State<MyFieldChatMainPage>
    with WidgetsBindingObserver {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  final TextEditingController _textEditingController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final FocusNode _focusNode = FocusNode();

  List<MyFieldQuestionRecordEntity> myFieldQuestionRecordList = [];
  List<MyFieldQuestionDictionaryEntity> myFieldQuestionDictionaryList = [];

  String currentResult = "";

  List<MyFieldSessionEntity> sessionList = [];
  bool isKeyborderShow = false;
  bool isInputWord = false;
  String currentTaskId = "";
  String currentConversationId = ""; //当前会话ID 首次为空, 二次问题填入
  String currentQuestion = ""; //当前问题

  bool isShowNewSession = false;

  bool isVoiceMode = false; // 是否为语音输入模式
  bool isRecording = false; // 是否正在录音
  bool isCanceled = false; // 是否取消录音
  bool isConvertToText = false; // 是否转为文字
  double startY = 0.0; // 开始触摸的Y坐标
  Timer? _timer; // 录音计时器
  int recordDuration = 0; // 录音时长(秒)
  final _audioRecorder = Record();
  String? _recordingPath;

  // 添加一个状态变量，用于控制"停止响应"按钮的显示
  bool _isResponding = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    queryQuestionDictionary();
    getHistoryRecord();
    // _initRecorder();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    WidgetsBinding.instance.removeObserver(this);
    stopCurrentSession();
    _timer?.cancel();
    _audioRecorder.dispose();
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final Size size = WidgetsBinding.instance.window.physicalSize;
    final double scale = WidgetsBinding.instance.window.devicePixelRatio;
    final insets = WidgetsBinding.instance.window.viewInsets;
    final bool showKeyboard = insets.bottom > 0; // 判断底部insets是否大于0来判断软键盘是否弹出
    if (showKeyboard != isKeyborderShow) {
      setState(() {
        isKeyborderShow = showKeyboard;
        if (isKeyborderShow) {
          // _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
          //   _scrollController.position.extentTotal,
          //   duration: Duration(milliseconds: 200), // 持续时间可以根据需要调整
          //   curve: Curves.easeInOut, // 动画曲线，可以根据需要调整
          // );
          if (sessionList.isNotEmpty) {
            // 延时执行滚动到底部操作
            Future.delayed(const Duration(milliseconds: 100), () {
              _scrollController
                  .jumpTo(_scrollController.position.maxScrollExtent);
            });
          }
        }
      });
      print('Keyboard is now ${isKeyborderShow ? 'visible' : 'hidden'}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: Drawer(
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        width: 248.px,
        elevation: 3,
        child: _buildLeftDrawer(),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      floatingActionButton: isShowNewSession
          ? Padding(
              padding: EdgeInsets.only(bottom: 64.px),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: () {
                  startNewChatMessage();
                },
                child: _buildActionButtons(),
              ),
            )
          : null,
      body: Container(
          alignment: Alignment.center,
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/images/chat/bg_chat.png'),
              fit: BoxFit.cover,
            ),
          ),
          child: Column(children: [
            SizedBox(
              height: 48.px,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 20.px,
                ),
                GestureDetector(
                  onTap: () {
                    _scaffoldKey.currentState?.openDrawer();
                    Future.delayed(const Duration(milliseconds: 100), () {
                      FocusScope.of(context).unfocus();
                    });
                  },
                  behavior: HitTestBehavior.opaque,
                  child: const ImageIcon(
                    AssetImage('assets/images/chat/ic_draw_left.png'),
                    size: 24,
                    color: Colors.black,
                  ),
                ),
                SizedBox(width: 10.px),
                const Text('北大荒Chat',
                    style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.black)),
                const Spacer(),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  behavior: HitTestBehavior.opaque,
                  child: Icon(
                    Icons.close,
                    size: 24.px,
                    color: const Color.fromRGBO(7, 44, 29, 1),
                  ),
                ),
                SizedBox(width: 10.px),
              ],
            ),
            Expanded(
              child: GestureDetector(
                onTap: () {
                  if (isKeyborderShow) {
                    FocusScope.of(context).requestFocus(FocusNode());
                  }
                },
                child: Container(
                  padding: EdgeInsets.only(bottom: 20.px, top: 20.px),
                  child: Column(
                    children: [
                      Container(
                        padding: EdgeInsets.only(
                            left: 12.px, right: 12.px, bottom: 0.px),
                        child: myFieldQuestionDictionaryList.isNotEmpty
                            ? MyFieldChatWeatherAddress(
                                onlyShowAddress: sessionList.isNotEmpty,
                              )
                            : Container(),
                      ),
                      Expanded(
                          child: sessionList.isEmpty
                              ? ListView(
                                  padding: EdgeInsets.only(top: 0.px),
                                  children: [
                                    _buildTipView(),
                                  ],
                                )
                              : Stack(
                                  children: [
                                    ListView.builder(
                                        padding: EdgeInsets.only(bottom: 20.px),
                                        controller: _scrollController,
                                        itemCount: sessionList.length,
                                        itemBuilder: (context, index) =>
                                            _buildSessionItem(
                                                sessionList[index])),
                                    const Positioned(
                                      bottom: -18,
                                      left: 0,
                                      right: 0,
                                      child: Image(
                                        fit: BoxFit.cover,
                                        image: AssetImage(
                                            'assets/images/chat/ic_messge_list_mb.png'),
                                      ),
                                    )
                                  ],
                                )),
                    if(!isShowNewSession) Padding(
              padding: EdgeInsets.only(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    'assets/images/chat/ic_deepseek.png',
                    color: const Color.fromRGBO(0, 0, 0, 0.4),
                    width: 24.px,
                    height: 24.px,
                  ),
                  Text(
                    " Deepseek",
                    style: TextStyle(
                        fontSize: 14.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(0, 0, 0, 0.5)),
                  ),
                ],
              ),
            ),
                      Container(
                        padding: EdgeInsets.only(left: 10.px, right: 10.px),
                        margin: EdgeInsets.only(top: 16.px),
                        constraints: BoxConstraints(minHeight: 44.px),
                        child: isVoiceMode
                          ? _buildVoiceInput()
                          : _buildTextInput(),
                      ),
                    ],
                  ),
                ),
              ),
            )
          ])),
    );
  }

  Widget _buildSessionItem(MyFieldSessionEntity item) {
    return Container(
      margin: EdgeInsets.only(bottom: 20.px, top: 16.px),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(mainAxisAlignment: MainAxisAlignment.end, children: [
            Container(
                alignment: Alignment.topRight,
                margin: EdgeInsets.only(left: 12.px, right: 12.px),
                padding: EdgeInsets.all(12.px),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(30, 192, 106, 1),
                  border: Border.all(
                      color: const Color.fromRGBO(255, 255, 255, 0.2),
                      width: 1.px),
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(20),
                      topRight: Radius.circular(20),
                      bottomLeft: Radius.circular(20),
                      bottomRight: Radius.circular(4)),
                ),
                child: Text(item.question,
                    style: TextStyle(
                        fontSize: 16.px,
                        fontWeight: FontWeight.w600,
                        color: Colors.white))),
          ]),
          SizedBox(height: 16.px),
          Container(
              margin: EdgeInsets.only(left: 12.px, right: 12.px),
              padding: EdgeInsets.all(12.px),
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(
                    color: const Color.fromRGBO(255, 255, 255, 0.2),
                    width: 1.px),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(20),
                    topRight: Radius.circular(20),
                    bottomLeft: Radius.circular(4),
                    bottomRight: Radius.circular(20)),
              ),
              child: item.thinkContent.isEmpty
                  ? SizedBox(
                      width: 20.px,
                      height: 20.px,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation(
                            Color.fromRGBO(16, 46, 83, 1)),
                      ),
                    )
                  // : Text(item.answer,
                  //     textAlign: TextAlign.justify,
                  //     style: TextStyle(
                  //         height: 2.px,
                  //         letterSpacing: -0.5,
                  //         fontSize: 16.px,
                  //         fontWeight: FontWeight.w400,
                  //         color: const Color.fromRGBO(16, 46, 83, 1)))
                  : Column(
                      children: [
                        Text(
                          item.thinkContent,
                          style: TextStyle(
                            color: Colors.grey,
                            fontSize: 14.px,
                          ),
                        ),
                        MarkdownBody(data: item.answer),
                      ],
                    )),
        ],
      ),
    );
  }

  // 进入提示
  Widget _buildTipView() {
    return Column(
      children: [
        myFieldQuestionDictionaryList.isNotEmpty
            ? Container(
                width: 351.px,
                margin:
                    EdgeInsets.only(bottom: 20.px, left: 12.px, right: 12.px),
                padding: EdgeInsets.only(
                    left: 16.px, right: 10.px, top: 20.px, bottom: 16.px),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(255, 255, 255, 0.6),
                  borderRadius: const BorderRadius.all(Radius.circular(20)),
                  border: Border.all(
                      color: const Color.fromRGBO(255, 255, 255, 0.2),
                      width: 1.px),
                ),
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ShaderMask(
                        shaderCallback: (Rect bounds) {
                          return const LinearGradient(
                            colors: [
                              Color.fromRGBO(8, 212, 79, 1),
                              Color.fromRGBO(9, 228, 232, 1)
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ).createShader(Offset.zero & bounds.size);
                        },
                        blendMode: BlendMode.srcATop,
                        child: Text(
                          "Hi，我是北大荒Chat",
                          style: TextStyle(
                            fontSize: 16.px,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                      SizedBox(height: 8.px),
                      Text(
                        "我是北大荒集团训练的农业专家,已接入DeepSeek可以随时回答你的问题~",
                        style: TextStyle(
                            letterSpacing: -0.2,
                            fontSize: 13.px,
                            color: const Color.fromRGBO(111, 126, 152, 1)),
                      ),
                      Column(
                        children: myFieldQuestionDictionaryList.isNotEmpty
                            ? myFieldQuestionDictionaryList
                                .map((item) => _buildQuestionDictionary(item))
                                .toList()
                            : [],
                      )
                    ]))
            : Container(),
        SizedBox(height: 8.px),
      ],
    );
  }

  // build 提示问题字典项
  Widget _buildQuestionDictionary(MyFieldQuestionDictionaryEntity item) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        queryChatMessagesResult(item.question!);
      },
      child: Container(
        padding: EdgeInsets.all(12.px),
        decoration: BoxDecoration(
          color: const Color.fromRGBO(255, 255, 255, 0.9),
          borderRadius: const BorderRadius.all(Radius.circular(12)),
          border: Border.all(
              color: const Color.fromRGBO(255, 255, 255, 1), width: 0.5.px),
        ),
        margin: EdgeInsets.only(top: 16.px),
        child: Row(children: [
          Text(item.question!,
              style: TextStyle(
                fontSize: 13.px,
                fontWeight: FontWeight.w600,
              )),
          const Spacer(),
          BdhNetworkImage(
            url: '${urlConfig.microfront}${item.image}',
            width: 36.px,
            height: 36.px,
          ),
        ]),
      ),
    );
  }

  // build 左侧抽屉
  Widget _buildLeftDrawer() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 36.px),
      width: 248.px,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(width: 14.px),
              Text("历史记录",
                  style: TextStyle(
                      fontSize: 12.px,
                      fontWeight: FontWeight.w600,
                      color: const Color.fromRGBO(51, 51, 51, 0.6))),
              // GestureDetector(
              //   behavior: HitTestBehavior.opaque,
              //   onTap: () {
              //     _scaffoldKey.currentState?.closeDrawer();
              //     startNewChatMessage();
              //   },
              //   child: Container(
              //     decoration: BoxDecoration(
              //       color: const Color.fromRGBO(255, 255, 255, 0.8),
              //       borderRadius: BorderRadius.circular(16),
              //       border: Border.all(
              //         color: const Color.fromRGBO(255, 255, 255, 1),
              //         width: 1,
              //       ),
              //     ),
              //     width: 132.px,
              //     height: 32.px,
              //     child: const Row(
              //         mainAxisAlignment: MainAxisAlignment.center,
              //         crossAxisAlignment: CrossAxisAlignment.center,
              //         children: [
              //           ImageIcon(
              //             AssetImage('assets/images/chat/ic_new_chat.png'),
              //             size: 24,
              //             color: Color.fromRGBO(30, 192, 106, 1),
              //           ),
              //           SizedBox(width: 2),
              //           Text(
              //             "开启新对话",
              //             style: TextStyle(
              //                 fontSize: 14,
              //                 fontWeight: FontWeight.w500,
              //                 color: Color.fromRGBO(30, 192, 106, 1)),
              //           ),
              //         ]),
              //   ),
              // )
            ],
          ),
          SizedBox(height: 4.px),
          Expanded(
            child: myFieldQuestionRecordList.isEmpty
                ? Center(
                    child: Text("暂无历史记录",
                        style: TextStyle(
                            fontSize: 14.px,
                            fontWeight: FontWeight.w600,
                            color: const Color.fromRGBO(51, 51, 51, 0.6))),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    padding: EdgeInsets.only(top: 10.px),
                    itemCount: myFieldQuestionRecordList.length,
                    itemBuilder: (context, index) =>
                        _buildHistoryRecord(myFieldQuestionRecordList[index])),
          ),
          Container(
            padding: EdgeInsets.only(bottom: 16.px),
            alignment: Alignment.center,
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                _scaffoldKey.currentState?.closeDrawer();
                startNewChatMessage();
              },
              child: Container(
                margin: EdgeInsets.only(top: 10.px),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(255, 255, 255, 0.8),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color.fromRGBO(255, 255, 255, 1),
                    width: 1,
                  ),
                ),
                width: 132.px,
                height: 32.px,
                child: const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      ImageIcon(
                        AssetImage('assets/images/chat/ic_new_chat.png'),
                        size: 24,
                        color: Color.fromRGBO(30, 192, 106, 1),
                      ),
                      SizedBox(width: 2),
                      Text(
                        "开启新对话",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color.fromRGBO(30, 192, 106, 1)),
                      ),
                    ]),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildHistoryRecord(MyFieldQuestionRecordEntity item) {
    return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          _scaffoldKey.currentState?.closeDrawer();
          getHistoryMessage(item.id!);
        },
        child: Container(
          alignment: Alignment.centerLeft,
          padding: EdgeInsets.only(left: 7.px, right: 30.px),
          child: Container(
            padding: EdgeInsets.only(
                left: 7.px, right: 7.px, bottom: 12.px, top: 12.px),
            decoration: isCurrentSession(item.id)
                ? BoxDecoration(
                    color: const Color.fromRGBO(30, 192, 106, 0.2),
                    borderRadius: BorderRadius.circular(16),
                  )
                : null,
            child: Text(
              item.name ?? "",
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w600,
                  color: isCurrentSession(item.id)
                      ? const Color.fromRGBO(30, 192, 106, 1)
                      : const Color.fromRGBO(51, 51, 51, 1)),
            ),
          ),
        ));
  }

  bool isCurrentSession(String? conversationId) {
    return currentConversationId == conversationId;
  }

  void startNewChatMessage() {
    setState(() {
      isShowNewSession = false;
      currentConversationId = "";
      _textEditingController.text = "";
      sessionList.clear();
      isInputWord = false;
      isGetResult = false;
    });
    stopCurrentSession();
  }

  bool isGetResult = false;
  void queryChatMessagesResult(String question) {
    _sendMessage(question);
  }

  void getHistoryRecord() {
    ChatRepository.queryHistorySession().then((response) {
      if (response.data["data"] != null) {
        myFieldQuestionRecordList.clear();
        for (var item in response.data["data"]) {
          MyFieldQuestionRecordEntity questionRecordEntity =
              MyFieldQuestionRecordEntity.fromJson(item);
          if (questionRecordEntity.name == "New conversation") {
            questionRecordEntity.name = currentQuestion;
          }
          setState(() {
            myFieldQuestionRecordList.add(questionRecordEntity);
          });
        }
      }
    });
  }

  void getHistoryMessage(String conversationId) {
    stopCurrentSession();
    ChatRepository.queryHistoryMessage(conversationId).then((response) {
      if (response.data["data"] != null) {
        sessionList.clear();
        for (var item in response.data["data"]) {
          MyFieldHistoryMessageEntity myFieldHistoryMessageEntity =
          MyFieldHistoryMessageEntity.fromJson(item);
          setState(() {
            MyFieldSessionEntity myFieldSessionEntity = MyFieldSessionEntity();
            myFieldSessionEntity.question = myFieldHistoryMessageEntity.query!;
            currentConversationId = myFieldHistoryMessageEntity.conversationId!;
            String answer = myFieldHistoryMessageEntity.answer!;
            if (answer.contains("</think>")) {
              myFieldSessionEntity.thinkContent =
                  answer.substring(0, answer.indexOf("</think>"));
              myFieldSessionEntity.answer =
                  answer.substring(answer.indexOf("</think>"));
            }
            if (answer.contains("</details>")) {
              myFieldSessionEntity.thinkContent =
                  answer.substring(0, answer.indexOf("</details>"));
              myFieldSessionEntity.answer =
                  answer.substring(answer.indexOf("</details>"));
            }
            // if (answer.contains("</think>")) {
            //   int startIndex = answer.indexOf("</think>");
            //   if (startIndex != -1) {
            //     answer = answer.substring(startIndex + 8);
            //     if (answer.startsWith("\n\n")) {
            //       answer = answer.replaceFirst("\n\n", "");
            //     }
            //   }
            // }

            // myFieldSessionEntity.answer = answer;
            sessionList.add(myFieldSessionEntity);
          });
        }
        setState(() {
          isShowNewSession = true;
        });
      }
    });
  }

  void queryQuestionDictionary() {
    myFieldQuestionDictionaryList.clear();
    ChatRepository.queryQuestionDictionary().then((response) {
      if (response.data["data"] != null) {
        for (var item in response.data["data"]) {
          MyFieldQuestionDictionaryEntity myFieldQuestionDictionaryEntity =
              MyFieldQuestionDictionaryEntity.fromJson(item);
          setState(() {
            myFieldQuestionDictionaryList.add(myFieldQuestionDictionaryEntity);
          });
        }
      }
    });
  }

  void stopCurrentSession() {
    if (currentTaskId.isNotEmpty) {
      // 停止消息响应
      ChatRepository.stopQuestionMessage(currentTaskId).then((response) {
        print("取消问题任务响应:" + jsonEncode(response.data));
        // setState(() {
        //   _textEditingController.text = "";
        //   sessionList.clear();
        //   isInputWord = false;
        //   isGetResult = false;
        // });
      });
    }
  }

  // 消息发送方法
  Future<void> _sendMessage(String question) async {
    if (question.isEmpty) {
      showToast("请输入要想要问的问题");
      return;
    }
    
    FocusScope.of(context).unfocus();
    final sessionEntity = MyFieldSessionEntity()..question = question;
    
    setState(() {
      currentQuestion = question;
      _textEditingController.clear();
      isInputWord = false;
      sessionList.add(sessionEntity);
      _isResponding = true; // 设置为响应中状态
    });
    
    isGetResult = false;
    _processMessageResponse(sessionEntity, question);
  }
  
  // 处理消息响应
  Future<void> _processMessageResponse(MyFieldSessionEntity sessionEntity, String question) async {
    try {
      final response = await ChatRepository.queryChatMessages(question, currentConversationId);
      sessionEntity.isThinkEnd = false;
      
      response.data.stream.listen(
        (data) => _handleStreamData(data, sessionEntity),
        onDone: () {
          print("done");
          if (mounted) {
            setState(() {
              _isResponding = false; // 响应结束
            });
            _textEditingController.clear();
            getHistoryRecord();
          }
        },
        onError: (error) {
          print("Stream error: $error");
          if (mounted) {
            setState(() {
              _isResponding = false; // 出错时也结束响应状态
            });
            // showToast("接收消息出错，请重试");
          }
        }
      );
    } catch (e) {
      print("消息发送错误: $e");
      showToast("发送消息失败，请重试");
      setState(() {
        _isResponding = false; // 发送出错时结束响应状态
      });
    }
  }
  
  // 处理流数据
  void _handleStreamData(dynamic data, MyFieldSessionEntity sessionEntity) {
    try {
      final dataUint8List = Uint8List.fromList(data);
      final str = String.fromCharCodes(dataUint8List);
      
      if (str.contains("event: ping")) return;
      
      if (str.contains("</think>")) {
        sessionEntity.isThinkEnd = true;
        return;
      }
      
      final jsonData = str.split('data: ')
          .where((element) => element.isNotEmpty)
          .toList();
      
      for (var element in jsonData) {
        final message = MyFieldChatMessageEntity.fromJson(jsonDecode(element));
        
        if (message.event == "message") {
          _handleMessage(message, sessionEntity);
        } else if (message.event == "message_end") {
          print("Chat Message End ..........");
        }
      }
    } catch (e) {
      print("处理数据流错误: $e");
    }
  }
  
  // 处理消息内容
  void _handleMessage(MyFieldChatMessageEntity message, MyFieldSessionEntity sessionEntity) {
    final answer = message.answer;
    currentTaskId = message.taskId!;
    currentConversationId = message.conversationId!;
    
    if (sessionEntity.conversation_id.isEmpty) {
      sessionEntity.conversation_id = currentConversationId;
      setState(() {
        isShowNewSession = true;
      });
      getHistoryRecord();
    }

    print("内容:$answer");
    print("会话ID:$currentConversationId");
    print("任务ID:$currentTaskId");

    if (mounted) {
      setState(() {
        if (!sessionEntity.isThinkEnd) {
          if(answer!.contains("</")){
            return;
          }
          sessionEntity.thinkContent += answer!;
        } else {
          if(answer!.contains("</")){
            return;
          }
          sessionEntity.answer += answer!;
        }

        _scrollToBottom();
      });
    }
  }
  
  // 滚动到底部
  void _scrollToBottom() {
    if (sessionList.isNotEmpty) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  // 更新录音状态
  void updateRecordingState(LongPressMoveUpdateDetails details) {
    if (isRecording) {
      final dy = details.globalPosition.dy - startY;
      setState(() {
        isCanceled = dy < -100; // 上滑超过100像素
        isConvertToText = !isCanceled;
      });
    }
  }

  // 创建输入容器
  Widget _buildInputContainer({required Widget child}) {
    return Container(
      height: 44.px,
      decoration: AppStyles.roundedContainer(),
      child: child,
    );
  }

  // 录音按钮
  Widget _buildVoiceInput() {
    return _buildInputContainer(
      child: GestureDetector(
        onLongPressStart: (details) {
          startY = details.globalPosition.dy;
          startRecording();
        },
        onLongPressEnd: (details) {
          stopRecording();
        },
        onLongPressMoveUpdate: updateRecordingState,
        child: Row(
          children: [
            _buildModeToggleButton(),
            Expanded(
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    if (isRecording) ...[
                      Container(
                        width: 8.px,
                        height: 8.px,
                        margin: EdgeInsets.only(right: 8.px),
                        decoration: BoxDecoration(
                          color: AppColors.red,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ],
                    Text(
                      isRecording
                        ? (isCanceled
                            ? '松开手指，取消发送'
                            : '松开转为文字发送 ${recordDuration}s')
                        : '按住说话',
                      style: TextStyle(
                        fontSize: 16.px,
                        color: isRecording && isCanceled
                          ? AppColors.red
                          : AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(width: 44.px),
          ],
        ),
      ),
    );
  }

  // 文本输入
  Widget _buildTextInput() {
    return _buildInputContainer(
      child: Row(
        children: [
          _buildModeToggleButton(),
          Expanded(
            child: TextField(
              controller: _textEditingController,
              maxLines: 1,
              style: AppStyles.messageText(),
              decoration: InputDecoration(
                contentPadding: EdgeInsets.zero,
                isDense: true,
                hintText: '有什么问题尽管问我',
                hintStyle: AppStyles.messageText(
                  color: AppColors.textSecondary,
                  fontWeight: FontWeight.w400,
                ),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                filled: false,
              ),
              onChanged: (value) {
                setState(() {
                  isInputWord = value.isNotEmpty;
                });
              },
              onSubmitted: (value) {
                if (value.isEmpty) {
                  showToast("请输入要想要问的问题");
                  return;
                }
                _sendMessage(value);
              },
            ),
          ),
          _buildSendButton(),
        ],
      ),
    );
  }

  // 模式切换按钮
  Widget _buildModeToggleButton() {
    return GestureDetector(
      onTap: () {
        setState(() {
          isVoiceMode = !isVoiceMode;
          if(isVoiceMode){
            PermissionUtil.requestMicroPhonePermission(context, "以便您使用语音进行交互")
                .then((res) {
                if (res) {
                  _initRecorder();
                }

            });
          }

        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.px),
        child: Transform.scale(
          scale: 0.65,
          child: Image.asset(
            isVoiceMode
              ? 'assets/images/chat/ic_keyboard.png'
              : 'assets/images/chat/ic_mic.png',
          ),
        ),
      ),
    );
  }

  // 优化发送按钮
  Widget _buildSendButton() {
    return GestureDetector(
      onTap: isInputWord
        ? () {
            _sendMessage(_textEditingController.text);
            FocusScope.of(context).unfocus();
          }
        : null,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.px),
        child: ImageIcon(
          AssetImage(isInputWord
              ? 'assets/images/chat/ic_send_file_f.png'
              : 'assets/images/chat/ic_send_file.png'),
          color: isInputWord
              ? AppColors.green
              : AppColors.black,
          size: 28.px,
        ),
      ),
    );
  }

  Future<void> _initRecorder() async {
    final status = await Permission.microphone.request();
    if (status != PermissionStatus.granted) {
      showToast('需要麦克风权限才能使用语音功能');
    }
  }

  // 开始录音
  Future<void> startRecording() async {
    try {
      if (!await checkAndRequestPermission()) {
        return;
      }

      final dir = await getTemporaryDirectory();
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      // _recordingPath = '/$timestamp.wav';
      _recordingPath = '${dir.path}/$timestamp.m4a';

      await _audioRecorder.start(
        path: _recordingPath,
        encoder: AudioEncoder.aacLc,
        bitRate: 128000,
        samplingRate: 44100,
      );

      setState(() {
        isRecording = true;
        recordDuration = 0;
        isCanceled = false;
        isConvertToText = true; // 默认状态为转为文字
      });

      // 开始计时
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        setState(() {
          recordDuration++;
          if (recordDuration >= 60) {
            stopRecording();
          }
        });
      });
    } catch (e) {
      showToast('录音失败，请检查麦克风权限');
    }
  }

  // 停止录音
  Future<void> stopRecording() async {
    _timer?.cancel();

    try {
      if (isRecording) {
        final path = await _audioRecorder.stop();

        setState(() {
          isRecording = false;
        });

        if (!isCanceled && path != null) {
          if (isConvertToText) {
            // 调用语音转文字API
            final audioFile = File(path);
            if (await audioFile.exists()) {
              final fileSize = await audioFile.length();
              print('音频文件大小: ${fileSize}字节');
              // 如果文件太小，可能是无效文件
              if (fileSize < 5000) {
                showToast('录音文件太小，请重试');
                return;
              }
              try {
                final response = await ChatRepository.getVoiceOcr(audioFile: audioFile);
                if (response.data != null && response.data['data'] != null) {
                  String text = response.data['data'];
                  setState(() {
                    _textEditingController.text = text;
                    isVoiceMode = false;
                  });
                  // 自动触发发送
                  if (text.isNotEmpty) {
                    queryChatMessagesResult(text);
                  }
                }
              } catch (e) {
                print('Error converting voice to text: $e');
                showToast('语音转换失败，请重试');
              }
            }
          } else {
            // TODO: 发送语音消息的逻辑
          }
        }
      }
    } catch (e) {
      print('Error stopping recording: $e');
      showToast('录音停止失败');
    }
  }

  // 检查和请求麦克风权限
  Future<bool> checkAndRequestPermission() async {
    // 检查麦克风权限
    var status = await Permission.microphone.status;

    if (status.isDenied) {
      // 首次请求权限
      status = await Permission.microphone.request();
      if (status.isDenied) {
        // 用户拒绝了权限
        showPermissionDialog();
        return false;
      }
    }

    if (status.isPermanentlyDenied) {
      // 用户永久拒绝了权限
      showPermissionDialog();
      return false;
    }

    return status.isGranted;
  }

  // 显示权限对话框
  void showPermissionDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('需要麦克风权限'),
        content: const Text('为了使用语音功能，我们需要访问您的麦克风'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
            },
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings();
            },
            child: const Text('去设置'),
          ),
        ],
      ),
    );
  }

  // 添加一个方法用于停止响应
  void _stopResponse() async {
    if (currentTaskId.isEmpty) {
      setState(() {
        _isResponding = false;
      });
      return;
    }

    try {
      // 调用停止API
      final response = await ChatRepository.stopQuestionMessage(currentTaskId);
      print("停止响应结果: $response");
      
      setState(() {
        _isResponding = false;
      });
      
      // 可选：在停止后添加提示信息到当前会话
      if (sessionList.isNotEmpty) {
        setState(() {
          var lastSession = sessionList.last;
          if (lastSession.answer.isEmpty) {
            lastSession.answer = "[回答已停止]";
          } else {
            lastSession.answer += "\n\n[回答已停止]";
          }
        });
      }
    } catch (e) {
      print("停止响应出错: $e");
      showToast("停止响应失败，请重试");
      setState(() {
        _isResponding = false;
      });
    }
  }

  // 修改按钮部分
  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          decoration: BoxDecoration(
            color: const Color.fromRGBO(255, 255, 255, 0.8),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color.fromRGBO(255, 255, 255, 1),
              width: 1,
            ),
          ),
          width: 132.px,
          height: 32.px,
          child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                ImageIcon(
                  AssetImage('assets/images/chat/ic_new_chat.png'),
                  size: 24,
                  color: Color.fromRGBO(75, 98, 130, 1),
                ),
                SizedBox(width: 5),
                Text(
                  "开启新对话",
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color.fromRGBO(111, 126, 152, 1)),
                ),
              ]),
        ),
        
        // 添加停止响应按钮，仅在响应中显示
        if (_isResponding)
          Padding(
            padding: EdgeInsets.only(left: 10.px),
            child: InkWell(
              onTap: _stopResponse,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(245, 108, 108, 0.8),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color.fromRGBO(255, 255, 255, 1),
                    width: 1,
                  ),
                ),
                width: 120.px,
                height: 32.px,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.stop_circle_outlined,
                      size: 20,
                      color: Colors.white,
                    ),
                    SizedBox(width: 5),
                    Text(
                      "停止响应",
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }

  // 在发送消息的地方设置_isResponding为true
  void _handleSendMessage() {
    // 现有的消息发送逻辑
    // ...
    
    // 设置为响应中状态
    setState(() {
      _isResponding = true;
    });
    
    // 当收到完整响应后设置_isResponding为false
    // 可能需要在接收响应的回调中执行：
    // setState(() {
    //   _isResponding = false;
    // });
  }
}
