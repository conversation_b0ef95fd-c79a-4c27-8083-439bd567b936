import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/subsidy/deepen_operation/deepen_operation_view.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/subsidy/free_sow/free_sow_view.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/subsidy/purchase/purchase_view.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../service/information/information_view.dart';
import '../service/maintenance/maintenance_view.dart';
import '../service/refuel/refuel_view.dart';
import 'agricultural_menu_state.dart';

class AgriculturalMenuLogic extends GetxController {
  final AgriculturalMenuState state = AgriculturalMenuState();

  @override
  void onReady() {
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void onMenuItemTap(BuildContext context, String menuName) {
    if (menuName == '维修保养') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const MaintenancePage(),
        ),
      );
    } else if (menuName == '农机加油') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const RefuelPage(),
        ),
      );
    } else if (menuName == '农机资讯') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const InformationPage(),
        ),
      );
    } else if (menuName == '购置补贴') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const PurchasePage(),
        ),
      );
    } else if (menuName == '深耕作业补贴') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const DeepenOperationPage(),
        ),
      );
    } else if (menuName == '免费播种补贴') {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const FreeSowPage(),
        ),
      );
    }
  }
}
