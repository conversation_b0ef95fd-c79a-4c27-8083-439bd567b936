import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'agricultural_menu_logic.dart';
import 'agricultural_menu_state.dart';

class AgriculturalMenuPage extends StatefulWidget {
  const AgriculturalMenuPage({Key? key}) : super(key: key);

  @override
  State<AgriculturalMenuPage> createState() => _AgriculturalMenuPageState();
}

class _AgriculturalMenuPageState extends State<AgriculturalMenuPage> {
  final AgriculturalMenuLogic logic = Get.put(AgriculturalMenuLogic());
  final AgriculturalMenuState state = Get.find<AgriculturalMenuLogic>().state;

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.only(top: 0.px, bottom: 0.px),
        itemCount: state.menuItems.length,
        itemBuilder: (ctx, idx) {
          return buildMenuBlockItemWidget(dataItem: state.menuItems[idx]);
        });
  }

  @override
  void dispose() {
    Get.delete<AgriculturalMenuLogic>();
    super.dispose();
  }

  Widget buildMenuBlockItemWidget({Map<String, dynamic>? dataItem}) {
    return Container(
        margin: EdgeInsets.only(left: 12.px, right: 12.px, top: 10.px),
        padding: EdgeInsets.only(
            left: 12.px, right: 12.px, top: 16.px, bottom: 16.px),
        decoration: BoxDecoration(
            color: const Color.fromRGBO(255, 255, 255, 1),
            borderRadius: BorderRadius.all(Radius.circular(8.px))),
        child: Column(children: [
          Row(children: [
            Text(
              dataItem!['title'] ?? '',
              style: TextStyle(
                  fontSize: 16.px,
                  fontWeight: FontWeight.w600,
                  color: const Color.fromRGBO(41, 41, 52, 1)),
            ),
          ]),
          GridView.builder(
            padding: EdgeInsets.only(top: 0.px),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            scrollDirection: Axis.vertical,
            itemCount: dataItem['items'].length,
            itemBuilder: (_, int index) => buildMenuItem(
              dataItem['items'][index],
            ),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 110 / 128,
            ),
          )
        ]));
  }

  buildMenuItem(dataItem) {
    return InkWell(
      onTap: () {
        logic.onMenuItemTap(context, dataItem['name']);
      },
      child: Container(
          margin: EdgeInsets.only(top: 16.px, right: 16.px),
          child: Column(children: [
            Image.asset(dataItem['icon'], width: 36.px, height: 36.px),
            Text(
              dataItem['name'] ?? '',
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 13.px,
                  fontWeight: FontWeight.w400,
                  color: const Color.fromRGBO(51, 51, 51, 1)),
            )
          ])),
    );
  }
}
