import 'package:bdh_smart_agric_app/generated/assets.dart';

class AgriculturalMenuState {
  List<Map<String, dynamic>> menuItems = [
    {
      'title': '农机4S店',
      'items': [
        {'name': '维修保养', 'icon': Assets.agriculturalBy},
        {'name': '农机加油', 'icon': Assets.agriculturalJy},
        {'name': '农机资讯', 'icon': Assets.agriculturalZx}
      ]
    },
    {
      'title': '农机补贴',
      'items': [
        {'name': '购置补贴', 'icon': Assets.agriculturalGz},
        {'name': '深耕作业补贴', 'icon': Assets.agriculturalSgzy},
        {'name': '免费播种补贴', 'icon': Assets.agriculturalMfbz}
      ],
    }
  ];

  AgriculturalMenuState() {
    ///Initialize variables
  }
}
