import 'dart:convert';
import 'dart:io';

import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/district_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/poi_entity.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';
import 'package:oktoast/oktoast.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../../../components/bdh_network_image.dart';
import '../../../../../components/bdh_no_data.dart';
import '../../../../../components/jh_cascade_tree_picker.dart';
import '../../../../../generated/assets.dart';
import '../../../../../utils/event_bus.dart';
import '../../../../../utils/tile_util.dart';
import 'maintenance_logic.dart';
import 'maintenance_state.dart';

/// 维修保养
class MaintenancePage extends StatefulWidget {
  const MaintenancePage({Key? key}) : super(key: key);

  @override
  State<MaintenancePage> createState() => _MaintenancePageState();
}

class _MaintenancePageState extends State<MaintenancePage>
    with WidgetsBindingObserver {
  final MaintenanceLogic logic = Get.put(MaintenanceLogic());
  final MaintenanceState state = Get.find<MaintenanceLogic>().state;
  final _focusNode = FocusNode();
  TextEditingController textController = TextEditingController();
  var flags = InteractiveFlag.all - InteractiveFlag.rotate;

  @override
  void initState() {
    super.initState();
    logic.initContext(context);
    WidgetsBinding.instance.addObserver(this);
    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        print('获得焦点');
      } else {
        print('失去焦点');
      }
    });

    bus.on("toRefreshPOI", (arg) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void didChangeMetrics() {
    final viewInsets = View.of(context).viewInsets;
    if (viewInsets.bottom > 0) {
      print('键盘弹出');
      state.isBottomPanelShow.value = false;
      state.isPOIDetailPanelShow.value = false;
    } else {
      print('键盘收起');
      state.isBottomPanelShow.value = true;
    }
  }

  // void onMapCreated(AMapController controller) {
  //   state.mapController = controller;
  // }

  @override
  Widget build(BuildContext context) {
    // AMapWidget map = AMapWidget(
    //   privacyStatement: ConstConfig.amapPrivacyStatement,
    //   initialCameraPosition: CameraPosition(
    //     target: LatLng(state.defaultLatitude, state.defaultLongitude),
    //     zoom: 10,
    //   ),
    //   markers: Set<Marker>.of(state.poiMarkers.values),
    //   apiKey: ConstConfig.amapApiKeys,
    //   onMapCreated: onMapCreated,
    // );

    return LayoutBuilder(builder: (BuildContext context, BoxConstraints cons) {
      return Obx(() {
        return Scaffold(
            backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
            body: SizedBox(
                width: cons.maxWidth,
                height: cons.maxHeight,
                child: Stack(alignment: Alignment.topCenter, children: [
                  Positioned(
                      child: SizedBox(
                          width: cons.maxWidth,
                          height: cons.maxHeight,
                          child: FlutterMap(
                              mapController: state.mapController,
                              options: MapOptions(
                                  initialCenter: LatLng(state.defaultLatitude,
                                      state.defaultLongitude),
                                  initialZoom: 10,
                                  interactionOptions:
                                      InteractionOptions(flags: flags),
                                  onMapEvent: (e) {},
                                  onMapReady: () {}),
                              children: [
                                TileLayerUtil.tileLayer(TianDiTuType.vec),
                                TileLayerUtil.tileLayer(TianDiTuType.cia),
                                MarkerLayer(markers: state.locationMarkers),
                                MarkerLayer(markers: state.poiMarkers),
                                // MapLocationButton(
                                //     xAlign: 0.9,
                                //     yAlign: 0.55,
                                //     autoLocate: true,
                                //     markerCallback: (marker) {
                                //       setState(() {
                                //         state.locationMarkers = [marker];
                                //       });
                                //     }),
                                // Align(
                                //   alignment: const Alignment(-0.9, -0.95),
                                //   child: Container(
                                //     height: 30.px,
                                //     width: 187.px,
                                //     color: const Color.fromRGBO(0, 0, 0, 0.4),
                                //     padding: EdgeInsets.only(left: 10.px),
                                //     alignment: Alignment.centerLeft,
                                //     child: SelectableText(
                                //       "${currentLatLng?.longitude.toStringAsFixed(6)},${currentLatLng?.latitude.toStringAsFixed(6)}",
                                //       style: const TextStyle(color: Colors.white),
                                //     ),
                                //   ),
                                // ),
                              ]))),
                  Positioned(
                    child: Container(
                      height: 158.px,
                      width: 375.px,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        fit: BoxFit.cover,
                        image: AssetImage(Assets.agriculturalSubTopBg),
                      )),
                    ),
                  ),
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 36.px, left: 8.px),
                            child: BackButton(
                              color: Colors.white,
                              onPressed: () {
                                Navigator.pop(context);
                              },
                            ),
                          )
                        ]),
                  ),
                  Positioned(
                    top: 10.px,
                    child: Container(
                      margin: EdgeInsets.only(top: 36.px, left: 8.px),
                      child: Text("维修保养",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 18.px,
                              fontWeight: FontWeight.w600)),
                    ),
                  ),
                  Positioned(
                    right: 6.px,
                    top: 176.px,
                    child: IconButton(
                      onPressed: () {
                        logic.toMineLocation();
                      },
                      icon: Image.asset(Assets.agriculturalIcLocation,
                          width: 44.px, height: 44.px),
                      iconSize: 44.px,
                    ),
                  ),
                  Positioned(
                    top: 85.px,
                    child: Container(
                        width: 341.px,
                        height: (state.isDistanceSelected.value ||
                                state.isSortSelected.value)
                            ? state.isSortSelected.value
                                ? 123.px
                                : 234.px
                            : 86.px,
                        margin: EdgeInsets.only(right: 12.px, left: 12.px),
                        padding: EdgeInsets.only(top: 8.px, bottom: 0.px),
                        decoration: BoxDecoration(
                            color: const Color.fromRGBO(255, 255, 255, 1),
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.px))),
                        child: Column(children: [
                          Container(
                            padding: EdgeInsets.only(left: 16.px, right: 16.px),
                            child: Column(
                              children: [
                                Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Image.asset(Assets.agriculturalIcAddress,
                                          width: 18.px, height: 18.px),
                                      SizedBox(
                                        height: 36.px,
                                        child: TextButton.icon(
                                          iconAlignment: IconAlignment.end,
                                          onPressed: () {
                                            showDistrictPicker(context);
                                          },
                                          label: Text(state.currentCity.value,
                                              style: TextStyle(
                                                  fontSize: 14.px,
                                                  fontWeight: FontWeight.w500,
                                                  color: const Color.fromRGBO(
                                                      44, 44, 52, 1))),
                                          icon: Image.asset(
                                              Assets.agriculturalIcArrowDown,
                                              width: 10.px,
                                              height: 10.px),
                                        ),
                                      ),
                                      Container(
                                          margin: EdgeInsets.only(right: 8.px),
                                          width: 1.px,
                                          height: 24.px,
                                          color: const Color.fromRGBO(
                                              217, 217, 217, 1)),
                                      Expanded(
                                          child: TextField(
                                              controller: textController,
                                              focusNode: _focusNode,
                                              textInputAction:
                                                  TextInputAction.search,
                                              onSubmitted: (value) {
                                                state.searchWord = value;
                                                logic.getResultList();
                                              },
                                              decoration: InputDecoration(
                                                isDense: true,
                                                contentPadding: EdgeInsets.only(
                                                    top: 0.px, bottom: 0.px),
                                                hintText: "请输入站点名称",
                                                hintStyle: TextStyle(
                                                  fontSize: 14.px,
                                                  fontWeight: FontWeight.w400,
                                                  color: const Color.fromRGBO(
                                                      44, 44, 52, 0.6),
                                                ),
                                                suffixIconConstraints:
                                                    BoxConstraints(
                                                  maxWidth: 22.px,
                                                  maxHeight: 22.px,
                                                ),
                                                suffixIcon: InkWell(
                                                  onTap: () {
                                                    state.searchWord =
                                                        textController.text;
                                                    logic.getResultList();
                                                  },
                                                  child: Image.asset(
                                                    Assets
                                                        .agriculturalIconSearch,
                                                  ),
                                                ),
                                                border: InputBorder.none,
                                              )))
                                    ]),
                                Divider(
                                    height: 1.px,
                                    thickness: 1.px,
                                    color: const Color.fromRGBO(
                                        217, 217, 217, 0.8)),
                                Container(
                                  margin:
                                      EdgeInsets.symmetric(horizontal: 12.px),
                                  child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceEvenly,
                                      children: [
                                        SizedBox(
                                          width: 90.px,
                                          height: 36.px,
                                          child: TextButton.icon(
                                            iconAlignment: IconAlignment.end,
                                            onPressed: () {
                                              state.isDistanceSelected.value =
                                                  !state
                                                      .isDistanceSelected.value;
                                              state.isSortSelected.value =
                                                  false;
                                            },
                                            style: ButtonStyle(
                                              padding:
                                                  MaterialStateProperty.all(
                                                      EdgeInsets.symmetric(
                                                          horizontal: 4.px,
                                                          vertical: 2.px)),
                                              shape: MaterialStateProperty.all(
                                                  RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              4.px))),
                                            ),
                                            label: Text(
                                                state
                                                    .distanceLocationType.value,
                                                style: TextStyle(
                                                    fontSize: 14.px,
                                                    fontWeight: FontWeight.w500,
                                                    color: state
                                                            .isDistanceSelected
                                                            .value
                                                        ? state.selectedColor
                                                        : state.normalColor)),
                                            icon: Image.asset(
                                                state.isDistanceSelected.value
                                                    ? Assets
                                                        .agriculturalIcArrowUp
                                                    : Assets
                                                        .agriculturalIcArrowDown,
                                                color: state.isDistanceSelected
                                                        .value
                                                    ? state.selectedColor
                                                    : state.normalIconColor,
                                                width: 10.px,
                                                height: 10.px),
                                          ),
                                        ),
                                        SizedBox(
                                          width: 90.px,
                                          height: 36.px,
                                          child: TextButton.icon(
                                            iconAlignment: IconAlignment.end,
                                            onPressed: () {
                                              state.isSortSelected.value =
                                                  !state.isSortSelected.value;
                                              state.isDistanceSelected.value =
                                                  false;
                                            },
                                            style: ButtonStyle(
                                              padding:
                                                  MaterialStateProperty.all(
                                                      EdgeInsets.symmetric(
                                                          horizontal: 4.px,
                                                          vertical: 2.px)),
                                              shape: MaterialStateProperty.all(
                                                  RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              4.px))),
                                            ),
                                            label: Text("距离优先",
                                                style: TextStyle(
                                                    fontSize: 14.px,
                                                    fontWeight: FontWeight.w500,
                                                    color: state.isSortSelected
                                                            .value
                                                        ? state.selectedColor
                                                        : state.normalColor)),
                                            icon: Image.asset(
                                                state.isSortSelected.value
                                                    ? Assets
                                                        .agriculturalIcArrowUp
                                                    : Assets
                                                        .agriculturalIcArrowDown,
                                                color:
                                                    state.isSortSelected.value
                                                        ? state.selectedColor
                                                        : state.normalIconColor,
                                                width: 10.px,
                                                height: 10.px),
                                          ),
                                        ),
                                      ]),
                                ),
                              ],
                            ),
                          ),
                          Visibility(
                            visible: state.isDistanceSelected.value ||
                                state.isSortSelected.value,
                            child: Column(children: [
                              SizedBox(height: 2.px),
                              Divider(
                                  height: 1.px,
                                  thickness: 1.px,
                                  color:
                                      const Color.fromRGBO(217, 217, 217, 0.8)),
                              Visibility(
                                  visible: state.isSortSelected.value,
                                  child: SizedBox(
                                      width: 375.px,
                                      height: 37.px,
                                      child: buildSortPanel(context))),
                              Visibility(
                                  visible: state.isDistanceSelected.value,
                                  child: SizedBox(
                                      width: 375.px,
                                      height: 148.px,
                                      child: buildDistancePanel(context))),
                            ]),
                          )
                        ])),
                  ),

                  /// poi列表
                  Visibility(
                    visible: state.isBottomPanelShow.value,
                    child: Positioned(
                      bottom: 0.px,
                      child: SizedBox(
                        width: 375.px,
                        height: state.bottomPanelHeight.value.px,
                        child: Stack(children: [
                          Positioned(
                            child: Container(
                              width: 375.px,
                              height: 64.px,
                              decoration: const BoxDecoration(
                                  image: DecorationImage(
                                      image: AssetImage(
                                          Assets.agriculturalIcBottomHeaderBg),
                                      fit: BoxFit.fill)),
                              child: Container(
                                alignment: Alignment.topCenter,
                                height: 18.px,
                                margin: EdgeInsets.only(top: 14.px),
                                child: Row(children: [
                                  SizedBox(width: 45.px),
                                  Text("已按当前定位为您推荐",
                                      style: TextStyle(
                                          fontSize: 13.px,
                                          fontWeight: FontWeight.w600,
                                          color: Colors.white)),
                                  const Spacer(),
                                  SizedBox(
                                    height: 18.px,
                                    child: TextButton.icon(
                                      iconAlignment: IconAlignment.end,
                                      style: ButtonStyle(
                                        padding: MaterialStateProperty.all(
                                            EdgeInsets.symmetric(
                                                horizontal: 4.px)),
                                      ),
                                      icon: Image.asset(
                                          state.isExpand.value
                                              ? Assets.agriculturalIcArrowDown
                                              : Assets.agriculturalIcArrowUp,
                                          width: 10.px,
                                          height: 10.px,
                                          color: Colors.white.withOpacity(0.8)),
                                      label: Text(
                                        state.isExpand.value ? "收起" : "展开",
                                        style: TextStyle(
                                            color:
                                                Colors.white.withOpacity(0.8)),
                                      ),
                                      onPressed: () {
                                        logic.onClickExpand(context);
                                      },
                                    ),
                                  ),
                                  SizedBox(width: 16.px),
                                ]),
                              ),
                            ),
                          ),
                          Container(
                              margin: EdgeInsets.only(top: 40.px),
                              padding: EdgeInsets.only(top: 16.px),
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(12.px),
                                      topRight: Radius.circular(12.px))),
                              child: state.poiList.isEmpty
                                  ? const Center(
                                      child: BdhNoData(desc: "暂无数据"),
                                    )
                                  : ListView.builder(
                                      padding: EdgeInsets.only(top: 0.px),
                                      itemCount: state.poiList.length,
                                      itemBuilder: (context, index) {
                                        return Container(
                                            child: buildPOIItem(
                                                context,
                                                state.poiList
                                                    .elementAt(index)));
                                      }))
                        ]),
                      ),
                    ),
                  ),

                  /// 详情面板
                  Visibility(
                    visible: state.isPOIDetailPanelShow.value,
                    child: Positioned(
                      bottom: 0.px,
                      child: Container(
                        width: 375.px,
                        height: 170.px,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(12.px),
                              topRight: Radius.circular(12.px),
                            )),
                        child: Container(
                            padding: EdgeInsets.only(
                                left: 16.px,
                                right: 16.px,
                                top: 16.px,
                                bottom: 8.px),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(12.px),
                                topRight: Radius.circular(12.px),
                              ),
                              gradient: const LinearGradient(
                                begin: Alignment.topCenter,
                                end: Alignment.bottomCenter,
                                stops: [0.0, 0.35],
                                colors: [
                                  Color.fromRGBO(47, 217, 160, 0.2),
                                  Color.fromRGBO(47, 217, 160, 0)
                                ],
                              ),
                            ),
                            child: Column(
                              children: [
                                Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      ClipRRect(
                                        borderRadius: BorderRadius.all(
                                            Radius.circular(8.px)),
                                        child: BdhNetworkImage(
                                          url:
                                              '${state.poiDetail.value.picAddr}',
                                          fit: BoxFit.cover,
                                          width: 90.px,
                                          height: 90.px,
                                          errorImage:
                                              Assets.agriculturalIcStation,
                                        ),
                                      ),
                                      SizedBox(width: 12.px),
                                      Expanded(
                                        child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                state.poiDetail.value
                                                        .maregasName ??
                                                    '',
                                                style: TextStyle(
                                                    fontSize: 15.px,
                                                    fontWeight: FontWeight.w500,
                                                    color: state.normalColor),
                                              ),
                                              SizedBox(height: 4.px),
                                              Container(
                                                height: 20.px,
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 4.px,
                                                    vertical: 1.5.px),
                                                decoration: BoxDecoration(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            4.px),
                                                    color: state.selectedColor
                                                        .withOpacity(0.1)),
                                                child: Text(
                                                  "距您${logic.getDistance(state.poiDetail.value.distance ?? 0)}km",
                                                  style: TextStyle(
                                                      fontSize: 12.px,
                                                      fontWeight:
                                                          FontWeight.w500,
                                                      color:
                                                          state.selectedColor),
                                                ),
                                              ),
                                              SizedBox(height: 8.px),
                                              Text(
                                                state.poiDetail.value
                                                        .maregasAddress ??
                                                    "",
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                                style: TextStyle(
                                                    fontSize: 12.px,
                                                    fontWeight: FontWeight.w400,
                                                    color: state.normalColor
                                                        .withOpacity(0.8)),
                                              ),
                                            ]),
                                      )
                                    ]),
                                SizedBox(
                                  height: 14.px,
                                ),
                                Row(
                                  children: [
                                    SizedBox(
                                      width: 82.px,
                                      height: 40.px,
                                      child: TextButton.icon(
                                          onPressed: () {
                                            showPhoneBottomSheet(
                                                context, state.poiDetail.value);
                                          },
                                          icon: Image.asset(
                                              Assets.agriculturalIcPhone,
                                              width: 20.px,
                                              height: 20.px),
                                          style: ButtonStyle(
                                            padding: MaterialStateProperty.all(
                                                EdgeInsets.symmetric(
                                                    horizontal: 4.px,
                                                    vertical: 2.px)),
                                          ),
                                          label: Text(
                                            "电话",
                                            style: TextStyle(
                                                color: state.selectedColor,
                                                fontWeight: FontWeight.w400,
                                                fontSize: 14.px),
                                          )),
                                    ),
                                    SizedBox(width: 8.px),
                                    SizedBox(
                                      width: 253.px,
                                      height: 40.px,
                                      child: TextButton.icon(
                                          onPressed: () {
                                            showNavigationBottomSheet(
                                                context, state.poiDetail.value);
                                          },
                                          icon: Image.asset(
                                              Assets.agriculturalIcNavigate,
                                              width: 20.px,
                                              height: 20.px),
                                          style: ButtonStyle(
                                            backgroundColor:
                                                MaterialStateProperty.all(
                                                    state.selectedColor),
                                            padding: MaterialStateProperty.all(
                                                EdgeInsets.symmetric(
                                                    horizontal: 4.px,
                                                    vertical: 2.px)),
                                            shape: MaterialStateProperty.all(
                                                RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6.px),
                                                    side: BorderSide(
                                                        color: state
                                                            .selectedColor
                                                            .withOpacity(0.6),
                                                        width: 1.px))),
                                          ),
                                          label: Text(
                                            "导航",
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.w500,
                                                fontSize: 16.px),
                                          )),
                                    ),
                                  ],
                                )
                              ],
                            )),
                      ),
                    ),
                  ),
                ])));
      });
    });
  }

  /// 搜索结果列表项
  Widget buildPOIItem(BuildContext context, POIEntity item) {
    return InkWell(
      onTap: () {
        logic.showPOIDetail(item);
      },
      child: Column(
        children: [
          Container(
              padding: EdgeInsets.only(
                left: 16.px,
                right: 16.px,
                bottom: 16.px,
              ),
              child: Row(children: [
                ClipRRect(
                  borderRadius: BorderRadius.all(Radius.circular(8.px)),
                  child: BdhNetworkImage(
                    url: '${item.picAddr}',
                    fit: BoxFit.cover,
                    width: 82.px,
                    height: 82.px,
                    errorImage: Assets.agriculturalIcStation,
                  ),
                ),
                SizedBox(width: 12.px),
                Expanded(
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          item.maregasName ?? "",
                          style: TextStyle(
                              fontSize: 15.px,
                              fontWeight: FontWeight.w500,
                              color: state.normalColor),
                        ),
                        SizedBox(height: 4.px),
                        Text(
                          item.maregasAddress ?? "",
                          style: TextStyle(
                              fontSize: 12.px,
                              fontWeight: FontWeight.w400,
                              color: state.normalColor.withOpacity(0.8)),
                        ),
                        SizedBox(height: 10.px),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              "距您${logic.getDistance(item.distance ?? 0)}km",
                              style: TextStyle(
                                  fontSize: 12.px,
                                  fontWeight: FontWeight.w400,
                                  color: state.normalColor.withOpacity(0.6)),
                            ),
                            Row(
                              children: [
                                SizedBox(
                                  width: 75.px,
                                  height: 28.px,
                                  child: TextButton.icon(
                                      onPressed: () {
                                        logic.showPOIDetail(item);
                                        showPhoneBottomSheet(context, item);
                                      },
                                      icon: Image.asset(
                                          Assets.agriculturalIcPhone,
                                          width: 13.px,
                                          height: 13.px),
                                      style: ButtonStyle(
                                        padding: MaterialStateProperty.all(
                                            EdgeInsets.symmetric(
                                                horizontal: 4.px,
                                                vertical: 2.px)),
                                        shape: MaterialStateProperty.all(
                                            RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(6.px),
                                                side: BorderSide(
                                                    color: state.selectedColor
                                                        .withOpacity(0.6),
                                                    width: 1.px))),
                                      ),
                                      label: Text(
                                        "电话",
                                        style: TextStyle(
                                            color: state.selectedColor,
                                            fontWeight: FontWeight.w500,
                                            fontSize: 12.px),
                                      )),
                                ),
                                SizedBox(width: 8.px),
                                SizedBox(
                                  width: 75.px,
                                  height: 28.px,
                                  child: TextButton.icon(
                                      onPressed: () {
                                        logic.showPOIDetail(item);
                                        showNavigationBottomSheet(
                                            context, item);
                                      },
                                      icon: Image.asset(
                                          Assets.agriculturalIcNavigate,
                                          width: 13.px,
                                          height: 13.px),
                                      style: ButtonStyle(
                                        backgroundColor:
                                            MaterialStateProperty.all(
                                                state.selectedColor),
                                        padding: MaterialStateProperty.all(
                                            EdgeInsets.symmetric(
                                                horizontal: 4.px,
                                                vertical: 2.px)),
                                        shape: MaterialStateProperty.all(
                                            RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(6.px),
                                                side: BorderSide(
                                                    color: state.selectedColor
                                                        .withOpacity(0.6),
                                                    width: 1.px))),
                                      ),
                                      label: Text(
                                        "导航",
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w500,
                                            fontSize: 12.px),
                                      )),
                                ),
                              ],
                            ),
                          ],
                        )
                      ]),
                )
              ])),
          Divider(
              height: 1.px,
              endIndent: 16.px,
              indent: 110.px,
              color: const Color.fromRGBO(217, 217, 217, 0.8)),
          SizedBox(
            height: 16.px,
          )
        ],
      ),
    );
  }

  Widget buildDistancePanel(BuildContext context) {
    return Row(children: [
      SizedBox(
        width: 84.px,
        child: Column(
          children: state.distanceTypeList
              .map((item) => InkWell(
                    onTap: () {
                      state.distanceTypeIndex.value =
                          state.distanceTypeList.indexOf(item);
                      state.distanceType.value = item;
                    },
                    child: Container(
                      alignment: Alignment.center,
                      color: state.distanceTypeIndex.value ==
                              state.distanceTypeList.indexOf(item)
                          ? Colors.white
                          : const Color.fromRGBO(247, 249, 253, 1),
                      height: 40.px,
                      child: Text(item,
                          style: TextStyle(
                              fontSize: 12.px,
                              color: state.normalColor,
                              fontWeight: state.distanceTypeIndex.value ==
                                      state.distanceTypeList.indexOf(item)
                                  ? FontWeight.w500
                                  : FontWeight.w400)),
                    ),
                  ))
              .toList(),
        ),
      ),
      Expanded(
          child: state.distanceType.value == "区域"
              ? buildAreaList(context)
              : Column(children: buildDistrictList(context)))
    ]);
  }

  List<Widget> buildDistrictList(BuildContext context) {
    return state.distanceList
        .map((item) => InkWell(
              onTap: () {
                state.distanceType.value = "直线距离";
                state.distanceIndex.value = state.distanceList.indexOf(item);
                logic.getResultList();
                state.isDistanceSelected.value = false;
                state.distanceLocationType.value = item;
              },
              child: Container(
                alignment: Alignment.centerLeft,
                padding: EdgeInsets.only(left: 18.px, right: 11.px),
                height: 37.px,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(item,
                        style: TextStyle(
                            fontSize: 12.px,
                            color: state.distanceIndex.value ==
                                    state.distanceList.indexOf(item)
                                ? state.selectedColor
                                : const Color.fromRGBO(44, 44, 52, 0.7),
                            fontWeight: state.distanceIndex.value ==
                                    state.distanceList.indexOf(item)
                                ? FontWeight.w500
                                : FontWeight.w400)),
                    Visibility(
                      visible: state.distanceIndex.value ==
                          state.distanceList.indexOf(item),
                      child: Image.asset(
                        Assets.agriculturalIcCheckbox,
                        width: 15.px,
                        height: 15.px,
                      ),
                    )
                  ],
                ),
              ),
            ))
        .toList();
  }

  Widget buildAreaList(BuildContext context) {
    return ListView.builder(
        padding: EdgeInsets.only(top: 0.px),
        itemCount: state.currentCityDistrictList.length,
        itemBuilder: (context, index) {
          var item = state.currentCityDistrictList[index];
          return InkWell(
            onTap: () {
              state.distanceType.value = "区域";
              state.districtIndex.value =
                  state.currentCityDistrictList.indexOf(item);
              logic.getResultList();
              state.isDistanceSelected.value = false;
              state.distanceLocationType.value = item.name!;
            },
            child: Container(
              alignment: Alignment.centerLeft,
              padding: EdgeInsets.only(left: 18.px, right: 11.px),
              height: 37.px,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(item.name ?? '',
                      style: TextStyle(
                          fontSize: 12.px,
                          color: state.districtIndex.value ==
                                  state.currentCityDistrictList.indexOf(item)
                              ? state.selectedColor
                              : const Color.fromRGBO(44, 44, 52, 0.7),
                          fontWeight: state.districtIndex.value ==
                                  state.currentCityDistrictList.indexOf(item)
                              ? FontWeight.w500
                              : FontWeight.w400)),
                  Visibility(
                    visible: state.districtIndex.value ==
                        state.currentCityDistrictList.indexOf(item),
                    child: Image.asset(
                      Assets.agriculturalIcCheckbox,
                      width: 15.px,
                      height: 15.px,
                    ),
                  )
                ],
              ),
            ),
          );
        });
  }

  Widget buildSortPanel(BuildContext context) {
    return Column(
      children: state.sortList
          .map((item) => InkWell(
                onTap: () {
                  state.sortIndex.value = state.sortList.indexOf(item);
                },
                child: SizedBox(
                  height: 37.px,
                  child: Center(
                    child: Text(item,
                        style: TextStyle(
                            fontSize: 12.px,
                            color: state.sortIndex.value ==
                                    state.sortList.indexOf(item)
                                ? state.selectedColor
                                : const Color.fromRGBO(44, 44, 52, 0.7),
                            fontWeight: state.sortIndex.value ==
                                    state.sortList.indexOf(item)
                                ? FontWeight.w500
                                : FontWeight.w400)),
                  ),
                ),
              ))
          .toList(),
    );
  }

  void showDistrictPicker(BuildContext context) {
    // Picker(
    //   adapter: PickerDataAdapter<String>(
    //     pickerData: [
    //       state.provinces,
    //       state.districts,
    //     ],
    //     isArray: true,
    //   ),
    //   title: const Text('选择地区'),
    //   cancelText: "取消",
    //   confirmText: "确定",
    //   textStyle: const TextStyle(color: Colors.black),
    //   cancelTextStyle: const TextStyle(color: Colors.red),
    //   confirmTextStyle: const TextStyle(color: Colors.blue),
    //   containerColor: Colors.white.withOpacity(0.85),
    //   headerDecoration: const BoxDecoration(
    //     color: Colors.white,
    //     borderRadius: BorderRadius.only(
    //       topLeft: Radius.circular(8.0),
    //       topRight: Radius.circular(8.0),
    //     ),
    //   ),
    //   onSelect: (Picker picker, int index, List<int> selected) {
    //     Logger().i("index:$index,selected:$selected");
    //     if (index == 0) {
    //       List<DistrictEntity> list =
    //           state.districtList.elementAt(selected.elementAt(0)).children;
    //       state.districts.value = list.map((e) => e.name!).toList();
    //       setState(() {});
    //     }
    //   },
    //   onConfirm: (Picker picker, List<int> value) {
    //     state.currentDistrict.value = state.districtList.elementAt(value[1]);
    //   },
    // ).showModal(context);

    var districtStr = json.encode(state.districtList);
    var cityList = json.decode(districtStr);
    var tempData = [];
    for (var e in cityList) {
      for (var ee in e['children']) {
        ee.remove('children');
      }
      tempData.add(e);
    }
    JhCascadeTreePicker.show(context,
        title: "选择地区",
        data: tempData,
        valueKey: "id",
        labelKey: "name",
        childrenKey: "children",
        clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
      DistrictEntity province = state.districtList
          .firstWhere((element) => ress.first['name'] == element.name);
      DistrictEntity district =
          province.children.firstWhere((e) => res['name'] == e.name);
      state.currentCityDistrictList.value = district.children;
      DistrictEntity cityDistrict = DistrictEntity();
      cityDistrict.id = res['id'];
      cityDistrict.name = "全市";
      state.currentCity.value = res['name'];
      state.currentCityDistrictList.insert(0, cityDistrict);
      Future.delayed(const Duration(milliseconds: 600), () {
        logic.getResultList();
      });
    });
  }

  void showPhoneBottomSheet(BuildContext context, POIEntity item) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: 145.px,
          margin: EdgeInsets.only(bottom: 8.px),
          child: Column(
            children: [
              Container(
                width: 359.px,
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(10.0))),
                child: Column(children: [
                  InkWell(
                    onTap: () {
                      final Uri tel = Uri.parse('tel:${item.phontNo}');
                      launchUrl(tel);
                      Navigator.of(context).pop();
                    },
                    child: SizedBox(
                      height: 45.px,
                      child: Center(
                        child: Text(
                          "${item.phontNo}",
                          style: TextStyle(
                            fontSize: 16.px,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const Divider(
                    thickness: 1.0,
                    height: 1.0,
                    color: Color.fromRGBO(244, 244, 244, 1),
                  ),
                  InkWell(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: item.phontNo!));
                      Navigator.of(context).pop();
                    },
                    child: SizedBox(
                      height: 45.px,
                      child: Center(
                        child: Text(
                          "复制号码",
                          style: TextStyle(
                            fontSize: 16.px,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                ]),
              ),
              InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  width: 359.px,
                  height: 45.px,
                  margin: EdgeInsets.only(top: 8.px),
                  decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(10.0))),
                  child: Center(
                      child: Text(
                    "取消",
                    style: TextStyle(fontSize: 16.px),
                  )),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void showNavigationBottomSheet(BuildContext context, POIEntity item) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          height: 100.px,
          margin: EdgeInsets.only(bottom: 8.px),
          child: Column(
            children: [
              Container(
                width: 359.px,
                decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(10.0))),
                child: Column(children: [
                  InkWell(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: item.phontNo!));
                      Navigator.of(context).pop();
                      openAmap(item.longitude!, item.latitude!,
                          title: "北大荒农服", address: item.maregasAddress);
                    },
                    child: SizedBox(
                      height: 45.px,
                      child: Center(
                        child: Text(
                          "高德地图",
                          style: TextStyle(
                            fontSize: 16.px,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ),
                ]),
              ),
              InkWell(
                onTap: () {
                  Navigator.of(context).pop();
                },
                child: Container(
                  width: 359.px,
                  height: 45.px,
                  margin: EdgeInsets.only(top: 8.px),
                  decoration: const BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(10.0))),
                  child: Center(
                      child: Text(
                    "取消",
                    style: TextStyle(fontSize: 16.px),
                  )),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<bool> openAmap(
    double longitude,
    double latitude, {
    String? address,
    String? title,
    bool showErr = true,
  }) async {
    String androidUrl =
        "amapuri://route/plan/?sourceApplication=${title ?? ""}&sid=&did=&dlat=$latitude&dlon=$longitude&dname=${address ?? ""}&dev=0&t=0";
    String iosUrl =
        "iosamap://path?sourceApplication=${title ?? ""}&sid=&did=&dlat=$latitude&dlon=$longitude&dname=${address ?? ""}&dev=0&t=0";
    String url = Platform.isAndroid ? androidUrl : iosUrl;
    if (Platform.isIOS) url = Uri.encodeFull(url);
    try {
      if (await canLaunchUrlString(url)) {
        await launchUrlString(url);
        return true;
      } else {
        if (showErr) showToast('无法调起高德地图');
        return false;
      }
    } on Exception catch (e) {
      if (showErr) showToast('无法调起高德地图');
      return false;
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    Get.delete<MaintenanceLogic>();
    super.dispose();
  }
}
