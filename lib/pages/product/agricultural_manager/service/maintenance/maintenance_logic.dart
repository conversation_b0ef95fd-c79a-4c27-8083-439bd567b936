import 'dart:convert';
import 'dart:math';

import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/district_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/poi_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/region_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/location_manager.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';
import 'package:logger/logger.dart';

import '../../../../../generated/assets.dart';
import '../../../../../utils/event_bus.dart';
import '../../api_server/agricultural_service.dart';
import 'maintenance_state.dart';

class MaintenanceLogic extends GetxController {
  final MaintenanceState state = MaintenanceState();

  LocationManager locationManager = LocationManager.instance;
  BuildContext? mContext;
  @override
  void onReady() {
    super.onReady();
    initLocation();
    getAllRegion(0);
  }

  initContext(BuildContext context) {
    mContext = context;
  }

  initLocation() async {
    locationManager.init();
    locationManager.addLocationChanged((result) {
      // Logger().i(json.encode(result));
      String city = result['city'] as String;
      state.currentLatitude = result['latitude'] as double;
      state.currentLongitude = result['longitude'] as double;
      if (state.currentCity.value.isEmpty) {
        state.currentCity.value = city;
        getUserRegion(state.currentLongitude, state.currentLatitude);
      }

      addGPSMaker();
    });
  }

  void getUserRegion(num? longitude, num? latitude) async {
    Map<String, dynamic> params = {
      "currPointLong": longitude,
      "currPointLat": latitude,
    };
    AgriculturalService.getUserRegion(params).then((result) {
      if (result.success == true) {
        List<RegionEntity> records = (result.data as List<dynamic>)
            .map((item) => RegionEntity.fromJson(item))
            .toList();
        state.currentRegion.value =
            records.firstWhere((item) => item.regionLevel == 2);
        // 查询所在城市的所有区域
        getAllRegion(state.currentRegion.value.regionId);
      }
    });
  }

  void getAllRegion(int? regionId) async {
    Map<String, dynamic> params = {
      "parentId": regionId,
    };
    AgriculturalService.queryAllZheRegion(params).then((result) {
      if (result.success == true) {
        List<DistrictEntity> records = (result.data as List<dynamic>)
            .map((item) => DistrictEntity.fromJson(item))
            .toList();
        if (regionId == 0) {
          state.districtList.value = records;
          List<String> districtList =
              records.map((item) => item.name!).toList();
          state.provinces.value = districtList;
          state.districts.value = state.districtList.first.children
              .map((item) => item.name!)
              .toList();
        } else {
          DistrictEntity cityDistrict = DistrictEntity();
          cityDistrict.id = regionId;
          cityDistrict.name = "全市";
          records.insert(0, cityDistrict);
          state.currentRegion.value.districtList = records;
          state.currentCityDistrictList.value = records;
          getResultList();
        }
      }
    });
  }

  void getResultList() {
    showCustomBusyDialog(mContext!);
    Map<String, dynamic> params = {
      "currPointLong": state.currentLongitude,
      "currPointLat": state.currentLatitude,
      "distance": state.distanceValueList.elementAt(
          state.distanceIndex.value < 0 ? 0 : state.distanceIndex.value),
      "maregasName": state.searchWord,
      "regionId": state.currentCityDistrictList
          .elementAt(
              state.districtIndex.value < 0 ? 0 : state.districtIndex.value)
          .id,
      "maregasType": "M", // //场所类型M保养修理 G加油站
    };

    if (state.distanceType.value == "直线距离") {
      params.remove("regionId");
    } else if (state.distanceType.value == "区域") {
      params.remove("distance");
    } else {
      params.remove("distance");
    }

    AgriculturalService.queryMachineMaregasApi(params).then((result) {
      Navigator.of(mContext!).pop();
      if (result.success == true) {
        state.poiMarkers.clear();
        List<POIEntity> records = (result.data as List<dynamic>)
            .map((item) => POIEntity.fromJson(item))
            .toList();
        state.poiList.value = records;
        state.isPOIDetailPanelShow.value = false;
        state.isBottomPanelShow.value = true;

        state.onMapPOIs.clear();
        // state.poiMarkers.removeWhere((key, marker) => key != "gps");
        for (var element in state.poiList) {
          LatLng position = LatLng(element.latitude!, element.longitude!);
          Marker marker = Marker(
            point: position, //使用默认hue的方式设置Marker的图标
            child: InkWell(
                onTap: () {
                  Logger().i("点击了Marker${state.poiMarkers}");
                  POIEntity clickPoi = element;
                  showPOIDetail(clickPoi);
                },
                child: Image.asset(Assets.agriculturalIcJyz)),
            // onTap: (mark) {
            //   Logger().i("点击了Marker${state.poiMarkers}");
            //   POIEntity clickPoi = state.onMapPOIs[mark]!;
            //   showPOIDetail(clickPoi);
            // },
          );
          state.poiMarkers.add(marker);
          // state.onMapPOIs[marker.id] = element;
          // state.poiMarkers[marker.id] = marker;
        }
        bus.emit("toRefreshPOI");
      }
    });
  }

  void addGPSMaker() async {
    // if (state.poiMarkers.containsKey("gps")) {
    //   state.poiMarkers.remove("gps");
    // }
    LatLng position = LatLng(state.currentLatitude, state.currentLongitude);
    Marker marker = Marker(
        width: 39.33.px,
        height: 39.33.px,
        point: position,
        child: Image.asset(Assets.agriculturalIcGpsLocation));
    state.locationMarkers = [marker];
    bus.emit("toRefreshPOI");
  }

  void showPOIDetail(POIEntity item) {
    state.isBottomPanelShow.value = false;
    state.isPOIDetailPanelShow.value = true;
    state.poiDetail.value = item;
    state.mapController.move(LatLng(item.latitude!, item.longitude!), 15);
  }

  String getDistance(double distance) {
    String distance0 = ((distance - 0) / 1000).toStringAsFixed(1);
    return distance0;
  }

  @override
  void onClose() {
    locationManager.removeLocationChanged();
    locationManager.stopLocation();
    locationManager.destroy();
    super.onClose();
  }

  void onClickExpand(BuildContext context) {
    state.isExpand.value = !state.isExpand.value;
    if (state.isExpand.value) {
      state.bottomPanelHeight.value =
          (MediaQuery.of(context).size.height - 210.0).toInt();
    } else {
      state.bottomPanelHeight.value = 329;
    }
  }

  Future<void> showCustomBusyDialog(BuildContext context) async {
    await showGeneralDialog(
      context: context,
      barrierDismissible: false, // 设置用户是否可以点击遮罩层来关闭对话框
      barrierLabel: '加载中...', // 遮罩层的提示文字
      barrierColor: Colors.black.withOpacity(0.5), // 遮罩层的颜色和透明度
      transitionDuration: const Duration(milliseconds: 150), // 动画时长
      pageBuilder: (BuildContext context, Animation<double> animation,
          Animation<double> secondaryAnimation) {
        return Center(
          // 对话框内容居中显示
          child: Container(
            alignment: Alignment.center,
            width: 120.px,
            height: 120.px,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10.px),
              color: Colors.white,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                    width: 40.px,
                    height: 40.px,
                    child: const CircularProgressIndicator()),
                SizedBox(height: 16.px),
                Text("加载中...",
                    style: TextStyle(
                        decoration: TextDecoration.none,
                        fontSize: 14.px,
                        fontWeight: FontWeight.w500,
                        color: const Color.fromRGBO(44, 44, 52, 0.8)))
              ],
            ),
          ), // 加载指示器
        );
      },
    );
  }

  void toMineLocation() {
    state.mapController
        .move(LatLng(state.currentLatitude, state.currentLongitude), 15);
  }
}
