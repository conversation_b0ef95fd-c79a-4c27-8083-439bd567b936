import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../api_server/entity/information_entity.dart';

class InformationState {
  Color selectedColor = const Color.fromRGBO(2, 139, 93, 1);
  Color normalColor = const Color.fromRGBO(41, 41, 52, 1);

  RefreshController refreshController =
      RefreshController(initialRefresh: false);

  int page = 1;
  int pageSize = 10;
  RxList<InformationEntity> dateRecords = <InformationEntity>[].obs;
  String keyword = '';
  RxBool isLoading = true.obs;

  InformationState() {
    ///Initialize variables
  }
}
