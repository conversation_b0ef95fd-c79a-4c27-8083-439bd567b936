import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/information_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/service/information/information_detail/information_detail_view.dart';
import 'package:bdh_smart_agric_app/utils/date_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import '../../../../../components/bdh_network_image.dart';
import '../../../../../generated/assets.dart';
import '../../../../../utils/color_util.dart';
import 'information_logic.dart';
import 'information_state.dart';

/// 农机资讯
class InformationPage extends StatefulWidget {
  const InformationPage({Key? key}) : super(key: key);

  @override
  State<InformationPage> createState() => _InformationPageState();
}

class _InformationPageState extends State<InformationPage> {
  final InformationLogic logic = Get.put(InformationLogic());
  final InformationState state = Get.find<InformationLogic>().state;
  TextEditingController textController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints cons) {
      return Scaffold(
          backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
          body: Obx(() {
            return SizedBox(
                width: cons.maxWidth,
                height: cons.maxHeight,
                child: Stack(alignment: Alignment.topCenter, children: [
                  Positioned(
                    child: Container(
                      height: 240.px,
                      width: 375.px,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        fit: BoxFit.cover,
                        image: AssetImage(Assets.agriculturalIcInfoTopBg),
                      )),
                    ),
                  ),
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 36.px, left: 8.px),
                            child: BackButton(
                              color: Colors.white,
                              onPressed: () {
                                Navigator.pop(context);
                              },
                            ),
                          )
                        ]),
                  ),
                  Positioned(
                    top: 139.5.px,
                    child: Container(
                      alignment: Alignment.center,
                      width: 345.px,
                      height: 34.px,
                      margin: EdgeInsets.only(right: 15.px, left: 15.px),
                      padding: EdgeInsets.only(left: 15.px, right: 16.px),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20.px),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextField(
                                controller: textController,
                                decoration: InputDecoration(
                                  isDense: true,
                                  hintText: "请输入站点名称",
                                  hintStyle: TextStyle(
                                    fontSize: 12.px,
                                    fontWeight: FontWeight.w400,
                                    color:
                                        const Color.fromRGBO(167, 172, 180, 1),
                                  ),
                                  suffixIcon: TextButton.icon(
                                      onPressed: () {
                                        state.keyword = textController.text;
                                        logic.getDataRecords();
                                      },
                                      style: ButtonStyle(
                                        padding: MaterialStateProperty.all(
                                            EdgeInsets.symmetric(
                                                horizontal: 4.px,
                                                vertical: 2.px)),
                                        shape: MaterialStateProperty.all(
                                            RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(
                                                        4.px))),
                                      ),
                                      label: Text("搜索",
                                          style: TextStyle(
                                            color: state.selectedColor,
                                          )),
                                      icon: Image.asset(
                                        Assets.agriculturalIconSearch,
                                        color: state.selectedColor,
                                        width: 17.px,
                                        height: 17.px,
                                      )),
                                  border: InputBorder.none,
                                )),
                          )
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    top: 189.5.px,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Container(
                      width: 375.px,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(6.px),
                          topRight: Radius.circular(6.px),
                        ),
                        color: Colors.white,
                      ),
                      margin: EdgeInsets.only(left: 12.px, right: 12.px),
                      // child: ListView.builder(
                      //     padding: EdgeInsets.only(top: 4.px),
                      //     itemCount: 10,
                      //     itemBuilder: (context, index) {
                      //       return Container(child: buildInfoItem(context));
                      //     }),
                      child: state.dateRecords.isEmpty
                          ? SizedBox(
                              height: 200.px,
                              child: Center(
                                child: state.isLoading.value == true
                                    ? SpinKitCircle(
                                        color: HexColor('#16B760'),
                                        size: 50.0,
                                      )
                                    : const BdhEmptyView(),
                              ),
                            )
                          : SmartRefresher(
                              controller: state.refreshController,
                              header: const MaterialClassicHeader(),
                              footer: const ClassicFooter(),
                              enablePullUp: true,
                              onRefresh: () {
                                logic.onRefresh();
                              },
                              onLoading: () {
                                logic.onLoadMore();
                              },
                              child: ListView.builder(
                                  padding: EdgeInsets.only(top: 4.px),
                                  itemCount: state.dateRecords.length,
                                  itemBuilder: (context, index) {
                                    return Container(
                                        child: buildInfoItem(
                                            context,
                                            state.dateRecords
                                                .elementAt(index)));
                                  }),
                            ),
                    ),
                  )
                ]));
          }));
    });
  }

  @override
  void dispose() {
    Get.delete<InformationLogic>();
    super.dispose();
  }

  Widget buildInfoItem(BuildContext context, InformationEntity item) {
    return InkWell(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => InformationDetailPage(
              dateItem: item,
            ),
          ),
        );
      },
      child: Container(
          padding: EdgeInsets.only(left: 16.px, right: 16.px, top: 12.px),
          child: Column(children: [
            Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Container(
                width: 190.px,
                height: 85.px,
                margin: EdgeInsets.only(bottom: 12.px),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(item.title ?? "",
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 15.px,
                            height: 1.3,
                            fontWeight: FontWeight.w400,
                            color: state.normalColor)),
                    const Spacer(),
                    Text(
                        DateUtil.getformatTimestamp(
                            item.createTime!, "yyyy-MM-dd"),
                        style: TextStyle(
                            fontSize: 12.px,
                            fontWeight: FontWeight.w400,
                            color: state.normalColor.withOpacity(0.4))),
                  ],
                ),
              ),
              const Spacer(),
              // Image.asset(
              //   Assets.agriculturalIcNewsZctz,
              //   width: 112.px,
              //   height: 85.px,
              // )
              ClipRRect(
                borderRadius: BorderRadius.all(Radius.circular(8.px)),
                child: BdhNetworkImage(
                  url: '${item.thumbUrl}',
                  fit: BoxFit.cover,
                  width: 112.px,
                  height: 85.px,
                  errorImage: Assets.agriculturalIcStation,
                ),
              ),
            ]),
            Divider(height: 1.px, color: state.normalColor.withOpacity(0.1))
          ])),
    );
  }
}
