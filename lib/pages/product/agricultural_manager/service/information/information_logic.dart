import 'dart:convert';

import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/information_entity.dart';
import 'package:get/get.dart';

import '../../api_server/agricultural_service.dart';
import 'information_state.dart';

class InformationLogic extends GetxController {
  final InformationState state = InformationState();

  @override
  void onReady() {
    super.onReady();
    getDataRecords();
  }

  void getDataRecords() async {
    // await AgriculturalService.getUserRegion();
    Map<String, dynamic> params = {
      "page": state.page,
      "rows": state.pageSize,
      "title": state.keyword
    };
    state.isLoading.value = true;
    AgriculturalService.queryInformationApi(params).then((result) {
      state.isLoading.value = false;
      if (result.success == true) {
        print(json.encode(result));
        List<InformationEntity> records =
            (result.data['records'] as List<dynamic>)
                .map((item) => InformationEntity.fromJson(item))
                .toList();
        if (state.page == 1) {
          state.refreshController.refreshCompleted();
          state.dateRecords.value = records;
          if (state.dateRecords.length >= result.data['total']) {
            state.refreshController.loadNoData();
          } else {
            state.refreshController.resetNoData();
          }
        } else {
          state.dateRecords.addAll(records);
          state.refreshController.loadComplete();
        }

        if (records.length < state.pageSize) {
          state.refreshController.loadNoData();
        }

        // state.currentRegion.value =
        //     records.firstWhere((item) => item.regionLevel == 2);
        // 查询所在城市的所有区域
      }
    });
  }

  onRefresh() {
    state.page = 1;
    getDataRecords();
  }

  onLoadMore() {
    state.page++;
    getDataRecords();
  }

  @override
  void onClose() {
    state.refreshController.dispose();
    super.onClose();
  }
}
