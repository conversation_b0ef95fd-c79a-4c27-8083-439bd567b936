import 'package:get/get.dart';

import '../../../api_server/entity/information_detail_entity.dart';
import '../../../api_server/entity/information_entity.dart';

class InformationDetailState {
  InformationEntity? dateItem;

  Rx<InformationDetailEntity> informationDetailEntity =
      InformationDetailEntity().obs;

  RxBool isLoading = true.obs;

  InformationDetailState() {
    ///Initialize variables
  }
}
