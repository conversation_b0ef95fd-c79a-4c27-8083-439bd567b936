import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/information_entity.dart';
import 'package:bdh_smart_agric_app/utils/date_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';

import '../../../../../../utils/color_util.dart';
import 'information_detail_logic.dart';
import 'information_detail_state.dart';

class InformationDetailPage extends StatefulWidget {
  final InformationEntity dateItem;

  const InformationDetailPage({Key? key, required this.dateItem})
      : super(key: key);

  @override
  State<InformationDetailPage> createState() => _InformationDetailPageState();
}

class _InformationDetailPageState extends State<InformationDetailPage> {
  final InformationDetailLogic logic = Get.put(InformationDetailLogic());
  final InformationDetailState state = Get.find<InformationDetailLogic>().state;

  @override
  void initState() {
    super.initState();
    state.dateItem = widget.dateItem;
    logic.getDateRecords();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            '资讯详情',
            style: TextStyle(fontSize: 18.px, fontWeight: FontWeight.w500),
          ),
          backgroundColor: Colors.white,
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: Obx(() {
          return Column(
            children: [
              Container(
                padding: EdgeInsets.only(top: 16.px),
                child: Text(
                  state.informationDetailEntity.value.title ?? "",
                  style: TextStyle(
                    fontSize: 16.px,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Container(
                alignment: Alignment.centerRight,
                padding: EdgeInsets.all(10.px),
                child: Text(
                  DateUtil.formatTimestamp(
                      state.informationDetailEntity.value.releaseTime ?? 0,
                      'yyyy-MM-dd HH:mm:ss'),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w400,
                    color: Colors.black12.withOpacity(0.6),
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.all(16.px),
                child: state.isLoading.value == true
                    ? Center(
                        child: SpinKitCircle(
                          color: HexColor('#16B760'),
                          size: 50.0,
                        ),
                      )
                    : SingleChildScrollView(
                        child: HtmlWidget(
                          state.informationDetailEntity.value.releaseContent ??
                              "",
                          textStyle: TextStyle(
                            fontSize: 15.px,
                          ),
                        ),
                      ),
              ),
            ],
          );
        }));
  }

  @override
  void dispose() {
    Get.delete<InformationDetailLogic>();
    super.dispose();
  }
}
