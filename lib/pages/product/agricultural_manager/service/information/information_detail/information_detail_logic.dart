import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/agricultural_service.dart';
import 'package:get/get.dart';

import '../../../api_server/entity/information_detail_entity.dart';
import 'information_detail_state.dart';

class InformationDetailLogic extends GetxController {
  final InformationDetailState state = InformationDetailState();

  @override
  void onReady() {
    super.onReady();
  }

  void getDateRecords() async {
    var releaseInfoId = state.dateItem!.releaseInfoId.toString();
    Map<String, dynamic> params = {
      "releaseInfoId": releaseInfoId,
    };

    AgriculturalService.queryInformationDetail(params, releaseInfoId)
        .then((result) {
      state.isLoading.value = false;
      if (result.success == true) {
        InformationDetailEntity dataItem =
            InformationDetailEntity.fromJson(result.data);
        state.informationDetailEntity.value = dataItem;
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
  }
}
