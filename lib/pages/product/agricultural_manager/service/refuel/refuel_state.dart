import 'dart:ui';

import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/district_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/poi_entity.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';

import '../../api_server/entity/region_entity.dart';

class RefuelState {
  MapController mapController = MapController();

  List<Marker> locationMarkers = [];
  List<Marker> poiMarkers = [];

  // final Map<String, Marker> poiMarkers = <String, Marker>{};
  final Map<String, POIEntity> onMapPOIs = <String, POIEntity>{};
  RxBool isMapReLoading = false.obs;

  RxBool isExpand = false.obs;
  RxInt bottomPanelHeight = 325.obs;
  RxBool isBottomPanelShow = false.obs;
  RxBool isPOIDetailPanelShow = false.obs;

  RxBool isDistanceSelected = false.obs;
  RxInt distanceIndex = RxInt(-1);
  RxInt districtIndex = RxInt(0);
  RxInt distanceTypeIndex = 0.obs;
  RxBool isSortSelected = false.obs;
  RxInt sortIndex = 0.obs;

  Color selectedColor = const Color.fromRGBO(2, 139, 93, 1);
  Color normalColor = const Color.fromRGBO(41, 41, 52, 1);
  Color normalIconColor = const Color.fromRGBO(122, 121, 124, 1);

  double defaultLatitude = 45.76091999999998;
  double defaultLongitude = 126.63125300000002;

  RxString currentCity = ''.obs;
  Rx<RegionEntity> currentRegion = RegionEntity().obs;
  RxList<String> provinces = <String>[].obs;
  RxList<String> districts = <String>[].obs;

  RxList<RegionEntity> regionList = <RegionEntity>[].obs;

  RxList<DistrictEntity> districtList = <DistrictEntity>[].obs;
  Rx<DistrictEntity> currentDistrict = DistrictEntity().obs;

  RxList<DistrictEntity> currentCityDistrictList = <DistrictEntity>[].obs;

  double currentLatitude = 0.0;
  double currentLongitude = 0.0;
  String searchWord = '';

  RxString distanceType = "".obs; // 空为默认  distance为距离 area为区域

  RxString distanceLocationType = "距离位置".obs;

  Rx<POIEntity> poiDetail = POIEntity().obs;

  List<String> distanceTypeList = [
    '直线距离',
    '区域',
  ];

  List<String> distanceList = [
    '默认距离',
    '500米',
    '1000米',
    '2000米',
  ];
  List<int> distanceValueList = [
    300,
    500,
    1000,
    2000,
  ];

  List<String> sortList = [
    '距离优先',
  ];

  RxList<POIEntity> poiList = <POIEntity>[].obs;

  MaintenanceState() {
    ///Initialize variables
    currentLatitude = defaultLatitude;
    currentLongitude = defaultLongitude;
  }
}
