import 'package:bdh_smart_agric_app/generated/json/base/json_field.dart';
import 'package:bdh_smart_agric_app/generated/json/information_detail_entity.g.dart';
import 'dart:convert';
export 'package:bdh_smart_agric_app/generated/json/information_detail_entity.g.dart';

@JsonSerializable()
class InformationDetailEntity {
  int? releaseInfoId = 0;
  String? title = '';
  String? orgCode = '';
  String? orgName = '';
  String? dtlType = '';
  String? thumbUrl = '';
  String? releaseContent = '';
  int? releaseTime = 0;
  List<dynamic>? annexList = [];

  InformationDetailEntity();

  factory InformationDetailEntity.fromJson(Map<String, dynamic> json) =>
      $InformationDetailEntityFromJson(json);

  Map<String, dynamic> toJson() => $InformationDetailEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
