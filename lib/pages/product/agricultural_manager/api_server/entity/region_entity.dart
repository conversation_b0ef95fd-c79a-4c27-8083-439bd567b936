import 'package:bdh_smart_agric_app/generated/json/base/json_field.dart';
import 'package:bdh_smart_agric_app/generated/json/region_entity.g.dart';
import 'dart:convert';

import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/district_entity.dart';
export 'package:bdh_smart_agric_app/generated/json/region_entity.g.dart';

@JsonSerializable()
class RegionEntity {
  String? regionName = '';
  int? regionId = 0;
  String? parentId = '';
  int? regionLevel = 0;
  String? currPointLong = '';
  String? currPointLat = '';
  List<DistrictEntity> districtList = [];

  RegionEntity();

  factory RegionEntity.fromJson(Map<String, dynamic> json) =>
      $RegionEntityFromJson(json);

  Map<String, dynamic> toJson() => $RegionEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
