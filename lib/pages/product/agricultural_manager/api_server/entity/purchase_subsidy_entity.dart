import 'package:bdh_smart_agric_app/generated/json/base/json_field.dart';
import 'package:bdh_smart_agric_app/generated/json/purchase_subsidy_entity.g.dart';
import 'dart:convert';
export 'package:bdh_smart_agric_app/generated/json/purchase_subsidy_entity.g.dart';

@JsonSerializable()
class PurchaseSubsidyEntity {
  String? orgCode = '';
  String? orgName = '';
  String? amOwnerName = '';
  String? amTypeCode3 = '';
  String? amTypeName3 = '';
  String? companyName = '';
  String? productName = '';
  String? amModelName = '';
  String? totalSubsidy = '';
  String? stateName = '';
  String? factoryCode = '';
  String? purchaseDate = '';
  String? singleSalePrice = '';
  String? saleCount = '';
  dynamic certNo;
  String? amTypeCode2 = '';
  String? amTypeName2 = '';
  String? amTypeCode1 = '';
  String? amTypeName1 = '';
  dynamic year;

  PurchaseSubsidyEntity();

  factory PurchaseSubsidyEntity.fromJson(Map<String, dynamic> json) =>
      $PurchaseSubsidyEntityFromJson(json);

  Map<String, dynamic> toJson() => $PurchaseSubsidyEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
