import 'package:bdh_smart_agric_app/generated/json/base/json_field.dart';
import 'package:bdh_smart_agric_app/generated/json/district_entity.g.dart';
import 'dart:convert';
export 'package:bdh_smart_agric_app/generated/json/district_entity.g.dart';

@JsonSerializable()
class DistrictEntity {
  int? id = 0;
  int? parentId = 0;
  String? name = '';
  int? weight = 0;
  List<DistrictEntity> children = [];

  DistrictEntity();

  factory DistrictEntity.fromJson(Map<String, dynamic> json) =>
      $DistrictEntityFromJson(json);

  Map<String, dynamic> toJson() => $DistrictEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
