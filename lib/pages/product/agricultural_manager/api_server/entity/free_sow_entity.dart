import 'package:bdh_smart_agric_app/generated/json/base/json_field.dart';
import 'package:bdh_smart_agric_app/generated/json/free_sow_entity.g.dart';
import 'dart:convert';
export 'package:bdh_smart_agric_app/generated/json/free_sow_entity.g.dart';

@JsonSerializable()
class FreeSowEntity {
  double? totalSum;
  List<FreeSowResultList>? resultList = [];

  FreeSowEntity();

  factory FreeSowEntity.fromJson(Map<String, dynamic> json) =>
      $FreeSowEntityFromJson(json);

  Map<String, dynamic> toJson() => $FreeSowEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class FreeSowResultList {
  int? year = 0;
  String? name = '';
  dynamic idCard;
  String? orgName = '';
  dynamic amount;
  dynamic endAddress;
  String? date = '';
  String? amTypeName1 = '';
  String? amTypeName2 = '';
  String? amTypeName3 = '';
  String? amModelName = '';
  String? companyName = '';
  String? machineNo = '';
  String? dsn = '';
  String? type = '';
  double? wid;
  int? wtype = 0;
  String? crop = '';
  String? start = '';
  String? end = '';
  double? area;
  double? qarea;
  String? subsidiesLevel = '';
  dynamic overlap;
  String? uuid = '';
  String? hasLoc = '';
  dynamic sex;
  int? workId = 0;

  FreeSowResultList();

  factory FreeSowResultList.fromJson(Map<String, dynamic> json) =>
      $FreeSowResultListFromJson(json);

  Map<String, dynamic> toJson() => $FreeSowResultListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
