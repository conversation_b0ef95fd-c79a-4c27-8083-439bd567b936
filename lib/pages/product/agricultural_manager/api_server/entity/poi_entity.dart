import 'package:bdh_smart_agric_app/generated/json/base/json_field.dart';
import 'dart:convert';

import '../../../../../generated/json/poi_entity.g.dart';

@JsonSerializable()
class POIEntity {
  String? updateBy = '';
  String? updateTime = '';
  int? statusCd = 0;
  int? maregasId = 0;
  String? maregasName = '';
  String? maregasType = '';
  String? residueGas = '';
  double? longitude;
  double? latitude;
  String? phontNo = '';
  String? remark = '';
  String? maregasAddress = '';
  String? picAddr = '';
  int? createBy = 0;
  int? createTime = 0;
  int? regionId = 0;
  String? params = '';
  String? currPointLong = '';
  String? currPointLat = '';
  double? distance = 0.0;

  POIEntity();

  factory POIEntity.fromJson(Map<String, dynamic> json) =>
      $POIEntityFromJson(json);

  Map<String, dynamic> toJson() => $POIEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
