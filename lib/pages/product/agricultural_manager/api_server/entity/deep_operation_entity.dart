import 'package:bdh_smart_agric_app/generated/json/base/json_field.dart';
import 'package:bdh_smart_agric_app/generated/json/deep_operation_entity.g.dart';
import 'dart:convert';
export 'package:bdh_smart_agric_app/generated/json/deep_operation_entity.g.dart';

@JsonSerializable()
class DeepOperationEntity {
  List<DeepOperationRecord>? subsoilingSupLst = [];
  double? totalEligibilityArea;
  double? workArea;
  double? deepEligibilityArea;
  double? recoverArea;

  DeepOperationEntity();

  factory DeepOperationEntity.fromJson(Map<String, dynamic> json) =>
      $DeepOperationEntityFromJson(json);

  Map<String, dynamic> toJson() => $DeepOperationEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class DeepOperationRecord {
  dynamic serialNumber;
  String? workId = '';
  String? statYear = '';
  String? uuid = '';
  dynamic endAddress;
  String? workStart = '';
  String? workEnd = '';
  double? slat;
  double? slon;
  double? elat;
  double? elon;
  double? wid;
  String? field = '';
  String? wtype = '';
  double? t;
  double? len;
  String? crop = '';
  String? wdType = '';
  String? workname = '';
  double? high;
  double? dep;
  dynamic subsidiesLevel;
  dynamic subsidiesStatus;
  int? statusCd = 0;
  String? dataStatus = '';
  int? createTime = 0;
  dynamic createBy;
  int? updateTime = 0;
  dynamic updateBy;
  String? facCode = '';
  String? workDate = '';
  double? area;
  double? qarea;
  double? depQualify;
  double? repeatArea;
  String? orgName = '';
  String? name = '';
  dynamic tel;
  String? dsn = '';
  String? facName = '';
  String? machineOrgName = '';
  String? machineNo = '';
  dynamic engineNo;
  String? companyName = '';
  String? licenseNo = '';
  String? amTypeName3 = '';
  String? amOwnerName = '';
  String? contactNum = '';
  dynamic params;
  String? certNo = '';
  String? amTypeCode1 = '';
  String? amTypeName1 = '';
  String? amTypeCode2 = '';
  String? amTypeName2 = '';
  String? amTypeCode3 = '';

  DeepOperationRecord();

  factory DeepOperationRecord.fromJson(Map<String, dynamic> json) =>
      $DeepOperationRecordFromJson(json);

  Map<String, dynamic> toJson() => $DeepOperationRecordToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
