import 'package:bdh_smart_agric_app/generated/json/base/json_field.dart';
import 'package:bdh_smart_agric_app/generated/json/information_entity.g.dart';
import 'dart:convert';
export 'package:bdh_smart_agric_app/generated/json/information_entity.g.dart';

@JsonSerializable()
class InformationEntity {
  int? releaseInfoId = 0;
  String? title = '';
  String? orgCode = '';
  String? orgName = '';
  int? columnLevelId = 0;
  String? columnLevelName = '';
  int? orders = 0;
  int? readNum = 0;
  String? manuscriptStatus = '';
  String? thumbUrl = '';
  int? releaseBy = 0;
  String? releaseByName = '';
  int? releaseTime = 0;
  int? withdrawBy = 0;
  String? withdrawByName = '';
  int? withdrawTime = 0;
  int? deleteBy = 0;
  String? deleteByName = '';
  int? deleteTime = 0;
  dynamic remark;
  int? createBy = 0;
  String? createByName = '';
  int? createTime = 0;
  dynamic updateBy;
  dynamic updateTime;
  dynamic statusCd;
  dynamic params;
  dynamic upDownFlag;
  dynamic systemCode;

  InformationEntity();

  factory InformationEntity.fromJson(Map<String, dynamic> json) =>
      $InformationEntityFromJson(json);

  Map<String, dynamic> toJson() => $InformationEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
