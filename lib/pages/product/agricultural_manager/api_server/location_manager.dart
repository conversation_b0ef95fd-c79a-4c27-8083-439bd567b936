import 'dart:convert';

import 'package:amap_flutter_location/amap_flutter_location.dart';
import 'package:amap_flutter_location/amap_location_option.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../../utils/gps/gps_receiver.dart';

class LocationManager {
  static LocationManager? _instance;
  final AMapFlutterLocation locationPlugin = AMapFlutterLocation();

  ValueChanged<Map<String, Object>>? onLocationChanged;

  static LocationManager get instance {
    _instance ??= LocationManager._internal();
    return _instance!;
  }

  LocationManager._internal();

  void addLocationChanged(ValueChanged<Map<String, Object>> callback) {
    onLocationChanged = callback;
  }

  Future<void> init() async {
    AMapFlutterLocation.setApiKey(
        "cb174d5f4d268a8a72cf54b7b6c724f2", "47ac324f0fda3f062e8cf85d90ae7a8d");
    AMapFlutterLocation.updatePrivacyShow(true, true);
    AMapFlutterLocation.updatePrivacyAgree(true);

    /// 动态申请定位权限
    requestPermission();

    locationPlugin.onLocationChanged().listen((Map<String, Object> result) {
      if (result['errorCode'] != null) {
        return;
      }

      // Logger().d("onLocationChanged: ${json.encode(result)}");
      if (onLocationChanged != null) {
        onLocationChanged!(result);
      }

      // num lat = 0;
      // num lng = 0;
      // if (Platform.isAndroid) {
      //   lat = result["latitude"] as num;
      //   lng = result["longitude"] as num;
      // } else if (Platform.isIOS) {
      //   lat = double.parse(result["latitude"] as String);
      //   lng = double.parse(result["longitude"] as String);
      // }
      //先转换下坐标
      // List<num> coordinates = GpsUtil.gcj02ToGps84(lat, lng);
      // BDHResponsitory.getAMapSearch(
      //         "${lng.toStringAsFixed(5)},${lat.toStringAsFixed(5)}")
      //     .then((res) {
      //   LocationResult location = LocationResult(
      //       coordinates[0],
      //       coordinates[1],
      //       result["altitude"] as num,
      //       result["accuracy"] as num,
      //       "${res.regeocode?.addressComponent?.province}${res.regeocode?.addressComponent?.city}${res.regeocode?.addressComponent?.district}",
      //       "${res.regeocode?.addressComponent?.city}",
      //       lat,
      //       lng);
      // });
      // LocationResult location = LocationResult(
      //     lat,
      //     lng,
      //     result["altitude"] as num,
      //     result["accuracy"] as num,
      //     result["address"] as String,
      //     result["city"] as String,
      //     lat,
      //     lng);
    });
  }

  /// 动态申请定位权限
  void requestPermission() async {
    // 申请权限
    bool hasLocationPermission = await requestLocationPermission();
    if (hasLocationPermission) {
      //print("定位权限申请通过");
      startLocation();
    } else {
      //print("定位权限申请不通过");
    }
  }

  /// 申请定位权限
  /// 授予定位权限返回true， 否则返回false
  Future<bool> requestLocationPermission() async {
    //获取当前的权限
    var status = await Permission.location.status;
    if (status == PermissionStatus.granted) {
      //已经授权
      return true;
    } else {
      //未授权则发起一次申请
      status = await Permission.location.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }

  ///设置定位参数
  void _setLocationOption({required bool isOnce, int? interval}) {
    AMapLocationOption locationOption = AMapLocationOption();
    locationOption.onceLocation = isOnce;
    locationOption.needAddress = false;
    locationOption.geoLanguage = GeoLanguage.DEFAULT;
    locationOption.desiredLocationAccuracyAuthorizationMode =
        AMapLocationAccuracyAuthorizationMode.ReduceAccuracy;
    locationOption.fullAccuracyPurposeKey = "AMapLocationScene";
    locationOption.locationMode = AMapLocationMode.Hight_Accuracy;
    locationOption.desiredAccuracy = DesiredAccuracy.Best;
    locationOption.needAddress = true;
    locationOption.pausesLocationUpdatesAutomatically = false;
    locationOption.locationInterval = interval ?? 30000;

    locationPlugin.setLocationOption(locationOption);
  }

  Future<void> startLocation() async {
    _setLocationOption(isOnce: false, interval: 3000);
    locationPlugin.startLocation();
  }

  Future<void> stopLocation() async {
    locationPlugin.stopLocation();
  }

  void destroy() {
    locationPlugin.destroy();
  }

  void removeLocationChanged() {
    onLocationChanged = null;
  }
}
