import 'package:dio/dio.dart';

import '../../../../model/dict_list_model.dart';
import '../../../../model/org_tree_list_model.dart';
import '../../../../model/request_no_data.dart';
import '../../../../utils/request/bdh_api.dart';

class AgriculturalService {
  /// 通过用户定位坐标查询用户归属地市区县接口
  static Future<RequestNoData> getUserRegion(params,
      {CancelToken? cancelToken}) async {
    var response = await agricultureHttp.post(
        "/servicshop/amMachineMaregas/queryUserRegion",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 行政区划下拉列表获取接口
  static Future<RequestNoData> queryAllZheRegion(params,
      {CancelToken? cancelToken}) async {
    var response = await agricultureHttp.post(
        "/servicshop/amMachineMaregas/queryAllZheRegion",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 维修保养，农机加油查询接口
  static Future<RequestNoData> queryMachineMaregasApi(params,
      {CancelToken? cancelToken}) async {
    var response = await agricultureHttp.post(
        "/servicshop/amMachineMaregas/queryMachineMaregas",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 获取农机资讯接口
  static Future<RequestNoData> queryInformationApi(params,
      {CancelToken? cancelToken}) async {
    var response = await agricultureHttp.post("/publish/queryList",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 获取农机资讯详情接口
  static Future<RequestNoData> queryInformationDetail(params, String id,
      {CancelToken? cancelToken}) async {
    var response = await agricultureHttp.post("/publish/query/info/$id",
        data: params, cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 获取购置补贴
  static Future<RequestNoData> querySubsidyInfo(params,
      {CancelToken? cancelToken}) async {
    var response = await agricultureHttp.post(
        "/subsidy/amPurchaseSubsidy/querySubsidyInfo",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 获取深耕作业补贴
  static Future<RequestNoData> queryDeepSubsidyInfo(params,
      {CancelToken? cancelToken}) async {
    var response = await agricultureHttp.post(
        "/subsidy/amPurchaseSubsidy/querySubsoilingSup",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  /// 获取免播种补贴
  static Future<RequestNoData> queryNoTillageSubsidies(params,
      {CancelToken? cancelToken}) async {
    var response = await agricultureHttp.post(
        "/subsidy/amPurchaseSubsidy/noTillageSubsidies",
        data: params,
        cancelToken: cancelToken);

    return RequestNoData.fromJson(response.data);
  }

  static Future<OrgTreeResult> getOrgList(params,
      {CancelToken? cancelToken}) async {
    var response = await samicHttp.post("/org/amporg/queryOrgTreeByUserOrg",
        data: params, cancelToken: cancelToken);

    return OrgTreeResult.fromJson(response.data);
  }

  //字典接口
  static Future<DictList> getDicByKey(String key) async {
    var response = await ssoHttp.post("/sso/dict/list/$key");
    return DictList.fromJson(response.data);
  }
}
