import 'package:bdh_smart_agric_app/generated/assets.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/agricultural_menu/agricultural_menu_view.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'agricultural_manager_logic.dart';
import 'agricultural_manager_state.dart';

/// @file agricultural_manager_view.dart
/// 农机管家菜单主界面
/// <AUTHOR>
/// @date 2025/6/5 11:28
/// @description
///
///
class AgriculturalManagerPage extends StatefulWidget {
  const AgriculturalManagerPage({Key? key}) : super(key: key);

  @override
  State<AgriculturalManagerPage> createState() =>
      _AgriculturalManagerPageState();
}

class _AgriculturalManagerPageState extends State<AgriculturalManagerPage>
    with SingleTickerProviderStateMixin {
  final AgriculturalManagerLogic logic = Get.put(AgriculturalManagerLogic());
  final AgriculturalManagerState state =
      Get.find<AgriculturalManagerLogic>().state;
  late TabController tabController;
  late PageController pageController;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: state.tabs.length, vsync: this);
    pageController = PageController(initialPage: 0, keepPage: true);
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints cons) {
      return Scaffold(
          backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
          body: SizedBox(
              width: cons.maxWidth,
              height: cons.maxHeight,
              child: Stack(alignment: Alignment.topCenter, children: [
                Positioned(
                  child: Container(
                    height: 240.px,
                    width: 375.px,
                    decoration: const BoxDecoration(
                        image: DecorationImage(
                      fit: BoxFit.cover,
                      image: AssetImage(Assets.agriculturalMainTopBg),
                    )),
                  ),
                ),
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: 36.px, left: 8.px),
                          child: BackButton(
                            color: Colors.white,
                            onPressed: () {
                              Navigator.pop(context);
                            },
                          ),
                        )
                      ]),
                ),
                Positioned(
                  top: 10.px,
                  child: Container(
                    margin: EdgeInsets.only(top: 36.px, left: 8.px),
                    child: Text("农机管家",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 18.px,
                            fontWeight: FontWeight.w600)),
                  ),
                ),
                Positioned(
                    top: 80.px,
                    child: Column(
                      children: [
                        SizedBox(
                          width: 375.px,
                          height: 40.px,
                          child: TabBar(
                              controller: tabController,
                              indicatorColor: Colors.transparent,
                              indicatorSize: TabBarIndicatorSize.label,
                              indicatorWeight: 2.px,
                              indicatorPadding: EdgeInsets.only(bottom: 4.px),
                              dividerHeight: 0.px,
                              dividerColor: Colors.transparent,
                              labelColor: Colors.white,
                              onTap: (index) {
                                pageController.animateToPage(index,
                                    duration: const Duration(milliseconds: 300),
                                    curve: Curves.easeInOut);
                              },
                              labelStyle: TextStyle(
                                  fontSize: 16.px, fontWeight: FontWeight.w600),
                              unselectedLabelStyle: TextStyle(
                                fontSize: 14.px,
                                color: const Color.fromRGBO(255, 255, 255, 0.6),
                              ),
                              tabs:
                                  state.tabs.map((e) => Tab(text: e)).toList()),
                        ),
                      ],
                    )),
                Positioned(
                  top: 116.px,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: PageView(
                    controller: pageController,
                    onPageChanged: (index) {
                      tabController.animateTo(index);
                    },
                    children: state.tabs.map((e) => buildPageView(e)).toList(),
                  ),
                )
              ])));
    });
  }

  Widget buildPageView(String tabTitle) {
    if (tabTitle == '撮合') {
      return const AgriculturalMenuPage();
    }
    return Container(
        color: Colors.white,
        child: const Center(
          child: Text("暂未实现,敬请期待", style: TextStyle(fontSize: 16)),
        ));
  }

  @override
  void dispose() {
    Get.delete<AgriculturalManagerLogic>();
    pageController.dispose();
    tabController.dispose();
    super.dispose();
  }
}
