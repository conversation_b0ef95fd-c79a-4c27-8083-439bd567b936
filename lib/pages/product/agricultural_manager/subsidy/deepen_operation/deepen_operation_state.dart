import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../api_server/entity/deep_operation_entity.dart';

class DeepenOperationState {
  Color selectedColor = const Color.fromRGBO(2, 139, 93, 1);
  Color normalColor = const Color.fromRGBO(41, 41, 52, 1);

  RxString year = ''.obs;
  int currentYearIndex = -1;
  int currentSelectedYearIndex = -1;

  RxBool isLoading = true.obs;

  List<dynamic> selectYears = [];

  RxString workEnd = ''.obs;
  RxString workStart = ''.obs;

  RxList<DeepOperationRecord> dataRecords = <DeepOperationRecord>[].obs;
  RxDouble totalEligibilityArea = 0.0.obs;
  RxDouble workArea = 0.0.obs;
  RxDouble deepEligibilityArea = 0.0.obs;
  RxDouble recoverArea = 0.0.obs;

  DeepenOperationState() {
    ///Initialize variables
    year.value = DateTime.now().year.toString();
  }
}
