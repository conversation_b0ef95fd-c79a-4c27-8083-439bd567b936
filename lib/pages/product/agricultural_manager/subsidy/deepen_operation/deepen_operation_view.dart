import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/deep_operation_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/subsidy/deepen_operation/deep_operation_detail_page.dart';
import 'package:bdh_smart_agric_app/utils/date_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:date_picker_plus/date_picker_plus.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import '../../../../../generated/assets.dart';
import '../../../../../utils/color_util.dart';
import '../../../../message/bdh_empty_View.dart';
import 'deepen_operation_logic.dart';
import 'deepen_operation_state.dart';

/// 深耕作用补贴
class DeepenOperationPage extends StatefulWidget {
  const DeepenOperationPage({Key? key}) : super(key: key);

  @override
  State<DeepenOperationPage> createState() => _DeepenOperationPageState();
}

class _DeepenOperationPageState extends State<DeepenOperationPage> {
  final DeepenOperationLogic logic = Get.put(DeepenOperationLogic());
  final DeepenOperationState state = Get.find<DeepenOperationLogic>().state;

  FixedExtentScrollController? yearSelectionController;

  List<DateTime?> _rangeDatePickerValueWithDefaultValue = [
    DateTime(1999, 5, 6),
    DateTime(1999, 5, 21),
  ];

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints cons) {
      return Scaffold(
          backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
          body: Obx(() {
            return SizedBox(
                width: cons.maxWidth,
                height: cons.maxHeight,
                child: Stack(alignment: Alignment.topCenter, children: [
                  Positioned(
                    child: Container(
                      height: 285.px,
                      width: 375.px,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        fit: BoxFit.cover,
                        image: AssetImage(Assets.subsidyIcDeepenOperationHead),
                      )),
                    ),
                  ),
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 36.px, left: 8.px),
                            child: BackButton(
                              color: Colors.white,
                              onPressed: () {
                                Navigator.pop(context);
                              },
                            ),
                          )
                        ]),
                  ),
                  Positioned(
                    top: 139.5.px,
                    left: 8.px,
                    child: Container(
                      height: 24.px,
                      margin: EdgeInsets.only(right: 15.px, left: 15.px),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(20.px),
                      ),
                      child: TextButton.icon(
                          iconAlignment: IconAlignment.end,
                          onPressed: () {
                            showModalBottomSheet(
                                context: context,
                                elevation: 10,
                                enableDrag: false,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.px)),
                                builder: (BuildContext context) {
                                  return showBottomSelectYearsPicker();
                                });
                          },
                          style: ButtonStyle(
                            padding: MaterialStateProperty.all(
                                EdgeInsets.symmetric(
                                    horizontal: 4.px, vertical: 0.px)),
                            shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                    borderRadius:
                                        BorderRadius.circular(10.px))),
                          ),
                          label: Text(state.year.value,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 12.px,
                                fontWeight: FontWeight.w400,
                              )),
                          icon: Image.asset(
                            Assets.agriculturalIcArrowDown,
                            color: Colors.white,
                            width: 10.px,
                            height: 10.px,
                          )),
                    ),
                  ),
                  Positioned(
                    top: 139.5.px,
                    left: 78.px,
                    child: InkWell(
                      onTap: () {
                        showModalBottomSheet(
                            context: context,
                            elevation: 10,
                            enableDrag: false,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8.px)),
                            builder: (BuildContext context) {
                              return showDatePicker();
                            });
                      },
                      child: Container(
                        height: 24.px,
                        width: 192.px,
                        margin: EdgeInsets.only(left: 15.px),
                        padding: EdgeInsets.symmetric(horizontal: 8.px),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.15),
                          borderRadius: BorderRadius.circular(20.px),
                        ),
                        child: Row(
                          children: [
                            Text(
                                state.workStart.value.isEmpty
                                    ? "请选择日期范围"
                                    : "${state.workStart.value} - ${state.workEnd.value}",
                                textAlign: TextAlign.left,
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 12.px,
                                  fontWeight: FontWeight.w400,
                                )),
                            const Spacer(),
                            state.workStart.value.isEmpty
                                ? Image.asset(
                                    Assets.agriculturalIcArrowDown,
                                    color: Colors.white,
                                    width: 10.px,
                                    height: 10.px,
                                  )
                                : InkWell(
                                    onTap: () {
                                      state.workStart.value = "";
                                      state.workEnd.value = "";
                                      logic.getDateRecords();
                                    },
                                    child: Icon(
                                      CupertinoIcons.clear_circled,
                                      color: Colors.white70,
                                      size: 16.px,
                                    ),
                                  ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    left: 12.px,
                    right: 12.px,
                    top: 177.5.px,
                    child: Container(
                        height: 178.5.px,
                        width: 351.px,
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                          fit: BoxFit.cover,
                          image: AssetImage(Assets.subsidyIcDeepenCenter),
                        )),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(
                                height: 12.px,
                              ),
                              Text("作业合格总面积(亩)",
                                  style: TextStyle(
                                      fontSize: 14.px,
                                      fontWeight: FontWeight.w500,
                                      color: state.normalColor)),
                              Text(
                                  state.totalEligibilityArea.value
                                      .toStringAsFixed(2),
                                  style: TextStyle(
                                      fontSize: 28.px,
                                      fontFamily: "BEBAS",
                                      fontWeight: FontWeight.w500,
                                      color: state.selectedColor)),
                              SizedBox(
                                height: 8.px,
                              ),
                              const Divider(
                                indent: 22.5,
                                endIndent: 22.5,
                                thickness: 1.5,
                                color: Color.fromRGBO(227, 229, 234, 1),
                              ),
                              SizedBox(
                                height: 5.px,
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceEvenly,
                                children: [
                                  SizedBox(
                                    width: 108.px,
                                    child: Column(
                                      children: [
                                        Text("作业总面积(亩)",
                                            style: TextStyle(
                                                fontSize: 12.px,
                                                fontWeight: FontWeight.w500,
                                                color: state.normalColor)),
                                        SizedBox(
                                          height: 10.px,
                                        ),
                                        Text(
                                            state.deepEligibilityArea.value
                                                .toStringAsFixed(2),
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontFamily: "BEBAS",
                                                fontWeight: FontWeight.w500,
                                                color: state.selectedColor)),
                                      ],
                                    ),
                                  ),
                                  Container(
                                      width: 1.px,
                                      height: 54.px,
                                      color: const Color.fromRGBO(
                                          217, 217, 217, 1)),
                                  SizedBox(
                                    width: 108.px,
                                    child: Column(
                                      children: [
                                        Text("深松达标总面积(亩)",
                                            style: TextStyle(
                                                fontSize: 12.px,
                                                fontWeight: FontWeight.w500,
                                                color: state.normalColor)),
                                        SizedBox(
                                          height: 10.px,
                                        ),
                                        Text(
                                            state.deepEligibilityArea.value
                                                .toStringAsFixed(2),
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontFamily: "BEBAS",
                                                fontWeight: FontWeight.w500,
                                                color: state.selectedColor)),
                                      ],
                                    ),
                                  ),
                                  Container(
                                      width: 1.px,
                                      height: 54.px,
                                      color: const Color.fromRGBO(
                                          217, 217, 217, 1)),
                                  SizedBox(
                                    width: 108.px,
                                    child: Column(
                                      children: [
                                        Text("重复总面积(亩)",
                                            style: TextStyle(
                                                fontSize: 12.px,
                                                fontWeight: FontWeight.w500,
                                                color: state.normalColor)),
                                        SizedBox(
                                          height: 10.px,
                                        ),
                                        Text(
                                            state.recoverArea.value
                                                .toStringAsFixed(2),
                                            style: TextStyle(
                                                fontSize: 16.px,
                                                fontFamily: "BEBAS",
                                                fontWeight: FontWeight.w500,
                                                color: state.selectedColor)),
                                      ],
                                    ),
                                  ),
                                ],
                              )
                            ])),
                  ),
                  Positioned(
                    top: 374.px,
                    left: 12.px,
                    right: 0,
                    child: Row(
                      children: [
                        Container(
                          width: 4.px,
                          height: 16.px,
                          margin: EdgeInsets.only(right: 10.px),
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.all(Radius.circular(2.px)),
                            color: state.selectedColor,
                          ),
                        ),
                        Text(
                          "补贴明细",
                          style: TextStyle(
                            fontSize: 16.px,
                            fontWeight: FontWeight.w500,
                            color: state.normalColor,
                          ),
                        )
                      ],
                    ),
                  ),
                  Positioned(
                    top: 411.px,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Container(
                      width: 375.px,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(6.px),
                          topRight: Radius.circular(6.px),
                        ),
                      ),
                      margin: EdgeInsets.only(left: 12.px, right: 12.px),
                      child: state.dataRecords.isEmpty
                          ? SizedBox(
                              height: 200.px,
                              child: Center(
                                child: state.isLoading.value == true
                                    ? SpinKitCircle(
                                        color: HexColor('#16B760'),
                                        size: 50.0,
                                      )
                                    : const BdhEmptyView(),
                              ),
                            )
                          : ListView.builder(
                              padding: EdgeInsets.only(top: 0.px),
                              itemCount: state.dataRecords.length,
                              itemBuilder: (context, index) {
                                return Container(
                                    child: buildInfoItem(context,
                                        state.dataRecords.elementAt(index)));
                              }),
                    ),
                  ),
                ]));
          }));
    });
  }

  Widget buildInfoItem(BuildContext context, DeepOperationRecord item) {
    return InkWell(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => DeepOperationDetailPage(
                      dateItem: item,
                    )));
      },
      child: Container(
          padding: EdgeInsets.only(
              left: 16.px, right: 16.px, top: 16.5.px, bottom: 16.5.px),
          margin: EdgeInsets.only(bottom: 5.px, top: 5.px),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(6.px)),
          ),
          child: Column(children: [
            Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Container(
                margin: EdgeInsets.only(bottom: 12.px),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 318.px,
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(item.machineNo ?? "",
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                              style: TextStyle(
                                  fontSize: 18.px,
                                  fontWeight: FontWeight.w600,
                                  color: state.normalColor)),
                          Text(item.workDate ?? "",
                              style: TextStyle(
                                  fontSize: 12.px,
                                  fontWeight: FontWeight.w400,
                                  color: state.normalColor.withOpacity(0.6))),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 6.px,
                    ),
                    Text(
                        "${item.amTypeName1 ?? ""} | ${item.amTypeName2 ?? ""}| ${item.amTypeName3 ?? ""}",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 12.px,
                            fontWeight: FontWeight.w400,
                            color: state.normalColor.withOpacity(0.8))),
                  ],
                ),
              ),
            ]),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.px, vertical: 15.px),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                      width: 0.5.px,
                      color: const Color.fromRGBO(243, 245, 249, 0.5)),
                ),
                borderRadius: BorderRadius.all(Radius.circular(4.px)),
                color: const Color.fromRGBO(243, 245, 249, 0.5),
              ),
              child: Column(children: [
                Row(children: [
                  Text("合格面积(亩)",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: state.normalColor.withOpacity(0.7))),
                  const Spacer(),
                  Text(item.qarea?.toStringAsFixed(2) ?? "",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: state.normalColor))
                ]),
                SizedBox(
                  height: 6.px,
                ),
                Row(children: [
                  Text("作业面积(亩)",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: state.normalColor.withOpacity(0.7))),
                  const Spacer(),
                  Text(item.area?.toStringAsFixed(2) ?? "",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: state.normalColor))
                ]),
                SizedBox(height: 6.px),
                Row(children: [
                  Text("深度达标面积(亩)",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: state.normalColor.withOpacity(0.7))),
                  const Spacer(),
                  Text(item.depQualify?.toStringAsFixed(2) ?? "0.00",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: state.normalColor))
                ]),
                SizedBox(height: 6.px),
                Row(children: [
                  Text("重复面积(亩)",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: state.normalColor.withOpacity(0.7))),
                  const Spacer(),
                  Text(item.repeatArea?.toStringAsFixed(2) ?? "0.00",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: state.normalColor))
                ]),
              ]),
            )
          ])),
    );
  }

  Widget showBottomSelectYearsPicker() {
    if (state.currentYearIndex == -1) {
      state.currentYearIndex = state.selectYears
          .indexWhere((item) => item["name"] == state.year.value);
      yearSelectionController =
          FixedExtentScrollController(initialItem: state.currentYearIndex);
    } else {
      yearSelectionController =
          FixedExtentScrollController(initialItem: state.currentYearIndex);
    }
    return BottomSheet(
        enableDrag: false,
        onClosing: () {},
        builder: (BuildContext context) {
          return Container(
              padding: EdgeInsets.only(left: 10.px, right: 10.px, top: 10.px),
              height: 280.px,
              width: 375.px,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "取消",
                              style: TextStyle(
                                  fontSize: 16.px, color: Colors.redAccent),
                            )),
                        Text(
                          "选择年份",
                          style: TextStyle(
                              color: const Color.fromRGBO(44, 44, 52, 1),
                              fontSize: 18.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextButton(
                            onPressed: () {
                              setState(() {
                                state.currentYearIndex =
                                    state.currentSelectedYearIndex;
                                state.year.value =
                                    state.selectYears[state.currentYearIndex]
                                        ['name'];
                              });
                              logic.getDateRecords();
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "确定",
                              style: TextStyle(
                                  color: Colors.blueAccent, fontSize: 16.px),
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 220.px,
                      child: CupertinoPicker(
                          scrollController: yearSelectionController,
                          itemExtent: 40.px,
                          squeeze: 1.5,
                          diameterRatio: 1,
                          onSelectedItemChanged: (index) {
                            setState(() {
                              state.currentSelectedYearIndex = index;
                            });
                          },
                          children: state.selectYears
                              .map((item) => Center(
                                    child: Text(item['name']),
                                  ))
                              .toList()),
                    )
                  ]));
        });
  }

  Widget showDatePicker() {
    DateTimeRange? selectedRange;
    if (state.workStart.value.isNotEmpty) {
      selectedRange = DateTimeRange(
          start: DateTime.parse(state.workStart.value),
          end: DateTime.parse(state.workEnd.value));
    }

    return BottomSheet(
        enableDrag: false,
        onClosing: () {},
        builder: (BuildContext context) {
          return SizedBox(
            width: 375.px,
            child: RangeDatePicker(
              centerLeadingDate: true,
              minDate: DateTime(1980, 1, 1),
              maxDate: DateTime.now(),
              selectedRange: selectedRange,
              slidersSize: 26,
              splashColor: Colors.lightBlueAccent,
              selectedCellsTextStyle: const TextStyle(),
              splashRadius: 40,
              enabledCellsTextStyle:
                  TextStyle(color: Colors.black, fontSize: 16.px),
              disabledCellsTextStyle:
                  TextStyle(color: Colors.grey, fontSize: 16.px),
              leadingDateTextStyle:
                  TextStyle(color: Colors.black, fontSize: 20.px),
              onRangeSelected: (value) {
                // Handle selected range
                Navigator.of(context).pop();
                state.workStart.value =
                    DateUtil.dateTimeFormat(value.start, "yyyy-MM-dd");
                state.workEnd.value =
                    DateUtil.dateTimeFormat(value.end, "yyyy-MM-dd");
                logic.getDateRecords();
              },
            ),
          );
        });
  }

  @override
  void dispose() {
    Get.delete<DeepenOperationLogic>();
    super.dispose();
  }
}
