import 'package:get/get.dart';

import '../../../../../model/user_info_model.dart';
import '../../../../../utils/request/online_pay_service.dart';
import '../../../../../utils/storage_util.dart';
import '../../api_server/agricultural_service.dart';
import '../../api_server/entity/deep_operation_entity.dart';
import 'deepen_operation_state.dart';

class DeepenOperationLogic extends GetxController {
  final DeepenOperationState state = DeepenOperationState();

  @override
  void onReady() {
    super.onReady();
    getSelectYears();
    getDateRecords();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void getSelectYears() {
    OnlinePayResponsitory.querySelectYears({}).then((result) {
      if (result.success!) {
        List<dynamic> data = result.data;
        state.selectYears = data;
      }
    });
  }

  void getDateRecords() {
    state.isLoading.value = true;
    UserInfo? userInfo = StorageUtil.userInfo();
    Map<String, dynamic> params = {
      "statYear": state.year.value,
      "tel": userInfo?.data?.telephone,
      "name": userInfo?.data?.staffName,
      "workEnd": state.workEnd.value,
      "workStart": state.workStart.value,
    };

    AgriculturalService.queryDeepSubsidyInfo(params).then((result) {
      state.isLoading.value = false;
      if (result.success == true) {
        DeepOperationEntity record = DeepOperationEntity.fromJson(result.data);
        state.dataRecords.value = record.subsoilingSupLst!;
        state.totalEligibilityArea.value = record.totalEligibilityArea!;
        state.workArea.value = record.workArea!;
        state.deepEligibilityArea.value = record.deepEligibilityArea!;
        state.recoverArea.value = record.recoverArea!;
      }
    });
  }
}
