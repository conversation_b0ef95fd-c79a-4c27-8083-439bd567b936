import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/purchase_subsidy_entity.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class PurchaseDetailPage extends StatefulWidget {
  final PurchaseSubsidyEntity dateItem;
  final String year;
  const PurchaseDetailPage(
      {super.key, required this.dateItem, required this.year});

  @override
  State<PurchaseDetailPage> createState() => _PurchaseDetailPageState();
}

class _PurchaseDetailPageState extends State<PurchaseDetailPage> {
  Color selectedColor = const Color.fromRGBO(2, 139, 93, 1);
  Color normalColor = const Color.fromRGBO(41, 41, 52, 1);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            '补贴详情',
            style: TextStyle(fontSize: 18.px, fontWeight: FontWeight.w500),
          ),
          backgroundColor: Colors.white,
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: Column(
          children: [
            buildInfoItem(context, widget.dateItem),
            Column(children: [
              Container(
                  padding: EdgeInsets.all(16.px),
                  margin: EdgeInsets.only(left: 12.px, right: 12.px),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(8.px)),
                      color: Colors.white),
                  child: Column(children: [
                    Row(children: [
                      Text("归属单位",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.orgName ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("机具型号",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.amModelName ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("生产企业",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.companyName ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("购机日期",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.purchaseDate ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("销售总价(元)",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.singleSalePrice ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ])
                  ]))
            ])
          ],
        ));
  }

  Widget buildInfoItem(BuildContext context, PurchaseSubsidyEntity item) {
    return Container(
        padding: EdgeInsets.only(left: 16.px, right: 16.px, top: 16.5.px),
        margin: EdgeInsets.only(
            bottom: 14.px, top: 14.px, left: 12.px, right: 12.px),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(6.px)),
        ),
        child: Column(children: [
          Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Container(
              width: 210.px,
              height: 55.px,
              margin: EdgeInsets.only(bottom: 12.px),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        alignment: Alignment.center,
                        height: 19.px,
                        margin: EdgeInsets.only(right: 4.px),
                        padding: EdgeInsets.symmetric(horizontal: 2.px),
                        decoration: BoxDecoration(
                          color: selectedColor.withOpacity(0.2),
                          borderRadius: BorderRadius.all(Radius.circular(4.px)),
                        ),
                        child: Text("${widget.year}年" ?? "",
                            style: TextStyle(
                                fontSize: 12.px,
                                fontWeight: FontWeight.w400,
                                color: selectedColor)),
                      ),
                      SizedBox(
                        width: 160.px,
                        child: Text(item.amModelName ?? "",
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                                fontSize: 18.px,
                                fontWeight: FontWeight.w600,
                                color: normalColor)),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Text(
                      "${item.amTypeName1 ?? ""} | ${item.amTypeName2 ?? ""}| ${item.amTypeName3 ?? ""}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: normalColor.withOpacity(0.8))),
                ],
              ),
            ),
            Spacer(),
            Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
              Container(
                alignment: Alignment.center,
                height: 19.px,
                margin: EdgeInsets.only(bottom: 8.px),
                padding: EdgeInsets.symmetric(horizontal: 8.px),
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(220, 143, 0, 0.1),
                  borderRadius: BorderRadius.all(Radius.circular(9.5.px)),
                ),
                child: Text(item.stateName ?? "",
                    style: TextStyle(
                        fontSize: 12.px,
                        fontWeight: FontWeight.w400,
                        color: const Color.fromRGBO(220, 143, 0, 1))),
              ),
              Text(item.totalSubsidy ?? "",
                  style: TextStyle(
                      fontSize: 24.px,
                      fontFamily: "BEBAS",
                      fontWeight: FontWeight.w400,
                      color: selectedColor)),
            ])
          ]),
        ]));
  }
}
