import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/purchase_subsidy_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/subsidy/purchase/purchase_detail_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

import '../../../../../generated/assets.dart';
import '../../../../../utils/color_util.dart';
import '../../../../message/bdh_empty_View.dart';
import 'purchase_logic.dart';
import 'purchase_state.dart';

/// 购置补贴
class PurchasePage extends StatefulWidget {
  const PurchasePage({Key? key}) : super(key: key);

  @override
  State<PurchasePage> createState() => _PurchasePageState();
}

class _PurchasePageState extends State<PurchasePage> {
  final PurchaseLogic logic = Get.put(PurchaseLogic());
  final PurchaseState state = Get.find<PurchaseLogic>().state;
  FixedExtentScrollController? yearSelectionController;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints cons) {
      return Scaffold(
          backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
          body: Obx(() {
            return SizedBox(
                width: cons.maxWidth,
                height: cons.maxHeight,
                child: Stack(alignment: Alignment.topCenter, children: [
                  Positioned(
                    child: Container(
                      height: 285.px,
                      width: 375.px,
                      decoration: const BoxDecoration(
                          image: DecorationImage(
                        fit: BoxFit.cover,
                        image: AssetImage(Assets.subsidyIcPurchaseHead),
                      )),
                    ),
                  ),
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 36.px, left: 8.px),
                            child: BackButton(
                              color: Colors.white,
                              onPressed: () {
                                Navigator.pop(context);
                              },
                            ),
                          )
                        ]),
                  ),
                  Positioned(
                    top: 139.5.px,
                    left: 8.px,
                    child: Container(
                      height: 24.px,
                      margin: EdgeInsets.only(right: 15.px, left: 15.px),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(20.px),
                      ),
                      child: TextButton.icon(
                          iconAlignment: IconAlignment.end,
                          onPressed: () {
                            showModalBottomSheet(
                                context: context,
                                elevation: 10,
                                enableDrag: false,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8.px)),
                                builder: (BuildContext context) {
                                  return showBottomSelectYearsPicker();
                                });
                          },
                          style: ButtonStyle(
                            padding: MaterialStateProperty.all(
                                EdgeInsets.symmetric(
                                    horizontal: 4.px, vertical: 0.px)),
                            shape: MaterialStateProperty.all(
                                RoundedRectangleBorder(
                                    borderRadius:
                                        BorderRadius.circular(10.px))),
                          ),
                          label: Text(state.year.value,
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.8),
                                fontSize: 12.px,
                                fontWeight: FontWeight.w400,
                              )),
                          icon: Image.asset(
                            Assets.agriculturalIcArrowDown,
                            color: Colors.white,
                            width: 10.px,
                            height: 10.px,
                          )),
                    ),
                  ),
                  Positioned(
                    left: 12.px,
                    right: 12.px,
                    top: 177.5.px,
                    child: Container(
                        height: 107.px,
                        width: 351.px,
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                          fit: BoxFit.cover,
                          image: AssetImage(Assets.subsidyIcCenterBg),
                        )),
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text("当年补贴总额(元)",
                                  style: TextStyle(
                                      fontSize: 14.px,
                                      fontWeight: FontWeight.w500,
                                      color: state.normalColor)),
                              Text(state.total.value.toStringAsFixed(2),
                                  style: TextStyle(
                                      fontSize: 28.px,
                                      fontFamily: "BEBAS",
                                      fontWeight: FontWeight.w500,
                                      color: state.selectedColor)),
                            ])),
                  ),
                  Positioned(
                    top: 305.px,
                    left: 12.px,
                    right: 0,
                    child: Row(
                      children: [
                        Container(
                          width: 4.px,
                          height: 16.px,
                          margin: EdgeInsets.only(right: 10.px),
                          decoration: BoxDecoration(
                            borderRadius:
                                BorderRadius.all(Radius.circular(2.px)),
                            color: state.selectedColor,
                          ),
                        ),
                        Text(
                          "补贴明细",
                          style: TextStyle(
                            fontSize: 16.px,
                            fontWeight: FontWeight.w500,
                            color: state.normalColor,
                          ),
                        )
                      ],
                    ),
                  ),
                  Positioned(
                    top: 339.5.px,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Container(
                      width: 375.px,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(6.px),
                          topRight: Radius.circular(6.px),
                        ),
                      ),
                      margin: EdgeInsets.only(left: 12.px, right: 12.px),
                      child: state.dataRecords.isEmpty
                          ? SizedBox(
                              height: 200.px,
                              child: Center(
                                child: state.isLoading.value == true
                                    ? SpinKitCircle(
                                        color: HexColor('#16B760'),
                                        size: 50.0,
                                      )
                                    : const BdhEmptyView(),
                              ),
                            )
                          : ListView.builder(
                              padding: EdgeInsets.only(top: 0.px),
                              itemCount: state.dataRecords.length,
                              itemBuilder: (context, index) {
                                return Container(
                                    child: buildInfoItem(context,
                                        state.dataRecords.elementAt(index)));
                              }),
                    ),
                  ),
                ]));
          }));
    });
  }

  Widget buildInfoItem(BuildContext context, PurchaseSubsidyEntity item) {
    return InkWell(
      onTap: () {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => PurchaseDetailPage(
                      dateItem: item,
                      year: state.year.value,
                    )));
      },
      child: Container(
          padding: EdgeInsets.only(left: 16.px, right: 16.px, top: 16.5.px),
          margin: EdgeInsets.only(bottom: 5.px, top: 5.px),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(6.px)),
          ),
          child: Column(children: [
            Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Container(
                width: 210.px,
                height: 85.px,
                margin: EdgeInsets.only(bottom: 12.px),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(item.amModelName ?? "",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 18.px,
                            fontWeight: FontWeight.w600,
                            color: state.normalColor)),
                    SizedBox(
                      height: 6.px,
                    ),
                    Text(
                        "${item.amTypeName1 ?? ""} | ${item.amTypeName2 ?? ""}| ${item.amTypeName3 ?? ""}",
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            fontSize: 12.px,
                            fontWeight: FontWeight.w400,
                            color: state.normalColor.withOpacity(0.8))),
                    SizedBox(
                      height: 12.px,
                    ),
                    Text(item.purchaseDate ?? "",
                        style: TextStyle(
                            fontSize: 12.px,
                            fontWeight: FontWeight.w400,
                            color: state.normalColor.withOpacity(0.6))),
                  ],
                ),
              ),
              const Spacer(),
              Column(crossAxisAlignment: CrossAxisAlignment.end, children: [
                Container(
                  alignment: Alignment.center,
                  height: 19.px,
                  margin: EdgeInsets.only(bottom: 12.px),
                  padding: EdgeInsets.symmetric(horizontal: 8.px),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(220, 143, 0, 0.1),
                    borderRadius: BorderRadius.all(Radius.circular(9.5.px)),
                  ),
                  child: Text(item.stateName ?? "",
                      style: TextStyle(
                          fontSize: 12.px,
                          fontWeight: FontWeight.w400,
                          color: const Color.fromRGBO(220, 143, 0, 1))),
                ),
                SizedBox(height: 17.5.px),
                Text(item.totalSubsidy ?? "",
                    style: TextStyle(
                        fontSize: 24.px,
                        fontFamily: "BEBAS",
                        fontWeight: FontWeight.w400,
                        color: state.selectedColor)),
              ])
            ]),
          ])),
    );
  }

  Widget showBottomSelectYearsPicker() {
    if (state.currentYearIndex == -1) {
      state.currentYearIndex = state.selectYears
          .indexWhere((item) => item["name"] == state.year.value);
      yearSelectionController =
          FixedExtentScrollController(initialItem: state.currentYearIndex);
    } else {
      yearSelectionController =
          FixedExtentScrollController(initialItem: state.currentYearIndex);
    }
    return BottomSheet(
        enableDrag: false,
        onClosing: () {},
        builder: (BuildContext context) {
          return Container(
              padding: EdgeInsets.only(left: 10.px, right: 10.px, top: 10.px),
              height: 280.px,
              width: 375.px,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextButton(
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "取消",
                              style: TextStyle(
                                  fontSize: 16.px, color: Colors.redAccent),
                            )),
                        Text(
                          "选择年份",
                          style: TextStyle(
                              color: const Color.fromRGBO(44, 44, 52, 1),
                              fontSize: 18.px,
                              fontWeight: FontWeight.w500),
                        ),
                        TextButton(
                            onPressed: () {
                              setState(() {
                                state.currentYearIndex =
                                    state.currentSelectedYearIndex;
                                state.year.value =
                                    state.selectYears[state.currentYearIndex]
                                        ['name'];
                              });
                              logic.getDateRecords();
                              Navigator.of(context).pop();
                            },
                            child: Text(
                              "确定",
                              style: TextStyle(
                                  color: Colors.blueAccent, fontSize: 16.px),
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 220.px,
                      child: CupertinoPicker(
                          scrollController: yearSelectionController,
                          itemExtent: 40.px,
                          squeeze: 1.5,
                          diameterRatio: 1,
                          onSelectedItemChanged: (index) {
                            setState(() {
                              state.currentSelectedYearIndex = index;
                            });
                          },
                          children: state.selectYears
                              .map((item) => Center(
                                    child: Text(item['name']),
                                  ))
                              .toList()),
                    )
                  ]));
        });
  }

  @override
  void dispose() {
    Get.delete<PurchaseLogic>();
    super.dispose();
  }
}
