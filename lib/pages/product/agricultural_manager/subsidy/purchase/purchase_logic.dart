import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/purchase_subsidy_entity.dart';
import 'package:get/get.dart';

import '../../../../../model/user_info_model.dart';
import '../../../../../utils/request/online_pay_service.dart';
import '../../../../../utils/storage_util.dart';
import '../../api_server/agricultural_service.dart';
import 'purchase_state.dart';

class PurchaseLogic extends GetxController {
  final PurchaseState state = PurchaseState();

  @override
  void onReady() {
    super.onReady();
    getSelectYears();
    getDateRecords();
  }

  void getDateRecords() async {
    state.isLoading.value = true;
    UserInfo? userInfo = StorageUtil.userInfo();
    Map<String, dynamic> params = {
      "year": state.year.value,
      "certNo": userInfo?.data?.idCard
    };

    AgriculturalService.querySubsidyInfo(params).then((result) {
      state.isLoading.value = false;
      if (result.success == true) {
        state.total.value = result.data['sum'].toDouble();
        List<PurchaseSubsidyEntity> records =
            (result.data['subsidyVoLst'] as List<dynamic>)
                .map((item) => PurchaseSubsidyEntity.fromJson(item))
                .toList();
        state.dataRecords.value = records;
      }
    });
  }

  void getSelectYears() {
    OnlinePayResponsitory.querySelectYears({}).then((result) {
      if (result.success!) {
        List<dynamic> data = result.data;
        state.selectYears = data;
      }
    });
  }

  @override
  void onClose() {
    super.onClose();
  }
}
