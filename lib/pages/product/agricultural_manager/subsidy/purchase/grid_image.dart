import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:ui' as ui;
import 'dart:typed_data';

import 'dart:io';

void main() => runApp(MaterialApp(home: GridImageExportPage()));

class GridImageExportPage extends StatefulWidget {
  @override
  _GridImageExportPageState createState() => _GridImageExportPageState();
}

class _GridImageExportPageState extends State<GridImageExportPage> {
  final int _rows = 3;
  final int _cols = 4;
  List<ui.Image> _images = [];
  bool _isProcessing = false;
  double _quality = 0.7;
  final GlobalKey _canvasKey = GlobalKey();

  Future<void> _pickAndCompress() async {
    final pickedFiles = await ImagePicker().pickMultiImage();
    if (pickedFiles.isEmpty) return;

    setState(() => _isProcessing = true);
    _images.clear();

    for (var file in pickedFiles.take(_rows * _cols)) {
      final bytes = await file.readAsBytes();
      final compressed = await FlutterImageCompress.compressWithList(
        bytes,
        quality: (_quality * 100).toInt(),
      );
      final codec = await ui.instantiateImageCodec(compressed);
      _images.add((await codec.getNextFrame()).image);
    }

    setState(() => _isProcessing = false);
  }

  Future<void> _exportImage() async {
    if (_images.isEmpty) return;
    setState(() => _isProcessing = true);

    final boundary =
        _canvasKey.currentContext?.findRenderObject() as RenderRepaintBoundary?;
    final image = await boundary?.toImage(pixelRatio: 3.0);
    final byteData = await image?.toByteData(format: ui.ImageByteFormat.png);

    if (byteData != null) {
      final dir = await getTemporaryDirectory();
      final file =
          File('${dir.path}/grid_${DateTime.now().millisecondsSinceEpoch}.png');
      await file.writeAsBytes(byteData.buffer.asUint8List());
      ScaffoldMessenger.of(context)
          .showSnackBar(SnackBar(content: Text('已保存到: ${file.path}')));
      print('已保存到: ${file.path}');
    }

    setState(() => _isProcessing = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('3x4图片网格导出')),
      body: Column(
        children: [
          Expanded(
            child: RepaintBoundary(
              key: _canvasKey,
              child: CustomPaint(
                painter: _GridPainter(_images, _rows, _cols),
                child: _isProcessing
                    ? Center(child: CircularProgressIndicator())
                    : _images.isEmpty
                        ? Center(child: Text('请选择图片'))
                        : Container(),
              ),
            ),
          ),
          Slider(
            value: _quality,
            onChanged: (v) => setState(() => _quality = v),
            label: '压缩质量 ${(_quality * 100).toInt()}%',
          ),
          Wrap(
            spacing: 10,
            children: [
              ElevatedButton(
                onPressed: _isProcessing ? null : _pickAndCompress,
                child: Text('选择图片'),
              ),
              ElevatedButton(
                onPressed:
                    _isProcessing || _images.isEmpty ? null : _exportImage,
                child: Text('导出图片'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _GridPainter extends CustomPainter {
  final List<ui.Image> images;
  final int rows;
  final int cols;

  _GridPainter(this.images, this.rows, this.cols);

  @override
  void paint(Canvas canvas, Size size) {
    if (images.isEmpty) return;

    final cellW = size.width / cols;
    final cellH = size.height / rows;
    var index = 0;

    for (var r = 0; r < rows; r++) {
      for (var c = 0; c < cols; c++) {
        if (index >= images.length) return;

        final img = images[index];
        final ratio = img.width / cellW;
        final drawH = img.height / ratio;
        final yOffset = (cellH - drawH) / 2;

        canvas.drawImageRect(
          img,
          Rect.fromLTWH(0, 0, img.width.toDouble(), img.height.toDouble()),
          Rect.fromLTWH(c * cellW, r * cellH + yOffset, cellW, drawH),
          Paint(),
        );
        index++;
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
