import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../api_server/entity/purchase_subsidy_entity.dart';

class PurchaseState {
  Color selectedColor = const Color.fromRGBO(2, 139, 93, 1);
  Color normalColor = const Color.fromRGBO(41, 41, 52, 1);

  RxString year = ''.obs;
  RxDouble total = 0.0.obs;
  int currentYearIndex = -1;
  int currentSelectedYearIndex = -1;

  RxBool isLoading = true.obs;

  List<dynamic> selectYears = [];

  RxList<PurchaseSubsidyEntity> dataRecords = <PurchaseSubsidyEntity>[].obs;

  PurchaseState() {
    ///Initialize variables
    year.value = DateTime.now().year.toString();
  }
}
