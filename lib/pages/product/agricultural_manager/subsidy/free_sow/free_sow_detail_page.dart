import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/free_sow_entity.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class FreeSowDetailPage extends StatefulWidget {
  final FreeSowResultList dateItem;
  const FreeSowDetailPage({super.key, required this.dateItem});

  @override
  State<FreeSowDetailPage> createState() => _FreeSowDetailState();
}

class _FreeSowDetailState extends State<FreeSowDetailPage> {
  Color selectedColor = const Color.fromRGBO(2, 139, 93, 1);
  Color normalColor = const Color.fromRGBO(41, 41, 52, 1);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            '补贴详情',
            style: TextStyle(fontSize: 18.px, fontWeight: FontWeight.w500),
          ),
          backgroundColor: Colors.white,
        ),
        backgroundColor: const Color.fromRGBO(243, 245, 249, 1),
        body: Column(
          children: [
            buildInfoItem(context, widget.dateItem),
            Column(children: [
              Container(
                  padding: EdgeInsets.all(16.px),
                  margin: EdgeInsets.only(left: 12.px, right: 12.px),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(8.px)),
                      color: Colors.white),
                  child: Column(children: [
                    Row(children: [
                      Text("归属单位",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.orgName ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("机具型号",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.amModelName ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("生成企业",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.companyName ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("作业地点",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.endAddress ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("作业类型",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.type ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("作业幅宽",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text("${widget.dateItem.wid}米",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("作业开始时间",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.start ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("作业完成时间",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.end ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("作业面积(亩)",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.area?.toStringAsFixed(2) ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                    SizedBox(height: 8.px),
                    Row(children: [
                      Text("合格面积(亩)",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor.withOpacity(0.7))),
                      const Spacer(),
                      Text(widget.dateItem.qarea?.toStringAsFixed(2) ?? "",
                          style: TextStyle(
                              fontSize: 14.px,
                              fontWeight: FontWeight.w400,
                              color: normalColor))
                    ]),
                  ]))
            ])
          ],
        ));
  }

  Widget buildInfoItem(BuildContext context, FreeSowResultList item) {
    return Container(
      margin:
          EdgeInsets.only(left: 12.px, right: 12.px, top: 14.px, bottom: 12.px),
      padding: EdgeInsets.only(
          left: 16.px, right: 16.px, top: 12.5.px, bottom: 8.px),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(6.px)),
      ),
      child: Column(children: [
        Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Container(
            margin: EdgeInsets.only(bottom: 12.px),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(item.amModelName ?? "",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: 18.px,
                        fontWeight: FontWeight.w600,
                        color: normalColor)),
                SizedBox(
                  height: 6.px,
                ),
                Text(
                    "${item.amTypeName1 ?? ""} | ${item.amTypeName2 ?? ""}| ${item.amTypeName3 ?? ""}",
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: 12.px,
                        fontWeight: FontWeight.w400,
                        color: normalColor.withOpacity(0.8))),
                SizedBox(
                  height: 9.px,
                ),
                Text(item.date ?? "",
                    style: TextStyle(
                        fontSize: 12.px,
                        fontWeight: FontWeight.w400,
                        color: normalColor.withOpacity(0.6))),
              ],
            ),
          ),
        ])
      ]),
    );
  }
}
