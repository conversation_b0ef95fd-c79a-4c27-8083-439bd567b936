import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/free_sow_entity.dart';
import 'package:get/get.dart';

import '../../../../../model/user_info_model.dart';
import '../../../../../utils/request/online_pay_service.dart';
import '../../../../../utils/storage_util.dart';
import '../../api_server/agricultural_service.dart';
import 'free_sow_state.dart';

class FreeSowLogic extends GetxController {
  final FreeSowState state = FreeSowState();

  @override
  void onReady() {
    getSelectYears();
    getDateRecords();
    super.onReady();
  }

  @override
  void onClose() {
    super.onClose();
  }

  void getDateRecords() async {
    state.isLoading.value = true;
    UserInfo? userInfo = StorageUtil.userInfo();
    Map<String, dynamic> params = {
      "year": state.year.value,
      "idCard": userInfo?.data?.idCard
    };

    AgriculturalService.queryNoTillageSubsidies(params).then((result) {
      state.isLoading.value = false;
      if (result.success == true) {
        FreeSowEntity record = FreeSowEntity.fromJson(result.data);
        state.total.value = record.totalSum!;
        state.dataRecords.value = record.resultList!;
      }
    });
  }

  void getSelectYears() {
    OnlinePayResponsitory.querySelectYears({}).then((result) {
      if (result.success!) {
        List<dynamic> data = result.data;
        state.selectYears = data;
      }
    });
  }
}
