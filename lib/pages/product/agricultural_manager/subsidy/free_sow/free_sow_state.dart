import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/free_sow_entity.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FreeSowState {
  Color selectedColor = const Color.fromRGBO(2, 139, 93, 1);
  Color normalColor = const Color.fromRGBO(41, 41, 52, 1);

  RxString year = ''.obs;
  RxDouble total = 0.0.obs;
  int currentYearIndex = -1;
  int currentSelectedYearIndex = -1;

  List<dynamic> selectYears = [];

  RxList<FreeSowResultList> dataRecords = <FreeSowResultList>[].obs;

  RxBool isLoading = true.obs;

  FreeSowState() {
    ///Initialize variables
    year.value = DateTime.now().year.toString();
  }
}
