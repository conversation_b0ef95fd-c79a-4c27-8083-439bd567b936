import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_dropdown_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_org_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_searchable_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalrecords/model/query_element_item.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/collection_extensions.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:oktoast/oktoast.dart';

import 'model/land_record.dart';
import 'batch_planting_plan.dart';
import 'request/agricultural_records_service.dart';

class BatchRecordingBlockPage extends StatefulWidget {
  final Map<String, dynamic> params;
  const BatchRecordingBlockPage({super.key, required this.params});

  @override
  State<BatchRecordingBlockPage> createState() =>
      _BatchRecordingBlockPageState();
}

class _BatchRecordingBlockPageState
    extends MixinUseState<BatchRecordingBlockPage> {
  late final controller = useController(_Controller(context, widget.params));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF3F5F9),
      body: Stack(
        children: [
          Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: Container(
                height: 300.px,
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                      Color.fromARGB(255, 94, 139, 245),
                      Color(0xFFF3F5F9),
                    ])),
              )),
          Column(
            children: [
              AppBar(
                title: const Text(
                  "批量记录地块",
                  style: TextStyle(color: Colors.white),
                ),
                leading: const BackButton(
                  color: Colors.white,
                ),
                foregroundColor: Colors.white,
                backgroundColor: Colors.transparent,
              ),
              Expanded(child: UseBuilder((context) {
                return UseBuilder((context) {
                  var status =
                      controller.loadingStatus.value ?? LoadingStatus.init;
                  switch (status) {
                    case LoadingStatus.loading:
                    case LoadingStatus.init:
                    case LoadingStatus.error:
                    case LoadingStatus.cancel:
                      return _widgetLoading();
                    case LoadingStatus.success:
                    case LoadingStatus.loadingMore:
                    case LoadingStatus.refreshing:
                      return _widgetBody();
                  }
                });
              }))
            ],
          )
        ],
      ),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    return Column(
      children: [
        widgetContainer(children: [
          UseBuilder(
            (context) => widgetOrgOption(
                title: "所属单位",
                initialValue: controller.chosenOrg.value,
                data: controller.orgDict.value ?? [],
                onChange: (v) {
                  controller.chosenOrg.value = v;
                }),
          ),
          UseBuilder((context) => widgetOption(
              title: "作物",
              initialValue: controller.chosenCorp.value,
              data: controller.corpDict,
              onChange: (v) {
                controller.chosenCorp.value = v;
              })),
          UseBuilder((context) => widgetOption(
              title: "种植方案",
              initialValue: controller.chosenPlantOption.value,
              data: controller.plantOptionDict.value ?? [],
              canShowPicker: () {
                if (controller.chosenOrg.value == null) {
                  showToast("请选择所属单位");
                  return false;
                }
                if (controller.chosenCorp.value == null) {
                  showToast("请选择作物");
                  return false;
                }
                if (controller.plantOptionDict.value?.isEmpty ?? true) {
                  showToast("当前作物无种植方案！");
                  return false;
                }
                return true;
              },
              onChange: (v) {
                controller.chosenPlantOption.value = v;
              })),
          UseBuilder((context) => widgetOption(
              title: "作业环节",
              showBottomLine: false,
              initialValue: controller.chosenLinkOption.value,
              data: controller.linkOptionDict.value ?? [],
              canShowPicker: () {
                if (controller.chosenOrg.value == null) {
                  showToast("请选择所属单位");
                  return false;
                }
                if (controller.chosenCorp.value == null) {
                  showToast("请选择作物");
                  return false;
                }
                if (controller.chosenPlantOption.value == null) {
                  showToast("请选种植方案");
                  return false;
                }
                if (controller.plantOptionDict.value?.isEmpty ?? true) {
                  showToast("当前作物无种植方案！");
                  return false;
                }
                if (controller.linkOptionDict.value?.isEmpty ?? true) {
                  showToast("当前种植方案无作业环节！");
                  return false;
                }

                return true;
              },
              onChange: (v) {
                controller.chosenLinkOption.value = v;
              })),
        ]),
        Expanded(child: UseBuilder((context) {
          return controller.showContentWidget
              ? _ContentWidget(
                  key: ValueKey(controller.contentWidgetKey),
                  chosenOrg: controller.chosenOrg.value,
                  chosenCorp: controller.chosenCorp.value,
                  chosenPlantOption: controller.chosenPlantOption.value,
                  chosenLinkOption: controller.chosenLinkOption.value,
                  statYear: controller.statYear,
                  raiseCrops: controller.raiseCrops,
                )
              : const SizedBox.shrink();
        }))
      ],
    );
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      width: 347.px,
      child: Column(
        children: children,
      ),
    );
  }

  Widget widgetHeightSpace({double? height}) {
    return SizedBox(
      height: height ?? 14.px,
    );
  }

  Widget widgetOrgOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      OrgTreeItem? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<OrgTreeItem>? data,
      void Function(OrgTreeItem?)? onChange}) {
    return BdhOrgPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 16.px,
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w600),
      initialValue: initialValue,
      placeholder: placeholder,
      showBottomLine: showBottomLine,
      onChange: onChange,
      checkState: true,
    );
  }

  Widget widgetOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DictNode? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(DictNode?)? onChange}) {
    return BdhSearchableSingleDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      initialValue: initialValue,
      placeholder: placeholder,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 16.px,
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w600),
      showBottomLine: showBottomLine,
      onChange: onChange,
      canShowPicker: canShowPicker,
      checkState: true,
    );
  }
}

class _Controller extends UseController {
  final Map<String, dynamic> params;
  late final List<DictNode> corpDict;
  late final String? statYear;
  late final String? raiseCrops;
  late final String? raiseText;
  _Controller(super.context, this.params) {
    statYear = params["statYear"];
    raiseCrops = params["raiseCrops"];
    raiseText = params["raiseText"];
    List<DictNode> tmpCorpDict = params["corpDict"] ?? [];
    tmpCorpDict.removeWhere((test) => test.code == "");
    corpDict = tmpCorpDict;
  }

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  late final orgDict = use<List<OrgTreeItem>>(null);
  late final plantOptionDict = use<List<DictNode>>(null);
  late final linkOptionDict = use<List<DictNode>>(null);

  late final chosenOrg = use<OrgTreeItem>(null);
  late final chosenCorp = use<DictNode>(null)
    ..onChange = (v) {
      plantOptionDict.value = null;
      linkOptionDict.value = null;
      chosenPlantOption.value = null;
      chosenLinkOption.value = null;
      loadPlantOptionDict();
    };
  late final chosenPlantOption = use<DictNode>(null)
    ..onChange = (v) {
      linkOptionDict.value = null;
      chosenLinkOption.value = null;
      loadLinkOptionDict();
    };
  late final chosenLinkOption = use<DictNode>(null);

  void loadPlantOptionDict() {
    var data = {
      "statYear": statYear,
      "orgCode": chosenOrg.peek()?.orgCode,
      "raiseCrops": chosenCorp.peek()?.code
    };
    showLoading(context, content: "正在加载..   ");
    AgriculturalRecordsService()
        .queryAllGrow(data: data, cancelToken: createCancelToken())
        .then((result) {
          if (result.success == true &&
              result.code == 0 &&
              result.data != null) {
            plantOptionDict.value = result.data
                    ?.map<DictNode>((v) => DictNode(
                        code: v["growPatternsId"].toString(),
                        name: v["growPatternsName"]))
                    .toList() ??
                [];
          }
        })
        .onError(handleError)
        .whenComplete(() {
          if (!context.mounted) {
            return;
          }
          hideLoading(context);
        });
  }

  void loadLinkOptionDict() {
    var data = {
      "statYear": statYear,
      "orgCode": chosenOrg.peek()?.orgCode,
      "raiseCrops": chosenCorp.peek()?.code,
      'growPatternsId': chosenPlantOption.peek()?.code
    };
    showLoading(context, content: "正在加载..   ");
    AgriculturalRecordsService()
        .queryAllGrowLink(data: data, cancelToken: createCancelToken())
        .then((result) {
          if (result.success == true &&
              result.code == 0 &&
              result.data != null) {
            linkOptionDict.value = result.data
                    ?.map<DictNode>((v) => DictNode(
                        code: v["patternsLinkId"].toString(),
                        name: v["linkName"]))
                    .toList() ??
                [];
          }
        })
        .onError(handleError)
        .whenComplete(() {
          if (!context.mounted) {
            return;
          }
          hideLoading(context);
        });
  }

  void onInit() {
    var userInfo = StorageUtil.userInfo();
    String? tmpOrgCode;
    if (userInfo?.data?.pluginInfo?.orgInfos != null &&
        (userInfo?.data?.pluginInfo?.orgInfos?.isNotEmpty ?? false)) {
      tmpOrgCode = userInfo?.data?.pluginInfo?.orgInfos?[0].orgCode;
    }

    AgriculturalRecordsService()
        .getOrgCode(cancelToken: createCancelToken())
        .then((result) {
      if (result.success == true && result.code == 0 && result.data != null) {
        loadingStatus.value = LoadingStatus.success;
        List<OrgTreeItem> orgDict = result.data
                .map<OrgTreeItem>((v) => OrgTreeItem.fromJson(v))
                .toList() ??
            [];
        chosenOrg.value =
            orgDict.firstWhereOrNull((test) => test.orgCode == tmpOrgCode);

        this.orgDict.value = orgDict;

        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }

  bool get showContentWidget {
    return chosenOrg.value != null &&
        chosenCorp.value != null &&
        chosenPlantOption.value != null &&
        chosenLinkOption.value != null;
  }

  String get contentWidgetKey {
    return "content-${chosenOrg.value}-${chosenCorp.value}-${chosenPlantOption.value}-${chosenLinkOption.value}";
  }
}

class _ContentWidget extends StatefulWidget {
  final OrgTreeItem? chosenOrg;
  final DictNode? chosenCorp;
  final DictNode? chosenPlantOption;
  final DictNode? chosenLinkOption;
  final String? statYear;
  final String? raiseCrops;
  const _ContentWidget(
      {super.key,
      this.chosenOrg,
      this.chosenCorp,
      this.chosenPlantOption,
      this.chosenLinkOption,
      this.statYear,
      this.raiseCrops});

  @override
  State<_ContentWidget> createState() => __ContentWidgetState();
}

class __ContentWidgetState extends MixinUseState<_ContentWidget> {
  late final controller = useController(_ContentController(
      context,
      widget.chosenOrg,
      widget.chosenCorp,
      widget.chosenPlantOption,
      widget.chosenLinkOption,
      widget.statYear,
      widget.raiseCrops));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return UseBuilder((context) {
      return UseBuilder((context) {
        var status = controller.loadingStatus.value ?? LoadingStatus.init;
        switch (status) {
          case LoadingStatus.loading:
          case LoadingStatus.init:
          case LoadingStatus.error:
          case LoadingStatus.cancel:
            return _widgetLoading();
          case LoadingStatus.success:
          case LoadingStatus.loadingMore:
          case LoadingStatus.refreshing:
            if (controller.isEmpty) {
              return _widgetEmpty();
            }
            return _widgetBody();
        }
      });
    });
  }

  Widget _widgetBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: 7.px,
        ),
        _widgetSubMenu(),
        SizedBox(
          height: 7.px,
        ),
        Row(
          children: [
            SizedBox(
              width: 14.px,
            ),
            Expanded(
                child: Text(
              "请选择批量记录的地块",
              style: TextStyle(
                  color: const Color.fromRGBO(44, 44, 44, 1), fontSize: 14.px),
            )),
            Text(
              "已选:${controller.checkedItems.value?.length ?? 0}",
              style: TextStyle(
                  color: const Color.fromARGB(255, 94, 139, 245),
                  fontSize: 14.px),
            ),
            SizedBox(
              width: 14.px,
            ),
          ],
        ),
        Expanded(child: UseBuilder((context) {
          var list = controller.filterItems.value ?? [];
          if (list.isEmpty) {
            return _widgetEmpty();
          }
          return ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: list.length,
            itemBuilder: (context, index) {
              var item = list[index];
              return _widgetItem(index, item);
            },
          );
        })),
        SizedBox(
          height: 14.px,
        ),
        _widgetBottom(),
        SizedBox(
          height: 14.px,
        )
      ],
    );
  }

  Widget _widgetItem(int index, LandRecord item) {
    bool checked = controller.isChecked(index);
    Widget child = Container(
      height: 44.px,
      margin: EdgeInsets.only(left: 14.px, right: 14.px, top: 14.px),
      padding: EdgeInsets.only(left: 7.px, right: 7.px),
      decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
              width: 1.5.px,
              color: checked
                  ? const Color.fromARGB(255, 94, 139, 245)
                  : Colors.white),
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Row(
        children: [
          Expanded(
              child: Text(
            "${item.landName}(${item.landNo})-${item.landArea}亩",
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: checked
                    ? const Color.fromARGB(255, 94, 139, 245)
                    : const Color.fromRGBO(44, 44, 44, 1),
                fontSize: 12.5.px),
          )),
          SizedBox(
            width: 7.px,
          ),
          Text(
            item.isHaveRecord == "1" ? "有记录" : "无记录",
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: item.isHaveRecord == "1"
                    ? const Color(0xFFDF4057)
                    : const Color(0xFF71B606),
                fontSize: 14.px),
          ),
          SizedBox(
            width: 7.px,
          ),
          BdhRadio(
            backgroundColor: Colors.transparent,
            selectColor: const Color.fromARGB(255, 94, 139, 245),
            checked: checked,
            iconSize: 16.px,
            onCheckBoxChanged: (c) {
              controller.onItemCheck(index, !checked);
            },
          ),
        ],
      ),
    );

    return GestureDetector(
      onTap: () {
        controller.onItemCheck(index, !checked);
      },
      child: child,
    );
  }

  Widget _widgetSubMenu() {
    return SizedBox(
        child: Row(
      children: [
        SizedBox(
          width: 14.px,
        ),
        SizedBox(
            width: 70.px,
            child: UseBuilder((context) => BdhDropDownSingleDataPicker(
                  mainAxisAlignment: MainAxisAlignment.start,
                  initialValue: controller.recordOption.value,
                  checkState: true,
                  onChange: (v) {
                    controller.recordOption.value = v;
                  },
                  item: FormItem(
                      title: "记录类型",
                      data: controller.recordOptionDict,
                      isRequired: false),
                ))),
        SizedBox(
          width: 14.px,
        ),
        SizedBox(
            width: 120.px,
            child: UseBuilder((context) => BdhDropDownSingleDataPicker(
                  mainAxisAlignment: MainAxisAlignment.start,
                  initialValue: controller.order.value,
                  checkState: true,
                  onChange: (v) {
                    controller.order.value = v;
                  },
                  item: FormItem(
                      title: "请排序方式",
                      data: controller.orderDict,
                      isRequired: true),
                )))
      ],
    ));
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetEmpty() {
    return const Center(
      child: BdhEmptyView(
        tipInfo: "未查到记录",
      ),
    );
  }

  Widget _widgetBottom() {
    return Row(
      children: [
        SizedBox(
          width: 14.px,
        ),
        BdhRadio(
          iconSize: 16.px,
          backgroundColor: Colors.transparent,
          selectColor: const Color.fromARGB(255, 94, 139, 245),
          checked: controller.checkedAll,
          onCheckBoxChanged: (selected) {
            if (selected == true) {
              controller.checkAll();
            } else {
              controller.uncheckAll();
            }
          },
        ),
        SizedBox(
          width: 3.px,
        ),
        const Text("全选"),
        SizedBox(
          width: 14.px,
        ),
        Expanded(
            child: BdhTextButton(
          height: 34.px,
          text: '确认添加',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(6.px)),
          backgroundColor: const Color.fromARGB(240, 94, 139, 245),
          disableBackgroundColor: const Color.fromARGB(255, 224, 223, 223),
          pressedBackgroundColor: const Color.fromARGB(255, 94, 139, 245),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: controller.canSubmit ? controller.onSubmit : null,
        )),
        SizedBox(
          width: 14.px,
        ),
      ],
    );
  }
}

class _ContentController extends UseController {
  final OrgTreeItem? chosenOrg;
  final DictNode? chosenCorp;
  final DictNode? chosenPlantOption;
  final DictNode? chosenLinkOption;
  final String? statYear;
  final String? raiseCrops;

  final orderDict = [
    DictNode(code: "", name: "默认排序"),
    DictNode(code: "1", name: "按地块名称排序"),
    DictNode(code: "2", name: "按地块面积排序")
  ];

  final recordOptionDict = [
    DictNode(code: "", name: "全部"),
    DictNode(code: "1", name: "有记录"),
    DictNode(code: "0", name: "无记录")
  ];

  _ContentController(
      super.context,
      this.chosenOrg,
      this.chosenCorp,
      this.chosenPlantOption,
      this.chosenLinkOption,
      this.statYear,
      this.raiseCrops);

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);
  late final order = use<DictNode>(orderDict[0])
    ..onChange = (v) {
      uncheckAll();
      recordOption.value = recordOptionDict[0];
      onInit();
    };
  late final recordOption = use<DictNode>(recordOptionDict[0]);

  late final items = use<List<LandRecord>>([]);
  late final checkedItems = use<List<LandRecord>>([]);

  late final filterItems = UseCompute<List<LandRecord>>(() {
    var r = recordOption.value;
    if (r?.code == "0") {
      return items.value?.where((test) => test.isHaveRecord != '1').toList();
    } else if (r?.code == "1") {
      return items.value?.where((test) => test.isHaveRecord == '1').toList();
    }
    return items.value;
  });

  bool get checkedAll {
    if (items.value?.isEmpty ?? true) {
      return false;
    }
    var list = filterItems.value ?? [];
    var cItems = checkedItems.value ?? [];

    for (var item in list) {
      if (!cItems.contains(item)) {
        return false;
      }
    }
    return true;
  }

  void checkAll() {
    var list = filterItems.value ?? [];

    var cItems = checkedItems.value ?? [];
    for (var item in list) {
      if (!cItems.contains(item)) {
        cItems.add(item);
      }
    }
    checkedItems.value = cItems;
    checkedItems.forceNotify();
  }

  void uncheckAll() {
    var list = filterItems.value ?? [];

    var cItems = checkedItems.value ?? [];
    for (var item in list) {
      cItems.remove(item);
    }
    checkedItems.value = cItems;
    checkedItems.forceNotify();
  }

  bool isChecked(int index) {
    var cItems = filterItems.value;
    var item = cItems?[index];
    return checkedItems.value?.contains(item) ?? false;
  }

  void onItemCheck(int index, bool checked) {
    var element = filterItems.value?[index];
    if (element == null) {
      return;
    }
    if (checked == true && !(checkedItems.value?.contains(element) ?? false)) {
      checkedItems.value?.add(element);
    } else if (checked == false &&
        (checkedItems.value?.contains(element) ?? false)) {
      checkedItems.value?.remove(element);
    }
    checkedItems.forceNotify();
  }

  void onInit() {
    // 获取地块
    var data = {
      "statYear": statYear,
      "orgCode": chosenOrg?.orgCode,
      "raiseCrops": chosenCorp?.code,
      "growPatternsId": chosenPlantOption?.code,
      "patternsLinkId": chosenLinkOption?.code,
      "outOeder": order.value?.code
    };
    loadingStatus.value = LoadingStatus.loading;
    AgriculturalRecordsService()
        .queryAllPlot(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (result.success == true && result.code == 0 && result.data != null) {
        var loadItems = result.data
                .map<LandRecord>((v) => LandRecord.fromJson(v))
                .toList() ??
            [];
        items.value = loadItems;
        checkedItems.value = [];
        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }

  bool get isEmpty {
    return items.value?.isEmpty ?? true;
  }

  bool get canSubmit {
    return checkedItems.value?.isNotEmpty ?? false;
  }

  void onSubmit() {
    var linkCode = chosenLinkOption?.code;
    if (linkCode == null) {
      return;
    }
    var landNoList =
        checkedItems.value?.map<String>((test) => test.landNo ?? "").toList() ??
            [];
    if (landNoList.isEmpty) {
      showToast('请至少选择一个地块');
      return;
    }
    showModalBottomSheet<bool>(
        context: context,
        useSafeArea: true,
        isScrollControlled: true,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: _SaveBottomSheet(params: {
              "patternsLinkId": chosenLinkOption?.code,
              "growPatternsId": chosenPlantOption?.code,
              'orgCode': chosenOrg?.orgCode,
              'statYear': statYear,
              'raiseCrops': chosenCorp?.code,
              'raiseCropsNm': chosenCorp?.name,
              'preCurrent': 0,
              'landNoList': landNoList,
              'landList': checkedItems.value
            }),
          );
        }).then((result) {
      if (result == true) {
        onInit();
      }
    });
  }
}

class _SaveBottomSheet extends StatefulWidget {
  final Map<String, dynamic> params;
  const _SaveBottomSheet({
    super.key,
    required this.params,
  });

  @override
  State<_SaveBottomSheet> createState() => __SaveBottomSheetState();
}

class __SaveBottomSheetState extends MixinUseState<_SaveBottomSheet>
    with SingleTickerProviderStateMixin {
  late final _animationController = createAnimationController(this);

  late final controller =
      useController(_SaveBottomSheetController(context, widget.params));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      width: 347.px,
      child: Column(
        children: children,
      ),
    );
  }

  Widget _widgetTitle() {
    return Container(
      height: 60.px,
      decoration: BoxDecoration(
          border: Border(
              bottom: BorderSide(
                  width: 1.px,
                  color: const Color.fromRGBO(226, 235, 231, 0.6)))),
      child: Row(children: [
        Expanded(
          child: Column(
            children: [
              SizedBox(
                height: 8.px,
              ),
              SizedBox(
                  height: 20.px,
                  child: Text(
                    "记录内容",
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 14.px),
                  )),
              SizedBox(
                height: 4.px,
              ),
              SizedBox(
                height: 20.px,
              ),
              SizedBox(
                height: 4.px,
              ),
            ],
          ),
        ),
        SizedBox(
            width: 120.px,
            child: Column(
              children: [
                SizedBox(
                  height: 8.px,
                ),
                SizedBox(
                    height: 20.px,
                    child: Text(
                      "填充空白项",
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 14.px),
                    )),
                SizedBox(
                  height: 4.px,
                ),
                SizedBox(
                    height: 20.px,
                    child: Center(child: UseBuilder((context) {
                      return BdhRadio(
                        iconSize: 16.px,
                        checked: controller.unCheckedAll,
                        backgroundColor: Colors.transparent,
                        selectColor: const Color.fromARGB(255, 94, 139, 245),
                        onCheckBoxChanged: (selected) {
                          controller.uncheckAll();
                        },
                      );
                    }))),
                SizedBox(
                  height: 4.px,
                ),
              ],
            )),
        SizedBox(
            width: 120.px,
            child: Column(
              children: [
                SizedBox(
                  height: 8.px,
                ),
                SizedBox(
                    height: 20.px,
                    child: Text(
                      "覆盖原数据",
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 14.px),
                    )),
                SizedBox(
                  height: 4.px,
                ),
                SizedBox(
                    height: 20.px,
                    child: Center(child: UseBuilder((context) {
                      return BdhRadio(
                        iconSize: 16.px,
                        checked: controller.checkedAll,
                        backgroundColor: Colors.transparent,
                        selectColor: const Color.fromARGB(255, 94, 139, 245),
                        onCheckBoxChanged: (selected) {
                          controller.checkAll();
                        },
                      );
                    }))),
                SizedBox(
                  height: 4.px,
                ),
              ],
            )),
      ]),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetItem(QueryElementItem item, int index) {
    return UseBuilder((context) {
      bool checked = controller.isChecked(index);
      return Container(
        height: 44.px,
        decoration: BoxDecoration(
            border: Border(
                bottom: BorderSide(
                    width: 1.px,
                    color: const Color.fromRGBO(226, 235, 231, 0.6)))),
        child: Row(children: [
          Expanded(
            child: Text(
              item.itemDesc ?? "",
              textAlign: TextAlign.start,
              style: TextStyle(fontSize: 14.px),
            ),
          ),
          SizedBox(
              width: 120.px,
              child: Center(
                  child: BdhRadio(
                iconSize: 16.px,
                backgroundColor: Colors.transparent,
                selectColor: const Color.fromARGB(255, 94, 139, 245),
                checked: !checked,
                onCheckBoxChanged: (selected) {
                  controller.onItemCheck(index, false);
                },
              ))),
          SizedBox(
              width: 120.px,
              child: Center(
                  child: BdhRadio(
                iconSize: 16.px,
                backgroundColor: Colors.transparent,
                selectColor: const Color.fromARGB(255, 94, 139, 245),
                checked: checked,
                onCheckBoxChanged: (selected) {
                  controller.onItemCheck(index, true);
                },
              ))),
        ]),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheet(
        enableDrag: false,
        backgroundColor: Colors.white,
        animationController: _animationController,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.px)),
        ),
        onClosing: () {},
        builder: (context) {
          return Container(
            constraints: BoxConstraints(maxHeight: 564.px),
            padding: EdgeInsets.only(left: 15.px, right: 15.px),
            decoration: const BoxDecoration(
                image: DecorationImage(
              image: AssetImage(
                  "assets/images/agriculturalrecords/batch_chooser_bg.png"), // 本地图片
              fit: BoxFit.fill,
            )),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 20.px, bottom: 20.px),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                          padding: EdgeInsets.only(left: 4.px),
                          child: Icon(
                            Icons.close,
                            size: 24.px,
                            color: Colors.transparent,
                          )),
                      const Spacer(),
                      Text(
                        "请选择保存方式",
                        strutStyle:
                            StrutStyle(fontSize: 16.px, forceStrutHeight: true),
                        style: TextStyle(
                            color: const Color.fromRGBO(44, 44, 52, 1),
                            fontSize: 16.px,
                            fontWeight: FontWeight.w600),
                      ),
                      const Spacer(),
                      GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Padding(
                              padding: EdgeInsets.only(right: 4.px),
                              child: Icon(
                                Icons.close,
                                size: 24.px,
                                color: const Color.fromRGBO(115, 116, 131, 1),
                              ))),
                    ],
                  ),
                ),
                Expanded(child: UseBuilder((context) {
                  return CustomScrollView(slivers: [
                    if (controller.loadingStatus.value == LoadingStatus.init ||
                        controller.loadingStatus.value == LoadingStatus.loading)
                      SliverFillRemaining(child: _widgetLoading()),
                    if (controller.loadingStatus.value ==
                        LoadingStatus.success) ...[
                      SliverToBoxAdapter(
                          child: widgetContainer(children: [
                        _widgetTitle(),
                        ...controller.items.value
                                ?.asMap()
                                .map<int, Widget>((i, test) =>
                                    MapEntry(i, _widgetItem(test, i)))
                                .values
                                .toList() ??
                            []
                      ])),
                      SliverToBoxAdapter(
                        child: Padding(
                            padding: EdgeInsets.only(left: 14.px, right: 14.px),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(
                                  height: 7.px,
                                ),
                                Text(
                                  "说明",
                                  textAlign: TextAlign.start,
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(44, 44, 44, 1),
                                      fontSize: 14.px,
                                      fontWeight: FontWeight.w600),
                                ),
                                SizedBox(
                                  height: 3.px,
                                ),
                                Text(
                                  "1.\"填充空白项\"不会覆盖原有地块记录数据，只会把本次记录数据填充到没有记录的地块中;",
                                  textAlign: TextAlign.start,
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(44, 44, 44, 1),
                                      fontSize: 14.px),
                                ),
                                Text(
                                  "2.\"覆盖原数据\"会使用本次记录数据覆盖掉所有选中地块原有记录数据。",
                                  textAlign: TextAlign.start,
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(44, 44, 44, 1),
                                      fontSize: 14.px),
                                ),
                                Text(
                                  "3.\"作业面积\"默认\"覆盖原数据\"，无须在此选择。",
                                  textAlign: TextAlign.start,
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(44, 44, 44, 1),
                                      fontSize: 14.px),
                                ),
                                SizedBox(
                                  height: 7.px,
                                ),
                              ],
                            )),
                      ),
                    ]
                  ]);
                })),
                SizedBox(
                  height: 7.px,
                ),
                UseBuilder((context) {
                  return controller.loadingStatus.value == LoadingStatus.success
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Expanded(
                                child: BdhTextButton(
                              height: 34.px,
                              text: '取消',
                              textFontWeight: FontWeight.w500,
                              textSize: 13.px,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(6.px)),
                              backgroundColor:
                                  const Color.fromRGBO(44, 44, 44, 0.2),
                              disableBackgroundColor:
                                  const Color.fromRGBO(44, 44, 44, 0.2),
                              pressedBackgroundColor:
                                  const Color.fromRGBO(44, 44, 44, 0.3),
                              foregroundColor: Colors.white,
                              disableForegroundColor: Colors.white,
                              pressedForegroundColor: Colors.white,
                              onPressed: () {
                                Navigator.of(context).pop(false);
                              },
                            )),
                            SizedBox(
                              width: 14.px,
                            ),
                            Expanded(
                                child: BdhTextButton(
                              height: 34.px,
                              text: '确认',
                              textFontWeight: FontWeight.w500,
                              textSize: 13.px,
                              borderRadius:
                                  BorderRadius.all(Radius.circular(6.px)),
                              backgroundColor:
                                  const Color.fromARGB(240, 94, 139, 245),
                              disableBackgroundColor:
                                  const Color.fromARGB(255, 224, 223, 223),
                              pressedBackgroundColor:
                                  const Color.fromARGB(255, 94, 139, 245),
                              foregroundColor: Colors.white,
                              disableForegroundColor: Colors.white,
                              pressedForegroundColor: Colors.white,
                              onPressed: controller.onSubmit,
                            ))
                          ],
                        )
                      : const SizedBox.shrink();
                }),
                SizedBox(
                  height: 7.px,
                ),
              ],
            ),
          );
        });
  }
}

class _SaveBottomSheetController extends UseController
    with MultiCheckUseController<QueryElementItem> {
  final Map<String, dynamic> params;

  _SaveBottomSheetController(super.context, this.params);

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  void onInit() {
    loadingStatus.value = LoadingStatus.loading;
    AgriculturalRecordsService().queryElementItem(
        data: {"patternsLinkId": params["patternsLinkId"]},
        cancelToken: createCancelToken()).then((result) {
      if (result.code == 0 && result.success == true && result.data != null) {
        items.value = result.data
                .map<QueryElementItem>(
                    (test) => QueryElementItem.fromJson(test))
                .toList() ??
            [];
        checkedItems.value = [];
        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }

  void onSubmit() {
    var settingList = items.value ?? [];

    for (int index = 0; index < settingList.length; index++) {
      if (isChecked(index)) {
        settingList[index] = settingList[index].copyWith(itemFlag: "2");
      } else {
        settingList[index] = settingList[index].copyWith(itemFlag: "1");
      }
    }

    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => BatchPlantingPlanPage(
                  params: {
                    'growPatternsId': params["growPatternsId"],
                    'orgCode': params["orgCode"],
                    'statYear': params["statYear"],
                    'raiseCrops': params["raiseCrops"],
                    'raiseCropsNm': params["raiseCropsNm"],
                    'preCurrent': 0,
                    'landNoList': params["landNoList"],
                    'patternsLinkId': params["patternsLinkId"],
                    'settingList': settingList,
                    'landList': params["landList"],
                  },
                )))
        .then((result) {
      if (!context.mounted) {
        return;
      }
      Navigator.of(context).pop(true);
    });
  }
}
