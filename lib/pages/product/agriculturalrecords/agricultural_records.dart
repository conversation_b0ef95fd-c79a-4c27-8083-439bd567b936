import 'dart:convert';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_org_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_dropdown_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/product/agriculturalrecords/widget/batch_chooser_alert.dart';
import 'package:bdh_smart_agric_app/pages/product/landcontract/bidding/widget/search_widget.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/collection_extensions.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:latlong2/latlong.dart';
import 'package:oktoast/oktoast.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';

import 'batch_finishing_block.dart';
import 'batch_recording_block.dart';
import 'live_broad.dart';
import 'map_distance_preview.dart';
import 'model/land_record.dart';
import 'planting_plan.dart';
import 'planting_plan_preview.dart';
import 'request/agricultural_records_service.dart';
import 'widget/base64_image.dart';

class AgriculturalRecordsPage extends StatefulWidget {
  const AgriculturalRecordsPage({super.key});

  @override
  State<AgriculturalRecordsPage> createState() =>
      _AgriculturalRecordsPageState();
}

class _AgriculturalRecordsPageState
    extends MixinUseState<AgriculturalRecordsPage> {
  late final controller = useController(_Controller(context));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
        child: Container(
            decoration: BoxDecoration(
                image: DecorationImage(
                    fit: BoxFit.fill,
                    image: AssetImage(ImageHelper.wrapAssets(
                        "agriculturalrecords/bkg.png")))),
            child: Column(
              children: [
                _widgetSearchAppBar(),
                Expanded(child: UseBuilder((context) {
                  var status =
                      controller.loadingStatus.value ?? LoadingStatus.init;
                  switch (status) {
                    case LoadingStatus.loading:
                    case LoadingStatus.init:
                    case LoadingStatus.error:
                    case LoadingStatus.cancel:
                      return _widgetLoading();
                    case LoadingStatus.success:
                    case LoadingStatus.loadingMore:
                    case LoadingStatus.refreshing:
                      return _widgetBody();
                  }
                }))
              ],
            )));
  }

  PreferredSizeWidget _widgetSearchAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      title: UseBuilder((context) => SearchWidget(
            initialValue: controller.searchText.value,
            backgroundColor: Colors.transparent,
            inputColor: const Color.fromRGBO(44, 44, 52, 0.1),
            placeholderStyle: TextStyle(
              fontSize: 14.px,
              color: const Color.fromRGBO(44, 44, 52, 0.4),
              fontWeight: FontWeight.w500,
            ),
            textStyle: TextStyle(
              fontSize: 14.px,
              color: const Color.fromRGBO(44, 44, 52, 0.6),
              fontWeight: FontWeight.w500,
            ),
            height: 30.px,
            margin: EdgeInsets.zero,
            placeholder: "搜索内容",
            onChanged: (String? value) {
              controller.searchText.value = value;
            },
            onSubmitted: (String? value) {
              controller.searchText.value = value;
            },
          )),
      actions: [
        GestureDetector(
          onTap: () {
            controller.onClickBatch();
          },
          child: Image.asset(
            ImageHelper.wrapAssets("agriculturalrecords/ic_edit.png"),
            width: 20.px,
            height: 20.px,
          ),
        ),
        SizedBox(
          width: 15.px,
        )
      ],
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    return Column(
      children: [
        _tabControl(),
        _widgetSubMenu(),
        Expanded(
            child: UseBuilder((context) => _ContentWidget(
                  key: ValueKey(controller.contentKey),
                  statYear: controller.chosenYear.value?.code,
                  orgCode: controller.chosenOrg.value?.orgCode,
                  landName: controller.searchText.value,
                  orderCode: controller.chosenOrder.value?.code,
                  raiseCrops: controller.chosenCorp.value?.code,
                  tabIndex: controller.tabIndex.value ?? 0,
                )))
      ],
    );
  }

  Widget _tabControl() {
    return UseBuilder((context) {
      return widgetTabControl(
          tabItems: controller.tabs,
          chooseIndex: controller.tabIndex.value ?? 0,
          onItemClick: (index) {
            controller.tabIndex.value = index;
          });
    });
  }

  Widget widgetTabControl(
      {required List<String> tabItems,
      required int chooseIndex,
      required void Function(int) onItemClick}) {
    var children = <Widget>[];

    for (int i = 0; i < tabItems.length; i++) {
      bool choose = i == chooseIndex;
      children.addAll([
        Expanded(
            child: Center(
          child: GestureDetector(
              onTap: () {
                onItemClick.call(i);
              },
              child: SizedBox(
                  child: Column(
                children: [
                  SizedBox(
                    height: 6.px,
                  ),
                  Center(
                      child: Text(
                          "${tabItems[i]}${controller.tabCountString(i)}",
                          strutStyle: StrutStyle(
                              fontSize: 18.px, forceStrutHeight: true),
                          style: TextStyle(
                            fontSize: choose ? 16.px : 16.px,
                            fontWeight:
                                choose ? FontWeight.w600 : FontWeight.w500,
                            color: choose
                                ? const Color(0xff2979ff)
                                : const Color.fromRGBO(41, 41, 52, 0.7),
                          ))),
                  SizedBox(
                    height: 6.px,
                  ),
                  Container(
                    height: 3.px,
                    width: 21.px,
                    decoration: BoxDecoration(
                        color: choose
                            ? const Color(0xff2979ff)
                            : Colors.transparent,
                        borderRadius:
                            BorderRadius.all(Radius.circular(1.5.px))),
                  ),
                  SizedBox(
                    height: 16.px,
                  ),
                ],
              ))),
        ))
      ]);
    }
    return Row(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          width: 14.px,
        ),
        ...children,
        SizedBox(
          width: 14.px,
        )
      ],
    );
  }

  Widget _widgetSubMenu() {
    return SizedBox(
        height: 42.px,
        child: Row(
          children: [
            Expanded(
                child: UseBuilder((context) => BdhDropDownSingleDataPicker(
                      initialValue: controller.chosenYear.value,
                      checkState: true,
                      onChange: (v) {
                        controller.chosenYear.value = v;
                      },
                      item: FormItem(
                          title: "请选择年份",
                          data: controller.yearDict.value ?? [],
                          isRequired: true),
                    ))),
            Expanded(
                child: UseBuilder((context) => BdhDropdownOrgPicker(
                      initialValue: controller.chosenOrg.value,
                      checkState: true,
                      placeholder: "请选择组织",
                      onChange: (v) {
                        controller.chosenOrg.value = v;
                      },
                      item: FormItem(
                          title: "请选择组织",
                          data: controller.orgDict.value ?? [],
                          isRequired: true),
                    ))),
            Expanded(
                child: UseBuilder((context) => BdhDropDownSingleDataPicker(
                      initialValue: controller.chosenCorp.value,
                      checkState: true,
                      onChange: (v) {
                        controller.chosenCorp.value = v;
                      },
                      item: FormItem(
                          title: "请选择账户",
                          data: controller.corpDict.value ?? [],
                          isRequired: true),
                    ))),
            Expanded(
                child: UseBuilder((context) => BdhDropDownSingleDataPicker(
                      initialValue: controller.chosenOrder.value,
                      checkState: true,
                      placeHolder: "排序",
                      onChange: (v) {
                        controller.chosenOrder.value = v;
                      },
                      item: FormItem(
                          title: "排序",
                          data: controller.orderDict,
                          isRequired: true),
                    ))),
          ],
        ));
  }
}

class _Controller extends UseController {
  _Controller(super.context);

  final orderDict = [
    DictNode(code: "1", name: "按地块名称"),
    DictNode(code: "2", name: "按地块面积"),
  ];

  late final tabIndex = use<int>(0);
  final tabs = ["待记地块", "地块列表"];

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  late final searchText = use<String>(null)
    ..onChange = (v) {
      loadRecordAndLandCount();
    };

  late final yearDict = use<List<DictNode>>(null);
  late final corpDict = use<List<DictNode>>(null);
  late final orgDict = use<List<OrgTreeItem>>(null);

  late final chosenYear = use<DictNode>(null)
    ..onChange = (v) {
      loadRecordAndLandCount();
    };
  late final chosenCorp = use<DictNode>(null)
    ..onChange = (v) {
      loadRecordAndLandCount();
    };
  late final chosenOrder = use<DictNode>(null);
  late final chosenOrg = use<OrgTreeItem>(null)
    ..onChange = (v) {
      loadRecordAndLandCount();
    };

  late final recordCount = use<int>(null);
  late final landCount = use<int>(null);

  String get contentKey {
    return "content-${tabIndex.value}-${chosenYear.value?.code}-${chosenOrg.value?.orgCode}-${chosenCorp.value?.code}-${chosenOrder.value?.code}-${searchText.value}";
  }

  String tabCountString(int index) {
    if (index == 0) {
      if (recordCount.value == null) return "";
      return "(${recordCount.value})";
    } else {
      if (landCount.value == null) return "";
      return "(${landCount.value})";
    }
  }

  void loadRecordAndLandCount() {
    if (loadingStatus.peek() != LoadingStatus.success) {
      return;
    }
    var data = <String, dynamic>{
      "statYear": chosenYear.peek()?.code,
      "orgCode": chosenOrg.peek()?.orgCode,
      "landName": searchText.peek() ?? "",
      "raiseCrops": chosenCorp.peek()?.code,
    };
    recordCount.value = null;
    landCount.value = null;
    AgriculturalRecordsService()
        .queryRecordAndLandCount(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true && result.data != null) {
        recordCount.value = result.data["record"];
        landCount.value = result.data["land"];
      }
    }).onError(handleError);
  }

  void onInit() {
    var userInfo = StorageUtil.userInfo();
    String? tmpOrgCode;
    if (userInfo?.data?.pluginInfo?.orgInfos != null &&
        (userInfo?.data?.pluginInfo?.orgInfos?.isNotEmpty ?? false)) {
      tmpOrgCode = userInfo?.data?.pluginInfo?.orgInfos?[0].orgCode;
    }
    Future.wait([
      AgriculturalRecordsService()
          .getDict(data: "year_cd", cancelToken: createCancelToken()),
      AgriculturalRecordsService()
          .getDict(data: "sys_crop_ar", cancelToken: createCancelToken()),
      AgriculturalRecordsService().getOrgCode(cancelToken: createCancelToken())
    ]).then((list) {
      var yearDict = (list[0] as DictList).data ?? [];
      if (yearDict.isNotEmpty) {
        chosenYear.value = yearDict[0];
      } else {
        chosenYear.value = null;
      }
      this.yearDict.value = yearDict;
      var corpDict = (list[1] as DictList).data ?? [];
      corpDict.insert(0, DictNode(code: "", name: "全部"));
      if (corpDict.isNotEmpty) {
        chosenCorp.value = corpDict[0];
      } else {
        chosenCorp.value = null;
      }

      this.corpDict.value = corpDict;
      var orgDict = ((list[2] as RequestNoData).data as List?)
              ?.map<OrgTreeItem>((v) => OrgTreeItem.fromJson(v))
              .toList() ??
          [];
      if (orgDict.isNotEmpty) {
        OrgTreeItem? choose;
        if (tmpOrgCode != null) {
          choose =
              orgDict.firstWhereOrNull((test) => test.orgCode == tmpOrgCode);
        }
        choose ??= orgDict[0];

        chosenOrg.value = choose;
      } else {
        chosenOrg.value = null;
      }
      this.orgDict.value = orgDict;

      loadingStatus.value = LoadingStatus.success;
      loadRecordAndLandCount();
    }).onError(handleError);
  }

  void batchRecording() {
    Navigator.of(context).push(CupertinoPageRoute(
        builder: (_) => BatchRecordingBlockPage(
              params: {
                "statYear": chosenYear.peek()?.code,
                "raiseCrops": chosenCorp.peek()?.code,
                "raiseText": chosenCorp.peek()?.name,
                "corpDict": corpDict.peek()
              },
            )));
  }

  void batchFinishing() {
    Navigator.of(context).push(CupertinoPageRoute(
        builder: (_) => BatchFinishingBlockPage(
              params: {
                "statYear": chosenYear.peek()?.code,
                "raiseCrops": chosenCorp.peek()?.code,
                "raiseText": chosenCorp.peek()?.name,
                "corpDict": corpDict.peek()
              },
            )));
  }

  void onClickBatch() {
    showBatchChooserDialog(context).then((result) {
      if (result == "record") {
        batchRecording();
      } else if (result == "finish") {
        batchFinishing();
      }
    });
  }
}

class _ContentWidget extends StatefulWidget {
  final String? statYear;
  final String? orgCode;
  final String? landName;
  final String? orderCode;
  final String? raiseCrops;
  final int tabIndex;
  const _ContentWidget(
      {super.key,
      this.statYear,
      this.orgCode,
      this.landName,
      this.orderCode,
      this.raiseCrops,
      required this.tabIndex});

  @override
  State<_ContentWidget> createState() => __ContentWidgetState();
}

class __ContentWidgetState extends MixinUseState<_ContentWidget> {
  late final controller = useController(_ContentController(
    context,
    statYear: widget.statYear,
    orgCode: widget.orgCode,
    landName: widget.landName,
    orderCode: widget.orderCode,
    raiseCrops: widget.raiseCrops,
    tabIndex: widget.tabIndex,
  ));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return UseBuilder((context) {
      var status = controller.loadingStatus.value ?? LoadingStatus.init;
      switch (status) {
        case LoadingStatus.loading:
        case LoadingStatus.init:
        case LoadingStatus.error:
        case LoadingStatus.cancel:
          return _widgetLoading();
        case LoadingStatus.success:
        case LoadingStatus.loadingMore:
        case LoadingStatus.refreshing:
          return _widgetBody();
      }
    });
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetEmpty() {
    return const Center(
      child: BdhEmptyView(
        tipInfo: "未查到申请记录",
      ),
    );
  }

  Widget _widgetBody() {
    if (controller.items.value?.isEmpty ?? true) {
      return _widgetEmpty();
    }
    return SmartRefresher(
      enablePullUp: true,
      onRefresh: controller.refresh,
      onLoading: controller.loadMore,
      controller: controller.refreshController,
      child: ListView.builder(
          controller: controller.scrollController,
          itemCount: controller.items.value?.length ?? 0,
          itemBuilder: (context, index) {
            return _widgetItem(index);
          }),
    );
  }

  Widget _widgetItem(int index) {
    var item = controller.items.value?[index];
    return GestureDetector(
        onTap: () {
          controller.onClickItem(item);
        },
        child: Container(
          margin: EdgeInsets.only(left: 14.px, right: 14.px, top: 14.px),
          padding: EdgeInsets.all(14.px),
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(8.px))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                      child: Stack(
                    children: [
                      Positioned(
                          bottom: 0,
                          child: Container(
                            width: 50.px,
                            height: 6.px,
                            decoration: const BoxDecoration(
                                gradient: LinearGradient(colors: [
                              Color(0xFF5D96FF),
                              Color(0xFFFFFFFF)
                            ])),
                          )),
                      Text.rich(
                          TextSpan(children: [
                            TextSpan(text: "${item?.statYear ?? ""}年"),
                            if (item?.isHave == "1")
                              TextSpan(text: item?.growPatternsName ?? ""),
                          ]),
                          style: TextStyle(
                              fontSize: 14.px,
                              color: const Color.fromRGBO(44, 44, 44, 1),
                              fontWeight: FontWeight.w500)),
                    ],
                  )),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.px,
                  ),
                ],
              ),
              SizedBox(
                height: 7.px,
              ),
              Row(
                children: [
                  GestureDetector(
                    onTap: () {
                      controller.onClickToMap(item);
                    },
                    child: Container(
                        width: 86.px,
                        height: 86.px,
                        decoration: BoxDecoration(
                            color: const Color(0xFFF3F5F9),
                            borderRadius:
                                BorderRadius.all(Radius.circular(4.px))),
                        child: Stack(
                          children: [
                            (item?.plotBase64 != null)
                                ? Center(
                                    child: Base64Image(
                                    item!.plotBase64!,
                                    width: 78.px,
                                    height: 78.px,
                                  ))
                                : Center(
                                    child: Icon(
                                      Icons.broken_image,
                                      size: 50.px,
                                      color: Colors.white,
                                    ),
                                  ),
                            if (item?.isTrace == '1')
                              Positioned(
                                  top: 0,
                                  left: 0,
                                  child: Container(
                                      decoration: BoxDecoration(
                                          color: Colors.green,
                                          borderRadius: BorderRadius.only(
                                              topLeft: Radius.circular(4.px),
                                              bottomRight:
                                                  Radius.circular(4.px))),
                                      height: 16.px,
                                      padding: EdgeInsets.only(
                                          left: 4.px, right: 4.px),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.thumb_up,
                                            size: 10.px,
                                            color: Colors.white,
                                          ),
                                          SizedBox(
                                            width: 2.px,
                                          ),
                                          Text(
                                            "优品链",
                                            style: TextStyle(
                                                fontSize: 8.px,
                                                fontFamily:
                                                    "AlimamaShuHeiTi-Bold",
                                                color: Colors.white),
                                          )
                                        ],
                                      )))
                          ],
                        )),
                  ),
                  SizedBox(
                    width: 14.px,
                  ),
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                              child: Text(item?.landName ?? "",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      fontSize: 20.px,
                                      color:
                                          const Color.fromRGBO(44, 44, 44, 1),
                                      fontWeight: FontWeight.w500))),
                          if (item?.isBack == '1')
                            BdhTextButton(
                              height: 24.px,
                              width: 40.px,
                              text: "退回",
                              textFontWeight: FontWeight.w500,
                              textSize: 12.px,
                              padding: EdgeInsets.only(left: 5.px, right: 5.px),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(5.px)),
                              backgroundColor:
                                  const Color.fromRGBO(255, 78, 54, 0.15),
                              disableBackgroundColor:
                                  const Color.fromRGBO(255, 78, 54, 0.15),
                              pressedBackgroundColor:
                                  const Color.fromRGBO(255, 78, 54, 0.3),
                              foregroundColor: const Color(0xFFFF4E36),
                              disableForegroundColor: const Color(0xFFFF4E36),
                              pressedForegroundColor: const Color(0xFFFF4E36),
                              onPressed: () {},
                            ),
                        ],
                      ),
                      SizedBox(
                        height: 2.px,
                      ),
                      Row(
                        children: [
                          Expanded(
                            child: Text(item?.orgName ?? "",
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    fontSize: 14.px,
                                    color: const Color.fromRGBO(44, 44, 44, 1),
                                    fontWeight: FontWeight.w500)),
                          )
                        ],
                      ),
                      SizedBox(
                        height: 4.px,
                      ),
                      Row(
                        children: [
                          if (item?.raiseCropsNm != null ||
                              item?.raiseCropsVarietyNm != null) ...[
                            Container(
                              height: 24.px,
                              decoration: BoxDecoration(
                                  border: Border.all(
                                      color: const Color(0xFF326DFF),
                                      width: 1.px),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(8.px))),
                              child: Row(
                                children: [
                                  if (item?.raiseCropsNm != null)
                                    Container(
                                      height: double.infinity,
                                      padding: EdgeInsets.only(
                                          left: 7.px, right: 7.px),
                                      alignment: Alignment.center,
                                      decoration: BoxDecoration(
                                          color: const Color(0xFFEFF5FF),
                                          borderRadius:
                                              item?.raiseCropsVarietyNm != null
                                                  ? BorderRadius.only(
                                                      topLeft:
                                                          Radius.circular(8.px),
                                                      bottomLeft:
                                                          Radius.circular(8.px))
                                                  : BorderRadius.all(
                                                      Radius.circular(8.px))),
                                      child: Text(item?.raiseCropsNm ?? "",
                                          style: TextStyle(
                                              fontSize: 12.px,
                                              color: const Color(0xFF326DFF),
                                              fontWeight: FontWeight.w500)),
                                    ),
                                  if (item?.raiseCropsNm != null &&
                                      item?.raiseCropsVarietyNm != null)
                                    Container(
                                      width: 1.px,
                                      height: double.infinity,
                                      color: const Color(0xFFD8E4FF),
                                    ),
                                  if (item?.raiseCropsVarietyNm != null)
                                    Container(
                                      height: double.infinity,
                                      width: 80.px,
                                      padding: EdgeInsets.only(
                                          left: 7.px, right: 7.px),
                                      alignment: Alignment.center,
                                      child: Text(
                                          item?.raiseCropsVarietyNm ?? "",
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                              fontSize: 12.px,
                                              color: const Color(0xFF326DFF),
                                              fontWeight: FontWeight.w500)),
                                    ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: 4.px,
                            ),
                          ],
                          Expanded(
                              child: Text.rich(
                                  textAlign: TextAlign.end,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  TextSpan(children: [
                                    TextSpan(
                                        text:
                                            "${num.tryParse(item?.landArea ?? "0")}"),
                                    TextSpan(
                                        text: "亩",
                                        style: TextStyle(
                                            fontSize: 12.px,
                                            color: const Color(0xFF326DFF),
                                            fontWeight: FontWeight.w500))
                                  ]),
                                  style: TextStyle(
                                      fontSize: 18.px,
                                      color: const Color(0xFF326DFF),
                                      fontWeight: FontWeight.w500)))
                        ],
                      ),
                    ],
                  ))
                ],
              ),
              if (item?.isHave == "0") ...[
                SizedBox(
                  height: 7.px,
                ),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    GestureDetector(
                      onTap: () {
                        controller.onClickImprove(item);
                      },
                      child: Container(
                          height: 22.px,
                          alignment: Alignment.centerLeft,
                          padding: EdgeInsets.only(left: 7.px, right: 7.px),
                          decoration: BoxDecoration(
                              color: const Color.fromRGBO(245, 47, 62, 0.1),
                              borderRadius:
                                  BorderRadius.all(Radius.circular(4.px))),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text("请完善实播后开始记录",
                                  style: TextStyle(
                                      fontSize: 14.px,
                                      color: const Color(0xFFF52F3E),
                                      fontWeight: FontWeight.w500)),
                              Icon(
                                Icons.arrow_forward_ios,
                                color: const Color(0xFFF52F3E),
                                size: 14.px,
                              ),
                            ],
                          )),
                    ),
                  ],
                )
              ]
            ],
          ),
        ));
  }
}

class _ContentController extends UseController
    with LoadMoreUseController<LandRecord> {
  final String? statYear;
  final String? orgCode;
  final String? landName;
  final String? orderCode;
  final String? raiseCrops;
  final int tabIndex;
  _ContentController(
    super.context, {
    this.statYear,
    this.orgCode,
    this.landName,
    this.orderCode,
    this.raiseCrops,
    required this.tabIndex,
  });

  late final scrollController = createScrollController();

  void onInit() async {
    await reload(showLoading: true);
  }

  @override
  Future reloadFuture(
      {required bool showLoading,
      required bool loadingMore,
      required bool refresh}) {
    var data = {
      "statYear": statYear,
      "orgCode": orgCode,
      "landName": landName,
      "raiseCrops": raiseCrops,
      "page": page,
      "rows": row,
      "outOeder": orderCode
    };

    late Future<RequestNoData> res;

    if (tabIndex == 0) {
      res = AgriculturalRecordsService()
          .queryToBeRecordedLand(data: data, cancelToken: createCancelToken());
    } else {
      res = AgriculturalRecordsService()
          .queryLandList(data: data, cancelToken: createCancelToken());
    }
    return res.then((result) {
      if (result.code == 0 && result.success == true) {
        total = result.data["total"] ?? 0;

        var loadItems = result.data["records"]
                .map<LandRecord>((v) => LandRecord.fromJson(v)) ??
            [];
        var currentItems = items.peek() ?? [];

        if (refresh || showLoading) {
          currentItems.clear();
        }
        currentItems.addAll(loadItems);

        items.value = currentItems;
      }
      loadingStatus.value = LoadingStatus.success;
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        loadingStatus.value = LoadingStatus.success;
      });
    });
  }

  void onClickItem(LandRecord? item) {
    if (item?.isHave == "0") {
      showToast('请完善实播后开始记录');
      return;
    }
    if (tabIndex == 0) {
      Navigator.of(context)
          .push(CupertinoPageRoute(
              builder: (_) => PlantingPlanPage(
                    params: {
                      "growPatternsId": item?.growPatternsId,
                      "landNo": item?.landNo,
                      "landName": item?.landName,
                      "raiseCrops": item?.raiseCrops,
                      "orgCode": item?.orgCode,
                      "orgName": item?.orgName,
                      "landArea": item?.landArea,
                      "raiseCropsNm": item?.raiseCropsNm,
                      "raiseCropsVarietyNm": item?.raiseCropsVarietyNm,
                      "statYear": statYear,
                      "workStationCode": item?.workStationCode,
                      "workStationName": item?.workStationName,
                      "isTrace": item?.isTrace ?? 0,
                    },
                  )))
          .then((result) {
        onInit();
      });
    } else if (tabIndex == 1) {
      Navigator.of(context)
          .push(CupertinoPageRoute(
              builder: (_) => PlantingPlanPreviewPage(
                    params: {
                      "growPatternsId": item?.growPatternsId,
                      "landNo": item?.landNo,
                      "landName": item?.landName,
                      "raiseCrops": item?.raiseCrops,
                      "orgCode": item?.orgCode,
                      "orgName": item?.orgName,
                      "landArea": item?.landArea,
                      "raiseCropsNm": item?.raiseCropsNm,
                      "raiseCropsVarietyNm": item?.raiseCropsVarietyNm,
                      "statYear": statYear,
                      "workStationCode": item?.workStationCode,
                      "workStationName": item?.workStationName,
                      "isTrace": item?.isTrace ?? 0
                    },
                  )))
          .then((result) {
        onInit();
      });
    }
  }

  void onClickToMap(LandRecord? item) {
    showLoading(context, content: "加载中..  ");
    AgriculturalRecordsService().queryGeomInfo(
        data: {"statYear": item?.statYear, "landNo": item?.landNo},
        cancelToken: createCancelToken()).then((result) {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
      if (result.success == true && result.code == 0) {
        Log.d("${result.data}");
        Navigator.of(context)
            .push(CupertinoPageRoute(
                builder: (_) => MapDistancePreview(
                      targetLocate:
                          LatLng(result.data["lat"], result.data["lng"]),
                      geoJson: result.data["geoJson"],
                    )))
            .then((result) {
          if (result != null) {}
        });
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    });
  }

  void onClickImprove(LandRecord? item) {
    if (item == null) {
      return;
    }
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => LiveBroadPage(
                  item: item,
                )))
        .then((result) {
      if (result == true) {
        onInit();
      }
    });
  }
}
