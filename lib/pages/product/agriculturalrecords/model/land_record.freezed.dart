// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'land_record.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

LandRecord _$LandRecordFromJson(Map<String, dynamic> json) {
  return _LandRecord.fromJson(json);
}

/// @nodoc
mixin _$LandRecord {
  int? get start => throw _privateConstructorUsedError;
  int? get end => throw _privateConstructorUsedError;
  int? get page => throw _privateConstructorUsedError;
  int? get rows => throw _privateConstructorUsedError;
  dynamic get sort => throw _privateConstructorUsedError;
  dynamic get dir => throw _privateConstructorUsedError;
  dynamic get oldValue => throw _privateConstructorUsedError;
  bool? get needPagination => throw _privateConstructorUsedError;
  dynamic get userId => throw _privateConstructorUsedError;
  int? get plantingActualId => throw _privateConstructorUsedError;
  int? get plotGrowPatternsId => throw _privateConstructorUsedError;
  dynamic get patternsLinkId => throw _privateConstructorUsedError;
  dynamic get agriRecordsId => throw _privateConstructorUsedError;
  int? get growPatternsId => throw _privateConstructorUsedError;
  String? get growPatternsName => throw _privateConstructorUsedError;
  String? get landNo => throw _privateConstructorUsedError;
  String? get landName => throw _privateConstructorUsedError;
  String? get landArea => throw _privateConstructorUsedError;
  dynamic get plotBase64 => throw _privateConstructorUsedError;
  String? get orgCode => throw _privateConstructorUsedError;
  String? get orgName => throw _privateConstructorUsedError;
  String? get statYear => throw _privateConstructorUsedError;
  String? get raiseCrops => throw _privateConstructorUsedError;
  String? get raiseCropsNm => throw _privateConstructorUsedError;
  int? get raiseCropsVariety => throw _privateConstructorUsedError;
  String? get raiseCropsVarietyNm => throw _privateConstructorUsedError;
  String? get isHave => throw _privateConstructorUsedError;
  dynamic get nowDate => throw _privateConstructorUsedError;
  dynamic get actBeginDate => throw _privateConstructorUsedError;
  dynamic get linkCode => throw _privateConstructorUsedError;
  dynamic get remark => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  dynamic get createBy => throw _privateConstructorUsedError;
  dynamic get statusCd => throw _privateConstructorUsedError;
  dynamic get params => throw _privateConstructorUsedError;
  String? get workStationCode => throw _privateConstructorUsedError;
  String? get workStationName => throw _privateConstructorUsedError;
  dynamic get landNoList => throw _privateConstructorUsedError;
  String? get isHaveRecord => throw _privateConstructorUsedError;
  dynamic get workArea => throw _privateConstructorUsedError;
  dynamic get closeFlag => throw _privateConstructorUsedError;
  dynamic get isTrace => throw _privateConstructorUsedError;
  dynamic get backReason => throw _privateConstructorUsedError;
  dynamic get isBack => throw _privateConstructorUsedError;
  dynamic get agriRecordsIds => throw _privateConstructorUsedError;
  dynamic get outOeder => throw _privateConstructorUsedError;
  dynamic get workAreaOpt => throw _privateConstructorUsedError;
  int? get plotId => throw _privateConstructorUsedError;

  /// Serializes this LandRecord to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of LandRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $LandRecordCopyWith<LandRecord> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $LandRecordCopyWith<$Res> {
  factory $LandRecordCopyWith(
          LandRecord value, $Res Function(LandRecord) then) =
      _$LandRecordCopyWithImpl<$Res, LandRecord>;
  @useResult
  $Res call(
      {int? start,
      int? end,
      int? page,
      int? rows,
      dynamic sort,
      dynamic dir,
      dynamic oldValue,
      bool? needPagination,
      dynamic userId,
      int? plantingActualId,
      int? plotGrowPatternsId,
      dynamic patternsLinkId,
      dynamic agriRecordsId,
      int? growPatternsId,
      String? growPatternsName,
      String? landNo,
      String? landName,
      String? landArea,
      dynamic plotBase64,
      String? orgCode,
      String? orgName,
      String? statYear,
      String? raiseCrops,
      String? raiseCropsNm,
      int? raiseCropsVariety,
      String? raiseCropsVarietyNm,
      String? isHave,
      dynamic nowDate,
      dynamic actBeginDate,
      dynamic linkCode,
      dynamic remark,
      String? createTime,
      dynamic createBy,
      dynamic statusCd,
      dynamic params,
      String? workStationCode,
      String? workStationName,
      dynamic landNoList,
      String? isHaveRecord,
      dynamic workArea,
      dynamic closeFlag,
      dynamic isTrace,
      dynamic backReason,
      dynamic isBack,
      dynamic agriRecordsIds,
      dynamic outOeder,
      dynamic workAreaOpt,
      int? plotId});
}

/// @nodoc
class _$LandRecordCopyWithImpl<$Res, $Val extends LandRecord>
    implements $LandRecordCopyWith<$Res> {
  _$LandRecordCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of LandRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? start = freezed,
    Object? end = freezed,
    Object? page = freezed,
    Object? rows = freezed,
    Object? sort = freezed,
    Object? dir = freezed,
    Object? oldValue = freezed,
    Object? needPagination = freezed,
    Object? userId = freezed,
    Object? plantingActualId = freezed,
    Object? plotGrowPatternsId = freezed,
    Object? patternsLinkId = freezed,
    Object? agriRecordsId = freezed,
    Object? growPatternsId = freezed,
    Object? growPatternsName = freezed,
    Object? landNo = freezed,
    Object? landName = freezed,
    Object? landArea = freezed,
    Object? plotBase64 = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? statYear = freezed,
    Object? raiseCrops = freezed,
    Object? raiseCropsNm = freezed,
    Object? raiseCropsVariety = freezed,
    Object? raiseCropsVarietyNm = freezed,
    Object? isHave = freezed,
    Object? nowDate = freezed,
    Object? actBeginDate = freezed,
    Object? linkCode = freezed,
    Object? remark = freezed,
    Object? createTime = freezed,
    Object? createBy = freezed,
    Object? statusCd = freezed,
    Object? params = freezed,
    Object? workStationCode = freezed,
    Object? workStationName = freezed,
    Object? landNoList = freezed,
    Object? isHaveRecord = freezed,
    Object? workArea = freezed,
    Object? closeFlag = freezed,
    Object? isTrace = freezed,
    Object? backReason = freezed,
    Object? isBack = freezed,
    Object? agriRecordsIds = freezed,
    Object? outOeder = freezed,
    Object? workAreaOpt = freezed,
    Object? plotId = freezed,
  }) {
    return _then(_value.copyWith(
      start: freezed == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as int?,
      end: freezed == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int?,
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      rows: freezed == rows
          ? _value.rows
          : rows // ignore: cast_nullable_to_non_nullable
              as int?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dir: freezed == dir
          ? _value.dir
          : dir // ignore: cast_nullable_to_non_nullable
              as dynamic,
      oldValue: freezed == oldValue
          ? _value.oldValue
          : oldValue // ignore: cast_nullable_to_non_nullable
              as dynamic,
      needPagination: freezed == needPagination
          ? _value.needPagination
          : needPagination // ignore: cast_nullable_to_non_nullable
              as bool?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plantingActualId: freezed == plantingActualId
          ? _value.plantingActualId
          : plantingActualId // ignore: cast_nullable_to_non_nullable
              as int?,
      plotGrowPatternsId: freezed == plotGrowPatternsId
          ? _value.plotGrowPatternsId
          : plotGrowPatternsId // ignore: cast_nullable_to_non_nullable
              as int?,
      patternsLinkId: freezed == patternsLinkId
          ? _value.patternsLinkId
          : patternsLinkId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      agriRecordsId: freezed == agriRecordsId
          ? _value.agriRecordsId
          : agriRecordsId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      growPatternsId: freezed == growPatternsId
          ? _value.growPatternsId
          : growPatternsId // ignore: cast_nullable_to_non_nullable
              as int?,
      growPatternsName: freezed == growPatternsName
          ? _value.growPatternsName
          : growPatternsName // ignore: cast_nullable_to_non_nullable
              as String?,
      landNo: freezed == landNo
          ? _value.landNo
          : landNo // ignore: cast_nullable_to_non_nullable
              as String?,
      landName: freezed == landName
          ? _value.landName
          : landName // ignore: cast_nullable_to_non_nullable
              as String?,
      landArea: freezed == landArea
          ? _value.landArea
          : landArea // ignore: cast_nullable_to_non_nullable
              as String?,
      plotBase64: freezed == plotBase64
          ? _value.plotBase64
          : plotBase64 // ignore: cast_nullable_to_non_nullable
              as dynamic,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      statYear: freezed == statYear
          ? _value.statYear
          : statYear // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCrops: freezed == raiseCrops
          ? _value.raiseCrops
          : raiseCrops // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsNm: freezed == raiseCropsNm
          ? _value.raiseCropsNm
          : raiseCropsNm // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsVariety: freezed == raiseCropsVariety
          ? _value.raiseCropsVariety
          : raiseCropsVariety // ignore: cast_nullable_to_non_nullable
              as int?,
      raiseCropsVarietyNm: freezed == raiseCropsVarietyNm
          ? _value.raiseCropsVarietyNm
          : raiseCropsVarietyNm // ignore: cast_nullable_to_non_nullable
              as String?,
      isHave: freezed == isHave
          ? _value.isHave
          : isHave // ignore: cast_nullable_to_non_nullable
              as String?,
      nowDate: freezed == nowDate
          ? _value.nowDate
          : nowDate // ignore: cast_nullable_to_non_nullable
              as dynamic,
      actBeginDate: freezed == actBeginDate
          ? _value.actBeginDate
          : actBeginDate // ignore: cast_nullable_to_non_nullable
              as dynamic,
      linkCode: freezed == linkCode
          ? _value.linkCode
          : linkCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as dynamic,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workStationCode: freezed == workStationCode
          ? _value.workStationCode
          : workStationCode // ignore: cast_nullable_to_non_nullable
              as String?,
      workStationName: freezed == workStationName
          ? _value.workStationName
          : workStationName // ignore: cast_nullable_to_non_nullable
              as String?,
      landNoList: freezed == landNoList
          ? _value.landNoList
          : landNoList // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isHaveRecord: freezed == isHaveRecord
          ? _value.isHaveRecord
          : isHaveRecord // ignore: cast_nullable_to_non_nullable
              as String?,
      workArea: freezed == workArea
          ? _value.workArea
          : workArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      closeFlag: freezed == closeFlag
          ? _value.closeFlag
          : closeFlag // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isTrace: freezed == isTrace
          ? _value.isTrace
          : isTrace // ignore: cast_nullable_to_non_nullable
              as dynamic,
      backReason: freezed == backReason
          ? _value.backReason
          : backReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isBack: freezed == isBack
          ? _value.isBack
          : isBack // ignore: cast_nullable_to_non_nullable
              as dynamic,
      agriRecordsIds: freezed == agriRecordsIds
          ? _value.agriRecordsIds
          : agriRecordsIds // ignore: cast_nullable_to_non_nullable
              as dynamic,
      outOeder: freezed == outOeder
          ? _value.outOeder
          : outOeder // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workAreaOpt: freezed == workAreaOpt
          ? _value.workAreaOpt
          : workAreaOpt // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotId: freezed == plotId
          ? _value.plotId
          : plotId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$LandRecordImplCopyWith<$Res>
    implements $LandRecordCopyWith<$Res> {
  factory _$$LandRecordImplCopyWith(
          _$LandRecordImpl value, $Res Function(_$LandRecordImpl) then) =
      __$$LandRecordImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? start,
      int? end,
      int? page,
      int? rows,
      dynamic sort,
      dynamic dir,
      dynamic oldValue,
      bool? needPagination,
      dynamic userId,
      int? plantingActualId,
      int? plotGrowPatternsId,
      dynamic patternsLinkId,
      dynamic agriRecordsId,
      int? growPatternsId,
      String? growPatternsName,
      String? landNo,
      String? landName,
      String? landArea,
      dynamic plotBase64,
      String? orgCode,
      String? orgName,
      String? statYear,
      String? raiseCrops,
      String? raiseCropsNm,
      int? raiseCropsVariety,
      String? raiseCropsVarietyNm,
      String? isHave,
      dynamic nowDate,
      dynamic actBeginDate,
      dynamic linkCode,
      dynamic remark,
      String? createTime,
      dynamic createBy,
      dynamic statusCd,
      dynamic params,
      String? workStationCode,
      String? workStationName,
      dynamic landNoList,
      String? isHaveRecord,
      dynamic workArea,
      dynamic closeFlag,
      dynamic isTrace,
      dynamic backReason,
      dynamic isBack,
      dynamic agriRecordsIds,
      dynamic outOeder,
      dynamic workAreaOpt,
      int? plotId});
}

/// @nodoc
class __$$LandRecordImplCopyWithImpl<$Res>
    extends _$LandRecordCopyWithImpl<$Res, _$LandRecordImpl>
    implements _$$LandRecordImplCopyWith<$Res> {
  __$$LandRecordImplCopyWithImpl(
      _$LandRecordImpl _value, $Res Function(_$LandRecordImpl) _then)
      : super(_value, _then);

  /// Create a copy of LandRecord
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? start = freezed,
    Object? end = freezed,
    Object? page = freezed,
    Object? rows = freezed,
    Object? sort = freezed,
    Object? dir = freezed,
    Object? oldValue = freezed,
    Object? needPagination = freezed,
    Object? userId = freezed,
    Object? plantingActualId = freezed,
    Object? plotGrowPatternsId = freezed,
    Object? patternsLinkId = freezed,
    Object? agriRecordsId = freezed,
    Object? growPatternsId = freezed,
    Object? growPatternsName = freezed,
    Object? landNo = freezed,
    Object? landName = freezed,
    Object? landArea = freezed,
    Object? plotBase64 = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? statYear = freezed,
    Object? raiseCrops = freezed,
    Object? raiseCropsNm = freezed,
    Object? raiseCropsVariety = freezed,
    Object? raiseCropsVarietyNm = freezed,
    Object? isHave = freezed,
    Object? nowDate = freezed,
    Object? actBeginDate = freezed,
    Object? linkCode = freezed,
    Object? remark = freezed,
    Object? createTime = freezed,
    Object? createBy = freezed,
    Object? statusCd = freezed,
    Object? params = freezed,
    Object? workStationCode = freezed,
    Object? workStationName = freezed,
    Object? landNoList = freezed,
    Object? isHaveRecord = freezed,
    Object? workArea = freezed,
    Object? closeFlag = freezed,
    Object? isTrace = freezed,
    Object? backReason = freezed,
    Object? isBack = freezed,
    Object? agriRecordsIds = freezed,
    Object? outOeder = freezed,
    Object? workAreaOpt = freezed,
    Object? plotId = freezed,
  }) {
    return _then(_$LandRecordImpl(
      start: freezed == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as int?,
      end: freezed == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int?,
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      rows: freezed == rows
          ? _value.rows
          : rows // ignore: cast_nullable_to_non_nullable
              as int?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dir: freezed == dir
          ? _value.dir
          : dir // ignore: cast_nullable_to_non_nullable
              as dynamic,
      oldValue: freezed == oldValue
          ? _value.oldValue
          : oldValue // ignore: cast_nullable_to_non_nullable
              as dynamic,
      needPagination: freezed == needPagination
          ? _value.needPagination
          : needPagination // ignore: cast_nullable_to_non_nullable
              as bool?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plantingActualId: freezed == plantingActualId
          ? _value.plantingActualId
          : plantingActualId // ignore: cast_nullable_to_non_nullable
              as int?,
      plotGrowPatternsId: freezed == plotGrowPatternsId
          ? _value.plotGrowPatternsId
          : plotGrowPatternsId // ignore: cast_nullable_to_non_nullable
              as int?,
      patternsLinkId: freezed == patternsLinkId
          ? _value.patternsLinkId
          : patternsLinkId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      agriRecordsId: freezed == agriRecordsId
          ? _value.agriRecordsId
          : agriRecordsId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      growPatternsId: freezed == growPatternsId
          ? _value.growPatternsId
          : growPatternsId // ignore: cast_nullable_to_non_nullable
              as int?,
      growPatternsName: freezed == growPatternsName
          ? _value.growPatternsName
          : growPatternsName // ignore: cast_nullable_to_non_nullable
              as String?,
      landNo: freezed == landNo
          ? _value.landNo
          : landNo // ignore: cast_nullable_to_non_nullable
              as String?,
      landName: freezed == landName
          ? _value.landName
          : landName // ignore: cast_nullable_to_non_nullable
              as String?,
      landArea: freezed == landArea
          ? _value.landArea
          : landArea // ignore: cast_nullable_to_non_nullable
              as String?,
      plotBase64: freezed == plotBase64
          ? _value.plotBase64
          : plotBase64 // ignore: cast_nullable_to_non_nullable
              as dynamic,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      statYear: freezed == statYear
          ? _value.statYear
          : statYear // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCrops: freezed == raiseCrops
          ? _value.raiseCrops
          : raiseCrops // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsNm: freezed == raiseCropsNm
          ? _value.raiseCropsNm
          : raiseCropsNm // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsVariety: freezed == raiseCropsVariety
          ? _value.raiseCropsVariety
          : raiseCropsVariety // ignore: cast_nullable_to_non_nullable
              as int?,
      raiseCropsVarietyNm: freezed == raiseCropsVarietyNm
          ? _value.raiseCropsVarietyNm
          : raiseCropsVarietyNm // ignore: cast_nullable_to_non_nullable
              as String?,
      isHave: freezed == isHave
          ? _value.isHave
          : isHave // ignore: cast_nullable_to_non_nullable
              as String?,
      nowDate: freezed == nowDate
          ? _value.nowDate
          : nowDate // ignore: cast_nullable_to_non_nullable
              as dynamic,
      actBeginDate: freezed == actBeginDate
          ? _value.actBeginDate
          : actBeginDate // ignore: cast_nullable_to_non_nullable
              as dynamic,
      linkCode: freezed == linkCode
          ? _value.linkCode
          : linkCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as dynamic,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workStationCode: freezed == workStationCode
          ? _value.workStationCode
          : workStationCode // ignore: cast_nullable_to_non_nullable
              as String?,
      workStationName: freezed == workStationName
          ? _value.workStationName
          : workStationName // ignore: cast_nullable_to_non_nullable
              as String?,
      landNoList: freezed == landNoList
          ? _value.landNoList
          : landNoList // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isHaveRecord: freezed == isHaveRecord
          ? _value.isHaveRecord
          : isHaveRecord // ignore: cast_nullable_to_non_nullable
              as String?,
      workArea: freezed == workArea
          ? _value.workArea
          : workArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      closeFlag: freezed == closeFlag
          ? _value.closeFlag
          : closeFlag // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isTrace: freezed == isTrace
          ? _value.isTrace
          : isTrace // ignore: cast_nullable_to_non_nullable
              as dynamic,
      backReason: freezed == backReason
          ? _value.backReason
          : backReason // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isBack: freezed == isBack
          ? _value.isBack
          : isBack // ignore: cast_nullable_to_non_nullable
              as dynamic,
      agriRecordsIds: freezed == agriRecordsIds
          ? _value.agriRecordsIds
          : agriRecordsIds // ignore: cast_nullable_to_non_nullable
              as dynamic,
      outOeder: freezed == outOeder
          ? _value.outOeder
          : outOeder // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workAreaOpt: freezed == workAreaOpt
          ? _value.workAreaOpt
          : workAreaOpt // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotId: freezed == plotId
          ? _value.plotId
          : plotId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$LandRecordImpl implements _LandRecord {
  _$LandRecordImpl(
      {this.start,
      this.end,
      this.page,
      this.rows,
      this.sort,
      this.dir,
      this.oldValue,
      this.needPagination,
      this.userId,
      this.plantingActualId,
      this.plotGrowPatternsId,
      this.patternsLinkId,
      this.agriRecordsId,
      this.growPatternsId,
      this.growPatternsName,
      this.landNo,
      this.landName,
      this.landArea,
      this.plotBase64,
      this.orgCode,
      this.orgName,
      this.statYear,
      this.raiseCrops,
      this.raiseCropsNm,
      this.raiseCropsVariety,
      this.raiseCropsVarietyNm,
      this.isHave,
      this.nowDate,
      this.actBeginDate,
      this.linkCode,
      this.remark,
      this.createTime,
      this.createBy,
      this.statusCd,
      this.params,
      this.workStationCode,
      this.workStationName,
      this.landNoList,
      this.isHaveRecord,
      this.workArea,
      this.closeFlag,
      this.isTrace,
      this.backReason,
      this.isBack,
      this.agriRecordsIds,
      this.outOeder,
      this.workAreaOpt,
      this.plotId});

  factory _$LandRecordImpl.fromJson(Map<String, dynamic> json) =>
      _$$LandRecordImplFromJson(json);

  @override
  final int? start;
  @override
  final int? end;
  @override
  final int? page;
  @override
  final int? rows;
  @override
  final dynamic sort;
  @override
  final dynamic dir;
  @override
  final dynamic oldValue;
  @override
  final bool? needPagination;
  @override
  final dynamic userId;
  @override
  final int? plantingActualId;
  @override
  final int? plotGrowPatternsId;
  @override
  final dynamic patternsLinkId;
  @override
  final dynamic agriRecordsId;
  @override
  final int? growPatternsId;
  @override
  final String? growPatternsName;
  @override
  final String? landNo;
  @override
  final String? landName;
  @override
  final String? landArea;
  @override
  final dynamic plotBase64;
  @override
  final String? orgCode;
  @override
  final String? orgName;
  @override
  final String? statYear;
  @override
  final String? raiseCrops;
  @override
  final String? raiseCropsNm;
  @override
  final int? raiseCropsVariety;
  @override
  final String? raiseCropsVarietyNm;
  @override
  final String? isHave;
  @override
  final dynamic nowDate;
  @override
  final dynamic actBeginDate;
  @override
  final dynamic linkCode;
  @override
  final dynamic remark;
  @override
  final String? createTime;
  @override
  final dynamic createBy;
  @override
  final dynamic statusCd;
  @override
  final dynamic params;
  @override
  final String? workStationCode;
  @override
  final String? workStationName;
  @override
  final dynamic landNoList;
  @override
  final String? isHaveRecord;
  @override
  final dynamic workArea;
  @override
  final dynamic closeFlag;
  @override
  final dynamic isTrace;
  @override
  final dynamic backReason;
  @override
  final dynamic isBack;
  @override
  final dynamic agriRecordsIds;
  @override
  final dynamic outOeder;
  @override
  final dynamic workAreaOpt;
  @override
  final int? plotId;

  @override
  String toString() {
    return 'LandRecord(start: $start, end: $end, page: $page, rows: $rows, sort: $sort, dir: $dir, oldValue: $oldValue, needPagination: $needPagination, userId: $userId, plantingActualId: $plantingActualId, plotGrowPatternsId: $plotGrowPatternsId, patternsLinkId: $patternsLinkId, agriRecordsId: $agriRecordsId, growPatternsId: $growPatternsId, growPatternsName: $growPatternsName, landNo: $landNo, landName: $landName, landArea: $landArea, plotBase64: $plotBase64, orgCode: $orgCode, orgName: $orgName, statYear: $statYear, raiseCrops: $raiseCrops, raiseCropsNm: $raiseCropsNm, raiseCropsVariety: $raiseCropsVariety, raiseCropsVarietyNm: $raiseCropsVarietyNm, isHave: $isHave, nowDate: $nowDate, actBeginDate: $actBeginDate, linkCode: $linkCode, remark: $remark, createTime: $createTime, createBy: $createBy, statusCd: $statusCd, params: $params, workStationCode: $workStationCode, workStationName: $workStationName, landNoList: $landNoList, isHaveRecord: $isHaveRecord, workArea: $workArea, closeFlag: $closeFlag, isTrace: $isTrace, backReason: $backReason, isBack: $isBack, agriRecordsIds: $agriRecordsIds, outOeder: $outOeder, workAreaOpt: $workAreaOpt, plotId: $plotId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$LandRecordImpl &&
            (identical(other.start, start) || other.start == start) &&
            (identical(other.end, end) || other.end == end) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.rows, rows) || other.rows == rows) &&
            const DeepCollectionEquality().equals(other.sort, sort) &&
            const DeepCollectionEquality().equals(other.dir, dir) &&
            const DeepCollectionEquality().equals(other.oldValue, oldValue) &&
            (identical(other.needPagination, needPagination) ||
                other.needPagination == needPagination) &&
            const DeepCollectionEquality().equals(other.userId, userId) &&
            (identical(other.plantingActualId, plantingActualId) ||
                other.plantingActualId == plantingActualId) &&
            (identical(other.plotGrowPatternsId, plotGrowPatternsId) ||
                other.plotGrowPatternsId == plotGrowPatternsId) &&
            const DeepCollectionEquality()
                .equals(other.patternsLinkId, patternsLinkId) &&
            const DeepCollectionEquality()
                .equals(other.agriRecordsId, agriRecordsId) &&
            (identical(other.growPatternsId, growPatternsId) ||
                other.growPatternsId == growPatternsId) &&
            (identical(other.growPatternsName, growPatternsName) ||
                other.growPatternsName == growPatternsName) &&
            (identical(other.landNo, landNo) || other.landNo == landNo) &&
            (identical(other.landName, landName) ||
                other.landName == landName) &&
            (identical(other.landArea, landArea) ||
                other.landArea == landArea) &&
            const DeepCollectionEquality()
                .equals(other.plotBase64, plotBase64) &&
            (identical(other.orgCode, orgCode) || other.orgCode == orgCode) &&
            (identical(other.orgName, orgName) || other.orgName == orgName) &&
            (identical(other.statYear, statYear) ||
                other.statYear == statYear) &&
            (identical(other.raiseCrops, raiseCrops) ||
                other.raiseCrops == raiseCrops) &&
            (identical(other.raiseCropsNm, raiseCropsNm) ||
                other.raiseCropsNm == raiseCropsNm) &&
            (identical(other.raiseCropsVariety, raiseCropsVariety) ||
                other.raiseCropsVariety == raiseCropsVariety) &&
            (identical(other.raiseCropsVarietyNm, raiseCropsVarietyNm) ||
                other.raiseCropsVarietyNm == raiseCropsVarietyNm) &&
            (identical(other.isHave, isHave) || other.isHave == isHave) &&
            const DeepCollectionEquality().equals(other.nowDate, nowDate) &&
            const DeepCollectionEquality()
                .equals(other.actBeginDate, actBeginDate) &&
            const DeepCollectionEquality().equals(other.linkCode, linkCode) &&
            const DeepCollectionEquality().equals(other.remark, remark) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            const DeepCollectionEquality().equals(other.createBy, createBy) &&
            const DeepCollectionEquality().equals(other.statusCd, statusCd) &&
            const DeepCollectionEquality().equals(other.params, params) &&
            (identical(other.workStationCode, workStationCode) ||
                other.workStationCode == workStationCode) &&
            (identical(other.workStationName, workStationName) ||
                other.workStationName == workStationName) &&
            const DeepCollectionEquality()
                .equals(other.landNoList, landNoList) &&
            (identical(other.isHaveRecord, isHaveRecord) ||
                other.isHaveRecord == isHaveRecord) &&
            const DeepCollectionEquality().equals(other.workArea, workArea) &&
            const DeepCollectionEquality().equals(other.closeFlag, closeFlag) &&
            const DeepCollectionEquality().equals(other.isTrace, isTrace) &&
            const DeepCollectionEquality()
                .equals(other.backReason, backReason) &&
            const DeepCollectionEquality().equals(other.isBack, isBack) &&
            const DeepCollectionEquality()
                .equals(other.agriRecordsIds, agriRecordsIds) &&
            const DeepCollectionEquality().equals(other.outOeder, outOeder) &&
            const DeepCollectionEquality()
                .equals(other.workAreaOpt, workAreaOpt) &&
            (identical(other.plotId, plotId) || other.plotId == plotId));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        start,
        end,
        page,
        rows,
        const DeepCollectionEquality().hash(sort),
        const DeepCollectionEquality().hash(dir),
        const DeepCollectionEquality().hash(oldValue),
        needPagination,
        const DeepCollectionEquality().hash(userId),
        plantingActualId,
        plotGrowPatternsId,
        const DeepCollectionEquality().hash(patternsLinkId),
        const DeepCollectionEquality().hash(agriRecordsId),
        growPatternsId,
        growPatternsName,
        landNo,
        landName,
        landArea,
        const DeepCollectionEquality().hash(plotBase64),
        orgCode,
        orgName,
        statYear,
        raiseCrops,
        raiseCropsNm,
        raiseCropsVariety,
        raiseCropsVarietyNm,
        isHave,
        const DeepCollectionEquality().hash(nowDate),
        const DeepCollectionEquality().hash(actBeginDate),
        const DeepCollectionEquality().hash(linkCode),
        const DeepCollectionEquality().hash(remark),
        createTime,
        const DeepCollectionEquality().hash(createBy),
        const DeepCollectionEquality().hash(statusCd),
        const DeepCollectionEquality().hash(params),
        workStationCode,
        workStationName,
        const DeepCollectionEquality().hash(landNoList),
        isHaveRecord,
        const DeepCollectionEquality().hash(workArea),
        const DeepCollectionEquality().hash(closeFlag),
        const DeepCollectionEquality().hash(isTrace),
        const DeepCollectionEquality().hash(backReason),
        const DeepCollectionEquality().hash(isBack),
        const DeepCollectionEquality().hash(agriRecordsIds),
        const DeepCollectionEquality().hash(outOeder),
        const DeepCollectionEquality().hash(workAreaOpt),
        plotId
      ]);

  /// Create a copy of LandRecord
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$LandRecordImplCopyWith<_$LandRecordImpl> get copyWith =>
      __$$LandRecordImplCopyWithImpl<_$LandRecordImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$LandRecordImplToJson(
      this,
    );
  }
}

abstract class _LandRecord implements LandRecord {
  factory _LandRecord(
      {final int? start,
      final int? end,
      final int? page,
      final int? rows,
      final dynamic sort,
      final dynamic dir,
      final dynamic oldValue,
      final bool? needPagination,
      final dynamic userId,
      final int? plantingActualId,
      final int? plotGrowPatternsId,
      final dynamic patternsLinkId,
      final dynamic agriRecordsId,
      final int? growPatternsId,
      final String? growPatternsName,
      final String? landNo,
      final String? landName,
      final String? landArea,
      final dynamic plotBase64,
      final String? orgCode,
      final String? orgName,
      final String? statYear,
      final String? raiseCrops,
      final String? raiseCropsNm,
      final int? raiseCropsVariety,
      final String? raiseCropsVarietyNm,
      final String? isHave,
      final dynamic nowDate,
      final dynamic actBeginDate,
      final dynamic linkCode,
      final dynamic remark,
      final String? createTime,
      final dynamic createBy,
      final dynamic statusCd,
      final dynamic params,
      final String? workStationCode,
      final String? workStationName,
      final dynamic landNoList,
      final String? isHaveRecord,
      final dynamic workArea,
      final dynamic closeFlag,
      final dynamic isTrace,
      final dynamic backReason,
      final dynamic isBack,
      final dynamic agriRecordsIds,
      final dynamic outOeder,
      final dynamic workAreaOpt,
      final int? plotId}) = _$LandRecordImpl;

  factory _LandRecord.fromJson(Map<String, dynamic> json) =
      _$LandRecordImpl.fromJson;

  @override
  int? get start;
  @override
  int? get end;
  @override
  int? get page;
  @override
  int? get rows;
  @override
  dynamic get sort;
  @override
  dynamic get dir;
  @override
  dynamic get oldValue;
  @override
  bool? get needPagination;
  @override
  dynamic get userId;
  @override
  int? get plantingActualId;
  @override
  int? get plotGrowPatternsId;
  @override
  dynamic get patternsLinkId;
  @override
  dynamic get agriRecordsId;
  @override
  int? get growPatternsId;
  @override
  String? get growPatternsName;
  @override
  String? get landNo;
  @override
  String? get landName;
  @override
  String? get landArea;
  @override
  dynamic get plotBase64;
  @override
  String? get orgCode;
  @override
  String? get orgName;
  @override
  String? get statYear;
  @override
  String? get raiseCrops;
  @override
  String? get raiseCropsNm;
  @override
  int? get raiseCropsVariety;
  @override
  String? get raiseCropsVarietyNm;
  @override
  String? get isHave;
  @override
  dynamic get nowDate;
  @override
  dynamic get actBeginDate;
  @override
  dynamic get linkCode;
  @override
  dynamic get remark;
  @override
  String? get createTime;
  @override
  dynamic get createBy;
  @override
  dynamic get statusCd;
  @override
  dynamic get params;
  @override
  String? get workStationCode;
  @override
  String? get workStationName;
  @override
  dynamic get landNoList;
  @override
  String? get isHaveRecord;
  @override
  dynamic get workArea;
  @override
  dynamic get closeFlag;
  @override
  dynamic get isTrace;
  @override
  dynamic get backReason;
  @override
  dynamic get isBack;
  @override
  dynamic get agriRecordsIds;
  @override
  dynamic get outOeder;
  @override
  dynamic get workAreaOpt;
  @override
  int? get plotId;

  /// Create a copy of LandRecord
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$LandRecordImplCopyWith<_$LandRecordImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
