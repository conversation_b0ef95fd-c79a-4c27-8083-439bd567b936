// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'record_farming_element.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$RecordFarmingElementImpl _$$RecordFarmingElementImplFromJson(
        Map<String, dynamic> json) =>
    _$RecordFarmingElementImpl(
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
      page: (json['page'] as num?)?.toInt(),
      rows: (json['rows'] as num?)?.toInt(),
      sort: json['sort'] as String?,
      dir: json['dir'] as String?,
      oldValue: json['oldValue'],
      needPagination: json['needPagination'] as bool?,
      userId: json['userId'] as String?,
      patternsLinkId: json['patternsLinkId'],
      actBeginDate: json['actBeginDate'] as String?,
      actEndDate: json['actEndDate'] as String?,
      remark: json['remark'] as String?,
      createBy: (json['createBy'] as num?)?.toInt(),
      createName: json['createName'] as String?,
      agriRecordsId: json['agriRecordsId'],
      statYear: json['statYear'],
      orgCode: json['orgCode'],
      orgName: json['orgName'],
      plotNo: json['plotNo'],
      plotName: json['plotName'],
      createTime: json['createTime'],
      updateBy: (json['updateBy'] as num?)?.toInt(),
      updateTime: json['updateTime'],
      statusCd: json['statusCd'],
      longitude: json['longitude'],
      latitude: json['latitude'],
      agriRecordsDesc: json['agriRecordsDesc'] as String?,
      workPic1: json['workPic1'] as String?,
      workPic2: json['workPic2'] as String?,
      workPic3: json['workPic3'] as String?,
      workVideo1: json['workVideo1'] as String?,
      workVideo2: json['workVideo2'] as String?,
      workVideo3: json['workVideo3'] as String?,
      otherVOList: (json['otherVOList'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      asAgriRecordsInputsList:
          (json['asAgriRecordsInputsList'] as List<dynamic>?)
              ?.map((e) => e as Map<String, dynamic>)
              .toList(),
      isGrow: json['isGrow'] as String?,
      isFertilize: json['isFertilize'] as String?,
      isPesticide: json['isPesticide'] as String?,
      params: json['params'],
      workStationCode: json['workStationCode'],
      workStationName: json['workStationName'],
      dataSource: json['dataSource'],
      plotList: (json['plotList'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
      growthPeriod: json['growthPeriod'],
      standard: json['standard'],
      workArea: (json['workArea'] as num?)?.toDouble(),
      plotArea: json['plotArea'],
      prodProcessCode: json['prodProcessCode'],
      prodProcessName: json['prodProcessName'],
      linkCode: json['linkCode'],
      linkName: json['linkName'],
      linkOrder: json['linkOrder'],
      children: json['children'],
      isSeedLand: json['isSeedLand'],
      raiseCrops: json['raiseCrops'],
      landType: json['landType'],
      growPatternsId: json['growPatternsId'],
      itemList: (json['itemList'] as List<dynamic>?)
          ?.map((e) => e as Map<String, dynamic>)
          .toList(),
    );

Map<String, dynamic> _$$RecordFarmingElementImplToJson(
        _$RecordFarmingElementImpl instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
      'page': instance.page,
      'rows': instance.rows,
      'sort': instance.sort,
      'dir': instance.dir,
      'oldValue': instance.oldValue,
      'needPagination': instance.needPagination,
      'userId': instance.userId,
      'patternsLinkId': instance.patternsLinkId,
      'actBeginDate': instance.actBeginDate,
      'actEndDate': instance.actEndDate,
      'remark': instance.remark,
      'createBy': instance.createBy,
      'createName': instance.createName,
      'agriRecordsId': instance.agriRecordsId,
      'statYear': instance.statYear,
      'orgCode': instance.orgCode,
      'orgName': instance.orgName,
      'plotNo': instance.plotNo,
      'plotName': instance.plotName,
      'createTime': instance.createTime,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'statusCd': instance.statusCd,
      'longitude': instance.longitude,
      'latitude': instance.latitude,
      'agriRecordsDesc': instance.agriRecordsDesc,
      'workPic1': instance.workPic1,
      'workPic2': instance.workPic2,
      'workPic3': instance.workPic3,
      'workVideo1': instance.workVideo1,
      'workVideo2': instance.workVideo2,
      'workVideo3': instance.workVideo3,
      'otherVOList': instance.otherVOList,
      'asAgriRecordsInputsList': instance.asAgriRecordsInputsList,
      'isGrow': instance.isGrow,
      'isFertilize': instance.isFertilize,
      'isPesticide': instance.isPesticide,
      'params': instance.params,
      'workStationCode': instance.workStationCode,
      'workStationName': instance.workStationName,
      'dataSource': instance.dataSource,
      'plotList': instance.plotList,
      'growthPeriod': instance.growthPeriod,
      'standard': instance.standard,
      'workArea': instance.workArea,
      'plotArea': instance.plotArea,
      'prodProcessCode': instance.prodProcessCode,
      'prodProcessName': instance.prodProcessName,
      'linkCode': instance.linkCode,
      'linkName': instance.linkName,
      'linkOrder': instance.linkOrder,
      'children': instance.children,
      'isSeedLand': instance.isSeedLand,
      'raiseCrops': instance.raiseCrops,
      'landType': instance.landType,
      'growPatternsId': instance.growPatternsId,
      'itemList': instance.itemList,
    };

_$OtherOVImpl _$$OtherOVImplFromJson(Map<String, dynamic> json) =>
    _$OtherOVImpl(
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
      page: (json['page'] as num?)?.toInt(),
      rows: (json['rows'] as num?)?.toInt(),
      sort: json['sort'],
      dir: json['dir'],
      oldValue: json['oldValue'],
      needPagination: json['needPagination'] as bool?,
      userId: json['userId'],
      elementLinkCfgId: (json['elementLinkCfgId'] as num?)?.toInt(),
      patternsLinkId: (json['patternsLinkId'] as num?)?.toInt(),
      elementCfgId: json['elementCfgId'],
      elementId: (json['elementId'] as num?)?.toInt(),
      elementName: json['elementName'] as String?,
      isNotNull: json['isNotNull'] as String?,
      remark: json['remark'],
      statYear: json['statYear'] as String?,
      orgCode: json['orgCode'] as String?,
      orgName: json['orgName'] as String?,
      cropCode: json['cropCode'] as String?,
      prodProcessCode: json['prodProcessCode'] as String?,
      elementType: json['elementType'] as String?,
      elementFillMethod: json['elementFillMethod'] as String?,
      elementPromptInfo: json['elementPromptInfo'] as String?,
      elementDefValue: json['elementDefValue'] as String?,
      elementLength: (json['elementLength'] as num?)?.toInt(),
      elementPrecision: (json['elementPrecision'] as num?)?.toInt(),
      dictKey: json['dictKey'] as String?,
      elementUnit: json['elementUnit'] as String?,
      orders: (json['orders'] as num?)?.toInt(),
      agriRecordsDtlId: (json['agriRecordsDtlId'] as num?)?.toInt(),
      elementValue: json['elementValue'] as String?,
      linkCode: json['linkCode'],
      externalDataId: json['externalDataId'],
      elementValueId: (json['elementValueId'] as num?)?.toInt(),
      isMultiple: json['isMultiple'] as String?,
      minValue: (json['minValue'] as num?)?.toDouble(),
      maxValue: (json['maxValue'] as num?)?.toDouble(),
      params: json['params'],
    );

Map<String, dynamic> _$$OtherOVImplToJson(_$OtherOVImpl instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
      'page': instance.page,
      'rows': instance.rows,
      'sort': instance.sort,
      'dir': instance.dir,
      'oldValue': instance.oldValue,
      'needPagination': instance.needPagination,
      'userId': instance.userId,
      'elementLinkCfgId': instance.elementLinkCfgId,
      'patternsLinkId': instance.patternsLinkId,
      'elementCfgId': instance.elementCfgId,
      'elementId': instance.elementId,
      'elementName': instance.elementName,
      'isNotNull': instance.isNotNull,
      'remark': instance.remark,
      'statYear': instance.statYear,
      'orgCode': instance.orgCode,
      'orgName': instance.orgName,
      'cropCode': instance.cropCode,
      'prodProcessCode': instance.prodProcessCode,
      'elementType': instance.elementType,
      'elementFillMethod': instance.elementFillMethod,
      'elementPromptInfo': instance.elementPromptInfo,
      'elementDefValue': instance.elementDefValue,
      'elementLength': instance.elementLength,
      'elementPrecision': instance.elementPrecision,
      'dictKey': instance.dictKey,
      'elementUnit': instance.elementUnit,
      'orders': instance.orders,
      'agriRecordsDtlId': instance.agriRecordsDtlId,
      'elementValue': instance.elementValue,
      'linkCode': instance.linkCode,
      'externalDataId': instance.externalDataId,
      'elementValueId': instance.elementValueId,
      'isMultiple': instance.isMultiple,
      'minValue': instance.minValue,
      'maxValue': instance.maxValue,
      'params': instance.params,
    };

_$AgriRecordsInputImpl _$$AgriRecordsInputImplFromJson(
        Map<String, dynamic> json) =>
    _$AgriRecordsInputImpl(
      agriRecordsInputsId: (json['agriRecordsInputsId'] as num?)?.toInt(),
      agriRecordsId: (json['agriRecordsId'] as num?)?.toInt(),
      meansProdType: json['meansProdType'] as String?,
      meansProdId: (json['meansProdId'] as num?)?.toInt(),
      meansProdName: json['meansProdName'] as String?,
      totalDosage: (json['totalDosage'] as num?)?.toDouble(),
      meansProdUnit: json['meansProdUnit'] as String?,
      meansProdUnitName: json['meansProdUnitName'] as String?,
      instructionsPic1: json['instructionsPic1'] as String?,
      instructionsPic2: json['instructionsPic2'] as String?,
      instructionsPic3: json['instructionsPic3'] as String?,
      goodsPic1: json['goodsPic1'] as String?,
      goodsPic2: json['goodsPic2'] as String?,
      goodsPic3: json['goodsPic3'] as String?,
      actGoodsPic1: json['actGoodsPic1'] as String?,
      actGoodsPic2: json['actGoodsPic2'] as String?,
      actGoodsPic3: json['actGoodsPic3'] as String?,
      orders: json['orders'] as String?,
      remark: json['remark'] as String?,
      createBy: (json['createBy'] as num?)?.toInt(),
      createTime: json['createTime'] as String?,
      updateBy: (json['updateBy'] as num?)?.toInt(),
      updateTime: json['updateTime'] as String?,
      minValue: (json['minValue'] as num?)?.toDouble(),
      maxValue: (json['maxValue'] as num?)?.toDouble(),
      statusCd: (json['statusCd'] as num?)?.toInt(),
      params: json['params'],
      content: json['content'],
      manufacturer: json['manufacturer'],
    );

Map<String, dynamic> _$$AgriRecordsInputImplToJson(
        _$AgriRecordsInputImpl instance) =>
    <String, dynamic>{
      'agriRecordsInputsId': instance.agriRecordsInputsId,
      'agriRecordsId': instance.agriRecordsId,
      'meansProdType': instance.meansProdType,
      'meansProdId': instance.meansProdId,
      'meansProdName': instance.meansProdName,
      'totalDosage': instance.totalDosage,
      'meansProdUnit': instance.meansProdUnit,
      'meansProdUnitName': instance.meansProdUnitName,
      'instructionsPic1': instance.instructionsPic1,
      'instructionsPic2': instance.instructionsPic2,
      'instructionsPic3': instance.instructionsPic3,
      'goodsPic1': instance.goodsPic1,
      'goodsPic2': instance.goodsPic2,
      'goodsPic3': instance.goodsPic3,
      'actGoodsPic1': instance.actGoodsPic1,
      'actGoodsPic2': instance.actGoodsPic2,
      'actGoodsPic3': instance.actGoodsPic3,
      'orders': instance.orders,
      'remark': instance.remark,
      'createBy': instance.createBy,
      'createTime': instance.createTime,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'minValue': instance.minValue,
      'maxValue': instance.maxValue,
      'statusCd': instance.statusCd,
      'params': instance.params,
      'content': instance.content,
      'manufacturer': instance.manufacturer,
    };
