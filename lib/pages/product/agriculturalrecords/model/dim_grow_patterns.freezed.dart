// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'dim_grow_patterns.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DimGrowPatterns _$DimGrowPatternsFromJson(Map<String, dynamic> json) {
  return _DimGrowPatterns.fromJson(json);
}

/// @nodoc
mixin _$DimGrowPatterns {
  int? get growPatternsId => throw _privateConstructorUsedError;
  String? get growPatternsName => throw _privateConstructorUsedError;
  String? get orgCode => throw _privateConstructorUsedError;
  String? get orgName => throw _privateConstructorUsedError;
  String? get raiseCrops => throw _privateConstructorUsedError;
  String? get raiseCropsNm => throw _privateConstructorUsedError;
  String? get remark => throw _privateConstructorUsedError;
  int? get createBy => throw _privateConstructorUsedError;
  String? get createName => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  int? get updateBy => throw _privateConstructorUsedError;
  String? get updateTime => throw _privateConstructorUsedError;
  String? get statYear => throw _privateConstructorUsedError;
  String? get isDef => throw _privateConstructorUsedError;
  dynamic get isOrgPlant => throw _privateConstructorUsedError;
  int? get statusCd => throw _privateConstructorUsedError;
  dynamic get params => throw _privateConstructorUsedError;
  dynamic get varietyCdArrayList => throw _privateConstructorUsedError;
  dynamic get raiseCropsVarietyNm => throw _privateConstructorUsedError;
  dynamic get isSeedLand => throw _privateConstructorUsedError;

  /// Serializes this DimGrowPatterns to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DimGrowPatterns
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DimGrowPatternsCopyWith<DimGrowPatterns> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DimGrowPatternsCopyWith<$Res> {
  factory $DimGrowPatternsCopyWith(
          DimGrowPatterns value, $Res Function(DimGrowPatterns) then) =
      _$DimGrowPatternsCopyWithImpl<$Res, DimGrowPatterns>;
  @useResult
  $Res call(
      {int? growPatternsId,
      String? growPatternsName,
      String? orgCode,
      String? orgName,
      String? raiseCrops,
      String? raiseCropsNm,
      String? remark,
      int? createBy,
      String? createName,
      String? createTime,
      int? updateBy,
      String? updateTime,
      String? statYear,
      String? isDef,
      dynamic isOrgPlant,
      int? statusCd,
      dynamic params,
      dynamic varietyCdArrayList,
      dynamic raiseCropsVarietyNm,
      dynamic isSeedLand});
}

/// @nodoc
class _$DimGrowPatternsCopyWithImpl<$Res, $Val extends DimGrowPatterns>
    implements $DimGrowPatternsCopyWith<$Res> {
  _$DimGrowPatternsCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DimGrowPatterns
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? growPatternsId = freezed,
    Object? growPatternsName = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? raiseCrops = freezed,
    Object? raiseCropsNm = freezed,
    Object? remark = freezed,
    Object? createBy = freezed,
    Object? createName = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statYear = freezed,
    Object? isDef = freezed,
    Object? isOrgPlant = freezed,
    Object? statusCd = freezed,
    Object? params = freezed,
    Object? varietyCdArrayList = freezed,
    Object? raiseCropsVarietyNm = freezed,
    Object? isSeedLand = freezed,
  }) {
    return _then(_value.copyWith(
      growPatternsId: freezed == growPatternsId
          ? _value.growPatternsId
          : growPatternsId // ignore: cast_nullable_to_non_nullable
              as int?,
      growPatternsName: freezed == growPatternsName
          ? _value.growPatternsName
          : growPatternsName // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCrops: freezed == raiseCrops
          ? _value.raiseCrops
          : raiseCrops // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsNm: freezed == raiseCropsNm
          ? _value.raiseCropsNm
          : raiseCropsNm // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as int?,
      createName: freezed == createName
          ? _value.createName
          : createName // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      statYear: freezed == statYear
          ? _value.statYear
          : statYear // ignore: cast_nullable_to_non_nullable
              as String?,
      isDef: freezed == isDef
          ? _value.isDef
          : isDef // ignore: cast_nullable_to_non_nullable
              as String?,
      isOrgPlant: freezed == isOrgPlant
          ? _value.isOrgPlant
          : isOrgPlant // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as int?,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
      varietyCdArrayList: freezed == varietyCdArrayList
          ? _value.varietyCdArrayList
          : varietyCdArrayList // ignore: cast_nullable_to_non_nullable
              as dynamic,
      raiseCropsVarietyNm: freezed == raiseCropsVarietyNm
          ? _value.raiseCropsVarietyNm
          : raiseCropsVarietyNm // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSeedLand: freezed == isSeedLand
          ? _value.isSeedLand
          : isSeedLand // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DimGrowPatternsImplCopyWith<$Res>
    implements $DimGrowPatternsCopyWith<$Res> {
  factory _$$DimGrowPatternsImplCopyWith(_$DimGrowPatternsImpl value,
          $Res Function(_$DimGrowPatternsImpl) then) =
      __$$DimGrowPatternsImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? growPatternsId,
      String? growPatternsName,
      String? orgCode,
      String? orgName,
      String? raiseCrops,
      String? raiseCropsNm,
      String? remark,
      int? createBy,
      String? createName,
      String? createTime,
      int? updateBy,
      String? updateTime,
      String? statYear,
      String? isDef,
      dynamic isOrgPlant,
      int? statusCd,
      dynamic params,
      dynamic varietyCdArrayList,
      dynamic raiseCropsVarietyNm,
      dynamic isSeedLand});
}

/// @nodoc
class __$$DimGrowPatternsImplCopyWithImpl<$Res>
    extends _$DimGrowPatternsCopyWithImpl<$Res, _$DimGrowPatternsImpl>
    implements _$$DimGrowPatternsImplCopyWith<$Res> {
  __$$DimGrowPatternsImplCopyWithImpl(
      _$DimGrowPatternsImpl _value, $Res Function(_$DimGrowPatternsImpl) _then)
      : super(_value, _then);

  /// Create a copy of DimGrowPatterns
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? growPatternsId = freezed,
    Object? growPatternsName = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? raiseCrops = freezed,
    Object? raiseCropsNm = freezed,
    Object? remark = freezed,
    Object? createBy = freezed,
    Object? createName = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statYear = freezed,
    Object? isDef = freezed,
    Object? isOrgPlant = freezed,
    Object? statusCd = freezed,
    Object? params = freezed,
    Object? varietyCdArrayList = freezed,
    Object? raiseCropsVarietyNm = freezed,
    Object? isSeedLand = freezed,
  }) {
    return _then(_$DimGrowPatternsImpl(
      growPatternsId: freezed == growPatternsId
          ? _value.growPatternsId
          : growPatternsId // ignore: cast_nullable_to_non_nullable
              as int?,
      growPatternsName: freezed == growPatternsName
          ? _value.growPatternsName
          : growPatternsName // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCrops: freezed == raiseCrops
          ? _value.raiseCrops
          : raiseCrops // ignore: cast_nullable_to_non_nullable
              as String?,
      raiseCropsNm: freezed == raiseCropsNm
          ? _value.raiseCropsNm
          : raiseCropsNm // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as int?,
      createName: freezed == createName
          ? _value.createName
          : createName // ignore: cast_nullable_to_non_nullable
              as String?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      statYear: freezed == statYear
          ? _value.statYear
          : statYear // ignore: cast_nullable_to_non_nullable
              as String?,
      isDef: freezed == isDef
          ? _value.isDef
          : isDef // ignore: cast_nullable_to_non_nullable
              as String?,
      isOrgPlant: freezed == isOrgPlant
          ? _value.isOrgPlant
          : isOrgPlant // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as int?,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
      varietyCdArrayList: freezed == varietyCdArrayList
          ? _value.varietyCdArrayList
          : varietyCdArrayList // ignore: cast_nullable_to_non_nullable
              as dynamic,
      raiseCropsVarietyNm: freezed == raiseCropsVarietyNm
          ? _value.raiseCropsVarietyNm
          : raiseCropsVarietyNm // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSeedLand: freezed == isSeedLand
          ? _value.isSeedLand
          : isSeedLand // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DimGrowPatternsImpl implements _DimGrowPatterns {
  _$DimGrowPatternsImpl(
      {this.growPatternsId,
      this.growPatternsName,
      this.orgCode,
      this.orgName,
      this.raiseCrops,
      this.raiseCropsNm,
      this.remark,
      this.createBy,
      this.createName,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.statYear,
      this.isDef,
      this.isOrgPlant,
      this.statusCd,
      this.params,
      this.varietyCdArrayList,
      this.raiseCropsVarietyNm,
      this.isSeedLand});

  factory _$DimGrowPatternsImpl.fromJson(Map<String, dynamic> json) =>
      _$$DimGrowPatternsImplFromJson(json);

  @override
  final int? growPatternsId;
  @override
  final String? growPatternsName;
  @override
  final String? orgCode;
  @override
  final String? orgName;
  @override
  final String? raiseCrops;
  @override
  final String? raiseCropsNm;
  @override
  final String? remark;
  @override
  final int? createBy;
  @override
  final String? createName;
  @override
  final String? createTime;
  @override
  final int? updateBy;
  @override
  final String? updateTime;
  @override
  final String? statYear;
  @override
  final String? isDef;
  @override
  final dynamic isOrgPlant;
  @override
  final int? statusCd;
  @override
  final dynamic params;
  @override
  final dynamic varietyCdArrayList;
  @override
  final dynamic raiseCropsVarietyNm;
  @override
  final dynamic isSeedLand;

  @override
  String toString() {
    return 'DimGrowPatterns(growPatternsId: $growPatternsId, growPatternsName: $growPatternsName, orgCode: $orgCode, orgName: $orgName, raiseCrops: $raiseCrops, raiseCropsNm: $raiseCropsNm, remark: $remark, createBy: $createBy, createName: $createName, createTime: $createTime, updateBy: $updateBy, updateTime: $updateTime, statYear: $statYear, isDef: $isDef, isOrgPlant: $isOrgPlant, statusCd: $statusCd, params: $params, varietyCdArrayList: $varietyCdArrayList, raiseCropsVarietyNm: $raiseCropsVarietyNm, isSeedLand: $isSeedLand)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DimGrowPatternsImpl &&
            (identical(other.growPatternsId, growPatternsId) ||
                other.growPatternsId == growPatternsId) &&
            (identical(other.growPatternsName, growPatternsName) ||
                other.growPatternsName == growPatternsName) &&
            (identical(other.orgCode, orgCode) || other.orgCode == orgCode) &&
            (identical(other.orgName, orgName) || other.orgName == orgName) &&
            (identical(other.raiseCrops, raiseCrops) ||
                other.raiseCrops == raiseCrops) &&
            (identical(other.raiseCropsNm, raiseCropsNm) ||
                other.raiseCropsNm == raiseCropsNm) &&
            (identical(other.remark, remark) || other.remark == remark) &&
            (identical(other.createBy, createBy) ||
                other.createBy == createBy) &&
            (identical(other.createName, createName) ||
                other.createName == createName) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateBy, updateBy) ||
                other.updateBy == updateBy) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.statYear, statYear) ||
                other.statYear == statYear) &&
            (identical(other.isDef, isDef) || other.isDef == isDef) &&
            const DeepCollectionEquality()
                .equals(other.isOrgPlant, isOrgPlant) &&
            (identical(other.statusCd, statusCd) ||
                other.statusCd == statusCd) &&
            const DeepCollectionEquality().equals(other.params, params) &&
            const DeepCollectionEquality()
                .equals(other.varietyCdArrayList, varietyCdArrayList) &&
            const DeepCollectionEquality()
                .equals(other.raiseCropsVarietyNm, raiseCropsVarietyNm) &&
            const DeepCollectionEquality()
                .equals(other.isSeedLand, isSeedLand));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        growPatternsId,
        growPatternsName,
        orgCode,
        orgName,
        raiseCrops,
        raiseCropsNm,
        remark,
        createBy,
        createName,
        createTime,
        updateBy,
        updateTime,
        statYear,
        isDef,
        const DeepCollectionEquality().hash(isOrgPlant),
        statusCd,
        const DeepCollectionEquality().hash(params),
        const DeepCollectionEquality().hash(varietyCdArrayList),
        const DeepCollectionEquality().hash(raiseCropsVarietyNm),
        const DeepCollectionEquality().hash(isSeedLand)
      ]);

  /// Create a copy of DimGrowPatterns
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DimGrowPatternsImplCopyWith<_$DimGrowPatternsImpl> get copyWith =>
      __$$DimGrowPatternsImplCopyWithImpl<_$DimGrowPatternsImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DimGrowPatternsImplToJson(
      this,
    );
  }
}

abstract class _DimGrowPatterns implements DimGrowPatterns {
  factory _DimGrowPatterns(
      {final int? growPatternsId,
      final String? growPatternsName,
      final String? orgCode,
      final String? orgName,
      final String? raiseCrops,
      final String? raiseCropsNm,
      final String? remark,
      final int? createBy,
      final String? createName,
      final String? createTime,
      final int? updateBy,
      final String? updateTime,
      final String? statYear,
      final String? isDef,
      final dynamic isOrgPlant,
      final int? statusCd,
      final dynamic params,
      final dynamic varietyCdArrayList,
      final dynamic raiseCropsVarietyNm,
      final dynamic isSeedLand}) = _$DimGrowPatternsImpl;

  factory _DimGrowPatterns.fromJson(Map<String, dynamic> json) =
      _$DimGrowPatternsImpl.fromJson;

  @override
  int? get growPatternsId;
  @override
  String? get growPatternsName;
  @override
  String? get orgCode;
  @override
  String? get orgName;
  @override
  String? get raiseCrops;
  @override
  String? get raiseCropsNm;
  @override
  String? get remark;
  @override
  int? get createBy;
  @override
  String? get createName;
  @override
  String? get createTime;
  @override
  int? get updateBy;
  @override
  String? get updateTime;
  @override
  String? get statYear;
  @override
  String? get isDef;
  @override
  dynamic get isOrgPlant;
  @override
  int? get statusCd;
  @override
  dynamic get params;
  @override
  dynamic get varietyCdArrayList;
  @override
  dynamic get raiseCropsVarietyNm;
  @override
  dynamic get isSeedLand;

  /// Create a copy of DimGrowPatterns
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DimGrowPatternsImplCopyWith<_$DimGrowPatternsImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
