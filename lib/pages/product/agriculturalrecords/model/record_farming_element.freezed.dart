// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'record_farming_element.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

RecordFarmingElement _$RecordFarmingElementFromJson(Map<String, dynamic> json) {
  return _RecordFarmingElement.fromJson(json);
}

/// @nodoc
mixin _$RecordFarmingElement {
  int? get start => throw _privateConstructorUsedError;
  int? get end => throw _privateConstructorUsedError;
  int? get page => throw _privateConstructorUsedError;
  int? get rows => throw _privateConstructorUsedError;
  String? get sort => throw _privateConstructorUsedError;
  String? get dir => throw _privateConstructorUsedError;
  dynamic get oldValue => throw _privateConstructorUsedError;
  bool? get needPagination => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;
  dynamic get patternsLinkId => throw _privateConstructorUsedError;
  String? get actBeginDate => throw _privateConstructorUsedError;
  String? get actEndDate => throw _privateConstructorUsedError;
  String? get remark => throw _privateConstructorUsedError;
  int? get createBy => throw _privateConstructorUsedError;
  String? get createName => throw _privateConstructorUsedError;
  dynamic get agriRecordsId => throw _privateConstructorUsedError;
  dynamic get statYear => throw _privateConstructorUsedError;
  dynamic get orgCode => throw _privateConstructorUsedError;
  dynamic get orgName => throw _privateConstructorUsedError;
  dynamic get plotNo => throw _privateConstructorUsedError;
  dynamic get plotName => throw _privateConstructorUsedError;
  dynamic get createTime => throw _privateConstructorUsedError;
  int? get updateBy => throw _privateConstructorUsedError;
  dynamic get updateTime => throw _privateConstructorUsedError;
  dynamic get statusCd => throw _privateConstructorUsedError;
  dynamic get longitude => throw _privateConstructorUsedError;
  dynamic get latitude => throw _privateConstructorUsedError;
  String? get agriRecordsDesc => throw _privateConstructorUsedError;
  String? get workPic1 => throw _privateConstructorUsedError;
  String? get workPic2 => throw _privateConstructorUsedError;
  String? get workPic3 => throw _privateConstructorUsedError;
  String? get workVideo1 => throw _privateConstructorUsedError;
  String? get workVideo2 => throw _privateConstructorUsedError;
  String? get workVideo3 => throw _privateConstructorUsedError;
  List<Map<String, dynamic>>? get otherVOList =>
      throw _privateConstructorUsedError;
  List<Map<String, dynamic>>? get asAgriRecordsInputsList =>
      throw _privateConstructorUsedError;
  String? get isGrow => throw _privateConstructorUsedError;
  String? get isFertilize => throw _privateConstructorUsedError;
  String? get isPesticide => throw _privateConstructorUsedError;
  dynamic get params => throw _privateConstructorUsedError;
  dynamic get workStationCode => throw _privateConstructorUsedError;
  dynamic get workStationName => throw _privateConstructorUsedError;
  dynamic get dataSource => throw _privateConstructorUsedError;
  List<Map<String, dynamic>>? get plotList =>
      throw _privateConstructorUsedError;
  dynamic get growthPeriod => throw _privateConstructorUsedError;
  dynamic get standard => throw _privateConstructorUsedError;
  double? get workArea => throw _privateConstructorUsedError;
  dynamic get plotArea => throw _privateConstructorUsedError;
  dynamic get prodProcessCode => throw _privateConstructorUsedError;
  dynamic get prodProcessName => throw _privateConstructorUsedError;
  dynamic get linkCode => throw _privateConstructorUsedError;
  dynamic get linkName => throw _privateConstructorUsedError;
  dynamic get linkOrder => throw _privateConstructorUsedError;
  dynamic get children => throw _privateConstructorUsedError;
  dynamic get isSeedLand => throw _privateConstructorUsedError;
  dynamic get raiseCrops => throw _privateConstructorUsedError;
  dynamic get landType => throw _privateConstructorUsedError;
  dynamic get growPatternsId => throw _privateConstructorUsedError;
  List<Map<String, dynamic>>? get itemList =>
      throw _privateConstructorUsedError;

  /// Serializes this RecordFarmingElement to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of RecordFarmingElement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $RecordFarmingElementCopyWith<RecordFarmingElement> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $RecordFarmingElementCopyWith<$Res> {
  factory $RecordFarmingElementCopyWith(RecordFarmingElement value,
          $Res Function(RecordFarmingElement) then) =
      _$RecordFarmingElementCopyWithImpl<$Res, RecordFarmingElement>;
  @useResult
  $Res call(
      {int? start,
      int? end,
      int? page,
      int? rows,
      String? sort,
      String? dir,
      dynamic oldValue,
      bool? needPagination,
      String? userId,
      dynamic patternsLinkId,
      String? actBeginDate,
      String? actEndDate,
      String? remark,
      int? createBy,
      String? createName,
      dynamic agriRecordsId,
      dynamic statYear,
      dynamic orgCode,
      dynamic orgName,
      dynamic plotNo,
      dynamic plotName,
      dynamic createTime,
      int? updateBy,
      dynamic updateTime,
      dynamic statusCd,
      dynamic longitude,
      dynamic latitude,
      String? agriRecordsDesc,
      String? workPic1,
      String? workPic2,
      String? workPic3,
      String? workVideo1,
      String? workVideo2,
      String? workVideo3,
      List<Map<String, dynamic>>? otherVOList,
      List<Map<String, dynamic>>? asAgriRecordsInputsList,
      String? isGrow,
      String? isFertilize,
      String? isPesticide,
      dynamic params,
      dynamic workStationCode,
      dynamic workStationName,
      dynamic dataSource,
      List<Map<String, dynamic>>? plotList,
      dynamic growthPeriod,
      dynamic standard,
      double? workArea,
      dynamic plotArea,
      dynamic prodProcessCode,
      dynamic prodProcessName,
      dynamic linkCode,
      dynamic linkName,
      dynamic linkOrder,
      dynamic children,
      dynamic isSeedLand,
      dynamic raiseCrops,
      dynamic landType,
      dynamic growPatternsId,
      List<Map<String, dynamic>>? itemList});
}

/// @nodoc
class _$RecordFarmingElementCopyWithImpl<$Res,
        $Val extends RecordFarmingElement>
    implements $RecordFarmingElementCopyWith<$Res> {
  _$RecordFarmingElementCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of RecordFarmingElement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? start = freezed,
    Object? end = freezed,
    Object? page = freezed,
    Object? rows = freezed,
    Object? sort = freezed,
    Object? dir = freezed,
    Object? oldValue = freezed,
    Object? needPagination = freezed,
    Object? userId = freezed,
    Object? patternsLinkId = freezed,
    Object? actBeginDate = freezed,
    Object? actEndDate = freezed,
    Object? remark = freezed,
    Object? createBy = freezed,
    Object? createName = freezed,
    Object? agriRecordsId = freezed,
    Object? statYear = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? plotNo = freezed,
    Object? plotName = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statusCd = freezed,
    Object? longitude = freezed,
    Object? latitude = freezed,
    Object? agriRecordsDesc = freezed,
    Object? workPic1 = freezed,
    Object? workPic2 = freezed,
    Object? workPic3 = freezed,
    Object? workVideo1 = freezed,
    Object? workVideo2 = freezed,
    Object? workVideo3 = freezed,
    Object? otherVOList = freezed,
    Object? asAgriRecordsInputsList = freezed,
    Object? isGrow = freezed,
    Object? isFertilize = freezed,
    Object? isPesticide = freezed,
    Object? params = freezed,
    Object? workStationCode = freezed,
    Object? workStationName = freezed,
    Object? dataSource = freezed,
    Object? plotList = freezed,
    Object? growthPeriod = freezed,
    Object? standard = freezed,
    Object? workArea = freezed,
    Object? plotArea = freezed,
    Object? prodProcessCode = freezed,
    Object? prodProcessName = freezed,
    Object? linkCode = freezed,
    Object? linkName = freezed,
    Object? linkOrder = freezed,
    Object? children = freezed,
    Object? isSeedLand = freezed,
    Object? raiseCrops = freezed,
    Object? landType = freezed,
    Object? growPatternsId = freezed,
    Object? itemList = freezed,
  }) {
    return _then(_value.copyWith(
      start: freezed == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as int?,
      end: freezed == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int?,
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      rows: freezed == rows
          ? _value.rows
          : rows // ignore: cast_nullable_to_non_nullable
              as int?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as String?,
      dir: freezed == dir
          ? _value.dir
          : dir // ignore: cast_nullable_to_non_nullable
              as String?,
      oldValue: freezed == oldValue
          ? _value.oldValue
          : oldValue // ignore: cast_nullable_to_non_nullable
              as dynamic,
      needPagination: freezed == needPagination
          ? _value.needPagination
          : needPagination // ignore: cast_nullable_to_non_nullable
              as bool?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      patternsLinkId: freezed == patternsLinkId
          ? _value.patternsLinkId
          : patternsLinkId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      actBeginDate: freezed == actBeginDate
          ? _value.actBeginDate
          : actBeginDate // ignore: cast_nullable_to_non_nullable
              as String?,
      actEndDate: freezed == actEndDate
          ? _value.actEndDate
          : actEndDate // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as int?,
      createName: freezed == createName
          ? _value.createName
          : createName // ignore: cast_nullable_to_non_nullable
              as String?,
      agriRecordsId: freezed == agriRecordsId
          ? _value.agriRecordsId
          : agriRecordsId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statYear: freezed == statYear
          ? _value.statYear
          : statYear // ignore: cast_nullable_to_non_nullable
              as dynamic,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotNo: freezed == plotNo
          ? _value.plotNo
          : plotNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotName: freezed == plotName
          ? _value.plotName
          : plotName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as dynamic,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as dynamic,
      agriRecordsDesc: freezed == agriRecordsDesc
          ? _value.agriRecordsDesc
          : agriRecordsDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      workPic1: freezed == workPic1
          ? _value.workPic1
          : workPic1 // ignore: cast_nullable_to_non_nullable
              as String?,
      workPic2: freezed == workPic2
          ? _value.workPic2
          : workPic2 // ignore: cast_nullable_to_non_nullable
              as String?,
      workPic3: freezed == workPic3
          ? _value.workPic3
          : workPic3 // ignore: cast_nullable_to_non_nullable
              as String?,
      workVideo1: freezed == workVideo1
          ? _value.workVideo1
          : workVideo1 // ignore: cast_nullable_to_non_nullable
              as String?,
      workVideo2: freezed == workVideo2
          ? _value.workVideo2
          : workVideo2 // ignore: cast_nullable_to_non_nullable
              as String?,
      workVideo3: freezed == workVideo3
          ? _value.workVideo3
          : workVideo3 // ignore: cast_nullable_to_non_nullable
              as String?,
      otherVOList: freezed == otherVOList
          ? _value.otherVOList
          : otherVOList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
      asAgriRecordsInputsList: freezed == asAgriRecordsInputsList
          ? _value.asAgriRecordsInputsList
          : asAgriRecordsInputsList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
      isGrow: freezed == isGrow
          ? _value.isGrow
          : isGrow // ignore: cast_nullable_to_non_nullable
              as String?,
      isFertilize: freezed == isFertilize
          ? _value.isFertilize
          : isFertilize // ignore: cast_nullable_to_non_nullable
              as String?,
      isPesticide: freezed == isPesticide
          ? _value.isPesticide
          : isPesticide // ignore: cast_nullable_to_non_nullable
              as String?,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workStationCode: freezed == workStationCode
          ? _value.workStationCode
          : workStationCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workStationName: freezed == workStationName
          ? _value.workStationName
          : workStationName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dataSource: freezed == dataSource
          ? _value.dataSource
          : dataSource // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotList: freezed == plotList
          ? _value.plotList
          : plotList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
      growthPeriod: freezed == growthPeriod
          ? _value.growthPeriod
          : growthPeriod // ignore: cast_nullable_to_non_nullable
              as dynamic,
      standard: freezed == standard
          ? _value.standard
          : standard // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workArea: freezed == workArea
          ? _value.workArea
          : workArea // ignore: cast_nullable_to_non_nullable
              as double?,
      plotArea: freezed == plotArea
          ? _value.plotArea
          : plotArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      prodProcessCode: freezed == prodProcessCode
          ? _value.prodProcessCode
          : prodProcessCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      prodProcessName: freezed == prodProcessName
          ? _value.prodProcessName
          : prodProcessName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      linkCode: freezed == linkCode
          ? _value.linkCode
          : linkCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      linkName: freezed == linkName
          ? _value.linkName
          : linkName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      linkOrder: freezed == linkOrder
          ? _value.linkOrder
          : linkOrder // ignore: cast_nullable_to_non_nullable
              as dynamic,
      children: freezed == children
          ? _value.children
          : children // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSeedLand: freezed == isSeedLand
          ? _value.isSeedLand
          : isSeedLand // ignore: cast_nullable_to_non_nullable
              as dynamic,
      raiseCrops: freezed == raiseCrops
          ? _value.raiseCrops
          : raiseCrops // ignore: cast_nullable_to_non_nullable
              as dynamic,
      landType: freezed == landType
          ? _value.landType
          : landType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      growPatternsId: freezed == growPatternsId
          ? _value.growPatternsId
          : growPatternsId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      itemList: freezed == itemList
          ? _value.itemList
          : itemList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$RecordFarmingElementImplCopyWith<$Res>
    implements $RecordFarmingElementCopyWith<$Res> {
  factory _$$RecordFarmingElementImplCopyWith(_$RecordFarmingElementImpl value,
          $Res Function(_$RecordFarmingElementImpl) then) =
      __$$RecordFarmingElementImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? start,
      int? end,
      int? page,
      int? rows,
      String? sort,
      String? dir,
      dynamic oldValue,
      bool? needPagination,
      String? userId,
      dynamic patternsLinkId,
      String? actBeginDate,
      String? actEndDate,
      String? remark,
      int? createBy,
      String? createName,
      dynamic agriRecordsId,
      dynamic statYear,
      dynamic orgCode,
      dynamic orgName,
      dynamic plotNo,
      dynamic plotName,
      dynamic createTime,
      int? updateBy,
      dynamic updateTime,
      dynamic statusCd,
      dynamic longitude,
      dynamic latitude,
      String? agriRecordsDesc,
      String? workPic1,
      String? workPic2,
      String? workPic3,
      String? workVideo1,
      String? workVideo2,
      String? workVideo3,
      List<Map<String, dynamic>>? otherVOList,
      List<Map<String, dynamic>>? asAgriRecordsInputsList,
      String? isGrow,
      String? isFertilize,
      String? isPesticide,
      dynamic params,
      dynamic workStationCode,
      dynamic workStationName,
      dynamic dataSource,
      List<Map<String, dynamic>>? plotList,
      dynamic growthPeriod,
      dynamic standard,
      double? workArea,
      dynamic plotArea,
      dynamic prodProcessCode,
      dynamic prodProcessName,
      dynamic linkCode,
      dynamic linkName,
      dynamic linkOrder,
      dynamic children,
      dynamic isSeedLand,
      dynamic raiseCrops,
      dynamic landType,
      dynamic growPatternsId,
      List<Map<String, dynamic>>? itemList});
}

/// @nodoc
class __$$RecordFarmingElementImplCopyWithImpl<$Res>
    extends _$RecordFarmingElementCopyWithImpl<$Res, _$RecordFarmingElementImpl>
    implements _$$RecordFarmingElementImplCopyWith<$Res> {
  __$$RecordFarmingElementImplCopyWithImpl(_$RecordFarmingElementImpl _value,
      $Res Function(_$RecordFarmingElementImpl) _then)
      : super(_value, _then);

  /// Create a copy of RecordFarmingElement
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? start = freezed,
    Object? end = freezed,
    Object? page = freezed,
    Object? rows = freezed,
    Object? sort = freezed,
    Object? dir = freezed,
    Object? oldValue = freezed,
    Object? needPagination = freezed,
    Object? userId = freezed,
    Object? patternsLinkId = freezed,
    Object? actBeginDate = freezed,
    Object? actEndDate = freezed,
    Object? remark = freezed,
    Object? createBy = freezed,
    Object? createName = freezed,
    Object? agriRecordsId = freezed,
    Object? statYear = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? plotNo = freezed,
    Object? plotName = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? statusCd = freezed,
    Object? longitude = freezed,
    Object? latitude = freezed,
    Object? agriRecordsDesc = freezed,
    Object? workPic1 = freezed,
    Object? workPic2 = freezed,
    Object? workPic3 = freezed,
    Object? workVideo1 = freezed,
    Object? workVideo2 = freezed,
    Object? workVideo3 = freezed,
    Object? otherVOList = freezed,
    Object? asAgriRecordsInputsList = freezed,
    Object? isGrow = freezed,
    Object? isFertilize = freezed,
    Object? isPesticide = freezed,
    Object? params = freezed,
    Object? workStationCode = freezed,
    Object? workStationName = freezed,
    Object? dataSource = freezed,
    Object? plotList = freezed,
    Object? growthPeriod = freezed,
    Object? standard = freezed,
    Object? workArea = freezed,
    Object? plotArea = freezed,
    Object? prodProcessCode = freezed,
    Object? prodProcessName = freezed,
    Object? linkCode = freezed,
    Object? linkName = freezed,
    Object? linkOrder = freezed,
    Object? children = freezed,
    Object? isSeedLand = freezed,
    Object? raiseCrops = freezed,
    Object? landType = freezed,
    Object? growPatternsId = freezed,
    Object? itemList = freezed,
  }) {
    return _then(_$RecordFarmingElementImpl(
      start: freezed == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as int?,
      end: freezed == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int?,
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      rows: freezed == rows
          ? _value.rows
          : rows // ignore: cast_nullable_to_non_nullable
              as int?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as String?,
      dir: freezed == dir
          ? _value.dir
          : dir // ignore: cast_nullable_to_non_nullable
              as String?,
      oldValue: freezed == oldValue
          ? _value.oldValue
          : oldValue // ignore: cast_nullable_to_non_nullable
              as dynamic,
      needPagination: freezed == needPagination
          ? _value.needPagination
          : needPagination // ignore: cast_nullable_to_non_nullable
              as bool?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      patternsLinkId: freezed == patternsLinkId
          ? _value.patternsLinkId
          : patternsLinkId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      actBeginDate: freezed == actBeginDate
          ? _value.actBeginDate
          : actBeginDate // ignore: cast_nullable_to_non_nullable
              as String?,
      actEndDate: freezed == actEndDate
          ? _value.actEndDate
          : actEndDate // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as int?,
      createName: freezed == createName
          ? _value.createName
          : createName // ignore: cast_nullable_to_non_nullable
              as String?,
      agriRecordsId: freezed == agriRecordsId
          ? _value.agriRecordsId
          : agriRecordsId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statYear: freezed == statYear
          ? _value.statYear
          : statYear // ignore: cast_nullable_to_non_nullable
              as dynamic,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotNo: freezed == plotNo
          ? _value.plotNo
          : plotNo // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotName: freezed == plotName
          ? _value.plotName
          : plotName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as dynamic,
      longitude: freezed == longitude
          ? _value.longitude
          : longitude // ignore: cast_nullable_to_non_nullable
              as dynamic,
      latitude: freezed == latitude
          ? _value.latitude
          : latitude // ignore: cast_nullable_to_non_nullable
              as dynamic,
      agriRecordsDesc: freezed == agriRecordsDesc
          ? _value.agriRecordsDesc
          : agriRecordsDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      workPic1: freezed == workPic1
          ? _value.workPic1
          : workPic1 // ignore: cast_nullable_to_non_nullable
              as String?,
      workPic2: freezed == workPic2
          ? _value.workPic2
          : workPic2 // ignore: cast_nullable_to_non_nullable
              as String?,
      workPic3: freezed == workPic3
          ? _value.workPic3
          : workPic3 // ignore: cast_nullable_to_non_nullable
              as String?,
      workVideo1: freezed == workVideo1
          ? _value.workVideo1
          : workVideo1 // ignore: cast_nullable_to_non_nullable
              as String?,
      workVideo2: freezed == workVideo2
          ? _value.workVideo2
          : workVideo2 // ignore: cast_nullable_to_non_nullable
              as String?,
      workVideo3: freezed == workVideo3
          ? _value.workVideo3
          : workVideo3 // ignore: cast_nullable_to_non_nullable
              as String?,
      otherVOList: freezed == otherVOList
          ? _value._otherVOList
          : otherVOList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
      asAgriRecordsInputsList: freezed == asAgriRecordsInputsList
          ? _value._asAgriRecordsInputsList
          : asAgriRecordsInputsList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
      isGrow: freezed == isGrow
          ? _value.isGrow
          : isGrow // ignore: cast_nullable_to_non_nullable
              as String?,
      isFertilize: freezed == isFertilize
          ? _value.isFertilize
          : isFertilize // ignore: cast_nullable_to_non_nullable
              as String?,
      isPesticide: freezed == isPesticide
          ? _value.isPesticide
          : isPesticide // ignore: cast_nullable_to_non_nullable
              as String?,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workStationCode: freezed == workStationCode
          ? _value.workStationCode
          : workStationCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workStationName: freezed == workStationName
          ? _value.workStationName
          : workStationName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dataSource: freezed == dataSource
          ? _value.dataSource
          : dataSource // ignore: cast_nullable_to_non_nullable
              as dynamic,
      plotList: freezed == plotList
          ? _value._plotList
          : plotList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
      growthPeriod: freezed == growthPeriod
          ? _value.growthPeriod
          : growthPeriod // ignore: cast_nullable_to_non_nullable
              as dynamic,
      standard: freezed == standard
          ? _value.standard
          : standard // ignore: cast_nullable_to_non_nullable
              as dynamic,
      workArea: freezed == workArea
          ? _value.workArea
          : workArea // ignore: cast_nullable_to_non_nullable
              as double?,
      plotArea: freezed == plotArea
          ? _value.plotArea
          : plotArea // ignore: cast_nullable_to_non_nullable
              as dynamic,
      prodProcessCode: freezed == prodProcessCode
          ? _value.prodProcessCode
          : prodProcessCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      prodProcessName: freezed == prodProcessName
          ? _value.prodProcessName
          : prodProcessName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      linkCode: freezed == linkCode
          ? _value.linkCode
          : linkCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      linkName: freezed == linkName
          ? _value.linkName
          : linkName // ignore: cast_nullable_to_non_nullable
              as dynamic,
      linkOrder: freezed == linkOrder
          ? _value.linkOrder
          : linkOrder // ignore: cast_nullable_to_non_nullable
              as dynamic,
      children: freezed == children
          ? _value.children
          : children // ignore: cast_nullable_to_non_nullable
              as dynamic,
      isSeedLand: freezed == isSeedLand
          ? _value.isSeedLand
          : isSeedLand // ignore: cast_nullable_to_non_nullable
              as dynamic,
      raiseCrops: freezed == raiseCrops
          ? _value.raiseCrops
          : raiseCrops // ignore: cast_nullable_to_non_nullable
              as dynamic,
      landType: freezed == landType
          ? _value.landType
          : landType // ignore: cast_nullable_to_non_nullable
              as dynamic,
      growPatternsId: freezed == growPatternsId
          ? _value.growPatternsId
          : growPatternsId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      itemList: freezed == itemList
          ? _value._itemList
          : itemList // ignore: cast_nullable_to_non_nullable
              as List<Map<String, dynamic>>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$RecordFarmingElementImpl implements _RecordFarmingElement {
  _$RecordFarmingElementImpl(
      {this.start,
      this.end,
      this.page,
      this.rows,
      this.sort,
      this.dir,
      this.oldValue,
      this.needPagination,
      this.userId,
      this.patternsLinkId,
      this.actBeginDate,
      this.actEndDate,
      this.remark,
      this.createBy,
      this.createName,
      this.agriRecordsId,
      this.statYear,
      this.orgCode,
      this.orgName,
      this.plotNo,
      this.plotName,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.statusCd,
      this.longitude,
      this.latitude,
      this.agriRecordsDesc,
      this.workPic1,
      this.workPic2,
      this.workPic3,
      this.workVideo1,
      this.workVideo2,
      this.workVideo3,
      final List<Map<String, dynamic>>? otherVOList,
      final List<Map<String, dynamic>>? asAgriRecordsInputsList,
      this.isGrow,
      this.isFertilize,
      this.isPesticide,
      this.params,
      this.workStationCode,
      this.workStationName,
      this.dataSource,
      final List<Map<String, dynamic>>? plotList,
      this.growthPeriod,
      this.standard,
      this.workArea,
      this.plotArea,
      this.prodProcessCode,
      this.prodProcessName,
      this.linkCode,
      this.linkName,
      this.linkOrder,
      this.children,
      this.isSeedLand,
      this.raiseCrops,
      this.landType,
      this.growPatternsId,
      final List<Map<String, dynamic>>? itemList})
      : _otherVOList = otherVOList,
        _asAgriRecordsInputsList = asAgriRecordsInputsList,
        _plotList = plotList,
        _itemList = itemList;

  factory _$RecordFarmingElementImpl.fromJson(Map<String, dynamic> json) =>
      _$$RecordFarmingElementImplFromJson(json);

  @override
  final int? start;
  @override
  final int? end;
  @override
  final int? page;
  @override
  final int? rows;
  @override
  final String? sort;
  @override
  final String? dir;
  @override
  final dynamic oldValue;
  @override
  final bool? needPagination;
  @override
  final String? userId;
  @override
  final dynamic patternsLinkId;
  @override
  final String? actBeginDate;
  @override
  final String? actEndDate;
  @override
  final String? remark;
  @override
  final int? createBy;
  @override
  final String? createName;
  @override
  final dynamic agriRecordsId;
  @override
  final dynamic statYear;
  @override
  final dynamic orgCode;
  @override
  final dynamic orgName;
  @override
  final dynamic plotNo;
  @override
  final dynamic plotName;
  @override
  final dynamic createTime;
  @override
  final int? updateBy;
  @override
  final dynamic updateTime;
  @override
  final dynamic statusCd;
  @override
  final dynamic longitude;
  @override
  final dynamic latitude;
  @override
  final String? agriRecordsDesc;
  @override
  final String? workPic1;
  @override
  final String? workPic2;
  @override
  final String? workPic3;
  @override
  final String? workVideo1;
  @override
  final String? workVideo2;
  @override
  final String? workVideo3;
  final List<Map<String, dynamic>>? _otherVOList;
  @override
  List<Map<String, dynamic>>? get otherVOList {
    final value = _otherVOList;
    if (value == null) return null;
    if (_otherVOList is EqualUnmodifiableListView) return _otherVOList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<Map<String, dynamic>>? _asAgriRecordsInputsList;
  @override
  List<Map<String, dynamic>>? get asAgriRecordsInputsList {
    final value = _asAgriRecordsInputsList;
    if (value == null) return null;
    if (_asAgriRecordsInputsList is EqualUnmodifiableListView)
      return _asAgriRecordsInputsList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? isGrow;
  @override
  final String? isFertilize;
  @override
  final String? isPesticide;
  @override
  final dynamic params;
  @override
  final dynamic workStationCode;
  @override
  final dynamic workStationName;
  @override
  final dynamic dataSource;
  final List<Map<String, dynamic>>? _plotList;
  @override
  List<Map<String, dynamic>>? get plotList {
    final value = _plotList;
    if (value == null) return null;
    if (_plotList is EqualUnmodifiableListView) return _plotList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final dynamic growthPeriod;
  @override
  final dynamic standard;
  @override
  final double? workArea;
  @override
  final dynamic plotArea;
  @override
  final dynamic prodProcessCode;
  @override
  final dynamic prodProcessName;
  @override
  final dynamic linkCode;
  @override
  final dynamic linkName;
  @override
  final dynamic linkOrder;
  @override
  final dynamic children;
  @override
  final dynamic isSeedLand;
  @override
  final dynamic raiseCrops;
  @override
  final dynamic landType;
  @override
  final dynamic growPatternsId;
  final List<Map<String, dynamic>>? _itemList;
  @override
  List<Map<String, dynamic>>? get itemList {
    final value = _itemList;
    if (value == null) return null;
    if (_itemList is EqualUnmodifiableListView) return _itemList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'RecordFarmingElement(start: $start, end: $end, page: $page, rows: $rows, sort: $sort, dir: $dir, oldValue: $oldValue, needPagination: $needPagination, userId: $userId, patternsLinkId: $patternsLinkId, actBeginDate: $actBeginDate, actEndDate: $actEndDate, remark: $remark, createBy: $createBy, createName: $createName, agriRecordsId: $agriRecordsId, statYear: $statYear, orgCode: $orgCode, orgName: $orgName, plotNo: $plotNo, plotName: $plotName, createTime: $createTime, updateBy: $updateBy, updateTime: $updateTime, statusCd: $statusCd, longitude: $longitude, latitude: $latitude, agriRecordsDesc: $agriRecordsDesc, workPic1: $workPic1, workPic2: $workPic2, workPic3: $workPic3, workVideo1: $workVideo1, workVideo2: $workVideo2, workVideo3: $workVideo3, otherVOList: $otherVOList, asAgriRecordsInputsList: $asAgriRecordsInputsList, isGrow: $isGrow, isFertilize: $isFertilize, isPesticide: $isPesticide, params: $params, workStationCode: $workStationCode, workStationName: $workStationName, dataSource: $dataSource, plotList: $plotList, growthPeriod: $growthPeriod, standard: $standard, workArea: $workArea, plotArea: $plotArea, prodProcessCode: $prodProcessCode, prodProcessName: $prodProcessName, linkCode: $linkCode, linkName: $linkName, linkOrder: $linkOrder, children: $children, isSeedLand: $isSeedLand, raiseCrops: $raiseCrops, landType: $landType, growPatternsId: $growPatternsId, itemList: $itemList)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$RecordFarmingElementImpl &&
            (identical(other.start, start) || other.start == start) &&
            (identical(other.end, end) || other.end == end) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.rows, rows) || other.rows == rows) &&
            (identical(other.sort, sort) || other.sort == sort) &&
            (identical(other.dir, dir) || other.dir == dir) &&
            const DeepCollectionEquality().equals(other.oldValue, oldValue) &&
            (identical(other.needPagination, needPagination) ||
                other.needPagination == needPagination) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            const DeepCollectionEquality()
                .equals(other.patternsLinkId, patternsLinkId) &&
            (identical(other.actBeginDate, actBeginDate) ||
                other.actBeginDate == actBeginDate) &&
            (identical(other.actEndDate, actEndDate) ||
                other.actEndDate == actEndDate) &&
            (identical(other.remark, remark) || other.remark == remark) &&
            (identical(other.createBy, createBy) ||
                other.createBy == createBy) &&
            (identical(other.createName, createName) ||
                other.createName == createName) &&
            const DeepCollectionEquality()
                .equals(other.agriRecordsId, agriRecordsId) &&
            const DeepCollectionEquality().equals(other.statYear, statYear) &&
            const DeepCollectionEquality().equals(other.orgCode, orgCode) &&
            const DeepCollectionEquality().equals(other.orgName, orgName) &&
            const DeepCollectionEquality().equals(other.plotNo, plotNo) &&
            const DeepCollectionEquality().equals(other.plotName, plotName) &&
            const DeepCollectionEquality()
                .equals(other.createTime, createTime) &&
            (identical(other.updateBy, updateBy) ||
                other.updateBy == updateBy) &&
            const DeepCollectionEquality()
                .equals(other.updateTime, updateTime) &&
            const DeepCollectionEquality().equals(other.statusCd, statusCd) &&
            const DeepCollectionEquality().equals(other.longitude, longitude) &&
            const DeepCollectionEquality().equals(other.latitude, latitude) &&
            (identical(other.agriRecordsDesc, agriRecordsDesc) ||
                other.agriRecordsDesc == agriRecordsDesc) &&
            (identical(other.workPic1, workPic1) ||
                other.workPic1 == workPic1) &&
            (identical(other.workPic2, workPic2) ||
                other.workPic2 == workPic2) &&
            (identical(other.workPic3, workPic3) ||
                other.workPic3 == workPic3) &&
            (identical(other.workVideo1, workVideo1) ||
                other.workVideo1 == workVideo1) &&
            (identical(other.workVideo2, workVideo2) ||
                other.workVideo2 == workVideo2) &&
            (identical(other.workVideo3, workVideo3) ||
                other.workVideo3 == workVideo3) &&
            const DeepCollectionEquality()
                .equals(other._otherVOList, _otherVOList) &&
            const DeepCollectionEquality().equals(
                other._asAgriRecordsInputsList, _asAgriRecordsInputsList) &&
            (identical(other.isGrow, isGrow) || other.isGrow == isGrow) &&
            (identical(other.isFertilize, isFertilize) ||
                other.isFertilize == isFertilize) &&
            (identical(other.isPesticide, isPesticide) ||
                other.isPesticide == isPesticide) &&
            const DeepCollectionEquality().equals(other.params, params) &&
            const DeepCollectionEquality()
                .equals(other.workStationCode, workStationCode) &&
            const DeepCollectionEquality()
                .equals(other.workStationName, workStationName) &&
            const DeepCollectionEquality()
                .equals(other.dataSource, dataSource) &&
            const DeepCollectionEquality().equals(other._plotList, _plotList) &&
            const DeepCollectionEquality()
                .equals(other.growthPeriod, growthPeriod) &&
            const DeepCollectionEquality().equals(other.standard, standard) &&
            (identical(other.workArea, workArea) ||
                other.workArea == workArea) &&
            const DeepCollectionEquality().equals(other.plotArea, plotArea) &&
            const DeepCollectionEquality()
                .equals(other.prodProcessCode, prodProcessCode) &&
            const DeepCollectionEquality()
                .equals(other.prodProcessName, prodProcessName) &&
            const DeepCollectionEquality().equals(other.linkCode, linkCode) &&
            const DeepCollectionEquality().equals(other.linkName, linkName) &&
            const DeepCollectionEquality().equals(other.linkOrder, linkOrder) &&
            const DeepCollectionEquality().equals(other.children, children) &&
            const DeepCollectionEquality()
                .equals(other.isSeedLand, isSeedLand) &&
            const DeepCollectionEquality()
                .equals(other.raiseCrops, raiseCrops) &&
            const DeepCollectionEquality().equals(other.landType, landType) &&
            const DeepCollectionEquality()
                .equals(other.growPatternsId, growPatternsId) &&
            const DeepCollectionEquality().equals(other._itemList, _itemList));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        start,
        end,
        page,
        rows,
        sort,
        dir,
        const DeepCollectionEquality().hash(oldValue),
        needPagination,
        userId,
        const DeepCollectionEquality().hash(patternsLinkId),
        actBeginDate,
        actEndDate,
        remark,
        createBy,
        createName,
        const DeepCollectionEquality().hash(agriRecordsId),
        const DeepCollectionEquality().hash(statYear),
        const DeepCollectionEquality().hash(orgCode),
        const DeepCollectionEquality().hash(orgName),
        const DeepCollectionEquality().hash(plotNo),
        const DeepCollectionEquality().hash(plotName),
        const DeepCollectionEquality().hash(createTime),
        updateBy,
        const DeepCollectionEquality().hash(updateTime),
        const DeepCollectionEquality().hash(statusCd),
        const DeepCollectionEquality().hash(longitude),
        const DeepCollectionEquality().hash(latitude),
        agriRecordsDesc,
        workPic1,
        workPic2,
        workPic3,
        workVideo1,
        workVideo2,
        workVideo3,
        const DeepCollectionEquality().hash(_otherVOList),
        const DeepCollectionEquality().hash(_asAgriRecordsInputsList),
        isGrow,
        isFertilize,
        isPesticide,
        const DeepCollectionEquality().hash(params),
        const DeepCollectionEquality().hash(workStationCode),
        const DeepCollectionEquality().hash(workStationName),
        const DeepCollectionEquality().hash(dataSource),
        const DeepCollectionEquality().hash(_plotList),
        const DeepCollectionEquality().hash(growthPeriod),
        const DeepCollectionEquality().hash(standard),
        workArea,
        const DeepCollectionEquality().hash(plotArea),
        const DeepCollectionEquality().hash(prodProcessCode),
        const DeepCollectionEquality().hash(prodProcessName),
        const DeepCollectionEquality().hash(linkCode),
        const DeepCollectionEquality().hash(linkName),
        const DeepCollectionEquality().hash(linkOrder),
        const DeepCollectionEquality().hash(children),
        const DeepCollectionEquality().hash(isSeedLand),
        const DeepCollectionEquality().hash(raiseCrops),
        const DeepCollectionEquality().hash(landType),
        const DeepCollectionEquality().hash(growPatternsId),
        const DeepCollectionEquality().hash(_itemList)
      ]);

  /// Create a copy of RecordFarmingElement
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$RecordFarmingElementImplCopyWith<_$RecordFarmingElementImpl>
      get copyWith =>
          __$$RecordFarmingElementImplCopyWithImpl<_$RecordFarmingElementImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$RecordFarmingElementImplToJson(
      this,
    );
  }
}

abstract class _RecordFarmingElement implements RecordFarmingElement {
  factory _RecordFarmingElement(
      {final int? start,
      final int? end,
      final int? page,
      final int? rows,
      final String? sort,
      final String? dir,
      final dynamic oldValue,
      final bool? needPagination,
      final String? userId,
      final dynamic patternsLinkId,
      final String? actBeginDate,
      final String? actEndDate,
      final String? remark,
      final int? createBy,
      final String? createName,
      final dynamic agriRecordsId,
      final dynamic statYear,
      final dynamic orgCode,
      final dynamic orgName,
      final dynamic plotNo,
      final dynamic plotName,
      final dynamic createTime,
      final int? updateBy,
      final dynamic updateTime,
      final dynamic statusCd,
      final dynamic longitude,
      final dynamic latitude,
      final String? agriRecordsDesc,
      final String? workPic1,
      final String? workPic2,
      final String? workPic3,
      final String? workVideo1,
      final String? workVideo2,
      final String? workVideo3,
      final List<Map<String, dynamic>>? otherVOList,
      final List<Map<String, dynamic>>? asAgriRecordsInputsList,
      final String? isGrow,
      final String? isFertilize,
      final String? isPesticide,
      final dynamic params,
      final dynamic workStationCode,
      final dynamic workStationName,
      final dynamic dataSource,
      final List<Map<String, dynamic>>? plotList,
      final dynamic growthPeriod,
      final dynamic standard,
      final double? workArea,
      final dynamic plotArea,
      final dynamic prodProcessCode,
      final dynamic prodProcessName,
      final dynamic linkCode,
      final dynamic linkName,
      final dynamic linkOrder,
      final dynamic children,
      final dynamic isSeedLand,
      final dynamic raiseCrops,
      final dynamic landType,
      final dynamic growPatternsId,
      final List<Map<String, dynamic>>? itemList}) = _$RecordFarmingElementImpl;

  factory _RecordFarmingElement.fromJson(Map<String, dynamic> json) =
      _$RecordFarmingElementImpl.fromJson;

  @override
  int? get start;
  @override
  int? get end;
  @override
  int? get page;
  @override
  int? get rows;
  @override
  String? get sort;
  @override
  String? get dir;
  @override
  dynamic get oldValue;
  @override
  bool? get needPagination;
  @override
  String? get userId;
  @override
  dynamic get patternsLinkId;
  @override
  String? get actBeginDate;
  @override
  String? get actEndDate;
  @override
  String? get remark;
  @override
  int? get createBy;
  @override
  String? get createName;
  @override
  dynamic get agriRecordsId;
  @override
  dynamic get statYear;
  @override
  dynamic get orgCode;
  @override
  dynamic get orgName;
  @override
  dynamic get plotNo;
  @override
  dynamic get plotName;
  @override
  dynamic get createTime;
  @override
  int? get updateBy;
  @override
  dynamic get updateTime;
  @override
  dynamic get statusCd;
  @override
  dynamic get longitude;
  @override
  dynamic get latitude;
  @override
  String? get agriRecordsDesc;
  @override
  String? get workPic1;
  @override
  String? get workPic2;
  @override
  String? get workPic3;
  @override
  String? get workVideo1;
  @override
  String? get workVideo2;
  @override
  String? get workVideo3;
  @override
  List<Map<String, dynamic>>? get otherVOList;
  @override
  List<Map<String, dynamic>>? get asAgriRecordsInputsList;
  @override
  String? get isGrow;
  @override
  String? get isFertilize;
  @override
  String? get isPesticide;
  @override
  dynamic get params;
  @override
  dynamic get workStationCode;
  @override
  dynamic get workStationName;
  @override
  dynamic get dataSource;
  @override
  List<Map<String, dynamic>>? get plotList;
  @override
  dynamic get growthPeriod;
  @override
  dynamic get standard;
  @override
  double? get workArea;
  @override
  dynamic get plotArea;
  @override
  dynamic get prodProcessCode;
  @override
  dynamic get prodProcessName;
  @override
  dynamic get linkCode;
  @override
  dynamic get linkName;
  @override
  dynamic get linkOrder;
  @override
  dynamic get children;
  @override
  dynamic get isSeedLand;
  @override
  dynamic get raiseCrops;
  @override
  dynamic get landType;
  @override
  dynamic get growPatternsId;
  @override
  List<Map<String, dynamic>>? get itemList;

  /// Create a copy of RecordFarmingElement
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$RecordFarmingElementImplCopyWith<_$RecordFarmingElementImpl>
      get copyWith => throw _privateConstructorUsedError;
}

OtherOV _$OtherOVFromJson(Map<String, dynamic> json) {
  return _OtherOV.fromJson(json);
}

/// @nodoc
mixin _$OtherOV {
  int? get start => throw _privateConstructorUsedError;
  int? get end => throw _privateConstructorUsedError;
  int? get page => throw _privateConstructorUsedError;
  int? get rows => throw _privateConstructorUsedError;
  dynamic get sort => throw _privateConstructorUsedError;
  dynamic get dir => throw _privateConstructorUsedError;
  dynamic get oldValue => throw _privateConstructorUsedError;
  bool? get needPagination => throw _privateConstructorUsedError;
  dynamic get userId => throw _privateConstructorUsedError;
  int? get elementLinkCfgId => throw _privateConstructorUsedError;
  int? get patternsLinkId => throw _privateConstructorUsedError;
  dynamic get elementCfgId => throw _privateConstructorUsedError;
  int? get elementId => throw _privateConstructorUsedError;
  String? get elementName => throw _privateConstructorUsedError;
  String? get isNotNull => throw _privateConstructorUsedError;
  dynamic get remark => throw _privateConstructorUsedError;
  String? get statYear => throw _privateConstructorUsedError;
  String? get orgCode => throw _privateConstructorUsedError;
  String? get orgName => throw _privateConstructorUsedError;
  String? get cropCode => throw _privateConstructorUsedError;
  String? get prodProcessCode => throw _privateConstructorUsedError;
  String? get elementType => throw _privateConstructorUsedError;
  String? get elementFillMethod => throw _privateConstructorUsedError;
  String? get elementPromptInfo => throw _privateConstructorUsedError;
  String? get elementDefValue => throw _privateConstructorUsedError;
  int? get elementLength => throw _privateConstructorUsedError;
  int? get elementPrecision => throw _privateConstructorUsedError;
  String? get dictKey => throw _privateConstructorUsedError;
  String? get elementUnit => throw _privateConstructorUsedError;
  int? get orders => throw _privateConstructorUsedError;
  int? get agriRecordsDtlId => throw _privateConstructorUsedError;
  String? get elementValue => throw _privateConstructorUsedError;
  dynamic get linkCode => throw _privateConstructorUsedError;
  dynamic get externalDataId => throw _privateConstructorUsedError;
  int? get elementValueId => throw _privateConstructorUsedError;
  String? get isMultiple => throw _privateConstructorUsedError;
  double? get minValue => throw _privateConstructorUsedError;
  double? get maxValue => throw _privateConstructorUsedError;
  dynamic get params => throw _privateConstructorUsedError;

  /// Serializes this OtherOV to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of OtherOV
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $OtherOVCopyWith<OtherOV> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $OtherOVCopyWith<$Res> {
  factory $OtherOVCopyWith(OtherOV value, $Res Function(OtherOV) then) =
      _$OtherOVCopyWithImpl<$Res, OtherOV>;
  @useResult
  $Res call(
      {int? start,
      int? end,
      int? page,
      int? rows,
      dynamic sort,
      dynamic dir,
      dynamic oldValue,
      bool? needPagination,
      dynamic userId,
      int? elementLinkCfgId,
      int? patternsLinkId,
      dynamic elementCfgId,
      int? elementId,
      String? elementName,
      String? isNotNull,
      dynamic remark,
      String? statYear,
      String? orgCode,
      String? orgName,
      String? cropCode,
      String? prodProcessCode,
      String? elementType,
      String? elementFillMethod,
      String? elementPromptInfo,
      String? elementDefValue,
      int? elementLength,
      int? elementPrecision,
      String? dictKey,
      String? elementUnit,
      int? orders,
      int? agriRecordsDtlId,
      String? elementValue,
      dynamic linkCode,
      dynamic externalDataId,
      int? elementValueId,
      String? isMultiple,
      double? minValue,
      double? maxValue,
      dynamic params});
}

/// @nodoc
class _$OtherOVCopyWithImpl<$Res, $Val extends OtherOV>
    implements $OtherOVCopyWith<$Res> {
  _$OtherOVCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of OtherOV
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? start = freezed,
    Object? end = freezed,
    Object? page = freezed,
    Object? rows = freezed,
    Object? sort = freezed,
    Object? dir = freezed,
    Object? oldValue = freezed,
    Object? needPagination = freezed,
    Object? userId = freezed,
    Object? elementLinkCfgId = freezed,
    Object? patternsLinkId = freezed,
    Object? elementCfgId = freezed,
    Object? elementId = freezed,
    Object? elementName = freezed,
    Object? isNotNull = freezed,
    Object? remark = freezed,
    Object? statYear = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? cropCode = freezed,
    Object? prodProcessCode = freezed,
    Object? elementType = freezed,
    Object? elementFillMethod = freezed,
    Object? elementPromptInfo = freezed,
    Object? elementDefValue = freezed,
    Object? elementLength = freezed,
    Object? elementPrecision = freezed,
    Object? dictKey = freezed,
    Object? elementUnit = freezed,
    Object? orders = freezed,
    Object? agriRecordsDtlId = freezed,
    Object? elementValue = freezed,
    Object? linkCode = freezed,
    Object? externalDataId = freezed,
    Object? elementValueId = freezed,
    Object? isMultiple = freezed,
    Object? minValue = freezed,
    Object? maxValue = freezed,
    Object? params = freezed,
  }) {
    return _then(_value.copyWith(
      start: freezed == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as int?,
      end: freezed == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int?,
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      rows: freezed == rows
          ? _value.rows
          : rows // ignore: cast_nullable_to_non_nullable
              as int?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dir: freezed == dir
          ? _value.dir
          : dir // ignore: cast_nullable_to_non_nullable
              as dynamic,
      oldValue: freezed == oldValue
          ? _value.oldValue
          : oldValue // ignore: cast_nullable_to_non_nullable
              as dynamic,
      needPagination: freezed == needPagination
          ? _value.needPagination
          : needPagination // ignore: cast_nullable_to_non_nullable
              as bool?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      elementLinkCfgId: freezed == elementLinkCfgId
          ? _value.elementLinkCfgId
          : elementLinkCfgId // ignore: cast_nullable_to_non_nullable
              as int?,
      patternsLinkId: freezed == patternsLinkId
          ? _value.patternsLinkId
          : patternsLinkId // ignore: cast_nullable_to_non_nullable
              as int?,
      elementCfgId: freezed == elementCfgId
          ? _value.elementCfgId
          : elementCfgId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      elementId: freezed == elementId
          ? _value.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as int?,
      elementName: freezed == elementName
          ? _value.elementName
          : elementName // ignore: cast_nullable_to_non_nullable
              as String?,
      isNotNull: freezed == isNotNull
          ? _value.isNotNull
          : isNotNull // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statYear: freezed == statYear
          ? _value.statYear
          : statYear // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      cropCode: freezed == cropCode
          ? _value.cropCode
          : cropCode // ignore: cast_nullable_to_non_nullable
              as String?,
      prodProcessCode: freezed == prodProcessCode
          ? _value.prodProcessCode
          : prodProcessCode // ignore: cast_nullable_to_non_nullable
              as String?,
      elementType: freezed == elementType
          ? _value.elementType
          : elementType // ignore: cast_nullable_to_non_nullable
              as String?,
      elementFillMethod: freezed == elementFillMethod
          ? _value.elementFillMethod
          : elementFillMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      elementPromptInfo: freezed == elementPromptInfo
          ? _value.elementPromptInfo
          : elementPromptInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      elementDefValue: freezed == elementDefValue
          ? _value.elementDefValue
          : elementDefValue // ignore: cast_nullable_to_non_nullable
              as String?,
      elementLength: freezed == elementLength
          ? _value.elementLength
          : elementLength // ignore: cast_nullable_to_non_nullable
              as int?,
      elementPrecision: freezed == elementPrecision
          ? _value.elementPrecision
          : elementPrecision // ignore: cast_nullable_to_non_nullable
              as int?,
      dictKey: freezed == dictKey
          ? _value.dictKey
          : dictKey // ignore: cast_nullable_to_non_nullable
              as String?,
      elementUnit: freezed == elementUnit
          ? _value.elementUnit
          : elementUnit // ignore: cast_nullable_to_non_nullable
              as String?,
      orders: freezed == orders
          ? _value.orders
          : orders // ignore: cast_nullable_to_non_nullable
              as int?,
      agriRecordsDtlId: freezed == agriRecordsDtlId
          ? _value.agriRecordsDtlId
          : agriRecordsDtlId // ignore: cast_nullable_to_non_nullable
              as int?,
      elementValue: freezed == elementValue
          ? _value.elementValue
          : elementValue // ignore: cast_nullable_to_non_nullable
              as String?,
      linkCode: freezed == linkCode
          ? _value.linkCode
          : linkCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      externalDataId: freezed == externalDataId
          ? _value.externalDataId
          : externalDataId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      elementValueId: freezed == elementValueId
          ? _value.elementValueId
          : elementValueId // ignore: cast_nullable_to_non_nullable
              as int?,
      isMultiple: freezed == isMultiple
          ? _value.isMultiple
          : isMultiple // ignore: cast_nullable_to_non_nullable
              as String?,
      minValue: freezed == minValue
          ? _value.minValue
          : minValue // ignore: cast_nullable_to_non_nullable
              as double?,
      maxValue: freezed == maxValue
          ? _value.maxValue
          : maxValue // ignore: cast_nullable_to_non_nullable
              as double?,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$OtherOVImplCopyWith<$Res> implements $OtherOVCopyWith<$Res> {
  factory _$$OtherOVImplCopyWith(
          _$OtherOVImpl value, $Res Function(_$OtherOVImpl) then) =
      __$$OtherOVImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? start,
      int? end,
      int? page,
      int? rows,
      dynamic sort,
      dynamic dir,
      dynamic oldValue,
      bool? needPagination,
      dynamic userId,
      int? elementLinkCfgId,
      int? patternsLinkId,
      dynamic elementCfgId,
      int? elementId,
      String? elementName,
      String? isNotNull,
      dynamic remark,
      String? statYear,
      String? orgCode,
      String? orgName,
      String? cropCode,
      String? prodProcessCode,
      String? elementType,
      String? elementFillMethod,
      String? elementPromptInfo,
      String? elementDefValue,
      int? elementLength,
      int? elementPrecision,
      String? dictKey,
      String? elementUnit,
      int? orders,
      int? agriRecordsDtlId,
      String? elementValue,
      dynamic linkCode,
      dynamic externalDataId,
      int? elementValueId,
      String? isMultiple,
      double? minValue,
      double? maxValue,
      dynamic params});
}

/// @nodoc
class __$$OtherOVImplCopyWithImpl<$Res>
    extends _$OtherOVCopyWithImpl<$Res, _$OtherOVImpl>
    implements _$$OtherOVImplCopyWith<$Res> {
  __$$OtherOVImplCopyWithImpl(
      _$OtherOVImpl _value, $Res Function(_$OtherOVImpl) _then)
      : super(_value, _then);

  /// Create a copy of OtherOV
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? start = freezed,
    Object? end = freezed,
    Object? page = freezed,
    Object? rows = freezed,
    Object? sort = freezed,
    Object? dir = freezed,
    Object? oldValue = freezed,
    Object? needPagination = freezed,
    Object? userId = freezed,
    Object? elementLinkCfgId = freezed,
    Object? patternsLinkId = freezed,
    Object? elementCfgId = freezed,
    Object? elementId = freezed,
    Object? elementName = freezed,
    Object? isNotNull = freezed,
    Object? remark = freezed,
    Object? statYear = freezed,
    Object? orgCode = freezed,
    Object? orgName = freezed,
    Object? cropCode = freezed,
    Object? prodProcessCode = freezed,
    Object? elementType = freezed,
    Object? elementFillMethod = freezed,
    Object? elementPromptInfo = freezed,
    Object? elementDefValue = freezed,
    Object? elementLength = freezed,
    Object? elementPrecision = freezed,
    Object? dictKey = freezed,
    Object? elementUnit = freezed,
    Object? orders = freezed,
    Object? agriRecordsDtlId = freezed,
    Object? elementValue = freezed,
    Object? linkCode = freezed,
    Object? externalDataId = freezed,
    Object? elementValueId = freezed,
    Object? isMultiple = freezed,
    Object? minValue = freezed,
    Object? maxValue = freezed,
    Object? params = freezed,
  }) {
    return _then(_$OtherOVImpl(
      start: freezed == start
          ? _value.start
          : start // ignore: cast_nullable_to_non_nullable
              as int?,
      end: freezed == end
          ? _value.end
          : end // ignore: cast_nullable_to_non_nullable
              as int?,
      page: freezed == page
          ? _value.page
          : page // ignore: cast_nullable_to_non_nullable
              as int?,
      rows: freezed == rows
          ? _value.rows
          : rows // ignore: cast_nullable_to_non_nullable
              as int?,
      sort: freezed == sort
          ? _value.sort
          : sort // ignore: cast_nullable_to_non_nullable
              as dynamic,
      dir: freezed == dir
          ? _value.dir
          : dir // ignore: cast_nullable_to_non_nullable
              as dynamic,
      oldValue: freezed == oldValue
          ? _value.oldValue
          : oldValue // ignore: cast_nullable_to_non_nullable
              as dynamic,
      needPagination: freezed == needPagination
          ? _value.needPagination
          : needPagination // ignore: cast_nullable_to_non_nullable
              as bool?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      elementLinkCfgId: freezed == elementLinkCfgId
          ? _value.elementLinkCfgId
          : elementLinkCfgId // ignore: cast_nullable_to_non_nullable
              as int?,
      patternsLinkId: freezed == patternsLinkId
          ? _value.patternsLinkId
          : patternsLinkId // ignore: cast_nullable_to_non_nullable
              as int?,
      elementCfgId: freezed == elementCfgId
          ? _value.elementCfgId
          : elementCfgId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      elementId: freezed == elementId
          ? _value.elementId
          : elementId // ignore: cast_nullable_to_non_nullable
              as int?,
      elementName: freezed == elementName
          ? _value.elementName
          : elementName // ignore: cast_nullable_to_non_nullable
              as String?,
      isNotNull: freezed == isNotNull
          ? _value.isNotNull
          : isNotNull // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as dynamic,
      statYear: freezed == statYear
          ? _value.statYear
          : statYear // ignore: cast_nullable_to_non_nullable
              as String?,
      orgCode: freezed == orgCode
          ? _value.orgCode
          : orgCode // ignore: cast_nullable_to_non_nullable
              as String?,
      orgName: freezed == orgName
          ? _value.orgName
          : orgName // ignore: cast_nullable_to_non_nullable
              as String?,
      cropCode: freezed == cropCode
          ? _value.cropCode
          : cropCode // ignore: cast_nullable_to_non_nullable
              as String?,
      prodProcessCode: freezed == prodProcessCode
          ? _value.prodProcessCode
          : prodProcessCode // ignore: cast_nullable_to_non_nullable
              as String?,
      elementType: freezed == elementType
          ? _value.elementType
          : elementType // ignore: cast_nullable_to_non_nullable
              as String?,
      elementFillMethod: freezed == elementFillMethod
          ? _value.elementFillMethod
          : elementFillMethod // ignore: cast_nullable_to_non_nullable
              as String?,
      elementPromptInfo: freezed == elementPromptInfo
          ? _value.elementPromptInfo
          : elementPromptInfo // ignore: cast_nullable_to_non_nullable
              as String?,
      elementDefValue: freezed == elementDefValue
          ? _value.elementDefValue
          : elementDefValue // ignore: cast_nullable_to_non_nullable
              as String?,
      elementLength: freezed == elementLength
          ? _value.elementLength
          : elementLength // ignore: cast_nullable_to_non_nullable
              as int?,
      elementPrecision: freezed == elementPrecision
          ? _value.elementPrecision
          : elementPrecision // ignore: cast_nullable_to_non_nullable
              as int?,
      dictKey: freezed == dictKey
          ? _value.dictKey
          : dictKey // ignore: cast_nullable_to_non_nullable
              as String?,
      elementUnit: freezed == elementUnit
          ? _value.elementUnit
          : elementUnit // ignore: cast_nullable_to_non_nullable
              as String?,
      orders: freezed == orders
          ? _value.orders
          : orders // ignore: cast_nullable_to_non_nullable
              as int?,
      agriRecordsDtlId: freezed == agriRecordsDtlId
          ? _value.agriRecordsDtlId
          : agriRecordsDtlId // ignore: cast_nullable_to_non_nullable
              as int?,
      elementValue: freezed == elementValue
          ? _value.elementValue
          : elementValue // ignore: cast_nullable_to_non_nullable
              as String?,
      linkCode: freezed == linkCode
          ? _value.linkCode
          : linkCode // ignore: cast_nullable_to_non_nullable
              as dynamic,
      externalDataId: freezed == externalDataId
          ? _value.externalDataId
          : externalDataId // ignore: cast_nullable_to_non_nullable
              as dynamic,
      elementValueId: freezed == elementValueId
          ? _value.elementValueId
          : elementValueId // ignore: cast_nullable_to_non_nullable
              as int?,
      isMultiple: freezed == isMultiple
          ? _value.isMultiple
          : isMultiple // ignore: cast_nullable_to_non_nullable
              as String?,
      minValue: freezed == minValue
          ? _value.minValue
          : minValue // ignore: cast_nullable_to_non_nullable
              as double?,
      maxValue: freezed == maxValue
          ? _value.maxValue
          : maxValue // ignore: cast_nullable_to_non_nullable
              as double?,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$OtherOVImpl implements _OtherOV {
  _$OtherOVImpl(
      {this.start,
      this.end,
      this.page,
      this.rows,
      this.sort,
      this.dir,
      this.oldValue,
      this.needPagination,
      this.userId,
      this.elementLinkCfgId,
      this.patternsLinkId,
      this.elementCfgId,
      this.elementId,
      this.elementName,
      this.isNotNull,
      this.remark,
      this.statYear,
      this.orgCode,
      this.orgName,
      this.cropCode,
      this.prodProcessCode,
      this.elementType,
      this.elementFillMethod,
      this.elementPromptInfo,
      this.elementDefValue,
      this.elementLength,
      this.elementPrecision,
      this.dictKey,
      this.elementUnit,
      this.orders,
      this.agriRecordsDtlId,
      this.elementValue,
      this.linkCode,
      this.externalDataId,
      this.elementValueId,
      this.isMultiple,
      this.minValue,
      this.maxValue,
      this.params});

  factory _$OtherOVImpl.fromJson(Map<String, dynamic> json) =>
      _$$OtherOVImplFromJson(json);

  @override
  final int? start;
  @override
  final int? end;
  @override
  final int? page;
  @override
  final int? rows;
  @override
  final dynamic sort;
  @override
  final dynamic dir;
  @override
  final dynamic oldValue;
  @override
  final bool? needPagination;
  @override
  final dynamic userId;
  @override
  final int? elementLinkCfgId;
  @override
  final int? patternsLinkId;
  @override
  final dynamic elementCfgId;
  @override
  final int? elementId;
  @override
  final String? elementName;
  @override
  final String? isNotNull;
  @override
  final dynamic remark;
  @override
  final String? statYear;
  @override
  final String? orgCode;
  @override
  final String? orgName;
  @override
  final String? cropCode;
  @override
  final String? prodProcessCode;
  @override
  final String? elementType;
  @override
  final String? elementFillMethod;
  @override
  final String? elementPromptInfo;
  @override
  final String? elementDefValue;
  @override
  final int? elementLength;
  @override
  final int? elementPrecision;
  @override
  final String? dictKey;
  @override
  final String? elementUnit;
  @override
  final int? orders;
  @override
  final int? agriRecordsDtlId;
  @override
  final String? elementValue;
  @override
  final dynamic linkCode;
  @override
  final dynamic externalDataId;
  @override
  final int? elementValueId;
  @override
  final String? isMultiple;
  @override
  final double? minValue;
  @override
  final double? maxValue;
  @override
  final dynamic params;

  @override
  String toString() {
    return 'OtherOV(start: $start, end: $end, page: $page, rows: $rows, sort: $sort, dir: $dir, oldValue: $oldValue, needPagination: $needPagination, userId: $userId, elementLinkCfgId: $elementLinkCfgId, patternsLinkId: $patternsLinkId, elementCfgId: $elementCfgId, elementId: $elementId, elementName: $elementName, isNotNull: $isNotNull, remark: $remark, statYear: $statYear, orgCode: $orgCode, orgName: $orgName, cropCode: $cropCode, prodProcessCode: $prodProcessCode, elementType: $elementType, elementFillMethod: $elementFillMethod, elementPromptInfo: $elementPromptInfo, elementDefValue: $elementDefValue, elementLength: $elementLength, elementPrecision: $elementPrecision, dictKey: $dictKey, elementUnit: $elementUnit, orders: $orders, agriRecordsDtlId: $agriRecordsDtlId, elementValue: $elementValue, linkCode: $linkCode, externalDataId: $externalDataId, elementValueId: $elementValueId, isMultiple: $isMultiple, minValue: $minValue, maxValue: $maxValue, params: $params)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$OtherOVImpl &&
            (identical(other.start, start) || other.start == start) &&
            (identical(other.end, end) || other.end == end) &&
            (identical(other.page, page) || other.page == page) &&
            (identical(other.rows, rows) || other.rows == rows) &&
            const DeepCollectionEquality().equals(other.sort, sort) &&
            const DeepCollectionEquality().equals(other.dir, dir) &&
            const DeepCollectionEquality().equals(other.oldValue, oldValue) &&
            (identical(other.needPagination, needPagination) ||
                other.needPagination == needPagination) &&
            const DeepCollectionEquality().equals(other.userId, userId) &&
            (identical(other.elementLinkCfgId, elementLinkCfgId) ||
                other.elementLinkCfgId == elementLinkCfgId) &&
            (identical(other.patternsLinkId, patternsLinkId) ||
                other.patternsLinkId == patternsLinkId) &&
            const DeepCollectionEquality()
                .equals(other.elementCfgId, elementCfgId) &&
            (identical(other.elementId, elementId) ||
                other.elementId == elementId) &&
            (identical(other.elementName, elementName) ||
                other.elementName == elementName) &&
            (identical(other.isNotNull, isNotNull) ||
                other.isNotNull == isNotNull) &&
            const DeepCollectionEquality().equals(other.remark, remark) &&
            (identical(other.statYear, statYear) ||
                other.statYear == statYear) &&
            (identical(other.orgCode, orgCode) || other.orgCode == orgCode) &&
            (identical(other.orgName, orgName) || other.orgName == orgName) &&
            (identical(other.cropCode, cropCode) ||
                other.cropCode == cropCode) &&
            (identical(other.prodProcessCode, prodProcessCode) ||
                other.prodProcessCode == prodProcessCode) &&
            (identical(other.elementType, elementType) ||
                other.elementType == elementType) &&
            (identical(other.elementFillMethod, elementFillMethod) ||
                other.elementFillMethod == elementFillMethod) &&
            (identical(other.elementPromptInfo, elementPromptInfo) ||
                other.elementPromptInfo == elementPromptInfo) &&
            (identical(other.elementDefValue, elementDefValue) ||
                other.elementDefValue == elementDefValue) &&
            (identical(other.elementLength, elementLength) ||
                other.elementLength == elementLength) &&
            (identical(other.elementPrecision, elementPrecision) ||
                other.elementPrecision == elementPrecision) &&
            (identical(other.dictKey, dictKey) || other.dictKey == dictKey) &&
            (identical(other.elementUnit, elementUnit) ||
                other.elementUnit == elementUnit) &&
            (identical(other.orders, orders) || other.orders == orders) &&
            (identical(other.agriRecordsDtlId, agriRecordsDtlId) ||
                other.agriRecordsDtlId == agriRecordsDtlId) &&
            (identical(other.elementValue, elementValue) ||
                other.elementValue == elementValue) &&
            const DeepCollectionEquality().equals(other.linkCode, linkCode) &&
            const DeepCollectionEquality()
                .equals(other.externalDataId, externalDataId) &&
            (identical(other.elementValueId, elementValueId) ||
                other.elementValueId == elementValueId) &&
            (identical(other.isMultiple, isMultiple) ||
                other.isMultiple == isMultiple) &&
            (identical(other.minValue, minValue) ||
                other.minValue == minValue) &&
            (identical(other.maxValue, maxValue) ||
                other.maxValue == maxValue) &&
            const DeepCollectionEquality().equals(other.params, params));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        start,
        end,
        page,
        rows,
        const DeepCollectionEquality().hash(sort),
        const DeepCollectionEquality().hash(dir),
        const DeepCollectionEquality().hash(oldValue),
        needPagination,
        const DeepCollectionEquality().hash(userId),
        elementLinkCfgId,
        patternsLinkId,
        const DeepCollectionEquality().hash(elementCfgId),
        elementId,
        elementName,
        isNotNull,
        const DeepCollectionEquality().hash(remark),
        statYear,
        orgCode,
        orgName,
        cropCode,
        prodProcessCode,
        elementType,
        elementFillMethod,
        elementPromptInfo,
        elementDefValue,
        elementLength,
        elementPrecision,
        dictKey,
        elementUnit,
        orders,
        agriRecordsDtlId,
        elementValue,
        const DeepCollectionEquality().hash(linkCode),
        const DeepCollectionEquality().hash(externalDataId),
        elementValueId,
        isMultiple,
        minValue,
        maxValue,
        const DeepCollectionEquality().hash(params)
      ]);

  /// Create a copy of OtherOV
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$OtherOVImplCopyWith<_$OtherOVImpl> get copyWith =>
      __$$OtherOVImplCopyWithImpl<_$OtherOVImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$OtherOVImplToJson(
      this,
    );
  }
}

abstract class _OtherOV implements OtherOV {
  factory _OtherOV(
      {final int? start,
      final int? end,
      final int? page,
      final int? rows,
      final dynamic sort,
      final dynamic dir,
      final dynamic oldValue,
      final bool? needPagination,
      final dynamic userId,
      final int? elementLinkCfgId,
      final int? patternsLinkId,
      final dynamic elementCfgId,
      final int? elementId,
      final String? elementName,
      final String? isNotNull,
      final dynamic remark,
      final String? statYear,
      final String? orgCode,
      final String? orgName,
      final String? cropCode,
      final String? prodProcessCode,
      final String? elementType,
      final String? elementFillMethod,
      final String? elementPromptInfo,
      final String? elementDefValue,
      final int? elementLength,
      final int? elementPrecision,
      final String? dictKey,
      final String? elementUnit,
      final int? orders,
      final int? agriRecordsDtlId,
      final String? elementValue,
      final dynamic linkCode,
      final dynamic externalDataId,
      final int? elementValueId,
      final String? isMultiple,
      final double? minValue,
      final double? maxValue,
      final dynamic params}) = _$OtherOVImpl;

  factory _OtherOV.fromJson(Map<String, dynamic> json) = _$OtherOVImpl.fromJson;

  @override
  int? get start;
  @override
  int? get end;
  @override
  int? get page;
  @override
  int? get rows;
  @override
  dynamic get sort;
  @override
  dynamic get dir;
  @override
  dynamic get oldValue;
  @override
  bool? get needPagination;
  @override
  dynamic get userId;
  @override
  int? get elementLinkCfgId;
  @override
  int? get patternsLinkId;
  @override
  dynamic get elementCfgId;
  @override
  int? get elementId;
  @override
  String? get elementName;
  @override
  String? get isNotNull;
  @override
  dynamic get remark;
  @override
  String? get statYear;
  @override
  String? get orgCode;
  @override
  String? get orgName;
  @override
  String? get cropCode;
  @override
  String? get prodProcessCode;
  @override
  String? get elementType;
  @override
  String? get elementFillMethod;
  @override
  String? get elementPromptInfo;
  @override
  String? get elementDefValue;
  @override
  int? get elementLength;
  @override
  int? get elementPrecision;
  @override
  String? get dictKey;
  @override
  String? get elementUnit;
  @override
  int? get orders;
  @override
  int? get agriRecordsDtlId;
  @override
  String? get elementValue;
  @override
  dynamic get linkCode;
  @override
  dynamic get externalDataId;
  @override
  int? get elementValueId;
  @override
  String? get isMultiple;
  @override
  double? get minValue;
  @override
  double? get maxValue;
  @override
  dynamic get params;

  /// Create a copy of OtherOV
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$OtherOVImplCopyWith<_$OtherOVImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

AgriRecordsInput _$AgriRecordsInputFromJson(Map<String, dynamic> json) {
  return _AgriRecordsInput.fromJson(json);
}

/// @nodoc
mixin _$AgriRecordsInput {
  int? get agriRecordsInputsId => throw _privateConstructorUsedError;
  int? get agriRecordsId => throw _privateConstructorUsedError;
  String? get meansProdType => throw _privateConstructorUsedError;
  int? get meansProdId => throw _privateConstructorUsedError;
  String? get meansProdName => throw _privateConstructorUsedError;
  double? get totalDosage => throw _privateConstructorUsedError;
  String? get meansProdUnit => throw _privateConstructorUsedError;
  String? get meansProdUnitName => throw _privateConstructorUsedError;
  String? get instructionsPic1 => throw _privateConstructorUsedError;
  String? get instructionsPic2 => throw _privateConstructorUsedError;
  String? get instructionsPic3 => throw _privateConstructorUsedError;
  String? get goodsPic1 => throw _privateConstructorUsedError;
  String? get goodsPic2 => throw _privateConstructorUsedError;
  String? get goodsPic3 => throw _privateConstructorUsedError;
  String? get actGoodsPic1 => throw _privateConstructorUsedError;
  String? get actGoodsPic2 => throw _privateConstructorUsedError;
  String? get actGoodsPic3 => throw _privateConstructorUsedError;
  String? get orders => throw _privateConstructorUsedError;
  String? get remark => throw _privateConstructorUsedError;
  int? get createBy => throw _privateConstructorUsedError;
  String? get createTime => throw _privateConstructorUsedError;
  int? get updateBy => throw _privateConstructorUsedError;
  String? get updateTime => throw _privateConstructorUsedError;
  double? get minValue => throw _privateConstructorUsedError;
  double? get maxValue => throw _privateConstructorUsedError;
  int? get statusCd => throw _privateConstructorUsedError;
  dynamic get params => throw _privateConstructorUsedError;
  dynamic get content => throw _privateConstructorUsedError;
  dynamic get manufacturer => throw _privateConstructorUsedError;

  /// Serializes this AgriRecordsInput to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of AgriRecordsInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AgriRecordsInputCopyWith<AgriRecordsInput> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AgriRecordsInputCopyWith<$Res> {
  factory $AgriRecordsInputCopyWith(
          AgriRecordsInput value, $Res Function(AgriRecordsInput) then) =
      _$AgriRecordsInputCopyWithImpl<$Res, AgriRecordsInput>;
  @useResult
  $Res call(
      {int? agriRecordsInputsId,
      int? agriRecordsId,
      String? meansProdType,
      int? meansProdId,
      String? meansProdName,
      double? totalDosage,
      String? meansProdUnit,
      String? meansProdUnitName,
      String? instructionsPic1,
      String? instructionsPic2,
      String? instructionsPic3,
      String? goodsPic1,
      String? goodsPic2,
      String? goodsPic3,
      String? actGoodsPic1,
      String? actGoodsPic2,
      String? actGoodsPic3,
      String? orders,
      String? remark,
      int? createBy,
      String? createTime,
      int? updateBy,
      String? updateTime,
      double? minValue,
      double? maxValue,
      int? statusCd,
      dynamic params,
      dynamic content,
      dynamic manufacturer});
}

/// @nodoc
class _$AgriRecordsInputCopyWithImpl<$Res, $Val extends AgriRecordsInput>
    implements $AgriRecordsInputCopyWith<$Res> {
  _$AgriRecordsInputCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AgriRecordsInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? agriRecordsInputsId = freezed,
    Object? agriRecordsId = freezed,
    Object? meansProdType = freezed,
    Object? meansProdId = freezed,
    Object? meansProdName = freezed,
    Object? totalDosage = freezed,
    Object? meansProdUnit = freezed,
    Object? meansProdUnitName = freezed,
    Object? instructionsPic1 = freezed,
    Object? instructionsPic2 = freezed,
    Object? instructionsPic3 = freezed,
    Object? goodsPic1 = freezed,
    Object? goodsPic2 = freezed,
    Object? goodsPic3 = freezed,
    Object? actGoodsPic1 = freezed,
    Object? actGoodsPic2 = freezed,
    Object? actGoodsPic3 = freezed,
    Object? orders = freezed,
    Object? remark = freezed,
    Object? createBy = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? minValue = freezed,
    Object? maxValue = freezed,
    Object? statusCd = freezed,
    Object? params = freezed,
    Object? content = freezed,
    Object? manufacturer = freezed,
  }) {
    return _then(_value.copyWith(
      agriRecordsInputsId: freezed == agriRecordsInputsId
          ? _value.agriRecordsInputsId
          : agriRecordsInputsId // ignore: cast_nullable_to_non_nullable
              as int?,
      agriRecordsId: freezed == agriRecordsId
          ? _value.agriRecordsId
          : agriRecordsId // ignore: cast_nullable_to_non_nullable
              as int?,
      meansProdType: freezed == meansProdType
          ? _value.meansProdType
          : meansProdType // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdId: freezed == meansProdId
          ? _value.meansProdId
          : meansProdId // ignore: cast_nullable_to_non_nullable
              as int?,
      meansProdName: freezed == meansProdName
          ? _value.meansProdName
          : meansProdName // ignore: cast_nullable_to_non_nullable
              as String?,
      totalDosage: freezed == totalDosage
          ? _value.totalDosage
          : totalDosage // ignore: cast_nullable_to_non_nullable
              as double?,
      meansProdUnit: freezed == meansProdUnit
          ? _value.meansProdUnit
          : meansProdUnit // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdUnitName: freezed == meansProdUnitName
          ? _value.meansProdUnitName
          : meansProdUnitName // ignore: cast_nullable_to_non_nullable
              as String?,
      instructionsPic1: freezed == instructionsPic1
          ? _value.instructionsPic1
          : instructionsPic1 // ignore: cast_nullable_to_non_nullable
              as String?,
      instructionsPic2: freezed == instructionsPic2
          ? _value.instructionsPic2
          : instructionsPic2 // ignore: cast_nullable_to_non_nullable
              as String?,
      instructionsPic3: freezed == instructionsPic3
          ? _value.instructionsPic3
          : instructionsPic3 // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsPic1: freezed == goodsPic1
          ? _value.goodsPic1
          : goodsPic1 // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsPic2: freezed == goodsPic2
          ? _value.goodsPic2
          : goodsPic2 // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsPic3: freezed == goodsPic3
          ? _value.goodsPic3
          : goodsPic3 // ignore: cast_nullable_to_non_nullable
              as String?,
      actGoodsPic1: freezed == actGoodsPic1
          ? _value.actGoodsPic1
          : actGoodsPic1 // ignore: cast_nullable_to_non_nullable
              as String?,
      actGoodsPic2: freezed == actGoodsPic2
          ? _value.actGoodsPic2
          : actGoodsPic2 // ignore: cast_nullable_to_non_nullable
              as String?,
      actGoodsPic3: freezed == actGoodsPic3
          ? _value.actGoodsPic3
          : actGoodsPic3 // ignore: cast_nullable_to_non_nullable
              as String?,
      orders: freezed == orders
          ? _value.orders
          : orders // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      minValue: freezed == minValue
          ? _value.minValue
          : minValue // ignore: cast_nullable_to_non_nullable
              as double?,
      maxValue: freezed == maxValue
          ? _value.maxValue
          : maxValue // ignore: cast_nullable_to_non_nullable
              as double?,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as int?,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as dynamic,
      manufacturer: freezed == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AgriRecordsInputImplCopyWith<$Res>
    implements $AgriRecordsInputCopyWith<$Res> {
  factory _$$AgriRecordsInputImplCopyWith(_$AgriRecordsInputImpl value,
          $Res Function(_$AgriRecordsInputImpl) then) =
      __$$AgriRecordsInputImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? agriRecordsInputsId,
      int? agriRecordsId,
      String? meansProdType,
      int? meansProdId,
      String? meansProdName,
      double? totalDosage,
      String? meansProdUnit,
      String? meansProdUnitName,
      String? instructionsPic1,
      String? instructionsPic2,
      String? instructionsPic3,
      String? goodsPic1,
      String? goodsPic2,
      String? goodsPic3,
      String? actGoodsPic1,
      String? actGoodsPic2,
      String? actGoodsPic3,
      String? orders,
      String? remark,
      int? createBy,
      String? createTime,
      int? updateBy,
      String? updateTime,
      double? minValue,
      double? maxValue,
      int? statusCd,
      dynamic params,
      dynamic content,
      dynamic manufacturer});
}

/// @nodoc
class __$$AgriRecordsInputImplCopyWithImpl<$Res>
    extends _$AgriRecordsInputCopyWithImpl<$Res, _$AgriRecordsInputImpl>
    implements _$$AgriRecordsInputImplCopyWith<$Res> {
  __$$AgriRecordsInputImplCopyWithImpl(_$AgriRecordsInputImpl _value,
      $Res Function(_$AgriRecordsInputImpl) _then)
      : super(_value, _then);

  /// Create a copy of AgriRecordsInput
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? agriRecordsInputsId = freezed,
    Object? agriRecordsId = freezed,
    Object? meansProdType = freezed,
    Object? meansProdId = freezed,
    Object? meansProdName = freezed,
    Object? totalDosage = freezed,
    Object? meansProdUnit = freezed,
    Object? meansProdUnitName = freezed,
    Object? instructionsPic1 = freezed,
    Object? instructionsPic2 = freezed,
    Object? instructionsPic3 = freezed,
    Object? goodsPic1 = freezed,
    Object? goodsPic2 = freezed,
    Object? goodsPic3 = freezed,
    Object? actGoodsPic1 = freezed,
    Object? actGoodsPic2 = freezed,
    Object? actGoodsPic3 = freezed,
    Object? orders = freezed,
    Object? remark = freezed,
    Object? createBy = freezed,
    Object? createTime = freezed,
    Object? updateBy = freezed,
    Object? updateTime = freezed,
    Object? minValue = freezed,
    Object? maxValue = freezed,
    Object? statusCd = freezed,
    Object? params = freezed,
    Object? content = freezed,
    Object? manufacturer = freezed,
  }) {
    return _then(_$AgriRecordsInputImpl(
      agriRecordsInputsId: freezed == agriRecordsInputsId
          ? _value.agriRecordsInputsId
          : agriRecordsInputsId // ignore: cast_nullable_to_non_nullable
              as int?,
      agriRecordsId: freezed == agriRecordsId
          ? _value.agriRecordsId
          : agriRecordsId // ignore: cast_nullable_to_non_nullable
              as int?,
      meansProdType: freezed == meansProdType
          ? _value.meansProdType
          : meansProdType // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdId: freezed == meansProdId
          ? _value.meansProdId
          : meansProdId // ignore: cast_nullable_to_non_nullable
              as int?,
      meansProdName: freezed == meansProdName
          ? _value.meansProdName
          : meansProdName // ignore: cast_nullable_to_non_nullable
              as String?,
      totalDosage: freezed == totalDosage
          ? _value.totalDosage
          : totalDosage // ignore: cast_nullable_to_non_nullable
              as double?,
      meansProdUnit: freezed == meansProdUnit
          ? _value.meansProdUnit
          : meansProdUnit // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdUnitName: freezed == meansProdUnitName
          ? _value.meansProdUnitName
          : meansProdUnitName // ignore: cast_nullable_to_non_nullable
              as String?,
      instructionsPic1: freezed == instructionsPic1
          ? _value.instructionsPic1
          : instructionsPic1 // ignore: cast_nullable_to_non_nullable
              as String?,
      instructionsPic2: freezed == instructionsPic2
          ? _value.instructionsPic2
          : instructionsPic2 // ignore: cast_nullable_to_non_nullable
              as String?,
      instructionsPic3: freezed == instructionsPic3
          ? _value.instructionsPic3
          : instructionsPic3 // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsPic1: freezed == goodsPic1
          ? _value.goodsPic1
          : goodsPic1 // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsPic2: freezed == goodsPic2
          ? _value.goodsPic2
          : goodsPic2 // ignore: cast_nullable_to_non_nullable
              as String?,
      goodsPic3: freezed == goodsPic3
          ? _value.goodsPic3
          : goodsPic3 // ignore: cast_nullable_to_non_nullable
              as String?,
      actGoodsPic1: freezed == actGoodsPic1
          ? _value.actGoodsPic1
          : actGoodsPic1 // ignore: cast_nullable_to_non_nullable
              as String?,
      actGoodsPic2: freezed == actGoodsPic2
          ? _value.actGoodsPic2
          : actGoodsPic2 // ignore: cast_nullable_to_non_nullable
              as String?,
      actGoodsPic3: freezed == actGoodsPic3
          ? _value.actGoodsPic3
          : actGoodsPic3 // ignore: cast_nullable_to_non_nullable
              as String?,
      orders: freezed == orders
          ? _value.orders
          : orders // ignore: cast_nullable_to_non_nullable
              as String?,
      remark: freezed == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String?,
      createBy: freezed == createBy
          ? _value.createBy
          : createBy // ignore: cast_nullable_to_non_nullable
              as int?,
      createTime: freezed == createTime
          ? _value.createTime
          : createTime // ignore: cast_nullable_to_non_nullable
              as String?,
      updateBy: freezed == updateBy
          ? _value.updateBy
          : updateBy // ignore: cast_nullable_to_non_nullable
              as int?,
      updateTime: freezed == updateTime
          ? _value.updateTime
          : updateTime // ignore: cast_nullable_to_non_nullable
              as String?,
      minValue: freezed == minValue
          ? _value.minValue
          : minValue // ignore: cast_nullable_to_non_nullable
              as double?,
      maxValue: freezed == maxValue
          ? _value.maxValue
          : maxValue // ignore: cast_nullable_to_non_nullable
              as double?,
      statusCd: freezed == statusCd
          ? _value.statusCd
          : statusCd // ignore: cast_nullable_to_non_nullable
              as int?,
      params: freezed == params
          ? _value.params
          : params // ignore: cast_nullable_to_non_nullable
              as dynamic,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as dynamic,
      manufacturer: freezed == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as dynamic,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AgriRecordsInputImpl implements _AgriRecordsInput {
  _$AgriRecordsInputImpl(
      {this.agriRecordsInputsId,
      this.agriRecordsId,
      this.meansProdType,
      this.meansProdId,
      this.meansProdName,
      this.totalDosage,
      this.meansProdUnit,
      this.meansProdUnitName,
      this.instructionsPic1,
      this.instructionsPic2,
      this.instructionsPic3,
      this.goodsPic1,
      this.goodsPic2,
      this.goodsPic3,
      this.actGoodsPic1,
      this.actGoodsPic2,
      this.actGoodsPic3,
      this.orders,
      this.remark,
      this.createBy,
      this.createTime,
      this.updateBy,
      this.updateTime,
      this.minValue,
      this.maxValue,
      this.statusCd,
      this.params,
      this.content,
      this.manufacturer});

  factory _$AgriRecordsInputImpl.fromJson(Map<String, dynamic> json) =>
      _$$AgriRecordsInputImplFromJson(json);

  @override
  final int? agriRecordsInputsId;
  @override
  final int? agriRecordsId;
  @override
  final String? meansProdType;
  @override
  final int? meansProdId;
  @override
  final String? meansProdName;
  @override
  final double? totalDosage;
  @override
  final String? meansProdUnit;
  @override
  final String? meansProdUnitName;
  @override
  final String? instructionsPic1;
  @override
  final String? instructionsPic2;
  @override
  final String? instructionsPic3;
  @override
  final String? goodsPic1;
  @override
  final String? goodsPic2;
  @override
  final String? goodsPic3;
  @override
  final String? actGoodsPic1;
  @override
  final String? actGoodsPic2;
  @override
  final String? actGoodsPic3;
  @override
  final String? orders;
  @override
  final String? remark;
  @override
  final int? createBy;
  @override
  final String? createTime;
  @override
  final int? updateBy;
  @override
  final String? updateTime;
  @override
  final double? minValue;
  @override
  final double? maxValue;
  @override
  final int? statusCd;
  @override
  final dynamic params;
  @override
  final dynamic content;
  @override
  final dynamic manufacturer;

  @override
  String toString() {
    return 'AgriRecordsInput(agriRecordsInputsId: $agriRecordsInputsId, agriRecordsId: $agriRecordsId, meansProdType: $meansProdType, meansProdId: $meansProdId, meansProdName: $meansProdName, totalDosage: $totalDosage, meansProdUnit: $meansProdUnit, meansProdUnitName: $meansProdUnitName, instructionsPic1: $instructionsPic1, instructionsPic2: $instructionsPic2, instructionsPic3: $instructionsPic3, goodsPic1: $goodsPic1, goodsPic2: $goodsPic2, goodsPic3: $goodsPic3, actGoodsPic1: $actGoodsPic1, actGoodsPic2: $actGoodsPic2, actGoodsPic3: $actGoodsPic3, orders: $orders, remark: $remark, createBy: $createBy, createTime: $createTime, updateBy: $updateBy, updateTime: $updateTime, minValue: $minValue, maxValue: $maxValue, statusCd: $statusCd, params: $params, content: $content, manufacturer: $manufacturer)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AgriRecordsInputImpl &&
            (identical(other.agriRecordsInputsId, agriRecordsInputsId) ||
                other.agriRecordsInputsId == agriRecordsInputsId) &&
            (identical(other.agriRecordsId, agriRecordsId) ||
                other.agriRecordsId == agriRecordsId) &&
            (identical(other.meansProdType, meansProdType) ||
                other.meansProdType == meansProdType) &&
            (identical(other.meansProdId, meansProdId) ||
                other.meansProdId == meansProdId) &&
            (identical(other.meansProdName, meansProdName) ||
                other.meansProdName == meansProdName) &&
            (identical(other.totalDosage, totalDosage) ||
                other.totalDosage == totalDosage) &&
            (identical(other.meansProdUnit, meansProdUnit) ||
                other.meansProdUnit == meansProdUnit) &&
            (identical(other.meansProdUnitName, meansProdUnitName) ||
                other.meansProdUnitName == meansProdUnitName) &&
            (identical(other.instructionsPic1, instructionsPic1) ||
                other.instructionsPic1 == instructionsPic1) &&
            (identical(other.instructionsPic2, instructionsPic2) ||
                other.instructionsPic2 == instructionsPic2) &&
            (identical(other.instructionsPic3, instructionsPic3) ||
                other.instructionsPic3 == instructionsPic3) &&
            (identical(other.goodsPic1, goodsPic1) ||
                other.goodsPic1 == goodsPic1) &&
            (identical(other.goodsPic2, goodsPic2) ||
                other.goodsPic2 == goodsPic2) &&
            (identical(other.goodsPic3, goodsPic3) ||
                other.goodsPic3 == goodsPic3) &&
            (identical(other.actGoodsPic1, actGoodsPic1) ||
                other.actGoodsPic1 == actGoodsPic1) &&
            (identical(other.actGoodsPic2, actGoodsPic2) ||
                other.actGoodsPic2 == actGoodsPic2) &&
            (identical(other.actGoodsPic3, actGoodsPic3) ||
                other.actGoodsPic3 == actGoodsPic3) &&
            (identical(other.orders, orders) || other.orders == orders) &&
            (identical(other.remark, remark) || other.remark == remark) &&
            (identical(other.createBy, createBy) ||
                other.createBy == createBy) &&
            (identical(other.createTime, createTime) ||
                other.createTime == createTime) &&
            (identical(other.updateBy, updateBy) ||
                other.updateBy == updateBy) &&
            (identical(other.updateTime, updateTime) ||
                other.updateTime == updateTime) &&
            (identical(other.minValue, minValue) ||
                other.minValue == minValue) &&
            (identical(other.maxValue, maxValue) ||
                other.maxValue == maxValue) &&
            (identical(other.statusCd, statusCd) ||
                other.statusCd == statusCd) &&
            const DeepCollectionEquality().equals(other.params, params) &&
            const DeepCollectionEquality().equals(other.content, content) &&
            const DeepCollectionEquality()
                .equals(other.manufacturer, manufacturer));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hashAll([
        runtimeType,
        agriRecordsInputsId,
        agriRecordsId,
        meansProdType,
        meansProdId,
        meansProdName,
        totalDosage,
        meansProdUnit,
        meansProdUnitName,
        instructionsPic1,
        instructionsPic2,
        instructionsPic3,
        goodsPic1,
        goodsPic2,
        goodsPic3,
        actGoodsPic1,
        actGoodsPic2,
        actGoodsPic3,
        orders,
        remark,
        createBy,
        createTime,
        updateBy,
        updateTime,
        minValue,
        maxValue,
        statusCd,
        const DeepCollectionEquality().hash(params),
        const DeepCollectionEquality().hash(content),
        const DeepCollectionEquality().hash(manufacturer)
      ]);

  /// Create a copy of AgriRecordsInput
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AgriRecordsInputImplCopyWith<_$AgriRecordsInputImpl> get copyWith =>
      __$$AgriRecordsInputImplCopyWithImpl<_$AgriRecordsInputImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AgriRecordsInputImplToJson(
      this,
    );
  }
}

abstract class _AgriRecordsInput implements AgriRecordsInput {
  factory _AgriRecordsInput(
      {final int? agriRecordsInputsId,
      final int? agriRecordsId,
      final String? meansProdType,
      final int? meansProdId,
      final String? meansProdName,
      final double? totalDosage,
      final String? meansProdUnit,
      final String? meansProdUnitName,
      final String? instructionsPic1,
      final String? instructionsPic2,
      final String? instructionsPic3,
      final String? goodsPic1,
      final String? goodsPic2,
      final String? goodsPic3,
      final String? actGoodsPic1,
      final String? actGoodsPic2,
      final String? actGoodsPic3,
      final String? orders,
      final String? remark,
      final int? createBy,
      final String? createTime,
      final int? updateBy,
      final String? updateTime,
      final double? minValue,
      final double? maxValue,
      final int? statusCd,
      final dynamic params,
      final dynamic content,
      final dynamic manufacturer}) = _$AgriRecordsInputImpl;

  factory _AgriRecordsInput.fromJson(Map<String, dynamic> json) =
      _$AgriRecordsInputImpl.fromJson;

  @override
  int? get agriRecordsInputsId;
  @override
  int? get agriRecordsId;
  @override
  String? get meansProdType;
  @override
  int? get meansProdId;
  @override
  String? get meansProdName;
  @override
  double? get totalDosage;
  @override
  String? get meansProdUnit;
  @override
  String? get meansProdUnitName;
  @override
  String? get instructionsPic1;
  @override
  String? get instructionsPic2;
  @override
  String? get instructionsPic3;
  @override
  String? get goodsPic1;
  @override
  String? get goodsPic2;
  @override
  String? get goodsPic3;
  @override
  String? get actGoodsPic1;
  @override
  String? get actGoodsPic2;
  @override
  String? get actGoodsPic3;
  @override
  String? get orders;
  @override
  String? get remark;
  @override
  int? get createBy;
  @override
  String? get createTime;
  @override
  int? get updateBy;
  @override
  String? get updateTime;
  @override
  double? get minValue;
  @override
  double? get maxValue;
  @override
  int? get statusCd;
  @override
  dynamic get params;
  @override
  dynamic get content;
  @override
  dynamic get manufacturer;

  /// Create a copy of AgriRecordsInput
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AgriRecordsInputImplCopyWith<_$AgriRecordsInputImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
