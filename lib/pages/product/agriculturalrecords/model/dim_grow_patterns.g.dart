// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dim_grow_patterns.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DimGrowPatternsImpl _$$DimGrowPatternsImplFromJson(
        Map<String, dynamic> json) =>
    _$DimGrowPatternsImpl(
      growPatternsId: (json['growPatternsId'] as num?)?.toInt(),
      growPatternsName: json['growPatternsName'] as String?,
      orgCode: json['orgCode'] as String?,
      orgName: json['orgName'] as String?,
      raiseCrops: json['raiseCrops'] as String?,
      raiseCropsNm: json['raiseCropsNm'] as String?,
      remark: json['remark'] as String?,
      createBy: (json['createBy'] as num?)?.toInt(),
      createName: json['createName'] as String?,
      createTime: json['createTime'] as String?,
      updateBy: (json['updateBy'] as num?)?.toInt(),
      updateTime: json['updateTime'] as String?,
      statYear: json['statYear'] as String?,
      isDef: json['isDef'] as String?,
      isOrgPlant: json['isOrgPlant'],
      statusCd: (json['statusCd'] as num?)?.toInt(),
      params: json['params'],
      varietyCdArrayList: json['varietyCdArrayList'],
      raiseCropsVarietyNm: json['raiseCropsVarietyNm'],
      isSeedLand: json['isSeedLand'],
    );

Map<String, dynamic> _$$DimGrowPatternsImplToJson(
        _$DimGrowPatternsImpl instance) =>
    <String, dynamic>{
      'growPatternsId': instance.growPatternsId,
      'growPatternsName': instance.growPatternsName,
      'orgCode': instance.orgCode,
      'orgName': instance.orgName,
      'raiseCrops': instance.raiseCrops,
      'raiseCropsNm': instance.raiseCropsNm,
      'remark': instance.remark,
      'createBy': instance.createBy,
      'createName': instance.createName,
      'createTime': instance.createTime,
      'updateBy': instance.updateBy,
      'updateTime': instance.updateTime,
      'statYear': instance.statYear,
      'isDef': instance.isDef,
      'isOrgPlant': instance.isOrgPlant,
      'statusCd': instance.statusCd,
      'params': instance.params,
      'varietyCdArrayList': instance.varietyCdArrayList,
      'raiseCropsVarietyNm': instance.raiseCropsVarietyNm,
      'isSeedLand': instance.isSeedLand,
    };
