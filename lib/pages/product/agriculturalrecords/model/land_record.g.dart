// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'land_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$LandRecordImpl _$$LandRecordImplFromJson(Map<String, dynamic> json) =>
    _$LandRecordImpl(
      start: (json['start'] as num?)?.toInt(),
      end: (json['end'] as num?)?.toInt(),
      page: (json['page'] as num?)?.toInt(),
      rows: (json['rows'] as num?)?.toInt(),
      sort: json['sort'],
      dir: json['dir'],
      oldValue: json['oldValue'],
      needPagination: json['needPagination'] as bool?,
      userId: json['userId'],
      plantingActualId: (json['plantingActualId'] as num?)?.toInt(),
      plotGrowPatternsId: (json['plotGrowPatternsId'] as num?)?.toInt(),
      patternsLinkId: json['patternsLinkId'],
      agriRecordsId: json['agriRecordsId'],
      growPatternsId: (json['growPatternsId'] as num?)?.toInt(),
      growPatternsName: json['growPatternsName'] as String?,
      landNo: json['landNo'] as String?,
      landName: json['landName'] as String?,
      landArea: json['landArea'] as String?,
      plotBase64: json['plotBase64'],
      orgCode: json['orgCode'] as String?,
      orgName: json['orgName'] as String?,
      statYear: json['statYear'] as String?,
      raiseCrops: json['raiseCrops'] as String?,
      raiseCropsNm: json['raiseCropsNm'] as String?,
      raiseCropsVariety: (json['raiseCropsVariety'] as num?)?.toInt(),
      raiseCropsVarietyNm: json['raiseCropsVarietyNm'] as String?,
      isHave: json['isHave'] as String?,
      nowDate: json['nowDate'],
      actBeginDate: json['actBeginDate'],
      linkCode: json['linkCode'],
      remark: json['remark'],
      createTime: json['createTime'] as String?,
      createBy: json['createBy'],
      statusCd: json['statusCd'],
      params: json['params'],
      workStationCode: json['workStationCode'] as String?,
      workStationName: json['workStationName'] as String?,
      landNoList: json['landNoList'],
      isHaveRecord: json['isHaveRecord'] as String?,
      workArea: json['workArea'],
      closeFlag: json['closeFlag'],
      isTrace: json['isTrace'],
      backReason: json['backReason'],
      isBack: json['isBack'],
      agriRecordsIds: json['agriRecordsIds'],
      outOeder: json['outOeder'],
      workAreaOpt: json['workAreaOpt'],
      plotId: (json['plotId'] as num?)?.toInt(),
    );

Map<String, dynamic> _$$LandRecordImplToJson(_$LandRecordImpl instance) =>
    <String, dynamic>{
      'start': instance.start,
      'end': instance.end,
      'page': instance.page,
      'rows': instance.rows,
      'sort': instance.sort,
      'dir': instance.dir,
      'oldValue': instance.oldValue,
      'needPagination': instance.needPagination,
      'userId': instance.userId,
      'plantingActualId': instance.plantingActualId,
      'plotGrowPatternsId': instance.plotGrowPatternsId,
      'patternsLinkId': instance.patternsLinkId,
      'agriRecordsId': instance.agriRecordsId,
      'growPatternsId': instance.growPatternsId,
      'growPatternsName': instance.growPatternsName,
      'landNo': instance.landNo,
      'landName': instance.landName,
      'landArea': instance.landArea,
      'plotBase64': instance.plotBase64,
      'orgCode': instance.orgCode,
      'orgName': instance.orgName,
      'statYear': instance.statYear,
      'raiseCrops': instance.raiseCrops,
      'raiseCropsNm': instance.raiseCropsNm,
      'raiseCropsVariety': instance.raiseCropsVariety,
      'raiseCropsVarietyNm': instance.raiseCropsVarietyNm,
      'isHave': instance.isHave,
      'nowDate': instance.nowDate,
      'actBeginDate': instance.actBeginDate,
      'linkCode': instance.linkCode,
      'remark': instance.remark,
      'createTime': instance.createTime,
      'createBy': instance.createBy,
      'statusCd': instance.statusCd,
      'params': instance.params,
      'workStationCode': instance.workStationCode,
      'workStationName': instance.workStationName,
      'landNoList': instance.landNoList,
      'isHaveRecord': instance.isHaveRecord,
      'workArea': instance.workArea,
      'closeFlag': instance.closeFlag,
      'isTrace': instance.isTrace,
      'backReason': instance.backReason,
      'isBack': instance.isBack,
      'agriRecordsIds': instance.agriRecordsIds,
      'outOeder': instance.outOeder,
      'workAreaOpt': instance.workAreaOpt,
      'plotId': instance.plotId,
    };
