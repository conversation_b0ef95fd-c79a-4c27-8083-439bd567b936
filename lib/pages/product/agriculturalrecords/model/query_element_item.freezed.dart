// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'query_element_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

QueryElementItem _$QueryElementItemFromJson(Map<String, dynamic> json) {
  return _QueryElementItem.fromJson(json);
}

/// @nodoc
mixin _$QueryElementItem {
  String? get itemKey => throw _privateConstructorUsedError;
  dynamic get eleValue => throw _privateConstructorUsedError;
  String? get itemDesc => throw _privateConstructorUsedError;
  String? get itemFlag => throw _privateConstructorUsedError;

  /// Serializes this QueryElementItem to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of QueryElementItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $QueryElementItemCopyWith<QueryElementItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $QueryElementItemCopyWith<$Res> {
  factory $QueryElementItemCopyWith(
          QueryElementItem value, $Res Function(QueryElementItem) then) =
      _$QueryElementItemCopyWithImpl<$Res, QueryElementItem>;
  @useResult
  $Res call(
      {String? itemKey, dynamic eleValue, String? itemDesc, String? itemFlag});
}

/// @nodoc
class _$QueryElementItemCopyWithImpl<$Res, $Val extends QueryElementItem>
    implements $QueryElementItemCopyWith<$Res> {
  _$QueryElementItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of QueryElementItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? itemKey = freezed,
    Object? eleValue = freezed,
    Object? itemDesc = freezed,
    Object? itemFlag = freezed,
  }) {
    return _then(_value.copyWith(
      itemKey: freezed == itemKey
          ? _value.itemKey
          : itemKey // ignore: cast_nullable_to_non_nullable
              as String?,
      eleValue: freezed == eleValue
          ? _value.eleValue
          : eleValue // ignore: cast_nullable_to_non_nullable
              as dynamic,
      itemDesc: freezed == itemDesc
          ? _value.itemDesc
          : itemDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      itemFlag: freezed == itemFlag
          ? _value.itemFlag
          : itemFlag // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$QueryElementItemImplCopyWith<$Res>
    implements $QueryElementItemCopyWith<$Res> {
  factory _$$QueryElementItemImplCopyWith(_$QueryElementItemImpl value,
          $Res Function(_$QueryElementItemImpl) then) =
      __$$QueryElementItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? itemKey, dynamic eleValue, String? itemDesc, String? itemFlag});
}

/// @nodoc
class __$$QueryElementItemImplCopyWithImpl<$Res>
    extends _$QueryElementItemCopyWithImpl<$Res, _$QueryElementItemImpl>
    implements _$$QueryElementItemImplCopyWith<$Res> {
  __$$QueryElementItemImplCopyWithImpl(_$QueryElementItemImpl _value,
      $Res Function(_$QueryElementItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of QueryElementItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? itemKey = freezed,
    Object? eleValue = freezed,
    Object? itemDesc = freezed,
    Object? itemFlag = freezed,
  }) {
    return _then(_$QueryElementItemImpl(
      itemKey: freezed == itemKey
          ? _value.itemKey
          : itemKey // ignore: cast_nullable_to_non_nullable
              as String?,
      eleValue: freezed == eleValue
          ? _value.eleValue
          : eleValue // ignore: cast_nullable_to_non_nullable
              as dynamic,
      itemDesc: freezed == itemDesc
          ? _value.itemDesc
          : itemDesc // ignore: cast_nullable_to_non_nullable
              as String?,
      itemFlag: freezed == itemFlag
          ? _value.itemFlag
          : itemFlag // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$QueryElementItemImpl implements _QueryElementItem {
  _$QueryElementItemImpl(
      {this.itemKey, this.eleValue, this.itemDesc, this.itemFlag});

  factory _$QueryElementItemImpl.fromJson(Map<String, dynamic> json) =>
      _$$QueryElementItemImplFromJson(json);

  @override
  final String? itemKey;
  @override
  final dynamic eleValue;
  @override
  final String? itemDesc;
  @override
  final String? itemFlag;

  @override
  String toString() {
    return 'QueryElementItem(itemKey: $itemKey, eleValue: $eleValue, itemDesc: $itemDesc, itemFlag: $itemFlag)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$QueryElementItemImpl &&
            (identical(other.itemKey, itemKey) || other.itemKey == itemKey) &&
            const DeepCollectionEquality().equals(other.eleValue, eleValue) &&
            (identical(other.itemDesc, itemDesc) ||
                other.itemDesc == itemDesc) &&
            (identical(other.itemFlag, itemFlag) ||
                other.itemFlag == itemFlag));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, itemKey,
      const DeepCollectionEquality().hash(eleValue), itemDesc, itemFlag);

  /// Create a copy of QueryElementItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$QueryElementItemImplCopyWith<_$QueryElementItemImpl> get copyWith =>
      __$$QueryElementItemImplCopyWithImpl<_$QueryElementItemImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$QueryElementItemImplToJson(
      this,
    );
  }
}

abstract class _QueryElementItem implements QueryElementItem {
  factory _QueryElementItem(
      {final String? itemKey,
      final dynamic eleValue,
      final String? itemDesc,
      final String? itemFlag}) = _$QueryElementItemImpl;

  factory _QueryElementItem.fromJson(Map<String, dynamic> json) =
      _$QueryElementItemImpl.fromJson;

  @override
  String? get itemKey;
  @override
  dynamic get eleValue;
  @override
  String? get itemDesc;
  @override
  String? get itemFlag;

  /// Create a copy of QueryElementItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$QueryElementItemImplCopyWith<_$QueryElementItemImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
