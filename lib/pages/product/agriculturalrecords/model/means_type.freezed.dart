// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'means_type.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

MeansType _$MeansTypeFromJson(Map<String, dynamic> json) {
  return _MeansType.fromJson(json);
}

/// @nodoc
mixin _$MeansType {
  int? get meansProdType => throw _privateConstructorUsedError;
  String? get meansProdTypeName => throw _privateConstructorUsedError;
  int? get meansProdId => throw _privateConstructorUsedError;
  String? get meansProdName => throw _privateConstructorUsedError;
  dynamic get orderNnum => throw _privateConstructorUsedError;
  List<MeansType>? get children => throw _privateConstructorUsedError;
  String? get content => throw _privateConstructorUsedError;
  String? get manufacturer => throw _privateConstructorUsedError;
  String? get meansProdUnit => throw _privateConstructorUsedError;
  double? get minValue => throw _privateConstructorUsedError;
  double? get maxValue => throw _privateConstructorUsedError;
  String? get applicCrops => throw _privateConstructorUsedError;
  String? get pesticideType => throw _privateConstructorUsedError;
  String? get meansProdSubtype => throw _privateConstructorUsedError;
  String? get pesticideTypeName => throw _privateConstructorUsedError;

  /// Serializes this MeansType to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of MeansType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $MeansTypeCopyWith<MeansType> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $MeansTypeCopyWith<$Res> {
  factory $MeansTypeCopyWith(MeansType value, $Res Function(MeansType) then) =
      _$MeansTypeCopyWithImpl<$Res, MeansType>;
  @useResult
  $Res call(
      {int? meansProdType,
      String? meansProdTypeName,
      int? meansProdId,
      String? meansProdName,
      dynamic orderNnum,
      List<MeansType>? children,
      String? content,
      String? manufacturer,
      String? meansProdUnit,
      double? minValue,
      double? maxValue,
      String? applicCrops,
      String? pesticideType,
      String? meansProdSubtype,
      String? pesticideTypeName});
}

/// @nodoc
class _$MeansTypeCopyWithImpl<$Res, $Val extends MeansType>
    implements $MeansTypeCopyWith<$Res> {
  _$MeansTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of MeansType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? meansProdType = freezed,
    Object? meansProdTypeName = freezed,
    Object? meansProdId = freezed,
    Object? meansProdName = freezed,
    Object? orderNnum = freezed,
    Object? children = freezed,
    Object? content = freezed,
    Object? manufacturer = freezed,
    Object? meansProdUnit = freezed,
    Object? minValue = freezed,
    Object? maxValue = freezed,
    Object? applicCrops = freezed,
    Object? pesticideType = freezed,
    Object? meansProdSubtype = freezed,
    Object? pesticideTypeName = freezed,
  }) {
    return _then(_value.copyWith(
      meansProdType: freezed == meansProdType
          ? _value.meansProdType
          : meansProdType // ignore: cast_nullable_to_non_nullable
              as int?,
      meansProdTypeName: freezed == meansProdTypeName
          ? _value.meansProdTypeName
          : meansProdTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdId: freezed == meansProdId
          ? _value.meansProdId
          : meansProdId // ignore: cast_nullable_to_non_nullable
              as int?,
      meansProdName: freezed == meansProdName
          ? _value.meansProdName
          : meansProdName // ignore: cast_nullable_to_non_nullable
              as String?,
      orderNnum: freezed == orderNnum
          ? _value.orderNnum
          : orderNnum // ignore: cast_nullable_to_non_nullable
              as dynamic,
      children: freezed == children
          ? _value.children
          : children // ignore: cast_nullable_to_non_nullable
              as List<MeansType>?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      manufacturer: freezed == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdUnit: freezed == meansProdUnit
          ? _value.meansProdUnit
          : meansProdUnit // ignore: cast_nullable_to_non_nullable
              as String?,
      minValue: freezed == minValue
          ? _value.minValue
          : minValue // ignore: cast_nullable_to_non_nullable
              as double?,
      maxValue: freezed == maxValue
          ? _value.maxValue
          : maxValue // ignore: cast_nullable_to_non_nullable
              as double?,
      applicCrops: freezed == applicCrops
          ? _value.applicCrops
          : applicCrops // ignore: cast_nullable_to_non_nullable
              as String?,
      pesticideType: freezed == pesticideType
          ? _value.pesticideType
          : pesticideType // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdSubtype: freezed == meansProdSubtype
          ? _value.meansProdSubtype
          : meansProdSubtype // ignore: cast_nullable_to_non_nullable
              as String?,
      pesticideTypeName: freezed == pesticideTypeName
          ? _value.pesticideTypeName
          : pesticideTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$MeansTypeImplCopyWith<$Res>
    implements $MeansTypeCopyWith<$Res> {
  factory _$$MeansTypeImplCopyWith(
          _$MeansTypeImpl value, $Res Function(_$MeansTypeImpl) then) =
      __$$MeansTypeImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? meansProdType,
      String? meansProdTypeName,
      int? meansProdId,
      String? meansProdName,
      dynamic orderNnum,
      List<MeansType>? children,
      String? content,
      String? manufacturer,
      String? meansProdUnit,
      double? minValue,
      double? maxValue,
      String? applicCrops,
      String? pesticideType,
      String? meansProdSubtype,
      String? pesticideTypeName});
}

/// @nodoc
class __$$MeansTypeImplCopyWithImpl<$Res>
    extends _$MeansTypeCopyWithImpl<$Res, _$MeansTypeImpl>
    implements _$$MeansTypeImplCopyWith<$Res> {
  __$$MeansTypeImplCopyWithImpl(
      _$MeansTypeImpl _value, $Res Function(_$MeansTypeImpl) _then)
      : super(_value, _then);

  /// Create a copy of MeansType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? meansProdType = freezed,
    Object? meansProdTypeName = freezed,
    Object? meansProdId = freezed,
    Object? meansProdName = freezed,
    Object? orderNnum = freezed,
    Object? children = freezed,
    Object? content = freezed,
    Object? manufacturer = freezed,
    Object? meansProdUnit = freezed,
    Object? minValue = freezed,
    Object? maxValue = freezed,
    Object? applicCrops = freezed,
    Object? pesticideType = freezed,
    Object? meansProdSubtype = freezed,
    Object? pesticideTypeName = freezed,
  }) {
    return _then(_$MeansTypeImpl(
      meansProdType: freezed == meansProdType
          ? _value.meansProdType
          : meansProdType // ignore: cast_nullable_to_non_nullable
              as int?,
      meansProdTypeName: freezed == meansProdTypeName
          ? _value.meansProdTypeName
          : meansProdTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdId: freezed == meansProdId
          ? _value.meansProdId
          : meansProdId // ignore: cast_nullable_to_non_nullable
              as int?,
      meansProdName: freezed == meansProdName
          ? _value.meansProdName
          : meansProdName // ignore: cast_nullable_to_non_nullable
              as String?,
      orderNnum: freezed == orderNnum
          ? _value.orderNnum
          : orderNnum // ignore: cast_nullable_to_non_nullable
              as dynamic,
      children: freezed == children
          ? _value._children
          : children // ignore: cast_nullable_to_non_nullable
              as List<MeansType>?,
      content: freezed == content
          ? _value.content
          : content // ignore: cast_nullable_to_non_nullable
              as String?,
      manufacturer: freezed == manufacturer
          ? _value.manufacturer
          : manufacturer // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdUnit: freezed == meansProdUnit
          ? _value.meansProdUnit
          : meansProdUnit // ignore: cast_nullable_to_non_nullable
              as String?,
      minValue: freezed == minValue
          ? _value.minValue
          : minValue // ignore: cast_nullable_to_non_nullable
              as double?,
      maxValue: freezed == maxValue
          ? _value.maxValue
          : maxValue // ignore: cast_nullable_to_non_nullable
              as double?,
      applicCrops: freezed == applicCrops
          ? _value.applicCrops
          : applicCrops // ignore: cast_nullable_to_non_nullable
              as String?,
      pesticideType: freezed == pesticideType
          ? _value.pesticideType
          : pesticideType // ignore: cast_nullable_to_non_nullable
              as String?,
      meansProdSubtype: freezed == meansProdSubtype
          ? _value.meansProdSubtype
          : meansProdSubtype // ignore: cast_nullable_to_non_nullable
              as String?,
      pesticideTypeName: freezed == pesticideTypeName
          ? _value.pesticideTypeName
          : pesticideTypeName // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$MeansTypeImpl implements _MeansType {
  _$MeansTypeImpl(
      {this.meansProdType,
      this.meansProdTypeName,
      this.meansProdId,
      this.meansProdName,
      this.orderNnum,
      final List<MeansType>? children,
      this.content,
      this.manufacturer,
      this.meansProdUnit,
      this.minValue,
      this.maxValue,
      this.applicCrops,
      this.pesticideType,
      this.meansProdSubtype,
      this.pesticideTypeName})
      : _children = children;

  factory _$MeansTypeImpl.fromJson(Map<String, dynamic> json) =>
      _$$MeansTypeImplFromJson(json);

  @override
  final int? meansProdType;
  @override
  final String? meansProdTypeName;
  @override
  final int? meansProdId;
  @override
  final String? meansProdName;
  @override
  final dynamic orderNnum;
  final List<MeansType>? _children;
  @override
  List<MeansType>? get children {
    final value = _children;
    if (value == null) return null;
    if (_children is EqualUnmodifiableListView) return _children;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? content;
  @override
  final String? manufacturer;
  @override
  final String? meansProdUnit;
  @override
  final double? minValue;
  @override
  final double? maxValue;
  @override
  final String? applicCrops;
  @override
  final String? pesticideType;
  @override
  final String? meansProdSubtype;
  @override
  final String? pesticideTypeName;

  @override
  String toString() {
    return 'MeansType(meansProdType: $meansProdType, meansProdTypeName: $meansProdTypeName, meansProdId: $meansProdId, meansProdName: $meansProdName, orderNnum: $orderNnum, children: $children, content: $content, manufacturer: $manufacturer, meansProdUnit: $meansProdUnit, minValue: $minValue, maxValue: $maxValue, applicCrops: $applicCrops, pesticideType: $pesticideType, meansProdSubtype: $meansProdSubtype, pesticideTypeName: $pesticideTypeName)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MeansTypeImpl &&
            (identical(other.meansProdType, meansProdType) ||
                other.meansProdType == meansProdType) &&
            (identical(other.meansProdTypeName, meansProdTypeName) ||
                other.meansProdTypeName == meansProdTypeName) &&
            (identical(other.meansProdId, meansProdId) ||
                other.meansProdId == meansProdId) &&
            (identical(other.meansProdName, meansProdName) ||
                other.meansProdName == meansProdName) &&
            const DeepCollectionEquality().equals(other.orderNnum, orderNnum) &&
            const DeepCollectionEquality().equals(other._children, _children) &&
            (identical(other.content, content) || other.content == content) &&
            (identical(other.manufacturer, manufacturer) ||
                other.manufacturer == manufacturer) &&
            (identical(other.meansProdUnit, meansProdUnit) ||
                other.meansProdUnit == meansProdUnit) &&
            (identical(other.minValue, minValue) ||
                other.minValue == minValue) &&
            (identical(other.maxValue, maxValue) ||
                other.maxValue == maxValue) &&
            (identical(other.applicCrops, applicCrops) ||
                other.applicCrops == applicCrops) &&
            (identical(other.pesticideType, pesticideType) ||
                other.pesticideType == pesticideType) &&
            (identical(other.meansProdSubtype, meansProdSubtype) ||
                other.meansProdSubtype == meansProdSubtype) &&
            (identical(other.pesticideTypeName, pesticideTypeName) ||
                other.pesticideTypeName == pesticideTypeName));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      meansProdType,
      meansProdTypeName,
      meansProdId,
      meansProdName,
      const DeepCollectionEquality().hash(orderNnum),
      const DeepCollectionEquality().hash(_children),
      content,
      manufacturer,
      meansProdUnit,
      minValue,
      maxValue,
      applicCrops,
      pesticideType,
      meansProdSubtype,
      pesticideTypeName);

  /// Create a copy of MeansType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MeansTypeImplCopyWith<_$MeansTypeImpl> get copyWith =>
      __$$MeansTypeImplCopyWithImpl<_$MeansTypeImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$MeansTypeImplToJson(
      this,
    );
  }
}

abstract class _MeansType implements MeansType {
  factory _MeansType(
      {final int? meansProdType,
      final String? meansProdTypeName,
      final int? meansProdId,
      final String? meansProdName,
      final dynamic orderNnum,
      final List<MeansType>? children,
      final String? content,
      final String? manufacturer,
      final String? meansProdUnit,
      final double? minValue,
      final double? maxValue,
      final String? applicCrops,
      final String? pesticideType,
      final String? meansProdSubtype,
      final String? pesticideTypeName}) = _$MeansTypeImpl;

  factory _MeansType.fromJson(Map<String, dynamic> json) =
      _$MeansTypeImpl.fromJson;

  @override
  int? get meansProdType;
  @override
  String? get meansProdTypeName;
  @override
  int? get meansProdId;
  @override
  String? get meansProdName;
  @override
  dynamic get orderNnum;
  @override
  List<MeansType>? get children;
  @override
  String? get content;
  @override
  String? get manufacturer;
  @override
  String? get meansProdUnit;
  @override
  double? get minValue;
  @override
  double? get maxValue;
  @override
  String? get applicCrops;
  @override
  String? get pesticideType;
  @override
  String? get meansProdSubtype;
  @override
  String? get pesticideTypeName;

  /// Create a copy of MeansType
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MeansTypeImplCopyWith<_$MeansTypeImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
