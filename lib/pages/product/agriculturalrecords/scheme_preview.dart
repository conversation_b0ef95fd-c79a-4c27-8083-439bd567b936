import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

import 'model/dim_grow_patterns.dart';
import 'model/grow_patterns.dart';
import 'request/agricultural_records_service.dart';
import 'widget/dotted_image.dart';
import 'widget/speck_widget.dart';

class SchemePreviewPage extends StatefulWidget {
  final DimGrowPatterns item;
  const SchemePreviewPage({super.key, required this.item});

  @override
  State<SchemePreviewPage> createState() => _SchemePreviewPageState();
}

class _SchemePreviewPageState extends MixinUseState<SchemePreviewPage> {
  late final controller = useController(_Controller(context, widget.item));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "方案预览",
        ),
      ),
      backgroundColor: const Color(0xFFF3F5F9),
      body: UseBuilder((context) {
        var status = controller.loadingStatus.value ?? LoadingStatus.init;
        switch (status) {
          case LoadingStatus.loading:
          case LoadingStatus.init:
          case LoadingStatus.error:
          case LoadingStatus.cancel:
            return _widgetLoading();
          case LoadingStatus.success:
          case LoadingStatus.loadingMore:
          case LoadingStatus.refreshing:
            return _widgetBody();
        }
      }),
    );
  }

  Widget _widgetEmpty() {
    return const Center(
      child: BdhEmptyView(
        tipInfo: "未查到记录",
      ),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    var list = controller.items.value ?? [];
    if (list.isEmpty) {
      return _widgetEmpty();
    }
    return Container(
        margin: EdgeInsets.all(14.px),
        child: Column(
          children: [
            SizedBox(
              height: 7.px,
            ),
            Row(
              children: [
                Container(
                  color: Colors.blue,
                  width: 4.px,
                  height: 20.px,
                ),
                SizedBox(
                  width: 7.px,
                ),
                Expanded(
                    child: Text(
                  widget.item.growPatternsName ?? "",
                  style: TextStyle(
                      color: const Color.fromRGBO(85, 145, 255, 1),
                      fontWeight: FontWeight.w500,
                      fontSize: 16.px),
                )),
              ],
            ),
            SizedBox(
              height: 14.px,
            ),
            Expanded(
                child: ListView(
              controller: controller.scrollController,
              children: [
                for (int i = 0; i < list.length; i++) _widgetItem(list[i], i)
              ],
            )),
            SizedBox(
              height: 14.px,
            ),
            BdhTextButton(
              height: 34.px,
              width: 351.px,
              text: '确认选择',
              textFontWeight: FontWeight.w500,
              textSize: 13.px,
              borderRadius: BorderRadius.all(Radius.circular(6.px)),
              backgroundColor: const Color.fromARGB(240, 94, 139, 245),
              disableBackgroundColor: const Color.fromARGB(255, 224, 223, 223),
              pressedBackgroundColor: const Color.fromARGB(255, 94, 139, 245),
              foregroundColor: Colors.white,
              disableForegroundColor: Colors.white,
              pressedForegroundColor: Colors.white,
              onPressed: controller.onClickConfirm,
            )
          ],
        ));
  }

  Widget _widgetItem(GrowPatterns item, int index) {
    return Column(
      children: [
        Row(
          children: [
            Container(
              width: 10.px,
              height: 10.px,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: const Color.fromRGBO(85, 145, 255, 0.3),
                  borderRadius: BorderRadius.all(Radius.circular(5.px))),
              child: Container(
                width: 6.px,
                height: 6.px,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: const Color.fromRGBO(85, 145, 255, 1),
                    borderRadius: BorderRadius.all(Radius.circular(3.px))),
              ),
            ),
            SizedBox(
              width: 14.px,
            ),
            Expanded(
                child: Text(
              item.prodProcessName ?? "",
              style: TextStyle(
                  color: const Color.fromRGBO(85, 145, 255, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: 14.px),
            )),
          ],
        ),
        Column(
          children: item.children
                  ?.map((test) => _widgetSubItem(item, test))
                  .toList() ??
              [],
        )
      ],
    );
  }

  Widget _widgetSubItem(GrowPatterns fatherItem, GrowPatterns item) {
    Widget rightWidget = Container(
      margin: EdgeInsets.only(top: 14.px),
      padding: EdgeInsets.all(14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                  child: Text(
                "${item.linkName ?? ""}${item.growthPeriod == null ? "" : "(${item.growthPeriod})"}",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: const Color.fromRGBO(44, 44, 44, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 14.px),
              )),
              SizedBox(
                width: 7.px,
              ),
              SpeckWidget(text: item.standard)
            ],
          ),
          SizedBox(
            height: 7.px,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                "${item.standard ?? "无"}",
                style: TextStyle(
                    color: const Color.fromRGBO(44, 44, 44, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 12.px),
              )),
            ],
          ),
          SizedBox(
            height: 4.px,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                "${dateFormat(item.planStartDate)}-${dateFormat(item.planEndDate)}",
                style: TextStyle(
                    color: const Color.fromRGBO(44, 44, 44, 0.4),
                    fontWeight: FontWeight.w400,
                    fontSize: 12.px),
              )),
            ],
          ),
          SizedBox(
            height: 7.px,
          ),
        ],
      ),
    );

    return IntrinsicHeight(
        child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          width: 10.px,
          alignment: Alignment.topCenter,
          child: Container(
            width: 2.px,
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(
              image: DecorationImage(
                  fit: BoxFit.contain,
                  image: DottedImage(size: 100, space: 100),
                  repeat: ImageRepeat.repeatY),
            ),
          )),
      SizedBox(
        width: 14.px,
      ),
      Expanded(child: rightWidget)
    ]));
  }

  String dateFormat(String s) {
    return '${s.substring(0, 2)}月${s.substring(2)}日';
  }
}

class _Controller extends UseController {
  final DimGrowPatterns item;
  _Controller(super.context, this.item);

  late final scrollController = createScrollController();

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  late final items = use<List<GrowPatterns>>(null);

  void onInit() {
    var data = {"growPatternsId": item.growPatternsId};
    loadingStatus.value = LoadingStatus.loading;
    AgriculturalRecordsService()
        .previewGrowPatterns(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (result.success == true && result.code == 0 && result.data != null) {
        var loadItems = result.data
                .map<GrowPatterns>((v) => GrowPatterns.fromJson(v))
                .toList() ??
            [];
        items.value = loadItems;
        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }

  void onClickConfirm() {
    showConfirmDialog(context, title: "注意", message: "一旦绑定种植方案，本年度不可更改")
        .then((result) {
      if (!context.mounted) {
        return;
      }
      if (result == true) {
        Navigator.of(context).pop(true);
      }
    });
  }
}
