import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_searchable_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';

import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

import 'model/dim_grow_patterns.dart';
import 'model/land_record.dart';
import 'request/agricultural_records_service.dart';
import 'scheme_preview.dart';

class LiveBroadPage extends StatefulWidget {
  final LandRecord item;
  const LiveBroadPage({super.key, required this.item});

  @override
  State<LiveBroadPage> createState() => _LiveBroadPageState();
}

class _LiveBroadPageState extends MixinUseState<LiveBroadPage> {
  late final controller = useController(_Controller(context, widget.item));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF3F5F9),
      body: Stack(
        children: [
          Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: Container(
                height: 150.px,
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                      Color.fromARGB(255, 94, 139, 245),
                      Color(0xFFF3F5F9),
                    ])),
              )),
          Column(
            children: [
              AppBar(
                title: const Text(
                  "实播信息",
                  style: TextStyle(color: Colors.white),
                ),
                leading: const BackButton(
                  color: Colors.white,
                ),
                foregroundColor: Colors.white,
                backgroundColor: Colors.transparent,
              ),
              Expanded(child: UseBuilder((context) {
                return UseBuilder((context) {
                  var status =
                      controller.loadingStatus.value ?? LoadingStatus.init;
                  switch (status) {
                    case LoadingStatus.loading:
                    case LoadingStatus.init:
                    case LoadingStatus.error:
                    case LoadingStatus.cancel:
                      return _widgetLoading();
                    case LoadingStatus.success:
                    case LoadingStatus.loadingMore:
                    case LoadingStatus.refreshing:
                      return _widgetBody();
                  }
                });
              }))
            ],
          )
        ],
      ),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    return Column(
      children: [
        widgetContainer(children: [
          widgetItem("作物", controller.item.raiseCropsNm ?? "", true),
          widgetItem("品种", controller.item.raiseCropsVarietyNm ?? "", true),
          UseBuilder((context) {
            Widget child = widgetItem("种植方案",
                controller.plantOption.value?.growPatternsName ?? "", true);
            return GestureDetector(
              onTap: () {
                controller.onClickPlatOption();
              },
              child: child,
            );
          })
        ]),
        const Spacer(),
        if (controller.plantOption.value != null) ...[
          BdhTextButton(
            height: 44.px,
            width: 351.px,
            text: '确认添加',
            textFontWeight: FontWeight.w500,
            textSize: 16.px,
            borderRadius: BorderRadius.all(Radius.circular(6.px)),
            backgroundColor: const Color.fromARGB(240, 94, 139, 245),
            disableBackgroundColor: const Color.fromARGB(255, 224, 223, 223),
            pressedBackgroundColor: const Color.fromARGB(255, 94, 139, 245),
            foregroundColor: Colors.white,
            disableForegroundColor: Colors.white,
            pressedForegroundColor: Colors.white,
            onPressed: controller.onSubmit,
          ),
          SizedBox(
            height: 14.px,
          )
        ]
      ],
    );
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      width: 347.px,
      child: Column(
        children: children,
      ),
    );
  }

  Widget widgetHeightSpace({double? height}) {
    return SizedBox(
      height: height ?? 14.px,
    );
  }

  Widget widgetItem(String title, String value, bool showBottomLine) {
    Widget titleWidget = SizedBox(
        child: Text.rich(TextSpan(children: [
      TextSpan(
          text: title,
          style: TextStyle(
              fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)))
    ])));

    Widget textWidget = Text(
      value,
      textAlign: TextAlign.right,
      style: TextStyle(
          fontSize: 16.px,
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w600),
    );

    textWidget = Expanded(child: textWidget);
    return Container(
      decoration: showBottomLine
          ? BoxDecoration(
              border: Border(
                  bottom: BorderSide(
                      width: 1.px,
                      color: const Color.fromRGBO(226, 235, 231, 0.6))))
          : null,
      constraints: BoxConstraints(minHeight: 44.px),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          titleWidget,
          Expanded(
              child: Row(
            children: [
              SizedBox(
                width: 10.px,
              ),
              // if (textAlign == TextAlign.right) const Spacer(),
              textWidget,
              // if (textAlign == TextAlign.left) const Spacer(),
              SizedBox(
                width: 10.px,
              ),
              Image.asset(
                  width: 6.9.px,
                  height: 11.07.px,
                  ImageHelper.wrapAssets("arrow_right_black.png")),
            ],
          ))
        ],
      ),
    );
  }

  Widget widgetOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DictNode? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(DictNode?)? onChange}) {
    return BdhSearchableSingleDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      initialValue: initialValue,
      placeholder: placeholder,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 16.px,
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w600),
      showBottomLine: showBottomLine,
      onChange: onChange,
      canShowPicker: canShowPicker,
      checkState: true,
    );
  }
}

class _Controller extends UseController {
  final LandRecord item;

  _Controller(super.context, this.item);

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  late final plantOption = use<DimGrowPatterns>(null);

  void onInit() {
    loadingStatus.value = LoadingStatus.success;
  }

  void onClickPlatOption() {
    showModalBottomSheet<DimGrowPatterns?>(
        context: context,
        useSafeArea: true,
        isScrollControlled: true,
        builder: (context) {
          return Padding(
            padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom),
            child: _BottomSheet(item: item),
          );
        }).then((result) {
      if (result != null) {
        plantOption.value = result;

        onInit();
      }
    });
  }

  void onSubmit() {
    if (plantOption.value == null) {
      return;
    }
    var data = {
      "growPatternsId": plantOption.value?.growPatternsId,
      "growPatternsName": plantOption.value?.growPatternsName,
      "landName": item.landName,
      "landNo": item.landNo,
      "orgCode": item.orgCode,
      "orgName": item.orgName,
      "statYear": item.statYear,
      "raiseCrops": item.raiseCrops,
      "raiseCropsVariety": item.raiseCropsVariety,
      "plantingActualId": item.plantingActualId,
    };
    showLoading(context, content: "正在保存..   ");
    AgriculturalRecordsService()
        .bindingGrowPatterns(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
      if (result.success == true && result.code == 0) {
        showToast("保存成功");
        Navigator.of(context).pop(true);
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    });
  }
}

class _BottomSheet extends StatefulWidget {
  final LandRecord item;
  const _BottomSheet({
    super.key,
    required this.item,
  });

  @override
  State<_BottomSheet> createState() => __BottomSheetState();
}

class __BottomSheetState extends MixinUseState<_BottomSheet>
    with SingleTickerProviderStateMixin {
  late final _animationController = createAnimationController(this);

  late final controller =
      useController(_BottomSheetController(context, widget.item));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      width: 347.px,
      child: Column(
        children: children,
      ),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetItem(DimGrowPatterns item, int index) {
    return UseBuilder((context) {
      return Container(
          margin: EdgeInsets.only(
              top: 6.px, bottom: 6.px, left: 12.px, right: 12.px),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.px),
          ),
          height: 54.px,
          child: Row(
            children: [
              SizedBox(
                width: 14.px,
              ),
              Container(
                color: Colors.blue,
                width: 4.px,
                height: 20.px,
              ),
              SizedBox(
                width: 7.px,
              ),
              Expanded(
                  child: Text(
                item.growPatternsName ?? "",
                style: TextStyle(
                    color: const Color.fromRGBO(44, 44, 44, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 16.px),
              )),
              BdhTextButton(
                height: 33.px,
                width: 60.px,
                text: "预览",
                textFontWeight: FontWeight.w500,
                textSize: 14.px,
                padding: EdgeInsets.only(left: 5.px, right: 5.px),
                borderRadius: BorderRadius.all(Radius.circular(5.px)),
                backgroundColor: const Color.fromARGB(240, 94, 139, 245),
                disableBackgroundColor:
                    const Color.fromARGB(255, 224, 223, 223),
                pressedBackgroundColor: const Color.fromARGB(255, 94, 139, 245),
                foregroundColor: Colors.white,
                disableForegroundColor: Colors.white,
                pressedForegroundColor: Colors.white,
                onPressed: () {
                  controller.onItemPreview(item);
                },
              ),
              SizedBox(
                width: 14.px,
              ),
            ],
          ));
    });
  }

  Widget _widgetEmpty() {
    return const Center(
      child: BdhEmptyView(
        tipInfo: "未查到记录",
      ),
    );
  }

  Widget _widgetBody() {
    List<DimGrowPatterns> list = controller.items.value ?? [];
    if (list.isEmpty) {
      return _widgetEmpty();
    }

    return ListView.builder(
      itemBuilder: (context, index) {
        var item = list[index];
        return _widgetItem(item, index);
      },
      itemCount: list.length,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BottomSheet(
        enableDrag: false,
        backgroundColor: Colors.white,
        animationController: _animationController,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(16.px)),
        ),
        onClosing: () {},
        builder: (context) {
          return Container(
            constraints: BoxConstraints(maxHeight: 400.px),
            padding: EdgeInsets.only(left: 15.px, right: 15.px, bottom: 20.px),
            decoration: const BoxDecoration(
                image: DecorationImage(
              image: AssetImage(
                  "assets/images/agriculturalrecords/batch_chooser_bg.png"), // 本地图片
              fit: BoxFit.fill,
            )),
            child: Column(
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 20.px),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                          padding: EdgeInsets.only(left: 4.px),
                          child: Icon(
                            Icons.close,
                            size: 24.px,
                            color: Colors.transparent,
                          )),
                      const Spacer(),
                      Text(
                        "请选择种植方案",
                        strutStyle:
                            StrutStyle(fontSize: 16.px, forceStrutHeight: true),
                        style: TextStyle(
                            color: const Color.fromRGBO(44, 44, 52, 1),
                            fontSize: 16.px,
                            fontWeight: FontWeight.w600),
                      ),
                      const Spacer(),
                      GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Padding(
                              padding: EdgeInsets.only(right: 4.px),
                              child: Icon(
                                Icons.close,
                                size: 24.px,
                                color: const Color.fromRGBO(115, 116, 131, 1),
                              ))),
                    ],
                  ),
                ),
                SizedBox(
                  height: 10.px,
                ),
                Text(
                  "一旦绑定,本年度",
                  strutStyle:
                      StrutStyle(fontSize: 12.px, forceStrutHeight: true),
                  style: TextStyle(
                      color: const Color.fromRGBO(44, 44, 52, 1),
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500),
                ),
                SizedBox(
                  height: 4.px,
                ),
                Text(
                  "不可修改",
                  strutStyle:
                      StrutStyle(fontSize: 12.px, forceStrutHeight: true),
                  style: TextStyle(
                      color: Colors.red,
                      fontSize: 12.px,
                      fontWeight: FontWeight.w500),
                ),
                SizedBox(
                  height: 7.px,
                ),
                Expanded(child: UseBuilder((context) {
                  var status =
                      controller.loadingStatus.value ?? LoadingStatus.init;
                  switch (status) {
                    case LoadingStatus.loading:
                    case LoadingStatus.init:
                    case LoadingStatus.error:
                    case LoadingStatus.cancel:
                      return _widgetLoading();
                    case LoadingStatus.success:
                    case LoadingStatus.loadingMore:
                    case LoadingStatus.refreshing:
                      return _widgetBody();
                  }
                })),
              ],
            ),
          );
        });
  }
}

class _BottomSheetController extends UseController {
  final LandRecord item;

  _BottomSheetController(super.context, this.item);

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  late final items = use<List<DimGrowPatterns>>(null);

  void onInit() {
    var data = {
      "raiseCrops": item.raiseCrops,
      "varietyCd": item.raiseCropsVariety,
      "orgCode": item.orgCode,
    };

    AgriculturalRecordsService()
        .dimGrowPatterns(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (result.code == 0 && result.success == true && result.data != null) {
        var loadItems = result.data
                .map<DimGrowPatterns>((v) => DimGrowPatterns.fromJson(v))
                .toList() ??
            [];
        items.value = loadItems;
        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }

  void onItemPreview(DimGrowPatterns item) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => SchemePreviewPage(
                  item: item,
                )))
        .then((result) {
      if (!context.mounted) {
        return;
      }
      if (result == true) {
        Navigator.of(context).pop(item);
      }
    });
  }
}
