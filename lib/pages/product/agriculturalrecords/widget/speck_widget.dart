import 'dart:async';

import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/tts_util.dart';
import 'package:flutter/material.dart';

class SpeckWidget extends StatefulWidget {
  final String? text;
  const SpeckWidget({super.key, this.text});

  @override
  State<SpeckWidget> createState() => _SpeckWidgetState();
}

class _SpeckWidgetState extends State<SpeckWidget>
    with TTSListener, SingleTickerProviderStateMixin {
  bool isSpecking = false;

  final icons = [
    Icons.volume_up,
    Icons.volume_down,
    Icons.volume_mute,
  ];
  AnimationController? _animationController;
  Tween<int>? _tween;

  Animation<int>? _animation;
  @override
  void initState() {
    super.initState();
    _animationController =
        AnimationController(duration: const Duration(seconds: 1), vsync: this);
    _tween = IntTween(begin: 0, end: icons.length - 1);
    _animationController?.addListener(() {
      Log.d("_animation value is ${_animation?.value}");
      setState(() {});
    });
    _animation = _tween?.animate(_animationController!);
  }

  @override
  void dispose() {
    super.dispose();

    _animationController?.dispose();
  }

  @override
  void setState(VoidCallback fn) {
    if (!mounted) {
      return;
    }
    super.setState(fn);
  }

  void startSpecking() {
    setState(() {
      isSpecking = true;
    });
    _animationController?.repeat(reverse: true);
  }

  void stopSpecking() {
    _animationController?.stop();
    setState(() {
      isSpecking = false;
    });
  }

  @override
  void onStart(String? speckingText) {
    Log.d("_SpeckWidgetState onStart ${widget.text}");
    if (isSpecking) {
      return;
    }
    startSpecking();
  }

  @override
  void onCancel(String? speckingText) {
    Log.d("_SpeckWidgetState onCancel ${widget.text}");
    if (speckingText == widget.text) {
      stopSpecking();
    }
  }

  @override
  void onComplete(String? speckingText) {
    Log.d("_SpeckWidgetState onComplete ${widget.text}");
    stopSpecking();
  }

  @override
  void onError(msg) {
    Log.d("_SpeckWidgetState onError $msg");
    stopSpecking();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          if (widget.text != null) {
            TTSService().speck(widget.text!, this);
          }
        },
        child: Container(
            width: 32.px,
            height: 24.px,
            decoration: BoxDecoration(
                color: isSpecking
                    ? const Color.fromRGBO(85, 145, 255, 0.6)
                    : const Color.fromRGBO(85, 145, 255, 1),
                borderRadius: BorderRadius.all(Radius.circular(15.px))),
            child: isSpecking
                ? Icon(
                    icons[_animation?.value ?? 0],
                    color: Colors.white,
                    size: 12.px,
                  )
                : Icon(
                    Icons.volume_up,
                    color: Colors.white,
                    size: 12.px,
                  )));
  }
}
