import 'package:bdh_smart_agric_app/components/bdh_dialog.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

Future<String?> showBatchChooserDialog(BuildContext context) {
  return showDialog<String>(
    context: context,
    barrierDismissible: false,
    builder: (BuildContext dialogContext) {
      return const BatchChooserDialog();
    },
  );
}

//批量处理弹框
class BatchChooserDialog extends StatelessWidget {
  final List<Map<String, String>> batchOptions;

  const BatchChooserDialog(
      {super.key,
      this.batchOptions = const [
        {
          "key": "record",
          "icon": "assets/images/agriculturalrecords/ic_batch_record.png",
          "title": "批量记录",
        },
        {
          "key": "finish",
          "icon": "assets/images/agriculturalrecords/ic_batch_finish.png",
          "title": "批量结束",
        },
      ]});
  @override
  Widget build(BuildContext context) {
    return BdhSimpleAlertDialog(
      scrollable: true,
      backgroundDecoration: const BoxDecoration(),
      contentPadding: const EdgeInsets.all(0),
      content: Container(
          width: 275.px,
          decoration: const BoxDecoration(
              image: DecorationImage(
            image: AssetImage(
                "assets/images/agriculturalrecords/batch_chooser_bg.png"), // 本地图片
            fit: BoxFit.fill,
          )),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(
                children: [
                  const Spacer(),
                  IconButton(
                    icon: const Icon(Icons.clear),
                    iconSize: 20.px,
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  )
                ],
              ),
              Text(
                "请选择",
                strutStyle: StrutStyle(fontSize: 19.px),
                style: TextStyle(
                    color: const Color.fromRGBO(41, 41, 52, 1),
                    fontWeight: FontWeight.w600,
                    fontSize: 19.px),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  ...batchOptions.map((params) {
                    return BatchChooserItem(
                      key: ValueKey(params["key"]),
                      params: params,
                      onClick: (params) {
                        Navigator.of(context).pop(params["key"]);
                      },
                    );
                  })
                ],
              ),
            ],
          )),
    );
  }
}

class BatchChooserItem extends StatelessWidget {
  final Map<String, String> params;

  final ValueChanged<Map<String, String>> onClick;
  const BatchChooserItem(
      {super.key, required this.params, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClick.call(params);
      },
      child: Container(
        width: 100.px,
        height: 100.px,
        margin: EdgeInsets.all(8.px),
        child: Image.asset(params["icon"]!),
      ),
    );
  }
}
