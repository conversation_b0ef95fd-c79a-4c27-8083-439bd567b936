import 'dart:convert';
import 'package:flutter/material.dart';

class Base64Image extends StatelessWidget {
  final String base64;
  final double? width;
  final double? height;

  const Base64Image(this.base64, {super.key, this.width, this.height});

  @override
  Widget build(BuildContext context) {
    try {
      final cleanStr = base64.contains(',') ? base64.split(',')[1] : base64;
      return Image.memory(
        fit: BoxFit.cover,
        base64Decode(cleanStr),
        width: width,
        height: height,
        errorBuilder: (_, __, ___) => Icon(
          Icons.error,
          size: width,
        ),
      );
    } catch (e) {
      return Icon(
        Icons.broken_image,
        size: width,
      );
    }
  }
}
