import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'dart:ui' as ui;

class DottedImage extends ImageProvider<DottedImage> {
  final int size;
  final Color color;
  final int space;

  DottedImage({this.size = 4, this.space = 4, this.color = Colors.blue});
  @override
  Future<DottedImage> obtainKey(ImageConfiguration configuration) {
    return SynchronousFuture<DottedImage>(this);
  }

  @override
  ImageStreamCompleter loadImage(DottedImage key, ImageDecoderCallback decode) {
    return OneFrameImageStreamCompleter(_loadAsync(key));
  }

  ui.Image? image;

  Future<ImageInfo> _loadAsync(DottedImage key) async {
    if (image == null) {
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      canvas.drawCircle(
        Offset(size / 2, (size + space) / 2),
        size / 2,
        Paint()..color = color,
      );

      final picture = recorder.endRecording();
      image = await picture.toImage(size, size + space);
    }

    return ImageInfo(image: image!);
  }
}
