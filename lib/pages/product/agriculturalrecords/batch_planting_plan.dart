import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dio/dio.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:oktoast/oktoast.dart';

import 'model/grow_patterns.dart';
import 'model/land_record.dart';
import 'record_detail_polygon.dart';
import 'request/agricultural_records_service.dart';
import 'web_page.dart';
import 'widget/dotted_image.dart';
import 'widget/speck_widget.dart';

class BatchPlantingPlanPage extends StatefulWidget {
  final Map<String, dynamic> params;
  const BatchPlantingPlanPage({super.key, required this.params});

  @override
  State<BatchPlantingPlanPage> createState() => _PlantingPlanPageState();
}

class _PlantingPlanPageState extends MixinUseState<BatchPlantingPlanPage> {
  late final controller = useController(_Controller(context, widget.params));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "批量农事记录",
        ),
        actions: [
          GestureDetector(
            onTap: controller.onClickArchive,
            child: Text("种植档案",
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                  color: const Color.fromRGBO(44, 44, 44, 1),
                )),
          ),
          SizedBox(
            width: 15.px,
          )
        ],
      ),
      backgroundColor: const Color(0xFFF3F5F9),
      body: UseBuilder((context) {
        var status = controller.loadingStatus.value ?? LoadingStatus.init;
        switch (status) {
          case LoadingStatus.loading:
          case LoadingStatus.init:
          case LoadingStatus.error:
          case LoadingStatus.cancel:
            return _widgetLoading();
          case LoadingStatus.success:
          case LoadingStatus.loadingMore:
          case LoadingStatus.refreshing:
            return _widgetBody();
        }
      }),
    );
  }

  Widget _widgetEmpty() {
    return const Center(
      child: BdhEmptyView(
        tipInfo: "未查到记录",
      ),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    return ListView(
      controller: controller.scrollController,
      children: [_widgetLandRecord(), _widgetPlans()],
    );
  }

  Widget _widgetLandRecordItem(int index) {
    var item = controller.landList[index];
    return SizedBox(
        height: 44.px,
        child: Row(
          children: [
            Container(
                width: 30.px,
                height: 20.px,
                decoration: BoxDecoration(
                  color: const Color(0xFFD9E5FC),
                  borderRadius: BorderRadius.all(Radius.circular(3.px)),
                ),
                child: Center(
                    child: Text(
                  "${index + 1}",
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                      color: const Color(0xFF4587FF), fontSize: 12.px),
                ))),
            SizedBox(
              width: 7.px,
            ),
            Expanded(
                child: Text(
              "${item.landName}(${item.landNo})",
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                  color: const Color.fromRGBO(44, 44, 44, 1), fontSize: 13.px),
            )),
          ],
        ));
  }

  Widget _widgetLandRecord() {
    return UseBuilder((context) {
      bool show = (controller.toggleFlag.value ?? true);
      List<Widget> children = [
        if (show)
          for (var i = 0; i < controller.landList.length; i++)
            _widgetLandRecordItem(i)
      ];

      Widget child = Stack(
        children: [
          Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: Container(
                padding: EdgeInsets.only(
                    left: 7.px, right: 7.px, bottom: 17.px, top: 7.px),
                alignment: Alignment.topCenter,
                margin: EdgeInsets.only(left: 14.px, right: 14.px, top: 14.px),
                decoration: BoxDecoration(
                    color: Colors.white,
                    gradient: const LinearGradient(
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                        colors: [Color(0xFFF5442C), Color(0x77F5442C)]),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8.px),
                        topRight: Radius.circular(8.px))),
                child: Row(
                  children: [
                    Icon(
                      Icons.volume_up,
                      color: Colors.white,
                      size: 12.px,
                    ),
                    SizedBox(
                      width: 4.px,
                    ),
                    Expanded(
                        child: Text(
                      "退出后无法进行批量编辑和批量结束，需重新批量记录。",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(color: Colors.white, fontSize: 12.px),
                    ))
                  ],
                ),
              )),
          Positioned(
              left: 0,
              right: 0,
              top: 30.px,
              bottom: 0,
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.only(
                        left: 14.px, right: 14.px, bottom: 14.px, top: 14.px),
                    margin:
                        EdgeInsets.only(left: 14.px, right: 14.px, top: 14.px),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(8.px)),
                    ),
                    child: Column(
                      children: [
                        SizedBox(
                            height: 40.px,
                            child: Row(
                              children: [
                                Expanded(
                                    child: Text(
                                  "批量记录地块(${controller.landList.length})",
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                      color:
                                          const Color.fromRGBO(44, 44, 44, 1),
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16.px),
                                )),
                                GestureDetector(
                                    onTap: () {
                                      controller.toggleFlag.value = !show;
                                    },
                                    child: Container(
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                                    width: 1.px,
                                                    color: const Color.fromRGBO(
                                                        44, 44, 44, 0.3)))),
                                        child: Text(
                                          show ? "收起" : "展开",
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style: TextStyle(
                                              color: const Color.fromRGBO(
                                                  44, 44, 44, 0.3),
                                              fontSize: 14.px),
                                        )))
                              ],
                            )),
                        ...children
                      ],
                    ),
                  ),
                ],
              ))
        ],
      );

      return SizedBox(
        height: show ? (controller.landList.length * 44.px + 113.px) : 113.px,
        child: child,
      );
    });
  }

  Widget _widgetPlans() {
    return UseBuilder((context) {
      var list = controller.items.value ?? [];
      if (list.isEmpty) {
        return _widgetEmpty();
      }
      return Container(
        margin: EdgeInsets.only(
            left: 14.px, right: 14.px, top: 14.px, bottom: 14.px),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                    child: Text(
                  "待记环节",
                  style: TextStyle(
                      color: const Color.fromRGBO(85, 145, 255, 1),
                      fontWeight: FontWeight.w500,
                      fontSize: 16.px),
                )),
              ],
            ),
            SizedBox(
              height: 14.px,
            ),
            for (int i = 0; i < list.length; i++) _widgetItem(list[i], i)
          ],
        ),
      );
    });
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      width: 347.px,
      child: Column(
        children: children,
      ),
    );
  }

  Widget _widgetItem(GrowPatterns item, int index) {
    bool show = !controller.isChecked(index);
    return Column(
      children: [
        Row(
          children: [
            Container(
              width: 10.px,
              height: 10.px,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: const Color.fromRGBO(85, 145, 255, 0.3),
                  borderRadius: BorderRadius.all(Radius.circular(5.px))),
              child: Container(
                width: 6.px,
                height: 6.px,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: const Color.fromRGBO(85, 145, 255, 1),
                    borderRadius: BorderRadius.all(Radius.circular(3.px))),
              ),
            ),
            SizedBox(
              width: 14.px,
            ),
            Expanded(
                child: Text(
              item.prodProcessName ?? "",
              style: TextStyle(
                  color: const Color.fromRGBO(85, 145, 255, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: 14.px),
            )),
            GestureDetector(
                onTap: () {
                  controller.onItemCheck(index, show);
                },
                child: Container(
                    decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(
                                width: 1.px,
                                color: const Color.fromRGBO(44, 44, 44, 0.3)))),
                    child: Text(
                      show ? "收起" : "展开",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: const Color.fromRGBO(44, 44, 44, 0.3),
                          fontSize: 12.px),
                    )))
          ],
        ),
        Column(
          children: show
              ? item.children
                      ?.map((test) => _widgetSubItem(item, test))
                      .toList() ??
                  []
              : [],
        )
      ],
    );
  }

  Widget _widgetSubItem(GrowPatterns fatherItem, GrowPatterns item) {
    Widget rightWidget = Container(
      margin: EdgeInsets.only(top: 14.px),
      padding: EdgeInsets.all(14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                  child: Text(
                "${item.linkName ?? ""}${item.growthPeriod == null ? "" : "(${item.growthPeriod})"}",
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: const Color.fromRGBO(44, 44, 44, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 14.px),
              )),
              SizedBox(
                width: 7.px,
              ),
              SpeckWidget(text: item.standard),
            ],
          ),
          SizedBox(
            height: 7.px,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                "${item.standard ?? "无"}",
                style: TextStyle(
                    color: const Color.fromRGBO(44, 44, 44, 1),
                    fontWeight: FontWeight.w500,
                    fontSize: 12.px),
              )),
            ],
          ),
          SizedBox(
            height: 4.px,
          ),
          Row(
            children: [
              Expanded(
                  child: Text(
                "${dateFormat(item.planStartDate)}-${dateFormat(item.planEndDate)}",
                style: TextStyle(
                    color: const Color.fromRGBO(44, 44, 44, 0.4),
                    fontWeight: FontWeight.w400,
                    fontSize: 12.px),
              )),
            ],
          ),
          SizedBox(
            height: 7.px,
          ),
          if (item.setFlag != true)
            Row(
              children: [
                const Spacer(),
                BdhTextButton(
                  height: 30.px,
                  width: 100.px,
                  text: '批量记录农事',
                  textFontWeight: FontWeight.w500,
                  textSize: 13.px,
                  borderRadius: BorderRadius.all(Radius.circular(6.px)),
                  backgroundColor: const Color.fromARGB(240, 94, 139, 245),
                  disableBackgroundColor:
                      const Color.fromARGB(255, 224, 223, 223),
                  pressedBackgroundColor:
                      const Color.fromARGB(255, 94, 139, 245),
                  foregroundColor: Colors.white,
                  disableForegroundColor: Colors.white,
                  pressedForegroundColor: Colors.white,
                  onPressed: () {
                    controller.onClickItem(fatherItem, item);
                  },
                )
              ],
            ),
          if (item.setFlag == true)
            Row(
              children: [
                const Spacer(),
                BdhTextButton(
                  height: 30.px,
                  width: 100.px,
                  text: '批量编辑农事',
                  textFontWeight: FontWeight.w500,
                  textSize: 13.px,
                  borderRadius: BorderRadius.all(Radius.circular(6.px)),
                  backgroundColor: Colors.white,
                  disableBackgroundColor: Colors.white,
                  pressedBackgroundColor: Colors.white,
                  side: BorderSide(
                      color: const Color.fromARGB(255, 94, 139, 245),
                      width: 1.px),
                  foregroundColor: const Color.fromARGB(255, 94, 139, 245),
                  disableForegroundColor:
                      const Color.fromARGB(255, 94, 139, 245),
                  pressedForegroundColor:
                      const Color.fromARGB(255, 94, 139, 245),
                  onPressed: () {
                    controller.onClickItemEdit(fatherItem, item);
                  },
                ),
                SizedBox(width: 7.px),
                BdhTextButton(
                  height: 30.px,
                  width: 100.px,
                  text: '批量结束农事',
                  textFontWeight: FontWeight.w500,
                  textSize: 13.px,
                  borderRadius: BorderRadius.all(Radius.circular(6.px)),
                  backgroundColor: const Color.fromRGBO(41, 41, 41, 0.2),
                  disableBackgroundColor: const Color.fromRGBO(41, 41, 41, 0.2),
                  pressedBackgroundColor: const Color.fromRGBO(41, 41, 41, 0.2),
                  foregroundColor: const Color.fromRGBO(41, 41, 41, 0.5),
                  disableForegroundColor: const Color.fromRGBO(41, 41, 41, 0.5),
                  pressedForegroundColor: const Color.fromRGBO(41, 41, 41, 0.5),
                  onPressed: () {
                    controller.onClickItemFinish(fatherItem, item);
                  },
                )
              ],
            ),
        ],
      ),
    );

    return IntrinsicHeight(
        child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
      Container(
          width: 10.px,
          alignment: Alignment.topCenter,
          child: Container(
            width: 2.px,
            padding: EdgeInsets.zero,
            decoration: BoxDecoration(
              image: DecorationImage(
                  fit: BoxFit.contain,
                  image: DottedImage(size: 100, space: 100),
                  repeat: ImageRepeat.repeatY),
            ),
          )),
      SizedBox(
        width: 14.px,
      ),
      Expanded(child: rightWidget)
    ]));
  }

  String dateFormat(String s) {
    return '${s.substring(0, 2)}月${s.substring(2)}日';
  }
}

class _Controller extends UseController
    with MultiCheckUseController<GrowPatterns> {
  final Map<String, dynamic> params;
  late final List<LandRecord> landList;

  _Controller(super.context, this.params) {
    landList = params["landList"];
  }

  late final scrollController = createScrollController();

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);
  late final toggleFlag = use<bool>(true);

  void onInit({bool addPatternsLinkId = true}) {
    var data = {
      "growPatternsId": params["growPatternsId"],
      "orgCode": params["orgName"],
      "statYear": params["statYear"],
      "landNoList": params["landNoList"]
    };
    if (addPatternsLinkId) {
      data["patternsLinkId"] = params["patternsLinkId"];
    }
    loadingStatus.value = LoadingStatus.loading;

    final double offset = scrollController.hasClients
        ? scrollController.position.pixels
        : 0; // 保存当前位置
    AgriculturalRecordsService()
        .queryAllRecordedLink(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (result.success == true && result.code == 0) {
        List<GrowPatterns> loadItems = result.data
                ?.map<GrowPatterns>((v) => GrowPatterns.fromJson(v))
                .toList() ??
            [];

        items.value = loadItems;
        checkedItems.value = [];
        loadingStatus.value = LoadingStatus.success;
        WidgetsBinding.instance.addPostFrameCallback((d) {
          if (scrollController.hasClients) {
            scrollController.jumpTo(offset); // 恢复位置
          }
        });
      }
    }).onError(handleError);
  }

  void setList(List<GrowPatterns> loadItems) {
    loadingStatus.value = LoadingStatus.loading;
    items.value = loadItems;
    checkedItems.value = [];
    loadingStatus.value = LoadingStatus.success;
  }

  void onClickArchive() {
    showLoading(context, content: "加载中..  ");
    AgriculturalRecordsService().encryptQRCodeUrl(
        data: {"statYear": params["statYear"], "plotNo": params["landNo"]},
        cancelToken: createCancelToken()).then((result) {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
      if (result.success == true && result.code == 0) {
        Navigator.of(context)
            .push(CupertinoPageRoute(
                builder: (_) => WebPage(
                      title: '种植档案',
                      url: result.data["url"],
                    )))
            .then((result) {
          if (result != null) {}
        });
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    });
  }

  void onClickItem(GrowPatterns fatherItem, GrowPatterns item) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => RecordDetailPolygonPage(
                  params: {
                    "flag": "1",
                    "type": "modify",
                    "statYear": params["statYear"],
                    "linkCode": item.linkCode,
                    "linkName": item.linkName,
                    "prodProcessName": fatherItem.prodProcessName,
                    "orgCode": params["orgCode"],
                    "orgName": params["orgName"],
                    "landNo": params["landNo"],
                    "landName": params["landName"],
                    "patternsLinkId": item.patternsLinkId,
                    "raiseCrops": params["raiseCrops"],
                    "landArea": params["landArea"],
                    "raiseCropsNm": params["raiseCropsNm"],
                    "raiseCropsVarietyNm": params["raiseCropsVarietyNm"],
                    "agriRecordsId": item.agriRecordsId,
                    "planStartDate": item.planStartDate,
                    "planEndDate": item.planEndDate,
                    "workStationCode": params["workStationCode"],
                    "workStationName": params["workStationName"],
                    "plotList": params["landList"],
                    "isSeedLand": item.isSeedLand, // 是否为秧田 1是0否
                    "isTrace": params["isTrace"],
                    "settingList": params["settingList"],
                  },
                )))
        .then((result) {
      if (result != null) {
        var fatherIndex = fatherItem.children?.indexOf(item);
        if (fatherIndex != null) {
          var index = fatherItem.children?.indexOf(item);
          if (index != null) {
            List<GrowPatterns> list = List.from(items.value ?? []);
            List<GrowPatterns> fatherChildren =
                List.from(fatherItem.children ?? []);
            fatherChildren[index] =
                item.copyWith(setFlag: true, setValue: result);
            var fItem = fatherItem.copyWith(children: fatherChildren);
            list[fatherIndex] = fItem;
            items.value = list;
          }
        }
      }
    });
  }

  void onClickItemEdit(GrowPatterns fatherItem, GrowPatterns item) {
    Navigator.of(context)
        .push(CupertinoPageRoute(
            builder: (_) => RecordDetailPolygonPage(
                  params: {
                    "flag": "1",
                    "type": "edit",
                    "statYear": params["statYear"],
                    "linkCode": item.linkCode,
                    "linkName": item.linkName,
                    "prodProcessName": fatherItem.prodProcessName,
                    "orgCode": params["orgCode"],
                    "orgName": params["orgName"],
                    "landNo": params["landNo"],
                    "landName": params["landName"],
                    "patternsLinkId": item.patternsLinkId,
                    "raiseCrops": params["raiseCrops"],
                    "landArea": params["landArea"],
                    "raiseCropsNm": params["raiseCropsNm"],
                    "raiseCropsVarietyNm": params["raiseCropsVarietyNm"],
                    "agriRecordsId": item.agriRecordsId,
                    "planStartDate": item.planStartDate,
                    "planEndDate": item.planEndDate,
                    "workStationCode": params["workStationCode"],
                    "workStationName": params["workStationName"],
                    "plotList": params["landList"],
                    "isSeedLand": item.isSeedLand, // 是否为秧田 1是0否
                    "isTrace": params["isTrace"],
                    "setValue": item.setValue,
                    "settingList": params["settingList"],
                  },
                )))
        .then((result) {
      if (result != null) {
        var fatherIndex = fatherItem.children?.indexOf(item);
        if (fatherIndex != null) {
          var index = fatherItem.children?.indexOf(item);
          if (index != null) {
            List<GrowPatterns> list = List.from(items.value ?? []);
            List<GrowPatterns> fatherChildren =
                List.from(fatherItem.children ?? []);
            fatherChildren[index] =
                item.copyWith(setFlag: true, setValue: result);
            var fItem = fatherItem.copyWith(children: fatherChildren);
            list[fatherIndex] = fItem;
            items.value = list;
          }
        }
      }
    });
  }

  void onClickItemFinish(GrowPatterns fatherItem, GrowPatterns item) {
    if (item.isBack == '1') {
      showToast('该数据已被退回,请重新编辑后操作！');
      return;
    }
    String? actBeginDate = item.setValue?["actBeginDate"];
    if (actBeginDate == null) {
      return;
    }
    var year = int.parse(actBeginDate.substring(0, 4));
    var month = int.parse(actBeginDate.substring(4, 6));
    var day = int.parse(actBeginDate.substring(6, 8));
    var actBeginTime = DateTime(year, month, day);
    var now = DateTime.now();
    var minDate = DateTime(int.parse(params["statYear"]), 0, 0);
    var maxDate = DateTime.now();
    showLoading(context, content: "加载中..   ");
    Future.wait([
      AgriculturalRecordsService().queryRightfulVerfEnd(data: {
        "orgCode": params["orgCode"], // ---所属单位
        "raiseCrops": params["raiseCrops"], //        ----作物编码
        "linkCodes": [item.linkCode],
        "patternsLinkId": item.patternsLinkId,

        ///  ---方案环节映射id
        "plotNos": params["landNoList"]
      }, cancelToken: createCancelToken()),
      AgriculturalRecordsService().queryRightfulVerf(data: {
        "raiseCrops": params["raiseCrops"],
        "linkCodes": [item.linkCode],
        "patternsLinkId": item.patternsLinkId,
      }, cancelToken: createCancelToken())
    ]).then((result) {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
      if (result[0].code == 0 &&
          result[0].success == true &&
          result[0].data != null &&
          result[1].code == 0 &&
          result[1].success == true &&
          result[1].data != null) {
        String planStartDate = result[0].data[0]["planStartDate"];

        var year = now.year;
        var month = int.parse(planStartDate.substring(0, 2));
        var day = int.parse(planStartDate.substring(2, 4));
        var time = DateTime(year, month, day, 23, 59, 59);
        minDate = (actBeginTime.isAfter(time) ? actBeginTime : time);

        var timeSection = result[1].data;

        if (timeSection != null && timeSection.isNotEmpty) {
          String planStartDate = timeSection[0]["planStartDate"];
          var month = int.parse(planStartDate.substring(0, 2));
          var day = int.parse(planStartDate.substring(2, 4));
          var date1 = DateTime(year, month, day, 0, 0, 0);
          var date2 = DateTime(year, month, day, 23, 59, 59);
          // 获取当天的时间戳
          var timeFlag = now.isAfter(date1) && now.isBefore(date2);
          if (timeFlag) {
            // 获取右区间的时间戳
            maxDate = now; // 在区间内 右区间为当前日期
          } else {
            String planEndDate = timeSection[0]["planEndDate"];
            var month = int.parse(planEndDate.substring(0, 2));
            var day = int.parse(planEndDate.substring(2, 4));
            maxDate = DateTime(int.parse(params["statYear"]), month, day);
          }
        }
        Log.d("${params["statYear"]} ${actBeginTime} ${minDate}  ${maxDate}");
        showDatePicker(
                context: context,
                initialEntryMode: DatePickerEntryMode.calendarOnly,
                firstDate: minDate,
                lastDate: maxDate)
            .then((time) {
          if (!context.mounted) {
            return;
          }
          if (time == null) {
            return;
          }

          showConfirmDialog(context,
                  title: "提示",
                  message: "确认结束${item.prodProcessName}农事?\n结束后不可修改该项农事")
              .then((result) {
            if (!context.mounted) {
              return;
            }

            if (result == true) {
              var data = {
                "actEndDate": DateFormat("yyyyMMdd").format(time),
                "orgCode": params["orgCode"],
                "agriRecordsId": item.agriRecordsId,
                "plotList": params["landList"],
                "patternsLinkId": params["patternsLinkId"],
              };
              showLoading(context, content: "正在提交..   ");
              AgriculturalRecordsService()
                  .recordAllFarmingEnd(
                      data: data, cancelToken: createCancelToken())
                  .then((result) {
                    if (result.code == 0 && result.success == true) {
                      showToast("修改成功");
                      onInit(addPatternsLinkId: false);
                    }
                  })
                  .onError(handleError)
                  .whenComplete(() {
                    if (!context.mounted) {
                      return;
                    }
                    hideLoading(context);
                  });
            }
          });
        });
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    });
  }
}
