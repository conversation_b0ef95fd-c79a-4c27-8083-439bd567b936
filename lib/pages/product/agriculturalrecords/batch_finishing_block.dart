import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_time_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_dropdown_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_org_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_searchable_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/collection_extensions.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:oktoast/oktoast.dart';

import 'model/land_record.dart';
import 'request/agricultural_records_service.dart';

class BatchFinishingBlockPage extends StatefulWidget {
  final Map<String, dynamic> params;
  const BatchFinishingBlockPage({super.key, required this.params});

  @override
  State<BatchFinishingBlockPage> createState() =>
      _BatchFinishingBlockPageState();
}

class _BatchFinishingBlockPageState
    extends MixinUseState<BatchFinishingBlockPage> {
  late final controller = useController(_Controller(context, widget.params));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF3F5F9),
      body: Stack(
        children: [
          Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: Container(
                height: 300.px,
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                      Color.fromARGB(255, 94, 139, 245),
                      Color(0xFFF3F5F9),
                    ])),
              )),
          Column(
            children: [
              AppBar(
                title: const Text(
                  "批量结束",
                  style: TextStyle(color: Colors.white),
                ),
                leading: const BackButton(
                  color: Colors.white,
                ),
                foregroundColor: Colors.white,
                backgroundColor: Colors.transparent,
              ),
              Expanded(child: UseBuilder((context) {
                return UseBuilder((context) {
                  var status =
                      controller.loadingStatus.value ?? LoadingStatus.init;
                  switch (status) {
                    case LoadingStatus.loading:
                    case LoadingStatus.init:
                    case LoadingStatus.error:
                    case LoadingStatus.cancel:
                      return _widgetLoading();
                    case LoadingStatus.success:
                    case LoadingStatus.loadingMore:
                    case LoadingStatus.refreshing:
                      return _widgetBody();
                  }
                });
              }))
            ],
          )
        ],
      ),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    return Column(
      children: [
        widgetContainer(children: [
          UseBuilder(
            (context) => widgetOrgOption(
                title: "所属单位",
                initialValue: controller.chosenOrg.value,
                data: controller.orgDict.value ?? [],
                onChange: (v) {
                  controller.chosenOrg.value = v;
                }),
          ),
          UseBuilder((context) => widgetOption(
              title: "作物",
              initialValue: controller.chosenCorp.value,
              data: controller.corpDict,
              onChange: (v) {
                controller.chosenCorp.value = v;
              })),
          UseBuilder((context) => widgetOption(
              title: "种植方案",
              initialValue: controller.chosenPlantOption.value,
              data: controller.plantOptionDict.value ?? [],
              canShowPicker: () {
                if (controller.chosenOrg.value == null) {
                  showToast("请选择所属单位");
                  return false;
                }
                if (controller.chosenCorp.value == null) {
                  showToast("请选择作物");
                  return false;
                }
                if (controller.plantOptionDict.value?.isEmpty ?? true) {
                  showToast("当前作物无种植方案！");
                  return false;
                }
                return true;
              },
              onChange: (v) {
                controller.chosenPlantOption.value = v;
              })),
          UseBuilder((context) => widgetOption(
              title: "作业环节",
              showBottomLine: true,
              initialValue: controller.chosenLinkOption.value,
              data: controller.linkOptionDict.value ?? [],
              canShowPicker: () {
                if (controller.chosenOrg.value == null) {
                  showToast("请选择所属单位");
                  return false;
                }
                if (controller.chosenCorp.value == null) {
                  showToast("请选择作物");
                  return false;
                }
                if (controller.chosenPlantOption.value == null) {
                  showToast("请选种植方案");
                  return false;
                }
                if (controller.plantOptionDict.value?.isEmpty ?? true) {
                  showToast("当前作物无种植方案！");
                  return false;
                }
                if (controller.linkOptionDict.value?.isEmpty ?? true) {
                  showToast("当前种植方案无作业环节！");
                  return false;
                }

                return true;
              },
              onChange: (v) {
                controller.chosenLinkOption.value = v;
              })),
          UseBuilder((context) => widgetDateOption(
              title: "结束日期",
              showBottomLine: false,
              minimumDate: controller.startLimitDate.value,
              maximumDate: controller.endLimitDate.value,
              initialValue: controller.choseEndDate.value,
              canShowPicker: () {
                if (controller.chosenOrg.value == null) {
                  showToast("请选择所属单位");
                  return false;
                }
                if (controller.chosenCorp.value == null) {
                  showToast("请选择作物");
                  return false;
                }
                if (controller.chosenPlantOption.value == null) {
                  showToast("请选种植方案");
                  return false;
                }
                if (controller.plantOptionDict.value?.isEmpty ?? true) {
                  showToast("当前作物无种植方案！");
                  return false;
                }
                if (controller.linkOptionDict.value?.isEmpty ?? true) {
                  showToast("当前种植方案无作业环节！");
                  return false;
                }

                if (controller.chosenLinkOption.value == null) {
                  showToast("请选择作业环节");
                  return false;
                }

                return true;
              },
              onChange: (v) {
                controller.choseEndDate.value = v;
              })),
        ]),
        Expanded(child: UseBuilder((context) {
          return controller.showContentWidget
              ? _ContentWidget(
                  key: ValueKey(controller.contentWidgetKey),
                  chosenOrg: controller.chosenOrg.value,
                  chosenCorp: controller.chosenCorp.value,
                  chosenPlantOption: controller.chosenPlantOption.value,
                  chosenLinkOption: controller.chosenLinkOption.value,
                  statYear: controller.statYear,
                  raiseCrops: controller.raiseCrops,
                  choseEndDate: controller.choseEndDate.value,
                )
              : const SizedBox.shrink();
        }))
      ],
    );
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      width: 347.px,
      child: Column(
        children: children,
      ),
    );
  }

  Widget widgetHeightSpace({double? height}) {
    return SizedBox(
      height: height ?? 14.px,
    );
  }

  Widget widgetOrgOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      OrgTreeItem? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<OrgTreeItem>? data,
      void Function(OrgTreeItem?)? onChange}) {
    return BdhOrgPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 16.px,
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w600),
      initialValue: initialValue,
      placeholder: placeholder,
      showBottomLine: showBottomLine,
      onChange: onChange,
      checkState: true,
    );
  }

  Widget widgetOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DictNode? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(DictNode?)? onChange}) {
    return BdhSearchableSingleDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      initialValue: initialValue,
      placeholder: placeholder,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 16.px,
          color: const Color.fromRGBO(51, 51, 51, 1),
          fontWeight: FontWeight.w600),
      showBottomLine: showBottomLine,
      onChange: onChange,
      canShowPicker: canShowPicker,
      checkState: true,
    );
  }

  Widget widgetDateOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DateTime? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      bool Function()? canShowPicker,
      DateTime? minimumDate,
      DateTime? maximumDate,
      void Function(DateTime?)? onChange}) {
    return BdhDatePicker(
        item: FormItem(
            title: title, isRequired: isRequired, isCanEdit: isCanEdit),
        initialValue: initialValue,
        placeholder: placeholder,
        textAlign: TextAlign.right,
        titleStyle: TextStyle(
            fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
        placeholderStyle: TextStyle(
            fontSize: 16.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
        textStyle: TextStyle(
            fontSize: 16.px,
            color: const Color.fromRGBO(51, 51, 51, 1),
            fontWeight: FontWeight.w600),
        showBottomLine: showBottomLine,
        onChanged: onChange,
        checkState: true,
        minimumDate: minimumDate,
        maximumDate: maximumDate,
        canShowPicker: canShowPicker);
  }
}

class _Controller extends UseController {
  final Map<String, dynamic> params;
  late final List<DictNode> corpDict;
  late final String? statYear;
  late final String? raiseCrops;
  late final String? raiseText;
  _Controller(super.context, this.params) {
    statYear = params["statYear"];
    raiseCrops = params["raiseCrops"];
    raiseText = params["raiseText"];
    List<DictNode> tmpCorpDict = params["corpDict"] ?? [];
    tmpCorpDict.removeWhere((test) => test.code == "");
    corpDict = tmpCorpDict;
  }

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);

  late final orgDict = use<List<OrgTreeItem>>(null);
  late final plantOptionDict = use<List<DictNode>>(null);
  late final linkOptionDict = use<List<DictNode>>(null);

  late final chosenOrg = use<OrgTreeItem>(null);
  late final startLimitDate = use<DateTime>(null);
  late final endLimitDate = use<DateTime>(null);
  late final choseEndDate = use<DateTime>(null);
  late final chosenCorp = use<DictNode>(null)
    ..onChange = (v) {
      plantOptionDict.value = null;
      linkOptionDict.value = null;
      chosenPlantOption.value = null;
      chosenLinkOption.value = null;
      loadPlantOptionDict();
    };
  late final chosenPlantOption = use<DictNode>(null)
    ..onChange = (v) {
      linkOptionDict.value = null;
      chosenLinkOption.value = null;
      loadLinkOptionDict();
    };
  late final chosenLinkOption = use<DictNode>(null)
    ..onChange = (v) {
      startLimitDate.value = null;
      startLimitDate.value = null;
      choseEndDate.value = null;
      loadDateLimit();
    };

  void loadPlantOptionDict() {
    var data = {
      "statYear": statYear,
      "orgCode": chosenOrg.peek()?.orgCode,
      "raiseCrops": chosenCorp.peek()?.code
    };
    showLoading(context, content: "正在加载..   ");
    AgriculturalRecordsService()
        .queryAllGrow(data: data, cancelToken: createCancelToken())
        .then((result) {
          if (result.success == true &&
              result.code == 0 &&
              result.data != null) {
            plantOptionDict.value = result.data
                    ?.map<DictNode>((v) => DictNode(
                        code: v["growPatternsId"].toString(),
                        name: v["growPatternsName"]))
                    .toList() ??
                [];
          }
        })
        .onError(handleError)
        .whenComplete(() {
          if (!context.mounted) {
            return;
          }
          hideLoading(context);
        });
  }

  void loadLinkOptionDict() {
    var data = {
      "statYear": statYear,
      "orgCode": chosenOrg.peek()?.orgCode,
      "raiseCrops": chosenCorp.peek()?.code,
      'growPatternsId': chosenPlantOption.peek()?.code
    };
    showLoading(context, content: "正在加载..   ");
    AgriculturalRecordsService()
        .queryAllGrowLink(data: data, cancelToken: createCancelToken())
        .then((result) {
          if (result.success == true &&
              result.code == 0 &&
              result.data != null) {
            linkOptionDict.value = result.data
                    ?.map<DictNode>(
                      (v) => DictNode(
                        code: v["patternsLinkId"].toString(),
                        name: v["linkName"],
                        data: v,
                      ),
                    )
                    .toList() ??
                [];
          }
        })
        .onError(handleError)
        .whenComplete(() {
          if (!context.mounted) {
            return;
          }
          hideLoading(context);
        });
  }

  void loadDateLimit() {
    var data = {
      "statYear": statYear,
      "raiseCrops": chosenCorp.peek()?.code,
      'linkCode': chosenLinkOption.peek()?.data["linkCode"],
      "patternsLinkId": chosenLinkOption.peek()?.data["patternsLinkId"],
    };

    showLoading(context, content: "正在加载..   ");
    AgriculturalRecordsService()
        .queryLinkLimit(data: data, cancelToken: createCancelToken())
        .then((result) {
          if (result.success == true &&
              result.code == 0 &&
              result.data != null) {
            var startDate = result.data["startDate"];
            var endDate = result.data["endDate"];

            Log.d("startDate: $startDate,endDate: $endDate");

            startLimitDate.value = DateTime.parse(startDate);
            endLimitDate.value = DateTime.parse(endDate);
          }
        })
        .onError(handleError)
        .whenComplete(() {
          if (!context.mounted) {
            return;
          }
          hideLoading(context);
        });
  }

  void onInit() {
    var userInfo = StorageUtil.userInfo();
    String? tmpOrgCode;
    if (userInfo?.data?.pluginInfo?.orgInfos != null &&
        (userInfo?.data?.pluginInfo?.orgInfos?.isNotEmpty ?? false)) {
      tmpOrgCode = userInfo?.data?.pluginInfo?.orgInfos?[0].orgCode;
    }

    AgriculturalRecordsService()
        .getOrgCode(cancelToken: createCancelToken())
        .then((result) {
      if (result.success == true && result.code == 0 && result.data != null) {
        loadingStatus.value = LoadingStatus.success;
        List<OrgTreeItem> orgDict = result.data
                .map<OrgTreeItem>((v) => OrgTreeItem.fromJson(v))
                .toList() ??
            [];
        chosenOrg.value =
            orgDict.firstWhereOrNull((test) => test.orgCode == tmpOrgCode);

        this.orgDict.value = orgDict;

        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }

  bool get showContentWidget {
    return chosenOrg.value != null &&
        chosenCorp.value != null &&
        chosenPlantOption.value != null &&
        chosenLinkOption.value != null &&
        startLimitDate.value != null &&
        endLimitDate.value != null;
  }

  String get contentWidgetKey {
    return "content-${chosenOrg.value}-${chosenCorp.value}-${chosenPlantOption.value}-${chosenLinkOption.value}";
  }
}

class _ContentWidget extends StatefulWidget {
  final OrgTreeItem? chosenOrg;
  final DictNode? chosenCorp;
  final DictNode? chosenPlantOption;
  final DictNode? chosenLinkOption;
  final String? statYear;
  final String? raiseCrops;
  final DateTime? choseEndDate;
  const _ContentWidget(
      {super.key,
      this.chosenOrg,
      this.chosenCorp,
      this.chosenPlantOption,
      this.chosenLinkOption,
      this.statYear,
      this.raiseCrops,
      this.choseEndDate});

  @override
  State<_ContentWidget> createState() => __ContentWidgetState();
}

class __ContentWidgetState extends MixinUseState<_ContentWidget> {
  late final controller = useController(_ContentController(
      context,
      widget.chosenOrg,
      widget.chosenCorp,
      widget.chosenPlantOption,
      widget.chosenLinkOption,
      widget.statYear,
      widget.raiseCrops));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return UseBuilder((context) {
      return UseBuilder((context) {
        var status = controller.loadingStatus.value ?? LoadingStatus.init;
        switch (status) {
          case LoadingStatus.loading:
          case LoadingStatus.init:
          case LoadingStatus.error:
          case LoadingStatus.cancel:
            return _widgetLoading();
          case LoadingStatus.success:
          case LoadingStatus.loadingMore:
          case LoadingStatus.refreshing:
            if (controller.isEmpty) {
              return _widgetEmpty();
            }
            return _widgetBody();
        }
      });
    });
  }

  Widget _widgetBody() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(
          height: 7.px,
        ),
        _widgetSubMenu(),
        SizedBox(
          height: 7.px,
        ),
        Row(
          children: [
            SizedBox(
              width: 14.px,
            ),
            Expanded(
                child: Text(
              "请选择批量结束的地块",
              style: TextStyle(
                  color: const Color.fromRGBO(44, 44, 44, 1), fontSize: 14.px),
            )),
            Text(
              "已选:${controller.checkedItems.value?.length ?? 0}",
              style: TextStyle(
                  color: const Color.fromARGB(255, 94, 139, 245),
                  fontSize: 14.px),
            ),
            SizedBox(
              width: 14.px,
            ),
          ],
        ),
        Expanded(child: UseBuilder((context) {
          var list = controller.filterItems.value ?? [];
          controller.errorPlotNos.value;
          if (list.isEmpty) {
            return _widgetEmpty();
          }
          return ListView.builder(
            padding: EdgeInsets.zero,
            itemCount: list.length,
            itemBuilder: (context, index) {
              var item = list[index];
              return _widgetItem(index, item);
            },
          );
        })),
        SizedBox(
          height: 14.px,
        ),
        _widgetBottom(),
        SizedBox(
          height: 14.px,
        )
      ],
    );
  }

  Widget _widgetItem(int index, LandRecord item) {
    bool checked = controller.isChecked(index);
    bool errorNoShow = controller.submitError(item.landNo);
    Widget child = Container(
      height: 44.px,
      margin: EdgeInsets.only(left: 14.px, right: 14.px, top: 14.px),
      padding: EdgeInsets.only(left: 7.px, right: 7.px),
      decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(
              width: 1.5.px,
              color: checked
                  ? const Color.fromARGB(255, 94, 139, 245)
                  : Colors.white),
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                  child: Text(
                "${item.landName}(${item.landNo})-${item.landArea}亩",
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: checked
                        ? const Color.fromARGB(255, 94, 139, 245)
                        : const Color.fromRGBO(44, 44, 44, 1),
                    fontSize: 12.5.px),
              )),
              SizedBox(
                width: 7.px,
              ),
              BdhRadio(
                backgroundColor: Colors.transparent,
                selectColor: const Color.fromARGB(255, 94, 139, 245),
                checked: checked,
                iconSize: 16.px,
                onCheckBoxChanged: (c) {
                  controller.onItemCheck(index, !checked);
                },
              ),
            ],
          ),
          if (errorNoShow) ...[
            Text(
              "结束日期应该在开始日期之后",
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: const Color(0xFFDF4057), fontSize: 12.px),
            ),
          ]
        ],
      ),
    );

    return GestureDetector(
      onTap: () {
        controller.onItemCheck(index, !checked);
      },
      child: child,
    );
  }

  Widget _widgetSubMenu() {
    return SizedBox(
        child: Row(
      children: [
        SizedBox(
          width: 14.px,
        ),
        SizedBox(
            width: 70.px,
            child: UseBuilder((context) => BdhDropDownSingleDataPicker(
                  mainAxisAlignment: MainAxisAlignment.start,
                  initialValue: controller.recordOption.value,
                  checkState: true,
                  onChange: (v) {
                    controller.recordOption.value = v;
                  },
                  item: FormItem(
                      title: "记录类型",
                      data: controller.recordOptionDict,
                      isRequired: false),
                ))),
        SizedBox(
          width: 14.px,
        ),
        SizedBox(
            width: 120.px,
            child: UseBuilder((context) => BdhDropDownSingleDataPicker(
                  mainAxisAlignment: MainAxisAlignment.start,
                  initialValue: controller.order.value,
                  checkState: true,
                  onChange: (v) {
                    controller.order.value = v;
                  },
                  item: FormItem(
                      title: "请排序方式",
                      data: controller.orderDict,
                      isRequired: true),
                )))
      ],
    ));
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetEmpty() {
    return const Center(
      child: BdhEmptyView(
        tipInfo: "未查到记录",
      ),
    );
  }

  Widget _widgetBottom() {
    return Row(
      children: [
        SizedBox(
          width: 14.px,
        ),
        BdhRadio(
          iconSize: 16.px,
          backgroundColor: Colors.transparent,
          selectColor: const Color.fromARGB(255, 94, 139, 245),
          checked: controller.checkedAll,
          onCheckBoxChanged: (selected) {
            if (selected == true) {
              controller.checkAll();
            } else {
              controller.uncheckAll();
            }
          },
        ),
        SizedBox(
          width: 3.px,
        ),
        const Text("全选"),
        SizedBox(
          width: 14.px,
        ),
        Expanded(
            child: BdhTextButton(
          height: 34.px,
          text: '确认结束',
          textFontWeight: FontWeight.w500,
          textSize: 13.px,
          borderRadius: BorderRadius.all(Radius.circular(6.px)),
          backgroundColor: const Color.fromARGB(240, 94, 139, 245),
          disableBackgroundColor: const Color.fromARGB(255, 224, 223, 223),
          pressedBackgroundColor: const Color.fromARGB(255, 94, 139, 245),
          foregroundColor: Colors.white,
          disableForegroundColor: Colors.white,
          pressedForegroundColor: Colors.white,
          onPressed: controller.canSubmit ? controller.onSubmit : null,
        )),
        SizedBox(
          width: 14.px,
        ),
      ],
    );
  }
}

class _ContentController extends UseController {
  final OrgTreeItem? chosenOrg;
  final DictNode? chosenCorp;
  final DictNode? chosenPlantOption;
  final DictNode? chosenLinkOption;
  final String? statYear;
  final String? raiseCrops;

  final orderDict = [
    DictNode(code: "", name: "默认排序"),
    DictNode(code: "1", name: "按地块名称排序"),
    DictNode(code: "2", name: "按地块面积排序")
  ];

  final recordOptionDict = [
    DictNode(code: "", name: "全部"),
    DictNode(code: "1", name: "有记录"),
    DictNode(code: "0", name: "无记录")
  ];

  _ContentController(
      super.context,
      this.chosenOrg,
      this.chosenCorp,
      this.chosenPlantOption,
      this.chosenLinkOption,
      this.statYear,
      this.raiseCrops);

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);
  late final order = use<DictNode>(orderDict[0])
    ..onChange = (v) {
      uncheckAll();
      recordOption.value = recordOptionDict[0];
      onInit();
    };
  late final recordOption = use<DictNode>(recordOptionDict[0]);
  late final errorPlotNos = use<List<String>>(null);

  late final items = use<List<LandRecord>>([]);
  late final checkedItems = use<List<LandRecord>>([]);

  late final filterItems = UseCompute<List<LandRecord>>(() {
    var r = recordOption.value;
    if (r?.code == "0") {
      return items.value?.where((test) => test.isHaveRecord != '1').toList();
    } else if (r?.code == "1") {
      return items.value?.where((test) => test.isHaveRecord == '1').toList();
    }
    return items.value;
  });

  bool get checkedAll {
    if (items.value?.isEmpty ?? true) {
      return false;
    }
    var list = filterItems.value ?? [];
    var cItems = checkedItems.value ?? [];

    for (var item in list) {
      if (!cItems.contains(item)) {
        return false;
      }
    }
    return true;
  }

  void checkAll() {
    var list = filterItems.value ?? [];

    var cItems = checkedItems.value ?? [];
    for (var item in list) {
      if (!cItems.contains(item)) {
        cItems.add(item);
      }
    }
    checkedItems.value = cItems;
    checkedItems.forceNotify();
  }

  void uncheckAll() {
    var list = filterItems.value ?? [];

    var cItems = checkedItems.value ?? [];
    for (var item in list) {
      cItems.remove(item);
    }
    checkedItems.value = cItems;
    checkedItems.forceNotify();
  }

  bool isChecked(int index) {
    var cItems = filterItems.value;
    var item = cItems?[index];
    return checkedItems.value?.contains(item) ?? false;
  }

  void onItemCheck(int index, bool checked) {
    var element = filterItems.value?[index];
    if (element == null) {
      return;
    }
    if (checked == true && !(checkedItems.value?.contains(element) ?? false)) {
      checkedItems.value?.add(element);
    } else if (checked == false &&
        (checkedItems.value?.contains(element) ?? false)) {
      checkedItems.value?.remove(element);
    }
    checkedItems.forceNotify();
  }

  void onInit() {
    // 获取地块
    var data = {
      "statYear": statYear,
      "orgCode": chosenOrg?.orgCode,
      "raiseCrops": chosenCorp?.code,
      "growPatternsId": chosenPlantOption?.code,
      "patternsLinkId": chosenLinkOption?.code,
      "outOeder": order.value?.code
    };

    loadingStatus.value = LoadingStatus.loading;
    AgriculturalRecordsService()
        .queryAllNotEndPlot(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (result.success == true && result.code == 0 && result.data != null) {
        List<LandRecord> loadItems = result.data
                .map<LandRecord>((v) => LandRecord.fromJson(v))
                .toList() ??
            [];

        items.value = loadItems;
        checkedItems.value = [];
        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }

  bool get isEmpty {
    return items.value?.isEmpty ?? true;
  }

  bool get canSubmit {
    return checkedItems.value?.isNotEmpty ?? false;
  }

  bool submitError(String? landNo) {
    var list = errorPlotNos.value ?? [];
    return list.contains(landNo);
  }

  void onSubmit() {
    var linkCode = chosenLinkOption?.code;
    if (linkCode == null) {
      return;
    }
    if (checkedItems.value?.isEmpty ?? true) {
      showToast('请至少选择一个地块');
      return;
    }
    var plotList = checkedItems.value
            ?.map<Map<String, dynamic>>((test) => {"landNo": test.landNo})
            .toList() ??
        [];

    var choseEndDate = context
        .findAncestorStateOfType<_BatchFinishingBlockPageState>()
        ?.controller
        .choseEndDate
        .value;

    if (choseEndDate == null) {
      showToast('请选择结束日期');
      return;
    }

    var data = {
      "orgCode": chosenOrg?.orgCode,
      "statYear": statYear,
      "patternsLinkId": chosenLinkOption?.code,
      "plotList": plotList,
      "actEndDate": DateFormat('yyyyMMdd').format(choseEndDate)
    };
    showLoading(context, content: "正在提交..   ");
    AgriculturalRecordsService()
        .batchRecordFarmingEnd(data: data, cancelToken: createCancelToken())
        .then((result) {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
      if (result.success == true && result.code == 0) {
        if (result.data?["errorPlotNos"] != null) {
          errorPlotNos.value = result.data["errorPlotNos"]
              .map<String>((test) => "$test")
              .toList();
          return;
        }
        showToast("提交成功");
        Navigator.of(context).pop(true);
      } else {
        if (result.data?["errorPlotNos"] != null) {
          errorPlotNos.value = result.data["errorPlotNos"];
        }
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    });
  }
}
