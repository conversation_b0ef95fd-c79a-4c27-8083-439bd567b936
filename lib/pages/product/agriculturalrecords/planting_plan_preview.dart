import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'model/grow_patterns.dart';
import 'record_detail_polygon.dart';
import 'request/agricultural_records_service.dart';
import 'web_page.dart';
import 'widget/speck_widget.dart';

class PlantingPlanPreviewPage extends StatefulWidget {
  final Map<String, dynamic> params;
  const PlantingPlanPreviewPage({super.key, required this.params});

  @override
  State<PlantingPlanPreviewPage> createState() => _PlantingPlanPageState();
}

class _PlantingPlanPageState extends MixinUseState<PlantingPlanPreviewPage> {
  late final controller = useController(_Controller(context, widget.params));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          "农事记录",
        ),
        actions: [
          GestureDetector(
            onTap: controller.onClickArchive,
            child: Text("种植档案",
                style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                  color: const Color.fromRGBO(44, 44, 44, 1),
                )),
          ),
          SizedBox(
            width: 15.px,
          )
        ],
      ),
      backgroundColor: const Color(0xFFF3F5F9),
      body: UseBuilder((context) {
        int tabIndex = controller.tabIndex.value ?? 0;

        return Column(
          children: [
            _tabControl(),
            Expanded(
                child: _ContentWidget(
                    key: ValueKey("content-$tabIndex"),
                    params: widget.params,
                    tabIndex: tabIndex))
          ],
        );
      }),
    );
  }

  Widget _tabControl() {
    return UseBuilder((context) {
      return widgetTabControl(
          tabItems: controller.tabs,
          chooseIndex: controller.tabIndex.value ?? 0,
          onItemClick: (index) {
            controller.tabIndex.value = index;
          });
    });
  }

  Widget widgetTabControl(
      {required List<String> tabItems,
      required int chooseIndex,
      required void Function(int) onItemClick}) {
    var children = <Widget>[];

    for (int i = 0; i < tabItems.length; i++) {
      bool choose = i == chooseIndex;
      children.addAll([
        Expanded(
            child: Center(
          child: GestureDetector(
              onTap: () {
                onItemClick.call(i);
              },
              child: SizedBox(
                  child: Column(
                children: [
                  Center(
                      child: Text(tabItems[i],
                          style: TextStyle(
                            fontSize: choose ? 14.px : 14.px,
                            fontWeight:
                                choose ? FontWeight.w600 : FontWeight.w500,
                            color: choose
                                ? const Color(0xff2979ff)
                                : const Color.fromRGBO(41, 41, 52, 0.7),
                          ))),
                  SizedBox(
                    height: 6.px,
                  ),
                  Container(
                    height: 3.px,
                    width: 21.px,
                    decoration: BoxDecoration(
                        color: choose
                            ? const Color(0xff2979ff)
                            : Colors.transparent,
                        borderRadius:
                            BorderRadius.all(Radius.circular(1.5.px))),
                  ),
                  SizedBox(
                    height: 6.px,
                  ),
                ],
              ))),
        ))
      ]);
    }
    return Container(
        color: Colors.white,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 14.px,
            ),
            ...children,
            SizedBox(
              width: 14.px,
            )
          ],
        ));
  }
}

class _Controller extends UseController
    with MultiCheckUseController<GrowPatterns> {
  final Map<String, dynamic> params;

  _Controller(super.context, this.params);

  late final scrollController = createScrollController();

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);
  late final toggleFlag = use<bool>(true);

  late final tabIndex = use<int>(0)
    ..onChange = (v) {
      onInit();
    };
  final tabs = ["未记环节", "已记环节"];

  void onInit() {
    loadingStatus.value = LoadingStatus.success;
  }

  void onClickArchive() {}
}

class _ContentWidget extends StatefulWidget {
  final Map<String, dynamic> params;
  final int tabIndex;
  const _ContentWidget(
      {super.key, required this.params, required this.tabIndex});

  @override
  State<_ContentWidget> createState() => __ContentWidgetState();
}

class __ContentWidgetState extends MixinUseState<_ContentWidget> {
  late final controller = useController(
      _ContentController(context, widget.tabIndex, widget.params));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return UseBuilder((context) {
      var status = controller.loadingStatus.value ?? LoadingStatus.init;

      switch (status) {
        case LoadingStatus.loading:
        case LoadingStatus.init:
        case LoadingStatus.error:
        case LoadingStatus.cancel:
          return _widgetLoading();
        case LoadingStatus.success:
        case LoadingStatus.loadingMore:
        case LoadingStatus.refreshing:
          return _widgetBody();
      }
    });
  }

  Widget _widgetEmpty() {
    return const Center(
      child: BdhEmptyView(
        tipInfo: "未查到记录",
      ),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetBody() {
    return UseBuilder((context) {
      var list = controller.items.value ?? [];
      if (list.isEmpty) {
        return _widgetEmpty();
      }
      return ListView(
        children: [_widgetPlans()],
      );
    });
  }

  Widget _widgetPlans() {
    var list = controller.items.value ?? [];

    return Container(
      margin: EdgeInsets.only(left: 14.px, right: 14.px, top: 14.px),
      child: Column(
        children: [
          for (int i = 0; i < list.length; i++) _widgetItem(list[i], i)
        ],
      ),
    );
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      width: 347.px,
      child: Column(
        children: children,
      ),
    );
  }

  Widget _widgetItem(GrowPatterns item, int index) {
    bool show = !controller.isChecked(index);
    return Column(
      children: [
        Row(
          children: [
            Container(
              width: 10.px,
              height: 10.px,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                  color: const Color.fromRGBO(85, 145, 255, 0.3),
                  borderRadius: BorderRadius.all(Radius.circular(5.px))),
              child: Container(
                width: 6.px,
                height: 6.px,
                alignment: Alignment.center,
                decoration: BoxDecoration(
                    color: const Color.fromRGBO(85, 145, 255, 1),
                    borderRadius: BorderRadius.all(Radius.circular(3.px))),
              ),
            ),
            SizedBox(
              width: 14.px,
            ),
            Expanded(
                child: Text(
              item.prodProcessName ?? "",
              style: TextStyle(
                  color: const Color.fromRGBO(85, 145, 255, 1),
                  fontWeight: FontWeight.w500,
                  fontSize: 14.px),
            )),
            GestureDetector(
                onTap: () {
                  controller.onItemCheck(index, show);
                },
                child: Container(
                    decoration: BoxDecoration(
                        border: Border(
                            bottom: BorderSide(
                                width: 1.px,
                                color: const Color.fromRGBO(44, 44, 44, 0.3)))),
                    child: Text(
                      show ? "收起" : "展开",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: const Color.fromRGBO(44, 44, 44, 0.3),
                          fontSize: 12.px),
                    )))
          ],
        ),
        Column(
          children: show
              ? item.children
                      ?.map((test) => _widgetSubItem(item, test))
                      .toList() ??
                  []
              : [],
        )
      ],
    );
  }

  Widget _widgetSubItem(GrowPatterns fatherItem, GrowPatterns item) {
    return SizedBox(
        height: 172.px,
        child: Row(children: [
          Container(
            width: 10.px,
            alignment: Alignment.center,
            child: Center(
                child: DottedLine(
              lineLength: double.infinity,
              direction: Axis.vertical,
              dashRadius: 1.px,
              dashLength: 2.px,
              dashGapLength: 2.px,
              lineThickness: 2.px,
              dashColor: const Color.fromRGBO(85, 145, 255, 1),
            )),
          ),
          SizedBox(
            width: 14.px,
          ),
          Expanded(
              child: Container(
            margin: EdgeInsets.only(top: 14.px, bottom: 14.px),
            padding: EdgeInsets.all(14.px),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.all(Radius.circular(8.px))),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                        child: Text(
                      "${item.linkName ?? ""}${item.growthPeriod ?? ""}",
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                          color: const Color.fromRGBO(44, 44, 44, 1),
                          fontWeight: FontWeight.w500,
                          fontSize: 14.px),
                    )),
                    SizedBox(
                      width: 7.px,
                    ),
                    SpeckWidget(text: item.standard),
                  ],
                ),
                Expanded(
                  child: Row(
                    children: [
                      Expanded(
                          child: Text(
                        "${item.standard ?? "无"}",
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: const Color.fromRGBO(44, 44, 44, 1),
                            fontWeight: FontWeight.w500,
                            fontSize: 12.px),
                      )),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                        child: Text(
                      "${dateFormat(item.planStartDate)}-${dateFormat(item.planEndDate)}",
                      style: TextStyle(
                          color: const Color.fromRGBO(44, 44, 44, 0.4),
                          fontWeight: FontWeight.w400,
                          fontSize: 12.px),
                    )),
                  ],
                ),
                if (controller.tabIndex == 1)
                  Row(
                    children: [
                      const Spacer(),
                      BdhTextButton(
                        height: 30.px,
                        width: 100.px,
                        text: '查看我的农事',
                        textFontWeight: FontWeight.w500,
                        textSize: 13.px,
                        borderRadius: BorderRadius.all(Radius.circular(6.px)),
                        backgroundColor:
                            const Color.fromARGB(240, 94, 139, 245),
                        disableBackgroundColor:
                            const Color.fromARGB(255, 224, 223, 223),
                        pressedBackgroundColor:
                            const Color.fromARGB(255, 94, 139, 245),
                        foregroundColor: Colors.white,
                        disableForegroundColor: Colors.white,
                        pressedForegroundColor: Colors.white,
                        onPressed: () {
                          controller.onClickItem(fatherItem, item);
                        },
                      )
                    ],
                  ),
              ],
            ),
          ))
        ]));
  }

  String dateFormat(String s) {
    return '${s.substring(0, 2)}月${s.substring(2)}日';
  }
}

class _ContentController extends UseController
    with MultiCheckUseController<GrowPatterns> {
  // "preCurrent": tabIndex,
  // "growPatternsId": item?.growPatternsId,
  // "landNo": item?.landNo,
  // "landName": item?.landName,
  // "raiseCrops": item?.raiseCrops,
  // "orgCode": item?.orgCode,
  // "orgName": item?.orgName,
  // "landArea": item?.landArea,
  // "raiseCropsNm": item?.raiseCropsNm,
  // "raiseCropsVarietyNm": item?.raiseCropsVarietyNm,
  // "statYear": statYear,
  // "workStationCode": item?.workStationCode,
  // "workStationName": item?.workStationName,
  // "isTrace": item?.isTrace ?? 0
  final int tabIndex;
  final Map<String, dynamic> params;

  _ContentController(super.context, this.tabIndex, this.params);

  late final scrollController = createScrollController();

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);
  late final toggleFlag = use<bool>(true);

  void onInit() {
    var data = {
      "growPatternsId": params["growPatternsId"],
      "landNo": params["landNo"],
      "statYear": params["statYear"],
    };
    loadingStatus.value = LoadingStatus.loading;
    late Future res;
    if (tabIndex == 0) {
      res = AgriculturalRecordsService()
          .landNotRecorded(data: data, cancelToken: createCancelToken());
    } else {
      res = AgriculturalRecordsService()
          .landRecorded(data: data, cancelToken: createCancelToken());
    }
    res.then((result) {
      if (result.success == true && result.code == 0 && result.data != null) {
        var loadItems = result.data
                .map<GrowPatterns>((v) => GrowPatterns.fromJson(v))
                .toList() ??
            [];

        items.value = loadItems;
        checkedItems.value = [];
        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }

  void okFun() {}

  void onClickConfirm() {}

  void onClickArchive() {
    showLoading(context, content: "加载中..  ");
    AgriculturalRecordsService().encryptQRCodeUrl(
        data: {"statYear": params["statYear"], "plotNo": params["landNo"]},
        cancelToken: createCancelToken()).then((result) {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
      if (result.success == true && result.code == 0) {
        Navigator.of(context)
            .push(CupertinoPageRoute(
                builder: (_) => WebPage(
                      title: '种植档案',
                      url: result.data["url"],
                    )))
            .then((result) {
          if (result != null) {}
        });
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    });
  }

  void onClickItem(GrowPatterns fatherItem, GrowPatterns item) {
    Navigator.of(context).push(CupertinoPageRoute(
        builder: (_) => RecordDetailPolygonPage(
              params: {
                "flag": "0",
                "type": "read",
                "statYear": params["statYear"],
                "linkCode": item.linkCode,
                "linkName": item.linkName,
                "prodProcessName": fatherItem.prodProcessName,
                "orgCode": params["orgCode"],
                "orgName": params["orgName"],
                "landNo": params["landNo"],
                "landName": params["landName"],
                "patternsLinkId": item.patternsLinkId,
                "raiseCrops": params["raiseCrops"],
                "landArea": params["landArea"],
                "raiseCropsNm": params["raiseCropsNm"],
                "raiseCropsVarietyNm": params["raiseCropsVarietyNm"],
                "agriRecordsId": item.agriRecordsId,
                "planStartDate": item.planStartDate,
                "planEndDate": item.planEndDate,
                "workStationCode": params["workStationCode"],
                "workStationName": params["workStationName"],
                // "plotList": this.landNoList,
                "isSeedLand": item.isSeedLand, // 是否为秧田 1是0否
                "isTrace": params["isTrace"],
                "setValue": item.setValue
                // "settingList": this.settingList
              },
            )));
  }
}
