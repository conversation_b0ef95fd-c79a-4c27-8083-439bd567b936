import 'dart:convert';

import 'package:bdh_smart_agric_app/components/bdh_radio.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_map_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_searchable_multi_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_area_input.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/components/bdh_check.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:latlong2/latlong.dart';
import 'package:oktoast/oktoast.dart';

import 'package:bdh_smart_agric_app/components/bdh_button.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_date_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_media_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_org_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_searchable_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_single_data_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_text_input_small.dart';
import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/dict_tree_model.dart';
import 'package:bdh_smart_agric_app/model/form_item_model.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/utils/auto_dispose_state_extension.dart';
import 'package:bdh_smart_agric_app/utils/collection_extensions.dart';
import 'package:bdh_smart_agric_app/utils/dialog_extensions.dart';
import 'package:bdh_smart_agric_app/utils/flutter_use.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';

import 'model/land_record.dart';
import 'model/means_type.dart';
import 'model/query_element_item.dart';
import 'model/record_farming_element.dart';
import 'request/agricultural_records_service.dart';
import 'share.dart';

class RecordDetailPolygonPage extends StatefulWidget {
  final Map<String, dynamic> params;
  const RecordDetailPolygonPage({super.key, required this.params});

  @override
  State<RecordDetailPolygonPage> createState() => _State();
}

class _State extends MixinUseState<RecordDetailPolygonPage> {
  late final controller = useController(_Controller(context, widget.params));

  @override
  void initState() {
    super.initState();
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF3F5F9),
      body: Stack(
        children: [
          Positioned(
              left: 0,
              right: 0,
              top: 0,
              child: Container(
                height: 300.px,
                decoration: const BoxDecoration(
                    gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                      Color.fromARGB(255, 94, 139, 245),
                      Color(0xFFF3F5F9),
                    ])),
              )),
          Column(
            children: [
              AppBar(
                title: Text(
                  widget.params["type"] == "read"
                      ? "查看我的农事"
                      : widget.params["type"] == "edit"
                          ? "编辑我的农事"
                          : "记录我的农事",
                  style: const TextStyle(color: Colors.white),
                ),
                leading: const BackButton(
                  color: Colors.white,
                ),
                foregroundColor: Colors.white,
                backgroundColor: Colors.transparent,
              ),
              Expanded(child: UseBuilder((context) {
                return UseBuilder((context) {
                  var status =
                      controller.loadingStatus.value ?? LoadingStatus.init;
                  switch (status) {
                    case LoadingStatus.loading:
                    case LoadingStatus.init:
                    case LoadingStatus.error:
                    case LoadingStatus.cancel:
                      return _widgetLoading();
                    case LoadingStatus.success:
                    case LoadingStatus.loadingMore:
                    case LoadingStatus.refreshing:
                      return _widgetBody();
                  }
                });
              }))
            ],
          )
        ],
      ),
    );
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  Widget _widgetHead() {
    return widgetContainer(children: [
      _widgetRow(showBottomLine: false, children: [
        Container(
          color: Colors.blue,
          width: 4.px,
          height: 20.px,
        ),
        SizedBox(
          width: 7.px,
        ),
        Expanded(
            child: Text(
          "种植信息",
          style: TextStyle(
              color: const Color.fromRGBO(44, 44, 44, 1),
              fontWeight: FontWeight.w500,
              fontSize: 16.px),
        )),
      ]),
      if (controller.isBatch) ...[
        widgetKeyValue("生产阶段", widget.params["prodProcessName"]),
        widgetKeyValue("作业环节", widget.params["linkName"],
            showBottomLine: false),
      ],
      if (!controller.isBatch) ...[
        widgetKeyValue("所属单位", widget.params["orgName"] ?? "-"),
        widgetKeyValue("具体地号",
            "${widget.params["landName"] ?? '-'}${widget.params["landNo"] != null ? "(${widget.params["landNo"] ?? "-"})" : ""}"),
        widgetKeyValue2(
            "种植面积",
            widget.params["landArea"] != null
                ? (num.parse(widget.params["landArea"]) > 9999.99
                    ? "${(num.parse(widget.params["landArea"]) / 10000)}万亩"
                    : "${num.parse(widget.params["landArea"])}亩")
                : '-',
            "种植作物",
            widget.params["raiseCropsNm"] ?? "-"),
        widgetKeyValue2("品种名称", widget.params["raiseCropsVarietyNm"] ?? "-",
            "生产阶段", widget.params["prodProcessName"] ?? "-"),
        widgetKeyValue("作业环节", widget.params["linkName"] ?? "-",
            showBottomLine: false),
      ]
    ]);
  }

  Widget _widgetForm1() {
    return widgetContainer(children: [
      UseBuilder((context) {
        return widgetDateOption(
            title: "开始日期",
            placeholder: "请选择开始日期",
            isRequired: true,
            canShowPicker: () => controller.canEdit,
            initialValue: controller.actBeginDate.value,
            minimumDate: controller.minDate.value,
            maximumDate: controller.maxDate.value,
            onChange: (v) {
              controller.actBeginDate.value = v;
            });
      }),
      _widgetRow(showBottomLine: false, children: [
        Expanded(
            child: Text(
          "经纬度",
          textAlign: TextAlign.left,
          style: TextStyle(
              fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
        )),
        SizedBox(
          width: 7.px,
        ),
        if (controller.canEdit)
          GestureDetector(
              onTap: controller.geLatLng,
              child: Text(
                "获取",
                textAlign: TextAlign.left,
                style: TextStyle(
                    fontSize: 14.px,
                    color: const Color.fromARGB(240, 94, 139, 245),
                    fontWeight: FontWeight.w500),
              ))
      ]),
      Container(
          padding: EdgeInsets.only(bottom: 14.px),
          decoration: BoxDecoration(
              border: controller.isSeedLand || controller.showElementList
                  ? Border(
                      bottom: BorderSide(
                          color: const Color.fromRGBO(226, 235, 231, 0.6),
                          width: 1.px))
                  : null),
          child: Container(
            padding: EdgeInsets.only(left: 7.px, right: 7.px),
            height: 30.px,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                color: const Color.fromRGBO(44, 44, 44, 0.1),
                border: Border(
                    bottom: BorderSide(
                        color: const Color.fromRGBO(226, 235, 231, 0.6),
                        width: 1.px)),
                borderRadius: BorderRadius.all(Radius.circular(8.px))),
            child: Row(
              children: [
                Expanded(
                    child: Row(
                  children: [
                    Text(
                      "经度",
                      style: TextStyle(
                          fontSize: 14.px,
                          color: const Color.fromRGBO(41, 41, 41, 1)),
                    ),
                    SizedBox(width: 4.px),
                    Expanded(child: UseBuilder((context) {
                      return Text(
                        controller.longitude.value ?? "-",
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.fade,
                        style: TextStyle(
                            fontSize: 14.px,
                            color: const Color.fromRGBO(41, 41, 41, 1)),
                      );
                    }))
                  ],
                )),
                SizedBox(width: 4.px),
                Expanded(
                    child: Row(
                  children: [
                    Text(
                      "纬度",
                      strutStyle: StrutStyle(
                        fontSize: 14.px,
                      ),
                      style: TextStyle(
                          fontSize: 14.px,
                          color: const Color.fromRGBO(41, 41, 41, 1)),
                    ),
                    SizedBox(width: 4.px),
                    Expanded(child: UseBuilder((context) {
                      return Text(
                        controller.latitude.value ?? "-",
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.fade,
                        strutStyle: StrutStyle(
                          fontSize: 14.px,
                        ),
                        style: TextStyle(
                            fontSize: 14.px,
                            color: const Color.fromRGBO(41, 41, 41, 1)),
                      );
                    }))
                  ],
                ))
              ],
            ),
          )),
      if (!controller.isSeedLand)
        UseBuilder((context) => widgetOption(
            title: "作业面积",
            isRequired: true,
            showBottomLine:
                controller.showWorkTypeInput || controller.showElementList,
            initialValue: controller.workType.value,
            data: controller.workTypeDict,
            canShowPicker: () => controller.canEdit,
            onChange: (v) {
              controller.workType.value = v;
            })),
      UseBuilder((context) {
        var workAreaValue = controller.workArea.value;
        return controller.showWorkTypeInput
            ? widgetInput(
                title: "",
                initialValue: workAreaValue,
                isCanEdit: controller.canEdit,
                placeholder: "请输入实际作业面积",
                showBottomLine: controller.showElementList,
                onChange: (v) {
                  controller.workArea.value = v;
                })
            : const SizedBox.shrink();
      }),
      UseBuilder((context) {
        var list = controller.formList.value ?? [];
        if (list.isEmpty) return const SizedBox.shrink();

        var children = <Widget>[];

        for (int i = 0; i < list.length; i++) {
          var test = list[i];
          Widget? child;
          bool showBottomLine = i != list.length - 1;
          if (test.isInput) {
            child = widgetInput(
                title: test.otherOV.elementName ?? '',
                initialValue: test.value,
                isCanEdit: controller.canEdit,
                maxLength: test.otherOV.elementLength,
                placeholder: test.otherOV.elementPromptInfo ?? "请输入",
                showBottomLine: showBottomLine,
                isRequired: test.otherOV.isNotNull == '1',
                unit: test.otherOV.elementUnit,
                onChange: (v) {
                  test.value = v;
                  controller.formList.forceNotify();
                });
          } else if (test.isInputDateTime) {
            child = widgetDateOption(
                title: test.otherOV.elementName ?? '',
                placeholder: test.otherOV.elementPromptInfo ?? "请选择",
                isRequired: test.otherOV.isNotNull == '1',
                canShowPicker: () => controller.canEdit,
                showBottomLine: showBottomLine,
                initialValue: test.value,
                onChange: (v) {
                  test.value = v;
                  controller.formList.forceNotify();
                });
          } else if (test.isSingleSelect) {
            child = widgetOption(
                title: test.otherOV.elementName ?? '',
                placeholder: test.otherOV.elementPromptInfo ?? "请选择",
                isRequired: test.otherOV.isNotNull == '1',
                data: test.dict,
                canShowPicker: () => controller.canEdit,
                showBottomLine: showBottomLine,
                initialValue: test.value,
                onChange: (v) {
                  test.value = v;
                  controller.formList.forceNotify();
                });
          } else if (test.isMultiSelect) {
            child = widgetMultiOption(
                title: test.otherOV.elementName ?? '',
                placeholder: test.otherOV.elementPromptInfo ?? "请选择",
                isRequired: test.otherOV.isNotNull == '1',
                data: test.dict,
                canShowPicker: () => controller.canEdit,
                showBottomLine: showBottomLine,
                initialValue: test.value,
                onChange: (v) {
                  test.value = v;
                  controller.formList.forceNotify();
                });
          } else if (test.isImage) {
            child = BdhMediaPicker(
              maxCount: 3,
              fontSize: 14.px,
              titleStyle: TextStyle(
                  fontSize: 14.px,
                  color: const Color.fromRGBO(51, 51, 51, 0.4)),
              initialValue: test.value
                      ?.map<MediaItem>(
                          (test) => MediaItem(url: test, type: MediaType.image))
                      .toList() ??
                  [],
              allowImage: true,
              allowVideo: false,
              showBottomLine: showBottomLine,
              item: FormItem(
                  title: test.otherOV.elementName == null
                      ? ''
                      : "${test.otherOV.elementName}(至少1张，最多3张)",
                  isRequired: test.otherOV.isNotNull == '1',
                  isCanEdit: controller.canEdit),
              showTitle: true,
              onPickerCallback: fellFarmMediaPickerCallback,
              onChange: (List<String>? fileList) {
                test.value = fileList;
                controller.formList.forceNotify();
              },
            );
          } else if (test.isVideo) {
            child = BdhMediaPicker(
              maxCount: 3,
              fontSize: 16.px,
              initialValue: test.value
                      ?.map<MediaItem>((test) => MediaItem(
                            url: test,
                            type: MediaType.video,
                          ))
                      .toList() ??
                  [],
              allowImage: false,
              allowVideo: true,
              onPickerCallback: fellFarmMediaPickerCallback,
              showBottomLine: showBottomLine,
              item: FormItem(
                  title: test.otherOV.elementName == null
                      ? ''
                      : "${test.otherOV.elementName}(至少1段，最多3段，横版)",
                  isRequired: test.otherOV.isNotNull == '1',
                  isCanEdit: controller.canEdit),
              showTitle: true,
              onChange: (List<String>? fileList) {
                controller.workVideoUrlList.value = fileList;
              },
            );
          } else {
            child = Text(test.otherOV.elementName ?? "-");
          }
          children.add(child);
        }

        return Column(children: children);
      })
    ]);
  }

  Widget _widgetForm2() {
    return widgetContainer(children: [
      UseBuilder((context) {
        return BdhTextAreaInput(
          item: FormItem(
              title: "农事描述", isRequired: false, isCanEdit: controller.canEdit),
          initialValue: controller.agriRecordsDesc.value,
          onChanged: (v) {
            controller.agriRecordsDesc.value = v;
          },
          lines: 4,
          maxLength: 200,
          showBottomLine: false,
        );
      }),
      UseBuilder((context) {
        return BdhMediaPicker(
          maxCount: 3,
          fontSize: 16.px,
          initialValue: controller.workPicUrlList.value
                  ?.map<MediaItem>(
                      (test) => MediaItem(url: test, type: MediaType.image))
                  .toList() ??
              [],
          allowImage: true,
          allowVideo: false,
          item: FormItem(
              title: "作业照片(选填,最多3张)",
              isRequired: false,
              isCanEdit: controller.canEdit),
          showTitle: true,
          onPickerCallback: fellFarmMediaPickerCallback,
          onChange: (List<String>? fileList) {
            controller.workPicUrlList.value = fileList;
          },
        );
      }),
      UseBuilder((context) {
        return BdhMediaPicker(
          maxCount: 3,
          fontSize: 16.px,
          initialValue: controller.workVideoUrlList.value
                  ?.map<MediaItem>((test) => MediaItem(
                        url: test,
                        type: MediaType.video,
                      ))
                  .toList() ??
              [],
          allowImage: false,
          allowVideo: true,
          onPickerCallback: fellFarmMediaPickerCallback,
          showBottomLine: false,
          item: FormItem(
              title: "作业视频(选填,最多3段,横版)",
              isRequired: false,
              isCanEdit: controller.canEdit),
          showTitle: true,
          onChange: (List<String>? fileList) {
            controller.workVideoUrlList.value = fileList;
          },
        );
      }),
    ]);
  }

  Widget _widgetForm3() {
    return Padding(
        padding: EdgeInsets.only(left: 14.px, right: 14.px),
        child: BdhTextButton(
          height: 40.px,
          width: 351.px,
          text: '新增投入品',
          textFontWeight: FontWeight.w500,
          textSize: 15.px,
          borderRadius: BorderRadius.all(Radius.circular(6.px)),
          backgroundColor: Colors.white,
          disableBackgroundColor: Colors.white,
          pressedBackgroundColor: const Color.fromRGBO(41, 41, 41, 0.1),
          foregroundColor: const Color.fromARGB(255, 94, 139, 245),
          disableForegroundColor: const Color.fromARGB(255, 94, 139, 245),
          pressedForegroundColor: const Color.fromARGB(255, 94, 139, 245),
          side: BorderSide(
              color: const Color.fromRGBO(41, 41, 41, 0.2), width: 1.px),
          pressedSide: BorderSide(
              color: const Color.fromRGBO(41, 41, 41, 0.2), width: 1.px),
          onPressed: () {
            controller.addNewAgriRecordsInput();
          },
        ));
  }

  Widget _widgetForm3Item(int index, AgriRecordsInputMapper item) {
    return widgetContainer(children: [
      _widgetRow(showBottomLine: false, children: [
        Container(
          color: Colors.blue,
          width: 4.px,
          height: 20.px,
        ),
        SizedBox(
          width: 7.px,
        ),
        Expanded(
            child: Text(
          "投入品${index + 1}",
          style: TextStyle(
              color: const Color.fromRGBO(44, 44, 44, 1),
              fontWeight: FontWeight.w500,
              fontSize: 16.px),
        )),
        if (controller.canEdit)
          GestureDetector(
              onTap: () {
                controller.removeNewAgriRecordsInput(item);
              },
              child: Row(
                children: [
                  Icon(
                    Icons.delete,
                    color: Colors.red,
                    size: 16.px,
                  ),
                  Text(
                    "删除投入品${index + 1}",
                    textAlign: TextAlign.left,
                    style: TextStyle(
                        fontSize: 14.px,
                        color: Colors.red,
                        fontWeight: FontWeight.w500),
                  )
                ],
              ))
      ]),
      widgetOption(
          title: "类型",
          placeholder: "请选择类型",
          initialValue: item.meansProdType,
          data: controller.meansProdTypeOption.value ?? [],
          canShowPicker: () => controller.canEdit,
          onChange: (v) {
            item.meansProdType = v;
            item.meansProd = null;
            var meansUnitDict = controller.getMeansUnitDict(item);
            //  如果选项里只有1项则直接选中
            if (meansUnitDict.length == 1) {
              item.meansProdUnit = meansUnitDict[0];
            } else {
              item.meansProdUnit = null;
            }

            controller.asAgriRecordsInputsList.forceNotify();
          }),
      if (item.meansProdType?.code != '2')
        widgetSearchableOption(
            title: "名称",
            placeholder: "请选择名称",
            canShowPicker: () {
              if (controller.canEdit) {
                if (item.meansProdType == null) {
                  showToast("请先选择类型");
                  return false;
                }
              }

              return controller.canEdit;
            },
            pickerItemBuilder: (context, index, dictNode, checked, setChecked) {
              return Container(
                  margin: EdgeInsets.only(left: 7.px, right: 7.px),
                  padding: EdgeInsets.all(7.px),
                  decoration: BoxDecoration(
                      color: checked
                          ? const Color.fromRGBO(44, 44, 44, 0.05)
                          : Colors.transparent,
                      border: Border.all(
                          width: 1.5.px,
                          color: checked
                              ? const Color.fromARGB(255, 94, 139, 245)
                              : Colors.transparent),
                      borderRadius: BorderRadius.all(Radius.circular(8.px))),
                  child: GestureDetector(
                      onTap: () {
                        setChecked(dictNode, !checked);
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                              height: 20.px,
                              padding: EdgeInsets.only(left: 5.px, right: 5.px),
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color:
                                      const Color.fromARGB(255, 94, 139, 245),
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(4.px))),
                              child: Text(
                                dictNode.data.pesticideTypeName ?? "",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10.px,
                                    fontWeight: FontWeight.w500),
                              )),
                          SizedBox(
                            width: 5.px,
                          ),
                          Expanded(
                              child: Text(
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            "${dictNode.name ?? ""}${dictNode.data.content != null ? "(${dictNode.data.content})" : ""}${dictNode.data.manufacturer != null ? "${dictNode.data.manufacturer}" : ""}",
                            style: TextStyle(
                                color: const Color.fromRGBO(51, 51, 51, 1),
                                fontSize: 14.px,
                                fontWeight: FontWeight.w500),
                          )),
                          BdhRadio(
                            backgroundColor: Colors.transparent,
                            selectColor:
                                const Color.fromARGB(255, 94, 139, 245),
                            checked: checked,
                            iconSize: 16.px,
                            onCheckBoxChanged: (c) {
                              setChecked(dictNode, c);
                            },
                          ),
                          SizedBox(
                            width: 7.px,
                          ),
                        ],
                      )));
            },
            initialValue: item.meansProd,
            data: controller.getMeansProdDict(item),
            onChange: (v) {
              item.meansProd = v;
              item.minValue = item.meansProd?.data.minValue;
              item.maxValue = item.meansProd?.data.maxValue;
              Log.d("max 2 is ${item.minValue}  ${item.maxValue}");
              var meansUnitDict = controller.getMeansUnitDict(item);
              if (meansUnitDict.length == 1) {
                item.meansProdUnit = meansUnitDict[0];
              } else {
                item.meansProdUnit = null;
              }

              controller.asAgriRecordsInputsList.forceNotify();
            }),
      widgetInput(
          title: "使用量",
          initialValue: item.totalDosage,
          isCanEdit: controller.canEdit,
          onChange: (v) {
            item.totalDosage = v;
            controller.asAgriRecordsInputsList.forceNotify();
          },
          showArrow: false,
          rightWidgetBuilder: (context, state) {
            return Container(
                padding: EdgeInsets.only(right: 7.px, left: 2.px),
                margin: EdgeInsets.only(left: 7.px),
                alignment: Alignment.center,
                width: 90.px,
                decoration: BoxDecoration(
                    color: const Color.fromRGBO(51, 51, 51, 0.1),
                    borderRadius: BorderRadius.all(Radius.circular(8.px))),
                child: BdhSingleDataPicker(
                  item: FormItem(
                      title: "",
                      isRequired: false,
                      isCanEdit: true,
                      data: controller.getMeansUnitDict(item)),
                  minHeight: 30.px,
                  showArrow: true,
                  valueSpace: 0,
                  initialValue: item.meansProdUnit,
                  placeholder: "选择单位",
                  textAlign: TextAlign.center,
                  titleStyle: TextStyle(
                      fontSize: 14.px,
                      color: const Color.fromRGBO(51, 51, 51, 0.4)),
                  placeholderStyle: TextStyle(
                      fontSize: 12.px,
                      color: const Color.fromRGBO(51, 51, 51, 0.4)),
                  textStyle: TextStyle(
                      fontSize: 14.px,
                      color: const Color.fromRGBO(51, 51, 51, 1)),
                  showBottomLine: false,
                  onChange: (v) {
                    item.meansProdUnit = v;
                    controller.asAgriRecordsInputsList.forceNotify();
                  },
                  canShowPicker: () {
                    controller._unfocus();
                    return controller.canEdit;
                  },
                  checkState: true,
                ));
          }),
      BdhMediaPicker(
        maxCount: 3,
        fontSize: 16.px,
        initialValue: controller.urlsToMediaItemList(item.meansPicUrl1list),
        allowImage: true,
        allowVideo: false,
        checkState: true,
        item: FormItem(
            title: "说明书照片(选填,最多3张)",
            isRequired: false,
            isCanEdit: controller.canEdit),
        showTitle: true,
        onPickerCallback: fellFarmMediaPickerCallback,
        onChange: (List<String>? fileList) {
          item.meansPicUrl1list = fileList;
          controller.asAgriRecordsInputsList.forceNotify();
        },
      ),
      BdhMediaPicker(
        maxCount: 3,
        fontSize: 16.px,
        initialValue: controller.urlsToMediaItemList(item.meansPicUrl2list),
        allowImage: true,
        allowVideo: false,
        checkState: true,
        item: FormItem(
            title: "商品名照片(选填,最多3张)",
            isRequired: false,
            isCanEdit: controller.canEdit),
        showTitle: true,
        onPickerCallback: fellFarmMediaPickerCallback,
        onChange: (List<String>? fileList) {
          item.meansPicUrl2list = fileList;
          controller.asAgriRecordsInputsList.forceNotify();
        },
      ),
      BdhMediaPicker(
        maxCount: 3,
        fontSize: 16.px,
        initialValue: controller.urlsToMediaItemList(item.meansPicUrl3list),
        allowImage: true,
        allowVideo: false,
        checkState: true,
        showBottomLine: false,
        item: FormItem(
            title: "实物照片(选填,最多3张)",
            isRequired: false,
            isCanEdit: controller.canEdit),
        showTitle: true,
        onPickerCallback: fellFarmMediaPickerCallback,
        onChange: (List<String>? fileList) {
          item.meansPicUrl3list = fileList;
          controller.asAgriRecordsInputsList.forceNotify();
        },
      ),
    ]);
  }

  Widget _widgetRow(
      {required List<Widget> children, bool showBottomLine = true}) {
    return Container(
        height: 44.px,
        decoration: BoxDecoration(
          border: showBottomLine
              ? Border(
                  bottom: BorderSide(
                      color: const Color.fromRGBO(226, 235, 231, 0.6),
                      width: 1.px))
              : null,
        ),
        child: Row(
          children: children,
        ));
  }

  Widget widgetKeyValue(String title, String value,
      {bool showBottomLine = true}) {
    return _widgetRow(showBottomLine: showBottomLine, children: [
      SizedBox(
          width: 70.px,
          child: Text(
            title,
            textAlign: TextAlign.left,
            style: TextStyle(
                fontSize: 14.px, color: const Color.fromRGBO(41, 41, 41, 0.5)),
          )),
      Expanded(
          child: Text(
        value,
        textAlign: TextAlign.left,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(41, 41, 41, 1)),
      ))
    ]);
  }

  Widget widgetKeyValue2(
      String title, String value, String title2, String value2,
      {bool showBottomLine = true}) {
    return _widgetRow(showBottomLine: showBottomLine, children: [
      Expanded(
          child: Row(
        children: [
          SizedBox(
            width: 70.px,
            child: Text(
              title,
              textAlign: TextAlign.left,
              style: TextStyle(
                  fontSize: 14.px,
                  color: const Color.fromRGBO(41, 41, 41, 0.5)),
            ),
          ),
          Expanded(
              child: Text(
            value,
            textAlign: TextAlign.left,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: 14.px, color: const Color.fromRGBO(41, 41, 41, 1)),
          ))
        ],
      )),
      Expanded(
          child: Row(
        children: [
          SizedBox(
            width: 70.px,
            child: Text(
              title2,
              textAlign: TextAlign.left,
              style: TextStyle(
                  fontSize: 14.px,
                  color: const Color.fromRGBO(41, 41, 41, 0.5)),
            ),
          ),
          Expanded(
              child: Text(
            value2,
            textAlign: TextAlign.left,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: 14.px, color: const Color.fromRGBO(41, 41, 41, 1)),
          ))
        ],
      ))
    ]);
  }

  Widget _widgetBody() {
    return Column(
      children: [
        Expanded(child: UseBuilder((context) {
          var list = controller.asAgriRecordsInputsList.value ?? [];
          return CustomScrollView(
            slivers: [
              SliverToBoxAdapter(
                child: _widgetHead(),
              ),
              SliverToBoxAdapter(
                child: SizedBox(
                  height: 14.px,
                ),
              ),
              SliverToBoxAdapter(
                child: _widgetForm1(),
              ),
              SliverToBoxAdapter(
                child: SizedBox(
                  height: 14.px,
                ),
              ),
              SliverToBoxAdapter(
                child: _widgetForm2(),
              ),
              if (controller.canEdit) ...[
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 14.px,
                  ),
                ),
                SliverToBoxAdapter(
                  child: _widgetForm3(),
                ),
              ],
              if (list.isNotEmpty) ...[
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 14.px,
                  ),
                ),
                SliverList.separated(
                  itemCount: list.length,
                  itemBuilder: ((context, index) {
                    return _widgetForm3Item(index, list[index]);
                  }),
                  separatorBuilder: (BuildContext context, int index) {
                    return SizedBox(
                      height: 14.px,
                    );
                  },
                ),
                SliverToBoxAdapter(
                  child: SizedBox(
                    height: 14.px,
                  ),
                ),
              ]
            ],
          );
        })),
        if (controller.canEdit) ...[
          SizedBox(
            height: 14.px,
          ),
          _widgetBottom(),
          SizedBox(
            height: 14.px,
          ),
        ]
      ],
    );
  }

  Widget _widgetBottom() {
    return BdhTextButton(
      height: 40.px,
      width: 351.px,
      text: '确认提交',
      textFontWeight: FontWeight.w500,
      textSize: 15.px,
      borderRadius: BorderRadius.all(Radius.circular(6.px)),
      backgroundColor: const Color.fromARGB(240, 94, 139, 245),
      disableBackgroundColor: const Color.fromARGB(255, 224, 223, 223),
      pressedBackgroundColor: const Color.fromARGB(255, 94, 139, 245),
      foregroundColor: Colors.white,
      disableForegroundColor: Colors.white,
      pressedForegroundColor: Colors.white,
      onPressed: controller.submitDisable ? null : controller.onSubmit,
    );
  }

  Widget widgetContainer({Key? key, required List<Widget> children}) {
    return Container(
      key: key,
      margin: EdgeInsets.only(left: 14.px, right: 14.px),
      padding: EdgeInsets.only(left: 14.px, right: 14.px),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(8.px))),
      width: 347.px,
      child: Column(
        children: children,
      ),
    );
  }

  Widget widgetHeightSpace({double? height}) {
    return SizedBox(
      height: height ?? 14.px,
    );
  }

  Widget widgetOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DictNode? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(DictNode?)? onChange,
      bool showArrow = true,
      Widget Function(BuildContext, FormFieldState<DictNode>)? suffixBuilder}) {
    return BdhSingleDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      minHeight: 44.px,
      showArrow: showArrow,
      initialValue: initialValue,
      placeholder: placeholder,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange != null
          ? (v) {
              controller._unfocus();
              onChange.call(v);
            }
          : null,
      canShowPicker: canShowPicker != null
          ? () {
              controller._unfocus();
              return canShowPicker.call();
            }
          : null,
      suffixBuilder: suffixBuilder,
      checkState: true,
    );
  }

  Widget widgetMultiOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      List<DictNode>? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(List<DictNode>?)? onChange,
      bool showArrow = true,
      Widget Function(BuildContext, FormFieldState<List<DictNode>>)?
          suffixBuilder}) {
    return BdhSearchableMultiDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      minHeight: 44.px,
      showArrow: showArrow,
      initialValue: initialValue,
      placeholder: placeholder,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange != null
          ? (v) {
              controller._unfocus();
              onChange.call(v);
            }
          : null,
      canShowPicker: canShowPicker != null
          ? () {
              controller._unfocus();
              return canShowPicker.call();
            }
          : null,
      suffixBuilder: suffixBuilder,
      checkState: true,
    );
  }

  Widget widgetSearchableOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DictNode? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      List<DictNode>? data,
      bool Function()? canShowPicker,
      void Function(DictNode?)? onChange,
      bool showArrow = true,
      Widget Function(BuildContext, FormFieldState<DictNode>)? suffixBuilder,
      final Widget Function(BuildContext, int, DictNode, bool,
              void Function(DictNode item, bool checked))?
          pickerItemBuilder}) {
    return BdhSearchableSingleDataPicker(
      item: FormItem(
          title: title,
          isRequired: isRequired,
          isCanEdit: isCanEdit,
          data: data),
      minHeight: 44.px,
      maxHeight: 400.px,
      showArrow: showArrow,
      initialValue: initialValue,
      placeholder: placeholder,
      diameterRatio: 100,
      squeeze: 1,
      itemExtent: 40.px,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange != null
          ? (v) {
              controller._unfocus();
              onChange.call(v);
            }
          : null,
      canShowPicker: canShowPicker != null
          ? () {
              controller._unfocus();
              return canShowPicker.call();
            }
          : null,
      suffixBuilder: suffixBuilder,
      pickerItemBuilder: pickerItemBuilder,
      checkState: true,
    );
  }

  Widget widgetInput(
      {required String title,
      String placeholder = "请输入",
      String? unit,
      String? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      bool Function()? canShowPicker,
      void Function(String?)? onChange,
      bool showArrow = true,
      int? maxLength,
      Widget Function(BuildContext, FormFieldState<String>)?
          rightWidgetBuilder}) {
    return BdhTextInputSmall(
      item:
          FormItem(title: title, isRequired: isRequired, isCanEdit: isCanEdit),
      minHeight: 44.px,
      initialValue: initialValue,
      placeHolder: placeholder,
      textInputType: const TextInputType.numberWithOptions(decimal: true),
      fontSize: 14.px,
      textAlign: TextAlign.right,
      titleStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      placeholderStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
      textStyle: TextStyle(
          fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
      showBottomLine: showBottomLine,
      onChange: onChange,
      maxLength: maxLength,
      rightWidgetBuilder: rightWidgetBuilder ??
          (unit != null
              ? (context, state) {
                  return Padding(
                      padding: EdgeInsets.only(left: 6.px),
                      child: Text(unit,
                          style: TextStyle(
                            fontSize: 14.px,
                            fontWeight: FontWeight.w400,
                            color: const Color.fromRGBO(44, 44, 52, 1),
                          )));
                }
              : null),
    );
  }

  Widget widgetDateOption(
      {required String title,
      String placeholder = "请选择",
      String? unit,
      DateTime? initialValue,
      bool isRequired = false,
      bool isCanEdit = true,
      bool showBottomLine = true,
      bool Function()? canShowPicker,
      DateTime? minimumDate,
      DateTime? maximumDate,
      void Function(DateTime?)? onChange}) {
    return BdhDatePicker(
        item: FormItem(
            title: title, isRequired: isRequired, isCanEdit: isCanEdit),
        initialValue: initialValue,
        placeholder: placeholder,
        textAlign: TextAlign.right,
        titleStyle: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
        placeholderStyle: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 0.4)),
        textStyle: TextStyle(
            fontSize: 14.px, color: const Color.fromRGBO(51, 51, 51, 1)),
        showBottomLine: showBottomLine,
        onChanged: onChange != null
            ? (v) {
                controller._unfocus();
                onChange.call(v);
              }
            : null,
        checkState: true,
        minimumDate: minimumDate,
        maximumDate: maximumDate,
        canShowPicker: canShowPicker != null
            ? () {
                controller._unfocus();
                return canShowPicker.call();
              }
            : null);
  }
}

class _Controller extends UseController {
  List<DictNode> workTypeDict = [
    DictNode(name: '按照地块面积作业', code: '01'),
    DictNode(name: '未作业', code: '02'),
    DictNode(name: '部分作业', code: '03')
  ];
  final Map<String, dynamic> params;

  _Controller(super.context, this.params);

  late final loadingStatus = use<LoadingStatus>(LoadingStatus.init);
  late final actBeginDate = use<DateTime>(null);
  late final longitude = use<String>(null);
  late final latitude = use<String>(null);

  late final workType = use<DictNode>(null)
    ..onChange = (v) {
      if (v == null) {
        workArea.value = null;
      } else if (v.code == '01') {
        //按照地块面积作业
        workArea.value = "99999";
      } else if (v.name == '02') {
        //未作业
        workArea.value = "0";
      } else if (v.code == '03') {
        workArea.value = null;
      }
    };
  late final meansUseUnitOption = useList<DictNode>();

  late final meansProdSubtypeOptions = useList<DictNode>();
  late final meansProdSubtypeOptions2 = useList<DictNode>();

  DateTime? timeSectionStartDate;
  DateTime? timeSectionEndDate;
  late final minDate = use<DateTime>(null);
  late final maxDate = use<DateTime>(null);
  late final workArea = use<String>(null);
  late final agriRecordsDesc = use<String>(null);

  late final meansProdTypeOption = useList<DictNode>();
  late final fertilizerTypeList = useList<DictNode>();
  late final pesticideTypeList = useList<DictNode>();

  late final recordFarmingElement = use<RecordFarmingElement>(null);
  late final formList = useList<OtherOVExt>();

  late final asAgriRecordsInputsList = useList<AgriRecordsInputMapper>()
    ..onChange = (v) {
      Log.d("asAgriRecordsInputsList changed ${v?.length}");
    };

  void addNewAgriRecordsInput() {
    asAgriRecordsInputsList.add(AgriRecordsInputMapper(isNew: true));
    showToast("已添加投入品${asAgriRecordsInputsList.value?.length ?? 0 + 1}");
  }

  void removeNewAgriRecordsInput(AgriRecordsInputMapper item) {
    showConfirmDialog(context, message: "是否删除该条投入品？").then((result) {
      if (result == true) {
        asAgriRecordsInputsList.remove(item);
      }
    });
  }

  List<DictNode> getMeansProdDict(AgriRecordsInputMapper item) {
    if (item.meansProdType == null) {
      return [];
    } else if (item.meansProdType?.code == "1") {
      return fertilizerTypeList.value ?? [];
    } else if (item.meansProdType?.code == "3") {
      return pesticideTypeList.value ?? [];
    }
    return [];
  }

  List<DictNode> getMeansUnitDict(AgriRecordsInputMapper item) {
    if (item.meansProdType == null) {
      return [];
    } else if (item.meansProdType?.code == "2") {
      //种子
      return meansUseUnitOption.value
              ?.where((test) => test.code == "1")
              .toList() ??
          [];
    } else {
      if (item.meansProd == null) {
        return [];
      }
      var type = item.meansProd?.data as MeansType?;
      if (type?.meansProdUnit != null) {
        return meansUseUnitOption.value
                ?.where((test) => test.code == type?.meansProdUnit)
                .toList() ??
            [];
      }
      return meansUseUnitOption.value ?? [];
    }
  }

  List<MediaItem>? urlsToMediaItemList(List<String>? urls) {
    return urls
        ?.map<MediaItem>((test) => MediaItem(url: test, type: MediaType.image))
        .toList();
  }

  late final workPicUrlList = useList<String>();
  late final workVideoUrlList = useList<String>();

  bool get canEdit {
    return params["type"] != "read";
  }

  bool get isSeedLand => params["isSeedLand"] == '1';

  bool get showWorkTypeInput => workType.value?.code == '03' && !isSeedLand;
  bool get showElementList => formList.value?.isNotEmpty ?? false;

  bool get hasPlotList => params["plotList"] != null;
  bool get hasSetValue => params["setValue"] != null;
  bool get hasSettingList => params["settingList"] != null;
  bool get isBatch => params["flag"] == "1";

  String get title {
    if (params["type"] == "read") {
      return "查看我的农事";
    } else if (params["type"] == "edit") {
      if (isBatch) {
        return "批量编辑农事";
      } else {
        return "编辑我的农事";
      }
    } else if (params["type"] == "modify") {
      if (isBatch) {
        return "批量记录农事";
      } else {
        return "记录我的农事";
      }
    }
    return "我的农事";
  }

  void onInit() {
    if (isBatch && hasPlotList) {
      workTypeDict.removeAt(2);
    }

    // 单位
    var future0 = AgriculturalRecordsService().getCostanalysisDict(
        data: "means_use_unit", cancelToken: createCancelToken());
    // 农药分类
    var future1 = AgriculturalRecordsService().getCostanalysisDict(
        data: "pesticide_type", cancelToken: createCancelToken());
    // 化肥分类
    var future2 = AgriculturalRecordsService().getCostanalysisDict(
        data: "means_prod_subtype", cancelToken: createCancelToken());
    var future3 = AgriculturalRecordsService().queryRightfulVerf(data: {
      "raiseCrops": params["raiseCrops"],
      "linkCodes": [
        params["linkCode"],
      ],
      "patternsLinkId": params["patternsLinkId"],
    }, cancelToken: createCancelToken());

    var future4 = AgriculturalRecordsService().queryMeansType(data: {
      "orgCode": params["orgCode"],
      "raiseCrops": params["raiseCrops"],
      "statYear": params["statYear"],
      "patternsLinkId": params["patternsLinkId"],
    }, cancelToken: createCancelToken());

    Future.wait([future0, future1, future2, future3, future4]).then((result) {
      var result0 = result[0] as DictList;

      if (result0.code == 0 &&
          result0.success == true &&
          result0.data != null) {
        meansUseUnitOption.value = result0.data;
      } else {
        throw RequestException(
            code: result0.code ?? -1, message: result0.msg ?? "获取单位失败");
      }

      var result1 = result[1] as DictList;
      if (result1.code == 0 &&
          result1.success == true &&
          result1.data != null) {
        meansProdSubtypeOptions.value = result1.data;
      } else {
        throw RequestException(
            code: result1.code ?? -1, message: result1.msg ?? "获取农药分类失败");
      }
      var result2 = result[2] as DictList;
      if (result2.code == 0 &&
          result2.success == true &&
          result2.data != null) {
        meansProdSubtypeOptions2.value = result2.data;
      } else {
        throw RequestException(
            code: result2.code ?? -1, message: result2.msg ?? "获取化肥分类失败");
      }
      var result3 = result[3] as RequestNoData;
      if (result3.code == 0 &&
          result3.success == true &&
          result3.data != null &&
          result3.data.isNotEmpty) {
        var start = result3.data[0]["planStartDate"];
        var end = result3.data[0]["planEndDate"];
        Log.d("start is $start,end is $end");
        timeSectionStartDate = DateTime(
            int.parse(params["statYear"]),
            int.parse(start.substring(0, 2)),
            int.parse(start.substring(2, 4)),
            0,
            0,
            0);
        timeSectionEndDate = DateTime(
            int.parse(params["statYear"]),
            int.parse(end.substring(0, 2)),
            int.parse(end.substring(2, 4)),
            23,
            59,
            59);
        if (timeSectionStartDate!.isAfter(timeSectionEndDate!)) {
          throw RequestException(
              code: -1,
              message: "queryRightfulVerf error,$start is after $end");
        }
      }
      var result4 = result[4] as RequestNoData;
      if (result4.code == 0 &&
          result4.success == true &&
          result4.data != null) {
        meansProdTypeOption.value = result4.data.map<DictNode>((v) {
              var type = MeansType.fromJson(v);
              return DictNode(
                  code: type.meansProdType?.toString(),
                  name: type.meansProdTypeName,
                  data: type);
            }).toList() ??
            [];

        fertilizerTypeList.value = meansProdTypeOption.value
                ?.firstWhereOrNull((test) => test.data.meansProdType == 1)
                ?.data
                .children
                ?.map<DictNode>((v) {
              MeansType vv = v;
              var name = meansProdSubtypeOptions2.value
                  ?.firstWhereOrNull((test) => test.code == vv.meansProdSubtype)
                  ?.name;
              return DictNode(
                  code: vv.meansProdId?.toString(),
                  name: vv.meansProdName,
                  data: vv.copyWith(
                      pesticideTypeName: name,
                      pesticideType: vv.meansProdSubtype));
            }).toList() ??
            [];

        pesticideTypeList.value = meansProdTypeOption.value
                ?.firstWhereOrNull((test) => test.data.meansProdType == 3)
                ?.data
                .children
                ?.map<DictNode>((v) {
              MeansType vv = v;
              var name = meansProdSubtypeOptions.value
                  ?.firstWhereOrNull((test) => test.code == vv.pesticideType)
                  ?.name;
              return DictNode(
                  code: vv.meansProdId?.toString(),
                  name: vv.meansProdName,
                  data: vv.copyWith(pesticideTypeName: name));
            }).toList() ??
            [];
      }

      if (params["type"] == 'edit' && isBatch && hasPlotList && hasSetValue) {
        // 批量修改数据走缓存
        getListFromCache();
      } else {
        getList();
      }
    }).onError(handleError);
  }

  Future getListFromCache() async {
    var data = params["setValue"];
    await setElement(data);
    loadingStatus.value = LoadingStatus.success;
  }

  Future setElement(dynamic data) async {
    var element = RecordFarmingElement.fromJson(data);
    //附加属性
    if (element.otherVOList != null) {
      var list = <OtherOVExt>[];
      for (var json in element.otherVOList!) {
        var vo = OtherOV.fromJson(json);
        var otherOVExt = OtherOVExt(vo);
        var v = vo.elementValue ?? vo.elementDefValue;

        if (otherOVExt.isInputLimitLength || otherOVExt.isInput) {
          otherOVExt.value = vo.elementValue ?? vo.elementDefValue;
        }

        //照片,视频类型时默认值类型更改
        else if (otherOVExt.isImage || otherOVExt.isVideo) {
          if (v != null) {
            otherOVExt.value = v.split(",").map((test) => test).toList();
          }
        } else if (otherOVExt.isSingleSelect) {
          var extVo = list.firstWhereOrNull(
              (test) => test.otherOV.elementId == otherOVExt.otherOV.elementId);
          extVo ??= otherOVExt;
          if (extVo.dict == null) {
            if (vo.elementFillMethod == '3') {
              var dictList = await AgriculturalRecordsService()
                  .getDict(data: vo.dictKey, cancelToken: createCancelToken());
              extVo.dict = dictList.data;
            } else if (vo.elementFillMethod == '4') {
              var r = await AgriculturalRecordsService().queryOutNum(data: {
                "externalDataId": vo.externalDataId,
                "orgCode": params["orgCode"]
              }, cancelToken: createCancelToken());
              extVo.dict = r.data
                      ?.map<DictNode>((test) => DictNode(
                          code: test["elementValueId"]?.toString(),
                          name: test["elementValue"]))
                      .toList() ??
                  [];
            }
          }

          if (v != null) {
            extVo.value =
                otherOVExt.dict?.firstWhereOrNull((test) => test.name == v);
          }
        } else if (otherOVExt.isMultiSelect) {
          var extVo = list.firstWhereOrNull(
              (test) => test.otherOV.elementId == otherOVExt.otherOV.elementId);
          if (extVo != null) {}
          extVo ??= otherOVExt;
          if (extVo.dict == null) {
            if (vo.elementFillMethod == '3') {
              var dictList = await AgriculturalRecordsService()
                  .getDict(data: vo.dictKey, cancelToken: createCancelToken());
              extVo.dict = dictList.data;
            } else if (vo.elementFillMethod == '4') {
              var data = {
                "externalDataId": vo.externalDataId,
                "orgCode": params["orgCode"],
              };

              if (vo.elementValueId != null) {
                data["driverName"] = vo.elementValueId;
              }
              var r = await AgriculturalRecordsService()
                  .queryOutNum(data: data, cancelToken: createCancelToken());
              extVo.dict = r.data
                      ?.map<DictNode>((test) => DictNode(
                          code: test["elementValueId"]?.toString(),
                          name: test["elementValue"]))
                      .toList() ??
                  [];
            }
          }

          if (vo.elementValueId != null) {
            var item = extVo.dict?.firstWhereOrNull(
                (test) => test.code == vo.elementValueId?.toString());

            if (item != null) {
              List<DictNode> value = extVo.value ?? [];
              value.add(item);
              extVo.value = value;
              Log.d("length is : ${extVo.value.length}");
            }
          }
        } else if (otherOVExt.isInputDateTime) {
          if (v != null) {
            otherOVExt.value = DateTime.parse(v);
          }
        }

        var exist = list.firstWhereOrNull(
            (test) => test.otherOV.elementId == otherOVExt.otherOV.elementId);

        if (exist == null) {
          list.add(otherOVExt);
        }
      }
      formList.value = list;
    } else {
      formList.value = null;
    }

    //初次记录
    if (params["type"] == "modify") {
      List<Map<String, dynamic>> list = [];
      if (element.isGrow == "1") {
        list.add({
          "meansProdType": "2",
          "meansProdUnit": meansUseUnitOption.value
              ?.where((test) => test.code == "1")
              .toList()
              .firstOrNull
              ?.code
        });
      }
      if (element.isPesticide == "1") {
        list.add({
          "meansProdType": "3",
        });
      }
      if (element.isFertilize == "1") {
        list.add({
          "meansProdType": "1",
        });
      }
      element = element.copyWith(asAgriRecordsInputsList: list);
    }

    // 投入品
    if (element.asAgriRecordsInputsList != null &&
        element.asAgriRecordsInputsList!.isNotEmpty) {
      List<AgriRecordsInputMapper> list = [];
      for (var json in element.asAgriRecordsInputsList!) {
        var v = AgriRecordsInput.fromJson(json);
        var agriRecordsInputs = AgriRecordsInputMapper(origin: v);
        if (v.instructionsPic1?.isNotEmpty ?? false) {
          agriRecordsInputs.meansPicUrl1list?.add(v.instructionsPic1!);
        }
        if (v.instructionsPic2?.isNotEmpty ?? false) {
          agriRecordsInputs.meansPicUrl1list?.add(v.instructionsPic2!);
        }
        if (v.instructionsPic3?.isNotEmpty ?? false) {
          agriRecordsInputs.meansPicUrl1list?.add(v.instructionsPic3!);
        }

        if (v.goodsPic1?.isNotEmpty ?? false) {
          agriRecordsInputs.meansPicUrl2list?.add(v.goodsPic1!);
        }
        if (v.goodsPic2?.isNotEmpty ?? false) {
          agriRecordsInputs.meansPicUrl2list?.add(v.goodsPic2!);
        }
        if (v.goodsPic3?.isNotEmpty ?? false) {
          agriRecordsInputs.meansPicUrl2list?.add(v.goodsPic3!);
        }

        if (v.actGoodsPic1?.isNotEmpty ?? false) {
          agriRecordsInputs.meansPicUrl3list?.add(v.actGoodsPic1!);
        }

        if (v.actGoodsPic2?.isNotEmpty ?? false) {
          agriRecordsInputs.meansPicUrl3list?.add(v.actGoodsPic2!);
        }

        if (v.actGoodsPic3?.isNotEmpty ?? false) {
          agriRecordsInputs.meansPicUrl3list?.add(v.actGoodsPic3!);
        }

        if (v.meansProdType != null) {
          agriRecordsInputs.meansProdType = meansProdTypeOption.value
              ?.firstWhereOrNull((test) => test.code == v.meansProdType);
          // 化肥
          if (agriRecordsInputs.meansProdType?.code == "1") {
            //名称
            agriRecordsInputs.meansProd = fertilizerTypeList.value
                ?.firstWhereOrNull(
                    (test) => test.code == v.meansProdId?.toString());

            agriRecordsInputs.minValue =
                agriRecordsInputs.meansProdType?.data.minValue;
            agriRecordsInputs.maxValue =
                agriRecordsInputs.meansProdType?.data.maxValue;
            Log.d(
                "message ${agriRecordsInputs.minValue} ${agriRecordsInputs.maxValue}");
            if (v.meansProdUnit != null) {
              //使用量单位
              agriRecordsInputs.meansProdUnit = meansUseUnitOption.value
                  ?.firstWhereOrNull((test) => test.code == v.meansProdUnit);
            }
            //数量
            agriRecordsInputs.totalDosage = v.totalDosage?.toString();
          }
          // 种子
          else if (agriRecordsInputs.meansProdType?.code == "2") {
            //名称
            agriRecordsInputs.meansProd = null;

            //使用量单位
            if (v.meansProdUnit != null) {
              //使用量单位
              agriRecordsInputs.meansProdUnit = meansUseUnitOption.value
                  ?.firstWhereOrNull((test) => test.code == v.meansProdUnit);
            }
            //数量
            agriRecordsInputs.totalDosage = v.totalDosage?.toString();
          }
          // 农药
          else if (agriRecordsInputs.meansProdType?.code == "3") {
            //名称
            agriRecordsInputs.meansProd = pesticideTypeList.value
                ?.firstWhereOrNull(
                    (test) => test.code == v.meansProdId?.toString());
            agriRecordsInputs.minValue =
                agriRecordsInputs.meansProdType?.data.minValue;
            agriRecordsInputs.maxValue =
                agriRecordsInputs.meansProdType?.data.maxValue;
            //使用量单位
            if (v.meansProdUnit != null) {
              agriRecordsInputs.meansProdUnit = meansUseUnitOption.value
                  ?.firstWhereOrNull((test) => test.code == v.meansProdUnit);
            }
            Log.d(
                "message ${agriRecordsInputs.minValue} ${agriRecordsInputs.maxValue}");
            //数量
            agriRecordsInputs.totalDosage = v.totalDosage?.toString();
          }
        } else {
          agriRecordsInputs.meansProd = null;
          agriRecordsInputs.meansProdUnit = null;
          agriRecordsInputs.totalDosage = null;
        }

        list.add(agriRecordsInputs);
      }

      asAgriRecordsInputsList.value = list;
    }

    //作业照片
    var workPicList = <String>[];

    if (element.workPic1?.isNotEmpty ?? false) {
      workPicList.add(element.workPic1!);
    }
    if (element.workPic2?.isNotEmpty ?? false) {
      workPicList.add(element.workPic2!);
    }
    if (element.workPic3?.isNotEmpty ?? false) {
      workPicList.add(element.workPic3!);
    }
    workPicUrlList.value = workPicList;

    //作业视频
    var workVideoList = <String>[];

    if (element.workVideo1?.isNotEmpty ?? false) {
      workVideoList.add(element.workVideo1!);
    }
    if (element.workVideo2?.isNotEmpty ?? false) {
      workVideoList.add(element.workVideo2!);
    }
    if (element.workVideo3?.isNotEmpty ?? false) {
      workVideoList.add(element.workVideo3!);
    }
    workVideoUrlList.value = workVideoList;

    if (element.actBeginDate != null) {
      var tmpActBeginDate = DateTime.parse(element.actBeginDate!);
      actBeginDate.value = DateTime(tmpActBeginDate.year, tmpActBeginDate.month,
          tmpActBeginDate.day, 0, 0, 1);
      Log.d(
          "actBeginDate is ${actBeginDate.value} timeSectionStartDate is $timeSectionStartDate timeSectionEndDate is $timeSectionEndDate ");
      if (timeSectionStartDate != null && timeSectionEndDate != null) {
        var now = DateTime.now();
        minDate.value = timeSectionStartDate;

        bool timeFlag = now.isAfter(timeSectionStartDate!) &&
            now.isBefore(timeSectionEndDate!);

        if (timeFlag) {
          // 获取右区间的时间戳
          maxDate.value = now;
        } else {
          // 获取右区间的时间戳
          maxDate.value = timeSectionEndDate!;
        }
        bool timeFlagUpdate =
            actBeginDate.value!.isAfter(timeSectionStartDate!) &&
                actBeginDate.value!.isBefore(timeSectionEndDate!);

        Log.d("timeFlagUpdate $timeFlagUpdate");
        if (!timeFlagUpdate) {
          // 修改在时间范围内
          actBeginDate.value = null;
        }
      }
    } else {
      if (timeSectionStartDate != null && timeSectionEndDate != null) {
        var now = DateTime.now();
        minDate.value = timeSectionStartDate;
        bool timeFlag = now.isAfter(timeSectionStartDate!) &&
            now.isBefore(timeSectionEndDate!);
        if (timeFlag) {
          actBeginDate.value = now;
          // 获取右区间的时间戳
          maxDate.value = now;
        } else {
          actBeginDate.value = null;
          // 获取右区间的时间戳
          maxDate.value = timeSectionEndDate!;
        }
      }
    }

    //作业面积
    if (element.workArea == 0) {
      workType.value =
          workTypeDict.firstWhereOrNull((test) => test.name == "未作业");
      workArea.value = "0";
    } else if (element.workArea == null ||
        element.workArea == 99999 ||
        (params["landArea"] != null &&
            (element.workArea ?? 0) >= double.parse(params["landArea"]!))) {
      workType.value =
          workTypeDict.firstWhereOrNull((test) => test.name == "按照地块面积作业");
      workArea.value = "99999";
    } else {
      workType.value =
          workTypeDict.firstWhereOrNull((test) => test.name == "部分作业");
      workArea.value = element.workArea?.toString();
    }

    //农事描述
    agriRecordsDesc.value = element.agriRecordsDesc;
    //经纬度
    latitude.value = element.latitude;
    longitude.value = element.longitude;
    recordFarmingElement.value = element;
    loadingStatus.value = LoadingStatus.success;
  }

  Future getList() {
    late Future<RequestNoData> future;

    var data = {
      "statYear": params["statYear"],
      "landNo": params["landNo"],
      "orgCode": params["orgCode"],
      "patternsLinkId": params["patternsLinkId"],
    };

    if (hasPlotList && isBatch) {
      data.remove("landNo");
    }

    if (params["type"] == 'modify') {
      future = AgriculturalRecordsService().asLinkProgressLndmyInfo(
          data: data, cancelToken: createCancelToken());
    } else {
      data["agriRecordsId"] = params["agriRecordsId"];
      future = AgriculturalRecordsService()
          .queryRecordFarming(data: data, cancelToken: createCancelToken());
    }
    loadingStatus.value = LoadingStatus.loading;
    return future.then((result) async {
      if (result.code == 0 && result.success == true && result.data != null) {
        await setElement(result.data);
        loadingStatus.value = LoadingStatus.success;
      }
    }).onError(handleError);
  }

  bool get submitDisable {
    return recordFarmingElement.value == null ||
        actBeginDate.value == null ||
        (workType.value == null) ||
        (workType.value?.code == "03" && (workArea.value?.isEmpty ?? true));
  }

  void onSubmit() {
    _unfocus();
    var element = recordFarmingElement.value;

    if (element == null) {
      return;
    }
    if (actBeginDate.value == null) {
      showToast('请选择开始日期');
      return;
    } else if (workType.value?.code == "03" &&
        (workArea.value?.isEmpty ?? true)) {
      showToast('请输入实际作业面积');
      return;
    } else if (workType.value?.code == "03" &&
        !checkNumber(workArea.value!, 5, precision: 4, isRequired: true)) {
      showToast('实际作业面积格式不正确,5位整数,4位小数');
      return;
    }

    var arr = ['请输入', '请选择', '请选择', '请上传', '请输入'];
    for (var index = 0; index < formList.length; index++) {
      var item = formList[index];
      bool isRequired = item.otherOV.isNotNull == "1";
      bool isEmpty = false;
      if (item.value == null) {
        isEmpty = true;
      } else if (item.value is List<DictNode>) {
        isEmpty = item.value.isEmpty;
      } else if (item.value is String) {
        isEmpty = item.value.isEmpty;
      }
      if ((isRequired && !isSeedLand && workType.value?.code != '02') ||
          (isRequired && isSeedLand)) {
        // 必填&&不是秧田
        if (isEmpty && isRequired) {
          showToast(
              '${arr[int.parse(item.otherOV.elementType!)]}${item.otherOV.elementName}');
          return;
        }
        if (item.otherOV.elementType == "4") {
          if (!checkNumber(item.value, item.otherOV.elementLength,
              precision: item.otherOV.elementPrecision,
              isRequired: isRequired)) {
            showToast(
                "${item.otherOV.elementName}格式不正确,${item.otherOV.elementLength}位整数,${item.otherOV.elementPrecision}位小数");
            return;
          } else if (item.otherOV.minValue != null &&
              item.otherOV.maxValue != null &&
              double.parse(item.value) < item.otherOV.minValue! &&
              double.parse(item.value) > item.otherOV.maxValue!) {
            showToast(
                "${item.otherOV.elementName}范围应在${item.otherOV.minValue}~${item.otherOV.maxValue}之间");
            return;
          } else if (item.otherOV.minValue != null &&
              double.parse(item.value) < item.otherOV.minValue!) {
            showToast(
                "${item.otherOV.elementName}应大于等于${item.otherOV.minValue}");
            return;
          } else if (item.otherOV.maxValue != null &&
              double.parse(item.value) > item.otherOV.maxValue!) {
            showToast(
                "${item.otherOV.elementName}应小于等于${item.otherOV.maxValue}");
            return;
          }
        }
      } else {
        if ((item.otherOV.elementType == "4" &&
                item.value != null &&
                !isSeedLand &&
                workType.value?.code != '02') || //未作业
            (item.otherOV.elementType == "4" &&
                item.value != null &&
                isSeedLand)) {
          if (!checkNumber(item.value, item.otherOV.elementLength,
              precision: item.otherOV.elementPrecision,
              isRequired: isRequired)) {
            showToast(
                "${item.otherOV.elementName}格式不正确,${item.otherOV.elementLength}位整数,${item.otherOV.elementPrecision}位小数");
            return;
          } else if (item.otherOV.minValue != null &&
              item.otherOV.maxValue != null &&
              double.parse(item.value) < item.otherOV.minValue! &&
              double.parse(item.value) > item.otherOV.maxValue!) {
            showToast(
                "${item.otherOV.elementName}范围应在${item.otherOV.minValue}~${item.otherOV.maxValue}之间");
            return;
          } else if (item.otherOV.minValue != null &&
              double.parse(item.value) < item.otherOV.minValue!) {
            showToast(
                "${item.otherOV.elementName}应大于等于${item.otherOV.minValue}");
            return;
          } else if (item.otherOV.maxValue != null &&
              double.parse(item.value) > item.otherOV.maxValue!) {
            showToast(
                "${item.otherOV.elementName}应小于等于${item.otherOV.maxValue}");
            return;
          }
        }
      }
    }

    var reg = RegExp(r"^([0-9]{1}|^[1-9]{1}\d{1,15})(\.\d{1,2})?$");
    for (var index = 0; index < asAgriRecordsInputsList.length; index++) {
      var v = asAgriRecordsInputsList[index];
      if (v.meansProdType == null) {
        showToast('投入品${index + 1}请选择类型');
        return;
      } else if (v.meansProd == null && v.meansProdType?.code != "2") {
        showToast('投入品${index + 1}请选择名称');

        return;
      } else if ((v.totalDosage?.isEmpty ?? true) ||
          num.parse(v.totalDosage!) <= 0) {
        showToast('投入品${index + 1}使用量必须大于等于0');
        return;
      } else if (!reg.hasMatch(v.totalDosage!)) {
        showToast("投入品${index + 1}使用量必须为数字(可保留两位小数)");
        return;
      } else if (v.minValue != null &&
          v.maxValue != null &&
          (double.parse(v.totalDosage!) < v.minValue! ||
              double.parse(v.totalDosage!) > v.maxValue!)) {
        showToast("投入品${index + 1}使用量数字范围应在${v.minValue}~${v.maxValue}之间");
        return;
      } else if (v.minValue != null &&
          double.parse(v.totalDosage!) < v.minValue!) {
        showToast("投入品${index + 1}使用量数字应大于等于${v.minValue}");
        return;
      } else if (v.maxValue != null &&
          double.parse(v.totalDosage!) > v.maxValue!) {
        showToast("投入品${index + 1}使用量数字应小于等于${v.maxValue}");
        return;
      } else if (v.meansProdUnit == null) {
        showToast("投入品${index + 1}请选择单位");
        return;
      }
    }

    if ((element.isFertilize == "1" &&
            !isSeedLand &&
            workType.value?.name != '未作业') ||
        element.isFertilize == "1" && isSeedLand) {
      if (asAgriRecordsInputsList.length > 0) {
        var n = asAgriRecordsInputsList.value
            ?.firstWhereOrNull((test) => test.meansProdType?.code == "1");
        if (n == null) {
          showToast("投入品类型请选择化肥");
          return;
        }
      } else {
        showToast("投入品类型请选择化肥");
        return;
      }
    }
    if ((element.isGrow == "1" &&
            !isSeedLand &&
            workType.value?.name != '未作业') ||
        element.isGrow == "1" && isSeedLand) {
      if (asAgriRecordsInputsList.length > 0) {
        var n = asAgriRecordsInputsList.value
            ?.firstWhereOrNull((test) => test.meansProdType?.code == "2");
        if (n == null) {
          showToast("投入品类型请选择种子");
          return;
        }
      } else {
        showToast("投入品类型请选择种子");
        return;
      }
    }

    if ((element.isPesticide == "1" &&
            !isSeedLand &&
            workType.value?.name != '未作业') ||
        element.isPesticide == "1" && isSeedLand) {
      if (asAgriRecordsInputsList.length > 0) {
        var n = asAgriRecordsInputsList.value
            ?.firstWhereOrNull((test) => test.meansProdType?.code == "3");
        if (n == null) {
          showToast("投入品类型请选择农药");
          return;
        }
      } else {
        showToast("请添加投入品类型,请选择农药");
        return;
      }
    }
    List<OtherOV> otherVOList = [];
    var formListTmp = formList.value;
    if (formListTmp != null && formListTmp.isNotEmpty) {
      for (var item in formListTmp) {
        otherVOList.addAll(item.getUpdated());
      }
    }

    double workAreaValue = 0;
    if (workType.value?.code == "01") {
      workAreaValue = 99999;
    } else if (workType.value?.code == "02") {
      workAreaValue = 0;
    } else if (workType.value?.code == "03") {
      if (isBatch) {
        workAreaValue = 0;
      } else {
        workAreaValue = double.parse(workArea.value!);
      }
    }

    if (isSeedLand) {
      workAreaValue = 0;
    }

    element = element.copyWith(
      actBeginDate: DateFormat("yyyyMMdd").format(actBeginDate.value!),
      patternsLinkId: params["patternsLinkId"],
      statYear: params["statYear"],
      orgCode: params["orgCode"],
      orgName: params["orgName"],
      plotNo: params["landNo"],
      plotName: params["landName"],
      workArea: workAreaValue,
      latitude: latitude.value,
      longitude: longitude.value,
      agriRecordsDesc: agriRecordsDesc.value,
      workPic1: workPicUrlList.value?.elementAtOrNull(0),
      workPic2: workPicUrlList.value?.elementAtOrNull(1),
      workPic3: workPicUrlList.value?.elementAtOrNull(2),
      workVideo1: workVideoUrlList.value?.elementAtOrNull(0),
      workVideo2: workVideoUrlList.value?.elementAtOrNull(1),
      workVideo3: workVideoUrlList.value?.elementAtOrNull(2),
      asAgriRecordsInputsList: asAgriRecordsInputsList.value
              ?.map<Map<String, dynamic>>((test) => test.getUpdated().toJson())
              .toList() ??
          [],
      workStationName: params["workStationName"],
      workStationCode: params["workStationCode"],
      otherVOList: otherVOList
          .map<Map<String, dynamic>>((test) => test.toJson())
          .toList(),
      plotList: isBatch
          ? (params["plotList"] as List<LandRecord>?)
                  ?.map<Map<String, dynamic>>((test) => test.toJson())
                  .toList() ??
              []
          : null,
      itemList: isBatch
          ? (params["settingList"] as List<QueryElementItem>?)
                  ?.map<Map<String, dynamic>>((test) => test.toJson())
                  .toList() ??
              []
          : null,
    );

    if (params["type"] == "edit") {
      element = element.copyWith(agriRecordsId: params["agriRecordsId"]);
    }

    var data = element.toJson();
    Log.d("data is ${jsonEncode(data)}");
    showLoading(context, content: "正在提交..   ");

    late Future<RequestNoData> res;
    if (isBatch) {
      res = AgriculturalRecordsService()
          .recordAllFarming(data: data, cancelToken: createCancelToken());
    } else {
      res = AgriculturalRecordsService()
          .saveRecordFarming(data: data, cancelToken: createCancelToken());
    }

    res.then((result) {
      if (!context.mounted) {
        return;
      }
      hideLoading(context);
      if (result.success == true && result.code == 0) {
        showToast("提交成功");
        //如是是批量修改则需要保存？
        if (isBatch) {
          Navigator.of(context).pop(data);
        } else {
          Navigator.of(context).pop(true);
        }
      }
    }).onError((error, stackTrace) {
      handleError(error, stackTrace, errorDo: () {
        hideLoading(context);
      });
    }).whenComplete(() {
      if (!context.mounted) {
        return;
      }
    });
  }

  void _unfocus() {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus && currentFocus.focusedChild != null) {
      FocusManager.instance.primaryFocus?.unfocus();
    }
  }

  void geLatLng() {
    LatLng? initLocate;
    if ((latitude.value?.isNotEmpty ?? false) &&
        (longitude.value?.isNotEmpty ?? false)) {
      initLocate =
          LatLng(double.parse(latitude.value!), double.parse(longitude.value!));
    }
    Navigator.of(context).push(CupertinoPageRoute(builder: (context) {
      return MapPage(
        initLocate: initLocate,
        onSelect: (latlng) {
          if (latlng != null) {
            latitude.value = latlng.latitude.toString();
            longitude.value = latlng.longitude.toString();
          }
        },
      );
    }));
  }

  bool checkNumber(String? num, int? length,
      {int? precision, bool isRequired = false}) {
    if ((num?.isEmpty ?? true) && !isRequired) {
      return true;
    }
    late RegExp regNumber;
    var l = length ?? 0;
    if (precision != null && precision >= 1) {
      regNumber = RegExp(
          "^([0-9]{1}|^[1-9]{1}\\d{1,${l > 1 ? l - 1 : 1}})(\\.\\d{1,$precision})?\$");
    } else {
      if (l > 1) {
        regNumber = RegExp("^([0-9]{1}|[1-9]{1}\\d{1,${l > 1 ? l - 1 : 1}})\$");
      } else {
        regNumber = RegExp("^[0-9]{1}\$");
      }
    }
    return regNumber.hasMatch(num!);
  }
}

//附加项
class OtherOVExt {
  final OtherOV otherOV;

  List<DictNode>? dict;
  dynamic value;

  OtherOVExt(this.otherOV, {this.dict, this.value});

  bool get isInputLimitLength => otherOV.elementType == '0';
  bool get isInputDateTime => otherOV.elementType == '1';

  bool get isSingleSelect =>
      otherOV.elementType == '2' && otherOV.elementFillMethod != '4';

  bool get isMultiSelect =>
      otherOV.elementType == '2' &&
      otherOV.elementFillMethod == '4' &&
      otherOV.isMultiple == '1';

  bool get isImage => otherOV.elementType == '3';

  bool get isInput => otherOV.elementType == '4' || otherOV.elementType == '0';

  bool get isVideo => otherOV.elementType == '5';

  List<OtherOV> getUpdated() {
    List<OtherOV> list = [];
    if (isInputLimitLength || isInput) {
      if (value != null) {
        var ov = otherOV.copyWith(elementValue: value.toString());
        list.add(ov);
      } else {
        list.add(otherOV);
      }
    } else if (isImage || isVideo) {
      var v = (value as List<String>?)?.join(",");
      if (v != null) {
        var ov = otherOV.copyWith(elementValue: v);
        list.add(ov);
      } else {
        list.add(otherOV);
      }
    } else if (isSingleSelect) {
      var v = (value as DictNode?);
      if (v != null) {
        var ov = otherOV.copyWith(
            elementValue: v.name, elementValueId: int.parse(v.code!));
        list.add(ov);
      } else {
        list.add(otherOV);
      }
    } else if (isMultiSelect) {
      var vList = (value as List<DictNode>?);
      if (vList != null) {
        for (var v in vList) {
          var ov = otherOV.copyWith(
              elementValue: v.name, elementValueId: int.parse(v.code!));
          list.add(ov);
        }
      } else {
        list.add(otherOV);
      }
    } else if (isInputDateTime) {
      var v = (value as DateTime?);
      if (v != null) {
        var ov =
            otherOV.copyWith(elementValue: DateFormat("yyyy-MM-dd").format(v));
        list.add(ov);
      } else {
        list.add(otherOV);
      }
    }

    return list;
  }
}

//投入品
class AgriRecordsInputMapper {
  final bool isNew;
  final AgriRecordsInput? origin;

  DictNode? meansProdType;
  DictNode? meansProd;
  DictNode? meansProdUnit;

  List<String>? meansPicUrl1list = [];
  List<String>? meansPicUrl2list = [];
  List<String>? meansPicUrl3list = [];

  String? totalDosage;
  double? maxValue;
  double? minValue;

  AgriRecordsInputMapper({this.isNew = false, this.origin});

  AgriRecordsInput getUpdated() {
    AgriRecordsInput agriRecordsInput = origin?.copyWith(
            instructionsPic1: meansPicUrl1list?.elementAtOrNull(0),
            instructionsPic2: meansPicUrl1list?.elementAtOrNull(1),
            instructionsPic3: meansPicUrl1list?.elementAtOrNull(2),
            goodsPic1: meansPicUrl2list?.elementAtOrNull(0),
            goodsPic2: meansPicUrl2list?.elementAtOrNull(1),
            goodsPic3: meansPicUrl2list?.elementAtOrNull(2),
            actGoodsPic1: meansPicUrl3list?.elementAtOrNull(0),
            actGoodsPic2: meansPicUrl3list?.elementAtOrNull(1),
            actGoodsPic3: meansPicUrl3list?.elementAtOrNull(2),
            meansProdType: meansProdType?.code,
            meansProdUnit: meansProdUnit?.code,
            meansProdUnitName: meansProdUnit?.name,
            manufacturer: origin?.manufacturer,
            meansProdId:
                meansProd?.code == null ? null : int.parse(meansProd!.code!),
            meansProdName: meansProd?.name,
            totalDosage: totalDosage == null ? 0 : double.parse(totalDosage!),
            minValue: minValue,
            maxValue: maxValue) ??
        AgriRecordsInput(
            instructionsPic1: meansPicUrl1list?.elementAtOrNull(0),
            instructionsPic2: meansPicUrl1list?.elementAtOrNull(1),
            instructionsPic3: meansPicUrl1list?.elementAtOrNull(2),
            goodsPic1: meansPicUrl2list?.elementAtOrNull(0),
            goodsPic2: meansPicUrl2list?.elementAtOrNull(1),
            goodsPic3: meansPicUrl2list?.elementAtOrNull(2),
            actGoodsPic1: meansPicUrl3list?.elementAtOrNull(0),
            actGoodsPic2: meansPicUrl3list?.elementAtOrNull(1),
            actGoodsPic3: meansPicUrl3list?.elementAtOrNull(2),
            meansProdType: meansProdType?.code,
            meansProdUnit: meansProdUnit?.code,
            meansProdUnitName: meansProdUnit?.name,
            manufacturer: origin?.manufacturer,
            meansProdId:
                meansProd?.code == null ? null : int.parse(meansProd!.code!),
            meansProdName: meansProd?.name,
            totalDosage: totalDosage == null ? 0 : double.parse(totalDosage!),
            minValue: minValue,
            maxValue: maxValue);

    return agriRecordsInput;
  }

  @override
  String toString() {
    return 'AgriRecordsInputMapper(isNew: $isNew, origin: $origin, meansProdType: $meansProdType, meansProd: $meansProd, meansProdUnit: $meansProdUnit, meansPicUrl1list: $meansPicUrl1list, meansPicUrl2list: $meansPicUrl2list, meansPicUrl3list: $meansPicUrl3list, totalDosage: $totalDosage, maxValue: $maxValue, minValue: $minValue)';
  }

  @override
  bool operator ==(covariant AgriRecordsInputMapper other) {
    if (identical(this, other)) return true;

    return other.isNew == isNew &&
        other.origin == origin &&
        other.meansProdType == meansProdType &&
        other.meansProd == meansProd &&
        other.meansProdUnit == meansProdUnit &&
        listEquals(other.meansPicUrl1list, meansPicUrl1list) &&
        listEquals(other.meansPicUrl2list, meansPicUrl2list) &&
        listEquals(other.meansPicUrl3list, meansPicUrl3list) &&
        other.totalDosage == totalDosage &&
        other.maxValue == maxValue &&
        other.minValue == minValue;
  }

  @override
  int get hashCode {
    return isNew.hashCode ^
        origin.hashCode ^
        meansProdType.hashCode ^
        meansProd.hashCode ^
        meansProdUnit.hashCode ^
        meansPicUrl1list.hashCode ^
        meansPicUrl2list.hashCode ^
        meansPicUrl3list.hashCode ^
        totalDosage.hashCode ^
        maxValue.hashCode ^
        minValue.hashCode;
  }
}
