import 'package:bdh_smart_agric_app/components/form/bdh_image_picker.dart';
import 'package:bdh_smart_agric_app/components/form/bdh_media_picker.dart'
    as md;
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/request/api.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:http_parser/http_parser.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mime/mime.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

import 'request/agricultural_records_service.dart';

Future fellFarmImagePickerCallback(FormFieldState<List<BDHFile>> field,
    OnChange? onChange, XFile? image) async {
  if (image != null) {
    //这里需要考虑几种图片的格式
    String? mimeType = lookupMimeType(image.name);

    if (mimeType != null) {
      // 显示上传提示
      TDToast.showLoading(text: "上传中...", context: field.context);
      var bytes = await image.readAsBytes();
      Log.d(" 图片大小 ${bytes.length / 1024} kb");

      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(bytes,
            filename: image.name,
            contentType:
                MediaType(mimeType.split("/").first, mimeType.split("/").last))
      });
      //先上传图片到服务器
      AgriculturalRecordsService()
          .uploadFile(data: postData)
          .then((result) async {
        if (!field.context.mounted) {
          return;
        }
        TDToast.dismissLoading();
        if (result.code == 0 && result.success == true && result.data != null) {
          if (result.data == null) {
            showToast("图片上传失败,请稍后重试");
            return;
          }
          field.value!.add(BDHFile(url: result.data));
          field.didChange(field.value);
          onChange?.call(field.value);
        }
      }).onError((e, s) {
        Log.e("upload image error", error: e, stackTrace: s);
        var request = RequestException.handleError(e);
        if (request.isCancel) {
          return;
        }
        if (!field.context.mounted) {
          return;
        }
        TDToast.dismissLoading();
        showToast(request.message ?? "图片上传失败,请稍后重试");
      });
    } else {
      showToast("未知文件");
    }
  }
}

// 默认上传文件回调
Future fellFarmMediaPickerCallback(FormFieldState<List<md.MediaItem>> field,
    md.OnMediaChange? onChange, XFile? file, bool isVideo) async {
  if (file != null) {
    // 检查文件类型
    String? mimeType = lookupMimeType(file.name);
    if (mimeType == null) return;

    // 显示上传提示
    TDToast.showLoading(text: "上传中...", context: field.context);

    try {
      var bytes = await file.readAsBytes();
      Log.d("文件大小: ${bytes.length / 1024} kb");

      // 判断文件类型
      md.MediaType mediaType =
          isVideo ? md.MediaType.video : md.MediaType.image;

      // 创建表单数据
      FormData postData = FormData.fromMap({
        "file": MultipartFile.fromBytes(bytes,
            filename: file.name,
            contentType:
                MediaType(mimeType.split("/").first, mimeType.split("/").last))
      });

      // 上传文件到服务器
      var updateFileResult =
          await AgriculturalRecordsService().uploadFile(data: postData);

      if (!field.context.mounted) {
        return;
      }

      TDToast.dismissLoading();

      if (updateFileResult.success != null) {
        // 添加到列表
        md.MediaItem newItem = md.MediaItem(
          url: updateFileResult.data!,
          type: mediaType,
        );

        field.value!.add(newItem);
        field.didChange(field.value);

        // 仅提供URL列表给外部
        List<String> urls = field.value!.map((item) => item.url).toList();
        onChange?.call(urls);
      }
    } catch (e) {
      Log.e("文件上传错误: $e");
      TDToast.dismissLoading();
      showToast("上传失败:");
    }
  }
}
