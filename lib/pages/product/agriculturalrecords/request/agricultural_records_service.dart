import 'package:bdh_smart_agric_app/model/dict_list_model.dart';
import 'package:bdh_smart_agric_app/model/request_no_data.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_api.dart';
import 'package:dio/dio.dart';

class AgriculturalRecordsService {
  const AgriculturalRecordsService._();

  static const AgriculturalRecordsService _instance =
      AgriculturalRecordsService._();

  factory AgriculturalRecordsService() => _instance;

  // queryToBeRecordedLand: (params, config = {}) => http.post('/digitalBDH/myLand/queryToBeRecordedLand', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 待记地块查询接口
  Future<RequestNoData> queryToBeRecordedLand(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryToBeRecordedLand",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	getDicts: (params, config = {}) => http.post('/client/dict/list/'+params , params, {custom: {API_URL: 'FARMFEEL_API'}}),//字典
  Future<DictList> getDict({Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/client/dict/list/$data", data: null, cancelToken: cancelToken)
        .then<DictList>((result) => DictList.fromJson(result.data));
  }

  Future<DictList> getCostanalysisDict(
      {Object? data, required CancelToken cancelToken}) {
    return costanalysisHttp
        .post("/client/dict/list/$data", data: null, cancelToken: cancelToken)
        .then<DictList>((result) => DictList.fromJson(result.data));
  }

  // 	queryToBeRecordedLink: (params, config = {}) => http.post('/digitalBDH/myLand/queryToBeRecordedLink', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 查询地块待记录环节
  Future<RequestNoData> queryToBeRecordedLink(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryToBeRecordedLink",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	dimGrowPatterns: (params, config = {}) => http.post('/farmingManagement/dimGrowPatterns/queryAll', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 查询种植方案
  Future<RequestNoData> dimGrowPatterns(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/farmingManagement/dimGrowPatterns/queryAll",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryAllCrop: (params, config = {}) => http.post('/farmingManagement/dimGrowPatterns/queryAllCrop', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 查询所有作物,
  Future<RequestNoData> queryAllCrop(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/farmingManagement/dimGrowPatterns/queryAllCrop",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryAllCropVariety: (params, config = {}) => http.post('/farmingManagement/dimGrowPatterns/queryAllCropVariety', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 查询作物下面品种
  Future<RequestNoData> queryAllCropVariety(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/farmingManagement/dimGrowPatterns/queryAllCropVariety",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	previewGrowPatterns: (params, config = {}) => http.post('/digitalBDH/myLand/previewGrowPatterns', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 预览种植方案
  Future<RequestNoData> previewGrowPatterns(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/previewGrowPatterns",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	bindingGrowPatterns: (params, config = {}) => http.post('/digitalBDH/myLand/bindingGrowPatterns', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 绑定种植方案
  Future<RequestNoData> bindingGrowPatterns(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/bindingGrowPatterns",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryLandList: (params, config = {}) => http.post('/digitalBDH/myLand/queryLandList', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 地块列表查询接口
  Future<RequestNoData> queryLandList(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryLandList",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	recordFarmingEnd: (params, config = {}) => http.post('/digitalBDH/myLand/recordFarmingEnd', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 结束农事
  Future<RequestNoData> recordFarmingEnd(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/recordFarmingEnd",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	landNotRecorded: (params, config = {}) => http.post('/digitalBDH/myLand/landNotRecorded', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 未记录
  Future<RequestNoData> landNotRecorded(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/landNotRecorded",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	landRecorded: (params, config = {}) => http.post('/digitalBDH/myLand/landRecorded', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 已记录
  Future<RequestNoData> landRecorded(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/landRecorded",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	asLinkProgressLndmyInfo:(params, config = {}) => http.post('/digitalBDH/myLand/recordFarmingElement', params,{custom: {API_URL: 'FARMFEEL_API'}}),
  Future<RequestNoData> asLinkProgressLndmyInfo(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/recordFarmingElement",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	saveRecordFarming:(params, config = {}) => http.post('/digitalBDH/myLand/recordFarming', params,{custom: {API_URL: 'FARMFEEL_API'}}),
  Future<RequestNoData> saveRecordFarming(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/recordFarming",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryRecordFarming:(params, config = {}) => http.post('/digitalBDH/myLand/queryRecordFarming', params,{custom: {API_URL: 'FARMFEEL_API'}}),
  Future<RequestNoData> queryRecordFarming(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryRecordFarming",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryAllGrow:(params, config = {}) => http.post('/digitalBDH/myLand/queryAllGrow', params,{custom: {API_URL: 'FARMFEEL_API'}}),// 批量记录—查询现有绑定种植方案（年份、组织机构、作物）
  Future<RequestNoData> queryAllGrow(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryAllGrow",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryAllPlot:(params, config = {}) => http.post('/digitalBDH/myLand/queryAllPlot', params,{custom: {API_URL: 'FARMFEEL_API'}}),// 批量记录—地块查询接口（年份、组织机构、种植方案id）
  Future<RequestNoData> queryAllPlot(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryAllPlot",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryAllRecordedLink:(params, config = {}) => http.post('/digitalBDH/myLand/queryAllRecordedLink', params,{custom: {API_URL: 'FARMFEEL_API'}}),// 批量记录—查询地块公共待记录或未结束环节
  Future<RequestNoData> queryAllRecordedLink(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryAllRecordedLink",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	recordAllFarming:(params, config = {}) => http.post('/digitalBDH/myLand/recordAllFarming', params,{custom: {API_URL: 'FARMFEEL_API'}}),// 批量记录——记录我的农事(保存农事)
  Future<RequestNoData> recordAllFarming(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/recordAllFarming",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	recordAllFarmingEnd:(params, config = {}) => http.post('/digitalBDH/myLand/recordAllFarmingEnd', params,{custom: {API_URL: 'FARMFEEL_API'}}),// 批量记录——结束我的农事
  Future<RequestNoData> recordAllFarmingEnd(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/recordAllFarmingEnd",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	getOrgCode:(params, config = {}) => http.post('/org/amporg/queryOrgTreeByUserOrg', params, {custom: {API_URL: 'FARMFEEL_API'}}),
  Future<RequestNoData> getOrgCode(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/org/amporg/queryOrgTreeByUserOrg",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryRecordAndLandCount:(params, config = {}) => http.post('/digitalBDH/myLand/queryRecordAndLandCount', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 待记和地块合并统计
  Future<RequestNoData> queryRecordAndLandCount(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryRecordAndLandCount",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryAllGrowLink:(params, config = {}) => http.post('/digitalBDH/myLand/queryAllGrowLink', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 查询作业环节
  Future<RequestNoData> queryAllGrowLink(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryAllGrowLink",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryRightfulVerf:(params, config = {}) => http.post('/digitalBDH/myLand/queryRightfulVerf', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 查询作业时间范围
  Future<RequestNoData> queryRightfulVerf(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryRightfulVerf",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryLinkLimit:(params, config = {}) => http.post('/digitalBDH/myLand/queryLinkLimit', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 批量记录(批量结束)—查询环节限制时间
  Future<RequestNoData> queryLinkLimit(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryLinkLimit",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryAllNotEndPlot:(params, config = {}) => http.post('/digitalBDH/myLand/queryAllNotEndPlot', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 批量记录(批量结束)——地块查询接口
  Future<RequestNoData> queryAllNotEndPlot(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryAllNotEndPlot",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	batchRecordFarmingEnd:(params, config = {}) => http.post('/digitalBDH/myLand/batchRecordFarmingEnd', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 批量记录(批量结束)—批量结束
  Future<RequestNoData> batchRecordFarmingEnd(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/batchRecordFarmingEnd",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	encryptQRCodeUrl:(params, config = {}) => http.post('/digitalBDH/myLand/encryptQRCodeUrl', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 种植档案获url
  Future<RequestNoData> encryptQRCodeUrl(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/encryptQRCodeUrl",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryElementItem:(params, config = {}) => http.post('/digitalBDH/myLand/queryElementItem', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 批量记录--查询因子配置项
  Future<RequestNoData> queryElementItem(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryElementItem",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  // 	queryRightfulVerfEnd:(params, config = {}) => http.post('/digitalBDH/myLand/queryRightfulVerfEnd', params, {custom: {API_URL: 'FARMFEEL_API'}}), // 批量结束-查询当前农事环节可选的时间区间
  Future<RequestNoData> queryRightfulVerfEnd(
      {Object? data, required CancelToken cancelToken}) {
    return farmfellHttp
        .post("/digitalBDH/myLand/queryRightfulVerfEnd",
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> uploadFile({Object? data, CancelToken? cancelToken}) {
    return farmfellHttp
        .post("/file/upload", data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> queryOutNum({Object? data, CancelToken? cancelToken}) {
    return farmfellHttp
        .post('/digitalBDH/myLand/queryOutNum',
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> queryGeomInfo(
      {Object? data, CancelToken? cancelToken}) {
    return farmfellHttp
        .post('/digitalBDH/myLand/queryGeomInfo',
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }

  Future<RequestNoData> queryMeansType(
      {Object? data, CancelToken? cancelToken}) {
    return expotrHttp
        .post('/situation/asMpActUseLnd/queryMeansType',
            data: data, cancelToken: cancelToken)
        .then<RequestNoData>((result) => RequestNoData.fromJson(result.data));
  }
}
