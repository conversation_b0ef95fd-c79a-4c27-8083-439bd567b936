import 'package:bdh_smart_agric_app/components/compass.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/gps/gps_receiver.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/permission_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/tile_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;

//显示两个地点的距离
class MapDistancePreview extends StatefulWidget {
  final LatLng? initLocate;
  final LatLng targetLocate;
  final String locationMessage;
  final Map<String, dynamic>? geoJson;
  const MapDistancePreview(
      {super.key,
      this.initLocate,
      this.locationMessage = "需要您开启定位信息才能继续",
      required this.targetLocate,
      this.geoJson});

  @override
  State<StatefulWidget> createState() => MapDistancePreviewState();
}

class MapDistancePreviewState extends State<MapDistancePreview> {
  var bounds = LatLngBounds(const LatLng(44, 121), const LatLng(52, 134));
  var flags = InteractiveFlag.all - InteractiveFlag.rotate;
  LatLng? currentLatLng;

  List<Polygon> _polygons = [];
  MapController mapController = MapController();

  bool showLoading = true;
  LatLng? location;

  final Distance distance = const Distance();
  double distanceKM = 0;
  @override
  void initState() {
    super.initState();
    if (widget.initLocate != null) {
      location = widget.initLocate;
      calculate();
    } else {
      PermissionUtil.requestLocationPermission(context, widget.locationMessage)
          .then((result) {
        if (!mounted) {
          return;
        }
        if (result) {
          LocationResult? locationResult =
              GpsReceiver.getInstance().locationResult;
          //没有取得过任何定位数据需要等待
          if (locationResult == null) {
            bus.on("location", busLocation);
            bus.on("locationError", busLocationError);
          } else {
            location = LatLng((locationResult.latitude ?? 0).toDouble(),
                (locationResult.longitude ?? 0).toDouble());
            calculate();
          }
        } else {
          Navigator.of(context).pop();
        }
      });
    }
  }

  void calculate() {
    //区域地块
    geoJsonParse();

    // 当前位置到地块的距离
    distanceKM =
        distance.as(LengthUnit.Kilometer, location!, widget.targetLocate);
    setState(() {
      showLoading = false;
    });
  }

  // 区域地块
  void geoJsonParse() {
    if (widget.geoJson == null) {
      return;
    }
    try {
      final parsed = widget.geoJson!;
      final coordinates = parsed['coordinates'] as List;

      // 存储所有多边形的点集合
      List<List<LatLng>> allPolygonPoints = [];

      // 根据coordinates数组长度进行不同处理
      if (coordinates.length == 1) {
        // 单一图层情况
        var polygonCoords = [];
        if (coordinates[0].length == 1) {
          polygonCoords = coordinates[0][0];
        } else {
          polygonCoords = coordinates[0];
        }
        List<LatLng> polygonPoints = _extractPoints(polygonCoords);
        if (polygonPoints.isNotEmpty) {
          allPolygonPoints.add(polygonPoints);
        }
      } else if (coordinates.length > 1) {
        // 多图层情况
        for (int i = 0; i < coordinates.length; i++) {
          final polygonCoords = coordinates[i][0]; // 每个图层取第一个子元素
          List<LatLng> polygonPoints = _extractPoints(polygonCoords);
          if (polygonPoints.isNotEmpty) {
            allPolygonPoints.add(polygonPoints);
          }
        }
      }

      Log.d("allPolygonPoints $allPolygonPoints");
      // 创建不同颜色的多边形列表
      List<Polygon> polygons = [];
      List<Color> colors = [
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
        Colors.green.withOpacity(0.4),
      ];

      for (int i = 0; i < allPolygonPoints.length; i++) {
        Color fillColor = colors[i % colors.length];
        Color borderColor = fillColor.withOpacity(0.5);
        polygons.add(Polygon(
          points: allPolygonPoints[i],
          color: fillColor,
          borderColor: borderColor,
          borderStrokeWidth: 2,
        ));
      }
      _polygons = polygons;
    } catch (e, stack) {
      Log.e('GeoJSON处理失败:', error: e, stackTrace: stack);
    }
  }

  // 从坐标数组中提取点
  List<LatLng> _extractPoints(List polygonCoords) {
    List<LatLng> result = [];

    for (var coord in polygonCoords) {
      if (coord is List && coord.length >= 2) {
        final lat = (coord[0] as num).toDouble();
        final lng = (coord[1] as num).toDouble();
        final point = LatLng(lng, lat);
        result.add(point);
      }
    }

    return result;
  }

  @override
  void dispose() {
    super.dispose();
    bus.off("location", busLocation);
    bus.off("locationError", busLocationError);
  }

  void busLocation(e) {
    if (!mounted) {
      return;
    }
    LocationResult locationResult = e as LocationResult;
    location = LatLng((locationResult.latitude ?? 0).toDouble(),
        (locationResult.longitude ?? 0).toDouble());
    setState(() {
      showLoading = false;
    });
  }

  void busLocationError(e, {bool force = false}) {
    if (!mounted) {
      return;
    }
    Log.d("busLocationError : $e");
  }

  Widget _widgetLoading() {
    return const Center(
      child: ViewStateBusyWidget(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("地图"),
      ),
      body: showLoading
          ? _widgetLoading()
          : FlutterMap(
              mapController: mapController,
              options: MapOptions(
                  initialCenter: location!,
                  initialZoom: 13,
                  interactionOptions: InteractionOptions(flags: flags),
                  onMapEvent: (e) {
                    // setState(() {
                    //   currentLatLng = e.camera.center;
                    // });
                  },
                  onMapReady: () {
                    // Future.delayed(Duration(milliseconds: 500), () {
                    //   mapController.move(location!, 18);
                    // });
                  }),
              children: [
                  TileLayerUtil.tileLayer(TianDiTuType.bdh),
                  TileLayerUtil.tileLayer(TianDiTuType.cia),
                  PolygonLayer(polygons: _polygons),
                  PolylineLayer(
                    polylines: [
                      Polyline(
                          points: [location!, widget.targetLocate],
                          color: Colors.yellow,
                          strokeWidth: 3,
                          pattern: StrokePattern.dotted(
                            spacingFactor: 1.5.px,
                          ))
                    ],
                  ),
                  MarkerLayer(markers: [
                    Marker(
                        width: 40.px,
                        height: 40.px,
                        point: location!,
                        child: const CompassView()),
                    Marker(
                        width: 40.px,
                        height: 80.px,
                        point: widget.targetLocate,
                        child: Padding(
                            padding: EdgeInsets.only(bottom: 40.px),
                            child: Icon(
                              Icons.location_on,
                              color: Colors.red,
                              size: 40.px,
                            ))),
                    Marker(
                        width: 200.px,
                        height: 200.px,
                        point: widget.targetLocate,
                        child: Padding(
                            padding: EdgeInsets.only(bottom: 120.px),
                            child: Text(
                              "地块\n经度：${widget.targetLocate.longitude}\n纬度：${widget.targetLocate.latitude}",
                              textAlign: TextAlign.center,
                              style: const TextStyle(color: Colors.white),
                            ))),
                    Marker(
                        width: 200.px,
                        height: 80.px,
                        point: LatLng(
                          (widget.targetLocate.latitude + location!.latitude) /
                                  2 +
                              (widget.targetLocate.latitude -
                                          location!.latitude)
                                      .abs() /
                                  4,
                          (widget.targetLocate.longitude +
                                      location!.longitude) /
                                  2 +
                              (widget.targetLocate.longitude -
                                          location!.longitude)
                                      .abs() /
                                  4,
                        ),
                        child: Text(
                          "直线距离\n${distanceKM}km",
                          textAlign: TextAlign.center,
                          style: const TextStyle(color: Colors.white),
                        )),
                  ])
                ]),
    );
  }
}
