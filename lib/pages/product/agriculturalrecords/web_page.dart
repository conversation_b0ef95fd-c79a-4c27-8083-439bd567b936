import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';

class WebPage extends StatefulWidget {
  final String url;
  final String title;
  const WebPage({super.key, required this.url, required this.title});

  @override
  State<StatefulWidget> createState() => _State();
}

class _State extends State<WebPage> {
  bool isLoading = true;
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(widget.title),
        ),
        backgroundColor: Colors.black,
        body: SafeArea(
            child: Stack(
          children: [
            Visibility(
                maintainState: true,
                visible: !isLoading,
                child: InAppWebView(
                  initialSettings: InAppWebViewSettings(
                    allowsInlineMediaPlayback: true,
                    allowsAirPlayForMediaPlayback: false,
                    allowsPictureInPictureMediaPlayback: false,
                    allowsLinkPreview: false,
                    mediaPlaybackRequiresUserGesture: false,
                  ),
                  initialUrlRequest: URLRequest(url: WebUri(widget.url)),
                  onCloseWindow: (controller) {
                    Navigator.of(context).pop();
                  },
                  onLoadStart: (controller, url) {
                    Log.d('url onLoadStart $url');
                  },
                  onLoadStop: (controller, url) {
                    Log.d('url onLoadStop $url');
                    setState(() {
                      isLoading = false;
                    });
                  },
                  onEnterFullscreen: (controller) {
                    Log.d('url onEnterFullscreen');
                  },
                  onExitFullscreen: (controller) {
                    Log.d('url onExitFullscreen');
                  },
                  gestureRecognizers: <Factory<OneSequenceGestureRecognizer>>{
                    Factory<VerticalDragGestureRecognizer>(
                        () => VerticalDragGestureRecognizer()),
                    Factory<HorizontalDragGestureRecognizer>(
                        () => HorizontalDragGestureRecognizer()),
                  },
                )),
            Visibility(
                visible: isLoading,
                child: Center(
                  child: CircularProgressIndicator(
                    color: Colors.green,
                    strokeWidth: 1.px,
                  ),
                ))
          ],
        )));
  }
}
