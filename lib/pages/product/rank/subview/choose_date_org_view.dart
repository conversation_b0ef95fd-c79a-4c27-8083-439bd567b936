import 'package:bdh_smart_agric_app/components/jh_cascade_tree_picker.dart';
import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';
import 'package:oktoast/oktoast.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

// ignore: must_be_immutable
class ChooseDateAndOrgView extends StatefulWidget {
  OrgTreeResult? treeResult;
  Function(String seletedData) clickedChooseDataAction;
  Function(String orgCode) clickedChooseOrgAction;
  ChooseDateAndOrgView(
      {super.key,
      required this.treeResult,
      required this.clickedChooseDataAction,
      required this.clickedChooseOrgAction});

  @override
  State<ChooseDateAndOrgView> createState() => _ChooseDateAndOrgViewState();
}

class _ChooseDateAndOrgViewState extends State<ChooseDateAndOrgView> {
  String orgName = '请选择所在单位';
  String currentYear = '2025';
  showBottomMultiSelectPicker(BuildContext context) {
    var tempData = [];
    for (var e in widget.treeResult!.data!) {
      tempData.add(e.toJson());
    }
    JhCascadeTreePicker.show(context,
        data: tempData,
        valueKey: "orgCode",
        labelKey: "orgName",
        childrenKey: "list",
        clickCallBack: (_, __) {}, ensureCallBack: (res, ress) {
      String orgCode = res['orgCode'];
      if (orgCode.length < 10) {
        showToast('组织请选择到管理区');
      } else {
        widget.clickedChooseOrgAction(orgCode);
        setState(() {
          orgName = res['orgName'];
        });
      }
      Logger().i(' widget.model.currentOrg=$res----$ress');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        GestureDetector(
          onTap: () {
            // Logger().i(' widget.model.currentOrg=');
            showBottomMultiSelectPicker(context);
          },
          child: Container(
            height: 50.px,
            width: 230.px,
            padding: EdgeInsets.only(
                left: 12.px, right: 12.px, top: 13.px, bottom: 13.px),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(8.px)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  // '请选择所在单位',
                  orgName,
                  style: TextStyle(
                      fontSize: 14.px,
                      fontWeight: FontWeight.w600,
                      color: const Color.fromRGBO(0, 0, 0, 0.4)),
                ),
                Image.asset(ImageHelper.wrapAssets('arrow_right.png'))
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            return TDPicker.showDatePicker(context, title: '选择时间',
                onConfirm: (selected) {
              String year = selected['year'].toString();
              setState(() {
                currentYear = year;
              });
              // currentModel?.year = year;
              // currentModel?.initData();
              widget.clickedChooseDataAction(year);
              Logger().i('$selected-----$year');
              Navigator.of(context).pop();
            }, useDay: false, useMonth: false, dateStart: [1999, 01, 01]);
            // dateEnd: [2023, 12, 31],
            // initialDate: [2012, 1, 1]);
          },
          child: Container(
            height: 50.px,
            width: 100.px,
            padding: EdgeInsets.only(
                left: 12.px, right: 12.px, top: 13.px, bottom: 13.px),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(8.px)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  // '2005',
                  currentYear,
                  style: TextStyle(
                      fontSize: 14.px,
                      fontWeight: FontWeight.w600,
                      color: const Color.fromRGBO(0, 0, 0, 1)),
                ),
                Image.asset(ImageHelper.wrapAssets('arrow_right.png'))
              ],
            ),
          ),
        ),
      ],
    );
  }
}
