// ignore: must_be_immutable
import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_model.dart';
// import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/subview/rank_choose_tabview.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:logger/logger.dart';

// ignore: must_be_immutable
class RankItemCellView extends StatefulWidget {
  RankRecord itemModel;
  RankItemCellView({super.key, required this.itemModel});

  @override
  State<RankItemCellView> createState() => _RankItemCellViewState();
}

class _RankItemCellViewState extends State<RankItemCellView> {
  List<RankBdhDigitalItemModel> tabList = [];
  @override
  void initState() {
    super.initState();
  }

// buildItem(SubjectElement model) {
  buildItem(String leftTitle, String rightTitle, Color? customColor) {
    return Container(
      width: 240.px,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // const SizedBox(width: 3),
          Expanded(
            flex: 1,
            child: Container(
              padding: EdgeInsets.only(top: 10.px, bottom: 10.px, left: 5.px),
              decoration: const BoxDecoration(
                border: Border(
                  left: BorderSide(
                    width: 1,
                    color: Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                  right: BorderSide(
                    width: 1,
                    color: Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                  top: BorderSide(
                    width: 1,
                    color: Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                  bottom: BorderSide(
                    width: 1,
                    color: Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                ),
              ),
              child: Center(
                child: Text(
                  leftTitle,
                  // model.subject ?? '',
                  style: TextStyle(
                      fontSize: 14.px,
                      fontWeight: FontWeight.w500,
                      color: customColor ?? Colors.black),
                ),
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(
              padding: EdgeInsets.only(top: 10.px, bottom: 10.px, left: 5.px),
              decoration: const BoxDecoration(
                border: Border(
                  left: BorderSide(
                    width: 1,
                    color: Color.fromRGBO(0, 0, 0, 0.001),
                  ),
                  right: BorderSide(
                    width: 1,
                    color: Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                  top: BorderSide(
                    width: 1,
                    color: Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                  bottom: BorderSide(
                    width: 1,
                    color: Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                ),
              ),
              child: Center(
                child: Text(
                  rightTitle,
                  // (model.score ?? '').toString(),
                  style: TextStyle(
                    fontSize: 14.px,
                    fontWeight: FontWeight.w500,
                    color: customColor ?? const Color.fromRGBO(47, 149, 255, 1),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> buildList(RankRecord itemModel) {
    List<Widget> tempList = [];
    tempList.add(buildItem('项目', '项目得分', const Color.fromRGBO(0, 0, 0, 0.4)));
    for (int i = 0; i < itemModel.subjects!.length; i++) {
      SubjectElement model = itemModel.subjects![i];
      String scoreString = (model.score ?? '').toString();
      tempList.add(buildItem(
          model.subject ?? '', scoreString == '0' ? '0.0' : scoreString, null));
    }
    return tempList;
  }

  Widget scoreItemView(String title, String iconName, String score) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Image.asset(
                width: 24.px, height: 24.px, ImageHelper.wrapAssets(iconName)),
            SizedBox(width: 3.px),
            Text(
              title,
              style: TextStyle(
                  fontSize: 14.px,
                  fontWeight: FontWeight.w500,
                  color: Colors.black),
            ),
          ],
        ),
        Container(
          margin: EdgeInsets.only(top: 6.px),
          child: Text(
            score,
            style: TextStyle(
                height: 1,
                fontSize: 32.px,
                fontWeight: FontWeight.w500,
                color: const Color.fromRGBO(47, 149, 255, 1)),
          ),
        )
      ],
    );
  }

  String maskString(String str, int startKeep, int endKeep) {
    if (str.length <= startKeep + endKeep) return str;

    return str.substring(0, startKeep) +
        '*' * (str.length - startKeep - endKeep) +
        str.substring(str.length - endKeep);
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          padding: EdgeInsets.only(bottom: 15.px, top: 10.px),
          // height: 136.px,
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(8.px)),
          child: ExpansionTile(
            shape: const Border(),
            tilePadding: EdgeInsets.zero,
            // collapsedShape: const Border(),
            backgroundColor: Colors.transparent,
            collapsedBackgroundColor: Colors.transparent,
            showTrailingIcon: false,
            title: Container(
              width: 375.px,
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.only(bottom: 3.px),
                    height: 30.px,
                    width: 305.px,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          widget.itemModel.farmerName ?? '',
                          style: TextStyle(
                              fontSize: 16.px,
                              fontWeight: FontWeight.w500,
                              color: Colors.black),
                        ),
                        Text(maskString(widget.itemModel.idNum ?? '', 2, 4),
                            // itemModel.idNum ?? '',
                            style: TextStyle(
                                fontSize: 16.px,
                                fontWeight: FontWeight.w500,
                                color: Colors.black)),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(bottom: 15.px),
                    height: 1,
                    width: 305.px,
                    color: const Color.fromRGBO(0, 0, 0, 0.05),
                  ),
                  Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        scoreItemView('得分', 'rankScoreImg.png',
                            '${widget.itemModel.score}'),
                        Container(
                            margin: EdgeInsets.only(left: 35.px, right: 35.px),
                            height: 60.px,
                            width: 1,
                            color: const Color.fromRGBO(0, 0, 0, 0.05)),
                        Stack(
                          clipBehavior: Clip.none,
                          children: [
                            scoreItemView('排行', 'rankSubImg.png',
                                widget.itemModel.rank ?? '0'),
                            Positioned(
                                right: -40.px,
                                child: Image.asset(
                                    width: 20.px,
                                    height: 20.px,
                                    ImageHelper.wrapAssets(
                                        widget.itemModel.open == true
                                            ? 'rankUp.png'
                                            : 'rankDown.png')))
                          ],
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            onExpansionChanged: (res) {
              Logger().i('res=$res');
              setState(() {
                widget.itemModel.open = res;
              });
            },
            // leading: const Icon(Icons.info),
            children: buildList(widget.itemModel),
          ),
        ),
        Positioned(
            top: (136.px - 72.px) * 0.5,
            child: Image.asset(
                width: 8.px,
                height: 72.px,
                ImageHelper.wrapAssets('rankItemLeftImg.png')))
      ],
    );
  } //end build
}
