// import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_page.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class RankChooseTabView extends StatefulWidget {
  Function(int index) clickedTabAction;
  RankChooseTabView({super.key, required this.clickedTabAction});

  @override
  State<RankChooseTabView> createState() => _RankChooseTabViewState();
}

class _RankChooseTabViewState extends State<RankChooseTabView> {
  List<RankBdhDigitalItemModel> tabList = [];
  @override
  void initState() {
    super.initState();
    tabList = [
      RankBdhDigitalItemModel(name: '水田', isSelected: true, index: 0),
      RankBdhDigitalItemModel(name: '旱田', isSelected: false, index: 1),
    ];
  }

  dealWithSelectedReadStatusWithModel(RankBdhDigitalItemModel model) {
    for (int i = 0; i < tabList.length; i++) {
      RankBdhDigitalItemModel item = tabList[i];
      if (item.name == model.name) {
        item.isSelected = true;
      } else {
        item.isSelected = false;
      }
    }
    setState(() {});
  }

  Widget getItem(RankBdhDigitalItemModel model) {
    return GestureDetector(
      onTap: () {
        dealWithSelectedReadStatusWithModel(model);
        widget.clickedTabAction(model.index);
      },
      child: Container(
        height: 48.px,
        width: 170.px,
        decoration: BoxDecoration(
          gradient: model.isSelected
              ? const LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Color.fromRGBO(0, 127, 255, 1),
                    Color.fromRGBO(61, 156, 255, 1)
                  ],
                )
              : const LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: [
                    Colors.white,
                    Colors.white,
                  ],
                ),
          borderRadius: BorderRadius.circular(8.px),
        ),
        child: Align(
          alignment: Alignment.center,
          child: Text(
            model.name,
            style: TextStyle(
                color: model.isSelected
                    ? Colors.white
                    : const Color.fromRGBO(0, 0, 0, 0.4),
                fontSize: 16.px,
                fontWeight: FontWeight.w500),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 12.px),
      height: 48.px,
      width: 375.px,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.px),
        color: Colors.white,
      ),
      child: Row(
        children: tabList.map((e) => getItem(e)).toList(),
      ),
    );
  }
}

class RankBdhDigitalItemModel {
  int index;
  String name;
  bool isSelected;
  RankBdhDigitalItemModel({
    required this.name,
    required this.isSelected,
    required this.index,
  });
}
