import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:oktoast/oktoast.dart';

// ignore: must_be_immutable
class RankSearchView extends StatefulWidget {
  TextEditingController searchController;
  FocusScopeNode focusScopeNode;
  Function(String content) clickedSeachBtn;
  Function() clickedClearBtn;
  RankSearchView({
    super.key,
    required this.searchController,
    required this.focusScopeNode,
    required this.clickedSeachBtn,
    required this.clickedClearBtn,
  });

  @override
  State<RankSearchView> createState() => _RankSearchViewState();
}

class _RankSearchViewState extends State<RankSearchView> {
  bool showDeletIcon = false;

  @override
  void initState() {
    super.initState();
    widget.searchController.addListener(() {
      setState(() {
        showDeletIcon = widget.searchController.text.isNotEmpty ? true : false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 12.px, bottom: 12.px),
      padding: EdgeInsets.only(left: 12.px, right: 12.px),
      height: 38.px,
      width: 375.px,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.px),
        color: Colors.white,
      ),
      child: Row(
        children: [
          Expanded(
            child: SearchCustomTextField(
              searchController: widget.searchController,
              clickedKeyBoradSearch: (e) {
                FocusScope.of(context).unfocus();
                widget.clickedSeachBtn(e);
              },
            ),
          ),
          SizedBox(width: 5.px),
          showDeletIcon
              ? GestureDetector(
                  onTap: () {
                    widget.searchController.text = '';
                    widget.clickedClearBtn();
                  },
                  child: Image.asset(
                      width: 16.px,
                      height: 16.px,
                      ImageHelper.wrapAssets('delteIcons.png')),
                )
              : Container(),
          GestureDetector(
            onTap: () {
              if (widget.searchController.text.isEmpty) {
                showToast('请输入搜索内容');
                return;
              }
              FocusScope.of(context).unfocus();
              widget.clickedSeachBtn(widget.searchController.text);
            },
            child: Container(
              padding: EdgeInsets.only(left: 15.px, right: 1.px),
              child: Image.asset(
                width: 24.px,
                height: 24.px,
                ImageHelper.wrapAssets('searchIconBDHDigital.png'),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// ignore: must_be_immutable
class SearchCustomTextField extends StatefulWidget {
  late TextEditingController searchController;
  Function(String content) clickedKeyBoradSearch;

  SearchCustomTextField(
      {super.key,
      required this.searchController,
      required this.clickedKeyBoradSearch});

  @override
  // ignore: library_private_types_in_public_api
  _MyTextFieldState createState() => _MyTextFieldState();
}

class _MyTextFieldState extends State<SearchCustomTextField> {
  final FocusNode _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 确保焦点和键盘同时处理
        if (!_focusNode.hasFocus) {
          _focusNode.requestFocus();
        }
      },
      child: CupertinoTextField.borderless(
        style: TextStyle(
          fontSize: 14.px,
          color: HexColor('#000000'),
          fontWeight: FontWeight.w400,
        ),
        textInputAction: TextInputAction.search,
        padding: EdgeInsets.zero,
        controller: widget.searchController,
        placeholder: "请输入农户姓名或身份证号",
        placeholderStyle: TextStyle(
            fontSize: 16.px,
            fontWeight: FontWeight.w500,
            color: HexColor('#A8B8B1')),
        focusNode: _focusNode,
        // autofocus: true,
        // onTapOutside: (e) => {focusNode.unfocus()},
        // onChanged: (e) => {},
        onEditingComplete: () {
          if (widget.searchController.text.isEmpty) {
            showToast('请输入农户姓名或身份证号');
            return;
          }
          widget.clickedKeyBoradSearch(widget.searchController.text);
          _focusNode.unfocus();
        },
      ),
    );
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }
}
