import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:flutter/cupertino.dart';

class RankBdhDigitalProvider extends ChangeNotifier {
  // String _orgCode = '**********';
  String _orgCode = '86';
  String _year = '2025';
  int _landType = 0; //旱田 0 ,水田 1 数字类型

  bool _isLoading = false;
  bool _hasMore = true;
  String? _errorMessage;
  List listData = [];
  static const int pageNumFirst = 1;
  static const int pageSize = 10;
  int _currentPageNum = pageNumFirst;

//
  Future<List<RankRecord>?> loadData({int? pageNum}) async {
    _setLoading(true);
    var items = await BdhDigitalService.getRankBdhDigital({
      // "orgCode": "**********",
      "orgCode": orgCode,
      "year": year,
      "landType": landType == 0 ? '水田' : '旱田',
      "page": pageNum,
      "rows": 10,
    });
    return items.data?.records;
  }

  Future<void> onRefresh() async {
    try {
      _currentPageNum = 1;
      var data = await loadData(pageNum: _currentPageNum);
      _setHasMore(true);
      listData.clear();
      listData.addAll(data!);
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  Future<void> loadMore() async {
    if (!_hasMore || _isLoading) return;
    try {
      _currentPageNum += 1;
      var data = await loadData(pageNum: _currentPageNum);
      if ((data ?? []).isEmpty) {
        _currentPageNum--;
        // _currentPageNum = pageNumFirst;
        _setHasMore(false);
      } else {
        listData.addAll(data!);
        _setHasMore(true);
      }
    } catch (e) {
      _setError(e.toString());
    } finally {
      _setLoading(false);
      notifyListeners();
    }
  }

  reSet() {
    _currentPageNum = pageNumFirst;
    listData.clear();
  }

//--------get methord----------------------------
  String get orgCode => _orgCode;
  String get year => _year;
  int get landType => _landType;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get hasMore => _hasMore;

//--------set methord----------------------------
  //设置类型
  void setOrgCode(String orgCode) {
    _orgCode = orgCode;
  }

  void _setHasMore(bool hasMore) {
    _hasMore = hasMore;
  }

  void setYear(String year) {
    _year = year;
  }

  void setLandType(int landType) {
    _landType = landType;
  }

  void _setLoading(bool isLoading) {
    if (_isLoading != isLoading) {
      _isLoading = isLoading;
    }
  }

  void _setError(String error) {
    _errorMessage = error;
  }

  void _clearError() {
    _errorMessage = null;
  }
}
