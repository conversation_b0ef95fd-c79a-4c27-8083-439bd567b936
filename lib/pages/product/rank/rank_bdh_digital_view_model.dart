import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_refresh_list_model.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_digital/bdh_digital_service.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';

class RankBdhDigitalViewModel extends ViewStateRefreshListModel {
  String orgCode = StorageUtil.orgCode() ?? '86';
  String year = '2025';
  int landType = 0; //旱田 0 ,水田 1 数字类型
  String nameOrIdNumber = '';

  @override
  Future<List<RankRecord>> loadData({int? pageNum}) async {
    var items = await BdhDigitalService.getRankBdhDigital({
      // "orgCode": "**********",
      "orgCode": orgCode,
      "year": year,
      "landType": landType == 0 ? '水田' : '旱田',
      "page": pageNum,
      "rows": 10,
      "nameOrIdNumber": nameOrIdNumber,
    });
    return items.data?.records ?? [];
  }
}
