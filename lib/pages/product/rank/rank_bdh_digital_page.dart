import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_view_model.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/subview/choose_date_org_view.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/subview/rank_choose_tabview.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/subview/rank_item_cell_view.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/subview/rank_search_view.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/provider_widget.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/request/land_contract_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class RankBdhDigitalPage extends StatefulWidget {
  const RankBdhDigitalPage({super.key});

  @override
  State<RankBdhDigitalPage> createState() => _RankBdhDigitalPageState();
}

class _RankBdhDigitalPageState extends State<RankBdhDigitalPage>
    with TickerProviderStateMixin {
  RankBdhDigitalViewModel? currentModel;
  TextEditingController searchController = TextEditingController();
  final FocusScopeNode focusScopeNode = FocusScopeNode();

  OrgTreeResult? treeResult;
  bool loadingChooseOrgView = true;
  @override
  void initState() {
    super.initState();
    loadOrg();
  }

  // 加载组织机构
  // loadOrg() {
  //   LandResponsitory.getOrgData().then((res) {
  //     setState(() {
  //       treeResult = res;
  //     });
  //   });
  // }

  loadOrg() {
    String orgCode = StorageUtil.orgCode() ?? '86';
    loadingChooseOrgView = true;
    BDHResponsitory.getOrgTree({
      "orgLevelList": [1, 2, 3, 4],
      'orgCode': orgCode,
    }).then((res) {
      setState(() {
        loadingChooseOrgView = false;
        treeResult = res;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget(
      builder: (BuildContext context, ChangeNotifier model, child) {
        model as RankBdhDigitalViewModel;
        return Scaffold(
          backgroundColor: HexColor("#F3F5F9"),
          appBar: AppBar(
            title: const Text('评价公示'),
          ),
          body: Container(
            padding: EdgeInsets.only(
                left: 16.px, right: 16.px, top: 10.px, bottom: 15.px),
            child: Column(
              children: [
                //日期+org
                loadingChooseOrgView
                    ? const TDLoading(
                        size: TDLoadingSize.small,
                        icon: TDLoadingIcon.activity,
                        text: '加载中…',
                        axis: Axis.horizontal,
                      )
                    : ChooseDateAndOrgView(
                        clickedChooseDataAction: (String seletedData) {
                          model.year = seletedData;
                          model.initData();
                        },
                        clickedChooseOrgAction: (String orgCode) {
                          model.orgCode = orgCode;
                          model.initData();
                        },
                        treeResult: treeResult,
                      ),

                //水田 旱田
                RankChooseTabView(
                  clickedTabAction: (index) {
                    model.landType = index;
                    model.initData();
                  },
                ),

                //搜索
                RankSearchView(
                  searchController: searchController,
                  focusScopeNode: focusScopeNode,
                  clickedSeachBtn: (res) {
                    model.nameOrIdNumber = res;
                    model.initData();
                  },
                  clickedClearBtn: () {
                    model.nameOrIdNumber = '';
                    model.initData();
                  },
                ),
                //list
                Expanded(
                  child: model.isBusy
                      ? const ViewStateBusyWidget()
                      : model.isEmpty
                          ? const BdhEmptyView()
                          : SmartRefresher(
                              enablePullDown: true,
                              enablePullUp: true,
                              onRefresh: model.refresh,
                              onLoading: model.loadMore,
                              controller: model.refreshController,
                              child: ListView.separated(
                                  itemBuilder: (context, index) {
                                    RankRecord itemModel = model.list[index];
                                    return RankItemCellView(
                                      itemModel: itemModel,
                                    );
                                  },
                                  separatorBuilder: (context, index) {
                                    return Container(
                                      height: 10.px,
                                      width: 375.px,
                                      color: Colors.transparent,
                                    );
                                  },
                                  itemCount: model.list.length),
                            ),
                ),
              ],
            ),
          ),
        );
      },
      model: RankBdhDigitalViewModel(),
      onModelReady: (ChangeNotifier model) {
        model as RankBdhDigitalViewModel;
        currentModel = model;
        model.initData();
      },
    );
  }
}

// ignore: must_be_immutable