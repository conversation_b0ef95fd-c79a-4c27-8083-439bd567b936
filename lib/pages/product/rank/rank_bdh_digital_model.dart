// To parse this JSON data, do
//
//     final rankBdhDigitalModel = rankBdhDigitalModelFromJson(jsonString);

import 'dart:convert';

RankBdhDigitalModel rankBdhDigitalModelFromJson(String str) =>
    RankBdhDigitalModel.fromJson(json.decode(str));

String rankBdhDigitalModelToJson(RankBdhDigitalModel data) =>
    json.encode(data.toJson());

class RankBdhDigitalModel {
  bool? success;
  int? code;
  String? msg;
  Data? data;

  RankBdhDigitalModel({
    this.success,
    this.code,
    this.msg,
    this.data,
  });

  factory RankBdhDigitalModel.fromJson(Map<String, dynamic> json) =>
      RankBdhDigitalModel(
        success: json["success"],
        code: json["code"],
        msg: json["msg"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "success": success,
        "code": code,
        "msg": msg,
        "data": data?.toJson(),
      };
}

class Data {
  List<RankRecord>? records;
  int? total;
  int? size;
  int? current;
  // Orders? orders;
  bool? optimizeCountSql;
  bool? hitCount;
  dynamic countId;
  dynamic maxLimit;
  bool? searchCount;
  int? pages;

  Data({
    this.records,
    this.total,
    this.size,
    this.current,
    // this.orders,
    this.optimizeCountSql,
    this.hitCount,
    this.countId,
    this.maxLimit,
    this.searchCount,
    this.pages,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        records: json["records"] == null
            ? []
            : List<RankRecord>.from(json["records"]!.map((x) {
                RankRecord item = RankRecord.fromJson(x);
                return item;
              })),
        total: json["total"],
        size: json["size"],
        current: json["current"],
        // orders: json["orders"] == null ? null : Orders.fromJson(json["orders"]),
        optimizeCountSql: json["optimizeCountSql"],
        hitCount: json["hitCount"],
        countId: json["countId"],
        maxLimit: json["maxLimit"],
        searchCount: json["searchCount"],
        pages: json["pages"],
      );

  Map<String, dynamic> toJson() => {
        "records": records == null
            ? []
            : List<dynamic>.from(records!.map((x) => x.toJson())),
        "total": total,
        "size": size,
        "current": current,
        // "orders": orders?.toJson(),
        "optimizeCountSql": optimizeCountSql,
        "hitCount": hitCount,
        "countId": countId,
        "maxLimit": maxLimit,
        "searchCount": searchCount,
        "pages": pages,
      };
}

class Orders {
  Orders();

  factory Orders.fromJson(Map<String, dynamic> json) => Orders();

  Map<String, dynamic> toJson() => {};
}

class RankRecord {
  String? farmerName;
  String? idNum;
  num? score;
  String? rank;
  bool? open;
  List<SubjectElement>? subjects;

  RankRecord({
    this.farmerName,
    this.idNum,
    this.score,
    this.rank,
    this.subjects,
    this.open,
  });

  factory RankRecord.fromJson(Map<String, dynamic> json) => RankRecord(
        open: false,
        farmerName: json["farmerName"],
        idNum: json["idNum"],
        score: json["score"],
        rank: json["rank"],
        subjects: json["subjects"] == null
            ? []
            : List<SubjectElement>.from(json["subjects"]!.map((x) {
                SubjectElement item = SubjectElement.fromJson(x);
                return item;
              })),
      );

  Map<String, dynamic> toJson() => {
        "farmerName": farmerName,
        "idNum": idNum,
        "score": score,
        "rank": rank,
        "subjects": subjects == null
            ? []
            : List<dynamic>.from(subjects!.map((x) => x.toJson())),
      };
}

class SubjectElement {
  String? subject;
  num? score;

  SubjectElement({
    this.subject,
    this.score,
  });

  factory SubjectElement.fromJson(Map<String, dynamic> json) => SubjectElement(
        subject: json["subject"],
        score: json["score"],
      );

  Map<String, dynamic> toJson() => {
        "subject": subject,
        "score": score,
      };
}

// class EnumValues<T> {
//   Map<String, T> map;
//   late Map<T, String> reverseMap;

//   EnumValues(this.map);

//   Map<T, String> get reverse {
//     reverseMap = map.map((k, v) => MapEntry(v, k));
//     return reverseMap;
//   }
// }
