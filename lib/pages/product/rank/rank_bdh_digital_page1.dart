import 'package:bdh_smart_agric_app/model/org_tree_list_model.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_View.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_model.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/rank_bdh_digital_provider.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/subview/choose_date_org_view.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/subview/rank_choose_tabview.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/subview/rank_item_cell_view.dart';
import 'package:bdh_smart_agric_app/pages/product/rank/subview/rank_search_view.dart';
import 'package:bdh_smart_agric_app/utils/color_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:pull_to_refresh_flutter3/pull_to_refresh_flutter3.dart';
import 'package:tdesign_flutter/tdesign_flutter.dart';

class RankBdhDigitalPage1 extends StatefulWidget {
  const RankBdhDigitalPage1({super.key});

  @override
  State<RankBdhDigitalPage1> createState() => _RankBdhDigitalPageState();
}

class _RankBdhDigitalPageState extends State<RankBdhDigitalPage1> {
  TextEditingController searchController = TextEditingController();
  final FocusScopeNode focusScopeNode = FocusScopeNode();
  OrgTreeResult? treeResult;
  bool loadingChooseOrgView = true;
  late RefreshController _refreshController;
  @override
  void initState() {
    super.initState();
    loadOrg();
    _refreshController = RefreshController(initialRefresh: false);
    final provider = context.read<RankBdhDigitalProvider>();
    provider.onRefresh();
  }

  loadOrg() {
    loadingChooseOrgView = true;
    BDHResponsitory.getOrgTree({
      "orgLevelList": [1, 2, 3, 4]
    }).then((res) {
      setState(() {
        treeResult = res;
        loadingChooseOrgView = false;
      });
    });
  }

  // 下拉刷新回调
  void _onRefresh() async {
    try {
      final provider = context.read<RankBdhDigitalProvider>();
      provider.onRefresh();
      if (provider.hasMore) {
        _refreshController.loadComplete();
      } else {
        _refreshController.loadNoData();
      }
    } catch (e) {
      _refreshController.refreshFailed();
    } finally {
      _refreshController.refreshCompleted();
    }
  }

  // 上拉加载回调
  void _onLoading() async {
    try {
      final provider = context.read<RankBdhDigitalProvider>();
      await provider.loadMore();
      if (provider.hasMore) {
        _refreshController.loadComplete();
      } else {
        _refreshController.loadNoData();
      }
    } catch (e) {
      _refreshController.loadFailed();
    } finally {
      _refreshController.refreshCompleted();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RankBdhDigitalProvider>(builder: (context, ref, child) {
      return Scaffold(
        backgroundColor: HexColor("#F3F5F9"),
        appBar: AppBar(
          title: const Text('评价公示'),
        ),
        body: Container(
          padding: EdgeInsets.only(
              left: 16.px, right: 16.px, top: 10.px, bottom: 15.px),
          child: Column(
            children: [
              //日期+org
              loadingChooseOrgView
                  ? const TDLoading(
                      size: TDLoadingSize.small,
                      icon: TDLoadingIcon.activity,
                      text: '加载中…',
                      axis: Axis.horizontal,
                    )
                  : ChooseDateAndOrgView(
                      clickedChooseDataAction: (String seletedData) {
                        ref.setYear(seletedData);
                        ref.onRefresh();
                      },
                      clickedChooseOrgAction: (String orgCode) {
                        ref.setOrgCode(orgCode);
                        ref.onRefresh();
                      },
                      treeResult: treeResult,
                    ),

              //水田 旱田
              RankChooseTabView(
                clickedTabAction: (index) {
                  ref.setLandType(index);
                  ref.onRefresh();
                },
              ),

              //搜索
              RankSearchView(
                searchController: searchController,
                focusScopeNode: focusScopeNode,
                clickedSeachBtn: (res) {
                  // model.nameOrIdNumber = res;
                  // model.initData();
                },
                clickedClearBtn: () {
                  // model.nameOrIdNumber = '';
                  // model.initData();
                },
              ),

              //list
              Expanded(
                  child: ref.isLoading
                      ? const ViewStateBusyWidget()
                      : ref.listData.isEmpty
                          ? const BdhEmptyView()
                          : SmartRefresher(
                              enablePullDown: true,
                              enablePullUp: true,
                              onRefresh: _onRefresh,
                              onLoading: _onLoading,
                              controller: _refreshController,
                              child: ListView.separated(
                                  itemBuilder: (context, index) {
                                    RankRecord itemModel = ref.listData[index];
                                    return RankItemCellView(
                                      itemModel: itemModel,
                                    );
                                  },
                                  separatorBuilder: (context, index) {
                                    return Container(
                                      height: 10.px,
                                      width: 375.px,
                                      color: Colors.transparent,
                                    );
                                  },
                                  itemCount: ref.listData.length),
                            )),
            ],
          ),
        ),
      );
    });
  } //end build
}
