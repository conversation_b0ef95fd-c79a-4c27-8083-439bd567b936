import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:flutter/material.dart';

class TabLivePage extends StatefulWidget {
  const TabLivePage({super.key});

  @override
  State<StatefulWidget> createState() => _TabLivePageState();
}

class _TabLivePageState extends State<TabLivePage> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child:
          Image.asset(width: 375.px, ImageHelper.wrapAssets("mock_live.png")),
    );
  }
}
