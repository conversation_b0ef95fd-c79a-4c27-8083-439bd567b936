import 'dart:async';
import 'dart:convert';
import 'package:bdh_smart_agric_app/manager/router_manager.dart';
import 'package:bdh_smart_agric_app/model/menu_config_model.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/global_service_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/service_dialog_view.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/tab_home_v6.dart';
import 'package:bdh_smart_agric_app/pages/home/<USER>/premium_classroom.dart';
import 'package:bdh_smart_agric_app/pages/message/bdh_empty_view.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/my_field_page.dart';
import 'package:bdh_smart_agric_app/pages/user/user_bdh_digital/tab_user_bdh_digital.dart';
import 'package:bdh_smart_agric_app/pages/user/we_chat_auth/native_paramter_back.dart';
import 'package:bdh_smart_agric_app/pages/worktable/work_table_page.dart';
import 'package:bdh_smart_agric_app/utils/event_bus.dart';
import 'package:bdh_smart_agric_app/utils/get_device_info.dart';
import 'package:bdh_smart_agric_app/utils/gps/gps_receiver.dart';
import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/log.dart';
import 'package:bdh_smart_agric_app/utils/native_util.dart';
import 'package:bdh_smart_agric_app/utils/provider/view_state_widget.dart';
import 'package:bdh_smart_agric_app/utils/request/bdh_service.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_tool.dart';
import 'package:bdh_smart_agric_app/utils/storage_util.dart';
import 'package:bdh_smart_agric_app/viewmodel/font_scale_model.dart';
// import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluwx/fluwx.dart';
import 'package:move_to_background/move_to_background.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:umeng_common_sdk/umeng_common_sdk.dart';
import 'package:umeng_push_sdk/umeng_push_sdk.dart';
import 'package:url_launcher/url_launcher.dart';
// import 'package:wakelock_plus/wakelock_plus.dart';
//import 'package:app_links/app_links.dart';
import 'package:bdh_smart_agric_app/utils/app_links.dart';
import 'package:badges/badges.dart' as badges;

import '../tabmain/bottom_navigation_bar.dart' as BottomNavigationBarS;

class TabMainPage extends StatefulWidget {
  const TabMainPage({super.key});

  @override
  State<StatefulWidget> createState() => TabMainPageState();
}

class TabMainPageState extends State<TabMainPage> {
  final _pageController = PageController(initialPage: 2);
  int _selectedIndex = 2;
  List<Widget> pages = [];
  Fluwx fluwx = Fluwx();
  List<MenuConfigItem> configItems = [];
  List<BottomNavigationBarItem> items = [];
  bool isLoading = false;
  bool isBlack = false;
  bool hasNewVersion = false;
  bool hasPop = false;

  StreamSubscription<Uri>? _linkSubscription;

  @override
  void initState() {
    super.initState();

    initGps();
    initUmeng();
    initNetSubcribe();
    initFluwx();
    loadConfig();
    check();
    if (StorageUtil.telephone() != null) {
      GlobalServiceView.loadServiceConfig();
      addGlobalFloatActionBtn();
    }
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: [SystemUiOverlay.top, SystemUiOverlay.bottom]);
    GetDeviceInfo.getDeviceInfo();
    bus.on("token_invalid", (v) {
      if (hasPop == false) {
        Navigator.of(context).popUntil((route) {
          return route.settings.name == RouteName.tabMain;
        });
        // Navigator.of(context).popAndPushNamed(RouteName.login);
        Navigator.of(context).popAndPushNamed(RouteName.loginBdhDigitalPage);
        hasPop = true;
      }
    });
    initDeepLinks();
  }

  @override
  void dispose() {
    super.dispose();
    _linkSubscription?.cancel();
  }

  Future<void> initDeepLinks() async {
    _linkSubscription = AppLinks().uriLinkStream.listen((uri) {
      if (!mounted) {
        return;
      }
      var settings = getRouteSettingsFromUri(uri);
      if (settings.name == null) {
        return;
      }
      Navigator.maybeOf(context)
          ?.pushNamed(settings.name!, arguments: settings.arguments);
    });
  }

  @override
  Widget build(BuildContext context) {
    initFontScale(context);
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (pop, res) {
          if (!pop) {
            MoveToBackground.moveTaskToBack();
          }
        },
        child: isLoading
            ? const Material(
                child: ViewStateBusyWidget(),
              )
            : (configItems.length < 2
                ? const Material(
                    child: BdhEmptyView(),
                  )
                : Scaffold(
                    body: PageView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        controller: _pageController,
                        itemCount: pages.length,
                        onPageChanged: (idx) {},
                        itemBuilder: (ctx, index) {
                          return pages[index];
                        }),
                    bottomNavigationBar:
                        BottomNavigationBarS.BottomNavigationBar(
                      backgroundColor: isBlack
                          ? const Color.fromRGBO(51, 42, 41, 1)
                          : Colors.white,
                      selectedItemColor: const Color.fromRGBO(0, 127, 255, 1),
                      unselectedItemColor:
                          const Color.fromRGBO(131, 149, 142, 1),
                      type: BottomNavigationBarS.BottomNavigationBarType.fixed,
                      selectedFontSize: 13.px,
                      unselectedFontSize: 13.px,
                      items: items,
                      currentIndex: _selectedIndex,
                      onTap: (index) {
                        bus.emit(
                            'UserChangeBottomTabbarAction', configItems[index]);

                        GlobalServiceView.loadServiceConfig();
                        String? itemName = configItems[index].url;

                        GlobalServiceView.needShowServiceBtn(itemName ?? '');

                        BDHResponsitory.saveMenuUse({
                          "menuCode": configItems[index].authCode,
                          "menuName": configItems[index].authName,
                          "menuType": "0", //导航入口类型
                        });

                        if (configItems[index].authCode == "digitizationPlot") {
                          isBlack = false;
                          // _pageController.jumpToPage(index);
                          // setState(() {
                          //   _selectedIndex = index;
                          // });
                          NativeUtil.openUni(
                              {"path": "pages/remotesense/index"});
                        } else if (configItems[index].authCode ==
                            "digitizationVideo") {
                          isBlack = true;
                          _pageController.jumpToPage(index);
                          setState(() {
                            _selectedIndex = index;
                          });
                        } else {
                          isBlack = false;
                          _pageController.jumpToPage(index);
                          setState(() {
                            _selectedIndex = index;
                          });
                        }
                      },
                    ),
                  )));
  }

  showServiceBtn(String itemName) {
    String userOrgCode = StorageUtil.orgCode() ?? '';
    bool isShowService =
        GlobalServiceView.needShowService(itemName, userOrgCode);
    isShowService
        ? GlobalServiceView.showView()
        : GlobalServiceView.hidenView();
  }

  addGlobalFloatActionBtn() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      GlobalServiceView.addOverlayEntry(
          (375 - 80).px, ScreenTool().screenHeight - 205.px);
      GlobalServiceView.callBlock = () {
        showDialog(
            context: context,
            builder: (ctx) {
              return PopScope(
                canPop: false,
                child: Center(
                  child: ServiceDialogView(
                    currentItem: GlobalServiceView.currentItem,
                    authCode: GlobalServiceView.authCode,
                    clickedCloseBtnCallBack: () {
                      GlobalServiceView.showView();
                    },
                    clickedTelephoneCallBack: (model) {
                      launchUrl(Uri.parse("tel:${model.phone}"));
                    },
                  ),
                ),
              );
            });
      };
    });
  }

  initGps() {
    // WakelockPlus.enable();
    Permission.locationWhenInUse.status.then((res) {
      if (res == PermissionStatus.granted) {
        GpsReceiver receiver = GpsReceiver.getInstance();
        receiver.start(true);
      }
    });
  }

  changeBottomTabbar() {
    bus.on('ChangeBottomTabbar', (index) {
      setState(() {
        _pageController.jumpToPage(index);
        setState(() {
          _selectedIndex = index;
        });
      });
    });
  }

  initNetSubcribe() {
    // Connectivity()
    //     .onConnectivityChanged
    //     .listen((List<ConnectivityResult> results) {
    //   if (results.contains(ConnectivityResult.none)) {
    //     showToast("当前网络异常");
    //   }
    // });
  }
  //old
  initUmeng1() {
    UmengCommonSdk.initCommon(
        '6826f9babc47b67d83683f42', '6826fd4f79267e0210660646', 'Umeng');
    // 友盟自动采集页面信息
    UmengCommonSdk.setPageCollectionModeManual();
  }

  initUmeng() {
    UmengCommonSdk.initCommon(
        '6826f9babc47b67d83683f42',
        '6826fd4f79267e0210660646',
        'BDH_PUSH',
        'd53d247a62802dee21b975585c89fcd3');
    UmengCommonSdk.setPageCollectionModeManual(); // 友盟自动采集页面信息
    UmengPushSdk.register(
        "6826fd4f79267e0210660646", "BDH_PUSH"); //ios push regist
    UmengPushSdk.setLogEnable(true); //开启 push log
    umPushCallBack();
  }

  void umPushCallBack() {
//设置deviceToken回调
    UmengPushSdk.setTokenCallback((deviceToken) {
      Log.d(
          "--------------------------------deviceToken callback:$deviceToken");
      NativeParamterBack.dealWithSendDeviceTokenFromFlutter(deviceToken);
    });

//设置自定义消息回调
    UmengPushSdk.setMessageCallback((msg) {
      Log.d("--------------------------------receiveCustomMessage:$msg");
    });

//设置通知消息回调
    UmengPushSdk.setNotificationCallback((receive) {
      Log.d("--------------------------------receivePushMessage:$receive");
    }, (open) {
      Log.d("openPushMessage:$open");
      Map<String, dynamic> parmater = {};
      parmater['msgBody'] = open;
      parmater['platform'] = 'android';
      NativeParamterBack.dealWithRouteData(parmater, context);
    });
    delayGetDeviceToken(2);
    delayGetDeviceToken(4);
  }

  delayGetDeviceToken(int waitTimeDuration) {
    Future.delayed(Duration(seconds: waitTimeDuration), () {
      getDeviceToken();
    });
  }

  getDeviceToken() async {
    String? deviceToken = await UmengPushSdk.getRegisteredId();
    Log.i(" --------------------------------deviceToken:$deviceToken");
    if (deviceToken != null) {
      NativeParamterBack.dealWithSendDeviceTokenFromFlutter(deviceToken);
    }
  }

  initFluwx() async {
    fluwx.registerApi(
        appId: "wx189b180bf286207a",
        doOnAndroid: true,
        doOnIOS: true,
        universalLink: "https://app.bdhic.com/");
  }

  initFontScale(BuildContext context) async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    double? fontScaleA = preferences.getDouble('FontScale');
    if (mounted) {
      context.read<FontScaleModel>().saveFontScale(fontScaleA ?? 1.0);
    }
  }

  check() {
    // if (Platform.isAndroid) {
    //   setState(() {
    //     isLoading = true;
    //   });
    //   GetCurrentInstallVersion.check(needShowDialog: false).then((res) {
    //     loadConfig();
    //     setState(() {
    //       hasNewVersion = res['haveNewVersion'];
    //     });
    //   });
    // } else {
    loadConfig();
    //}
  }

  loadConfig() {
    setState(() {
      isLoading = true;
    });
    BDHResponsitory.getConfig().then((res) {
      setState(() {
        isLoading = false;
      });
      for (var item in res.data!) {
        if (item.authCode == "digMainMenu") {
          configItems = item.children ?? [];
          items = item.children!.map((e) {
            return e.authName == '工作台'
                ? _buildWorkTable()
                : BottomNavigationBarItem(
                    activeIcon: ((e.authName ?? '') == '我的')
                        ? badges.Badge(
                            showBadge: hasNewVersion ? true : false,
                            position: badges.BadgePosition.topEnd(),
                            badgeContent: Container(
                              height: 0.5.px,
                              width: 0.5.px,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.red,
                              ),
                            ),
                            child: Image.asset(
                              ImageHelper.wrapAssets("${e.icon}_select.png"),
                              width: 24.px,
                              height: 24.px,
                            ),
                          )
                        : Image.asset(
                            ImageHelper.wrapAssets("${e.icon}_select.png"),
                            width: 24.px,
                            height: 24.px,
                          ),
                    icon: ((e.authName ?? '') == '我的')
                        ? badges.Badge(
                            showBadge: hasNewVersion ? true : false,
                            position: badges.BadgePosition.topEnd(),
                            badgeContent: Container(
                              height: 0.5.px,
                              width: 0.5.px,
                              decoration: const BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.red,
                              ),
                            ),
                            child: Image.asset(
                              ImageHelper.wrapAssets("${e.icon}_unselect.png"),
                              width: 24.px,
                              height: 24.px,
                            ),
                          )
                        : Image.asset(
                            ImageHelper.wrapAssets("${e.icon}_unselect.png"),
                            width: 24.px,
                            height: 24.px,
                          ),
                    label: e.authName ?? "");
          }).toList();
          List<Widget> temps = [];
          for (var v in item.children!) {
            if (v.authCode == "digitizationHome") {
              temps.add(const TabHomePageV6());
            }
            if (v.authCode == "digitizationVideo") {
              temps.add(
                PremiumClassroom(
                  type: EntryType.home,
                  pageController: _pageController,
                ),
              );
            }
            if (v.authCode == "digitizationPlot") {
              temps.add(const MyFieldPage());
            }
            if (v.authCode == "digitizationService") {
              MenuConfigItem configItem = v;
              StorageUtil.saveServiceItem(jsonEncode(configItem.toJson()));

              temps.add(
                WorkTablePage(
                  item: v,
                ),
              );
            }
            if (v.authCode == "digitizitionUser") {
              temps.add(const TabUserBdhDigital());
            }
          }
          pages = temps;
          setState(() {});
        }
      }
    });
  }

  BottomNavigationBarItem _buildWorkTable() {
    return BottomNavigationBarItem(
        activeIcon: Image.asset(
          ImageHelper.wrapAssets("work_select.png"),
          width: 54.px,
        ),
        icon: Image.asset(
          ImageHelper.wrapAssets("work_unselect.png"),
          width: 54.px,
        ),
        label: "");
  }
}
