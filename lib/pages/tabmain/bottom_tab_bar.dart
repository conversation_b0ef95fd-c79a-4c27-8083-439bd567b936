import 'package:bdh_smart_agric_app/utils/image_util.dart';
import 'package:bdh_smart_agric_app/utils/screen/screen_extension.dart';
import 'package:bdh_smart_agric_app/viewmodel/user_model.dart';
import 'package:flutter/material.dart';
import 'package:badges/badges.dart' as badges;
import 'package:provider/provider.dart';

class BottomTabBar extends StatefulWidget {
  final Function(int) onChange;
  const BottomTabBar({super.key, required this.onChange});

  @override
  State<StatefulWidget> createState() => BottomTabBarState();
}

class BottomTabBarState extends State<BottomTabBar> {
  int curIndex = 0;

  @override
  Widget build(BuildContext context) {
    var lightTheme = TabBarStyle(Colors.white, const Color.fromRGBO(0, 0, 0, 1),
        const Color.fromRGBO(0, 0, 0, 0.6));
    var darkTheme = TabBarStyle(
        Colors.black,
        const Color.fromRGBO(255, 255, 255, 1),
        const Color.fromRGBO(255, 255, 255, 0.6));

    var theme =
        context.watch<UserModel>().isDark ?? true ? darkTheme : lightTheme;
    return SizedBox(
      height: 40.px,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          GestureDetector(
            onTap: () {
              onClick(0);
              context.read<UserModel>().setIsDark(true);
              context.read<UserModel>().notify();
            },
            child: Text(
              "首页",
              style: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 16.px,
                  color:
                      curIndex == 0 ? theme.selectColor : theme.unSelectColor),
            ),
          ),
          GestureDetector(
            onTap: () {},
            child: SizedBox(
              width: 97.5.px,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text("发视频",
                      style: TextStyle(
                          fontSize: 16.px,
                          fontWeight: FontWeight.w500,
                          color: theme.unSelectColor)),
                  Image.asset(
                    ImageHelper.wrapAssets(
                      "video.png",
                    ),
                    width: 20.px,
                  )
                ],
              ),
            ),
          ),
          badges.Badge(
            badgeStyle: const badges.BadgeStyle(
                shape: badges.BadgeShape.square,
                borderRadius: BorderRadius.all(Radius.circular(7)),
                padding: EdgeInsets.only(left: 4, top: 0, bottom: 0, right: 4)),
            badgeContent: const Text(
              '29',
              style: TextStyle(color: Colors.white, fontSize: 10),
            ),
            child: GestureDetector(
              onTap: () {
                onClick(1);
              },
              child: Text("消息",
                  style: TextStyle(
                      fontSize: 16.px,
                      fontWeight: FontWeight.w500,
                      color: curIndex == 1
                          ? theme.selectColor
                          : theme.unSelectColor)),
            ),
          ),
          GestureDetector(
            onTap: () {
              onClick(2);
              context.read<UserModel>().setIsDark(false);
              context.read<UserModel>().notify();
            },
            child: Text("我的",
                style: TextStyle(
                    fontSize: 16.px,
                    fontWeight: FontWeight.w500,
                    color: curIndex == 2
                        ? theme.selectColor
                        : theme.unSelectColor)),
          )
        ],
      ),
    );
  }

  onClick(int index) {
    curIndex = index;
    widget.onChange(index);
    setState(() {});
  }
}

class TabBarStyle {
  Color backgroundColor;
  Color selectColor;
  Color unSelectColor;
  TabBarStyle(this.backgroundColor, this.selectColor, this.unSelectColor);
}
