# 采样任务本地数据库

基于您提供的数据格式创建的完整Flutter本地数据库解决方案，用于管理三农普查采样任务数据。

## 目录结构

```
lib/database/
├── README.md                          # 使用说明文档
├── database_helper.dart               # 数据库帮助类
├── sampling_task_service.dart         # 服务层接口
├── example_usage.dart                 # 使用示例
├── models/                           # 数据模型
│   ├── sampling_task_local.dart      # 采样任务模型
│   ├── sampling_task_image_local.dart # 采样图片模型
│   └── sampling_task_sample_local.dart # 采样样品模型
└── dao/                              # 数据访问对象
    └── sampling_task_dao.dart        # 采样任务DAO
```

## 数据库表结构

### 1. 采样任务主表 (sampling_tasks)
- **id**: 本地主键，自增
- **task_id**: 远程任务ID，唯一索引
- **task_code**: 任务编号
- **point_code**: 采样点编号
- **sampling_status**: 采样状态 ("1"=已采样, "0"=未采样)
- **org_name**: 组织名称
- **org_code**: 组织编码
- **longitude**: 经度
- **latitude**: 纬度
- **land_use_type**: 土地利用类型
- **saline_alkali_flag**: 是否盐碱地 (1=是, 0=否)
- **sampling_type**: 采样类型
- **point_type**: 点位类型
- **survey_longitude**: 调查经度
- **survey_latitude**: 调查纬度
- **correct**: 校正信息
- **created_at**: 创建时间戳
- **updated_at**: 更新时间戳

### 2. 采样图片表 (sampling_task_images)
- **id**: 本地主键，自增
- **task_image_id**: 远程图片ID
- **image_url**: 图片URL
- **image_type**: 图片类型 (E=东, S=南, W=西, N=北, O=其他)
- **image_time**: 图片时间戳
- **image_angle**: 图片角度
- **task_id**: 关联的任务ID (外键)
- **created_at**: 创建时间戳

### 3. 采样样品表 (sampling_task_samples)
- **id**: 本地主键，自增
- **task_sample_id**: 远程样品ID
- **sample_code**: 样品编号
- **sample_type**: 样品类型 (surface=表层, deep=深层)
- **sample_weight**: 样品重量
- **task_id**: 关联的任务ID (外键)
- **created_at**: 创建时间戳

## 核心功能

### 1. 数据存储
- 支持完整的远程数据本地化存储
- 自动处理关联数据（图片、样品）
- 支持批量操作，提高性能
- 事务处理确保数据一致性

### 2. 数据查询
- 根据状态查询任务
- 分页查询支持
- 关键词搜索功能
- 按图片类型分类查询
- 完整任务详情查询（包含所有关联数据）

### 3. 数据更新
- 支持表单数据更新
- 图片管理（增删）
- 样品管理（增删）
- 级联删除保证数据完整性

### 4. 数据同步
- 本地到远程数据格式转换
- 支持离线操作
- 数据完整性验证

## 使用方法

### 1. 基本初始化

```dart
import 'package:your_app/database/sampling_task_service.dart';

final service = SamplingTaskService();
```

### 2. 保存远程数据到本地

```dart
// 您提供的数据格式
final Map<String, dynamic> remoteData = {
  "taskId": 7,
  "taskCode": "OUESnjXV46",
  "pointCode": "w2g4uHMbWa",
  "samplingStatus": "1",
  "orgName": "二九〇农场",
  // ... 其他字段
  "samplingTaskImage": [
    {
      "taskImageId": 71,
      "imageUrl": "http://example.com/image.png",
      "imageType": "E",
      // ... 其他字段
    }
  ],
  "samplingTaskSample": [
    {
      "taskSampleId": 5,
      "sampleCode": "SAMPLE-002",
      "sampleType": "surface",
      "sampleWeight": 2.51,
      // ... 其他字段
    }
  ]
};

// 保存到本地数据库
await service.saveRemoteDataToLocal(remoteData);
```

### 3. 查询任务详情

```dart
final taskDetail = await service.getTaskDetail(7);

if (taskDetail != null) {
  final task = taskDetail['task'] as SamplingTaskLocal;
  final images = taskDetail['images'] as List<SamplingTaskImageLocal>;
  final samples = taskDetail['samples'] as List<SamplingTaskSampleLocal>;
  final imagesByType = taskDetail['imagesByType'] as Map<String, List<SamplingTaskImageLocal>>;
  
  print('任务编号: ${task.taskCode}');
  print('图片总数: ${images.length}');
  print('东方向图片: ${imagesByType['E']?.length ?? 0} 张');
  print('样品总数: ${samples.length}');
}
```

### 4. 更新任务数据

```dart
final formData = {
  'landUseType': '0202',
  'samplingType': '2',
  'salineAlkaliFlag': 0,
  'pointType': '2',
};

await service.updateTaskFormData(7, formData);
```

### 5. 管理图片

```dart
// 添加单张图片
await service.addTaskImage(7, 'http://example.com/new_image.jpg', 'E');

// 批量添加图片
final imageDataList = [
  {'url': 'http://example.com/image1.jpg', 'type': 'E'},
  {'url': 'http://example.com/image2.jpg', 'type': 'S'},
];
await service.addTaskImages(7, imageDataList);

// 获取特定方向的图片
final eastImages = await service.getImagesByType(7, 'E');
```

### 6. 管理样品

```dart
// 添加样品
await service.addTaskSample(7, 'SAMPLE-004', 'deep', 3.25);
```

### 7. 数据查询

```dart
// 获取所有本地任务
final allTasks = await service.getAllLocalTasks();

// 根据状态查询
final completedTasks = await service.getLocalTasksByStatus("1");

// 分页查询
final tasks = await service.getLocalTasksWithPagination(
  page: 1,
  pageSize: 20,
  status: "1",
  searchKeyword: "二九〇",
);
```

### 8. 统计信息

```dart
final stats = await service.getDatabaseStats();
print('总任务数: ${stats['totalTasks']}');
print('完成率: ${stats['completionRate']}%');
```

### 9. 数据同步

```dart
// 将本地数据转换为远程格式
final remoteData = await service.syncToRemote(7);

// 然后可以发送到远程服务器
// await remoteApiService.uploadTaskData(remoteData);
```

## 图片类型说明

- **E**: 东方向照片
- **S**: 南方向照片  
- **W**: 西方向照片
- **N**: 北方向照片
- **O**: 其他照片

## 样品类型说明

- **surface**: 表层样品
- **deep**: 深层样品

## 错误处理

所有方法都会抛出异常，建议使用 try-catch 包装：

```dart
try {
  await service.saveRemoteDataToLocal(remoteData);
  print('保存成功');
} catch (e) {
  print('保存失败: $e');
}
```

## 性能优化

1. **批量操作**: 优先使用批量插入方法
2. **索引优化**: 已在关键字段创建索引
3. **事务处理**: 复杂操作使用事务确保原子性
4. **分页查询**: 大量数据使用分页避免内存问题

## 数据库文件位置

SQLite数据库文件存储在应用文档目录下：
```
{ApplicationDocumentsDirectory}/sampling_tasks.db
```

## 依赖包

请确保在 `pubspec.yaml` 中添加以下依赖：

```yaml
dependencies:
  sqflite: ^2.3.0
  path_provider: ^2.1.1
  logger: ^2.0.2
```

## 完整示例

查看 `example_usage.dart` 文件获取完整的使用示例，包含所有功能的演示代码。

```dart
final example = SamplingTaskDatabaseExample();
await example.runAllExamples();
``` 