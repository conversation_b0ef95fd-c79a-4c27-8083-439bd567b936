import 'dart:io';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;
  final Logger _logger = Logger();

  // 数据库名称和版本
  static const String _dbName = 'sampling_tasks.db';
  static const int _dbVersion = 3;

  // 表名
  static const String _tableSamplingTasks = 'sampling_tasks';
  static const String _tableSamplingTaskImages = 'sampling_task_images';
  static const String _tableSamplingTaskSamples = 'sampling_task_samples';

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    try {
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, _dbName);
      
      _logger.i('初始化数据库路径: $path');

      return await openDatabase(
        path,
        version: _dbVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      _logger.e('数据库初始化失败: $e');
      rethrow;
    }
  }

  Future<void> _onCreate(Database db, int version) async {
    try {
      _logger.i('创建数据库表，版本: $version');

      // 创建采样任务主表
      await db.execute('''
        CREATE TABLE $_tableSamplingTasks (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          task_id INTEGER NOT NULL UNIQUE,
          task_code TEXT NOT NULL,
          point_code TEXT NOT NULL,
          sampling_status TEXT NOT NULL,
          org_name TEXT NOT NULL,
          org_code TEXT NOT NULL,
          longitude TEXT NOT NULL,
          latitude TEXT NOT NULL,
          land_use_type TEXT,
          saline_alkali_flag INTEGER,
          sampling_type TEXT,
          point_type TEXT,
          survey_longitude REAL,
          survey_latitude REAL,
          correct TEXT,
          sampling_point_range REAL,
          submit_status INTEGER DEFAULT 0,
          created_at INTEGER NOT NULL,
          updated_at INTEGER NOT NULL
        )
      ''');

      // 创建采样任务图片表
      await db.execute('''
        CREATE TABLE $_tableSamplingTaskImages (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          task_image_id INTEGER NOT NULL,
          image_url TEXT NOT NULL,
          image_type TEXT NOT NULL,
          image_time INTEGER NOT NULL,
          image_angle TEXT,
          task_id INTEGER NOT NULL,
          created_at INTEGER NOT NULL,
          FOREIGN KEY (task_id) REFERENCES $_tableSamplingTasks (task_id) ON DELETE CASCADE
        )
      ''');

      // 创建采样任务样品表
      await db.execute('''
        CREATE TABLE $_tableSamplingTaskSamples (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          task_sample_id INTEGER NOT NULL,
          sample_code TEXT NOT NULL,
          sample_type TEXT NOT NULL,
          sample_weight REAL NOT NULL,
          task_id INTEGER NOT NULL,
          created_at INTEGER NOT NULL,
          FOREIGN KEY (task_id) REFERENCES $_tableSamplingTasks (task_id) ON DELETE CASCADE
        )
      ''');

      // 创建索引
      await db.execute('CREATE INDEX idx_sampling_tasks_task_id ON $_tableSamplingTasks (task_id)');
      await db.execute('CREATE INDEX idx_sampling_task_images_task_id ON $_tableSamplingTaskImages (task_id)');
      await db.execute('CREATE INDEX idx_sampling_task_samples_task_id ON $_tableSamplingTaskSamples (task_id)');
      await db.execute('CREATE INDEX idx_sampling_tasks_sampling_status ON $_tableSamplingTasks (sampling_status)');
      await db.execute('CREATE INDEX idx_sampling_tasks_submit_status ON $_tableSamplingTasks (submit_status)');
      await db.execute('CREATE INDEX idx_sampling_task_images_type ON $_tableSamplingTaskImages (image_type)');

      _logger.i('数据库表创建完成');
    } catch (e) {
      _logger.e('创建数据库表失败: $e');
      rethrow;
    }
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    _logger.i('数据库升级: $oldVersion -> $newVersion');
    
    try {
      // 从版本1升级到版本2：添加submit_status字段
      if (oldVersion < 2) {
        await db.execute('ALTER TABLE $_tableSamplingTasks ADD COLUMN submit_status INTEGER DEFAULT 0');
        await db.execute('CREATE INDEX idx_sampling_tasks_submit_status ON $_tableSamplingTasks (submit_status)');
        _logger.i('已添加submit_status字段和索引');
      }
      
      // 从版本2升级到版本3：添加sampling_point_range字段
      if (oldVersion < 3) {
        await db.execute('ALTER TABLE $_tableSamplingTasks ADD COLUMN sampling_point_range REAL');
        _logger.i('已添加sampling_point_range字段');
      }
    } catch (e) {
      _logger.e('数据库升级失败: $e');
      rethrow;
    }
  }

  // 获取表名
  String get tableSamplingTasks => _tableSamplingTasks;
  String get tableSamplingTaskImages => _tableSamplingTaskImages;
  String get tableSamplingTaskSamples => _tableSamplingTaskSamples;

  // 关闭数据库
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
      _logger.i('数据库连接已关闭');
    }
  }

  // 删除数据库文件
  Future<void> deleteDatabase() async {
    try {
      Directory documentsDirectory = await getApplicationDocumentsDirectory();
      String path = join(documentsDirectory.path, _dbName);
      
      if (await File(path).exists()) {
        await close();
        await File(path).delete();
        _logger.i('数据库文件已删除: $path');
      }
    } catch (e) {
      _logger.e('删除数据库文件失败: $e');
      rethrow;
    }
  }

  // 获取数据库信息
  Future<Map<String, dynamic>> getDatabaseInfo() async {
    final db = await database;
    final version = await db.getVersion();
    final path = db.path;
    
    // 获取各表的记录数
    final tasksCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $_tableSamplingTasks')
    ) ?? 0;
    
    final imagesCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $_tableSamplingTaskImages')
    ) ?? 0;
    
    final samplesCount = Sqflite.firstIntValue(
      await db.rawQuery('SELECT COUNT(*) FROM $_tableSamplingTaskSamples')
    ) ?? 0;

    return {
      'path': path,
      'version': version,
      'tasksCount': tasksCount,
      'imagesCount': imagesCount,
      'samplesCount': samplesCount,
    };
  }
} 