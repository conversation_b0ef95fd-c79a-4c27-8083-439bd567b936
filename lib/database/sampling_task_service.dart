import 'package:logger/logger.dart';
import 'dao/sampling_task_dao.dart';
import 'models/sampling_task_local.dart';
import 'models/sampling_task_image_local.dart';
import 'models/sampling_task_sample_local.dart';

class SamplingTaskService {
  static final SamplingTaskService _instance = SamplingTaskService._internal();
  factory SamplingTaskService() => _instance;
  SamplingTaskService._internal();

  final SamplingTaskDao _dao = SamplingTaskDao();
  final Logger _logger = Logger();

  /// 保存远程数据到本地数据库
  Future<void> saveRemoteDataToLocal(Map<String, dynamic> remoteData) async {
    try {
      await _dao.saveCompleteTaskData(remoteData);
      _logger.i('远程数据保存到本地成功: ${remoteData['taskId']}');
    } catch (e) {
      _logger.e('保存远程数据到本地失败: $e');
      rethrow;
    }
  }

  /// 批量保存远程数据到本地数据库
  Future<void> saveRemoteDataListToLocal(List<Map<String, dynamic>> remoteDataList) async {
    try {
      for (final remoteData in remoteDataList) {
        await _dao.saveCompleteTaskData(remoteData);
      }
      _logger.i('批量保存远程数据到本地成功: ${remoteDataList.length} 条记录');
    } catch (e) {
      _logger.e('批量保存远程数据到本地失败: $e');
      rethrow;
    }
  }

  /// 获取所有本地采样任务
  Future<List<SamplingTaskLocal>> getAllLocalTasks() async {
    try {
      return await _dao.getAllTasks();
    } catch (e) {
      _logger.e('获取所有本地任务失败: $e');
      rethrow;
    }
  }

  /// 根据状态获取本地采样任务
  Future<List<SamplingTaskLocal>> getLocalTasksByStatus(String status) async {
    try {
      return await _dao.getTasksByStatus(status);
    } catch (e) {
      _logger.e('根据状态获取本地任务失败: $e');
      rethrow;
    }
  }

  /// 根据状态获取本地采样任务（数字格式）
  Future<List<SamplingTaskLocal>> getTasksByStatus(int status) async {
    try {
      return await _dao.getTasksByStatus(status.toString());
    } catch (e) {
      _logger.e('根据状态获取本地任务失败: $e');
      rethrow;
    }
  }

  /// 更新任务状态为未提交状态（samplingStatus = 8）
  Future<void> updateTaskToUnsubmitted(int taskId) async {
    try {
      final existingTask = await _dao.getTaskByTaskId(taskId);
      if (existingTask == null) {
        throw Exception('任务不存在: $taskId');
      }

      final updatedTask = existingTask.copyWith(
        samplingStatus: '8', // 设置为未提交状态
        submitStatus: 0, // 设置为未提交
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _dao.updateTask(updatedTask);
      _logger.i('更新任务状态为未提交: $taskId');
    } catch (e) {
      _logger.e('更新任务状态失败: $e');
      rethrow;
    }
  }

  /// 更新任务状态为已提交状态（samplingStatus = 1）
  Future<void> updateTaskToSubmitted(int taskId) async {
    try {
      final existingTask = await _dao.getTaskByTaskId(taskId);
      if (existingTask == null) {
        throw Exception('任务不存在: $taskId');
      }

      final updatedTask = existingTask.copyWith(
        samplingStatus: '1', // 设置为已提交状态
        submitStatus: 2, // 设置为已提交
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _dao.updateTask(updatedTask);
      _logger.i('更新任务状态为已提交: $taskId');
    } catch (e) {
      _logger.e('更新任务状态失败: $e');
      rethrow;
    }
  }

  /// 批量更新任务状态为已提交
  Future<void> updateTasksToSubmitted(List<int> taskIds) async {
    try {
      for (final taskId in taskIds) {
        await updateTaskToSubmitted(taskId);
      }
      _logger.i('批量更新任务状态为已提交: ${taskIds.length} 个任务');
    } catch (e) {
      _logger.e('批量更新任务状态失败: $e');
      rethrow;
    }
  }

  /// 分页获取本地采样任务
  Future<List<SamplingTaskLocal>> getLocalTasksWithPagination({
    int page = 1,
    int pageSize = 20,
    String? status,
    String? searchKeyword,
  }) async {
    try {
      return await _dao.getTasksWithPagination(
        page: page,
        pageSize: pageSize,
        status: status,
        searchKeyword: searchKeyword,
      );
    } catch (e) {
      _logger.e('分页获取本地任务失败: $e');
      rethrow;
    }
  }

  /// 获取完整的任务详情（包括图片和样品）
  Future<Map<String, dynamic>?> getTaskDetail(int taskId) async {
    try {
      final result = await _dao.getCompleteTaskData(taskId);
      if (result == null) return null;

      final task = result['task'] as SamplingTaskLocal;
      final images = result['images'] as List<SamplingTaskImageLocal>;
      final samples = result['samples'] as List<SamplingTaskSampleLocal>;

      // 按方向分类图片
      Map<String, List<SamplingTaskImageLocal>> imagesByType = {
        'E': [], // 东
        'S': [], // 南
        'W': [], // 西
        'N': [], // 北
        'O': [], // 其他
      };

      for (final image in images) {
        final type = image.imageType;
        if (imagesByType.containsKey(type)) {
          imagesByType[type]!.add(image);
        } else {
          imagesByType['O']!.add(image);
        }
      }

      return {
        'task': task,
        'images': images,
        'imagesByType': imagesByType,
        'samples': samples,
      };
    } catch (e) {
      _logger.e('获取任务详情失败: $e');
      rethrow;
    }
  }

  /// 更新任务的表单数据
  Future<void> updateTaskFormData(int taskId, Map<String, dynamic> formData) async {
    try {
      final existingTask = await _dao.getTaskByTaskId(taskId);
      if (existingTask == null) {
        throw Exception('任务不存在: $taskId');
      }

      final updatedTask = existingTask.copyWith(
        landUseType: formData['landUseType'],
        samplingType: formData['samplingType'],
        salineAlkaliFlag: formData['salineAlkaliFlag'],
        pointType: formData['pointType'],
        surveyLongitude: formData['surveyLongitude'],
        surveyLatitude: formData['surveyLatitude'],
        samplingStatus: formData['samplingStatus'] ?? existingTask.samplingStatus,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _dao.updateTask(updatedTask);
      _logger.i('更新任务表单数据成功: $taskId');
    } catch (e) {
      _logger.e('更新任务表单数据失败: $e');
      rethrow;
    }
  }

  /// 添加任务图片
  Future<void> addTaskImage(int taskId, String imageUrl, String imageType) async {
    try {
      final image = SamplingTaskImageLocal(
        taskImageId: DateTime.now().millisecondsSinceEpoch, // 临时使用时间戳作为ID
        imageUrl: imageUrl,
        imageType: imageType,
        imageTime: DateTime.now().millisecondsSinceEpoch,
        taskId: taskId,
        createdAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _dao.insertImage(image);
      _logger.i('添加任务图片成功: $taskId, $imageType');
    } catch (e) {
      _logger.e('添加任务图片失败: $e');
      rethrow;
    }
  }

  /// 批量添加任务图片
  Future<void> addTaskImages(int taskId, List<Map<String, String>> imageDataList) async {
    try {
      final images = imageDataList.map((imageData) {
        return SamplingTaskImageLocal(
          taskImageId: DateTime.now().millisecondsSinceEpoch + imageDataList.indexOf(imageData),
          imageUrl: imageData['url']!,
          imageType: imageData['type']!,
          imageTime: DateTime.now().millisecondsSinceEpoch,
          taskId: taskId,
          createdAt: DateTime.now().millisecondsSinceEpoch,
        );
      }).toList();

      await _dao.insertImagesBatch(images);
      _logger.i('批量添加任务图片成功: $taskId, ${images.length} 张');
    } catch (e) {
      _logger.e('批量添加任务图片失败: $e');
      rethrow;
    }
  }

  /// 删除指定ID的图片
  Future<void> deleteTaskImage(int imageId) async {
    try {
      await _dao.deleteImage(imageId);
      _logger.i('删除图片成功: $imageId');
    } catch (e) {
      _logger.e('删除图片失败: $e');
      rethrow;
    }
  }

  /// 按任务ID删除所有图片
  Future<void> deleteImagesByTaskId(int taskId) async {
    try {
      await _dao.deleteImagesByTaskId(taskId);
      _logger.i('按任务ID删除所有图片成功: $taskId');
    } catch (e) {
      _logger.e('按任务ID删除图片失败: $e');
      rethrow;
    }
  }

  /// 根据图片类型获取图片
  Future<List<SamplingTaskImageLocal>> getImagesByType(int taskId, String imageType) async {
    try {
      return await _dao.getImagesByTaskId(taskId)
          .then((images) => images.where((img) => img.imageType == imageType).toList());
    } catch (e) {
      _logger.e('根据类型获取图片失败: $e');
      rethrow;
    }
  }

  /// 根据任务ID获取所有图片
  Future<List<SamplingTaskImageLocal>> getImagesByTaskId(int taskId) async {
    try {
      return await _dao.getImagesByTaskId(taskId);
    } catch (e) {
      _logger.e('获取任务图片失败: $e');
      rethrow;
    }
  }

  /// 根据任务ID获取所有样品
  Future<List<SamplingTaskSampleLocal>> getSamplesByTaskId(int taskId) async {
    try {
      return await _dao.getSamplesByTaskId(taskId);
    } catch (e) {
      _logger.e('获取任务样品失败: $e');
      rethrow;
    }
  }

  /// 添加任务样品
  Future<void> addTaskSample(int taskId, String sampleCode, String sampleType, double sampleWeight) async {
    try {
      final sample = SamplingTaskSampleLocal(
        taskSampleId: DateTime.now().millisecondsSinceEpoch, // 临时使用时间戳作为ID
        sampleCode: sampleCode,
        sampleType: sampleType,
        sampleWeight: sampleWeight,
        taskId: taskId,
        createdAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _dao.insertSample(sample);
      _logger.i('添加任务样品成功: $taskId, $sampleCode');
    } catch (e) {
      _logger.e('添加任务样品失败: $e');
      rethrow;
    }
  }

  /// 删除任务样品
  Future<void> deleteTaskSample(int sampleId) async {
    try {
      await _dao.deleteSample(sampleId);
      _logger.i('删除任务样品成功: $sampleId');
    } catch (e) {
      _logger.e('删除任务样品失败: $e');
      rethrow;
    }
  }

  /// 按任务ID删除所有样品
  Future<void> deleteSamplesByTaskId(int taskId) async {
    try {
      await _dao.deleteSamplesByTaskId(taskId);
      _logger.i('按任务ID删除所有样品成功: $taskId');
    } catch (e) {
      _logger.e('按任务ID删除样品失败: $e');
      rethrow;
    }
  }

  /// 删除整个任务（包括相关图片和样品）
  Future<void> deleteTask(int taskId) async {
    try {
      await _dao.deleteTask(taskId);
      _logger.i('删除任务成功: $taskId');
    } catch (e) {
      _logger.e('删除任务失败: $e');
      rethrow;
    }
  }

  /// 同步数据到远程服务器
  Future<Map<String, dynamic>> syncToRemote(int taskId) async {
    try {
      final taskDetail = await getTaskDetail(taskId);
      if (taskDetail == null) {
        throw Exception('任务不存在: $taskId');
      }

      final task = taskDetail['task'] as SamplingTaskLocal;
      final images = taskDetail['images'] as List<SamplingTaskImageLocal>;
      final samples = taskDetail['samples'] as List<SamplingTaskSampleLocal>;

      // 构建远程数据格式
      final remoteData = {
        'taskId': task.taskId,
        'taskCode': task.taskCode,
        'pointCode': task.pointCode,
        'samplingStatus': task.samplingStatus,
        'orgName': task.orgName,
        'orgCode': task.orgCode,
        'longitude': task.longitude,
        'latitude': task.latitude,
        'landUseType': task.landUseType,
        'salineAlkaliFlag': task.salineAlkaliFlag,
        'samplingType': task.samplingType,
        'pointType': task.pointType,
        'surveyLongitude': task.surveyLongitude,
        'surveyLatitude': task.surveyLatitude,
        'correct': task.correct,
        'samplingTaskImage': images.map((img) => {
          'taskImageId': img.taskImageId,
          'imageUrl': img.imageUrl,
          'imageType': img.imageType,
          'imageTime': img.imageTime,
          'imageAngle': img.imageAngle,
          'taskId': img.taskId,
        }).toList(),
        'samplingTaskSample': samples.map((sample) => {
          'taskSampleId': sample.taskSampleId,
          'sampleCode': sample.sampleCode,
          'sampleType': sample.sampleType,
          'sampleWeight': sample.sampleWeight,
          'taskId': sample.taskId,
        }).toList(),
      };

      _logger.i('构建同步数据成功: $taskId');
      return remoteData;
    } catch (e) {
      _logger.e('构建同步数据失败: $e');
      rethrow;
    }
  }

  /// 获取数据库统计信息
  Future<Map<String, dynamic>> getDatabaseStats() async {
    try {
      final allTasks = await _dao.getAllTasks();
      final completedTasks = allTasks.where((task) => task.samplingStatus == "1").length;
      final pendingTasks = allTasks.where((task) => task.samplingStatus != "1").length;

      return {
        'totalTasks': allTasks.length,
        'completedTasks': completedTasks,
        'pendingTasks': pendingTasks,
        'completionRate': allTasks.isNotEmpty ? (completedTasks / allTasks.length * 100).toStringAsFixed(1) : '0.0',
      };
    } catch (e) {
      _logger.e('获取数据库统计信息失败: $e');
      rethrow;
    }
  }

  /// 清空本地数据库
  Future<void> clearLocalDatabase() async {
    try {
      await _dao.clearAllData();
      _logger.i('清空本地数据库成功');
    } catch (e) {
      _logger.e('清空本地数据库失败: $e');
      rethrow;
    }
  }

  /// 检查任务是否存在
  Future<bool> isTaskExists(int taskId) async {
    try {
      final task = await _dao.getTaskByTaskId(taskId);
      return task != null;
    } catch (e) {
      _logger.e('检查任务是否存在失败: $e');
      return false;
    }
  }

  /// 检查任务是否已提交
  /// 返回true表示该任务在本地数据库中是已提交状态 (submitStatus = 2)
  Future<bool> isTaskSubmitted(int taskId) async {
    try {
      final task = await _dao.getTaskByTaskId(taskId);
      if (task == null) {
        return false;
      }
      return task.submitStatus == 2;
    } catch (e) {
      _logger.e('检查任务是否已提交失败: $e');
      return false;
    }
  }

  /// 更新任务状态
  Future<void> updateTaskStatus(int taskId, int status) async {
    try {
      final existingTask = await _dao.getTaskByTaskId(taskId);
      if (existingTask == null) {
        throw Exception('任务不存在: $taskId');
      }

      final updatedTask = existingTask.copyWith(
        samplingStatus: status.toString(),
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _dao.updateTask(updatedTask);
      _logger.i('更新任务状态成功: $taskId -> $status');
    } catch (e) {
      _logger.e('更新任务状态失败: $e');
      rethrow;
    }
  }

  /// 根据提交状态获取任务
  Future<List<SamplingTaskLocal>> getTasksBySubmitStatus(int submitStatus) async {
    try {
      return await _dao.getTasksBySubmitStatus(submitStatus);
    } catch (e) {
      _logger.e('根据提交状态获取任务失败: $e');
      rethrow;
    }
  }

  /// 更新任务提交状态
  Future<void> updateTaskSubmitStatus(int taskId, int submitStatus) async {
    try {
      final existingTask = await _dao.getTaskByTaskId(taskId);
      if (existingTask == null) {
        throw Exception('任务不存在: $taskId');
      }

      final updatedTask = existingTask.copyWith(
        submitStatus: submitStatus,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _dao.updateTask(updatedTask);
      _logger.i('更新任务提交状态成功: $taskId -> $submitStatus');
    } catch (e) {
      _logger.e('更新任务提交状态失败: $e');
      rethrow;
    }
  }

  /// 更新任务采样状态
  Future<void> updateTaskSamplingStatus(int taskId, String samplingStatus) async {
    try {
      final existingTask = await _dao.getTaskByTaskId(taskId);
      if (existingTask == null) {
        throw Exception('任务不存在: $taskId');
      }

      final updatedTask = existingTask.copyWith(
        samplingStatus: samplingStatus,
        updatedAt: DateTime.now().millisecondsSinceEpoch,
      );

      await _dao.updateTask(updatedTask);
      _logger.i('更新任务采样状态成功: $taskId -> $samplingStatus');
    } catch (e) {
      _logger.e('更新任务采样状态失败: $e');
      rethrow;
    }
  }

  /// 将本地数据转换为远程API格式
  Map<String, dynamic> convertLocalDataToRemoteFormat(Map<String, dynamic> localData) {
    final task = localData['task'] as SamplingTaskLocal;
    final images = localData['images'] as List<SamplingTaskImageLocal>;
    final samples = localData['samples'] as List<SamplingTaskSampleLocal>;

    return {
      'taskId': task.taskId,
      'taskCode': task.taskCode,
      'pointCode': task.pointCode,
      'samplingStatus': task.samplingStatus,
      'orgName': task.orgName,
      'orgCode': task.orgCode,
      'longitude': task.longitude,
      'latitude': task.latitude,
      'landUseType': task.landUseType,
      'salineAlkaliFlag': task.salineAlkaliFlag,
      'samplingType': task.samplingType,
      'pointType': task.pointType,
      'surveyLongitude': task.surveyLongitude,
      'surveyLatitude': task.surveyLatitude,
      'correct': task.correct,
      'samplingTaskImage': images.map((img) => {
        'taskImageId': img.taskImageId,
        'imageUrl': img.imageUrl,
        'imageType': img.imageType,
        'imageTime': img.imageTime,
        'imageAngle': img.imageAngle,
        'taskId': img.taskId,
      }).toList(),
      'samplingTaskSample': samples.map((sample) => {
        'taskSampleId': sample.taskSampleId,
        'sampleCode': sample.sampleCode,
        'sampleType': sample.sampleType,
        'sampleWeight': sample.sampleWeight,
        'taskId': sample.taskId,
      }).toList(),
    };
  }
} 