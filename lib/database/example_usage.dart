import 'package:logger/logger.dart';
import 'sampling_task_service.dart';

/// 采样任务数据库使用示例
class SamplingTaskDatabaseExample {
  final SamplingTaskService _service = SamplingTaskService();
  final Logger _logger = Logger();

  /// 示例：保存远程数据到本地数据库
  Future<void> exampleSaveRemoteData() async {
    try {
      // 您提供的示例数据
      final Map<String, dynamic> remoteData = {
        "taskId": 7,
        "taskCode": "OUESnjXV46",
        "pointCode": "w2g4uHMbWa",
        "samplingStatus": "1",
        "orgName": "二九〇农场",
        "orgCode": "860101",
        "longitude": "126.413719999999998",
        "latitude": "45.4028440000000018",
        "landUseType": "0201",
        "salineAlkaliFlag": 1,
        "samplingType": "1",
        "pointType": "1",
        "samplingTaskImage": [
          {
            "taskImageId": 71,
            "imageUrl": "http://10.11.14.211:30111/bdh-dev-new/bdh-soil-census/c263bb9c4c2144a4b8731683f4919e901747388226088.png",
            "imageType": "O",
            "imageTime": 1747290981725,
            "imageAngle": null,
            "taskId": 7
          },
          {
            "taskImageId": 70,
            "imageUrl": "http://10.11.14.211:30111/bdh-dev-new/bdh-soil-census/b9a815463b284dbf9b37bf54c694a88c1748417506580.mp4",
            "imageType": "O",
            "imageTime": 1747290979525,
            "imageAngle": null,
            "taskId": 7
          },
          {
            "taskImageId": 69,
            "imageUrl": "http://10.11.14.211:30111/bdh-dev-new/bdh-soil-census/b9a815463b284dbf9b37bf54c694a88c1748417506580.mp4",
            "imageType": "N",
            "imageTime": 1747290974022,
            "imageAngle": null,
            "taskId": 7
          },
          {
            "taskImageId": 68,
            "imageUrl": "http://10.11.14.211:30111/bdh-dev-new/bdh-soil-census/c263bb9c4c2144a4b8731683f4919e901747388226088.png",
            "imageType": "W",
            "imageTime": 1747290966900,
            "imageAngle": null,
            "taskId": 7
          },
          {
            "taskImageId": 67,
            "imageUrl": "http://10.11.14.211:30111/bdh-dev-new/bdh-soil-census/16383444f0344ea981ef59d9e67000d11747635055913.png",
            "imageType": "S",
            "imageTime": 1747624208426,
            "imageAngle": null,
            "taskId": 7
          },
          {
            "taskImageId": 66,
            "imageUrl": "http://10.11.14.211:30111/bdh-dev-new/bdh-soil-census/06c89d358ca54de2a325e8102d9a4d521747624118155.png",
            "imageType": "E",
            "imageTime": 1747624118507,
            "imageAngle": null,
            "taskId": 7
          }
        ],
        "samplingTaskSample": [
          {
            "taskSampleId": 5,
            "sampleCode": "SAMPLE-002",
            "sampleType": "surface",
            "sampleWeight": 2.51,
            "taskId": 7
          },
          {
            "taskSampleId": 4,
            "sampleCode": "SAMPLE-003",
            "sampleType": "surface",
            "sampleWeight": 5.5,
            "taskId": 7
          }
        ],
        "surveyLongitude": 126.41322,
        "surveyLatitude": 45.402844,
        "correct": null
      };

      // 保存到本地数据库
      await _service.saveRemoteDataToLocal(remoteData);
      _logger.i('示例数据保存成功');

    } catch (e) {
      _logger.e('保存示例数据失败: $e');
    }
  }

  /// 示例：查询任务详情
  Future<void> exampleGetTaskDetail() async {
    try {
      final taskDetail = await _service.getTaskDetail(7);
      
      if (taskDetail != null) {
        _logger.i('任务详情查询成功');
        _logger.i('任务信息: ${taskDetail['task']}');
        _logger.i('图片数量: ${(taskDetail['images'] as List).length}');
        _logger.i('样品数量: ${(taskDetail['samples'] as List).length}');
        
        // 按方向分类的图片
        final imagesByType = taskDetail['imagesByType'] as Map<String, dynamic>;
        _logger.i('东方向图片: ${imagesByType['E']?.length ?? 0} 张');
        _logger.i('南方向图片: ${imagesByType['S']?.length ?? 0} 张');
        _logger.i('西方向图片: ${imagesByType['W']?.length ?? 0} 张');
        _logger.i('北方向图片: ${imagesByType['N']?.length ?? 0} 张');
        _logger.i('其他图片: ${imagesByType['O']?.length ?? 0} 张');
      } else {
        _logger.w('任务不存在');
      }
    } catch (e) {
      _logger.e('查询任务详情失败: $e');
    }
  }

  /// 示例：更新任务表单数据
  Future<void> exampleUpdateTaskFormData() async {
    try {
      final formData = {
        'landUseType': '0202', // 更新土地利用类型
        'samplingType': '2',   // 更新采样类型
        'salineAlkaliFlag': 0, // 更新盐碱地标识
        'pointType': '2',      // 更新点位类型
      };

      await _service.updateTaskFormData(7, formData);
      _logger.i('任务表单数据更新成功');
    } catch (e) {
      _logger.e('更新任务表单数据失败: $e');
    }
  }

  /// 示例：添加任务图片
  Future<void> exampleAddTaskImage() async {
    try {
      await _service.addTaskImage(
        7, 
        'http://example.com/new_image.jpg', 
        'E' // 东方向
      );
      _logger.i('添加任务图片成功');
    } catch (e) {
      _logger.e('添加任务图片失败: $e');
    }
  }

  /// 示例：批量添加任务图片
  Future<void> exampleAddTaskImages() async {
    try {
      final imageDataList = [
        {'url': 'http://example.com/image1.jpg', 'type': 'E'},
        {'url': 'http://example.com/image2.jpg', 'type': 'S'},
        {'url': 'http://example.com/video1.mp4', 'type': 'N'},
      ];

      await _service.addTaskImages(7, imageDataList);
      _logger.i('批量添加任务图片成功');
    } catch (e) {
      _logger.e('批量添加任务图片失败: $e');
    }
  }

  /// 示例：添加任务样品
  Future<void> exampleAddTaskSample() async {
    try {
      await _service.addTaskSample(
        7, 
        'SAMPLE-004', 
        'deep', 
        3.25
      );
      _logger.i('添加任务样品成功');
    } catch (e) {
      _logger.e('添加任务样品失败: $e');
    }
  }

  /// 示例：获取所有本地任务
  Future<void> exampleGetAllLocalTasks() async {
    try {
      final tasks = await _service.getAllLocalTasks();
      _logger.i('本地任务总数: ${tasks.length}');
      
      for (final task in tasks) {
        _logger.i('任务 ${task.taskId}: ${task.taskCode} - ${task.samplingStatus == "1" ? "已采样" : "未采样"}');
      }
    } catch (e) {
      _logger.e('获取所有本地任务失败: $e');
    }
  }

  /// 示例：根据状态查询任务
  Future<void> exampleGetTasksByStatus() async {
    try {
      // 查询已完成的任务
      final completedTasks = await _service.getLocalTasksByStatus("1");
      _logger.i('已完成任务数: ${completedTasks.length}');

      // 查询未完成的任务
      final pendingTasks = await _service.getLocalTasksByStatus("0");
      _logger.i('未完成任务数: ${pendingTasks.length}');
    } catch (e) {
      _logger.e('根据状态查询任务失败: $e');
    }
  }

  /// 示例：分页查询任务
  Future<void> exampleGetTasksWithPagination() async {
    try {
      final tasks = await _service.getLocalTasksWithPagination(
        page: 1,
        pageSize: 10,
        status: "1", // 只查询已完成的任务
        searchKeyword: "二九〇", // 搜索关键词
      );
      
      _logger.i('分页查询结果: ${tasks.length} 条记录');
    } catch (e) {
      _logger.e('分页查询任务失败: $e');
    }
  }

  /// 示例：获取数据库统计信息
  Future<void> exampleGetDatabaseStats() async {
    try {
      final stats = await _service.getDatabaseStats();
      
      _logger.i('=== 数据库统计信息 ===');
      _logger.i('总任务数: ${stats['totalTasks']}');
      _logger.i('已完成任务数: ${stats['completedTasks']}');
      _logger.i('未完成任务数: ${stats['pendingTasks']}');
      _logger.i('完成率: ${stats['completionRate']}%');
    } catch (e) {
      _logger.e('获取数据库统计信息失败: $e');
    }
  }

  /// 示例：同步数据到远程服务器
  Future<void> exampleSyncToRemote() async {
    try {
      final remoteData = await _service.syncToRemote(7);
      
      _logger.i('准备同步到远程服务器的数据:');
      _logger.i('任务ID: ${remoteData['taskId']}');
      _logger.i('任务编码: ${remoteData['taskCode']}');
      _logger.i('图片数量: ${(remoteData['samplingTaskImage'] as List).length}');
      _logger.i('样品数量: ${(remoteData['samplingTaskSample'] as List).length}');
      
      // 这里可以调用实际的远程API
      // await remoteApiService.uploadTaskData(remoteData);
      
    } catch (e) {
      _logger.e('同步数据到远程服务器失败: $e');
    }
  }

  /// 示例：根据图片类型获取图片
  Future<void> exampleGetImagesByType() async {
    try {
      // 获取东方向的图片
      final eastImages = await _service.getImagesByType(7, 'E');
      _logger.i('东方向图片数量: ${eastImages.length}');
      
      // 获取南方向的图片
      final southImages = await _service.getImagesByType(7, 'S');
      _logger.i('南方向图片数量: ${southImages.length}');
      
    } catch (e) {
      _logger.e('根据类型获取图片失败: $e');
    }
  }

  /// 示例：检查任务是否存在
  Future<void> exampleCheckTaskExists() async {
    try {
      final exists = await _service.isTaskExists(7);
      _logger.i('任务 7 是否存在: $exists');
      
      final notExists = await _service.isTaskExists(999);
      _logger.i('任务 999 是否存在: $notExists');
    } catch (e) {
      _logger.e('检查任务是否存在失败: $e');
    }
  }

  /// 运行所有示例
  Future<void> runAllExamples() async {
    _logger.i('=== 开始运行采样任务数据库示例 ===');
    
    // 1. 保存示例数据
    await exampleSaveRemoteData();
    
    // 2. 查询任务详情
    await exampleGetTaskDetail();
    
    // 3. 更新任务表单数据
    await exampleUpdateTaskFormData();
    
    // 4. 添加图片
    await exampleAddTaskImage();
    await exampleAddTaskImages();
    
    // 5. 添加样品
    await exampleAddTaskSample();
    
    // 6. 查询操作
    await exampleGetAllLocalTasks();
    await exampleGetTasksByStatus();
    await exampleGetTasksWithPagination();
    
    // 7. 统计信息
    await exampleGetDatabaseStats();
    
    // 8. 按类型获取图片
    await exampleGetImagesByType();
    
    // 9. 检查任务存在性
    await exampleCheckTaskExists();
    
    // 10. 同步到远程服务器
    await exampleSyncToRemote();
    
    _logger.i('=== 所有示例运行完成 ===');
  }
}

/// 使用方法示例
/// 
/// ```dart
/// final example = SamplingTaskDatabaseExample();
/// 
/// // 运行所有示例
/// await example.runAllExamples();
/// 
/// // 或者单独运行某个示例
/// await example.exampleSaveRemoteData();
/// await example.exampleGetTaskDetail();
/// ``` 