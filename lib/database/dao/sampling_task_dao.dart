import 'package:sqflite/sqflite.dart';
import 'package:logger/logger.dart';
import '../database_helper.dart';
import '../models/sampling_task_local.dart';
import '../models/sampling_task_image_local.dart';
import '../models/sampling_task_sample_local.dart';

class SamplingTaskDao {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final Logger _logger = Logger();

  // ========== 采样任务主表操作 ==========

  /// 插入采样任务
  Future<int> insertTask(SamplingTaskLocal task) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert(
        _dbHelper.tableSamplingTasks,
        task.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      _logger.i('插入采样任务成功: ${task.taskId}, 本地ID: $id');
      return id;
    } catch (e) {
      _logger.e('插入采样任务失败: $e');
      rethrow;
    }
  }

  /// 批量插入采样任务
  Future<void> insertTasksBatch(List<SamplingTaskLocal> tasks) async {
    try {
      final db = await _dbHelper.database;
      final batch = db.batch();
      
      for (final task in tasks) {
        batch.insert(
          _dbHelper.tableSamplingTasks,
          task.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
      
      await batch.commit(noResult: true);
      _logger.i('批量插入采样任务成功: ${tasks.length} 条记录');
    } catch (e) {
      _logger.e('批量插入采样任务失败: $e');
      rethrow;
    }
  }

  /// 更新采样任务
  Future<int> updateTask(SamplingTaskLocal task) async {
    try {
      final db = await _dbHelper.database;
      final updatedTask = task.copyWith(updatedAt: DateTime.now().millisecondsSinceEpoch);
      
      final count = await db.update(
        _dbHelper.tableSamplingTasks,
        updatedTask.toMap(),
        where: 'task_id = ?',
        whereArgs: [task.taskId],
      );
      
      _logger.i('更新采样任务: ${task.taskId}, 影响行数: $count');
      return count;
    } catch (e) {
      _logger.e('更新采样任务失败: $e');
      rethrow;
    }
  }

  /// 删除采样任务（级联删除相关图片和样品）
  Future<int> deleteTask(int taskId) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.delete(
        _dbHelper.tableSamplingTasks,
        where: 'task_id = ?',
        whereArgs: [taskId],
      );
      
      _logger.i('删除采样任务: $taskId, 影响行数: $count');
      return count;
    } catch (e) {
      _logger.e('删除采样任务失败: $e');
      rethrow;
    }
  }

  /// 根据taskId查询采样任务
  Future<SamplingTaskLocal?> getTaskByTaskId(int taskId) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        _dbHelper.tableSamplingTasks,
        where: 'task_id = ?',
        whereArgs: [taskId],
      );
      
      if (maps.isNotEmpty) {
        return SamplingTaskLocal.fromMap(maps.first);
      }
      return null;
    } catch (e) {
      _logger.e('查询采样任务失败: $e');
      rethrow;
    }
  }

  /// 查询所有采样任务
  Future<List<SamplingTaskLocal>> getAllTasks() async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        _dbHelper.tableSamplingTasks,
        orderBy: 'updated_at DESC',
      );
      
      return maps.map((map) => SamplingTaskLocal.fromMap(map)).toList();
    } catch (e) {
      _logger.e('查询所有采样任务失败: $e');
      rethrow;
    }
  }

  /// 根据采样状态查询任务
  Future<List<SamplingTaskLocal>> getTasksByStatus(String status) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        _dbHelper.tableSamplingTasks,
        where: 'sampling_status = ?',
        whereArgs: [status],
        orderBy: 'updated_at DESC',
      );
      
      return maps.map((map) => SamplingTaskLocal.fromMap(map)).toList();
    } catch (e) {
      _logger.e('根据状态查询采样任务失败: $e');
      rethrow;
    }
  }

  /// 根据提交状态查询任务
  Future<List<SamplingTaskLocal>> getTasksBySubmitStatus(int submitStatus) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        _dbHelper.tableSamplingTasks,
        where: 'submit_status = ?',
        whereArgs: [submitStatus],
        orderBy: 'updated_at DESC',
      );
      
      return maps.map((map) => SamplingTaskLocal.fromMap(map)).toList();
    } catch (e) {
      _logger.e('根据提交状态查询采样任务失败: $e');
      rethrow;
    }
  }

  /// 分页查询采样任务
  Future<List<SamplingTaskLocal>> getTasksWithPagination({
    int page = 1,
    int pageSize = 20,
    String? status,
    String? searchKeyword,
  }) async {
    try {
      final db = await _dbHelper.database;
      final offset = (page - 1) * pageSize;
      
      String whereClause = '';
      List<dynamic> whereArgs = [];
      
      if (status != null) {
        whereClause += 'sampling_status = ?';
        whereArgs.add(status);
      }
      
      if (searchKeyword != null && searchKeyword.isNotEmpty) {
        if (whereClause.isNotEmpty) whereClause += ' AND ';
        whereClause += '(task_code LIKE ? OR point_code LIKE ? OR org_name LIKE ?)';
        final keyword = '%$searchKeyword%';
        whereArgs.addAll([keyword, keyword, keyword]);
      }
      
      final maps = await db.query(
        _dbHelper.tableSamplingTasks,
        where: whereClause.isNotEmpty ? whereClause : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        orderBy: 'updated_at DESC',
        limit: pageSize,
        offset: offset,
      );
      
      return maps.map((map) => SamplingTaskLocal.fromMap(map)).toList();
    } catch (e) {
      _logger.e('分页查询采样任务失败: $e');
      rethrow;
    }
  }

  // ========== 采样任务图片操作 ==========

  /// 插入图片
  Future<int> insertImage(SamplingTaskImageLocal image) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert(
        _dbHelper.tableSamplingTaskImages,
        image.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      _logger.i('插入图片成功: ${image.imageUrl}, 本地ID: $id');
      return id;
    } catch (e) {
      _logger.e('插入图片失败: $e');
      rethrow;
    }
  }

  /// 批量插入图片
  Future<void> insertImagesBatch(List<SamplingTaskImageLocal> images) async {
    try {
      final db = await _dbHelper.database;
      final batch = db.batch();
      
      for (final image in images) {
        batch.insert(
          _dbHelper.tableSamplingTaskImages,
          image.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
      
      await batch.commit(noResult: true);
      _logger.i('批量插入图片成功: ${images.length} 条记录');
    } catch (e) {
      _logger.e('批量插入图片失败: $e');
      rethrow;
    }
  }

  /// 根据taskId查询图片
  Future<List<SamplingTaskImageLocal>> getImagesByTaskId(int taskId) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        _dbHelper.tableSamplingTaskImages,
        where: 'task_id = ?',
        whereArgs: [taskId],
        orderBy: 'image_time ASC',
      );
      
      return maps.map((map) => SamplingTaskImageLocal.fromMap(map)).toList();
    } catch (e) {
      _logger.e('查询图片失败: $e');
      rethrow;
    }
  }

  /// 根据图片类型查询图片
  Future<List<SamplingTaskImageLocal>> getImagesByType(int taskId, String imageType) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        _dbHelper.tableSamplingTaskImages,
        where: 'task_id = ? AND image_type = ?',
        whereArgs: [taskId, imageType],
        orderBy: 'image_time ASC',
      );
      
      return maps.map((map) => SamplingTaskImageLocal.fromMap(map)).toList();
    } catch (e) {
      _logger.e('根据类型查询图片失败: $e');
      rethrow;
    }
  }

  /// 删除图片
  Future<int> deleteImage(int imageId) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.delete(
        _dbHelper.tableSamplingTaskImages,
        where: 'id = ?',
        whereArgs: [imageId],
      );
      
      _logger.i('删除图片: $imageId, 影响行数: $count');
      return count;
    } catch (e) {
      _logger.e('删除图片失败: $e');
      rethrow;
    }
  }

  /// 按任务ID删除所有图片
  Future<int> deleteImagesByTaskId(int taskId) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.delete(
        _dbHelper.tableSamplingTaskImages,
        where: 'task_id = ?',
        whereArgs: [taskId],
      );
      
      _logger.i('按任务ID删除图片: $taskId, 影响行数: $count');
      return count;
    } catch (e) {
      _logger.e('按任务ID删除图片失败: $e');
      rethrow;
    }
  }

  // ========== 采样任务样品操作 ==========

  /// 插入样品
  Future<int> insertSample(SamplingTaskSampleLocal sample) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert(
        _dbHelper.tableSamplingTaskSamples,
        sample.toMap(),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
      
      _logger.i('插入样品成功: ${sample.sampleCode}, 本地ID: $id');
      return id;
    } catch (e) {
      _logger.e('插入样品失败: $e');
      rethrow;
    }
  }

  /// 批量插入样品
  Future<void> insertSamplesBatch(List<SamplingTaskSampleLocal> samples) async {
    try {
      final db = await _dbHelper.database;
      final batch = db.batch();
      
      for (final sample in samples) {
        batch.insert(
          _dbHelper.tableSamplingTaskSamples,
          sample.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
      
      await batch.commit(noResult: true);
      _logger.i('批量插入样品成功: ${samples.length} 条记录');
    } catch (e) {
      _logger.e('批量插入样品失败: $e');
      rethrow;
    }
  }

  /// 根据taskId查询样品
  Future<List<SamplingTaskSampleLocal>> getSamplesByTaskId(int taskId) async {
    try {
      final db = await _dbHelper.database;
      final maps = await db.query(
        _dbHelper.tableSamplingTaskSamples,
        where: 'task_id = ?',
        whereArgs: [taskId],
        orderBy: 'created_at ASC',
      );
      
      return maps.map((map) => SamplingTaskSampleLocal.fromMap(map)).toList();
    } catch (e) {
      _logger.e('查询样品失败: $e');
      rethrow;
    }
  }

  /// 删除样品
  Future<int> deleteSample(int sampleId) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.delete(
        _dbHelper.tableSamplingTaskSamples,
        where: 'id = ?',
        whereArgs: [sampleId],
      );
      
      _logger.i('删除样品: $sampleId, 影响行数: $count');
      return count;
    } catch (e) {
      _logger.e('删除样品失败: $e');
      rethrow;
    }
  }

  /// 按任务ID删除所有样品
  Future<int> deleteSamplesByTaskId(int taskId) async {
    try {
      final db = await _dbHelper.database;
      final count = await db.delete(
        _dbHelper.tableSamplingTaskSamples,
        where: 'task_id = ?',
        whereArgs: [taskId],
      );
      
      _logger.i('按任务ID删除样品: $taskId, 影响行数: $count');
      return count;
    } catch (e) {
      _logger.e('按任务ID删除样品失败: $e');
      rethrow;
    }
  }

  // ========== 复合操作 ==========

  /// 保存完整的采样任务数据（包括图片和样品）
  Future<void> saveCompleteTaskData(Map<String, dynamic> remoteData) async {
    try {
      final db = await _dbHelper.database;
      
      await db.transaction((txn) async {
        // 1. 保存主任务
        final task = SamplingTaskLocal.fromRemoteData(remoteData);
        await txn.insert(
          _dbHelper.tableSamplingTasks,
          task.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
        
        // 2. 保存图片
        final images = remoteData['samplingTaskImage'] as List?;
        if (images != null && images.isNotEmpty) {
          final batch = txn.batch();
          
          // 先删除旧图片
          batch.delete(
            _dbHelper.tableSamplingTaskImages,
            where: 'task_id = ?',
            whereArgs: [task.taskId],
          );
          
          // 插入新图片
          for (final imageData in images) {
            final image = SamplingTaskImageLocal.fromRemoteData(imageData, task.taskId);
            batch.insert(
              _dbHelper.tableSamplingTaskImages,
              image.toMap(),
            );
          }
          
          await batch.commit(noResult: true);
        }
        
        // 3. 保存样品
        final samples = remoteData['samplingTaskSample'] as List?;
        if (samples != null && samples.isNotEmpty) {
          final batch = txn.batch();
          
          // 先删除旧样品
          batch.delete(
            _dbHelper.tableSamplingTaskSamples,
            where: 'task_id = ?',
            whereArgs: [task.taskId],
          );
          
          // 插入新样品
          for (final sampleData in samples) {
            final sample = SamplingTaskSampleLocal.fromRemoteData(sampleData, task.taskId);
            batch.insert(
              _dbHelper.tableSamplingTaskSamples,
              sample.toMap(),
            );
          }
          
          await batch.commit(noResult: true);
        }
      });
      
      _logger.i('保存完整任务数据成功: ${remoteData['taskId']}');
    } catch (e) {
      _logger.e('保存完整任务数据失败: $e');
      rethrow;
    }
  }

  /// 获取完整的任务数据（包括图片和样品）
  Future<Map<String, dynamic>?> getCompleteTaskData(int taskId) async {
    try {
      final task = await getTaskByTaskId(taskId);
      if (task == null) return null;
      
      final images = await getImagesByTaskId(taskId);
      final samples = await getSamplesByTaskId(taskId);
      
      return {
        'task': task,
        'images': images,
        'samples': samples,
      };
    } catch (e) {
      _logger.e('获取完整任务数据失败: $e');
      rethrow;
    }
  }

  /// 清空所有数据
  Future<void> clearAllData() async {
    try {
      final db = await _dbHelper.database;
      
      await db.transaction((txn) async {
        await txn.delete(_dbHelper.tableSamplingTaskImages);
        await txn.delete(_dbHelper.tableSamplingTaskSamples);
        await txn.delete(_dbHelper.tableSamplingTasks);
      });
      
      _logger.i('清空所有数据成功');
    } catch (e) {
      _logger.e('清空所有数据失败: $e');
      rethrow;
    }
  }
} 