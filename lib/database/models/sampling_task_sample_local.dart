class SamplingTaskSampleLocal {
  final int? id; // 本地主键
  final int taskSampleId;
  final String sampleCode;
  final String sampleType;
  final double sampleWeight;
  final int taskId;
  final int createdAt; // 创建时间戳

  SamplingTaskSampleLocal({
    this.id,
    required this.taskSampleId,
    required this.sampleCode,
    required this.sampleType,
    required this.sampleWeight,
    required this.taskId,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'task_sample_id': taskSampleId,
      'sample_code': sampleCode,
      'sample_type': sampleType,
      'sample_weight': sampleWeight,
      'task_id': taskId,
      'created_at': createdAt,
    };
  }

  factory SamplingTaskSampleLocal.fromMap(Map<String, dynamic> map) {
    return SamplingTaskSampleLocal(
      id: map['id']?.toInt(),
      taskSampleId: map['task_sample_id']?.toInt() ?? 0,
      sampleCode: map['sample_code'] ?? '',
      sampleType: map['sample_type'] ?? '',
      sampleWeight: map['sample_weight']?.toDouble() ?? 0.0,
      taskId: map['task_id']?.toInt() ?? 0,
      createdAt: map['created_at']?.toInt() ?? 0,
    );
  }

  // 从远程数据创建本地数据
  factory SamplingTaskSampleLocal.fromRemoteData(Map<String, dynamic> remoteData, int taskId) {
    return SamplingTaskSampleLocal(
      taskSampleId: remoteData['taskSampleId']?.toInt() ?? 0,
      sampleCode: remoteData['sampleCode'] ?? '',
      sampleType: remoteData['sampleType'] ?? '',
      sampleWeight: remoteData['sampleWeight']?.toDouble() ?? 0.0,
      taskId: taskId,
      createdAt: DateTime.now().millisecondsSinceEpoch,
    );
  }

  SamplingTaskSampleLocal copyWith({
    int? id,
    int? taskSampleId,
    String? sampleCode,
    String? sampleType,
    double? sampleWeight,
    int? taskId,
    int? createdAt,
  }) {
    return SamplingTaskSampleLocal(
      id: id ?? this.id,
      taskSampleId: taskSampleId ?? this.taskSampleId,
      sampleCode: sampleCode ?? this.sampleCode,
      sampleType: sampleType ?? this.sampleType,
      sampleWeight: sampleWeight ?? this.sampleWeight,
      taskId: taskId ?? this.taskId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'SamplingTaskSampleLocal(id: $id, taskSampleId: $taskSampleId, sampleCode: $sampleCode, sampleType: $sampleType, sampleWeight: $sampleWeight, taskId: $taskId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SamplingTaskSampleLocal && other.taskSampleId == taskSampleId;
  }

  @override
  int get hashCode => taskSampleId.hashCode;
} 