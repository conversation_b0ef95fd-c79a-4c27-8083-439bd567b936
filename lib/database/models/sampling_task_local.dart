class SamplingTaskLocal {
  final int? id; // 本地主键
  final int taskId;
  final String taskCode;
  final String pointCode;
  final String samplingStatus;
  final String orgName;
  final String orgCode;
  final String longitude;
  final String latitude;
  final String? landUseType;
  final int? salineAlkaliFlag;
  final String? samplingType;
  final String? pointType;
  final double? surveyLongitude;
  final double? surveyLatitude;
  final String? correct;
  final double? samplingPointRange; // 采样点范围，单位米
  final int? submitStatus; // 提交状态：0-未提交，2-已提交
  final int createdAt; // 创建时间戳
  final int updatedAt; // 更新时间戳

  SamplingTaskLocal({
    this.id,
    required this.taskId,
    required this.taskCode,
    required this.pointCode,
    required this.samplingStatus,
    required this.orgName,
    required this.orgCode,
    required this.longitude,
    required this.latitude,
    this.landUseType,
    this.salineAlkaliFlag,
    this.samplingType,
    this.pointType,
    this.surveyLongitude,
    this.surveyLatitude,
    this.correct,
    this.samplingPointRange,
    this.submitStatus,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'task_id': taskId,
      'task_code': taskCode,
      'point_code': pointCode,
      'sampling_status': samplingStatus,
      'org_name': orgName,
      'org_code': orgCode,
      'longitude': longitude,
      'latitude': latitude,
      'land_use_type': landUseType,
      'saline_alkali_flag': salineAlkaliFlag,
      'sampling_type': samplingType,
      'point_type': pointType,
      'survey_longitude': surveyLongitude,
      'survey_latitude': surveyLatitude,
      'correct': correct,
      'sampling_point_range': samplingPointRange,
      'submit_status': submitStatus,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  factory SamplingTaskLocal.fromMap(Map<String, dynamic> map) {
    return SamplingTaskLocal(
      id: map['id']?.toInt(),
      taskId: map['task_id']?.toInt() ?? 0,
      taskCode: map['task_code'] ?? '',
      pointCode: map['point_code'] ?? '',
      samplingStatus: map['sampling_status'] ?? '0',
      orgName: map['org_name'] ?? '',
      orgCode: map['org_code'] ?? '',
      longitude: map['longitude'] ?? '',
      latitude: map['latitude'] ?? '',
      landUseType: map['land_use_type'],
      salineAlkaliFlag: map['saline_alkali_flag']?.toInt(),
      samplingType: map['sampling_type'],
      pointType: map['point_type'],
      surveyLongitude: map['survey_longitude']?.toDouble(),
      surveyLatitude: map['survey_latitude']?.toDouble(),
      correct: map['correct'],
      samplingPointRange: map['sampling_point_range']?.toDouble(),
      submitStatus: map['submit_status']?.toInt() ?? 0, // 默认为未提交
      createdAt: map['created_at']?.toInt() ?? 0,
      updatedAt: map['updated_at']?.toInt() ?? 0,
    );
  }

  // 从远程数据创建本地数据
  factory SamplingTaskLocal.fromRemoteData(Map<String, dynamic> remoteData) {
    return SamplingTaskLocal(
      taskId: remoteData['taskId']?.toInt() ?? 0,
      taskCode: remoteData['taskCode'] ?? '',
      pointCode: remoteData['pointCode'] ?? '',
      samplingStatus: remoteData['samplingStatus']?.toString() ?? '0',
      orgName: remoteData['orgName'] ?? '',
      orgCode: remoteData['orgCode'] ?? '',
      longitude: remoteData['longitude']?.toString() ?? '',
      latitude: remoteData['latitude']?.toString() ?? '',
      landUseType: remoteData['landUseType'],
      salineAlkaliFlag: remoteData['salineAlkaliFlag']?.toInt(),
      samplingType: remoteData['samplingType'],
      pointType: remoteData['pointType'],
      surveyLongitude: remoteData['surveyLongitude']?.toDouble(),
      surveyLatitude: remoteData['surveyLatitude']?.toDouble(),
      correct: remoteData['correct'],
      samplingPointRange: remoteData['samplingPointRange']?.toDouble(),
      submitStatus: 0, // 新创建的任务默认为未提交
      createdAt: DateTime.now().millisecondsSinceEpoch,
      updatedAt: DateTime.now().millisecondsSinceEpoch,
    );
  }

  SamplingTaskLocal copyWith({
    int? id,
    int? taskId,
    String? taskCode,
    String? pointCode,
    String? samplingStatus,
    String? orgName,
    String? orgCode,
    String? longitude,
    String? latitude,
    String? landUseType,
    int? salineAlkaliFlag,
    String? samplingType,
    String? pointType,
    double? surveyLongitude,
    double? surveyLatitude,
    String? correct,
    double? samplingPointRange,
    int? submitStatus,
    int? createdAt,
    int? updatedAt,
  }) {
    return SamplingTaskLocal(
      id: id ?? this.id,
      taskId: taskId ?? this.taskId,
      taskCode: taskCode ?? this.taskCode,
      pointCode: pointCode ?? this.pointCode,
      samplingStatus: samplingStatus ?? this.samplingStatus,
      orgName: orgName ?? this.orgName,
      orgCode: orgCode ?? this.orgCode,
      longitude: longitude ?? this.longitude,
      latitude: latitude ?? this.latitude,
      landUseType: landUseType ?? this.landUseType,
      salineAlkaliFlag: salineAlkaliFlag ?? this.salineAlkaliFlag,
      samplingType: samplingType ?? this.samplingType,
      pointType: pointType ?? this.pointType,
      surveyLongitude: surveyLongitude ?? this.surveyLongitude,
      surveyLatitude: surveyLatitude ?? this.surveyLatitude,
      correct: correct ?? this.correct,
      samplingPointRange: samplingPointRange ?? this.samplingPointRange,
      submitStatus: submitStatus ?? this.submitStatus,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'SamplingTaskLocal(id: $id, taskId: $taskId, taskCode: $taskCode, pointCode: $pointCode, samplingStatus: $samplingStatus, submitStatus: $submitStatus, orgName: $orgName)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SamplingTaskLocal && other.taskId == taskId;
  }

  @override
  int get hashCode => taskId.hashCode;
} 