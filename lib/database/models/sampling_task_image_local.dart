class SamplingTaskImageLocal {
  final int? id; // 本地主键
  final int taskImageId;
  final String imageUrl;
  final String imageType;
  final int imageTime;
  final String? imageAngle;
  final int taskId;
  final int createdAt; // 创建时间戳

  SamplingTaskImageLocal({
    this.id,
    required this.taskImageId,
    required this.imageUrl,
    required this.imageType,
    required this.imageTime,
    this.imageAngle,
    required this.taskId,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'task_image_id': taskImageId,
      'image_url': imageUrl,
      'image_type': imageType,
      'image_time': imageTime,
      'image_angle': imageAngle,
      'task_id': taskId,
      'created_at': createdAt,
    };
  }

  factory SamplingTaskImageLocal.fromMap(Map<String, dynamic> map) {
    return SamplingTaskImageLocal(
      id: map['id']?.toInt(),
      taskImageId: map['task_image_id']?.toInt() ?? 0,
      imageUrl: map['image_url'] ?? '',
      imageType: map['image_type'] ?? '',
      imageTime: map['image_time']?.toInt() ?? 0,
      imageAngle: map['image_angle'],
      taskId: map['task_id']?.toInt() ?? 0,
      createdAt: map['created_at']?.toInt() ?? 0,
    );
  }

  // 从远程数据创建本地数据
  factory SamplingTaskImageLocal.fromRemoteData(Map<String, dynamic> remoteData, int taskId) {
    return SamplingTaskImageLocal(
      taskImageId: remoteData['taskImageId']?.toInt() ?? 0,
      imageUrl: remoteData['imageUrl'] ?? '',
      imageType: remoteData['imageType'] ?? '',
      imageTime: remoteData['imageTime']?.toInt() ?? 0,
      imageAngle: remoteData['imageAngle'],
      taskId: taskId,
      createdAt: DateTime.now().millisecondsSinceEpoch,
    );
  }

  SamplingTaskImageLocal copyWith({
    int? id,
    int? taskImageId,
    String? imageUrl,
    String? imageType,
    int? imageTime,
    String? imageAngle,
    int? taskId,
    int? createdAt,
  }) {
    return SamplingTaskImageLocal(
      id: id ?? this.id,
      taskImageId: taskImageId ?? this.taskImageId,
      imageUrl: imageUrl ?? this.imageUrl,
      imageType: imageType ?? this.imageType,
      imageTime: imageTime ?? this.imageTime,
      imageAngle: imageAngle ?? this.imageAngle,
      taskId: taskId ?? this.taskId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'SamplingTaskImageLocal(id: $id, taskImageId: $taskImageId, imageUrl: $imageUrl, imageType: $imageType, taskId: $taskId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SamplingTaskImageLocal && other.taskImageId == taskImageId;
  }

  @override
  int get hashCode => taskImageId.hashCode;
} 