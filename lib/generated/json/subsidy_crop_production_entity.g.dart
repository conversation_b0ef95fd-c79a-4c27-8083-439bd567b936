import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_crop_production_entity.dart';

SubsidyCropProductionEntity $SubsidyCropProductionEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyCropProductionEntity subsidyCropProductionEntity = SubsidyCropProductionEntity();
  final String? subsidyDetailId = jsonConvert.convert<String>(
      json['subsidyDetailId']);
  if (subsidyDetailId != null) {
    subsidyCropProductionEntity.subsidyDetailId = subsidyDetailId;
  }
  final String? subsidyPaymentNo = jsonConvert.convert<String>(
      json['subsidyPaymentNo']);
  if (subsidyPaymentNo != null) {
    subsidyCropProductionEntity.subsidyPaymentNo = subsidyPaymentNo;
  }
  final String? subsidyYear = jsonConvert.convert<String>(json['subsidyYear']);
  if (subsidyYear != null) {
    subsidyCropProductionEntity.subsidyYear = subsidyYear;
  }
  final String? subsidyItemName = jsonConvert.convert<String>(
      json['subsidyItemName']);
  if (subsidyItemName != null) {
    subsidyCropProductionEntity.subsidyItemName = subsidyItemName;
  }
  final String? subsidyConfigId = jsonConvert.convert<String>(
      json['subsidyConfigId']);
  if (subsidyConfigId != null) {
    subsidyCropProductionEntity.subsidyConfigId = subsidyConfigId;
  }
  final String? subsidyType = jsonConvert.convert<String>(json['subsidyType']);
  if (subsidyType != null) {
    subsidyCropProductionEntity.subsidyType = subsidyType;
  }
  final String? subsidyCropCode = jsonConvert.convert<String>(
      json['subsidyCropCode']);
  if (subsidyCropCode != null) {
    subsidyCropProductionEntity.subsidyCropCode = subsidyCropCode;
  }
  final String? organizationNo = jsonConvert.convert<String>(
      json['organizationNo']);
  if (organizationNo != null) {
    subsidyCropProductionEntity.organizationNo = organizationNo;
  }
  final String? contractSerialNumber = jsonConvert.convert<String>(
      json['contractSerialNumber']);
  if (contractSerialNumber != null) {
    subsidyCropProductionEntity.contractSerialNumber = contractSerialNumber;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    subsidyCropProductionEntity.farmerName = farmerName;
  }
  final String? farmerIdNumber = jsonConvert.convert<String>(
      json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    subsidyCropProductionEntity.farmerIdNumber = farmerIdNumber;
  }
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  if (bankAccount != null) {
    subsidyCropProductionEntity.bankAccount = bankAccount;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    subsidyCropProductionEntity.bankName = bankName;
  }
  final String? landNumber = jsonConvert.convert<String>(json['landNumber']);
  if (landNumber != null) {
    subsidyCropProductionEntity.landNumber = landNumber;
  }
  final String? actualPaymentFee = jsonConvert.convert<String>(
      json['actualPaymentFee']);
  if (actualPaymentFee != null) {
    subsidyCropProductionEntity.actualPaymentFee = actualPaymentFee;
  }
  final String? subsidyStandard1 = jsonConvert.convert<String>(
      json['subsidyStandard1']);
  if (subsidyStandard1 != null) {
    subsidyCropProductionEntity.subsidyStandard1 = subsidyStandard1;
  }
  final String? subsidyArea1 = jsonConvert.convert<String>(
      json['subsidyArea1']);
  if (subsidyArea1 != null) {
    subsidyCropProductionEntity.subsidyArea1 = subsidyArea1;
  }
  final String? subsidyFee1 = jsonConvert.convert<String>(json['subsidyFee1']);
  if (subsidyFee1 != null) {
    subsidyCropProductionEntity.subsidyFee1 = subsidyFee1;
  }
  final String? subsidyStandard2 = jsonConvert.convert<String>(
      json['subsidyStandard2']);
  if (subsidyStandard2 != null) {
    subsidyCropProductionEntity.subsidyStandard2 = subsidyStandard2;
  }
  final String? subsidyArea2 = jsonConvert.convert<String>(
      json['subsidyArea2']);
  if (subsidyArea2 != null) {
    subsidyCropProductionEntity.subsidyArea2 = subsidyArea2;
  }
  final String? subsidyFee2 = jsonConvert.convert<String>(json['subsidyFee2']);
  if (subsidyFee2 != null) {
    subsidyCropProductionEntity.subsidyFee2 = subsidyFee2;
  }
  final String? subsidyProjectCode = jsonConvert.convert<String>(
      json['subsidyProjectCode']);
  if (subsidyProjectCode != null) {
    subsidyCropProductionEntity.subsidyProjectCode = subsidyProjectCode;
  }
  final String? organizationName = jsonConvert.convert<String>(
      json['organizationName']);
  if (organizationName != null) {
    subsidyCropProductionEntity.organizationName = organizationName;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    subsidyCropProductionEntity.farmerId = farmerId;
  }
  final String? approvalStatusNo = jsonConvert.convert<String>(
      json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    subsidyCropProductionEntity.approvalStatusNo = approvalStatusNo;
  }
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    subsidyCropProductionEntity.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    subsidyCropProductionEntity.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    subsidyCropProductionEntity.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    subsidyCropProductionEntity.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    subsidyCropProductionEntity.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    subsidyCropProductionEntity.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    subsidyCropProductionEntity.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    subsidyCropProductionEntity.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    subsidyCropProductionEntity.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    subsidyCropProductionEntity.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    subsidyCropProductionEntity.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    subsidyCropProductionEntity.auditCTime = auditCTime;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    subsidyCropProductionEntity.auditDId = auditDId;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    subsidyCropProductionEntity.auditDFlag = auditDFlag;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    subsidyCropProductionEntity.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    subsidyCropProductionEntity.auditDTime = auditDTime;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    subsidyCropProductionEntity.auditEId = auditEId;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    subsidyCropProductionEntity.auditEFlag = auditEFlag;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    subsidyCropProductionEntity.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    subsidyCropProductionEntity.auditETime = auditETime;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    subsidyCropProductionEntity.auditFId = auditFId;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    subsidyCropProductionEntity.auditFFlag = auditFFlag;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    subsidyCropProductionEntity.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    subsidyCropProductionEntity.auditFTime = auditFTime;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    subsidyCropProductionEntity.auditGId = auditGId;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    subsidyCropProductionEntity.auditGFlag = auditGFlag;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    subsidyCropProductionEntity.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    subsidyCropProductionEntity.auditGTime = auditGTime;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    subsidyCropProductionEntity.auditHId = auditHId;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    subsidyCropProductionEntity.auditHFlag = auditHFlag;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    subsidyCropProductionEntity.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    subsidyCropProductionEntity.auditHTime = auditHTime;
  }
  final String? currentAuditRoleId = jsonConvert.convert<String>(
      json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    subsidyCropProductionEntity.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName = jsonConvert.convert<String>(
      json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    subsidyCropProductionEntity.currentAuditRoleName = currentAuditRoleName;
  }
  final String? approvalRemark = jsonConvert.convert<String>(
      json['approvalRemark']);
  if (approvalRemark != null) {
    subsidyCropProductionEntity.approvalRemark = approvalRemark;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    subsidyCropProductionEntity.auditLevel = auditLevel;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    subsidyCropProductionEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    subsidyCropProductionEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    subsidyCropProductionEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    subsidyCropProductionEntity.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    subsidyCropProductionEntity.statusCd = statusCd;
  }
  final String? dataStatus = jsonConvert.convert<String>(json['dataStatus']);
  if (dataStatus != null) {
    subsidyCropProductionEntity.dataStatus = dataStatus;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    subsidyCropProductionEntity.params = params;
  }
  final String? subsidyPaymentDetailSubs = jsonConvert.convert<String>(
      json['subsidyPaymentDetailSubs']);
  if (subsidyPaymentDetailSubs != null) {
    subsidyCropProductionEntity.subsidyPaymentDetailSubs =
        subsidyPaymentDetailSubs;
  }
  final String? paymentStatus = jsonConvert.convert<String>(
      json['paymentStatus']);
  if (paymentStatus != null) {
    subsidyCropProductionEntity.paymentStatus = paymentStatus;
  }
  final String? paymentRemark = jsonConvert.convert<String>(
      json['paymentRemark']);
  if (paymentRemark != null) {
    subsidyCropProductionEntity.paymentRemark = paymentRemark;
  }
  final String? children = jsonConvert.convert<String>(json['children']);
  if (children != null) {
    subsidyCropProductionEntity.children = children;
  }
  final String? subsidyTypeName = jsonConvert.convert<String>(
      json['subsidyTypeName']);
  if (subsidyTypeName != null) {
    subsidyCropProductionEntity.subsidyTypeName = subsidyTypeName;
  }
  final String? ifUpdate = jsonConvert.convert<String>(json['ifUpdate']);
  if (ifUpdate != null) {
    subsidyCropProductionEntity.ifUpdate = ifUpdate;
  }
  final String? contractSignType = jsonConvert.convert<String>(
      json['contractSignType']);
  if (contractSignType != null) {
    subsidyCropProductionEntity.contractSignType = contractSignType;
  }
  final String? telephone = jsonConvert.convert<String>(json['telephone']);
  if (telephone != null) {
    subsidyCropProductionEntity.telephone = telephone;
  }
  final String? bankNum = jsonConvert.convert<String>(json['bankNum']);
  if (bankNum != null) {
    subsidyCropProductionEntity.bankNum = bankNum;
  }
  final String? partnerId = jsonConvert.convert<String>(json['partnerId']);
  if (partnerId != null) {
    subsidyCropProductionEntity.partnerId = partnerId;
  }
  final String? partnerCode = jsonConvert.convert<String>(json['partnerCode']);
  if (partnerCode != null) {
    subsidyCropProductionEntity.partnerCode = partnerCode;
  }
  final String? subsidyBatchNo = jsonConvert.convert<String>(
      json['subsidyBatchNo']);
  if (subsidyBatchNo != null) {
    subsidyCropProductionEntity.subsidyBatchNo = subsidyBatchNo;
  }
  final String? network = jsonConvert.convert<String>(json['network']);
  if (network != null) {
    subsidyCropProductionEntity.network = network;
  }
  final String? lineNumber = jsonConvert.convert<String>(json['lineNumber']);
  if (lineNumber != null) {
    subsidyCropProductionEntity.lineNumber = lineNumber;
  }
  final String? subsidyDetailIds = jsonConvert.convert<String>(
      json['subsidyDetailIds']);
  if (subsidyDetailIds != null) {
    subsidyCropProductionEntity.subsidyDetailIds = subsidyDetailIds;
  }
  final String? subsidyStandard3 = jsonConvert.convert<String>(
      json['subsidyStandard3']);
  if (subsidyStandard3 != null) {
    subsidyCropProductionEntity.subsidyStandard3 = subsidyStandard3;
  }
  final String? subsidyArea3 = jsonConvert.convert<String>(
      json['subsidyArea3']);
  if (subsidyArea3 != null) {
    subsidyCropProductionEntity.subsidyArea3 = subsidyArea3;
  }
  final String? subsidyFee3 = jsonConvert.convert<String>(json['subsidyFee3']);
  if (subsidyFee3 != null) {
    subsidyCropProductionEntity.subsidyFee3 = subsidyFee3;
  }
  final String? subsidyStandard4 = jsonConvert.convert<String>(
      json['subsidyStandard4']);
  if (subsidyStandard4 != null) {
    subsidyCropProductionEntity.subsidyStandard4 = subsidyStandard4;
  }
  final String? subsidyArea4 = jsonConvert.convert<String>(
      json['subsidyArea4']);
  if (subsidyArea4 != null) {
    subsidyCropProductionEntity.subsidyArea4 = subsidyArea4;
  }
  final String? subsidyFee4 = jsonConvert.convert<String>(json['subsidyFee4']);
  if (subsidyFee4 != null) {
    subsidyCropProductionEntity.subsidyFee4 = subsidyFee4;
  }
  final String? subsidyStandard5 = jsonConvert.convert<String>(
      json['subsidyStandard5']);
  if (subsidyStandard5 != null) {
    subsidyCropProductionEntity.subsidyStandard5 = subsidyStandard5;
  }
  final String? subsidyArea5 = jsonConvert.convert<String>(
      json['subsidyArea5']);
  if (subsidyArea5 != null) {
    subsidyCropProductionEntity.subsidyArea5 = subsidyArea5;
  }
  final String? subsidyFee5 = jsonConvert.convert<String>(json['subsidyFee5']);
  if (subsidyFee5 != null) {
    subsidyCropProductionEntity.subsidyFee5 = subsidyFee5;
  }
  final String? subsidyClassifyClass = jsonConvert.convert<String>(
      json['subsidyClassifyClass']);
  if (subsidyClassifyClass != null) {
    subsidyCropProductionEntity.subsidyClassifyClass = subsidyClassifyClass;
  }
  final String? subsidyPaymentId = jsonConvert.convert<String>(
      json['subsidyPaymentId']);
  if (subsidyPaymentId != null) {
    subsidyCropProductionEntity.subsidyPaymentId = subsidyPaymentId;
  }
  return subsidyCropProductionEntity;
}

Map<String, dynamic> $SubsidyCropProductionEntityToJson(
    SubsidyCropProductionEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyDetailId'] = entity.subsidyDetailId;
  data['subsidyPaymentNo'] = entity.subsidyPaymentNo;
  data['subsidyYear'] = entity.subsidyYear;
  data['subsidyItemName'] = entity.subsidyItemName;
  data['subsidyConfigId'] = entity.subsidyConfigId;
  data['subsidyType'] = entity.subsidyType;
  data['subsidyCropCode'] = entity.subsidyCropCode;
  data['organizationNo'] = entity.organizationNo;
  data['contractSerialNumber'] = entity.contractSerialNumber;
  data['farmerName'] = entity.farmerName;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['bankAccount'] = entity.bankAccount;
  data['bankName'] = entity.bankName;
  data['landNumber'] = entity.landNumber;
  data['actualPaymentFee'] = entity.actualPaymentFee;
  data['subsidyStandard1'] = entity.subsidyStandard1;
  data['subsidyArea1'] = entity.subsidyArea1;
  data['subsidyFee1'] = entity.subsidyFee1;
  data['subsidyStandard2'] = entity.subsidyStandard2;
  data['subsidyArea2'] = entity.subsidyArea2;
  data['subsidyFee2'] = entity.subsidyFee2;
  data['subsidyProjectCode'] = entity.subsidyProjectCode;
  data['organizationName'] = entity.organizationName;
  data['farmerId'] = entity.farmerId;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['auditDId'] = entity.auditDId;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEId'] = entity.auditEId;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFId'] = entity.auditFId;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGId'] = entity.auditGId;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHId'] = entity.auditHId;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['approvalRemark'] = entity.approvalRemark;
  data['auditLevel'] = entity.auditLevel;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['dataStatus'] = entity.dataStatus;
  data['params'] = entity.params;
  data['subsidyPaymentDetailSubs'] = entity.subsidyPaymentDetailSubs;
  data['paymentStatus'] = entity.paymentStatus;
  data['paymentRemark'] = entity.paymentRemark;
  data['children'] = entity.children;
  data['subsidyTypeName'] = entity.subsidyTypeName;
  data['ifUpdate'] = entity.ifUpdate;
  data['contractSignType'] = entity.contractSignType;
  data['telephone'] = entity.telephone;
  data['bankNum'] = entity.bankNum;
  data['partnerId'] = entity.partnerId;
  data['partnerCode'] = entity.partnerCode;
  data['subsidyBatchNo'] = entity.subsidyBatchNo;
  data['network'] = entity.network;
  data['lineNumber'] = entity.lineNumber;
  data['subsidyDetailIds'] = entity.subsidyDetailIds;
  data['subsidyStandard3'] = entity.subsidyStandard3;
  data['subsidyArea3'] = entity.subsidyArea3;
  data['subsidyFee3'] = entity.subsidyFee3;
  data['subsidyStandard4'] = entity.subsidyStandard4;
  data['subsidyArea4'] = entity.subsidyArea4;
  data['subsidyFee4'] = entity.subsidyFee4;
  data['subsidyStandard5'] = entity.subsidyStandard5;
  data['subsidyArea5'] = entity.subsidyArea5;
  data['subsidyFee5'] = entity.subsidyFee5;
  data['subsidyClassifyClass'] = entity.subsidyClassifyClass;
  data['subsidyPaymentId'] = entity.subsidyPaymentId;
  return data;
}

extension SubsidyCropProductionEntityExtension on SubsidyCropProductionEntity {
  SubsidyCropProductionEntity copyWith({
    String? subsidyDetailId,
    String? subsidyPaymentNo,
    String? subsidyYear,
    String? subsidyItemName,
    String? subsidyConfigId,
    String? subsidyType,
    String? subsidyCropCode,
    String? organizationNo,
    String? contractSerialNumber,
    String? farmerName,
    String? farmerIdNumber,
    String? bankAccount,
    String? bankName,
    String? landNumber,
    String? actualPaymentFee,
    String? subsidyStandard1,
    String? subsidyArea1,
    String? subsidyFee1,
    String? subsidyStandard2,
    String? subsidyArea2,
    String? subsidyFee2,
    String? subsidyProjectCode,
    String? organizationName,
    String? farmerId,
    String? approvalStatusNo,
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? auditDId,
    String? auditDFlag,
    String? auditDName,
    String? auditDTime,
    String? auditEId,
    String? auditEFlag,
    String? auditEName,
    String? auditETime,
    String? auditFId,
    String? auditFFlag,
    String? auditFName,
    String? auditFTime,
    String? auditGId,
    String? auditGFlag,
    String? auditGName,
    String? auditGTime,
    String? auditHId,
    String? auditHFlag,
    String? auditHName,
    String? auditHTime,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? approvalRemark,
    String? auditLevel,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? dataStatus,
    String? params,
    String? subsidyPaymentDetailSubs,
    String? paymentStatus,
    String? paymentRemark,
    String? children,
    String? subsidyTypeName,
    String? ifUpdate,
    String? contractSignType,
    String? telephone,
    String? bankNum,
    String? partnerId,
    String? partnerCode,
    String? subsidyBatchNo,
    String? network,
    String? lineNumber,
    String? subsidyDetailIds,
    String? subsidyStandard3,
    String? subsidyArea3,
    String? subsidyFee3,
    String? subsidyStandard4,
    String? subsidyArea4,
    String? subsidyFee4,
    String? subsidyStandard5,
    String? subsidyArea5,
    String? subsidyFee5,
    String? subsidyClassifyClass,
    String? subsidyPaymentId,
  }) {
    return SubsidyCropProductionEntity()
      ..subsidyDetailId = subsidyDetailId ?? this.subsidyDetailId
      ..subsidyPaymentNo = subsidyPaymentNo ?? this.subsidyPaymentNo
      ..subsidyYear = subsidyYear ?? this.subsidyYear
      ..subsidyItemName = subsidyItemName ?? this.subsidyItemName
      ..subsidyConfigId = subsidyConfigId ?? this.subsidyConfigId
      ..subsidyType = subsidyType ?? this.subsidyType
      ..subsidyCropCode = subsidyCropCode ?? this.subsidyCropCode
      ..organizationNo = organizationNo ?? this.organizationNo
      ..contractSerialNumber = contractSerialNumber ?? this.contractSerialNumber
      ..farmerName = farmerName ?? this.farmerName
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..bankAccount = bankAccount ?? this.bankAccount
      ..bankName = bankName ?? this.bankName
      ..landNumber = landNumber ?? this.landNumber
      ..actualPaymentFee = actualPaymentFee ?? this.actualPaymentFee
      ..subsidyStandard1 = subsidyStandard1 ?? this.subsidyStandard1
      ..subsidyArea1 = subsidyArea1 ?? this.subsidyArea1
      ..subsidyFee1 = subsidyFee1 ?? this.subsidyFee1
      ..subsidyStandard2 = subsidyStandard2 ?? this.subsidyStandard2
      ..subsidyArea2 = subsidyArea2 ?? this.subsidyArea2
      ..subsidyFee2 = subsidyFee2 ?? this.subsidyFee2
      ..subsidyProjectCode = subsidyProjectCode ?? this.subsidyProjectCode
      ..organizationName = organizationName ?? this.organizationName
      ..farmerId = farmerId ?? this.farmerId
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..auditDId = auditDId ?? this.auditDId
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEId = auditEId ?? this.auditEId
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFId = auditFId ?? this.auditFId
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGId = auditGId ?? this.auditGId
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHId = auditHId ?? this.auditHId
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..auditLevel = auditLevel ?? this.auditLevel
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..dataStatus = dataStatus ?? this.dataStatus
      ..params = params ?? this.params
      ..subsidyPaymentDetailSubs = subsidyPaymentDetailSubs ??
          this.subsidyPaymentDetailSubs
      ..paymentStatus = paymentStatus ?? this.paymentStatus
      ..paymentRemark = paymentRemark ?? this.paymentRemark
      ..children = children ?? this.children
      ..subsidyTypeName = subsidyTypeName ?? this.subsidyTypeName
      ..ifUpdate = ifUpdate ?? this.ifUpdate
      ..contractSignType = contractSignType ?? this.contractSignType
      ..telephone = telephone ?? this.telephone
      ..bankNum = bankNum ?? this.bankNum
      ..partnerId = partnerId ?? this.partnerId
      ..partnerCode = partnerCode ?? this.partnerCode
      ..subsidyBatchNo = subsidyBatchNo ?? this.subsidyBatchNo
      ..network = network ?? this.network
      ..lineNumber = lineNumber ?? this.lineNumber
      ..subsidyDetailIds = subsidyDetailIds ?? this.subsidyDetailIds
      ..subsidyStandard3 = subsidyStandard3 ?? this.subsidyStandard3
      ..subsidyArea3 = subsidyArea3 ?? this.subsidyArea3
      ..subsidyFee3 = subsidyFee3 ?? this.subsidyFee3
      ..subsidyStandard4 = subsidyStandard4 ?? this.subsidyStandard4
      ..subsidyArea4 = subsidyArea4 ?? this.subsidyArea4
      ..subsidyFee4 = subsidyFee4 ?? this.subsidyFee4
      ..subsidyStandard5 = subsidyStandard5 ?? this.subsidyStandard5
      ..subsidyArea5 = subsidyArea5 ?? this.subsidyArea5
      ..subsidyFee5 = subsidyFee5 ?? this.subsidyFee5
      ..subsidyClassifyClass = subsidyClassifyClass ?? this.subsidyClassifyClass
      ..subsidyPaymentId = subsidyPaymentId ?? this.subsidyPaymentId;
  }
}