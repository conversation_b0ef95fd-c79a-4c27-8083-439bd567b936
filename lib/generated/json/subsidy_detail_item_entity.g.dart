import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_detail_item_entity.dart';

SubsidyDetailItemEntity $SubsidyDetailItemEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyDetailItemEntity subsidyDetailItemEntity = SubsidyDetailItemEntity();
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    subsidyDetailItemEntity.total = total;
  }
  final List<SubsidyDetailItemDetails>? details = (json['details'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<SubsidyDetailItemDetails>(
          e) as SubsidyDetailItemDetails).toList();
  if (details != null) {
    subsidyDetailItemEntity.details = details;
  }
  final String? paymentTime = jsonConvert.convert<String>(json['paymentTime']);
  if (paymentTime != null) {
    subsidyDetailItemEntity.paymentTime = paymentTime;
  }
  return subsidyDetailItemEntity;
}

Map<String, dynamic> $SubsidyDetailItemEntityToJson(
    SubsidyDetailItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['total'] = entity.total;
  data['details'] = entity.details?.map((v) => v.toJson()).toList();
  data['paymentTime'] = entity.paymentTime;
  return data;
}

extension SubsidyDetailItemEntityExtension on SubsidyDetailItemEntity {
  SubsidyDetailItemEntity copyWith({
    int? total,
    List<SubsidyDetailItemDetails>? details,
    String? paymentTime,
  }) {
    return SubsidyDetailItemEntity()
      ..total = total ?? this.total
      ..details = details ?? this.details
      ..paymentTime = paymentTime ?? this.paymentTime;
  }
}

SubsidyDetailItemDetails $SubsidyDetailItemDetailsFromJson(
    Map<String, dynamic> json) {
  final SubsidyDetailItemDetails subsidyDetailItemDetails = SubsidyDetailItemDetails();
  final int? subsidyType = jsonConvert.convert<int>(json['subsidyType']);
  if (subsidyType != null) {
    subsidyDetailItemDetails.subsidyType = subsidyType;
  }
  final int? actualPaymentFee = jsonConvert.convert<int>(
      json['actualPaymentFee']);
  if (actualPaymentFee != null) {
    subsidyDetailItemDetails.actualPaymentFee = actualPaymentFee;
  }
  final String? paymentTime = jsonConvert.convert<String>(json['paymentTime']);
  if (paymentTime != null) {
    subsidyDetailItemDetails.paymentTime = paymentTime;
  }
  final int? subsidyStandard1 = jsonConvert.convert<int>(
      json['subsidyStandard1']);
  if (subsidyStandard1 != null) {
    subsidyDetailItemDetails.subsidyStandard1 = subsidyStandard1;
  }
  final int? subsidyArea1 = jsonConvert.convert<int>(json['subsidyArea1']);
  if (subsidyArea1 != null) {
    subsidyDetailItemDetails.subsidyArea1 = subsidyArea1;
  }
  final int? subsidyFee1 = jsonConvert.convert<int>(json['subsidyFee1']);
  if (subsidyFee1 != null) {
    subsidyDetailItemDetails.subsidyFee1 = subsidyFee1;
  }
  final int? subsidyStandard2 = jsonConvert.convert<int>(
      json['subsidyStandard2']);
  if (subsidyStandard2 != null) {
    subsidyDetailItemDetails.subsidyStandard2 = subsidyStandard2;
  }
  final int? subsidyArea2 = jsonConvert.convert<int>(json['subsidyArea2']);
  if (subsidyArea2 != null) {
    subsidyDetailItemDetails.subsidyArea2 = subsidyArea2;
  }
  final int? subsidyFee2 = jsonConvert.convert<int>(json['subsidyFee2']);
  if (subsidyFee2 != null) {
    subsidyDetailItemDetails.subsidyFee2 = subsidyFee2;
  }
  return subsidyDetailItemDetails;
}

Map<String, dynamic> $SubsidyDetailItemDetailsToJson(
    SubsidyDetailItemDetails entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyType'] = entity.subsidyType;
  data['actualPaymentFee'] = entity.actualPaymentFee;
  data['paymentTime'] = entity.paymentTime;
  data['subsidyStandard1'] = entity.subsidyStandard1;
  data['subsidyArea1'] = entity.subsidyArea1;
  data['subsidyFee1'] = entity.subsidyFee1;
  data['subsidyStandard2'] = entity.subsidyStandard2;
  data['subsidyArea2'] = entity.subsidyArea2;
  data['subsidyFee2'] = entity.subsidyFee2;
  return data;
}

extension SubsidyDetailItemDetailsExtension on SubsidyDetailItemDetails {
  SubsidyDetailItemDetails copyWith({
    int? subsidyType,
    int? actualPaymentFee,
    String? paymentTime,
    int? subsidyStandard1,
    int? subsidyArea1,
    int? subsidyFee1,
    int? subsidyStandard2,
    int? subsidyArea2,
    int? subsidyFee2,
  }) {
    return SubsidyDetailItemDetails()
      ..subsidyType = subsidyType ?? this.subsidyType
      ..actualPaymentFee = actualPaymentFee ?? this.actualPaymentFee
      ..paymentTime = paymentTime ?? this.paymentTime
      ..subsidyStandard1 = subsidyStandard1 ?? this.subsidyStandard1
      ..subsidyArea1 = subsidyArea1 ?? this.subsidyArea1
      ..subsidyFee1 = subsidyFee1 ?? this.subsidyFee1
      ..subsidyStandard2 = subsidyStandard2 ?? this.subsidyStandard2
      ..subsidyArea2 = subsidyArea2 ?? this.subsidyArea2
      ..subsidyFee2 = subsidyFee2 ?? this.subsidyFee2;
  }
}