import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_apply_info_entity.dart';

SubsidyApplyInfoEntity $SubsidyApplyInfoEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyApplyInfoEntity subsidyApplyInfoEntity =
      SubsidyApplyInfoEntity();
  final String? lcSubsidyApplyId =
      jsonConvert.convert<String>(json['lcSubsidyApplyId']);
  if (lcSubsidyApplyId != null) {
    subsidyApplyInfoEntity.lcSubsidyApplyId = lcSubsidyApplyId;
  }
  final String? subsidyYear = jsonConvert.convert<String>(json['subsidyYear']);
  if (subsidyYear != null) {
    subsidyApplyInfoEntity.subsidyYear = subsidyYear;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    subsidyApplyInfoEntity.organizationName = organizationName;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    subsidyApplyInfoEntity.organizationNo = organizationNo;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    subsidyApplyInfoEntity.farmerId = farmerId;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    subsidyApplyInfoEntity.farmerName = farmerName;
  }
  final String? farmerIdNumber =
      jsonConvert.convert<String>(json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    subsidyApplyInfoEntity.farmerIdNumber = farmerIdNumber;
  }
  final String? subsidyArea1 =
      jsonConvert.convert<String>(json['subsidyArea1']);
  if (subsidyArea1 != null) {
    subsidyApplyInfoEntity.subsidyArea1 = subsidyArea1;
  }
  final String? subsidyArea2 =
      jsonConvert.convert<String>(json['subsidyArea2']);
  if (subsidyArea2 != null) {
    subsidyApplyInfoEntity.subsidyArea2 = subsidyArea2;
  }
  final String? subsidyConfigId =
      jsonConvert.convert<String>(json['subsidyConfigId']);
  if (subsidyConfigId != null) {
    subsidyApplyInfoEntity.subsidyConfigId = subsidyConfigId;
  }
  final String? network = jsonConvert.convert<String>(json['network']);
  if (network != null) {
    subsidyApplyInfoEntity.network = network;
  }
  final String? lineNumber = jsonConvert.convert<String>(json['lineNumber']);
  if (lineNumber != null) {
    subsidyApplyInfoEntity.lineNumber = lineNumber;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    subsidyApplyInfoEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    subsidyApplyInfoEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    subsidyApplyInfoEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    subsidyApplyInfoEntity.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    subsidyApplyInfoEntity.statusCd = statusCd;
  }
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    subsidyApplyInfoEntity.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    subsidyApplyInfoEntity.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    subsidyApplyInfoEntity.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    subsidyApplyInfoEntity.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    subsidyApplyInfoEntity.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    subsidyApplyInfoEntity.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    subsidyApplyInfoEntity.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    subsidyApplyInfoEntity.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    subsidyApplyInfoEntity.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    subsidyApplyInfoEntity.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    subsidyApplyInfoEntity.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    subsidyApplyInfoEntity.auditCTime = auditCTime;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    subsidyApplyInfoEntity.auditDId = auditDId;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    subsidyApplyInfoEntity.auditDFlag = auditDFlag;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    subsidyApplyInfoEntity.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    subsidyApplyInfoEntity.auditDTime = auditDTime;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    subsidyApplyInfoEntity.auditEId = auditEId;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    subsidyApplyInfoEntity.auditEFlag = auditEFlag;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    subsidyApplyInfoEntity.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    subsidyApplyInfoEntity.auditETime = auditETime;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    subsidyApplyInfoEntity.auditFId = auditFId;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    subsidyApplyInfoEntity.auditFFlag = auditFFlag;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    subsidyApplyInfoEntity.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    subsidyApplyInfoEntity.auditFTime = auditFTime;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    subsidyApplyInfoEntity.auditGId = auditGId;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    subsidyApplyInfoEntity.auditGFlag = auditGFlag;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    subsidyApplyInfoEntity.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    subsidyApplyInfoEntity.auditGTime = auditGTime;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    subsidyApplyInfoEntity.auditHId = auditHId;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    subsidyApplyInfoEntity.auditHFlag = auditHFlag;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    subsidyApplyInfoEntity.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    subsidyApplyInfoEntity.auditHTime = auditHTime;
  }
  final String? currentAuditRoleId =
      jsonConvert.convert<String>(json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    subsidyApplyInfoEntity.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName =
      jsonConvert.convert<String>(json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    subsidyApplyInfoEntity.currentAuditRoleName = currentAuditRoleName;
  }
  final String? approvalRemark =
      jsonConvert.convert<String>(json['approvalRemark']);
  if (approvalRemark != null) {
    subsidyApplyInfoEntity.approvalRemark = approvalRemark;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    subsidyApplyInfoEntity.auditLevel = auditLevel;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    subsidyApplyInfoEntity.params = params;
  }
  final String? subsidyType = jsonConvert.convert<String>(json['subsidyType']);
  if (subsidyType != null) {
    subsidyApplyInfoEntity.subsidyType = subsidyType;
  }
  final String? subsidyClassify =
      jsonConvert.convert<String>(json['subsidyClassify']);
  if (subsidyClassify != null) {
    subsidyApplyInfoEntity.subsidyClassify = subsidyClassify;
  }
  final double? totalArea = jsonConvert.convert<double>(json['totalArea']);
  if (totalArea != null) {
    subsidyApplyInfoEntity.totalArea = totalArea;
  }
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  if (bankAccount != null) {
    subsidyApplyInfoEntity.bankAccount = bankAccount;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    subsidyApplyInfoEntity.bankName = bankName;
  }
  final double? contractSignType =
      jsonConvert.convert<double>(json['contractSignType']);
  if (contractSignType != null) {
    subsidyApplyInfoEntity.contractSignType = contractSignType;
  }
  final String? approvalStatusNo =
      jsonConvert.convert<String>(json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    subsidyApplyInfoEntity.approvalStatusNo = approvalStatusNo;
  }
  final String? partnerId = jsonConvert.convert<String>(json['partnerId']);
  if (partnerId != null) {
    subsidyApplyInfoEntity.partnerId = partnerId;
  }
  final String? subsidyApplyDetailSubs =
      jsonConvert.convert<String>(json['subsidyApplyDetailSubs']);
  if (subsidyApplyDetailSubs != null) {
    subsidyApplyInfoEntity.subsidyApplyDetailSubs = subsidyApplyDetailSubs;
  }
  final String? subsidyItemName =
      jsonConvert.convert<String>(json['subsidyItemName']);
  if (subsidyItemName != null) {
    subsidyApplyInfoEntity.subsidyItemName = subsidyItemName;
  }
  final String? subsidyTypeName =
      jsonConvert.convert<String>(json['subsidyTypeName']);
  if (subsidyTypeName != null) {
    subsidyApplyInfoEntity.subsidyTypeName = subsidyTypeName;
  }
  final String? landNumber = jsonConvert.convert<String>(json['landNumber']);
  if (landNumber != null) {
    subsidyApplyInfoEntity.landNumber = landNumber;
  }
  final String? subsidyCropCode =
      jsonConvert.convert<String>(json['subsidyCropCode']);
  if (subsidyCropCode != null) {
    subsidyApplyInfoEntity.subsidyCropCode = subsidyCropCode;
  }
  final double? contractGenerateStatusNo =
      jsonConvert.convert<double>(json['contractGenerateStatusNo']);
  if (contractGenerateStatusNo != null) {
    subsidyApplyInfoEntity.contractGenerateStatusNo = contractGenerateStatusNo;
  }
  final String? contractGenerateStartTime =
      jsonConvert.convert<String>(json['contractGenerateStartTime']);
  if (contractGenerateStartTime != null) {
    subsidyApplyInfoEntity.contractGenerateStartTime =
        contractGenerateStartTime;
  }
  final String? contractGenerateCompleteTime =
      jsonConvert.convert<String>(json['contractGenerateCompleteTime']);
  if (contractGenerateCompleteTime != null) {
    subsidyApplyInfoEntity.contractGenerateCompleteTime =
        contractGenerateCompleteTime;
  }
  final String? contractGenerateMsg =
      jsonConvert.convert<String>(json['contractGenerateMsg']);
  if (contractGenerateMsg != null) {
    subsidyApplyInfoEntity.contractGenerateMsg = contractGenerateMsg;
  }
  final String? farmerSignNo =
      jsonConvert.convert<String>(json['farmerSignNo']);
  if (farmerSignNo != null) {
    subsidyApplyInfoEntity.farmerSignNo = farmerSignNo;
  }
  final String? farmerSignDate =
      jsonConvert.convert<String>(json['farmerSignDate']);
  if (farmerSignDate != null) {
    subsidyApplyInfoEntity.farmerSignDate = farmerSignDate;
  }
  final String? fddContractNo =
      jsonConvert.convert<String>(json['fddContractNo']);
  if (fddContractNo != null) {
    subsidyApplyInfoEntity.fddContractNo = fddContractNo;
  }
  final double? retryTimes = jsonConvert.convert<double>(json['retryTimes']);
  if (retryTimes != null) {
    subsidyApplyInfoEntity.retryTimes = retryTimes;
  }
  final String? serialNumber =
      jsonConvert.convert<String>(json['serialNumber']);
  if (serialNumber != null) {
    subsidyApplyInfoEntity.serialNumber = serialNumber;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    subsidyApplyInfoEntity.isSelected = isSelected;
  }
  return subsidyApplyInfoEntity;
}

Map<String, dynamic> $SubsidyApplyInfoEntityToJson(
    SubsidyApplyInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['lcSubsidyApplyId'] = entity.lcSubsidyApplyId;
  data['subsidyYear'] = entity.subsidyYear;
  data['organizationName'] = entity.organizationName;
  data['organizationNo'] = entity.organizationNo;
  data['farmerId'] = entity.farmerId;
  data['farmerName'] = entity.farmerName;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['subsidyArea1'] = entity.subsidyArea1;
  data['subsidyArea2'] = entity.subsidyArea2;
  data['subsidyConfigId'] = entity.subsidyConfigId;
  data['network'] = entity.network;
  data['lineNumber'] = entity.lineNumber;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['auditDId'] = entity.auditDId;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEId'] = entity.auditEId;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFId'] = entity.auditFId;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGId'] = entity.auditGId;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHId'] = entity.auditHId;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['approvalRemark'] = entity.approvalRemark;
  data['auditLevel'] = entity.auditLevel;
  data['params'] = entity.params;
  data['subsidyType'] = entity.subsidyType;
  data['subsidyClassify'] = entity.subsidyClassify;
  data['totalArea'] = entity.totalArea;
  data['bankAccount'] = entity.bankAccount;
  data['bankName'] = entity.bankName;
  data['contractSignType'] = entity.contractSignType;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['partnerId'] = entity.partnerId;
  data['subsidyApplyDetailSubs'] = entity.subsidyApplyDetailSubs;
  data['subsidyItemName'] = entity.subsidyItemName;
  data['subsidyTypeName'] = entity.subsidyTypeName;
  data['landNumber'] = entity.landNumber;
  data['subsidyCropCode'] = entity.subsidyCropCode;
  data['contractGenerateStatusNo'] = entity.contractGenerateStatusNo;
  data['contractGenerateStartTime'] = entity.contractGenerateStartTime;
  data['contractGenerateCompleteTime'] = entity.contractGenerateCompleteTime;
  data['contractGenerateMsg'] = entity.contractGenerateMsg;
  data['farmerSignNo'] = entity.farmerSignNo;
  data['farmerSignDate'] = entity.farmerSignDate;
  data['fddContractNo'] = entity.fddContractNo;
  data['retryTimes'] = entity.retryTimes;
  data['serialNumber'] = entity.serialNumber;
  data['isSelected'] = entity.isSelected;
  return data;
}

extension SubsidyApplyInfoEntityExtension on SubsidyApplyInfoEntity {
  SubsidyApplyInfoEntity copyWith({
    String? lcSubsidyApplyId,
    String? subsidyYear,
    String? organizationName,
    String? organizationNo,
    String? farmerId,
    String? farmerName,
    String? farmerIdNumber,
    String? subsidyArea1,
    String? subsidyArea2,
    String? subsidyConfigId,
    String? network,
    String? lineNumber,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? auditDId,
    String? auditDFlag,
    String? auditDName,
    String? auditDTime,
    String? auditEId,
    String? auditEFlag,
    String? auditEName,
    String? auditETime,
    String? auditFId,
    String? auditFFlag,
    String? auditFName,
    String? auditFTime,
    String? auditGId,
    String? auditGFlag,
    String? auditGName,
    String? auditGTime,
    String? auditHId,
    String? auditHFlag,
    String? auditHName,
    String? auditHTime,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? approvalRemark,
    String? auditLevel,
    String? params,
    String? subsidyType,
    String? subsidyClassify,
    double? totalArea,
    String? bankAccount,
    String? bankName,
    double? contractSignType,
    String? approvalStatusNo,
    String? partnerId,
    String? subsidyApplyDetailSubs,
    String? subsidyItemName,
    String? subsidyTypeName,
    String? landNumber,
    String? subsidyCropCode,
    double? contractGenerateStatusNo,
    String? contractGenerateStartTime,
    String? contractGenerateCompleteTime,
    String? contractGenerateMsg,
    String? farmerSignNo,
    String? farmerSignDate,
    String? fddContractNo,
    double? retryTimes,
    String? serialNumber,
    bool? isSelected,
  }) {
    return SubsidyApplyInfoEntity()
      ..lcSubsidyApplyId = lcSubsidyApplyId ?? this.lcSubsidyApplyId
      ..subsidyYear = subsidyYear ?? this.subsidyYear
      ..organizationName = organizationName ?? this.organizationName
      ..organizationNo = organizationNo ?? this.organizationNo
      ..farmerId = farmerId ?? this.farmerId
      ..farmerName = farmerName ?? this.farmerName
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..subsidyArea1 = subsidyArea1 ?? this.subsidyArea1
      ..subsidyArea2 = subsidyArea2 ?? this.subsidyArea2
      ..subsidyConfigId = subsidyConfigId ?? this.subsidyConfigId
      ..network = network ?? this.network
      ..lineNumber = lineNumber ?? this.lineNumber
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..auditDId = auditDId ?? this.auditDId
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEId = auditEId ?? this.auditEId
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFId = auditFId ?? this.auditFId
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGId = auditGId ?? this.auditGId
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHId = auditHId ?? this.auditHId
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..auditLevel = auditLevel ?? this.auditLevel
      ..params = params ?? this.params
      ..subsidyType = subsidyType ?? this.subsidyType
      ..subsidyClassify = subsidyClassify ?? this.subsidyClassify
      ..totalArea = totalArea ?? this.totalArea
      ..bankAccount = bankAccount ?? this.bankAccount
      ..bankName = bankName ?? this.bankName
      ..contractSignType = contractSignType ?? this.contractSignType
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..partnerId = partnerId ?? this.partnerId
      ..subsidyApplyDetailSubs =
          subsidyApplyDetailSubs ?? this.subsidyApplyDetailSubs
      ..subsidyItemName = subsidyItemName ?? this.subsidyItemName
      ..subsidyTypeName = subsidyTypeName ?? this.subsidyTypeName
      ..landNumber = landNumber ?? this.landNumber
      ..subsidyCropCode = subsidyCropCode ?? this.subsidyCropCode
      ..contractGenerateStatusNo =
          contractGenerateStatusNo ?? this.contractGenerateStatusNo
      ..contractGenerateStartTime =
          contractGenerateStartTime ?? this.contractGenerateStartTime
      ..contractGenerateCompleteTime =
          contractGenerateCompleteTime ?? this.contractGenerateCompleteTime
      ..contractGenerateMsg = contractGenerateMsg ?? this.contractGenerateMsg
      ..farmerSignNo = farmerSignNo ?? this.farmerSignNo
      ..farmerSignDate = farmerSignDate ?? this.farmerSignDate
      ..fddContractNo = fddContractNo ?? this.fddContractNo
      ..retryTimes = retryTimes ?? this.retryTimes
      ..serialNumber = serialNumber ?? this.serialNumber
      ..isSelected = isSelected ?? this.isSelected;
  }
}
