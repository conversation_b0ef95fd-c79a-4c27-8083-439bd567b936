import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/free_sow_entity.dart';

FreeSowEntity $FreeSowEntityFromJson(Map<String, dynamic> json) {
  final FreeSowEntity freeSowEntity = FreeSowEntity();
  final double? totalSum = jsonConvert.convert<double>(json['totalSum']);
  if (totalSum != null) {
    freeSowEntity.totalSum = totalSum;
  }
  final List<FreeSowResultList>? resultList = (json['resultList']
          as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<FreeSowResultList>(e) as FreeSowResultList)
      .toList();
  if (resultList != null) {
    freeSowEntity.resultList = resultList;
  }
  return freeSowEntity;
}

Map<String, dynamic> $FreeSowEntityToJson(FreeSowEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['totalSum'] = entity.totalSum;
  data['resultList'] = entity.resultList?.map((v) => v.toJson()).toList();
  return data;
}

extension FreeSowEntityExtension on FreeSowEntity {
  FreeSowEntity copyWith({
    double? totalSum,
    List<FreeSowResultList>? resultList,
  }) {
    return FreeSowEntity()
      ..totalSum = totalSum ?? this.totalSum
      ..resultList = resultList ?? this.resultList;
  }
}

FreeSowResultList $FreeSowResultListFromJson(Map<String, dynamic> json) {
  final FreeSowResultList freeSowResultList = FreeSowResultList();
  final int? year = jsonConvert.convert<int>(json['year']);
  if (year != null) {
    freeSowResultList.year = year;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    freeSowResultList.name = name;
  }
  final dynamic idCard = json['idCard'];
  if (idCard != null) {
    freeSowResultList.idCard = idCard;
  }
  final String? orgName = jsonConvert.convert<String>(json['orgName']);
  if (orgName != null) {
    freeSowResultList.orgName = orgName;
  }
  final dynamic amount = json['amount'];
  if (amount != null) {
    freeSowResultList.amount = amount;
  }
  final dynamic endAddress = json['endAddress'];
  if (endAddress != null) {
    freeSowResultList.endAddress = endAddress;
  }
  final String? date = jsonConvert.convert<String>(json['date']);
  if (date != null) {
    freeSowResultList.date = date;
  }
  final String? amTypeName1 = jsonConvert.convert<String>(json['amTypeName1']);
  if (amTypeName1 != null) {
    freeSowResultList.amTypeName1 = amTypeName1;
  }
  final String? amTypeName2 = jsonConvert.convert<String>(json['amTypeName2']);
  if (amTypeName2 != null) {
    freeSowResultList.amTypeName2 = amTypeName2;
  }
  final String? amTypeName3 = jsonConvert.convert<String>(json['amTypeName3']);
  if (amTypeName3 != null) {
    freeSowResultList.amTypeName3 = amTypeName3;
  }
  final String? amModelName = jsonConvert.convert<String>(json['amModelName']);
  if (amModelName != null) {
    freeSowResultList.amModelName = amModelName;
  }
  final String? companyName = jsonConvert.convert<String>(json['companyName']);
  if (companyName != null) {
    freeSowResultList.companyName = companyName;
  }
  final String? machineNo = jsonConvert.convert<String>(json['machineNo']);
  if (machineNo != null) {
    freeSowResultList.machineNo = machineNo;
  }
  final String? dsn = jsonConvert.convert<String>(json['dsn']);
  if (dsn != null) {
    freeSowResultList.dsn = dsn;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    freeSowResultList.type = type;
  }
  final double? wid = jsonConvert.convert<double>(json['wid']);
  if (wid != null) {
    freeSowResultList.wid = wid;
  }
  final int? wtype = jsonConvert.convert<int>(json['wtype']);
  if (wtype != null) {
    freeSowResultList.wtype = wtype;
  }
  final String? crop = jsonConvert.convert<String>(json['crop']);
  if (crop != null) {
    freeSowResultList.crop = crop;
  }
  final String? start = jsonConvert.convert<String>(json['start']);
  if (start != null) {
    freeSowResultList.start = start;
  }
  final String? end = jsonConvert.convert<String>(json['end']);
  if (end != null) {
    freeSowResultList.end = end;
  }
  final double? area = jsonConvert.convert<double>(json['area']);
  if (area != null) {
    freeSowResultList.area = area;
  }
  final double? qarea = jsonConvert.convert<double>(json['qarea']);
  if (qarea != null) {
    freeSowResultList.qarea = qarea;
  }
  final String? subsidiesLevel =
      jsonConvert.convert<String>(json['subsidiesLevel']);
  if (subsidiesLevel != null) {
    freeSowResultList.subsidiesLevel = subsidiesLevel;
  }
  final dynamic overlap = json['overlap'];
  if (overlap != null) {
    freeSowResultList.overlap = overlap;
  }
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    freeSowResultList.uuid = uuid;
  }
  final String? hasLoc = jsonConvert.convert<String>(json['hasLoc']);
  if (hasLoc != null) {
    freeSowResultList.hasLoc = hasLoc;
  }
  final dynamic sex = json['sex'];
  if (sex != null) {
    freeSowResultList.sex = sex;
  }
  final int? workId = jsonConvert.convert<int>(json['workId']);
  if (workId != null) {
    freeSowResultList.workId = workId;
  }
  return freeSowResultList;
}

Map<String, dynamic> $FreeSowResultListToJson(FreeSowResultList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['year'] = entity.year;
  data['name'] = entity.name;
  data['idCard'] = entity.idCard;
  data['orgName'] = entity.orgName;
  data['amount'] = entity.amount;
  data['endAddress'] = entity.endAddress;
  data['date'] = entity.date;
  data['amTypeName1'] = entity.amTypeName1;
  data['amTypeName2'] = entity.amTypeName2;
  data['amTypeName3'] = entity.amTypeName3;
  data['amModelName'] = entity.amModelName;
  data['companyName'] = entity.companyName;
  data['machineNo'] = entity.machineNo;
  data['dsn'] = entity.dsn;
  data['type'] = entity.type;
  data['wid'] = entity.wid;
  data['wtype'] = entity.wtype;
  data['crop'] = entity.crop;
  data['start'] = entity.start;
  data['end'] = entity.end;
  data['area'] = entity.area;
  data['qarea'] = entity.qarea;
  data['subsidiesLevel'] = entity.subsidiesLevel;
  data['overlap'] = entity.overlap;
  data['uuid'] = entity.uuid;
  data['hasLoc'] = entity.hasLoc;
  data['sex'] = entity.sex;
  data['workId'] = entity.workId;
  return data;
}

extension FreeSowResultListExtension on FreeSowResultList {
  FreeSowResultList copyWith({
    int? year,
    String? name,
    dynamic idCard,
    String? orgName,
    dynamic amount,
    dynamic endAddress,
    String? date,
    String? amTypeName1,
    String? amTypeName2,
    String? amTypeName3,
    String? amModelName,
    String? companyName,
    String? machineNo,
    String? dsn,
    String? type,
    double? wid,
    int? wtype,
    String? crop,
    String? start,
    String? end,
    double? area,
    double? qarea,
    String? subsidiesLevel,
    dynamic overlap,
    String? uuid,
    String? hasLoc,
    dynamic sex,
    int? workId,
  }) {
    return FreeSowResultList()
      ..year = year ?? this.year
      ..name = name ?? this.name
      ..idCard = idCard ?? this.idCard
      ..orgName = orgName ?? this.orgName
      ..amount = amount ?? this.amount
      ..endAddress = endAddress ?? this.endAddress
      ..date = date ?? this.date
      ..amTypeName1 = amTypeName1 ?? this.amTypeName1
      ..amTypeName2 = amTypeName2 ?? this.amTypeName2
      ..amTypeName3 = amTypeName3 ?? this.amTypeName3
      ..amModelName = amModelName ?? this.amModelName
      ..companyName = companyName ?? this.companyName
      ..machineNo = machineNo ?? this.machineNo
      ..dsn = dsn ?? this.dsn
      ..type = type ?? this.type
      ..wid = wid ?? this.wid
      ..wtype = wtype ?? this.wtype
      ..crop = crop ?? this.crop
      ..start = start ?? this.start
      ..end = end ?? this.end
      ..area = area ?? this.area
      ..qarea = qarea ?? this.qarea
      ..subsidiesLevel = subsidiesLevel ?? this.subsidiesLevel
      ..overlap = overlap ?? this.overlap
      ..uuid = uuid ?? this.uuid
      ..hasLoc = hasLoc ?? this.hasLoc
      ..sex = sex ?? this.sex
      ..workId = workId ?? this.workId;
  }
}
