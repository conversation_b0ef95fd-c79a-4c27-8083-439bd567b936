import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/new_detail_entity.dart';

NewDetailEntity $NewDetailEntityFromJson(Map<String, dynamic> json) {
  final NewDetailEntity newDetailEntity = NewDetailEntity();
  final int? releaseInfoId = jsonConvert.convert<int>(json['releaseInfoId']);
  if (releaseInfoId != null) {
    newDetailEntity.releaseInfoId = releaseInfoId;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    newDetailEntity.title = title;
  }
  final String? orgCode = jsonConvert.convert<String>(json['orgCode']);
  if (orgCode != null) {
    newDetailEntity.orgCode = orgCode;
  }
  final String? orgName = jsonConvert.convert<String>(json['orgName']);
  if (orgName != null) {
    newDetailEntity.orgName = orgName;
  }
  final String? dtlType = jsonConvert.convert<String>(json['dtlType']);
  if (dtlType != null) {
    newDetailEntity.dtlType = dtlType;
  }
  final String? thumbUrl = jsonConvert.convert<String>(json['thumbUrl']);
  if (thumbUrl != null) {
    newDetailEntity.thumbUrl = thumbUrl;
  }
  final String? releaseContent = jsonConvert.convert<String>(
      json['releaseContent']);
  if (releaseContent != null) {
    newDetailEntity.releaseContent = releaseContent;
  }
  final int? releaseTime = jsonConvert.convert<int>(json['releaseTime']);
  if (releaseTime != null) {
    newDetailEntity.releaseTime = releaseTime;
  }
  final List<NewDetailAnnexList>? annexList = (json['annexList'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<NewDetailAnnexList>(e) as NewDetailAnnexList)
      .toList();
  if (annexList != null) {
    newDetailEntity.annexList = annexList;
  }
  return newDetailEntity;
}

Map<String, dynamic> $NewDetailEntityToJson(NewDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['releaseInfoId'] = entity.releaseInfoId;
  data['title'] = entity.title;
  data['orgCode'] = entity.orgCode;
  data['orgName'] = entity.orgName;
  data['dtlType'] = entity.dtlType;
  data['thumbUrl'] = entity.thumbUrl;
  data['releaseContent'] = entity.releaseContent;
  data['releaseTime'] = entity.releaseTime;
  data['annexList'] = entity.annexList?.map((v) => v.toJson()).toList();
  return data;
}

extension NewDetailEntityExtension on NewDetailEntity {
  NewDetailEntity copyWith({
    int? releaseInfoId,
    String? title,
    String? orgCode,
    String? orgName,
    String? dtlType,
    String? thumbUrl,
    String? releaseContent,
    int? releaseTime,
    List<NewDetailAnnexList>? annexList,
  }) {
    return NewDetailEntity()
      ..releaseInfoId = releaseInfoId ?? this.releaseInfoId
      ..title = title ?? this.title
      ..orgCode = orgCode ?? this.orgCode
      ..orgName = orgName ?? this.orgName
      ..dtlType = dtlType ?? this.dtlType
      ..thumbUrl = thumbUrl ?? this.thumbUrl
      ..releaseContent = releaseContent ?? this.releaseContent
      ..releaseTime = releaseTime ?? this.releaseTime
      ..annexList = annexList ?? this.annexList;
  }
}

NewDetailAnnexList $NewDetailAnnexListFromJson(Map<String, dynamic> json) {
  final NewDetailAnnexList newDetailAnnexList = NewDetailAnnexList();
  final int? releaseInfoAnnexId = jsonConvert.convert<int>(
      json['releaseInfoAnnexId']);
  if (releaseInfoAnnexId != null) {
    newDetailAnnexList.releaseInfoAnnexId = releaseInfoAnnexId;
  }
  final int? releaseInfoDtlId = jsonConvert.convert<int>(
      json['releaseInfoDtlId']);
  if (releaseInfoDtlId != null) {
    newDetailAnnexList.releaseInfoDtlId = releaseInfoDtlId;
  }
  final String? annexName = jsonConvert.convert<String>(json['annexName']);
  if (annexName != null) {
    newDetailAnnexList.annexName = annexName;
  }
  final String? annexUrl = jsonConvert.convert<String>(json['annexUrl']);
  if (annexUrl != null) {
    newDetailAnnexList.annexUrl = annexUrl;
  }
  final dynamic remark = json['remark'];
  if (remark != null) {
    newDetailAnnexList.remark = remark;
  }
  final int? createBy = jsonConvert.convert<int>(json['createBy']);
  if (createBy != null) {
    newDetailAnnexList.createBy = createBy;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    newDetailAnnexList.createTime = createTime;
  }
  final dynamic updateBy = json['updateBy'];
  if (updateBy != null) {
    newDetailAnnexList.updateBy = updateBy;
  }
  final dynamic updateTime = json['updateTime'];
  if (updateTime != null) {
    newDetailAnnexList.updateTime = updateTime;
  }
  final dynamic statusCd = json['statusCd'];
  if (statusCd != null) {
    newDetailAnnexList.statusCd = statusCd;
  }
  final dynamic params = json['params'];
  if (params != null) {
    newDetailAnnexList.params = params;
  }
  final dynamic uid = json['uid'];
  if (uid != null) {
    newDetailAnnexList.uid = uid;
  }
  final dynamic status = json['status'];
  if (status != null) {
    newDetailAnnexList.status = status;
  }
  return newDetailAnnexList;
}

Map<String, dynamic> $NewDetailAnnexListToJson(NewDetailAnnexList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['releaseInfoAnnexId'] = entity.releaseInfoAnnexId;
  data['releaseInfoDtlId'] = entity.releaseInfoDtlId;
  data['annexName'] = entity.annexName;
  data['annexUrl'] = entity.annexUrl;
  data['remark'] = entity.remark;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['params'] = entity.params;
  data['uid'] = entity.uid;
  data['status'] = entity.status;
  return data;
}

extension NewDetailAnnexListExtension on NewDetailAnnexList {
  NewDetailAnnexList copyWith({
    int? releaseInfoAnnexId,
    int? releaseInfoDtlId,
    String? annexName,
    String? annexUrl,
    dynamic remark,
    int? createBy,
    int? createTime,
    dynamic updateBy,
    dynamic updateTime,
    dynamic statusCd,
    dynamic params,
    dynamic uid,
    dynamic status,
  }) {
    return NewDetailAnnexList()
      ..releaseInfoAnnexId = releaseInfoAnnexId ?? this.releaseInfoAnnexId
      ..releaseInfoDtlId = releaseInfoDtlId ?? this.releaseInfoDtlId
      ..annexName = annexName ?? this.annexName
      ..annexUrl = annexUrl ?? this.annexUrl
      ..remark = remark ?? this.remark
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..params = params ?? this.params
      ..uid = uid ?? this.uid
      ..status = status ?? this.status;
  }
}