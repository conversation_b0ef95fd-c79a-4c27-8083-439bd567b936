import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/question_dictionary_entity.dart';

QuestionDictionaryEntity $QuestionDictionaryEntityFromJson(
    Map<String, dynamic> json) {
  final QuestionDictionaryEntity questionDictionaryEntity = QuestionDictionaryEntity();
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    questionDictionaryEntity.code = code;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    questionDictionaryEntity.description = description;
  }
  final int? dictId = jsonConvert.convert<int>(json['dictId']);
  if (dictId != null) {
    questionDictionaryEntity.dictId = dictId;
  }
  final String? dictKey = jsonConvert.convert<String>(json['dictKey']);
  if (dictKey != null) {
    questionDictionaryEntity.dictKey = dictKey;
  }
  final String? image = jsonConvert.convert<String>(json['image']);
  if (image != null) {
    questionDictionaryEntity.image = image;
  }
  final int? level = jsonConvert.convert<int>(json['level']);
  if (level != null) {
    questionDictionaryEntity.level = level;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    questionDictionaryEntity.name = name;
  }
  final int? orderNum = jsonConvert.convert<int>(json['orderNum']);
  if (orderNum != null) {
    questionDictionaryEntity.orderNum = orderNum;
  }
  final String? parentCode = jsonConvert.convert<String>(json['parentCode']);
  if (parentCode != null) {
    questionDictionaryEntity.parentCode = parentCode;
  }
  final String? question = jsonConvert.convert<String>(json['question']);
  if (question != null) {
    questionDictionaryEntity.question = question;
  }
  final String? state = jsonConvert.convert<String>(json['state']);
  if (state != null) {
    questionDictionaryEntity.state = state;
  }
  return questionDictionaryEntity;
}

Map<String, dynamic> $QuestionDictionaryEntityToJson(
    QuestionDictionaryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['description'] = entity.description;
  data['dictId'] = entity.dictId;
  data['dictKey'] = entity.dictKey;
  data['image'] = entity.image;
  data['level'] = entity.level;
  data['name'] = entity.name;
  data['orderNum'] = entity.orderNum;
  data['parentCode'] = entity.parentCode;
  data['question'] = entity.question;
  data['state'] = entity.state;
  return data;
}

extension QuestionDictionaryEntityExtension on QuestionDictionaryEntity {
  QuestionDictionaryEntity copyWith({
    String? code,
    String? description,
    int? dictId,
    String? dictKey,
    String? image,
    int? level,
    String? name,
    int? orderNum,
    String? parentCode,
    String? question,
    String? state,
  }) {
    return QuestionDictionaryEntity()
      ..code = code ?? this.code
      ..description = description ?? this.description
      ..dictId = dictId ?? this.dictId
      ..dictKey = dictKey ?? this.dictKey
      ..image = image ?? this.image
      ..level = level ?? this.level
      ..name = name ?? this.name
      ..orderNum = orderNum ?? this.orderNum
      ..parentCode = parentCode ?? this.parentCode
      ..question = question ?? this.question
      ..state = state ?? this.state;
  }
}