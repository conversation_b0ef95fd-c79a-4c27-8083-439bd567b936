import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_protect_plough_entity.dart';

SubsidyProtectPloughEntity $SubsidyProtectPloughEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyProtectPloughEntity subsidyProtectPloughEntity =
      SubsidyProtectPloughEntity();
  final String? subsidyAgmachineWorkId =
      jsonConvert.convert<String>(json['subsidyAgmachineWorkId']);
  if (subsidyAgmachineWorkId != null) {
    subsidyProtectPloughEntity.subsidyAgmachineWorkId = subsidyAgmachineWorkId;
  }
  final String? subsidyConfigId =
      jsonConvert.convert<String>(json['subsidyConfigId']);
  if (subsidyConfigId != null) {
    subsidyProtectPloughEntity.subsidyConfigId = subsidyConfigId;
  }
  final String? subsidyYear = jsonConvert.convert<String>(json['subsidyYear']);
  if (subsidyYear != null) {
    subsidyProtectPloughEntity.subsidyYear = subsidyYear;
  }
  final String? subsidyType = jsonConvert.convert<String>(json['subsidyType']);
  if (subsidyType != null) {
    subsidyProtectPloughEntity.subsidyType = subsidyType;
  }
  final String? subsidyProjectCode =
      jsonConvert.convert<String>(json['subsidyProjectCode']);
  if (subsidyProjectCode != null) {
    subsidyProtectPloughEntity.subsidyProjectCode = subsidyProjectCode;
  }
  final String? subsidyBudgetId =
      jsonConvert.convert<String>(json['subsidyBudgetId']);
  if (subsidyBudgetId != null) {
    subsidyProtectPloughEntity.subsidyBudgetId = subsidyBudgetId;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    subsidyProtectPloughEntity.farmerName = farmerName;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    subsidyProtectPloughEntity.farmerId = farmerId;
  }
  final String? farmerIdNumber =
      jsonConvert.convert<String>(json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    subsidyProtectPloughEntity.farmerIdNumber = farmerIdNumber;
  }
  final String? amModelName = jsonConvert.convert<String>(json['amModelName']);
  if (amModelName != null) {
    subsidyProtectPloughEntity.amModelName = amModelName;
  }
  final String? factoryCode = jsonConvert.convert<String>(json['factoryCode']);
  if (factoryCode != null) {
    subsidyProtectPloughEntity.factoryCode = factoryCode;
  }
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  if (bankAccount != null) {
    subsidyProtectPloughEntity.bankAccount = bankAccount;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    subsidyProtectPloughEntity.bankName = bankName;
  }
  final String? network = jsonConvert.convert<String>(json['network']);
  if (network != null) {
    subsidyProtectPloughEntity.network = network;
  }
  final String? lineNumber = jsonConvert.convert<String>(json['lineNumber']);
  if (lineNumber != null) {
    subsidyProtectPloughEntity.lineNumber = lineNumber;
  }
  final String? subsidyCashArea =
      jsonConvert.convert<String>(json['subsidyCashArea']);
  if (subsidyCashArea != null) {
    subsidyProtectPloughEntity.subsidyCashArea = subsidyCashArea;
  }
  final String? actualPaymentFee =
      jsonConvert.convert<String>(json['actualPaymentFee']);
  if (actualPaymentFee != null) {
    subsidyProtectPloughEntity.actualPaymentFee = actualPaymentFee;
  }
  final String? subsidyArea1 =
      jsonConvert.convert<String>(json['subsidyArea1']);
  if (subsidyArea1 != null) {
    subsidyProtectPloughEntity.subsidyArea1 = subsidyArea1;
  }
  final String? subsidyStandard1 =
      jsonConvert.convert<String>(json['subsidyStandard1']);
  if (subsidyStandard1 != null) {
    subsidyProtectPloughEntity.subsidyStandard1 = subsidyStandard1;
  }
  final String? subsidyFee1 = jsonConvert.convert<String>(json['subsidyFee1']);
  if (subsidyFee1 != null) {
    subsidyProtectPloughEntity.subsidyFee1 = subsidyFee1;
  }
  final String? subsidyArea2 =
      jsonConvert.convert<String>(json['subsidyArea2']);
  if (subsidyArea2 != null) {
    subsidyProtectPloughEntity.subsidyArea2 = subsidyArea2;
  }
  final String? subsidyStandard2 =
      jsonConvert.convert<String>(json['subsidyStandard2']);
  if (subsidyStandard2 != null) {
    subsidyProtectPloughEntity.subsidyStandard2 = subsidyStandard2;
  }
  final String? subsidyFee2 = jsonConvert.convert<String>(json['subsidyFee2']);
  if (subsidyFee2 != null) {
    subsidyProtectPloughEntity.subsidyFee2 = subsidyFee2;
  }
  final String? subsidyArea3 =
      jsonConvert.convert<String>(json['subsidyArea3']);
  if (subsidyArea3 != null) {
    subsidyProtectPloughEntity.subsidyArea3 = subsidyArea3;
  }
  final String? subsidyStandard3 =
      jsonConvert.convert<String>(json['subsidyStandard3']);
  if (subsidyStandard3 != null) {
    subsidyProtectPloughEntity.subsidyStandard3 = subsidyStandard3;
  }
  final String? subsidyFee3 = jsonConvert.convert<String>(json['subsidyFee3']);
  if (subsidyFee3 != null) {
    subsidyProtectPloughEntity.subsidyFee3 = subsidyFee3;
  }
  final String? subsidyArea4 =
      jsonConvert.convert<String>(json['subsidyArea4']);
  if (subsidyArea4 != null) {
    subsidyProtectPloughEntity.subsidyArea4 = subsidyArea4;
  }
  final String? subsidyStandard4 =
      jsonConvert.convert<String>(json['subsidyStandard4']);
  if (subsidyStandard4 != null) {
    subsidyProtectPloughEntity.subsidyStandard4 = subsidyStandard4;
  }
  final String? subsidyFee4 = jsonConvert.convert<String>(json['subsidyFee4']);
  if (subsidyFee4 != null) {
    subsidyProtectPloughEntity.subsidyFee4 = subsidyFee4;
  }
  final String? subsidyArea5 =
      jsonConvert.convert<String>(json['subsidyArea5']);
  if (subsidyArea5 != null) {
    subsidyProtectPloughEntity.subsidyArea5 = subsidyArea5;
  }
  final String? subsidyStandard5 =
      jsonConvert.convert<String>(json['subsidyStandard5']);
  if (subsidyStandard5 != null) {
    subsidyProtectPloughEntity.subsidyStandard5 = subsidyStandard5;
  }
  final String? subsidyFee5 = jsonConvert.convert<String>(json['subsidyFee5']);
  if (subsidyFee5 != null) {
    subsidyProtectPloughEntity.subsidyFee5 = subsidyFee5;
  }
  final String? approvalStatusNo =
      jsonConvert.convert<String>(json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    subsidyProtectPloughEntity.approvalStatusNo = approvalStatusNo;
  }
  final String? contractSignType =
      jsonConvert.convert<String>(json['contractSignType']);
  if (contractSignType != null) {
    subsidyProtectPloughEntity.contractSignType = contractSignType;
  }
  final String? partnerId = jsonConvert.convert<String>(json['partnerId']);
  if (partnerId != null) {
    subsidyProtectPloughEntity.partnerId = partnerId;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    subsidyProtectPloughEntity.organizationNo = organizationNo;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    subsidyProtectPloughEntity.organizationName = organizationName;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    subsidyProtectPloughEntity.auditLevel = auditLevel;
  }
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    subsidyProtectPloughEntity.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    subsidyProtectPloughEntity.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    subsidyProtectPloughEntity.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    subsidyProtectPloughEntity.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    subsidyProtectPloughEntity.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    subsidyProtectPloughEntity.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    subsidyProtectPloughEntity.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    subsidyProtectPloughEntity.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    subsidyProtectPloughEntity.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    subsidyProtectPloughEntity.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    subsidyProtectPloughEntity.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    subsidyProtectPloughEntity.auditCTime = auditCTime;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    subsidyProtectPloughEntity.auditDId = auditDId;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    subsidyProtectPloughEntity.auditDFlag = auditDFlag;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    subsidyProtectPloughEntity.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    subsidyProtectPloughEntity.auditDTime = auditDTime;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    subsidyProtectPloughEntity.auditEId = auditEId;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    subsidyProtectPloughEntity.auditEFlag = auditEFlag;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    subsidyProtectPloughEntity.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    subsidyProtectPloughEntity.auditETime = auditETime;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    subsidyProtectPloughEntity.auditFId = auditFId;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    subsidyProtectPloughEntity.auditFFlag = auditFFlag;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    subsidyProtectPloughEntity.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    subsidyProtectPloughEntity.auditFTime = auditFTime;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    subsidyProtectPloughEntity.auditGId = auditGId;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    subsidyProtectPloughEntity.auditGFlag = auditGFlag;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    subsidyProtectPloughEntity.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    subsidyProtectPloughEntity.auditGTime = auditGTime;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    subsidyProtectPloughEntity.auditHId = auditHId;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    subsidyProtectPloughEntity.auditHFlag = auditHFlag;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    subsidyProtectPloughEntity.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    subsidyProtectPloughEntity.auditHTime = auditHTime;
  }
  final String? approvalRemark =
      jsonConvert.convert<String>(json['approvalRemark']);
  if (approvalRemark != null) {
    subsidyProtectPloughEntity.approvalRemark = approvalRemark;
  }
  final String? currentAuditRoleId =
      jsonConvert.convert<String>(json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    subsidyProtectPloughEntity.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName =
      jsonConvert.convert<String>(json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    subsidyProtectPloughEntity.currentAuditRoleName = currentAuditRoleName;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    subsidyProtectPloughEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    subsidyProtectPloughEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    subsidyProtectPloughEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    subsidyProtectPloughEntity.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    subsidyProtectPloughEntity.statusCd = statusCd;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    subsidyProtectPloughEntity.remark = remark;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    subsidyProtectPloughEntity.params = params;
  }
  final String? ifUpdate = jsonConvert.convert<String>(json['ifUpdate']);
  if (ifUpdate != null) {
    subsidyProtectPloughEntity.ifUpdate = ifUpdate;
  }
  final String? subsidyItemName =
      jsonConvert.convert<String>(json['subsidyItemName']);
  if (subsidyItemName != null) {
    subsidyProtectPloughEntity.subsidyItemName = subsidyItemName;
  }
  final String? subsidyBudgetTitle =
      jsonConvert.convert<String>(json['subsidyBudgetTitle']);
  if (subsidyBudgetTitle != null) {
    subsidyProtectPloughEntity.subsidyBudgetTitle = subsidyBudgetTitle;
  }
  final String? subsidyAgmachineWorkSubs =
      jsonConvert.convert<String>(json['subsidyAgmachineWorkSubs']);
  if (subsidyAgmachineWorkSubs != null) {
    subsidyProtectPloughEntity.subsidyAgmachineWorkSubs =
        subsidyAgmachineWorkSubs;
  }
  return subsidyProtectPloughEntity;
}

Map<String, dynamic> $SubsidyProtectPloughEntityToJson(
    SubsidyProtectPloughEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyAgmachineWorkId'] = entity.subsidyAgmachineWorkId;
  data['subsidyConfigId'] = entity.subsidyConfigId;
  data['subsidyYear'] = entity.subsidyYear;
  data['subsidyType'] = entity.subsidyType;
  data['subsidyProjectCode'] = entity.subsidyProjectCode;
  data['subsidyBudgetId'] = entity.subsidyBudgetId;
  data['farmerName'] = entity.farmerName;
  data['farmerId'] = entity.farmerId;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['amModelName'] = entity.amModelName;
  data['factoryCode'] = entity.factoryCode;
  data['bankAccount'] = entity.bankAccount;
  data['bankName'] = entity.bankName;
  data['network'] = entity.network;
  data['lineNumber'] = entity.lineNumber;
  data['subsidyCashArea'] = entity.subsidyCashArea;
  data['actualPaymentFee'] = entity.actualPaymentFee;
  data['subsidyArea1'] = entity.subsidyArea1;
  data['subsidyStandard1'] = entity.subsidyStandard1;
  data['subsidyFee1'] = entity.subsidyFee1;
  data['subsidyArea2'] = entity.subsidyArea2;
  data['subsidyStandard2'] = entity.subsidyStandard2;
  data['subsidyFee2'] = entity.subsidyFee2;
  data['subsidyArea3'] = entity.subsidyArea3;
  data['subsidyStandard3'] = entity.subsidyStandard3;
  data['subsidyFee3'] = entity.subsidyFee3;
  data['subsidyArea4'] = entity.subsidyArea4;
  data['subsidyStandard4'] = entity.subsidyStandard4;
  data['subsidyFee4'] = entity.subsidyFee4;
  data['subsidyArea5'] = entity.subsidyArea5;
  data['subsidyStandard5'] = entity.subsidyStandard5;
  data['subsidyFee5'] = entity.subsidyFee5;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['contractSignType'] = entity.contractSignType;
  data['partnerId'] = entity.partnerId;
  data['organizationNo'] = entity.organizationNo;
  data['organizationName'] = entity.organizationName;
  data['auditLevel'] = entity.auditLevel;
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['auditDId'] = entity.auditDId;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEId'] = entity.auditEId;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFId'] = entity.auditFId;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGId'] = entity.auditGId;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHId'] = entity.auditHId;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['approvalRemark'] = entity.approvalRemark;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['remark'] = entity.remark;
  data['params'] = entity.params;
  data['ifUpdate'] = entity.ifUpdate;
  data['subsidyItemName'] = entity.subsidyItemName;
  data['subsidyBudgetTitle'] = entity.subsidyBudgetTitle;
  data['subsidyAgmachineWorkSubs'] = entity.subsidyAgmachineWorkSubs;
  return data;
}

extension SubsidyProtectPloughEntityExtension on SubsidyProtectPloughEntity {
  SubsidyProtectPloughEntity copyWith({
    String? subsidyAgmachineWorkId,
    String? subsidyConfigId,
    String? subsidyYear,
    String? subsidyType,
    String? subsidyProjectCode,
    String? subsidyBudgetId,
    String? farmerName,
    String? farmerId,
    String? farmerIdNumber,
    String? amModelName,
    String? factoryCode,
    String? bankAccount,
    String? bankName,
    String? network,
    String? lineNumber,
    String? subsidyCashArea,
    String? actualPaymentFee,
    String? subsidyArea1,
    String? subsidyStandard1,
    String? subsidyFee1,
    String? subsidyArea2,
    String? subsidyStandard2,
    String? subsidyFee2,
    String? subsidyArea3,
    String? subsidyStandard3,
    String? subsidyFee3,
    String? subsidyArea4,
    String? subsidyStandard4,
    String? subsidyFee4,
    String? subsidyArea5,
    String? subsidyStandard5,
    String? subsidyFee5,
    String? approvalStatusNo,
    String? contractSignType,
    String? partnerId,
    String? organizationNo,
    String? organizationName,
    String? auditLevel,
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? auditDId,
    String? auditDFlag,
    String? auditDName,
    String? auditDTime,
    String? auditEId,
    String? auditEFlag,
    String? auditEName,
    String? auditETime,
    String? auditFId,
    String? auditFFlag,
    String? auditFName,
    String? auditFTime,
    String? auditGId,
    String? auditGFlag,
    String? auditGName,
    String? auditGTime,
    String? auditHId,
    String? auditHFlag,
    String? auditHName,
    String? auditHTime,
    String? approvalRemark,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? remark,
    String? params,
    String? ifUpdate,
    String? subsidyItemName,
    String? subsidyBudgetTitle,
    String? subsidyAgmachineWorkSubs,
  }) {
    return SubsidyProtectPloughEntity()
      ..subsidyAgmachineWorkId =
          subsidyAgmachineWorkId ?? this.subsidyAgmachineWorkId
      ..subsidyConfigId = subsidyConfigId ?? this.subsidyConfigId
      ..subsidyYear = subsidyYear ?? this.subsidyYear
      ..subsidyType = subsidyType ?? this.subsidyType
      ..subsidyProjectCode = subsidyProjectCode ?? this.subsidyProjectCode
      ..subsidyBudgetId = subsidyBudgetId ?? this.subsidyBudgetId
      ..farmerName = farmerName ?? this.farmerName
      ..farmerId = farmerId ?? this.farmerId
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..amModelName = amModelName ?? this.amModelName
      ..factoryCode = factoryCode ?? this.factoryCode
      ..bankAccount = bankAccount ?? this.bankAccount
      ..bankName = bankName ?? this.bankName
      ..network = network ?? this.network
      ..lineNumber = lineNumber ?? this.lineNumber
      ..subsidyCashArea = subsidyCashArea ?? this.subsidyCashArea
      ..actualPaymentFee = actualPaymentFee ?? this.actualPaymentFee
      ..subsidyArea1 = subsidyArea1 ?? this.subsidyArea1
      ..subsidyStandard1 = subsidyStandard1 ?? this.subsidyStandard1
      ..subsidyFee1 = subsidyFee1 ?? this.subsidyFee1
      ..subsidyArea2 = subsidyArea2 ?? this.subsidyArea2
      ..subsidyStandard2 = subsidyStandard2 ?? this.subsidyStandard2
      ..subsidyFee2 = subsidyFee2 ?? this.subsidyFee2
      ..subsidyArea3 = subsidyArea3 ?? this.subsidyArea3
      ..subsidyStandard3 = subsidyStandard3 ?? this.subsidyStandard3
      ..subsidyFee3 = subsidyFee3 ?? this.subsidyFee3
      ..subsidyArea4 = subsidyArea4 ?? this.subsidyArea4
      ..subsidyStandard4 = subsidyStandard4 ?? this.subsidyStandard4
      ..subsidyFee4 = subsidyFee4 ?? this.subsidyFee4
      ..subsidyArea5 = subsidyArea5 ?? this.subsidyArea5
      ..subsidyStandard5 = subsidyStandard5 ?? this.subsidyStandard5
      ..subsidyFee5 = subsidyFee5 ?? this.subsidyFee5
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..contractSignType = contractSignType ?? this.contractSignType
      ..partnerId = partnerId ?? this.partnerId
      ..organizationNo = organizationNo ?? this.organizationNo
      ..organizationName = organizationName ?? this.organizationName
      ..auditLevel = auditLevel ?? this.auditLevel
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..auditDId = auditDId ?? this.auditDId
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEId = auditEId ?? this.auditEId
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFId = auditFId ?? this.auditFId
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGId = auditGId ?? this.auditGId
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHId = auditHId ?? this.auditHId
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..remark = remark ?? this.remark
      ..params = params ?? this.params
      ..ifUpdate = ifUpdate ?? this.ifUpdate
      ..subsidyItemName = subsidyItemName ?? this.subsidyItemName
      ..subsidyBudgetTitle = subsidyBudgetTitle ?? this.subsidyBudgetTitle
      ..subsidyAgmachineWorkSubs =
          subsidyAgmachineWorkSubs ?? this.subsidyAgmachineWorkSubs;
  }
}
