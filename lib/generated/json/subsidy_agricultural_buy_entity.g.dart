import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_agricultural_buy_entity.dart';

SubsidyAgriculturalBuyEntity $SubsidyAgriculturalBuyEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyAgriculturalBuyEntity subsidyAgriculturalBuyEntity =
      SubsidyAgriculturalBuyEntity();
  final String? subsidyYear = jsonConvert.convert<String>(json['subsidyYear']);
  if (subsidyYear != null) {
    subsidyAgriculturalBuyEntity.subsidyYear = subsidyYear;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    subsidyAgriculturalBuyEntity.organizationNo = organizationNo;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    subsidyAgriculturalBuyEntity.organizationName = organizationName;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    subsidyAgriculturalBuyEntity.farmerName = farmerName;
  }
  final String? certNo = jsonConvert.convert<String>(json['certNo']);
  if (certNo != null) {
    subsidyAgriculturalBuyEntity.certNo = certNo;
  }
  final String? amTypeCode1 = jsonConvert.convert<String>(json['amTypeCode1']);
  if (amTypeCode1 != null) {
    subsidyAgriculturalBuyEntity.amTypeCode1 = amTypeCode1;
  }
  final String? amTypeName1 = jsonConvert.convert<String>(json['amTypeName1']);
  if (amTypeName1 != null) {
    subsidyAgriculturalBuyEntity.amTypeName1 = amTypeName1;
  }
  final String? amTypeCode2 = jsonConvert.convert<String>(json['amTypeCode2']);
  if (amTypeCode2 != null) {
    subsidyAgriculturalBuyEntity.amTypeCode2 = amTypeCode2;
  }
  final String? amTypeName2 = jsonConvert.convert<String>(json['amTypeName2']);
  if (amTypeName2 != null) {
    subsidyAgriculturalBuyEntity.amTypeName2 = amTypeName2;
  }
  final String? amTypeCode3 = jsonConvert.convert<String>(json['amTypeCode3']);
  if (amTypeCode3 != null) {
    subsidyAgriculturalBuyEntity.amTypeCode3 = amTypeCode3;
  }
  final String? amTypeName3 = jsonConvert.convert<String>(json['amTypeName3']);
  if (amTypeName3 != null) {
    subsidyAgriculturalBuyEntity.amTypeName3 = amTypeName3;
  }
  final String? companyName = jsonConvert.convert<String>(json['companyName']);
  if (companyName != null) {
    subsidyAgriculturalBuyEntity.companyName = companyName;
  }
  final String? productName = jsonConvert.convert<String>(json['productName']);
  if (productName != null) {
    subsidyAgriculturalBuyEntity.productName = productName;
  }
  final String? amModelName = jsonConvert.convert<String>(json['amModelName']);
  if (amModelName != null) {
    subsidyAgriculturalBuyEntity.amModelName = amModelName;
  }
  final String? factoryCode = jsonConvert.convert<String>(json['factoryCode']);
  if (factoryCode != null) {
    subsidyAgriculturalBuyEntity.factoryCode = factoryCode;
  }
  final String? saleCount = jsonConvert.convert<String>(json['saleCount']);
  if (saleCount != null) {
    subsidyAgriculturalBuyEntity.saleCount = saleCount;
  }
  final String? businessName =
      jsonConvert.convert<String>(json['businessName']);
  if (businessName != null) {
    subsidyAgriculturalBuyEntity.businessName = businessName;
  }
  final String? purchaseDate =
      jsonConvert.convert<String>(json['purchaseDate']);
  if (purchaseDate != null) {
    subsidyAgriculturalBuyEntity.purchaseDate = purchaseDate;
  }
  final String? singleSalePrice =
      jsonConvert.convert<String>(json['singleSalePrice']);
  if (singleSalePrice != null) {
    subsidyAgriculturalBuyEntity.singleSalePrice = singleSalePrice;
  }
  final String? price = jsonConvert.convert<String>(json['price']);
  if (price != null) {
    subsidyAgriculturalBuyEntity.price = price;
  }
  final String? singleSubsidy =
      jsonConvert.convert<String>(json['singleSubsidy']);
  if (singleSubsidy != null) {
    subsidyAgriculturalBuyEntity.singleSubsidy = singleSubsidy;
  }
  final String? subsidyTotalFee =
      jsonConvert.convert<String>(json['subsidyTotalFee']);
  if (subsidyTotalFee != null) {
    subsidyAgriculturalBuyEntity.subsidyTotalFee = subsidyTotalFee;
  }
  final String? subsidyGrantFee =
      jsonConvert.convert<String>(json['subsidyGrantFee']);
  if (subsidyGrantFee != null) {
    subsidyAgriculturalBuyEntity.subsidyGrantFee = subsidyGrantFee;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    subsidyAgriculturalBuyEntity.remark = remark;
  }
  final String? subsidyAgmachineDetailId =
      jsonConvert.convert<String>(json['subsidyAgmachineDetailId']);
  if (subsidyAgmachineDetailId != null) {
    subsidyAgriculturalBuyEntity.subsidyAgmachineDetailId =
        subsidyAgmachineDetailId;
  }
  final String? subsidyConfigId =
      jsonConvert.convert<String>(json['subsidyConfigId']);
  if (subsidyConfigId != null) {
    subsidyAgriculturalBuyEntity.subsidyConfigId = subsidyConfigId;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    subsidyAgriculturalBuyEntity.farmerId = farmerId;
  }
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  if (bankAccount != null) {
    subsidyAgriculturalBuyEntity.bankAccount = bankAccount;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    subsidyAgriculturalBuyEntity.bankName = bankName;
  }
  final String? grantFlag = jsonConvert.convert<String>(json['grantFlag']);
  if (grantFlag != null) {
    subsidyAgriculturalBuyEntity.grantFlag = grantFlag;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    subsidyAgriculturalBuyEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    subsidyAgriculturalBuyEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    subsidyAgriculturalBuyEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    subsidyAgriculturalBuyEntity.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    subsidyAgriculturalBuyEntity.statusCd = statusCd;
  }
  final String? dataStatus = jsonConvert.convert<String>(json['dataStatus']);
  if (dataStatus != null) {
    subsidyAgriculturalBuyEntity.dataStatus = dataStatus;
  }
  final String? approvalStatusNo =
      jsonConvert.convert<String>(json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    subsidyAgriculturalBuyEntity.approvalStatusNo = approvalStatusNo;
  }
  final String? network = jsonConvert.convert<String>(json['network']);
  if (network != null) {
    subsidyAgriculturalBuyEntity.network = network;
  }
  final String? lineNumber = jsonConvert.convert<String>(json['lineNumber']);
  if (lineNumber != null) {
    subsidyAgriculturalBuyEntity.lineNumber = lineNumber;
  }
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    subsidyAgriculturalBuyEntity.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    subsidyAgriculturalBuyEntity.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    subsidyAgriculturalBuyEntity.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    subsidyAgriculturalBuyEntity.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    subsidyAgriculturalBuyEntity.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    subsidyAgriculturalBuyEntity.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    subsidyAgriculturalBuyEntity.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    subsidyAgriculturalBuyEntity.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    subsidyAgriculturalBuyEntity.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    subsidyAgriculturalBuyEntity.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    subsidyAgriculturalBuyEntity.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    subsidyAgriculturalBuyEntity.auditCTime = auditCTime;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    subsidyAgriculturalBuyEntity.auditDId = auditDId;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    subsidyAgriculturalBuyEntity.auditDFlag = auditDFlag;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    subsidyAgriculturalBuyEntity.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    subsidyAgriculturalBuyEntity.auditDTime = auditDTime;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    subsidyAgriculturalBuyEntity.auditEId = auditEId;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    subsidyAgriculturalBuyEntity.auditEFlag = auditEFlag;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    subsidyAgriculturalBuyEntity.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    subsidyAgriculturalBuyEntity.auditETime = auditETime;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    subsidyAgriculturalBuyEntity.auditFId = auditFId;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    subsidyAgriculturalBuyEntity.auditFFlag = auditFFlag;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    subsidyAgriculturalBuyEntity.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    subsidyAgriculturalBuyEntity.auditFTime = auditFTime;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    subsidyAgriculturalBuyEntity.auditGId = auditGId;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    subsidyAgriculturalBuyEntity.auditGFlag = auditGFlag;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    subsidyAgriculturalBuyEntity.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    subsidyAgriculturalBuyEntity.auditGTime = auditGTime;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    subsidyAgriculturalBuyEntity.auditHId = auditHId;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    subsidyAgriculturalBuyEntity.auditHFlag = auditHFlag;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    subsidyAgriculturalBuyEntity.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    subsidyAgriculturalBuyEntity.auditHTime = auditHTime;
  }
  final String? currentAuditRoleId =
      jsonConvert.convert<String>(json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    subsidyAgriculturalBuyEntity.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName =
      jsonConvert.convert<String>(json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    subsidyAgriculturalBuyEntity.currentAuditRoleName = currentAuditRoleName;
  }
  final String? contractSignType =
      jsonConvert.convert<String>(json['contractSignType']);
  if (contractSignType != null) {
    subsidyAgriculturalBuyEntity.contractSignType = contractSignType;
  }
  final String? partnerId = jsonConvert.convert<String>(json['partnerId']);
  if (partnerId != null) {
    subsidyAgriculturalBuyEntity.partnerId = partnerId;
  }
  final String? agmachineDetailId =
      jsonConvert.convert<String>(json['agmachineDetailId']);
  if (agmachineDetailId != null) {
    subsidyAgriculturalBuyEntity.agmachineDetailId = agmachineDetailId;
  }
  final String? subsidyType = jsonConvert.convert<String>(json['subsidyType']);
  if (subsidyType != null) {
    subsidyAgriculturalBuyEntity.subsidyType = subsidyType;
  }
  final String? subsidyBudgetId =
      jsonConvert.convert<String>(json['subsidyBudgetId']);
  if (subsidyBudgetId != null) {
    subsidyAgriculturalBuyEntity.subsidyBudgetId = subsidyBudgetId;
  }
  final String? subsidyAgmachineType =
      jsonConvert.convert<String>(json['subsidyAgmachineType']);
  if (subsidyAgmachineType != null) {
    subsidyAgriculturalBuyEntity.subsidyAgmachineType = subsidyAgmachineType;
  }
  final String? approvalRemark =
      jsonConvert.convert<String>(json['approvalRemark']);
  if (approvalRemark != null) {
    subsidyAgriculturalBuyEntity.approvalRemark = approvalRemark;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    subsidyAgriculturalBuyEntity.params = params;
  }
  final String? farmerIdNumber =
      jsonConvert.convert<String>(json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    subsidyAgriculturalBuyEntity.farmerIdNumber = farmerIdNumber;
  }
  final String? subsidySendFee =
      jsonConvert.convert<String>(json['subsidySendFee']);
  if (subsidySendFee != null) {
    subsidyAgriculturalBuyEntity.subsidySendFee = subsidySendFee;
  }
  final String? subsidyItemName =
      jsonConvert.convert<String>(json['subsidyItemName']);
  if (subsidyItemName != null) {
    subsidyAgriculturalBuyEntity.subsidyItemName = subsidyItemName;
  }
  final String? ifUpdate = jsonConvert.convert<String>(json['ifUpdate']);
  if (ifUpdate != null) {
    subsidyAgriculturalBuyEntity.ifUpdate = ifUpdate;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    subsidyAgriculturalBuyEntity.auditLevel = auditLevel;
  }
  final String? subsidyBudgetTitle =
      jsonConvert.convert<String>(json['subsidyBudgetTitle']);
  if (subsidyBudgetTitle != null) {
    subsidyAgriculturalBuyEntity.subsidyBudgetTitle = subsidyBudgetTitle;
  }
  final String? subsidyBatchNo =
      jsonConvert.convert<String>(json['subsidyBatchNo']);
  if (subsidyBatchNo != null) {
    subsidyAgriculturalBuyEntity.subsidyBatchNo = subsidyBatchNo;
  }
  return subsidyAgriculturalBuyEntity;
}

Map<String, dynamic> $SubsidyAgriculturalBuyEntityToJson(
    SubsidyAgriculturalBuyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyYear'] = entity.subsidyYear;
  data['organizationNo'] = entity.organizationNo;
  data['organizationName'] = entity.organizationName;
  data['farmerName'] = entity.farmerName;
  data['certNo'] = entity.certNo;
  data['amTypeCode1'] = entity.amTypeCode1;
  data['amTypeName1'] = entity.amTypeName1;
  data['amTypeCode2'] = entity.amTypeCode2;
  data['amTypeName2'] = entity.amTypeName2;
  data['amTypeCode3'] = entity.amTypeCode3;
  data['amTypeName3'] = entity.amTypeName3;
  data['companyName'] = entity.companyName;
  data['productName'] = entity.productName;
  data['amModelName'] = entity.amModelName;
  data['factoryCode'] = entity.factoryCode;
  data['saleCount'] = entity.saleCount;
  data['businessName'] = entity.businessName;
  data['purchaseDate'] = entity.purchaseDate;
  data['singleSalePrice'] = entity.singleSalePrice;
  data['price'] = entity.price;
  data['singleSubsidy'] = entity.singleSubsidy;
  data['subsidyTotalFee'] = entity.subsidyTotalFee;
  data['subsidyGrantFee'] = entity.subsidyGrantFee;
  data['remark'] = entity.remark;
  data['subsidyAgmachineDetailId'] = entity.subsidyAgmachineDetailId;
  data['subsidyConfigId'] = entity.subsidyConfigId;
  data['farmerId'] = entity.farmerId;
  data['bankAccount'] = entity.bankAccount;
  data['bankName'] = entity.bankName;
  data['grantFlag'] = entity.grantFlag;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['dataStatus'] = entity.dataStatus;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['network'] = entity.network;
  data['lineNumber'] = entity.lineNumber;
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['auditDId'] = entity.auditDId;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEId'] = entity.auditEId;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFId'] = entity.auditFId;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGId'] = entity.auditGId;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHId'] = entity.auditHId;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['contractSignType'] = entity.contractSignType;
  data['partnerId'] = entity.partnerId;
  data['agmachineDetailId'] = entity.agmachineDetailId;
  data['subsidyType'] = entity.subsidyType;
  data['subsidyBudgetId'] = entity.subsidyBudgetId;
  data['subsidyAgmachineType'] = entity.subsidyAgmachineType;
  data['approvalRemark'] = entity.approvalRemark;
  data['params'] = entity.params;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['subsidySendFee'] = entity.subsidySendFee;
  data['subsidyItemName'] = entity.subsidyItemName;
  data['ifUpdate'] = entity.ifUpdate;
  data['auditLevel'] = entity.auditLevel;
  data['subsidyBudgetTitle'] = entity.subsidyBudgetTitle;
  data['subsidyBatchNo'] = entity.subsidyBatchNo;
  return data;
}

extension SubsidyAgriculturalBuyEntityExtension
    on SubsidyAgriculturalBuyEntity {
  SubsidyAgriculturalBuyEntity copyWith({
    String? subsidyYear,
    String? organizationNo,
    String? organizationName,
    String? farmerName,
    String? certNo,
    String? amTypeCode1,
    String? amTypeName1,
    String? amTypeCode2,
    String? amTypeName2,
    String? amTypeCode3,
    String? amTypeName3,
    String? companyName,
    String? productName,
    String? amModelName,
    String? factoryCode,
    String? saleCount,
    String? businessName,
    String? purchaseDate,
    String? singleSalePrice,
    String? price,
    String? singleSubsidy,
    String? subsidyTotalFee,
    String? subsidyGrantFee,
    String? remark,
    String? subsidyAgmachineDetailId,
    String? subsidyConfigId,
    String? farmerId,
    String? bankAccount,
    String? bankName,
    String? grantFlag,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? dataStatus,
    String? approvalStatusNo,
    String? network,
    String? lineNumber,
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? auditDId,
    String? auditDFlag,
    String? auditDName,
    String? auditDTime,
    String? auditEId,
    String? auditEFlag,
    String? auditEName,
    String? auditETime,
    String? auditFId,
    String? auditFFlag,
    String? auditFName,
    String? auditFTime,
    String? auditGId,
    String? auditGFlag,
    String? auditGName,
    String? auditGTime,
    String? auditHId,
    String? auditHFlag,
    String? auditHName,
    String? auditHTime,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? contractSignType,
    String? partnerId,
    String? agmachineDetailId,
    String? subsidyType,
    String? subsidyBudgetId,
    String? subsidyAgmachineType,
    String? approvalRemark,
    String? params,
    String? farmerIdNumber,
    String? subsidySendFee,
    String? subsidyItemName,
    String? ifUpdate,
    String? auditLevel,
    String? subsidyBudgetTitle,
    String? subsidyBatchNo,
  }) {
    return SubsidyAgriculturalBuyEntity()
      ..subsidyYear = subsidyYear ?? this.subsidyYear
      ..organizationNo = organizationNo ?? this.organizationNo
      ..organizationName = organizationName ?? this.organizationName
      ..farmerName = farmerName ?? this.farmerName
      ..certNo = certNo ?? this.certNo
      ..amTypeCode1 = amTypeCode1 ?? this.amTypeCode1
      ..amTypeName1 = amTypeName1 ?? this.amTypeName1
      ..amTypeCode2 = amTypeCode2 ?? this.amTypeCode2
      ..amTypeName2 = amTypeName2 ?? this.amTypeName2
      ..amTypeCode3 = amTypeCode3 ?? this.amTypeCode3
      ..amTypeName3 = amTypeName3 ?? this.amTypeName3
      ..companyName = companyName ?? this.companyName
      ..productName = productName ?? this.productName
      ..amModelName = amModelName ?? this.amModelName
      ..factoryCode = factoryCode ?? this.factoryCode
      ..saleCount = saleCount ?? this.saleCount
      ..businessName = businessName ?? this.businessName
      ..purchaseDate = purchaseDate ?? this.purchaseDate
      ..singleSalePrice = singleSalePrice ?? this.singleSalePrice
      ..price = price ?? this.price
      ..singleSubsidy = singleSubsidy ?? this.singleSubsidy
      ..subsidyTotalFee = subsidyTotalFee ?? this.subsidyTotalFee
      ..subsidyGrantFee = subsidyGrantFee ?? this.subsidyGrantFee
      ..remark = remark ?? this.remark
      ..subsidyAgmachineDetailId =
          subsidyAgmachineDetailId ?? this.subsidyAgmachineDetailId
      ..subsidyConfigId = subsidyConfigId ?? this.subsidyConfigId
      ..farmerId = farmerId ?? this.farmerId
      ..bankAccount = bankAccount ?? this.bankAccount
      ..bankName = bankName ?? this.bankName
      ..grantFlag = grantFlag ?? this.grantFlag
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..dataStatus = dataStatus ?? this.dataStatus
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..network = network ?? this.network
      ..lineNumber = lineNumber ?? this.lineNumber
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..auditDId = auditDId ?? this.auditDId
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEId = auditEId ?? this.auditEId
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFId = auditFId ?? this.auditFId
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGId = auditGId ?? this.auditGId
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHId = auditHId ?? this.auditHId
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..contractSignType = contractSignType ?? this.contractSignType
      ..partnerId = partnerId ?? this.partnerId
      ..agmachineDetailId = agmachineDetailId ?? this.agmachineDetailId
      ..subsidyType = subsidyType ?? this.subsidyType
      ..subsidyBudgetId = subsidyBudgetId ?? this.subsidyBudgetId
      ..subsidyAgmachineType = subsidyAgmachineType ?? this.subsidyAgmachineType
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..params = params ?? this.params
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..subsidySendFee = subsidySendFee ?? this.subsidySendFee
      ..subsidyItemName = subsidyItemName ?? this.subsidyItemName
      ..ifUpdate = ifUpdate ?? this.ifUpdate
      ..auditLevel = auditLevel ?? this.auditLevel
      ..subsidyBudgetTitle = subsidyBudgetTitle ?? this.subsidyBudgetTitle
      ..subsidyBatchNo = subsidyBatchNo ?? this.subsidyBatchNo;
  }
}
