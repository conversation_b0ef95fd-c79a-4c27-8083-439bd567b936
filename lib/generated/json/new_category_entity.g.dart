import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/new_category_entity.dart';

NewCategoryEntity $NewCategoryEntityFromJson(Map<String, dynamic> json) {
  final NewCategoryEntity newCategoryEntity = NewCategoryEntity();
  final int? columnLevelId = jsonConvert.convert<int>(json['columnLevelId']);
  if (columnLevelId != null) {
    newCategoryEntity.columnLevelId = columnLevelId;
  }
  final String? columnLevelName =
      jsonConvert.convert<String>(json['columnLevelName']);
  if (columnLevelName != null) {
    newCategoryEntity.columnLevelName = columnLevelName;
  }
  final dynamic parentId = json['parentId'];
  if (parentId != null) {
    newCategoryEntity.parentId = parentId;
  }
  final int? columnLevel = jsonConvert.convert<int>(json['columnLevel']);
  if (columnLevel != null) {
    newCategoryEntity.columnLevel = columnLevel;
  }
  final String? systemCode = jsonConvert.convert<String>(json['systemCode']);
  if (systemCode != null) {
    newCategoryEntity.systemCode = systemCode;
  }
  final String? systemName = jsonConvert.convert<String>(json['systemName']);
  if (systemName != null) {
    newCategoryEntity.systemName = systemName;
  }
  final dynamic remark = json['remark'];
  if (remark != null) {
    newCategoryEntity.remark = remark;
  }
  final int? createBy = jsonConvert.convert<int>(json['createBy']);
  if (createBy != null) {
    newCategoryEntity.createBy = createBy;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    newCategoryEntity.createTime = createTime;
  }
  final dynamic updateBy = json['updateBy'];
  if (updateBy != null) {
    newCategoryEntity.updateBy = updateBy;
  }
  final dynamic updateTime = json['updateTime'];
  if (updateTime != null) {
    newCategoryEntity.updateTime = updateTime;
  }
  final dynamic statusCd = json['statusCd'];
  if (statusCd != null) {
    newCategoryEntity.statusCd = statusCd;
  }
  final dynamic params = json['params'];
  if (params != null) {
    newCategoryEntity.params = params;
  }
  final String? userType = jsonConvert.convert<String>(json['userType']);
  if (userType != null) {
    newCategoryEntity.userType = userType;
  }
  final dynamic list = json['list'];
  if (list != null) {
    newCategoryEntity.list = list;
  }
  return newCategoryEntity;
}

Map<String, dynamic> $NewCategoryEntityToJson(NewCategoryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['columnLevelId'] = entity.columnLevelId;
  data['columnLevelName'] = entity.columnLevelName;
  data['parentId'] = entity.parentId;
  data['columnLevel'] = entity.columnLevel;
  data['systemCode'] = entity.systemCode;
  data['systemName'] = entity.systemName;
  data['remark'] = entity.remark;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['params'] = entity.params;
  data['userType'] = entity.userType;
  data['list'] = entity.list;
  return data;
}

extension NewCategoryEntityExtension on NewCategoryEntity {
  NewCategoryEntity copyWith({
    int? columnLevelId,
    String? columnLevelName,
    dynamic parentId,
    int? columnLevel,
    String? systemCode,
    String? systemName,
    dynamic remark,
    int? createBy,
    int? createTime,
    dynamic updateBy,
    dynamic updateTime,
    dynamic statusCd,
    dynamic params,
    String? userType,
    dynamic list,
  }) {
    return NewCategoryEntity()
      ..columnLevelId = columnLevelId ?? this.columnLevelId
      ..columnLevelName = columnLevelName ?? this.columnLevelName
      ..parentId = parentId ?? this.parentId
      ..columnLevel = columnLevel ?? this.columnLevel
      ..systemCode = systemCode ?? this.systemCode
      ..systemName = systemName ?? this.systemName
      ..remark = remark ?? this.remark
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..params = params ?? this.params
      ..userType = userType ?? this.userType
      ..list = list ?? this.list;
  }
}
