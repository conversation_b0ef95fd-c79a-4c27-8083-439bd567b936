import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/myfieldchat/my_field_chat_message_entity.dart';

MyFieldChatMessageEntity $MyFieldChatMessageEntityFromJson(
    Map<String, dynamic> json) {
  final MyFieldChatMessageEntity myFieldChatMessageEntity =
      MyFieldChatMessageEntity();
  final String? event = jsonConvert.convert<String>(json['event']);
  if (event != null) {
    myFieldChatMessageEntity.event = event;
  }
  final String? conversationId =
      jsonConvert.convert<String>(json['conversation_id']);
  if (conversationId != null) {
    myFieldChatMessageEntity.conversationId = conversationId;
  }
  final String? messageId = jsonConvert.convert<String>(json['message_id']);
  if (messageId != null) {
    myFieldChatMessageEntity.messageId = messageId;
  }
  final int? createdAt = jsonConvert.convert<int>(json['created_at']);
  if (createdAt != null) {
    myFieldChatMessageEntity.createdAt = createdAt;
  }
  final String? taskId = jsonConvert.convert<String>(json['task_id']);
  if (taskId != null) {
    myFieldChatMessageEntity.taskId = taskId;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    myFieldChatMessageEntity.id = id;
  }
  final String? answer = jsonConvert.convert<String>(json['answer']);
  if (answer != null) {
    myFieldChatMessageEntity.answer = answer;
  }
  final String? metadata = jsonConvert.convert<String>(json['metadata']);
  if (metadata != null) {
    myFieldChatMessageEntity.metadata = metadata;
  }
  return myFieldChatMessageEntity;
}

Map<String, dynamic> $MyFieldChatMessageEntityToJson(
    MyFieldChatMessageEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['event'] = entity.event;
  data['conversation_id'] = entity.conversationId;
  data['message_id'] = entity.messageId;
  data['created_at'] = entity.createdAt;
  data['task_id'] = entity.taskId;
  data['id'] = entity.id;
  data['answer'] = entity.answer;
  data['metadata'] = entity.metadata;
  return data;
}

extension MyFieldChatMessageEntityExtension on MyFieldChatMessageEntity {
  MyFieldChatMessageEntity copyWith({
    String? event,
    String? conversationId,
    String? messageId,
    int? createdAt,
    String? taskId,
    String? id,
    String? answer,
    String? metadata,
  }) {
    return MyFieldChatMessageEntity()
      ..event = event ?? this.event
      ..conversationId = conversationId ?? this.conversationId
      ..messageId = messageId ?? this.messageId
      ..createdAt = createdAt ?? this.createdAt
      ..taskId = taskId ?? this.taskId
      ..id = id ?? this.id
      ..answer = answer ?? this.answer
      ..metadata = metadata ?? this.metadata;
  }
}
