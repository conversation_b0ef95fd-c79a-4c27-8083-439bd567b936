import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/region_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/district_entity.dart';

RegionEntity $RegionEntityFromJson(Map<String, dynamic> json) {
  final RegionEntity regionEntity = RegionEntity();
  final String? regionName = jsonConvert.convert<String>(json['regionName']);
  if (regionName != null) {
    regionEntity.regionName = regionName;
  }
  final int? regionId = jsonConvert.convert<int>(json['regionId']);
  if (regionId != null) {
    regionEntity.regionId = regionId;
  }
  final String? parentId = jsonConvert.convert<String>(json['parentId']);
  if (parentId != null) {
    regionEntity.parentId = parentId;
  }
  final int? regionLevel = jsonConvert.convert<int>(json['regionLevel']);
  if (regionLevel != null) {
    regionEntity.regionLevel = regionLevel;
  }
  final String? currPointLong =
      jsonConvert.convert<String>(json['currPointLong']);
  if (currPointLong != null) {
    regionEntity.currPointLong = currPointLong;
  }
  final String? currPointLat =
      jsonConvert.convert<String>(json['currPointLat']);
  if (currPointLat != null) {
    regionEntity.currPointLat = currPointLat;
  }
  final List<DistrictEntity>? districtList =
      (json['districtList'] as List<dynamic>?)
          ?.map((e) => jsonConvert.convert<DistrictEntity>(e) as DistrictEntity)
          .toList();
  if (districtList != null) {
    regionEntity.districtList = districtList;
  }
  return regionEntity;
}

Map<String, dynamic> $RegionEntityToJson(RegionEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['regionName'] = entity.regionName;
  data['regionId'] = entity.regionId;
  data['parentId'] = entity.parentId;
  data['regionLevel'] = entity.regionLevel;
  data['currPointLong'] = entity.currPointLong;
  data['currPointLat'] = entity.currPointLat;
  data['districtList'] = entity.districtList.map((v) => v.toJson()).toList();
  return data;
}

extension RegionEntityExtension on RegionEntity {
  RegionEntity copyWith({
    String? regionName,
    int? regionId,
    String? parentId,
    int? regionLevel,
    String? currPointLong,
    String? currPointLat,
    List<DistrictEntity>? districtList,
  }) {
    return RegionEntity()
      ..regionName = regionName ?? this.regionName
      ..regionId = regionId ?? this.regionId
      ..parentId = parentId ?? this.parentId
      ..regionLevel = regionLevel ?? this.regionLevel
      ..currPointLong = currPointLong ?? this.currPointLong
      ..currPointLat = currPointLat ?? this.currPointLat
      ..districtList = districtList ?? this.districtList;
  }
}
