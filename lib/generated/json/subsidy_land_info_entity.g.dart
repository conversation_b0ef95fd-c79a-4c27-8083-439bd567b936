import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_land_info_entity.dart';

SubsidyLandInfoEntity $SubsidyLandInfoEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyLandInfoEntity subsidyLandInfoEntity = SubsidyLandInfoEntity();
  final double? subsidyApplyDetailSubId =
      jsonConvert.convert<double>(json['subsidyApplyDetailSubId']);
  if (subsidyApplyDetailSubId != null) {
    subsidyLandInfoEntity.subsidyApplyDetailSubId = subsidyApplyDetailSubId;
  }
  final double? subsidyApplyDetailId =
      jsonConvert.convert<double>(json['subsidyApplyDetailId']);
  if (subsidyApplyDetailId != null) {
    subsidyLandInfoEntity.subsidyApplyDetailId = subsidyApplyDetailId;
  }
  final String? subsidyCropCode =
      jsonConvert.convert<String>(json['subsidyCropCode']);
  if (subsidyCropCode != null) {
    subsidyLandInfoEntity.subsidyCropCode = subsidyCropCode;
  }
  final String? landNumber = jsonConvert.convert<String>(json['landNumber']);
  if (landNumber != null) {
    subsidyLandInfoEntity.landNumber = landNumber;
  }
  final num? subsidyArea1 = jsonConvert.convert<num>(json['subsidyArea1']);
  if (subsidyArea1 != null) {
    subsidyLandInfoEntity.subsidyArea1 = subsidyArea1;
  }
  final num? subsidyArea2 = jsonConvert.convert<num>(json['subsidyArea2']);
  if (subsidyArea2 != null) {
    subsidyLandInfoEntity.subsidyArea2 = subsidyArea2;
  }
  final double? createBy = jsonConvert.convert<double>(json['createBy']);
  if (createBy != null) {
    subsidyLandInfoEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    subsidyLandInfoEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    subsidyLandInfoEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    subsidyLandInfoEntity.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    subsidyLandInfoEntity.statusCd = statusCd;
  }
  final double? subsidyYear = jsonConvert.convert<double>(json['subsidyYear']);
  if (subsidyYear != null) {
    subsidyLandInfoEntity.subsidyYear = subsidyYear;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    subsidyLandInfoEntity.organizationNo = organizationNo;
  }
  final double? subsidyConfigId =
      jsonConvert.convert<double>(json['subsidyConfigId']);
  if (subsidyConfigId != null) {
    subsidyLandInfoEntity.subsidyConfigId = subsidyConfigId;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    subsidyLandInfoEntity.organizationName = organizationName;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    subsidyLandInfoEntity.remark = remark;
  }
  final String? serialNumber =
      jsonConvert.convert<String>(json['serialNumber']);
  if (serialNumber != null) {
    subsidyLandInfoEntity.serialNumber = serialNumber;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    subsidyLandInfoEntity.params = params;
  }
  return subsidyLandInfoEntity;
}

Map<String, dynamic> $SubsidyLandInfoEntityToJson(
    SubsidyLandInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyApplyDetailSubId'] = entity.subsidyApplyDetailSubId;
  data['subsidyApplyDetailId'] = entity.subsidyApplyDetailId;
  data['subsidyCropCode'] = entity.subsidyCropCode;
  data['landNumber'] = entity.landNumber;
  data['subsidyArea1'] = entity.subsidyArea1;
  data['subsidyArea2'] = entity.subsidyArea2;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['subsidyYear'] = entity.subsidyYear;
  data['organizationNo'] = entity.organizationNo;
  data['subsidyConfigId'] = entity.subsidyConfigId;
  data['organizationName'] = entity.organizationName;
  data['remark'] = entity.remark;
  data['serialNumber'] = entity.serialNumber;
  data['params'] = entity.params;
  return data;
}

extension SubsidyLandInfoEntityExtension on SubsidyLandInfoEntity {
  SubsidyLandInfoEntity copyWith({
    double? subsidyApplyDetailSubId,
    double? subsidyApplyDetailId,
    String? subsidyCropCode,
    String? landNumber,
    num? subsidyArea1,
    num? subsidyArea2,
    double? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    double? subsidyYear,
    String? organizationNo,
    double? subsidyConfigId,
    String? organizationName,
    String? remark,
    String? serialNumber,
    String? params,
  }) {
    return SubsidyLandInfoEntity()
      ..subsidyApplyDetailSubId =
          subsidyApplyDetailSubId ?? this.subsidyApplyDetailSubId
      ..subsidyApplyDetailId = subsidyApplyDetailId ?? this.subsidyApplyDetailId
      ..subsidyCropCode = subsidyCropCode ?? this.subsidyCropCode
      ..landNumber = landNumber ?? this.landNumber
      ..subsidyArea1 = subsidyArea1 ?? this.subsidyArea1
      ..subsidyArea2 = subsidyArea2 ?? this.subsidyArea2
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..subsidyYear = subsidyYear ?? this.subsidyYear
      ..organizationNo = organizationNo ?? this.organizationNo
      ..subsidyConfigId = subsidyConfigId ?? this.subsidyConfigId
      ..organizationName = organizationName ?? this.organizationName
      ..remark = remark ?? this.remark
      ..serialNumber = serialNumber ?? this.serialNumber
      ..params = params ?? this.params;
  }
}
