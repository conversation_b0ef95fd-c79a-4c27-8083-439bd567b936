// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/deep_operation_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/district_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/free_sow_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/information_detail_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/information_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/poi_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/purchase_subsidy_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/region_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/chat_message_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/history_message_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/question_dictionary_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/question_record_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_black_land_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_confire_detail_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_contract_info_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_contract_item_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/myfieldchat/my_field_chat_message_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/myfieldchat/my_field_history_message_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/myfieldchat/my_field_question_dictionary_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/myfieldchat/my_field_question_record_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/new_category_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/new_detail_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/news_of_subsidy_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_agricultural_buy_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_agricultural_scrap_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_crop_production_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_grain_feed_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_protect_plough_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity_response_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_apply_info_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_detail_item_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_file_item_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_item_info_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_land_info_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_of_mine_entity.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_publicity_entity.dart';
import 'package:bdh_smart_agric_app/pages/worktable/entity/task_to_do_entity.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(
    Object error, StackTrace stackTrace);

extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value
          .map((dynamic e) => _asT<T>(e, enumConvert: enumConvert))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>)
          .map((dynamic e) => _asT<T>(e, enumConvert: enumConvert)!)
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if (covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        } else {
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError(
            '$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<DeepOperationEntity>[] is M) {
      return data
          .map<DeepOperationEntity>(
              (Map<String, dynamic> e) => DeepOperationEntity.fromJson(e))
          .toList() as M;
    }
    if (<DeepOperationRecord>[] is M) {
      return data
          .map<DeepOperationRecord>(
              (Map<String, dynamic> e) => DeepOperationRecord.fromJson(e))
          .toList() as M;
    }
    if (<DistrictEntity>[] is M) {
      return data
          .map<DistrictEntity>(
              (Map<String, dynamic> e) => DistrictEntity.fromJson(e))
          .toList() as M;
    }
    if (<FreeSowEntity>[] is M) {
      return data
          .map<FreeSowEntity>(
              (Map<String, dynamic> e) => FreeSowEntity.fromJson(e))
          .toList() as M;
    }
    if (<FreeSowResultList>[] is M) {
      return data
          .map<FreeSowResultList>(
              (Map<String, dynamic> e) => FreeSowResultList.fromJson(e))
          .toList() as M;
    }
    if (<InformationDetailEntity>[] is M) {
      return data
          .map<InformationDetailEntity>(
              (Map<String, dynamic> e) => InformationDetailEntity.fromJson(e))
          .toList() as M;
    }
    if (<InformationEntity>[] is M) {
      return data
          .map<InformationEntity>(
              (Map<String, dynamic> e) => InformationEntity.fromJson(e))
          .toList() as M;
    }
    if (<POIEntity>[] is M) {
      return data
          .map<POIEntity>((Map<String, dynamic> e) => POIEntity.fromJson(e))
          .toList() as M;
    }
    if (<PurchaseSubsidyEntity>[] is M) {
      return data
          .map<PurchaseSubsidyEntity>(
              (Map<String, dynamic> e) => PurchaseSubsidyEntity.fromJson(e))
          .toList() as M;
    }
    if (<RegionEntity>[] is M) {
      return data
          .map<RegionEntity>(
              (Map<String, dynamic> e) => RegionEntity.fromJson(e))
          .toList() as M;
    }
    if (<ChatMessageEntity>[] is M) {
      return data
          .map<ChatMessageEntity>(
              (Map<String, dynamic> e) => ChatMessageEntity.fromJson(e))
          .toList() as M;
    }
    if (<HistoryMessageEntity>[] is M) {
      return data
          .map<HistoryMessageEntity>(
              (Map<String, dynamic> e) => HistoryMessageEntity.fromJson(e))
          .toList() as M;
    }
    if (<HistoryMessageInputs>[] is M) {
      return data
          .map<HistoryMessageInputs>(
              (Map<String, dynamic> e) => HistoryMessageInputs.fromJson(e))
          .toList() as M;
    }
    if (<HistoryMessageRetrieverResources>[] is M) {
      return data
          .map<HistoryMessageRetrieverResources>((Map<String, dynamic> e) =>
              HistoryMessageRetrieverResources.fromJson(e))
          .toList() as M;
    }
    if (<QuestionDictionaryEntity>[] is M) {
      return data
          .map<QuestionDictionaryEntity>(
              (Map<String, dynamic> e) => QuestionDictionaryEntity.fromJson(e))
          .toList() as M;
    }
    if (<QuestionRecordEntity>[] is M) {
      return data
          .map<QuestionRecordEntity>(
              (Map<String, dynamic> e) => QuestionRecordEntity.fromJson(e))
          .toList() as M;
    }
    if (<QuestionRecordInputs>[] is M) {
      return data
          .map<QuestionRecordInputs>(
              (Map<String, dynamic> e) => QuestionRecordInputs.fromJson(e))
          .toList() as M;
    }
    if (<InsureBlackLandEntity>[] is M) {
      return data
          .map<InsureBlackLandEntity>(
              (Map<String, dynamic> e) => InsureBlackLandEntity.fromJson(e))
          .toList() as M;
    }
    if (<InsureConfirmDetailEntity>[] is M) {
      return data
          .map<InsureConfirmDetailEntity>(
              (Map<String, dynamic> e) => InsureConfirmDetailEntity.fromJson(e))
          .toList() as M;
    }
    if (<InsureConfireDetailBindPlotInfos>[] is M) {
      return data
          .map<InsureConfireDetailBindPlotInfos>((Map<String, dynamic> e) =>
              InsureConfireDetailBindPlotInfos.fromJson(e))
          .toList() as M;
    }
    if (<InsureContractInfoEntity>[] is M) {
      return data
          .map<InsureContractInfoEntity>(
              (Map<String, dynamic> e) => InsureContractInfoEntity.fromJson(e))
          .toList() as M;
    }
    if (<InsureContractInfoContract>[] is M) {
      return data
          .map<InsureContractInfoContract>((Map<String, dynamic> e) =>
              InsureContractInfoContract.fromJson(e))
          .toList() as M;
    }
    if (<InsureContractInfoFarmer>[] is M) {
      return data
          .map<InsureContractInfoFarmer>(
              (Map<String, dynamic> e) => InsureContractInfoFarmer.fromJson(e))
          .toList() as M;
    }
    if (<InsureContractInfoFarmerFarmer>[] is M) {
      return data
          .map<InsureContractInfoFarmerFarmer>((Map<String, dynamic> e) =>
              InsureContractInfoFarmerFarmer.fromJson(e))
          .toList() as M;
    }
    if (<InsureContractInfoPlotList>[] is M) {
      return data
          .map<InsureContractInfoPlotList>((Map<String, dynamic> e) =>
              InsureContractInfoPlotList.fromJson(e))
          .toList() as M;
    }
    if (<InsureContractInfoBankcard>[] is M) {
      return data
          .map<InsureContractInfoBankcard>((Map<String, dynamic> e) =>
              InsureContractInfoBankcard.fromJson(e))
          .toList() as M;
    }
    if (<InsureContractItemEntity>[] is M) {
      return data
          .map<InsureContractItemEntity>(
              (Map<String, dynamic> e) => InsureContractItemEntity.fromJson(e))
          .toList() as M;
    }
    if (<InsureContractItemContractSource>[] is M) {
      return data
          .map<InsureContractItemContractSource>((Map<String, dynamic> e) =>
              InsureContractItemContractSource.fromJson(e))
          .toList() as M;
    }
    if (<MyFieldChatMessageEntity>[] is M) {
      return data
          .map<MyFieldChatMessageEntity>(
              (Map<String, dynamic> e) => MyFieldChatMessageEntity.fromJson(e))
          .toList() as M;
    }
    if (<MyFieldHistoryMessageEntity>[] is M) {
      return data
          .map<MyFieldHistoryMessageEntity>((Map<String, dynamic> e) =>
              MyFieldHistoryMessageEntity.fromJson(e))
          .toList() as M;
    }
    if (<MyFieldHistoryMessageInputs>[] is M) {
      return data
          .map<MyFieldHistoryMessageInputs>((Map<String, dynamic> e) =>
              MyFieldHistoryMessageInputs.fromJson(e))
          .toList() as M;
    }
    if (<MyFieldHistoryMessageRetrieverResources>[] is M) {
      return data
          .map<MyFieldHistoryMessageRetrieverResources>(
              (Map<String, dynamic> e) =>
                  MyFieldHistoryMessageRetrieverResources.fromJson(e))
          .toList() as M;
    }
    if (<MyFieldQuestionDictionaryEntity>[] is M) {
      return data
          .map<MyFieldQuestionDictionaryEntity>((Map<String, dynamic> e) =>
              MyFieldQuestionDictionaryEntity.fromJson(e))
          .toList() as M;
    }
    if (<MyFieldQuestionRecordEntity>[] is M) {
      return data
          .map<MyFieldQuestionRecordEntity>((Map<String, dynamic> e) =>
              MyFieldQuestionRecordEntity.fromJson(e))
          .toList() as M;
    }
    if (<MyFieldQuestionRecordInputs>[] is M) {
      return data
          .map<MyFieldQuestionRecordInputs>((Map<String, dynamic> e) =>
              MyFieldQuestionRecordInputs.fromJson(e))
          .toList() as M;
    }
    if (<NewCategoryEntity>[] is M) {
      return data
          .map<NewCategoryEntity>(
              (Map<String, dynamic> e) => NewCategoryEntity.fromJson(e))
          .toList() as M;
    }
    if (<NewDetailEntity>[] is M) {
      return data
          .map<NewDetailEntity>(
              (Map<String, dynamic> e) => NewDetailEntity.fromJson(e))
          .toList() as M;
    }
    if (<NewDetailAnnexList>[] is M) {
      return data
          .map<NewDetailAnnexList>(
              (Map<String, dynamic> e) => NewDetailAnnexList.fromJson(e))
          .toList() as M;
    }
    if (<NewsOfSubsidyEntity>[] is M) {
      return data
          .map<NewsOfSubsidyEntity>(
              (Map<String, dynamic> e) => NewsOfSubsidyEntity.fromJson(e))
          .toList() as M;
    }
    if (<NewsOfSubsidyRecords>[] is M) {
      return data
          .map<NewsOfSubsidyRecords>(
              (Map<String, dynamic> e) => NewsOfSubsidyRecords.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyAgriculturalBuyEntity>[] is M) {
      return data
          .map<SubsidyAgriculturalBuyEntity>((Map<String, dynamic> e) =>
              SubsidyAgriculturalBuyEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyAgriculturalScrapEntity>[] is M) {
      return data
          .map<SubsidyAgriculturalScrapEntity>((Map<String, dynamic> e) =>
              SubsidyAgriculturalScrapEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyCropProductionEntity>[] is M) {
      return data
          .map<SubsidyCropProductionEntity>((Map<String, dynamic> e) =>
              SubsidyCropProductionEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyGrainFeedEntity>[] is M) {
      return data
          .map<SubsidyGrainFeedEntity>(
              (Map<String, dynamic> e) => SubsidyGrainFeedEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyProtectPloughEntity>[] is M) {
      return data
          .map<SubsidyProtectPloughEntity>((Map<String, dynamic> e) =>
              SubsidyProtectPloughEntity.fromJson(e))
          .toList() as M;
    }
    if (<PublicityResponseEntity>[] is M) {
      return data
          .map<PublicityResponseEntity>(
              (Map<String, dynamic> e) => PublicityResponseEntity.fromJson(e))
          .toList() as M;
    }
    if (<PublicityDetailItemDetails>[] is M) {
      return data
          .map<PublicityDetailItemDetails>((Map<String, dynamic> e) =>
              PublicityDetailItemDetails.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyApplyInfoEntity>[] is M) {
      return data
          .map<SubsidyApplyInfoEntity>(
              (Map<String, dynamic> e) => SubsidyApplyInfoEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyDetailItemEntity>[] is M) {
      return data
          .map<SubsidyDetailItemEntity>(
              (Map<String, dynamic> e) => SubsidyDetailItemEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyDetailItemDetails>[] is M) {
      return data
          .map<SubsidyDetailItemDetails>(
              (Map<String, dynamic> e) => SubsidyDetailItemDetails.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyFileItemEntity>[] is M) {
      return data
          .map<SubsidyFileItemEntity>(
              (Map<String, dynamic> e) => SubsidyFileItemEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyItemInfoEntity>[] is M) {
      return data
          .map<SubsidyItemInfoEntity>(
              (Map<String, dynamic> e) => SubsidyItemInfoEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyItemInfoRecords>[] is M) {
      return data
          .map<SubsidyItemInfoRecords>(
              (Map<String, dynamic> e) => SubsidyItemInfoRecords.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyLandInfoEntity>[] is M) {
      return data
          .map<SubsidyLandInfoEntity>(
              (Map<String, dynamic> e) => SubsidyLandInfoEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyOfMineEntity>[] is M) {
      return data
          .map<SubsidyOfMineEntity>(
              (Map<String, dynamic> e) => SubsidyOfMineEntity.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyOfMineDetails>[] is M) {
      return data
          .map<SubsidyOfMineDetails>(
              (Map<String, dynamic> e) => SubsidyOfMineDetails.fromJson(e))
          .toList() as M;
    }
    if (<SubsidyPublicityEntity>[] is M) {
      return data
          .map<SubsidyPublicityEntity>(
              (Map<String, dynamic> e) => SubsidyPublicityEntity.fromJson(e))
          .toList() as M;
    }
    if (<TaskToDoEntity>[] is M) {
      return data
          .map<TaskToDoEntity>(
              (Map<String, dynamic> e) => TaskToDoEntity.fromJson(e))
          .toList() as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(
          json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (DeepOperationEntity).toString(): DeepOperationEntity.fromJson,
    (DeepOperationRecord).toString(): DeepOperationRecord.fromJson,
    (DistrictEntity).toString(): DistrictEntity.fromJson,
    (FreeSowEntity).toString(): FreeSowEntity.fromJson,
    (FreeSowResultList).toString(): FreeSowResultList.fromJson,
    (InformationDetailEntity).toString(): InformationDetailEntity.fromJson,
    (InformationEntity).toString(): InformationEntity.fromJson,
    (POIEntity).toString(): POIEntity.fromJson,
    (PurchaseSubsidyEntity).toString(): PurchaseSubsidyEntity.fromJson,
    (RegionEntity).toString(): RegionEntity.fromJson,
    (ChatMessageEntity).toString(): ChatMessageEntity.fromJson,
    (HistoryMessageEntity).toString(): HistoryMessageEntity.fromJson,
    (HistoryMessageInputs).toString(): HistoryMessageInputs.fromJson,
    (HistoryMessageRetrieverResources).toString():
        HistoryMessageRetrieverResources.fromJson,
    (QuestionDictionaryEntity).toString(): QuestionDictionaryEntity.fromJson,
    (QuestionRecordEntity).toString(): QuestionRecordEntity.fromJson,
    (QuestionRecordInputs).toString(): QuestionRecordInputs.fromJson,
    (InsureBlackLandEntity).toString(): InsureBlackLandEntity.fromJson,
    (InsureConfirmDetailEntity).toString(): InsureConfirmDetailEntity.fromJson,
    (InsureConfireDetailBindPlotInfos).toString():
        InsureConfireDetailBindPlotInfos.fromJson,
    (InsureContractInfoEntity).toString(): InsureContractInfoEntity.fromJson,
    (InsureContractInfoContract).toString():
        InsureContractInfoContract.fromJson,
    (InsureContractInfoFarmer).toString(): InsureContractInfoFarmer.fromJson,
    (InsureContractInfoFarmerFarmer).toString():
        InsureContractInfoFarmerFarmer.fromJson,
    (InsureContractInfoPlotList).toString():
        InsureContractInfoPlotList.fromJson,
    (InsureContractInfoBankcard).toString():
        InsureContractInfoBankcard.fromJson,
    (InsureContractItemEntity).toString(): InsureContractItemEntity.fromJson,
    (InsureContractItemContractSource).toString():
        InsureContractItemContractSource.fromJson,
    (MyFieldChatMessageEntity).toString(): MyFieldChatMessageEntity.fromJson,
    (MyFieldHistoryMessageEntity).toString():
        MyFieldHistoryMessageEntity.fromJson,
    (MyFieldHistoryMessageInputs).toString():
        MyFieldHistoryMessageInputs.fromJson,
    (MyFieldHistoryMessageRetrieverResources).toString():
        MyFieldHistoryMessageRetrieverResources.fromJson,
    (MyFieldQuestionDictionaryEntity).toString():
        MyFieldQuestionDictionaryEntity.fromJson,
    (MyFieldQuestionRecordEntity).toString():
        MyFieldQuestionRecordEntity.fromJson,
    (MyFieldQuestionRecordInputs).toString():
        MyFieldQuestionRecordInputs.fromJson,
    (NewCategoryEntity).toString(): NewCategoryEntity.fromJson,
    (NewDetailEntity).toString(): NewDetailEntity.fromJson,
    (NewDetailAnnexList).toString(): NewDetailAnnexList.fromJson,
    (NewsOfSubsidyEntity).toString(): NewsOfSubsidyEntity.fromJson,
    (NewsOfSubsidyRecords).toString(): NewsOfSubsidyRecords.fromJson,
    (SubsidyAgriculturalBuyEntity).toString():
        SubsidyAgriculturalBuyEntity.fromJson,
    (SubsidyAgriculturalScrapEntity).toString():
        SubsidyAgriculturalScrapEntity.fromJson,
    (SubsidyCropProductionEntity).toString():
        SubsidyCropProductionEntity.fromJson,
    (SubsidyGrainFeedEntity).toString(): SubsidyGrainFeedEntity.fromJson,
    (SubsidyProtectPloughEntity).toString():
        SubsidyProtectPloughEntity.fromJson,
    (PublicityResponseEntity).toString(): PublicityResponseEntity.fromJson,
    (PublicityDetailItemDetails).toString():
        PublicityDetailItemDetails.fromJson,
    (SubsidyApplyInfoEntity).toString(): SubsidyApplyInfoEntity.fromJson,
    (SubsidyDetailItemEntity).toString(): SubsidyDetailItemEntity.fromJson,
    (SubsidyDetailItemDetails).toString(): SubsidyDetailItemDetails.fromJson,
    (SubsidyFileItemEntity).toString(): SubsidyFileItemEntity.fromJson,
    (SubsidyItemInfoEntity).toString(): SubsidyItemInfoEntity.fromJson,
    (SubsidyItemInfoRecords).toString(): SubsidyItemInfoRecords.fromJson,
    (SubsidyLandInfoEntity).toString(): SubsidyLandInfoEntity.fromJson,
    (SubsidyOfMineEntity).toString(): SubsidyOfMineEntity.fromJson,
    (SubsidyOfMineDetails).toString(): SubsidyOfMineDetails.fromJson,
    (SubsidyPublicityEntity).toString(): SubsidyPublicityEntity.fromJson,
    (TaskToDoEntity).toString(): TaskToDoEntity.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}
