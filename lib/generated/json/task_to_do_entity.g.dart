import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/worktable/entity/task_to_do_entity.dart';

TaskToDoEntity $TaskToDoEntityFromJson(Map<String, dynamic> json) {
  final TaskToDoEntity taskToDoEntity = TaskToDoEntity();
  final String? receiveTime = jsonConvert.convert<String>(json['receiveTime']);
  if (receiveTime != null) {
    taskToDoEntity.receiveTime = receiveTime;
  }
  final String? senderName = jsonConvert.convert<String>(json['senderName']);
  if (senderName != null) {
    taskToDoEntity.senderName = senderName;
  }
  final int? readState = jsonConvert.convert<int>(json['readState']);
  if (readState != null) {
    taskToDoEntity.readState = readState;
  }
  final String? subject = jsonConvert.convert<String>(json['subject']);
  if (subject != null) {
    taskToDoEntity.subject = subject;
  }
  final String? edocMark = jsonConvert.convert<String>(json['edocMark']);
  if (edocMark != null) {
    taskToDoEntity.edocMark = edocMark;
  }
  final int? importantLevel = jsonConvert.convert<int>(json['importantLevel']);
  if (importantLevel != null) {
    taskToDoEntity.importantLevel = importantLevel;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    taskToDoEntity.type = type;
  }
  final String? urlLink = jsonConvert.convert<String>(json['urlLink']);
  if (urlLink != null) {
    taskToDoEntity.urlLink = urlLink;
  }
  final String? createDate = jsonConvert.convert<String>(json['createDate']);
  if (createDate != null) {
    taskToDoEntity.createDate = createDate;
  }
  return taskToDoEntity;
}

Map<String, dynamic> $TaskToDoEntityToJson(TaskToDoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['receiveTime'] = entity.receiveTime;
  data['senderName'] = entity.senderName;
  data['readState'] = entity.readState;
  data['subject'] = entity.subject;
  data['edocMark'] = entity.edocMark;
  data['importantLevel'] = entity.importantLevel;
  data['type'] = entity.type;
  data['urlLink'] = entity.urlLink;
  data['createDate'] = entity.createDate;
  return data;
}

extension TaskToDoEntityExtension on TaskToDoEntity {
  TaskToDoEntity copyWith({
    String? receiveTime,
    String? senderName,
    int? readState,
    String? subject,
    String? edocMark,
    int? importantLevel,
    int? type,
    String? urlLink,
    String? createDate,
  }) {
    return TaskToDoEntity()
      ..receiveTime = receiveTime ?? this.receiveTime
      ..senderName = senderName ?? this.senderName
      ..readState = readState ?? this.readState
      ..subject = subject ?? this.subject
      ..edocMark = edocMark ?? this.edocMark
      ..importantLevel = importantLevel ?? this.importantLevel
      ..type = type ?? this.type
      ..urlLink = urlLink ?? this.urlLink
      ..createDate = createDate ?? this.createDate;
  }
}
