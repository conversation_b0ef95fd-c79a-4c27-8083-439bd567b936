import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/deep_operation_entity.dart';

DeepOperationEntity $DeepOperationEntityFromJson(Map<String, dynamic> json) {
  final DeepOperationEntity deepOperationEntity = DeepOperationEntity();
  final List<DeepOperationRecord>? subsoilingSupLst = (json['subsoilingSupLst']
          as List<dynamic>?)
      ?.map((e) =>
          jsonConvert.convert<DeepOperationRecord>(e) as DeepOperationRecord)
      .toList();
  if (subsoilingSupLst != null) {
    deepOperationEntity.subsoilingSupLst = subsoilingSupLst;
  }
  final double? totalEligibilityArea =
      jsonConvert.convert<double>(json['totalEligibilityArea']);
  if (totalEligibilityArea != null) {
    deepOperationEntity.totalEligibilityArea = totalEligibilityArea;
  }
  final double? workArea = jsonConvert.convert<double>(json['workArea']);
  if (workArea != null) {
    deepOperationEntity.workArea = workArea;
  }
  final double? deepEligibilityArea =
      jsonConvert.convert<double>(json['deepEligibilityArea']);
  if (deepEligibilityArea != null) {
    deepOperationEntity.deepEligibilityArea = deepEligibilityArea;
  }
  final double? recoverArea = jsonConvert.convert<double>(json['recoverArea']);
  if (recoverArea != null) {
    deepOperationEntity.recoverArea = recoverArea;
  }
  return deepOperationEntity;
}

Map<String, dynamic> $DeepOperationEntityToJson(DeepOperationEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsoilingSupLst'] =
      entity.subsoilingSupLst?.map((v) => v.toJson()).toList();
  data['totalEligibilityArea'] = entity.totalEligibilityArea;
  data['workArea'] = entity.workArea;
  data['deepEligibilityArea'] = entity.deepEligibilityArea;
  data['recoverArea'] = entity.recoverArea;
  return data;
}

extension DeepOperationEntityExtension on DeepOperationEntity {
  DeepOperationEntity copyWith({
    List<DeepOperationRecord>? subsoilingSupLst,
    double? totalEligibilityArea,
    double? workArea,
    double? deepEligibilityArea,
    double? recoverArea,
  }) {
    return DeepOperationEntity()
      ..subsoilingSupLst = subsoilingSupLst ?? this.subsoilingSupLst
      ..totalEligibilityArea = totalEligibilityArea ?? this.totalEligibilityArea
      ..workArea = workArea ?? this.workArea
      ..deepEligibilityArea = deepEligibilityArea ?? this.deepEligibilityArea
      ..recoverArea = recoverArea ?? this.recoverArea;
  }
}

DeepOperationRecord $DeepOperationRecordFromJson(Map<String, dynamic> json) {
  final DeepOperationRecord deepOperationRecord = DeepOperationRecord();
  final dynamic serialNumber = json['serialNumber'];
  if (serialNumber != null) {
    deepOperationRecord.serialNumber = serialNumber;
  }
  final String? workId = jsonConvert.convert<String>(json['workId']);
  if (workId != null) {
    deepOperationRecord.workId = workId;
  }
  final String? statYear = jsonConvert.convert<String>(json['statYear']);
  if (statYear != null) {
    deepOperationRecord.statYear = statYear;
  }
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    deepOperationRecord.uuid = uuid;
  }
  final dynamic endAddress = json['endAddress'];
  if (endAddress != null) {
    deepOperationRecord.endAddress = endAddress;
  }
  final String? workStart = jsonConvert.convert<String>(json['workStart']);
  if (workStart != null) {
    deepOperationRecord.workStart = workStart;
  }
  final String? workEnd = jsonConvert.convert<String>(json['workEnd']);
  if (workEnd != null) {
    deepOperationRecord.workEnd = workEnd;
  }
  final double? slat = jsonConvert.convert<double>(json['slat']);
  if (slat != null) {
    deepOperationRecord.slat = slat;
  }
  final double? slon = jsonConvert.convert<double>(json['slon']);
  if (slon != null) {
    deepOperationRecord.slon = slon;
  }
  final double? elat = jsonConvert.convert<double>(json['elat']);
  if (elat != null) {
    deepOperationRecord.elat = elat;
  }
  final double? elon = jsonConvert.convert<double>(json['elon']);
  if (elon != null) {
    deepOperationRecord.elon = elon;
  }
  final double? wid = jsonConvert.convert<double>(json['wid']);
  if (wid != null) {
    deepOperationRecord.wid = wid;
  }
  final String? field = jsonConvert.convert<String>(json['field']);
  if (field != null) {
    deepOperationRecord.field = field;
  }
  final String? wtype = jsonConvert.convert<String>(json['wtype']);
  if (wtype != null) {
    deepOperationRecord.wtype = wtype;
  }
  final double? t = jsonConvert.convert<double>(json['t']);
  if (t != null) {
    deepOperationRecord.t = t;
  }
  final double? len = jsonConvert.convert<double>(json['len']);
  if (len != null) {
    deepOperationRecord.len = len;
  }
  final String? crop = jsonConvert.convert<String>(json['crop']);
  if (crop != null) {
    deepOperationRecord.crop = crop;
  }
  final String? wdType = jsonConvert.convert<String>(json['wdType']);
  if (wdType != null) {
    deepOperationRecord.wdType = wdType;
  }
  final String? workname = jsonConvert.convert<String>(json['workname']);
  if (workname != null) {
    deepOperationRecord.workname = workname;
  }
  final double? high = jsonConvert.convert<double>(json['high']);
  if (high != null) {
    deepOperationRecord.high = high;
  }
  final double? dep = jsonConvert.convert<double>(json['dep']);
  if (dep != null) {
    deepOperationRecord.dep = dep;
  }
  final dynamic subsidiesLevel = json['subsidiesLevel'];
  if (subsidiesLevel != null) {
    deepOperationRecord.subsidiesLevel = subsidiesLevel;
  }
  final dynamic subsidiesStatus = json['subsidiesStatus'];
  if (subsidiesStatus != null) {
    deepOperationRecord.subsidiesStatus = subsidiesStatus;
  }
  final int? statusCd = jsonConvert.convert<int>(json['statusCd']);
  if (statusCd != null) {
    deepOperationRecord.statusCd = statusCd;
  }
  final String? dataStatus = jsonConvert.convert<String>(json['dataStatus']);
  if (dataStatus != null) {
    deepOperationRecord.dataStatus = dataStatus;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    deepOperationRecord.createTime = createTime;
  }
  final dynamic createBy = json['createBy'];
  if (createBy != null) {
    deepOperationRecord.createBy = createBy;
  }
  final int? updateTime = jsonConvert.convert<int>(json['updateTime']);
  if (updateTime != null) {
    deepOperationRecord.updateTime = updateTime;
  }
  final dynamic updateBy = json['updateBy'];
  if (updateBy != null) {
    deepOperationRecord.updateBy = updateBy;
  }
  final String? facCode = jsonConvert.convert<String>(json['facCode']);
  if (facCode != null) {
    deepOperationRecord.facCode = facCode;
  }
  final String? workDate = jsonConvert.convert<String>(json['workDate']);
  if (workDate != null) {
    deepOperationRecord.workDate = workDate;
  }
  final double? area = jsonConvert.convert<double>(json['area']);
  if (area != null) {
    deepOperationRecord.area = area;
  }
  final double? qarea = jsonConvert.convert<double>(json['qarea']);
  if (qarea != null) {
    deepOperationRecord.qarea = qarea;
  }
  final double? depQualify = jsonConvert.convert<double>(json['depQualify']);
  if (depQualify != null) {
    deepOperationRecord.depQualify = depQualify;
  }
  final double? repeatArea = jsonConvert.convert<double>(json['repeatArea']);
  if (repeatArea != null) {
    deepOperationRecord.repeatArea = repeatArea;
  }
  final String? orgName = jsonConvert.convert<String>(json['orgName']);
  if (orgName != null) {
    deepOperationRecord.orgName = orgName;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    deepOperationRecord.name = name;
  }
  final dynamic tel = json['tel'];
  if (tel != null) {
    deepOperationRecord.tel = tel;
  }
  final String? dsn = jsonConvert.convert<String>(json['dsn']);
  if (dsn != null) {
    deepOperationRecord.dsn = dsn;
  }
  final String? facName = jsonConvert.convert<String>(json['facName']);
  if (facName != null) {
    deepOperationRecord.facName = facName;
  }
  final String? machineOrgName =
      jsonConvert.convert<String>(json['machineOrgName']);
  if (machineOrgName != null) {
    deepOperationRecord.machineOrgName = machineOrgName;
  }
  final String? machineNo = jsonConvert.convert<String>(json['machineNo']);
  if (machineNo != null) {
    deepOperationRecord.machineNo = machineNo;
  }
  final dynamic engineNo = json['engineNo'];
  if (engineNo != null) {
    deepOperationRecord.engineNo = engineNo;
  }
  final String? companyName = jsonConvert.convert<String>(json['companyName']);
  if (companyName != null) {
    deepOperationRecord.companyName = companyName;
  }
  final String? licenseNo = jsonConvert.convert<String>(json['licenseNo']);
  if (licenseNo != null) {
    deepOperationRecord.licenseNo = licenseNo;
  }
  final String? amTypeName3 = jsonConvert.convert<String>(json['amTypeName3']);
  if (amTypeName3 != null) {
    deepOperationRecord.amTypeName3 = amTypeName3;
  }
  final String? amOwnerName = jsonConvert.convert<String>(json['amOwnerName']);
  if (amOwnerName != null) {
    deepOperationRecord.amOwnerName = amOwnerName;
  }
  final String? contactNum = jsonConvert.convert<String>(json['contactNum']);
  if (contactNum != null) {
    deepOperationRecord.contactNum = contactNum;
  }
  final dynamic params = json['params'];
  if (params != null) {
    deepOperationRecord.params = params;
  }
  final String? certNo = jsonConvert.convert<String>(json['certNo']);
  if (certNo != null) {
    deepOperationRecord.certNo = certNo;
  }
  final String? amTypeCode1 = jsonConvert.convert<String>(json['amTypeCode1']);
  if (amTypeCode1 != null) {
    deepOperationRecord.amTypeCode1 = amTypeCode1;
  }
  final String? amTypeName1 = jsonConvert.convert<String>(json['amTypeName1']);
  if (amTypeName1 != null) {
    deepOperationRecord.amTypeName1 = amTypeName1;
  }
  final String? amTypeCode2 = jsonConvert.convert<String>(json['amTypeCode2']);
  if (amTypeCode2 != null) {
    deepOperationRecord.amTypeCode2 = amTypeCode2;
  }
  final String? amTypeName2 = jsonConvert.convert<String>(json['amTypeName2']);
  if (amTypeName2 != null) {
    deepOperationRecord.amTypeName2 = amTypeName2;
  }
  final String? amTypeCode3 = jsonConvert.convert<String>(json['amTypeCode3']);
  if (amTypeCode3 != null) {
    deepOperationRecord.amTypeCode3 = amTypeCode3;
  }
  return deepOperationRecord;
}

Map<String, dynamic> $DeepOperationRecordToJson(DeepOperationRecord entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['serialNumber'] = entity.serialNumber;
  data['workId'] = entity.workId;
  data['statYear'] = entity.statYear;
  data['uuid'] = entity.uuid;
  data['endAddress'] = entity.endAddress;
  data['workStart'] = entity.workStart;
  data['workEnd'] = entity.workEnd;
  data['slat'] = entity.slat;
  data['slon'] = entity.slon;
  data['elat'] = entity.elat;
  data['elon'] = entity.elon;
  data['wid'] = entity.wid;
  data['field'] = entity.field;
  data['wtype'] = entity.wtype;
  data['t'] = entity.t;
  data['len'] = entity.len;
  data['crop'] = entity.crop;
  data['wdType'] = entity.wdType;
  data['workname'] = entity.workname;
  data['high'] = entity.high;
  data['dep'] = entity.dep;
  data['subsidiesLevel'] = entity.subsidiesLevel;
  data['subsidiesStatus'] = entity.subsidiesStatus;
  data['statusCd'] = entity.statusCd;
  data['dataStatus'] = entity.dataStatus;
  data['createTime'] = entity.createTime;
  data['createBy'] = entity.createBy;
  data['updateTime'] = entity.updateTime;
  data['updateBy'] = entity.updateBy;
  data['facCode'] = entity.facCode;
  data['workDate'] = entity.workDate;
  data['area'] = entity.area;
  data['qarea'] = entity.qarea;
  data['depQualify'] = entity.depQualify;
  data['repeatArea'] = entity.repeatArea;
  data['orgName'] = entity.orgName;
  data['name'] = entity.name;
  data['tel'] = entity.tel;
  data['dsn'] = entity.dsn;
  data['facName'] = entity.facName;
  data['machineOrgName'] = entity.machineOrgName;
  data['machineNo'] = entity.machineNo;
  data['engineNo'] = entity.engineNo;
  data['companyName'] = entity.companyName;
  data['licenseNo'] = entity.licenseNo;
  data['amTypeName3'] = entity.amTypeName3;
  data['amOwnerName'] = entity.amOwnerName;
  data['contactNum'] = entity.contactNum;
  data['params'] = entity.params;
  data['certNo'] = entity.certNo;
  data['amTypeCode1'] = entity.amTypeCode1;
  data['amTypeName1'] = entity.amTypeName1;
  data['amTypeCode2'] = entity.amTypeCode2;
  data['amTypeName2'] = entity.amTypeName2;
  data['amTypeCode3'] = entity.amTypeCode3;
  return data;
}

extension DeepOperationRecordExtension on DeepOperationRecord {
  DeepOperationRecord copyWith({
    dynamic serialNumber,
    String? workId,
    String? statYear,
    String? uuid,
    dynamic endAddress,
    String? workStart,
    String? workEnd,
    double? slat,
    double? slon,
    double? elat,
    double? elon,
    double? wid,
    String? field,
    String? wtype,
    double? t,
    double? len,
    String? crop,
    String? wdType,
    String? workname,
    double? high,
    double? dep,
    dynamic subsidiesLevel,
    dynamic subsidiesStatus,
    int? statusCd,
    String? dataStatus,
    int? createTime,
    dynamic createBy,
    int? updateTime,
    dynamic updateBy,
    String? facCode,
    String? workDate,
    double? area,
    double? qarea,
    double? depQualify,
    double? repeatArea,
    String? orgName,
    String? name,
    dynamic tel,
    String? dsn,
    String? facName,
    String? machineOrgName,
    String? machineNo,
    dynamic engineNo,
    String? companyName,
    String? licenseNo,
    String? amTypeName3,
    String? amOwnerName,
    String? contactNum,
    dynamic params,
    String? certNo,
    String? amTypeCode1,
    String? amTypeName1,
    String? amTypeCode2,
    String? amTypeName2,
    String? amTypeCode3,
  }) {
    return DeepOperationRecord()
      ..serialNumber = serialNumber ?? this.serialNumber
      ..workId = workId ?? this.workId
      ..statYear = statYear ?? this.statYear
      ..uuid = uuid ?? this.uuid
      ..endAddress = endAddress ?? this.endAddress
      ..workStart = workStart ?? this.workStart
      ..workEnd = workEnd ?? this.workEnd
      ..slat = slat ?? this.slat
      ..slon = slon ?? this.slon
      ..elat = elat ?? this.elat
      ..elon = elon ?? this.elon
      ..wid = wid ?? this.wid
      ..field = field ?? this.field
      ..wtype = wtype ?? this.wtype
      ..t = t ?? this.t
      ..len = len ?? this.len
      ..crop = crop ?? this.crop
      ..wdType = wdType ?? this.wdType
      ..workname = workname ?? this.workname
      ..high = high ?? this.high
      ..dep = dep ?? this.dep
      ..subsidiesLevel = subsidiesLevel ?? this.subsidiesLevel
      ..subsidiesStatus = subsidiesStatus ?? this.subsidiesStatus
      ..statusCd = statusCd ?? this.statusCd
      ..dataStatus = dataStatus ?? this.dataStatus
      ..createTime = createTime ?? this.createTime
      ..createBy = createBy ?? this.createBy
      ..updateTime = updateTime ?? this.updateTime
      ..updateBy = updateBy ?? this.updateBy
      ..facCode = facCode ?? this.facCode
      ..workDate = workDate ?? this.workDate
      ..area = area ?? this.area
      ..qarea = qarea ?? this.qarea
      ..depQualify = depQualify ?? this.depQualify
      ..repeatArea = repeatArea ?? this.repeatArea
      ..orgName = orgName ?? this.orgName
      ..name = name ?? this.name
      ..tel = tel ?? this.tel
      ..dsn = dsn ?? this.dsn
      ..facName = facName ?? this.facName
      ..machineOrgName = machineOrgName ?? this.machineOrgName
      ..machineNo = machineNo ?? this.machineNo
      ..engineNo = engineNo ?? this.engineNo
      ..companyName = companyName ?? this.companyName
      ..licenseNo = licenseNo ?? this.licenseNo
      ..amTypeName3 = amTypeName3 ?? this.amTypeName3
      ..amOwnerName = amOwnerName ?? this.amOwnerName
      ..contactNum = contactNum ?? this.contactNum
      ..params = params ?? this.params
      ..certNo = certNo ?? this.certNo
      ..amTypeCode1 = amTypeCode1 ?? this.amTypeCode1
      ..amTypeName1 = amTypeName1 ?? this.amTypeName1
      ..amTypeCode2 = amTypeCode2 ?? this.amTypeCode2
      ..amTypeName2 = amTypeName2 ?? this.amTypeName2
      ..amTypeCode3 = amTypeCode3 ?? this.amTypeCode3;
  }
}
