import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_contract_info_entity.dart';

InsureContractInfoEntity $InsureContractInfoEntityFromJson(
    Map<String, dynamic> json) {
  final InsureContractInfoEntity insureContractInfoEntity =
      InsureContractInfoEntity();
  final InsureContractInfoContract? contract =
      jsonConvert.convert<InsureContractInfoContract>(json['contract']);
  if (contract != null) {
    insureContractInfoEntity.contract = contract;
  }
  final InsureContractInfoFarmer? farmer =
      jsonConvert.convert<InsureContractInfoFarmer>(json['farmer']);
  if (farmer != null) {
    insureContractInfoEntity.farmer = farmer;
  }
  final List<InsureContractInfoPlotList>? plotList =
      (json['plotList'] as List<dynamic>?)
          ?.map((e) => jsonConvert.convert<InsureContractInfoPlotList>(e)
              as InsureContractInfoPlotList)
          .toList();
  if (plotList != null) {
    insureContractInfoEntity.plotList = plotList;
  }
  final List<InsureContractInfoBankcard>? bankcard =
      (json['bankcard'] as List<dynamic>?)
          ?.map((e) => jsonConvert.convert<InsureContractInfoBankcard>(e)
              as InsureContractInfoBankcard)
          .toList();
  if (bankcard != null) {
    insureContractInfoEntity.bankcard = bankcard;
  }
  return insureContractInfoEntity;
}

Map<String, dynamic> $InsureContractInfoEntityToJson(
    InsureContractInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['contract'] = entity.contract?.toJson();
  data['farmer'] = entity.farmer?.toJson();
  data['plotList'] = entity.plotList?.map((v) => v.toJson()).toList();
  data['bankcard'] = entity.bankcard?.map((v) => v.toJson()).toList();
  return data;
}

extension InsureContractInfoEntityExtension on InsureContractInfoEntity {
  InsureContractInfoEntity copyWith({
    InsureContractInfoContract? contract,
    InsureContractInfoFarmer? farmer,
    List<InsureContractInfoPlotList>? plotList,
    List<InsureContractInfoBankcard>? bankcard,
  }) {
    return InsureContractInfoEntity()
      ..contract = contract ?? this.contract
      ..farmer = farmer ?? this.farmer
      ..plotList = plotList ?? this.plotList
      ..bankcard = bankcard ?? this.bankcard;
  }
}

InsureContractInfoContract $InsureContractInfoContractFromJson(
    Map<String, dynamic> json) {
  final InsureContractInfoContract insureContractInfoContract =
      InsureContractInfoContract();
  final String? yearNo = jsonConvert.convert<String>(json['yearNo']);
  if (yearNo != null) {
    insureContractInfoContract.yearNo = yearNo;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    insureContractInfoContract.organizationName = organizationName;
  }
  final String? precinctName =
      jsonConvert.convert<String>(json['precinctName']);
  if (precinctName != null) {
    insureContractInfoContract.precinctName = precinctName;
  }
  final String? serialNumber =
      jsonConvert.convert<String>(json['serialNumber']);
  if (serialNumber != null) {
    insureContractInfoContract.serialNumber = serialNumber;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    insureContractInfoContract.farmerName = farmerName;
  }
  final String? farmerIdNumber =
      jsonConvert.convert<String>(json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    insureContractInfoContract.farmerIdNumber = farmerIdNumber;
  }
  final String? scaleArea = jsonConvert.convert<String>(json['scaleArea']);
  if (scaleArea != null) {
    insureContractInfoContract.scaleArea = scaleArea;
  }
  final String? chargeArea = jsonConvert.convert<String>(json['chargeArea']);
  if (chargeArea != null) {
    insureContractInfoContract.chargeArea = chargeArea;
  }
  final String? growArea = jsonConvert.convert<String>(json['growArea']);
  if (growArea != null) {
    insureContractInfoContract.growArea = growArea;
  }
  final String? totalFee = jsonConvert.convert<String>(json['totalFee']);
  if (totalFee != null) {
    insureContractInfoContract.totalFee = totalFee;
  }
  final String? printName = jsonConvert.convert<String>(json['printName']);
  if (printName != null) {
    insureContractInfoContract.printName = printName;
  }
  final String? printStatusNo =
      jsonConvert.convert<String>(json['printStatusNo']);
  if (printStatusNo != null) {
    insureContractInfoContract.printStatusNo = printStatusNo;
  }
  final String? contractSignDate =
      jsonConvert.convert<String>(json['contractSignDate']);
  if (contractSignDate != null) {
    insureContractInfoContract.contractSignDate = contractSignDate;
  }
  final String? printCount = jsonConvert.convert<String>(json['printCount']);
  if (printCount != null) {
    insureContractInfoContract.printCount = printCount;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    insureContractInfoContract.remark = remark;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    insureContractInfoContract.address = address;
  }
  final String? contractStartDate =
      jsonConvert.convert<String>(json['contractStartDate']);
  if (contractStartDate != null) {
    insureContractInfoContract.contractStartDate = contractStartDate;
  }
  final String? contractEndDate =
      jsonConvert.convert<String>(json['contractEndDate']);
  if (contractEndDate != null) {
    insureContractInfoContract.contractEndDate = contractEndDate;
  }
  final String? contractH5Url =
      jsonConvert.convert<String>(json['contractH5Url']);
  if (contractH5Url != null) {
    insureContractInfoContract.contractH5Url = contractH5Url;
  }
  final String? contractUrl = jsonConvert.convert<String>(json['contractUrl']);
  if (contractUrl != null) {
    insureContractInfoContract.contractUrl = contractUrl;
  }
  final String? managementAuditId =
      jsonConvert.convert<String>(json['managementAuditId']);
  if (managementAuditId != null) {
    insureContractInfoContract.managementAuditId = managementAuditId;
  }
  final String? managementAuditName =
      jsonConvert.convert<String>(json['managementAuditName']);
  if (managementAuditName != null) {
    insureContractInfoContract.managementAuditName = managementAuditName;
  }
  final String? managementAuditDate =
      jsonConvert.convert<String>(json['management_audit_date']);
  if (managementAuditDate != null) {
    insureContractInfoContract.managementAuditDate = managementAuditDate;
  }
  final String? farmName = jsonConvert.convert<String>(json['farmName']);
  if (farmName != null) {
    insureContractInfoContract.farmName = farmName;
  }
  final String? workstationName =
      jsonConvert.convert<String>(json['workstationName']);
  if (workstationName != null) {
    insureContractInfoContract.workstationName = workstationName;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    insureContractInfoContract.organizationNo = organizationNo;
  }
  final String? contractId = jsonConvert.convert<String>(json['contractId']);
  if (contractId != null) {
    insureContractInfoContract.contractId = contractId;
  }
  final String? filialeNo = jsonConvert.convert<String>(json['filialeNo']);
  if (filialeNo != null) {
    insureContractInfoContract.filialeNo = filialeNo;
  }
  final String? farmNo = jsonConvert.convert<String>(json['farmNo']);
  if (farmNo != null) {
    insureContractInfoContract.farmNo = farmNo;
  }
  final String? precinctNo = jsonConvert.convert<String>(json['precinctNo']);
  if (precinctNo != null) {
    insureContractInfoContract.precinctNo = precinctNo;
  }
  final String? workstationNo =
      jsonConvert.convert<String>(json['workstationNo']);
  if (workstationNo != null) {
    insureContractInfoContract.workstationNo = workstationNo;
  }
  final String? orderNumber = jsonConvert.convert<String>(json['orderNumber']);
  if (orderNumber != null) {
    insureContractInfoContract.orderNumber = orderNumber;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    insureContractInfoContract.farmerId = farmerId;
  }
  final String? farmerIdentityNo =
      jsonConvert.convert<String>(json['farmerIdentityNo']);
  if (farmerIdentityNo != null) {
    insureContractInfoContract.farmerIdentityNo = farmerIdentityNo;
  }
  final String? paymentStatusNo =
      jsonConvert.convert<String>(json['paymentStatusNo']);
  if (paymentStatusNo != null) {
    insureContractInfoContract.paymentStatusNo = paymentStatusNo;
  }
  final String? financeStatusNo =
      jsonConvert.convert<String>(json['financeStatusNo']);
  if (financeStatusNo != null) {
    insureContractInfoContract.financeStatusNo = financeStatusNo;
  }
  final String? contractPrintDate =
      jsonConvert.convert<String>(json['contractPrintDate']);
  if (contractPrintDate != null) {
    insureContractInfoContract.contractPrintDate = contractPrintDate;
  }
  final String? printUserId = jsonConvert.convert<String>(json['printUserId']);
  if (printUserId != null) {
    insureContractInfoContract.printUserId = printUserId;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    insureContractInfoContract.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    insureContractInfoContract.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    insureContractInfoContract.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    insureContractInfoContract.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    insureContractInfoContract.statusCd = statusCd;
  }
  final String? bidFee = jsonConvert.convert<String>(json['bidFee']);
  if (bidFee != null) {
    insureContractInfoContract.bidFee = bidFee;
  }
  final String? standardPrice =
      jsonConvert.convert<String>(json['standardPrice']);
  if (standardPrice != null) {
    insureContractInfoContract.standardPrice = standardPrice;
  }
  final String? bidPrice = jsonConvert.convert<String>(json['bidPrice']);
  if (bidPrice != null) {
    insureContractInfoContract.bidPrice = bidPrice;
  }
  final String? rentFee = jsonConvert.convert<String>(json['rentFee']);
  if (rentFee != null) {
    insureContractInfoContract.rentFee = rentFee;
  }
  final String? assuranceFee =
      jsonConvert.convert<String>(json['assuranceFee']);
  if (assuranceFee != null) {
    insureContractInfoContract.assuranceFee = assuranceFee;
  }
  final String? paymentMethodNo =
      jsonConvert.convert<String>(json['paymentMethodNo']);
  if (paymentMethodNo != null) {
    insureContractInfoContract.paymentMethodNo = paymentMethodNo;
  }
  final String? chargeTypeNo =
      jsonConvert.convert<String>(json['chargeTypeNo']);
  if (chargeTypeNo != null) {
    insureContractInfoContract.chargeTypeNo = chargeTypeNo;
  }
  final String? creatorName = jsonConvert.convert<String>(json['creatorName']);
  if (creatorName != null) {
    insureContractInfoContract.creatorName = creatorName;
  }
  final String? updaterName = jsonConvert.convert<String>(json['updaterName']);
  if (updaterName != null) {
    insureContractInfoContract.updaterName = updaterName;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    insureContractInfoContract.params = params;
  }
  final String? contractSignNo =
      jsonConvert.convert<String>(json['contractSignNo']);
  if (contractSignNo != null) {
    insureContractInfoContract.contractSignNo = contractSignNo;
  }
  final String? additionArea =
      jsonConvert.convert<String>(json['additionArea']);
  if (additionArea != null) {
    insureContractInfoContract.additionArea = additionArea;
  }
  final String? additionFee = jsonConvert.convert<String>(json['additionFee']);
  if (additionFee != null) {
    insureContractInfoContract.additionFee = additionFee;
  }
  final String? signPicUrl = jsonConvert.convert<String>(json['signPicUrl']);
  if (signPicUrl != null) {
    insureContractInfoContract.signPicUrl = signPicUrl;
  }
  final String? photoPath = jsonConvert.convert<String>(json['photoPath']);
  if (photoPath != null) {
    insureContractInfoContract.photoPath = photoPath;
  }
  final String? auditNo = jsonConvert.convert<String>(json['auditNo']);
  if (auditNo != null) {
    insureContractInfoContract.auditNo = auditNo;
  }
  final String? familyCount = jsonConvert.convert<String>(json['familyCount']);
  if (familyCount != null) {
    insureContractInfoContract.familyCount = familyCount;
  }
  final String? practitionerCount =
      jsonConvert.convert<String>(json['practitionerCount']);
  if (practitionerCount != null) {
    insureContractInfoContract.practitionerCount = practitionerCount;
  }
  final String? fddContractNo =
      jsonConvert.convert<String>(json['fddContractNo']);
  if (fddContractNo != null) {
    insureContractInfoContract.fddContractNo = fddContractNo;
  }
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    insureContractInfoContract.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    insureContractInfoContract.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    insureContractInfoContract.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    insureContractInfoContract.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    insureContractInfoContract.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    insureContractInfoContract.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    insureContractInfoContract.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    insureContractInfoContract.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    insureContractInfoContract.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    insureContractInfoContract.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    insureContractInfoContract.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    insureContractInfoContract.auditCTime = auditCTime;
  }
  final String? approvalStatusNo =
      jsonConvert.convert<String>(json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    insureContractInfoContract.approvalStatusNo = approvalStatusNo;
  }
  final String? approvalRemark =
      jsonConvert.convert<String>(json['approvalRemark']);
  if (approvalRemark != null) {
    insureContractInfoContract.approvalRemark = approvalRemark;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    insureContractInfoContract.auditDFlag = auditDFlag;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    insureContractInfoContract.auditDId = auditDId;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    insureContractInfoContract.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    insureContractInfoContract.auditDTime = auditDTime;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    insureContractInfoContract.auditEFlag = auditEFlag;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    insureContractInfoContract.auditEId = auditEId;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    insureContractInfoContract.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    insureContractInfoContract.auditETime = auditETime;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    insureContractInfoContract.auditFFlag = auditFFlag;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    insureContractInfoContract.auditFId = auditFId;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    insureContractInfoContract.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    insureContractInfoContract.auditFTime = auditFTime;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    insureContractInfoContract.auditGFlag = auditGFlag;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    insureContractInfoContract.auditGId = auditGId;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    insureContractInfoContract.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    insureContractInfoContract.auditGTime = auditGTime;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    insureContractInfoContract.auditHFlag = auditHFlag;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    insureContractInfoContract.auditHId = auditHId;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    insureContractInfoContract.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    insureContractInfoContract.auditHTime = auditHTime;
  }
  final String? currentAuditRoleId =
      jsonConvert.convert<String>(json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    insureContractInfoContract.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName =
      jsonConvert.convert<String>(json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    insureContractInfoContract.currentAuditRoleName = currentAuditRoleName;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    insureContractInfoContract.auditLevel = auditLevel;
  }
  final String? payedAmount = jsonConvert.convert<String>(json['payedAmount']);
  if (payedAmount != null) {
    insureContractInfoContract.payedAmount = payedAmount;
  }
  final String? listPlan = jsonConvert.convert<String>(json['listPlan']);
  if (listPlan != null) {
    insureContractInfoContract.listPlan = listPlan;
  }
  final String? ids = jsonConvert.convert<String>(json['ids']);
  if (ids != null) {
    insureContractInfoContract.ids = ids;
  }
  final String? contractTypeNo =
      jsonConvert.convert<String>(json['contractTypeNo']);
  if (contractTypeNo != null) {
    insureContractInfoContract.contractTypeNo = contractTypeNo;
  }
  final String? relationContractId =
      jsonConvert.convert<String>(json['relationContractId']);
  if (relationContractId != null) {
    insureContractInfoContract.relationContractId = relationContractId;
  }
  final String? relationSerialNumber =
      jsonConvert.convert<String>(json['relationSerialNumber']);
  if (relationSerialNumber != null) {
    insureContractInfoContract.relationSerialNumber = relationSerialNumber;
  }
  final String? contractType =
      jsonConvert.convert<String>(json['contractType']);
  if (contractType != null) {
    insureContractInfoContract.contractType = contractType;
  }
  final String? contractTemplateNo =
      jsonConvert.convert<String>(json['contractTemplateNo']);
  if (contractTemplateNo != null) {
    insureContractInfoContract.contractTemplateNo = contractTemplateNo;
  }
  final String? chargeEndDate =
      jsonConvert.convert<String>(json['chargeEndDate']);
  if (chargeEndDate != null) {
    insureContractInfoContract.chargeEndDate = chargeEndDate;
  }
  final String? penalty = jsonConvert.convert<String>(json['penalty']);
  if (penalty != null) {
    insureContractInfoContract.penalty = penalty;
  }
  final String? otherMatters =
      jsonConvert.convert<String>(json['otherMatters']);
  if (otherMatters != null) {
    insureContractInfoContract.otherMatters = otherMatters;
  }
  final String? retryTimes = jsonConvert.convert<String>(json['retryTimes']);
  if (retryTimes != null) {
    insureContractInfoContract.retryTimes = retryTimes;
  }
  final String? farmSignStatusNo =
      jsonConvert.convert<String>(json['farmSignStatusNo']);
  if (farmSignStatusNo != null) {
    insureContractInfoContract.farmSignStatusNo = farmSignStatusNo;
  }
  final String? farmSignCompleteTime =
      jsonConvert.convert<String>(json['farmSignCompleteTime']);
  if (farmSignCompleteTime != null) {
    insureContractInfoContract.farmSignCompleteTime = farmSignCompleteTime;
  }
  final String? farmSignStartTime =
      jsonConvert.convert<String>(json['farmSignStartTime']);
  if (farmSignStartTime != null) {
    insureContractInfoContract.farmSignStartTime = farmSignStartTime;
  }
  final String? farmSignMsg = jsonConvert.convert<String>(json['farmSignMsg']);
  if (farmSignMsg != null) {
    insureContractInfoContract.farmSignMsg = farmSignMsg;
  }
  final String? farmSignBy = jsonConvert.convert<String>(json['farmSignBy']);
  if (farmSignBy != null) {
    insureContractInfoContract.farmSignBy = farmSignBy;
  }
  final String? signVerifyMode =
      jsonConvert.convert<String>(json['signVerifyMode']);
  if (signVerifyMode != null) {
    insureContractInfoContract.signVerifyMode = signVerifyMode;
  }
  final String? contractTotalQuantity =
      jsonConvert.convert<String>(json['contractTotalQuantity']);
  if (contractTotalQuantity != null) {
    insureContractInfoContract.contractTotalQuantity = contractTotalQuantity;
  }
  final String? dissolutionStatus =
      jsonConvert.convert<String>(json['dissolutionStatus']);
  if (dissolutionStatus != null) {
    insureContractInfoContract.dissolutionStatus = dissolutionStatus;
  }
  final String? dissolutionStatusCh =
      jsonConvert.convert<String>(json['dissolutionStatusCh']);
  if (dissolutionStatusCh != null) {
    insureContractInfoContract.dissolutionStatusCh = dissolutionStatusCh;
  }
  final String? accountId = jsonConvert.convert<String>(json['accountId']);
  if (accountId != null) {
    insureContractInfoContract.accountId = accountId;
  }
  final String? signPersonType =
      jsonConvert.convert<String>(json['signPersonType']);
  if (signPersonType != null) {
    insureContractInfoContract.signPersonType = signPersonType;
  }
  final String? signPersonTypeApp =
      jsonConvert.convert<String>(json['signPersonTypeApp']);
  if (signPersonTypeApp != null) {
    insureContractInfoContract.signPersonTypeApp = signPersonTypeApp;
  }
  final String? memberInSignFlag =
      jsonConvert.convert<String>(json['memberInSignFlag']);
  if (memberInSignFlag != null) {
    insureContractInfoContract.memberInSignFlag = memberInSignFlag;
  }
  final String? guarantorSignFlag =
      jsonConvert.convert<String>(json['guarantorSignFlag']);
  if (guarantorSignFlag != null) {
    insureContractInfoContract.guarantorSignFlag = guarantorSignFlag;
  }
  final String? contractSignType =
      jsonConvert.convert<String>(json['contractSignType']);
  if (contractSignType != null) {
    insureContractInfoContract.contractSignType = contractSignType;
  }
  final String? partnerId = jsonConvert.convert<String>(json['partnerId']);
  if (partnerId != null) {
    insureContractInfoContract.partnerId = partnerId;
  }
  final String? partnerName = jsonConvert.convert<String>(json['partnerName']);
  if (partnerName != null) {
    insureContractInfoContract.partnerName = partnerName;
  }
  final String? partnerCode = jsonConvert.convert<String>(json['partnerCode']);
  if (partnerCode != null) {
    insureContractInfoContract.partnerCode = partnerCode;
  }
  final String? contractPeriod =
      jsonConvert.convert<String>(json['contractPeriod']);
  if (contractPeriod != null) {
    insureContractInfoContract.contractPeriod = contractPeriod;
  }
  final String? objectContractFeeDate =
      jsonConvert.convert<String>(json['objectContractFeeDate']);
  if (objectContractFeeDate != null) {
    insureContractInfoContract.objectContractFeeDate = objectContractFeeDate;
  }
  final String? objectContractFeePenalty =
      jsonConvert.convert<String>(json['objectContractFeePenalty']);
  if (objectContractFeePenalty != null) {
    insureContractInfoContract.objectContractFeePenalty =
        objectContractFeePenalty;
  }
  final String? ifVerifyLandArea =
      jsonConvert.convert<String>(json['ifVerifyLandArea']);
  if (ifVerifyLandArea != null) {
    insureContractInfoContract.ifVerifyLandArea = ifVerifyLandArea;
  }
  final String? lcAccessMode =
      jsonConvert.convert<String>(json['lcAccessMode']);
  if (lcAccessMode != null) {
    insureContractInfoContract.lcAccessMode = lcAccessMode;
  }
  final String? serialNumberLengthLimit =
      jsonConvert.convert<String>(json['serialNumberLengthLimit']);
  if (serialNumberLengthLimit != null) {
    insureContractInfoContract.serialNumberLengthLimit =
        serialNumberLengthLimit;
  }
  final String? serialNumberLength =
      jsonConvert.convert<String>(json['serialNumberLength']);
  if (serialNumberLength != null) {
    insureContractInfoContract.serialNumberLength = serialNumberLength;
  }
  final String? modifyPermit =
      jsonConvert.convert<String>(json['modifyPermit']);
  if (modifyPermit != null) {
    insureContractInfoContract.modifyPermit = modifyPermit;
  }
  final String? extMemberInSignFlag =
      jsonConvert.convert<String>(json['extMemberInSignFlag']);
  if (extMemberInSignFlag != null) {
    insureContractInfoContract.extMemberInSignFlag = extMemberInSignFlag;
  }
  final String? disMemberInSignFlag =
      jsonConvert.convert<String>(json['disMemberInSignFlag']);
  if (disMemberInSignFlag != null) {
    insureContractInfoContract.disMemberInSignFlag = disMemberInSignFlag;
  }
  final String? ifVerifyPersonAccess =
      jsonConvert.convert<String>(json['ifVerifyPersonAccess']);
  if (ifVerifyPersonAccess != null) {
    insureContractInfoContract.ifVerifyPersonAccess = ifVerifyPersonAccess;
  }
  final String? objectStandardLevel =
      jsonConvert.convert<String>(json['objectStandardLevel']);
  if (objectStandardLevel != null) {
    insureContractInfoContract.objectStandardLevel = objectStandardLevel;
  }
  final String? contractTemplateVersion =
      jsonConvert.convert<String>(json['contractTemplateVersion']);
  if (contractTemplateVersion != null) {
    insureContractInfoContract.contractTemplateVersion =
        contractTemplateVersion;
  }
  final String? chargeAreaSum =
      jsonConvert.convert<String>(json['chargeAreaSum']);
  if (chargeAreaSum != null) {
    insureContractInfoContract.chargeAreaSum = chargeAreaSum;
  }
  final String? totalFeeSum = jsonConvert.convert<String>(json['totalFeeSum']);
  if (totalFeeSum != null) {
    insureContractInfoContract.totalFeeSum = totalFeeSum;
  }
  final String? ifOaAuditFlag =
      jsonConvert.convert<String>(json['ifOaAuditFlag']);
  if (ifOaAuditFlag != null) {
    insureContractInfoContract.ifOaAuditFlag = ifOaAuditFlag;
  }
  final String? oaWfRecordId =
      jsonConvert.convert<String>(json['oaWfRecordId']);
  if (oaWfRecordId != null) {
    insureContractInfoContract.oaWfRecordId = oaWfRecordId;
  }
  final String? oaWfSummaryId =
      jsonConvert.convert<String>(json['oaWfSummaryId']);
  if (oaWfSummaryId != null) {
    insureContractInfoContract.oaWfSummaryId = oaWfSummaryId;
  }
  final String? oaAuditBatchNo =
      jsonConvert.convert<String>(json['oaAuditBatchNo']);
  if (oaAuditBatchNo != null) {
    insureContractInfoContract.oaAuditBatchNo = oaAuditBatchNo;
  }
  final String? oaApprovalStatusNo =
      jsonConvert.convert<String>(json['oaApprovalStatusNo']);
  if (oaApprovalStatusNo != null) {
    insureContractInfoContract.oaApprovalStatusNo = oaApprovalStatusNo;
  }
  final String? totalTieredRentFee =
      jsonConvert.convert<String>(json['totalTieredRentFee']);
  if (totalTieredRentFee != null) {
    insureContractInfoContract.totalTieredRentFee = totalTieredRentFee;
  }
  return insureContractInfoContract;
}

Map<String, dynamic> $InsureContractInfoContractToJson(
    InsureContractInfoContract entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['yearNo'] = entity.yearNo;
  data['organizationName'] = entity.organizationName;
  data['precinctName'] = entity.precinctName;
  data['serialNumber'] = entity.serialNumber;
  data['farmerName'] = entity.farmerName;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['scaleArea'] = entity.scaleArea;
  data['chargeArea'] = entity.chargeArea;
  data['growArea'] = entity.growArea;
  data['totalFee'] = entity.totalFee;
  data['printName'] = entity.printName;
  data['printStatusNo'] = entity.printStatusNo;
  data['contractSignDate'] = entity.contractSignDate;
  data['printCount'] = entity.printCount;
  data['remark'] = entity.remark;
  data['address'] = entity.address;
  data['contractStartDate'] = entity.contractStartDate;
  data['contractEndDate'] = entity.contractEndDate;
  data['contractH5Url'] = entity.contractH5Url;
  data['contractUrl'] = entity.contractUrl;
  data['managementAuditId'] = entity.managementAuditId;
  data['managementAuditName'] = entity.managementAuditName;
  data['management_audit_date'] = entity.managementAuditDate;
  data['farmName'] = entity.farmName;
  data['workstationName'] = entity.workstationName;
  data['organizationNo'] = entity.organizationNo;
  data['contractId'] = entity.contractId;
  data['filialeNo'] = entity.filialeNo;
  data['farmNo'] = entity.farmNo;
  data['precinctNo'] = entity.precinctNo;
  data['workstationNo'] = entity.workstationNo;
  data['orderNumber'] = entity.orderNumber;
  data['farmerId'] = entity.farmerId;
  data['farmerIdentityNo'] = entity.farmerIdentityNo;
  data['paymentStatusNo'] = entity.paymentStatusNo;
  data['financeStatusNo'] = entity.financeStatusNo;
  data['contractPrintDate'] = entity.contractPrintDate;
  data['printUserId'] = entity.printUserId;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['bidFee'] = entity.bidFee;
  data['standardPrice'] = entity.standardPrice;
  data['bidPrice'] = entity.bidPrice;
  data['rentFee'] = entity.rentFee;
  data['assuranceFee'] = entity.assuranceFee;
  data['paymentMethodNo'] = entity.paymentMethodNo;
  data['chargeTypeNo'] = entity.chargeTypeNo;
  data['creatorName'] = entity.creatorName;
  data['updaterName'] = entity.updaterName;
  data['params'] = entity.params;
  data['contractSignNo'] = entity.contractSignNo;
  data['additionArea'] = entity.additionArea;
  data['additionFee'] = entity.additionFee;
  data['signPicUrl'] = entity.signPicUrl;
  data['photoPath'] = entity.photoPath;
  data['auditNo'] = entity.auditNo;
  data['familyCount'] = entity.familyCount;
  data['practitionerCount'] = entity.practitionerCount;
  data['fddContractNo'] = entity.fddContractNo;
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['approvalRemark'] = entity.approvalRemark;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDId'] = entity.auditDId;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEId'] = entity.auditEId;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFId'] = entity.auditFId;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGId'] = entity.auditGId;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHId'] = entity.auditHId;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['auditLevel'] = entity.auditLevel;
  data['payedAmount'] = entity.payedAmount;
  data['listPlan'] = entity.listPlan;
  data['ids'] = entity.ids;
  data['contractTypeNo'] = entity.contractTypeNo;
  data['relationContractId'] = entity.relationContractId;
  data['relationSerialNumber'] = entity.relationSerialNumber;
  data['contractType'] = entity.contractType;
  data['contractTemplateNo'] = entity.contractTemplateNo;
  data['chargeEndDate'] = entity.chargeEndDate;
  data['penalty'] = entity.penalty;
  data['otherMatters'] = entity.otherMatters;
  data['retryTimes'] = entity.retryTimes;
  data['farmSignStatusNo'] = entity.farmSignStatusNo;
  data['farmSignCompleteTime'] = entity.farmSignCompleteTime;
  data['farmSignStartTime'] = entity.farmSignStartTime;
  data['farmSignMsg'] = entity.farmSignMsg;
  data['farmSignBy'] = entity.farmSignBy;
  data['signVerifyMode'] = entity.signVerifyMode;
  data['contractTotalQuantity'] = entity.contractTotalQuantity;
  data['dissolutionStatus'] = entity.dissolutionStatus;
  data['dissolutionStatusCh'] = entity.dissolutionStatusCh;
  data['accountId'] = entity.accountId;
  data['signPersonType'] = entity.signPersonType;
  data['signPersonTypeApp'] = entity.signPersonTypeApp;
  data['memberInSignFlag'] = entity.memberInSignFlag;
  data['guarantorSignFlag'] = entity.guarantorSignFlag;
  data['contractSignType'] = entity.contractSignType;
  data['partnerId'] = entity.partnerId;
  data['partnerName'] = entity.partnerName;
  data['partnerCode'] = entity.partnerCode;
  data['contractPeriod'] = entity.contractPeriod;
  data['objectContractFeeDate'] = entity.objectContractFeeDate;
  data['objectContractFeePenalty'] = entity.objectContractFeePenalty;
  data['ifVerifyLandArea'] = entity.ifVerifyLandArea;
  data['lcAccessMode'] = entity.lcAccessMode;
  data['serialNumberLengthLimit'] = entity.serialNumberLengthLimit;
  data['serialNumberLength'] = entity.serialNumberLength;
  data['modifyPermit'] = entity.modifyPermit;
  data['extMemberInSignFlag'] = entity.extMemberInSignFlag;
  data['disMemberInSignFlag'] = entity.disMemberInSignFlag;
  data['ifVerifyPersonAccess'] = entity.ifVerifyPersonAccess;
  data['objectStandardLevel'] = entity.objectStandardLevel;
  data['contractTemplateVersion'] = entity.contractTemplateVersion;
  data['chargeAreaSum'] = entity.chargeAreaSum;
  data['totalFeeSum'] = entity.totalFeeSum;
  data['ifOaAuditFlag'] = entity.ifOaAuditFlag;
  data['oaWfRecordId'] = entity.oaWfRecordId;
  data['oaWfSummaryId'] = entity.oaWfSummaryId;
  data['oaAuditBatchNo'] = entity.oaAuditBatchNo;
  data['oaApprovalStatusNo'] = entity.oaApprovalStatusNo;
  data['totalTieredRentFee'] = entity.totalTieredRentFee;
  return data;
}

extension InsureContractInfoContractExtension on InsureContractInfoContract {
  InsureContractInfoContract copyWith({
    String? yearNo,
    String? organizationName,
    String? precinctName,
    String? serialNumber,
    String? farmerName,
    String? farmerIdNumber,
    String? scaleArea,
    String? chargeArea,
    String? growArea,
    String? totalFee,
    String? printName,
    String? printStatusNo,
    String? contractSignDate,
    String? printCount,
    String? remark,
    String? address,
    String? contractStartDate,
    String? contractEndDate,
    String? contractH5Url,
    String? contractUrl,
    String? managementAuditId,
    String? managementAuditName,
    String? managementAuditDate,
    String? farmName,
    String? workstationName,
    String? organizationNo,
    String? contractId,
    String? filialeNo,
    String? farmNo,
    String? precinctNo,
    String? workstationNo,
    String? orderNumber,
    String? farmerId,
    String? farmerIdentityNo,
    String? paymentStatusNo,
    String? financeStatusNo,
    String? contractPrintDate,
    String? printUserId,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? bidFee,
    String? standardPrice,
    String? bidPrice,
    String? rentFee,
    String? assuranceFee,
    String? paymentMethodNo,
    String? chargeTypeNo,
    String? creatorName,
    String? updaterName,
    String? params,
    String? contractSignNo,
    String? additionArea,
    String? additionFee,
    String? signPicUrl,
    String? photoPath,
    String? auditNo,
    String? familyCount,
    String? practitionerCount,
    String? fddContractNo,
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? approvalStatusNo,
    String? approvalRemark,
    String? auditDFlag,
    String? auditDId,
    String? auditDName,
    String? auditDTime,
    String? auditEFlag,
    String? auditEId,
    String? auditEName,
    String? auditETime,
    String? auditFFlag,
    String? auditFId,
    String? auditFName,
    String? auditFTime,
    String? auditGFlag,
    String? auditGId,
    String? auditGName,
    String? auditGTime,
    String? auditHFlag,
    String? auditHId,
    String? auditHName,
    String? auditHTime,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? auditLevel,
    String? payedAmount,
    String? listPlan,
    String? ids,
    String? contractTypeNo,
    String? relationContractId,
    String? relationSerialNumber,
    String? contractType,
    String? contractTemplateNo,
    String? chargeEndDate,
    String? penalty,
    String? otherMatters,
    String? retryTimes,
    String? farmSignStatusNo,
    String? farmSignCompleteTime,
    String? farmSignStartTime,
    String? farmSignMsg,
    String? farmSignBy,
    String? signVerifyMode,
    String? contractTotalQuantity,
    String? dissolutionStatus,
    String? dissolutionStatusCh,
    String? accountId,
    String? signPersonType,
    String? signPersonTypeApp,
    String? memberInSignFlag,
    String? guarantorSignFlag,
    String? contractSignType,
    String? partnerId,
    String? partnerName,
    String? partnerCode,
    String? contractPeriod,
    String? objectContractFeeDate,
    String? objectContractFeePenalty,
    String? ifVerifyLandArea,
    String? lcAccessMode,
    String? serialNumberLengthLimit,
    String? serialNumberLength,
    String? modifyPermit,
    String? extMemberInSignFlag,
    String? disMemberInSignFlag,
    String? ifVerifyPersonAccess,
    String? objectStandardLevel,
    String? contractTemplateVersion,
    String? chargeAreaSum,
    String? totalFeeSum,
    String? ifOaAuditFlag,
    String? oaWfRecordId,
    String? oaWfSummaryId,
    String? oaAuditBatchNo,
    String? oaApprovalStatusNo,
    String? totalTieredRentFee,
  }) {
    return InsureContractInfoContract()
      ..yearNo = yearNo ?? this.yearNo
      ..organizationName = organizationName ?? this.organizationName
      ..precinctName = precinctName ?? this.precinctName
      ..serialNumber = serialNumber ?? this.serialNumber
      ..farmerName = farmerName ?? this.farmerName
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..scaleArea = scaleArea ?? this.scaleArea
      ..chargeArea = chargeArea ?? this.chargeArea
      ..growArea = growArea ?? this.growArea
      ..totalFee = totalFee ?? this.totalFee
      ..printName = printName ?? this.printName
      ..printStatusNo = printStatusNo ?? this.printStatusNo
      ..contractSignDate = contractSignDate ?? this.contractSignDate
      ..printCount = printCount ?? this.printCount
      ..remark = remark ?? this.remark
      ..address = address ?? this.address
      ..contractStartDate = contractStartDate ?? this.contractStartDate
      ..contractEndDate = contractEndDate ?? this.contractEndDate
      ..contractH5Url = contractH5Url ?? this.contractH5Url
      ..contractUrl = contractUrl ?? this.contractUrl
      ..managementAuditId = managementAuditId ?? this.managementAuditId
      ..managementAuditName = managementAuditName ?? this.managementAuditName
      ..managementAuditDate = managementAuditDate ?? this.managementAuditDate
      ..farmName = farmName ?? this.farmName
      ..workstationName = workstationName ?? this.workstationName
      ..organizationNo = organizationNo ?? this.organizationNo
      ..contractId = contractId ?? this.contractId
      ..filialeNo = filialeNo ?? this.filialeNo
      ..farmNo = farmNo ?? this.farmNo
      ..precinctNo = precinctNo ?? this.precinctNo
      ..workstationNo = workstationNo ?? this.workstationNo
      ..orderNumber = orderNumber ?? this.orderNumber
      ..farmerId = farmerId ?? this.farmerId
      ..farmerIdentityNo = farmerIdentityNo ?? this.farmerIdentityNo
      ..paymentStatusNo = paymentStatusNo ?? this.paymentStatusNo
      ..financeStatusNo = financeStatusNo ?? this.financeStatusNo
      ..contractPrintDate = contractPrintDate ?? this.contractPrintDate
      ..printUserId = printUserId ?? this.printUserId
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..bidFee = bidFee ?? this.bidFee
      ..standardPrice = standardPrice ?? this.standardPrice
      ..bidPrice = bidPrice ?? this.bidPrice
      ..rentFee = rentFee ?? this.rentFee
      ..assuranceFee = assuranceFee ?? this.assuranceFee
      ..paymentMethodNo = paymentMethodNo ?? this.paymentMethodNo
      ..chargeTypeNo = chargeTypeNo ?? this.chargeTypeNo
      ..creatorName = creatorName ?? this.creatorName
      ..updaterName = updaterName ?? this.updaterName
      ..params = params ?? this.params
      ..contractSignNo = contractSignNo ?? this.contractSignNo
      ..additionArea = additionArea ?? this.additionArea
      ..additionFee = additionFee ?? this.additionFee
      ..signPicUrl = signPicUrl ?? this.signPicUrl
      ..photoPath = photoPath ?? this.photoPath
      ..auditNo = auditNo ?? this.auditNo
      ..familyCount = familyCount ?? this.familyCount
      ..practitionerCount = practitionerCount ?? this.practitionerCount
      ..fddContractNo = fddContractNo ?? this.fddContractNo
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDId = auditDId ?? this.auditDId
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEId = auditEId ?? this.auditEId
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFId = auditFId ?? this.auditFId
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGId = auditGId ?? this.auditGId
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHId = auditHId ?? this.auditHId
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..auditLevel = auditLevel ?? this.auditLevel
      ..payedAmount = payedAmount ?? this.payedAmount
      ..listPlan = listPlan ?? this.listPlan
      ..ids = ids ?? this.ids
      ..contractTypeNo = contractTypeNo ?? this.contractTypeNo
      ..relationContractId = relationContractId ?? this.relationContractId
      ..relationSerialNumber = relationSerialNumber ?? this.relationSerialNumber
      ..contractType = contractType ?? this.contractType
      ..contractTemplateNo = contractTemplateNo ?? this.contractTemplateNo
      ..chargeEndDate = chargeEndDate ?? this.chargeEndDate
      ..penalty = penalty ?? this.penalty
      ..otherMatters = otherMatters ?? this.otherMatters
      ..retryTimes = retryTimes ?? this.retryTimes
      ..farmSignStatusNo = farmSignStatusNo ?? this.farmSignStatusNo
      ..farmSignCompleteTime = farmSignCompleteTime ?? this.farmSignCompleteTime
      ..farmSignStartTime = farmSignStartTime ?? this.farmSignStartTime
      ..farmSignMsg = farmSignMsg ?? this.farmSignMsg
      ..farmSignBy = farmSignBy ?? this.farmSignBy
      ..signVerifyMode = signVerifyMode ?? this.signVerifyMode
      ..contractTotalQuantity =
          contractTotalQuantity ?? this.contractTotalQuantity
      ..dissolutionStatus = dissolutionStatus ?? this.dissolutionStatus
      ..dissolutionStatusCh = dissolutionStatusCh ?? this.dissolutionStatusCh
      ..accountId = accountId ?? this.accountId
      ..signPersonType = signPersonType ?? this.signPersonType
      ..signPersonTypeApp = signPersonTypeApp ?? this.signPersonTypeApp
      ..memberInSignFlag = memberInSignFlag ?? this.memberInSignFlag
      ..guarantorSignFlag = guarantorSignFlag ?? this.guarantorSignFlag
      ..contractSignType = contractSignType ?? this.contractSignType
      ..partnerId = partnerId ?? this.partnerId
      ..partnerName = partnerName ?? this.partnerName
      ..partnerCode = partnerCode ?? this.partnerCode
      ..contractPeriod = contractPeriod ?? this.contractPeriod
      ..objectContractFeeDate =
          objectContractFeeDate ?? this.objectContractFeeDate
      ..objectContractFeePenalty =
          objectContractFeePenalty ?? this.objectContractFeePenalty
      ..ifVerifyLandArea = ifVerifyLandArea ?? this.ifVerifyLandArea
      ..lcAccessMode = lcAccessMode ?? this.lcAccessMode
      ..serialNumberLengthLimit =
          serialNumberLengthLimit ?? this.serialNumberLengthLimit
      ..serialNumberLength = serialNumberLength ?? this.serialNumberLength
      ..modifyPermit = modifyPermit ?? this.modifyPermit
      ..extMemberInSignFlag = extMemberInSignFlag ?? this.extMemberInSignFlag
      ..disMemberInSignFlag = disMemberInSignFlag ?? this.disMemberInSignFlag
      ..ifVerifyPersonAccess = ifVerifyPersonAccess ?? this.ifVerifyPersonAccess
      ..objectStandardLevel = objectStandardLevel ?? this.objectStandardLevel
      ..contractTemplateVersion =
          contractTemplateVersion ?? this.contractTemplateVersion
      ..chargeAreaSum = chargeAreaSum ?? this.chargeAreaSum
      ..totalFeeSum = totalFeeSum ?? this.totalFeeSum
      ..ifOaAuditFlag = ifOaAuditFlag ?? this.ifOaAuditFlag
      ..oaWfRecordId = oaWfRecordId ?? this.oaWfRecordId
      ..oaWfSummaryId = oaWfSummaryId ?? this.oaWfSummaryId
      ..oaAuditBatchNo = oaAuditBatchNo ?? this.oaAuditBatchNo
      ..oaApprovalStatusNo = oaApprovalStatusNo ?? this.oaApprovalStatusNo
      ..totalTieredRentFee = totalTieredRentFee ?? this.totalTieredRentFee;
  }
}

InsureContractInfoFarmer $InsureContractInfoFarmerFromJson(
    Map<String, dynamic> json) {
  final InsureContractInfoFarmer insureContractInfoFarmer =
      InsureContractInfoFarmer();
  final InsureContractInfoFarmerFarmer? farmer =
      jsonConvert.convert<InsureContractInfoFarmerFarmer>(json['farmer']);
  if (farmer != null) {
    insureContractInfoFarmer.farmer = farmer;
  }
  final String? idNumber = jsonConvert.convert<String>(json['idNumber']);
  if (idNumber != null) {
    insureContractInfoFarmer.idNumber = idNumber;
  }
  return insureContractInfoFarmer;
}

Map<String, dynamic> $InsureContractInfoFarmerToJson(
    InsureContractInfoFarmer entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['farmer'] = entity.farmer?.toJson();
  data['idNumber'] = entity.idNumber;
  return data;
}

extension InsureContractInfoFarmerExtension on InsureContractInfoFarmer {
  InsureContractInfoFarmer copyWith({
    InsureContractInfoFarmerFarmer? farmer,
    String? idNumber,
  }) {
    return InsureContractInfoFarmer()
      ..farmer = farmer ?? this.farmer
      ..idNumber = idNumber ?? this.idNumber;
  }
}

InsureContractInfoFarmerFarmer $InsureContractInfoFarmerFarmerFromJson(
    Map<String, dynamic> json) {
  final InsureContractInfoFarmerFarmer insureContractInfoFarmerFarmer =
      InsureContractInfoFarmerFarmer();
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    insureContractInfoFarmerFarmer.organizationNo = organizationNo;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    insureContractInfoFarmerFarmer.organizationName = organizationName;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    insureContractInfoFarmerFarmer.name = name;
  }
  final String? idNumber = jsonConvert.convert<String>(json['idNumber']);
  if (idNumber != null) {
    insureContractInfoFarmerFarmer.idNumber = idNumber;
  }
  final String? farmerIdentityNo =
      jsonConvert.convert<String>(json['farmerIdentityNo']);
  if (farmerIdentityNo != null) {
    insureContractInfoFarmerFarmer.farmerIdentityNo = farmerIdentityNo;
  }
  final String? phone1 = jsonConvert.convert<String>(json['phone1']);
  if (phone1 != null) {
    insureContractInfoFarmerFarmer.phone1 = phone1;
  }
  final String? householderNumber =
      jsonConvert.convert<String>(json['householderNumber']);
  if (householderNumber != null) {
    insureContractInfoFarmerFarmer.householderNumber = householderNumber;
  }
  final String? employeeNumber =
      jsonConvert.convert<String>(json['employeeNumber']);
  if (employeeNumber != null) {
    insureContractInfoFarmerFarmer.employeeNumber = employeeNumber;
  }
  final String? archiveNumber =
      jsonConvert.convert<String>(json['archiveNumber']);
  if (archiveNumber != null) {
    insureContractInfoFarmerFarmer.archiveNumber = archiveNumber;
  }
  final String? archiveBirthday =
      jsonConvert.convert<String>(json['archiveBirthday']);
  if (archiveBirthday != null) {
    insureContractInfoFarmerFarmer.archiveBirthday = archiveBirthday;
  }
  final String? bankcardTypeNo =
      jsonConvert.convert<String>(json['bankcardTypeNo']);
  if (bankcardTypeNo != null) {
    insureContractInfoFarmerFarmer.bankcardTypeNo = bankcardTypeNo;
  }
  final String? bankcardPhotoPath =
      jsonConvert.convert<String>(json['bankcardPhotoPath']);
  if (bankcardPhotoPath != null) {
    insureContractInfoFarmerFarmer.bankcardPhotoPath = bankcardPhotoPath;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    insureContractInfoFarmerFarmer.bankName = bankName;
  }
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  if (bankAccount != null) {
    insureContractInfoFarmerFarmer.bankAccount = bankAccount;
  }
  final String? rationPrecinctNo =
      jsonConvert.convert<String>(json['rationPrecinctNo']);
  if (rationPrecinctNo != null) {
    insureContractInfoFarmerFarmer.rationPrecinctNo = rationPrecinctNo;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    insureContractInfoFarmerFarmer.remark = remark;
  }
  final String? rationRight = jsonConvert.convert<String>(json['rationRight']);
  if (rationRight != null) {
    insureContractInfoFarmerFarmer.rationRight = rationRight;
  }
  final String? sexNo = jsonConvert.convert<String>(json['sexNo']);
  if (sexNo != null) {
    insureContractInfoFarmerFarmer.sexNo = sexNo;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    insureContractInfoFarmerFarmer.address = address;
  }
  final String? nationNo = jsonConvert.convert<String>(json['nationNo']);
  if (nationNo != null) {
    insureContractInfoFarmerFarmer.nationNo = nationNo;
  }
  final String? issuingAuthority =
      jsonConvert.convert<String>(json['issuingAuthority']);
  if (issuingAuthority != null) {
    insureContractInfoFarmerFarmer.issuingAuthority = issuingAuthority;
  }
  final String? validDate = jsonConvert.convert<String>(json['validDate']);
  if (validDate != null) {
    insureContractInfoFarmerFarmer.validDate = validDate;
  }
  final String? householderRelation =
      jsonConvert.convert<String>(json['householderRelation']);
  if (householderRelation != null) {
    insureContractInfoFarmerFarmer.householderRelation = householderRelation;
  }
  final String? householderId =
      jsonConvert.convert<String>(json['householderId']);
  if (householderId != null) {
    insureContractInfoFarmerFarmer.householderId = householderId;
  }
  final String? archiveSaved =
      jsonConvert.convert<String>(json['archiveSaved']);
  if (archiveSaved != null) {
    insureContractInfoFarmerFarmer.archiveSaved = archiveSaved;
  }
  final String? isRealName = jsonConvert.convert<String>(json['isRealName']);
  if (isRealName != null) {
    insureContractInfoFarmerFarmer.isRealName = isRealName;
  }
  final String? isFamilyMember =
      jsonConvert.convert<String>(json['isFamilyMember']);
  if (isFamilyMember != null) {
    insureContractInfoFarmerFarmer.isFamilyMember = isFamilyMember;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    insureContractInfoFarmerFarmer.updateTime = updateTime;
  }
  final String? birthday = jsonConvert.convert<String>(json['birthday']);
  if (birthday != null) {
    insureContractInfoFarmerFarmer.birthday = birthday;
  }
  final String? idFront = jsonConvert.convert<String>(json['idFront']);
  if (idFront != null) {
    insureContractInfoFarmerFarmer.idFront = idFront;
  }
  final String? idBack = jsonConvert.convert<String>(json['idBack']);
  if (idBack != null) {
    insureContractInfoFarmerFarmer.idBack = idBack;
  }
  final String? serialNumber =
      jsonConvert.convert<String>(json['serialNumber']);
  if (serialNumber != null) {
    insureContractInfoFarmerFarmer.serialNumber = serialNumber;
  }
  final String? signPath = jsonConvert.convert<String>(json['signPath']);
  if (signPath != null) {
    insureContractInfoFarmerFarmer.signPath = signPath;
  }
  final String? photoPath = jsonConvert.convert<String>(json['photoPath']);
  if (photoPath != null) {
    insureContractInfoFarmerFarmer.photoPath = photoPath;
  }
  final String? policySignStatus =
      jsonConvert.convert<String>(json['policySignStatus']);
  if (policySignStatus != null) {
    insureContractInfoFarmerFarmer.policySignStatus = policySignStatus;
  }
  final String? source = jsonConvert.convert<String>(json['source']);
  if (source != null) {
    insureContractInfoFarmerFarmer.source = source;
  }
  final String? householdHomePage =
      jsonConvert.convert<String>(json['householdHomePage']);
  if (householdHomePage != null) {
    insureContractInfoFarmerFarmer.householdHomePage = householdHomePage;
  }
  final String? householdSelfPage =
      jsonConvert.convert<String>(json['householdSelfPage']);
  if (householdSelfPage != null) {
    insureContractInfoFarmerFarmer.householdSelfPage = householdSelfPage;
  }
  final String? verifyRequired =
      jsonConvert.convert<String>(json['verifyRequired']);
  if (verifyRequired != null) {
    insureContractInfoFarmerFarmer.verifyRequired = verifyRequired;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    insureContractInfoFarmerFarmer.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    insureContractInfoFarmerFarmer.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    insureContractInfoFarmerFarmer.updateBy = updateBy;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    insureContractInfoFarmerFarmer.statusCd = statusCd;
  }
  final String? userId = jsonConvert.convert<String>(json['userId']);
  if (userId != null) {
    insureContractInfoFarmerFarmer.userId = userId;
  }
  final String? dataStatus = jsonConvert.convert<String>(json['dataStatus']);
  if (dataStatus != null) {
    insureContractInfoFarmerFarmer.dataStatus = dataStatus;
  }
  final String? familyFarmerId =
      jsonConvert.convert<String>(json['familyFarmerId']);
  if (familyFarmerId != null) {
    insureContractInfoFarmerFarmer.familyFarmerId = familyFarmerId;
  }
  final String? rationRightSaved =
      jsonConvert.convert<String>(json['rationRightSaved']);
  if (rationRightSaved != null) {
    insureContractInfoFarmerFarmer.rationRightSaved = rationRightSaved;
  }
  final String? supplement = jsonConvert.convert<String>(json['supplement']);
  if (supplement != null) {
    insureContractInfoFarmerFarmer.supplement = supplement;
  }
  final String? rationRequired =
      jsonConvert.convert<String>(json['rationRequired']);
  if (rationRequired != null) {
    insureContractInfoFarmerFarmer.rationRequired = rationRequired;
  }
  final String? version = jsonConvert.convert<String>(json['version']);
  if (version != null) {
    insureContractInfoFarmerFarmer.version = version;
  }
  final String? creatorName = jsonConvert.convert<String>(json['creatorName']);
  if (creatorName != null) {
    insureContractInfoFarmerFarmer.creatorName = creatorName;
  }
  final String? updaterName = jsonConvert.convert<String>(json['updaterName']);
  if (updaterName != null) {
    insureContractInfoFarmerFarmer.updaterName = updaterName;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    insureContractInfoFarmerFarmer.farmerId = farmerId;
  }
  final String? province = jsonConvert.convert<String>(json['province']);
  if (province != null) {
    insureContractInfoFarmerFarmer.province = province;
  }
  final String? city = jsonConvert.convert<String>(json['city']);
  if (city != null) {
    insureContractInfoFarmerFarmer.city = city;
  }
  final String? district = jsonConvert.convert<String>(json['district']);
  if (district != null) {
    insureContractInfoFarmerFarmer.district = district;
  }
  final String? street = jsonConvert.convert<String>(json['street']);
  if (street != null) {
    insureContractInfoFarmerFarmer.street = street;
  }
  final String? building = jsonConvert.convert<String>(json['building']);
  if (building != null) {
    insureContractInfoFarmerFarmer.building = building;
  }
  final String? yearNo = jsonConvert.convert<String>(json['yearNo']);
  if (yearNo != null) {
    insureContractInfoFarmerFarmer.yearNo = yearNo;
  }
  final String? num = jsonConvert.convert<String>(json['num']);
  if (num != null) {
    insureContractInfoFarmerFarmer.num = num;
  }
  final String? rationPrecinctName =
      jsonConvert.convert<String>(json['rationPrecinctName']);
  if (rationPrecinctName != null) {
    insureContractInfoFarmerFarmer.rationPrecinctName = rationPrecinctName;
  }
  final String? approvalStatusNo =
      jsonConvert.convert<String>(json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    insureContractInfoFarmerFarmer.approvalStatusNo = approvalStatusNo;
  }
  final String? approvalRemark =
      jsonConvert.convert<String>(json['approvalRemark']);
  if (approvalRemark != null) {
    insureContractInfoFarmerFarmer.approvalRemark = approvalRemark;
  }
  final bool? isModified = jsonConvert.convert<bool>(json['isModified']);
  if (isModified != null) {
    insureContractInfoFarmerFarmer.isModified = isModified;
  }
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    insureContractInfoFarmerFarmer.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    insureContractInfoFarmerFarmer.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    insureContractInfoFarmerFarmer.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    insureContractInfoFarmerFarmer.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    insureContractInfoFarmerFarmer.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    insureContractInfoFarmerFarmer.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    insureContractInfoFarmerFarmer.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    insureContractInfoFarmerFarmer.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    insureContractInfoFarmerFarmer.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    insureContractInfoFarmerFarmer.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    insureContractInfoFarmerFarmer.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    insureContractInfoFarmerFarmer.auditCTime = auditCTime;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    insureContractInfoFarmerFarmer.auditDFlag = auditDFlag;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    insureContractInfoFarmerFarmer.auditDId = auditDId;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    insureContractInfoFarmerFarmer.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    insureContractInfoFarmerFarmer.auditDTime = auditDTime;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    insureContractInfoFarmerFarmer.auditEFlag = auditEFlag;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    insureContractInfoFarmerFarmer.auditEId = auditEId;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    insureContractInfoFarmerFarmer.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    insureContractInfoFarmerFarmer.auditETime = auditETime;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    insureContractInfoFarmerFarmer.auditFFlag = auditFFlag;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    insureContractInfoFarmerFarmer.auditFId = auditFId;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    insureContractInfoFarmerFarmer.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    insureContractInfoFarmerFarmer.auditFTime = auditFTime;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    insureContractInfoFarmerFarmer.auditGFlag = auditGFlag;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    insureContractInfoFarmerFarmer.auditGId = auditGId;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    insureContractInfoFarmerFarmer.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    insureContractInfoFarmerFarmer.auditGTime = auditGTime;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    insureContractInfoFarmerFarmer.auditHFlag = auditHFlag;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    insureContractInfoFarmerFarmer.auditHId = auditHId;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    insureContractInfoFarmerFarmer.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    insureContractInfoFarmerFarmer.auditHTime = auditHTime;
  }
  final String? currentAuditRoleId =
      jsonConvert.convert<String>(json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    insureContractInfoFarmerFarmer.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName =
      jsonConvert.convert<String>(json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    insureContractInfoFarmerFarmer.currentAuditRoleName = currentAuditRoleName;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    insureContractInfoFarmerFarmer.auditLevel = auditLevel;
  }
  final String? initializeNo =
      jsonConvert.convert<String>(json['initializeNo']);
  if (initializeNo != null) {
    insureContractInfoFarmerFarmer.initializeNo = initializeNo;
  }
  final bool? isdel = jsonConvert.convert<bool>(json['isdel']);
  if (isdel != null) {
    insureContractInfoFarmerFarmer.isdel = isdel;
  }
  final String? network = jsonConvert.convert<String>(json['network']);
  if (network != null) {
    insureContractInfoFarmerFarmer.network = network;
  }
  final String? lineNumber = jsonConvert.convert<String>(json['lineNumber']);
  if (lineNumber != null) {
    insureContractInfoFarmerFarmer.lineNumber = lineNumber;
  }
  final String? certLcFlag = jsonConvert.convert<String>(json['certLcFlag']);
  if (certLcFlag != null) {
    insureContractInfoFarmerFarmer.certLcFlag = certLcFlag;
  }
  final String? certAmFlag = jsonConvert.convert<String>(json['certAmFlag']);
  if (certAmFlag != null) {
    insureContractInfoFarmerFarmer.certAmFlag = certAmFlag;
  }
  final String? certSpecialFlag =
      jsonConvert.convert<String>(json['certSpecialFlag']);
  if (certSpecialFlag != null) {
    insureContractInfoFarmerFarmer.certSpecialFlag = certSpecialFlag;
  }
  final String? appRoleId = jsonConvert.convert<String>(json['appRoleId']);
  if (appRoleId != null) {
    insureContractInfoFarmerFarmer.appRoleId = appRoleId;
  }
  return insureContractInfoFarmerFarmer;
}

Map<String, dynamic> $InsureContractInfoFarmerFarmerToJson(
    InsureContractInfoFarmerFarmer entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['organizationNo'] = entity.organizationNo;
  data['organizationName'] = entity.organizationName;
  data['name'] = entity.name;
  data['idNumber'] = entity.idNumber;
  data['farmerIdentityNo'] = entity.farmerIdentityNo;
  data['phone1'] = entity.phone1;
  data['householderNumber'] = entity.householderNumber;
  data['employeeNumber'] = entity.employeeNumber;
  data['archiveNumber'] = entity.archiveNumber;
  data['archiveBirthday'] = entity.archiveBirthday;
  data['bankcardTypeNo'] = entity.bankcardTypeNo;
  data['bankcardPhotoPath'] = entity.bankcardPhotoPath;
  data['bankName'] = entity.bankName;
  data['bankAccount'] = entity.bankAccount;
  data['rationPrecinctNo'] = entity.rationPrecinctNo;
  data['remark'] = entity.remark;
  data['rationRight'] = entity.rationRight;
  data['sexNo'] = entity.sexNo;
  data['address'] = entity.address;
  data['nationNo'] = entity.nationNo;
  data['issuingAuthority'] = entity.issuingAuthority;
  data['validDate'] = entity.validDate;
  data['householderRelation'] = entity.householderRelation;
  data['householderId'] = entity.householderId;
  data['archiveSaved'] = entity.archiveSaved;
  data['isRealName'] = entity.isRealName;
  data['isFamilyMember'] = entity.isFamilyMember;
  data['updateTime'] = entity.updateTime;
  data['birthday'] = entity.birthday;
  data['idFront'] = entity.idFront;
  data['idBack'] = entity.idBack;
  data['serialNumber'] = entity.serialNumber;
  data['signPath'] = entity.signPath;
  data['photoPath'] = entity.photoPath;
  data['policySignStatus'] = entity.policySignStatus;
  data['source'] = entity.source;
  data['householdHomePage'] = entity.householdHomePage;
  data['householdSelfPage'] = entity.householdSelfPage;
  data['verifyRequired'] = entity.verifyRequired;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['statusCd'] = entity.statusCd;
  data['userId'] = entity.userId;
  data['dataStatus'] = entity.dataStatus;
  data['familyFarmerId'] = entity.familyFarmerId;
  data['rationRightSaved'] = entity.rationRightSaved;
  data['supplement'] = entity.supplement;
  data['rationRequired'] = entity.rationRequired;
  data['version'] = entity.version;
  data['creatorName'] = entity.creatorName;
  data['updaterName'] = entity.updaterName;
  data['farmerId'] = entity.farmerId;
  data['province'] = entity.province;
  data['city'] = entity.city;
  data['district'] = entity.district;
  data['street'] = entity.street;
  data['building'] = entity.building;
  data['yearNo'] = entity.yearNo;
  data['num'] = entity.num;
  data['rationPrecinctName'] = entity.rationPrecinctName;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['approvalRemark'] = entity.approvalRemark;
  data['isModified'] = entity.isModified;
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDId'] = entity.auditDId;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEId'] = entity.auditEId;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFId'] = entity.auditFId;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGId'] = entity.auditGId;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHId'] = entity.auditHId;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['auditLevel'] = entity.auditLevel;
  data['initializeNo'] = entity.initializeNo;
  data['isdel'] = entity.isdel;
  data['network'] = entity.network;
  data['lineNumber'] = entity.lineNumber;
  data['certLcFlag'] = entity.certLcFlag;
  data['certAmFlag'] = entity.certAmFlag;
  data['certSpecialFlag'] = entity.certSpecialFlag;
  data['appRoleId'] = entity.appRoleId;
  return data;
}

extension InsureContractInfoFarmerFarmerExtension
    on InsureContractInfoFarmerFarmer {
  InsureContractInfoFarmerFarmer copyWith({
    String? organizationNo,
    String? organizationName,
    String? name,
    String? idNumber,
    String? farmerIdentityNo,
    String? phone1,
    String? householderNumber,
    String? employeeNumber,
    String? archiveNumber,
    String? archiveBirthday,
    String? bankcardTypeNo,
    String? bankcardPhotoPath,
    String? bankName,
    String? bankAccount,
    String? rationPrecinctNo,
    String? remark,
    String? rationRight,
    String? sexNo,
    String? address,
    String? nationNo,
    String? issuingAuthority,
    String? validDate,
    String? householderRelation,
    String? householderId,
    String? archiveSaved,
    String? isRealName,
    String? isFamilyMember,
    String? updateTime,
    String? birthday,
    String? idFront,
    String? idBack,
    String? serialNumber,
    String? signPath,
    String? photoPath,
    String? policySignStatus,
    String? source,
    String? householdHomePage,
    String? householdSelfPage,
    String? verifyRequired,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? statusCd,
    String? userId,
    String? dataStatus,
    String? familyFarmerId,
    String? rationRightSaved,
    String? supplement,
    String? rationRequired,
    String? version,
    String? creatorName,
    String? updaterName,
    String? farmerId,
    String? province,
    String? city,
    String? district,
    String? street,
    String? building,
    String? yearNo,
    String? num,
    String? rationPrecinctName,
    String? approvalStatusNo,
    String? approvalRemark,
    bool? isModified,
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? auditDFlag,
    String? auditDId,
    String? auditDName,
    String? auditDTime,
    String? auditEFlag,
    String? auditEId,
    String? auditEName,
    String? auditETime,
    String? auditFFlag,
    String? auditFId,
    String? auditFName,
    String? auditFTime,
    String? auditGFlag,
    String? auditGId,
    String? auditGName,
    String? auditGTime,
    String? auditHFlag,
    String? auditHId,
    String? auditHName,
    String? auditHTime,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? auditLevel,
    String? initializeNo,
    bool? isdel,
    String? network,
    String? lineNumber,
    String? certLcFlag,
    String? certAmFlag,
    String? certSpecialFlag,
    String? appRoleId,
  }) {
    return InsureContractInfoFarmerFarmer()
      ..organizationNo = organizationNo ?? this.organizationNo
      ..organizationName = organizationName ?? this.organizationName
      ..name = name ?? this.name
      ..idNumber = idNumber ?? this.idNumber
      ..farmerIdentityNo = farmerIdentityNo ?? this.farmerIdentityNo
      ..phone1 = phone1 ?? this.phone1
      ..householderNumber = householderNumber ?? this.householderNumber
      ..employeeNumber = employeeNumber ?? this.employeeNumber
      ..archiveNumber = archiveNumber ?? this.archiveNumber
      ..archiveBirthday = archiveBirthday ?? this.archiveBirthday
      ..bankcardTypeNo = bankcardTypeNo ?? this.bankcardTypeNo
      ..bankcardPhotoPath = bankcardPhotoPath ?? this.bankcardPhotoPath
      ..bankName = bankName ?? this.bankName
      ..bankAccount = bankAccount ?? this.bankAccount
      ..rationPrecinctNo = rationPrecinctNo ?? this.rationPrecinctNo
      ..remark = remark ?? this.remark
      ..rationRight = rationRight ?? this.rationRight
      ..sexNo = sexNo ?? this.sexNo
      ..address = address ?? this.address
      ..nationNo = nationNo ?? this.nationNo
      ..issuingAuthority = issuingAuthority ?? this.issuingAuthority
      ..validDate = validDate ?? this.validDate
      ..householderRelation = householderRelation ?? this.householderRelation
      ..householderId = householderId ?? this.householderId
      ..archiveSaved = archiveSaved ?? this.archiveSaved
      ..isRealName = isRealName ?? this.isRealName
      ..isFamilyMember = isFamilyMember ?? this.isFamilyMember
      ..updateTime = updateTime ?? this.updateTime
      ..birthday = birthday ?? this.birthday
      ..idFront = idFront ?? this.idFront
      ..idBack = idBack ?? this.idBack
      ..serialNumber = serialNumber ?? this.serialNumber
      ..signPath = signPath ?? this.signPath
      ..photoPath = photoPath ?? this.photoPath
      ..policySignStatus = policySignStatus ?? this.policySignStatus
      ..source = source ?? this.source
      ..householdHomePage = householdHomePage ?? this.householdHomePage
      ..householdSelfPage = householdSelfPage ?? this.householdSelfPage
      ..verifyRequired = verifyRequired ?? this.verifyRequired
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..statusCd = statusCd ?? this.statusCd
      ..userId = userId ?? this.userId
      ..dataStatus = dataStatus ?? this.dataStatus
      ..familyFarmerId = familyFarmerId ?? this.familyFarmerId
      ..rationRightSaved = rationRightSaved ?? this.rationRightSaved
      ..supplement = supplement ?? this.supplement
      ..rationRequired = rationRequired ?? this.rationRequired
      ..version = version ?? this.version
      ..creatorName = creatorName ?? this.creatorName
      ..updaterName = updaterName ?? this.updaterName
      ..farmerId = farmerId ?? this.farmerId
      ..province = province ?? this.province
      ..city = city ?? this.city
      ..district = district ?? this.district
      ..street = street ?? this.street
      ..building = building ?? this.building
      ..yearNo = yearNo ?? this.yearNo
      ..num = num ?? this.num
      ..rationPrecinctName = rationPrecinctName ?? this.rationPrecinctName
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..isModified = isModified ?? this.isModified
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDId = auditDId ?? this.auditDId
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEId = auditEId ?? this.auditEId
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFId = auditFId ?? this.auditFId
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGId = auditGId ?? this.auditGId
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHId = auditHId ?? this.auditHId
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..auditLevel = auditLevel ?? this.auditLevel
      ..initializeNo = initializeNo ?? this.initializeNo
      ..isdel = isdel ?? this.isdel
      ..network = network ?? this.network
      ..lineNumber = lineNumber ?? this.lineNumber
      ..certLcFlag = certLcFlag ?? this.certLcFlag
      ..certAmFlag = certAmFlag ?? this.certAmFlag
      ..certSpecialFlag = certSpecialFlag ?? this.certSpecialFlag
      ..appRoleId = appRoleId ?? this.appRoleId;
  }
}

InsureContractInfoPlotList $InsureContractInfoPlotListFromJson(
    Map<String, dynamic> json) {
  final InsureContractInfoPlotList insureContractInfoPlotList =
      InsureContractInfoPlotList();
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    insureContractInfoPlotList.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    insureContractInfoPlotList.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    insureContractInfoPlotList.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    insureContractInfoPlotList.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    insureContractInfoPlotList.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    insureContractInfoPlotList.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    insureContractInfoPlotList.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    insureContractInfoPlotList.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    insureContractInfoPlotList.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    insureContractInfoPlotList.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    insureContractInfoPlotList.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    insureContractInfoPlotList.auditCTime = auditCTime;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    insureContractInfoPlotList.auditDFlag = auditDFlag;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    insureContractInfoPlotList.auditDId = auditDId;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    insureContractInfoPlotList.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    insureContractInfoPlotList.auditDTime = auditDTime;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    insureContractInfoPlotList.auditEFlag = auditEFlag;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    insureContractInfoPlotList.auditEId = auditEId;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    insureContractInfoPlotList.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    insureContractInfoPlotList.auditETime = auditETime;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    insureContractInfoPlotList.auditFFlag = auditFFlag;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    insureContractInfoPlotList.auditFId = auditFId;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    insureContractInfoPlotList.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    insureContractInfoPlotList.auditFTime = auditFTime;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    insureContractInfoPlotList.auditGFlag = auditGFlag;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    insureContractInfoPlotList.auditGId = auditGId;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    insureContractInfoPlotList.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    insureContractInfoPlotList.auditGTime = auditGTime;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    insureContractInfoPlotList.auditHFlag = auditHFlag;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    insureContractInfoPlotList.auditHId = auditHId;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    insureContractInfoPlotList.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    insureContractInfoPlotList.auditHTime = auditHTime;
  }
  final String? landContractChargePlanId =
      jsonConvert.convert<String>(json['landContractChargePlanId']);
  if (landContractChargePlanId != null) {
    insureContractInfoPlotList.landContractChargePlanId =
        landContractChargePlanId;
  }
  final String? yearNo = jsonConvert.convert<String>(json['yearNo']);
  if (yearNo != null) {
    insureContractInfoPlotList.yearNo = yearNo;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    insureContractInfoPlotList.organizationNo = organizationNo;
  }
  final String? partnerName = jsonConvert.convert<String>(json['partnerName']);
  if (partnerName != null) {
    insureContractInfoPlotList.partnerName = partnerName;
  }
  final String? partnerCode = jsonConvert.convert<String>(json['partnerCode']);
  if (partnerCode != null) {
    insureContractInfoPlotList.partnerCode = partnerCode;
  }
  final String? contractId = jsonConvert.convert<String>(json['contractId']);
  if (contractId != null) {
    insureContractInfoPlotList.contractId = contractId;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    insureContractInfoPlotList.farmerId = farmerId;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    insureContractInfoPlotList.farmerName = farmerName;
  }
  final String? farmerIdNumber =
      jsonConvert.convert<String>(json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    insureContractInfoPlotList.farmerIdNumber = farmerIdNumber;
  }
  final String? farmerIdentityNo =
      jsonConvert.convert<String>(json['farmerIdentityNo']);
  if (farmerIdentityNo != null) {
    insureContractInfoPlotList.farmerIdentityNo = farmerIdentityNo;
  }
  final String? chargeTypeNo =
      jsonConvert.convert<String>(json['chargeTypeNo']);
  if (chargeTypeNo != null) {
    insureContractInfoPlotList.chargeTypeNo = chargeTypeNo;
  }
  final String? landTypeNo = jsonConvert.convert<String>(json['landTypeNo']);
  if (landTypeNo != null) {
    insureContractInfoPlotList.landTypeNo = landTypeNo;
  }
  final String? plowlandNo = jsonConvert.convert<String>(json['plowlandNo']);
  if (plowlandNo != null) {
    insureContractInfoPlotList.plowlandNo = plowlandNo;
  }
  final String? landRankNo = jsonConvert.convert<String>(json['landRankNo']);
  if (landRankNo != null) {
    insureContractInfoPlotList.landRankNo = landRankNo;
  }
  final String? cropNo = jsonConvert.convert<String>(json['cropNo']);
  if (cropNo != null) {
    insureContractInfoPlotList.cropNo = cropNo;
  }
  final String? beforeCropNo =
      jsonConvert.convert<String>(json['beforeCropNo']);
  if (beforeCropNo != null) {
    insureContractInfoPlotList.beforeCropNo = beforeCropNo;
  }
  final String? landNumber = jsonConvert.convert<String>(json['landNumber']);
  if (landNumber != null) {
    insureContractInfoPlotList.landNumber = landNumber;
  }
  final String? landBlock = jsonConvert.convert<String>(json['landBlock']);
  if (landBlock != null) {
    insureContractInfoPlotList.landBlock = landBlock;
  }
  final String? area = jsonConvert.convert<String>(json['area']);
  if (area != null) {
    insureContractInfoPlotList.area = area;
  }
  final String? subtotalContractFee =
      jsonConvert.convert<String>(json['subtotalContractFee']);
  if (subtotalContractFee != null) {
    insureContractInfoPlotList.subtotalContractFee = subtotalContractFee;
  }
  final String? standardPrice =
      jsonConvert.convert<String>(json['standardPrice']);
  if (standardPrice != null) {
    insureContractInfoPlotList.standardPrice = standardPrice;
  }
  final String? rentFee = jsonConvert.convert<String>(json['rentFee']);
  if (rentFee != null) {
    insureContractInfoPlotList.rentFee = rentFee;
  }
  final String? bidPrice = jsonConvert.convert<String>(json['bidPrice']);
  if (bidPrice != null) {
    insureContractInfoPlotList.bidPrice = bidPrice;
  }
  final String? bidFee = jsonConvert.convert<String>(json['bidFee']);
  if (bidFee != null) {
    insureContractInfoPlotList.bidFee = bidFee;
  }
  final String? tieredStandardPrice =
      jsonConvert.convert<String>(json['tieredStandardPrice']);
  if (tieredStandardPrice != null) {
    insureContractInfoPlotList.tieredStandardPrice = tieredStandardPrice;
  }
  final String? tieredRentFee =
      jsonConvert.convert<String>(json['tieredRentFee']);
  if (tieredRentFee != null) {
    insureContractInfoPlotList.tieredRentFee = tieredRentFee;
  }
  final String? totalFee = jsonConvert.convert<String>(json['totalFee']);
  if (totalFee != null) {
    insureContractInfoPlotList.totalFee = totalFee;
  }
  final String? rentInKindPerMu =
      jsonConvert.convert<String>(json['rentInKindPerMu']);
  if (rentInKindPerMu != null) {
    insureContractInfoPlotList.rentInKindPerMu = rentInKindPerMu;
  }
  final String? rentInKindTotal =
      jsonConvert.convert<String>(json['rentInKindTotal']);
  if (rentInKindTotal != null) {
    insureContractInfoPlotList.rentInKindTotal = rentInKindTotal;
  }
  final String? locationName =
      jsonConvert.convert<String>(json['locationName']);
  if (locationName != null) {
    insureContractInfoPlotList.locationName = locationName;
  }
  final String? familyCount = jsonConvert.convert<String>(json['familyCount']);
  if (familyCount != null) {
    insureContractInfoPlotList.familyCount = familyCount;
  }
  final String? practitionerCount =
      jsonConvert.convert<String>(json['practitionerCount']);
  if (practitionerCount != null) {
    insureContractInfoPlotList.practitionerCount = practitionerCount;
  }
  final String? deposit = jsonConvert.convert<String>(json['deposit']);
  if (deposit != null) {
    insureContractInfoPlotList.deposit = deposit;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    insureContractInfoPlotList.remark = remark;
  }
  final String? paymentMethodNo =
      jsonConvert.convert<String>(json['paymentMethodNo']);
  if (paymentMethodNo != null) {
    insureContractInfoPlotList.paymentMethodNo = paymentMethodNo;
  }
  final String? bankNo = jsonConvert.convert<String>(json['bankNo']);
  if (bankNo != null) {
    insureContractInfoPlotList.bankNo = bankNo;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    insureContractInfoPlotList.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    insureContractInfoPlotList.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    insureContractInfoPlotList.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    insureContractInfoPlotList.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    insureContractInfoPlotList.statusCd = statusCd;
  }
  final String? plotNo = jsonConvert.convert<String>(json['plotNo']);
  if (plotNo != null) {
    insureContractInfoPlotList.plotNo = plotNo;
  }
  final String? paymentStatusNo =
      jsonConvert.convert<String>(json['paymentStatusNo']);
  if (paymentStatusNo != null) {
    insureContractInfoPlotList.paymentStatusNo = paymentStatusNo;
  }
  final String? growArea = jsonConvert.convert<String>(json['growArea']);
  if (growArea != null) {
    insureContractInfoPlotList.growArea = growArea;
  }
  final String? assuranceFee =
      jsonConvert.convert<String>(json['assuranceFee']);
  if (assuranceFee != null) {
    insureContractInfoPlotList.assuranceFee = assuranceFee;
  }
  final String? scaleArea = jsonConvert.convert<String>(json['scaleArea']);
  if (scaleArea != null) {
    insureContractInfoPlotList.scaleArea = scaleArea;
  }
  final String? planStatus = jsonConvert.convert<String>(json['planStatus']);
  if (planStatus != null) {
    insureContractInfoPlotList.planStatus = planStatus;
  }
  final String? creatorName = jsonConvert.convert<String>(json['creatorName']);
  if (creatorName != null) {
    insureContractInfoPlotList.creatorName = creatorName;
  }
  final String? updaterName = jsonConvert.convert<String>(json['updaterName']);
  if (updaterName != null) {
    insureContractInfoPlotList.updaterName = updaterName;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    insureContractInfoPlotList.organizationName = organizationName;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    insureContractInfoPlotList.params = params;
  }
  final String? approverId = jsonConvert.convert<String>(json['approverId']);
  if (approverId != null) {
    insureContractInfoPlotList.approverId = approverId;
  }
  final String? approverName =
      jsonConvert.convert<String>(json['approverName']);
  if (approverName != null) {
    insureContractInfoPlotList.approverName = approverName;
  }
  final String? approverDate =
      jsonConvert.convert<String>(json['approverDate']);
  if (approverDate != null) {
    insureContractInfoPlotList.approverDate = approverDate;
  }
  final String? financeStatusNo =
      jsonConvert.convert<String>(json['financeStatusNo']);
  if (financeStatusNo != null) {
    insureContractInfoPlotList.financeStatusNo = financeStatusNo;
  }
  final String? approvalStatusNo =
      jsonConvert.convert<String>(json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    insureContractInfoPlotList.approvalStatusNo = approvalStatusNo;
  }
  final String? approvalRemark =
      jsonConvert.convert<String>(json['approvalRemark']);
  if (approvalRemark != null) {
    insureContractInfoPlotList.approvalRemark = approvalRemark;
  }
  final String? currentAuditRoleId =
      jsonConvert.convert<String>(json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    insureContractInfoPlotList.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName =
      jsonConvert.convert<String>(json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    insureContractInfoPlotList.currentAuditRoleName = currentAuditRoleName;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    insureContractInfoPlotList.auditLevel = auditLevel;
  }
  final String? groupIds = jsonConvert.convert<String>(json['groupIds']);
  if (groupIds != null) {
    insureContractInfoPlotList.groupIds = groupIds;
  }
  final String? groupIdArr = jsonConvert.convert<String>(json['groupIdArr']);
  if (groupIdArr != null) {
    insureContractInfoPlotList.groupIdArr = groupIdArr;
  }
  final String? hasContract = jsonConvert.convert<String>(json['hasContract']);
  if (hasContract != null) {
    insureContractInfoPlotList.hasContract = hasContract;
  }
  final String? serialNumber =
      jsonConvert.convert<String>(json['serialNumber']);
  if (serialNumber != null) {
    insureContractInfoPlotList.serialNumber = serialNumber;
  }
  final String? otherMatters =
      jsonConvert.convert<String>(json['otherMatters']);
  if (otherMatters != null) {
    insureContractInfoPlotList.otherMatters = otherMatters;
  }
  final String? contractGenerateStatusNo =
      jsonConvert.convert<String>(json['contractGenerateStatusNo']);
  if (contractGenerateStatusNo != null) {
    insureContractInfoPlotList.contractGenerateStatusNo =
        contractGenerateStatusNo;
  }
  final String? contractGenerateStartTime =
      jsonConvert.convert<String>(json['contractGenerateStartTime']);
  if (contractGenerateStartTime != null) {
    insureContractInfoPlotList.contractGenerateStartTime =
        contractGenerateStartTime;
  }
  final String? contractGenerateCompleteTime =
      jsonConvert.convert<String>(json['contractGenerateCompleteTime']);
  if (contractGenerateCompleteTime != null) {
    insureContractInfoPlotList.contractGenerateCompleteTime =
        contractGenerateCompleteTime;
  }
  final String? contractGenerateMsg =
      jsonConvert.convert<String>(json['contractGenerateMsg']);
  if (contractGenerateMsg != null) {
    insureContractInfoPlotList.contractGenerateMsg = contractGenerateMsg;
  }
  final String? contractTemplateNo =
      jsonConvert.convert<String>(json['contractTemplateNo']);
  if (contractTemplateNo != null) {
    insureContractInfoPlotList.contractTemplateNo = contractTemplateNo;
  }
  final String? planBatchNo = jsonConvert.convert<String>(json['planBatchNo']);
  if (planBatchNo != null) {
    insureContractInfoPlotList.planBatchNo = planBatchNo;
  }
  final String? isCurrentPlan =
      jsonConvert.convert<String>(json['isCurrentPlan']);
  if (isCurrentPlan != null) {
    insureContractInfoPlotList.isCurrentPlan = isCurrentPlan;
  }
  final String? serialNumberOld =
      jsonConvert.convert<String>(json['serialNumberOld']);
  if (serialNumberOld != null) {
    insureContractInfoPlotList.serialNumberOld = serialNumberOld;
  }
  final String? dissolutionStatus =
      jsonConvert.convert<String>(json['dissolutionStatus']);
  if (dissolutionStatus != null) {
    insureContractInfoPlotList.dissolutionStatus = dissolutionStatus;
  }
  final String? relationContractId =
      jsonConvert.convert<String>(json['relationContractId']);
  if (relationContractId != null) {
    insureContractInfoPlotList.relationContractId = relationContractId;
  }
  final String? relationSerialNumber =
      jsonConvert.convert<String>(json['relationSerialNumber']);
  if (relationSerialNumber != null) {
    insureContractInfoPlotList.relationSerialNumber = relationSerialNumber;
  }
  final String? contractSignType =
      jsonConvert.convert<String>(json['contractSignType']);
  if (contractSignType != null) {
    insureContractInfoPlotList.contractSignType = contractSignType;
  }
  final String? partnerId = jsonConvert.convert<String>(json['partnerId']);
  if (partnerId != null) {
    insureContractInfoPlotList.partnerId = partnerId;
  }
  final String? contractChargePlanList =
      jsonConvert.convert<String>(json['contractChargePlanList']);
  if (contractChargePlanList != null) {
    insureContractInfoPlotList.contractChargePlanList = contractChargePlanList;
  }
  final String? contractSignNo =
      jsonConvert.convert<String>(json['contractSignNo']);
  if (contractSignNo != null) {
    insureContractInfoPlotList.contractSignNo = contractSignNo;
  }
  final String? contractTypeNo =
      jsonConvert.convert<String>(json['contractTypeNo']);
  if (contractTypeNo != null) {
    insureContractInfoPlotList.contractTypeNo = contractTypeNo;
  }
  final String? sexNo = jsonConvert.convert<String>(json['sexNo']);
  if (sexNo != null) {
    insureContractInfoPlotList.sexNo = sexNo;
  }
  final String? age = jsonConvert.convert<String>(json['age']);
  if (age != null) {
    insureContractInfoPlotList.age = age;
  }
  final String? contractTemplateName =
      jsonConvert.convert<String>(json['contractTemplateName']);
  if (contractTemplateName != null) {
    insureContractInfoPlotList.contractTemplateName = contractTemplateName;
  }
  final String? exportTemplateType =
      jsonConvert.convert<String>(json['exportTemplateType']);
  if (exportTemplateType != null) {
    insureContractInfoPlotList.exportTemplateType = exportTemplateType;
  }
  return insureContractInfoPlotList;
}

Map<String, dynamic> $InsureContractInfoPlotListToJson(
    InsureContractInfoPlotList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDId'] = entity.auditDId;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEId'] = entity.auditEId;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFId'] = entity.auditFId;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGId'] = entity.auditGId;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHId'] = entity.auditHId;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['landContractChargePlanId'] = entity.landContractChargePlanId;
  data['yearNo'] = entity.yearNo;
  data['organizationNo'] = entity.organizationNo;
  data['partnerName'] = entity.partnerName;
  data['partnerCode'] = entity.partnerCode;
  data['contractId'] = entity.contractId;
  data['farmerId'] = entity.farmerId;
  data['farmerName'] = entity.farmerName;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['farmerIdentityNo'] = entity.farmerIdentityNo;
  data['chargeTypeNo'] = entity.chargeTypeNo;
  data['landTypeNo'] = entity.landTypeNo;
  data['plowlandNo'] = entity.plowlandNo;
  data['landRankNo'] = entity.landRankNo;
  data['cropNo'] = entity.cropNo;
  data['beforeCropNo'] = entity.beforeCropNo;
  data['landNumber'] = entity.landNumber;
  data['landBlock'] = entity.landBlock;
  data['area'] = entity.area;
  data['subtotalContractFee'] = entity.subtotalContractFee;
  data['standardPrice'] = entity.standardPrice;
  data['rentFee'] = entity.rentFee;
  data['bidPrice'] = entity.bidPrice;
  data['bidFee'] = entity.bidFee;
  data['tieredStandardPrice'] = entity.tieredStandardPrice;
  data['tieredRentFee'] = entity.tieredRentFee;
  data['totalFee'] = entity.totalFee;
  data['rentInKindPerMu'] = entity.rentInKindPerMu;
  data['rentInKindTotal'] = entity.rentInKindTotal;
  data['locationName'] = entity.locationName;
  data['familyCount'] = entity.familyCount;
  data['practitionerCount'] = entity.practitionerCount;
  data['deposit'] = entity.deposit;
  data['remark'] = entity.remark;
  data['paymentMethodNo'] = entity.paymentMethodNo;
  data['bankNo'] = entity.bankNo;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['plotNo'] = entity.plotNo;
  data['paymentStatusNo'] = entity.paymentStatusNo;
  data['growArea'] = entity.growArea;
  data['assuranceFee'] = entity.assuranceFee;
  data['scaleArea'] = entity.scaleArea;
  data['planStatus'] = entity.planStatus;
  data['creatorName'] = entity.creatorName;
  data['updaterName'] = entity.updaterName;
  data['organizationName'] = entity.organizationName;
  data['params'] = entity.params;
  data['approverId'] = entity.approverId;
  data['approverName'] = entity.approverName;
  data['approverDate'] = entity.approverDate;
  data['financeStatusNo'] = entity.financeStatusNo;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['approvalRemark'] = entity.approvalRemark;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['auditLevel'] = entity.auditLevel;
  data['groupIds'] = entity.groupIds;
  data['groupIdArr'] = entity.groupIdArr;
  data['hasContract'] = entity.hasContract;
  data['serialNumber'] = entity.serialNumber;
  data['otherMatters'] = entity.otherMatters;
  data['contractGenerateStatusNo'] = entity.contractGenerateStatusNo;
  data['contractGenerateStartTime'] = entity.contractGenerateStartTime;
  data['contractGenerateCompleteTime'] = entity.contractGenerateCompleteTime;
  data['contractGenerateMsg'] = entity.contractGenerateMsg;
  data['contractTemplateNo'] = entity.contractTemplateNo;
  data['planBatchNo'] = entity.planBatchNo;
  data['isCurrentPlan'] = entity.isCurrentPlan;
  data['serialNumberOld'] = entity.serialNumberOld;
  data['dissolutionStatus'] = entity.dissolutionStatus;
  data['relationContractId'] = entity.relationContractId;
  data['relationSerialNumber'] = entity.relationSerialNumber;
  data['contractSignType'] = entity.contractSignType;
  data['partnerId'] = entity.partnerId;
  data['contractChargePlanList'] = entity.contractChargePlanList;
  data['contractSignNo'] = entity.contractSignNo;
  data['contractTypeNo'] = entity.contractTypeNo;
  data['sexNo'] = entity.sexNo;
  data['age'] = entity.age;
  data['contractTemplateName'] = entity.contractTemplateName;
  data['exportTemplateType'] = entity.exportTemplateType;
  return data;
}

extension InsureContractInfoPlotListExtension on InsureContractInfoPlotList {
  InsureContractInfoPlotList copyWith({
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? auditDFlag,
    String? auditDId,
    String? auditDName,
    String? auditDTime,
    String? auditEFlag,
    String? auditEId,
    String? auditEName,
    String? auditETime,
    String? auditFFlag,
    String? auditFId,
    String? auditFName,
    String? auditFTime,
    String? auditGFlag,
    String? auditGId,
    String? auditGName,
    String? auditGTime,
    String? auditHFlag,
    String? auditHId,
    String? auditHName,
    String? auditHTime,
    String? landContractChargePlanId,
    String? yearNo,
    String? organizationNo,
    String? partnerName,
    String? partnerCode,
    String? contractId,
    String? farmerId,
    String? farmerName,
    String? farmerIdNumber,
    String? farmerIdentityNo,
    String? chargeTypeNo,
    String? landTypeNo,
    String? plowlandNo,
    String? landRankNo,
    String? cropNo,
    String? beforeCropNo,
    String? landNumber,
    String? landBlock,
    String? area,
    String? subtotalContractFee,
    String? standardPrice,
    String? rentFee,
    String? bidPrice,
    String? bidFee,
    String? tieredStandardPrice,
    String? tieredRentFee,
    String? totalFee,
    String? rentInKindPerMu,
    String? rentInKindTotal,
    String? locationName,
    String? familyCount,
    String? practitionerCount,
    String? deposit,
    String? remark,
    String? paymentMethodNo,
    String? bankNo,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? plotNo,
    String? paymentStatusNo,
    String? growArea,
    String? assuranceFee,
    String? scaleArea,
    String? planStatus,
    String? creatorName,
    String? updaterName,
    String? organizationName,
    String? params,
    String? approverId,
    String? approverName,
    String? approverDate,
    String? financeStatusNo,
    String? approvalStatusNo,
    String? approvalRemark,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? auditLevel,
    String? groupIds,
    String? groupIdArr,
    String? hasContract,
    String? serialNumber,
    String? otherMatters,
    String? contractGenerateStatusNo,
    String? contractGenerateStartTime,
    String? contractGenerateCompleteTime,
    String? contractGenerateMsg,
    String? contractTemplateNo,
    String? planBatchNo,
    String? isCurrentPlan,
    String? serialNumberOld,
    String? dissolutionStatus,
    String? relationContractId,
    String? relationSerialNumber,
    String? contractSignType,
    String? partnerId,
    String? contractChargePlanList,
    String? contractSignNo,
    String? contractTypeNo,
    String? sexNo,
    String? age,
    String? contractTemplateName,
    String? exportTemplateType,
  }) {
    return InsureContractInfoPlotList()
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDId = auditDId ?? this.auditDId
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEId = auditEId ?? this.auditEId
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFId = auditFId ?? this.auditFId
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGId = auditGId ?? this.auditGId
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHId = auditHId ?? this.auditHId
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..landContractChargePlanId =
          landContractChargePlanId ?? this.landContractChargePlanId
      ..yearNo = yearNo ?? this.yearNo
      ..organizationNo = organizationNo ?? this.organizationNo
      ..partnerName = partnerName ?? this.partnerName
      ..partnerCode = partnerCode ?? this.partnerCode
      ..contractId = contractId ?? this.contractId
      ..farmerId = farmerId ?? this.farmerId
      ..farmerName = farmerName ?? this.farmerName
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..farmerIdentityNo = farmerIdentityNo ?? this.farmerIdentityNo
      ..chargeTypeNo = chargeTypeNo ?? this.chargeTypeNo
      ..landTypeNo = landTypeNo ?? this.landTypeNo
      ..plowlandNo = plowlandNo ?? this.plowlandNo
      ..landRankNo = landRankNo ?? this.landRankNo
      ..cropNo = cropNo ?? this.cropNo
      ..beforeCropNo = beforeCropNo ?? this.beforeCropNo
      ..landNumber = landNumber ?? this.landNumber
      ..landBlock = landBlock ?? this.landBlock
      ..area = area ?? this.area
      ..subtotalContractFee = subtotalContractFee ?? this.subtotalContractFee
      ..standardPrice = standardPrice ?? this.standardPrice
      ..rentFee = rentFee ?? this.rentFee
      ..bidPrice = bidPrice ?? this.bidPrice
      ..bidFee = bidFee ?? this.bidFee
      ..tieredStandardPrice = tieredStandardPrice ?? this.tieredStandardPrice
      ..tieredRentFee = tieredRentFee ?? this.tieredRentFee
      ..totalFee = totalFee ?? this.totalFee
      ..rentInKindPerMu = rentInKindPerMu ?? this.rentInKindPerMu
      ..rentInKindTotal = rentInKindTotal ?? this.rentInKindTotal
      ..locationName = locationName ?? this.locationName
      ..familyCount = familyCount ?? this.familyCount
      ..practitionerCount = practitionerCount ?? this.practitionerCount
      ..deposit = deposit ?? this.deposit
      ..remark = remark ?? this.remark
      ..paymentMethodNo = paymentMethodNo ?? this.paymentMethodNo
      ..bankNo = bankNo ?? this.bankNo
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..plotNo = plotNo ?? this.plotNo
      ..paymentStatusNo = paymentStatusNo ?? this.paymentStatusNo
      ..growArea = growArea ?? this.growArea
      ..assuranceFee = assuranceFee ?? this.assuranceFee
      ..scaleArea = scaleArea ?? this.scaleArea
      ..planStatus = planStatus ?? this.planStatus
      ..creatorName = creatorName ?? this.creatorName
      ..updaterName = updaterName ?? this.updaterName
      ..organizationName = organizationName ?? this.organizationName
      ..params = params ?? this.params
      ..approverId = approverId ?? this.approverId
      ..approverName = approverName ?? this.approverName
      ..approverDate = approverDate ?? this.approverDate
      ..financeStatusNo = financeStatusNo ?? this.financeStatusNo
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..auditLevel = auditLevel ?? this.auditLevel
      ..groupIds = groupIds ?? this.groupIds
      ..groupIdArr = groupIdArr ?? this.groupIdArr
      ..hasContract = hasContract ?? this.hasContract
      ..serialNumber = serialNumber ?? this.serialNumber
      ..otherMatters = otherMatters ?? this.otherMatters
      ..contractGenerateStatusNo =
          contractGenerateStatusNo ?? this.contractGenerateStatusNo
      ..contractGenerateStartTime =
          contractGenerateStartTime ?? this.contractGenerateStartTime
      ..contractGenerateCompleteTime =
          contractGenerateCompleteTime ?? this.contractGenerateCompleteTime
      ..contractGenerateMsg = contractGenerateMsg ?? this.contractGenerateMsg
      ..contractTemplateNo = contractTemplateNo ?? this.contractTemplateNo
      ..planBatchNo = planBatchNo ?? this.planBatchNo
      ..isCurrentPlan = isCurrentPlan ?? this.isCurrentPlan
      ..serialNumberOld = serialNumberOld ?? this.serialNumberOld
      ..dissolutionStatus = dissolutionStatus ?? this.dissolutionStatus
      ..relationContractId = relationContractId ?? this.relationContractId
      ..relationSerialNumber = relationSerialNumber ?? this.relationSerialNumber
      ..contractSignType = contractSignType ?? this.contractSignType
      ..partnerId = partnerId ?? this.partnerId
      ..contractChargePlanList =
          contractChargePlanList ?? this.contractChargePlanList
      ..contractSignNo = contractSignNo ?? this.contractSignNo
      ..contractTypeNo = contractTypeNo ?? this.contractTypeNo
      ..sexNo = sexNo ?? this.sexNo
      ..age = age ?? this.age
      ..contractTemplateName = contractTemplateName ?? this.contractTemplateName
      ..exportTemplateType = exportTemplateType ?? this.exportTemplateType;
  }
}

InsureContractInfoBankcard $InsureContractInfoBankcardFromJson(
    Map<String, dynamic> json) {
  final InsureContractInfoBankcard insureContractInfoBankcard =
      InsureContractInfoBankcard();
  final String? bankAccountId =
      jsonConvert.convert<String>(json['bankAccountId']);
  if (bankAccountId != null) {
    insureContractInfoBankcard.bankAccountId = bankAccountId;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    insureContractInfoBankcard.bankName = bankName;
  }
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  if (bankAccount != null) {
    insureContractInfoBankcard.bankAccount = bankAccount;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    insureContractInfoBankcard.farmerId = farmerId;
  }
  final String? source = jsonConvert.convert<String>(json['source']);
  if (source != null) {
    insureContractInfoBankcard.source = source;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    insureContractInfoBankcard.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    insureContractInfoBankcard.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    insureContractInfoBankcard.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    insureContractInfoBankcard.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    insureContractInfoBankcard.statusCd = statusCd;
  }
  final String? dataStatus = jsonConvert.convert<String>(json['dataStatus']);
  if (dataStatus != null) {
    insureContractInfoBankcard.dataStatus = dataStatus;
  }
  final String? orderNumber = jsonConvert.convert<String>(json['orderNumber']);
  if (orderNumber != null) {
    insureContractInfoBankcard.orderNumber = orderNumber;
  }
  final String? version = jsonConvert.convert<String>(json['version']);
  if (version != null) {
    insureContractInfoBankcard.version = version;
  }
  final String? creatorName = jsonConvert.convert<String>(json['creatorName']);
  if (creatorName != null) {
    insureContractInfoBankcard.creatorName = creatorName;
  }
  final String? updaterName = jsonConvert.convert<String>(json['updaterName']);
  if (updaterName != null) {
    insureContractInfoBankcard.updaterName = updaterName;
  }
  final String? farmerIdNumber =
      jsonConvert.convert<String>(json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    insureContractInfoBankcard.farmerIdNumber = farmerIdNumber;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    insureContractInfoBankcard.farmerName = farmerName;
  }
  final String? bankcardTypeNo =
      jsonConvert.convert<String>(json['bankcardTypeNo']);
  if (bankcardTypeNo != null) {
    insureContractInfoBankcard.bankcardTypeNo = bankcardTypeNo;
  }
  final String? isDefault = jsonConvert.convert<String>(json['isDefault']);
  if (isDefault != null) {
    insureContractInfoBankcard.isDefault = isDefault;
  }
  final String? bankcardPhotoPath =
      jsonConvert.convert<String>(json['bankcardPhotoPath']);
  if (bankcardPhotoPath != null) {
    insureContractInfoBankcard.bankcardPhotoPath = bankcardPhotoPath;
  }
  final String? approvalStatusNo =
      jsonConvert.convert<String>(json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    insureContractInfoBankcard.approvalStatusNo = approvalStatusNo;
  }
  final String? approvalRemark =
      jsonConvert.convert<String>(json['approvalRemark']);
  if (approvalRemark != null) {
    insureContractInfoBankcard.approvalRemark = approvalRemark;
  }
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    insureContractInfoBankcard.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    insureContractInfoBankcard.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    insureContractInfoBankcard.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    insureContractInfoBankcard.auditATime = auditATime;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    insureContractInfoBankcard.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    insureContractInfoBankcard.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    insureContractInfoBankcard.auditBTime = auditBTime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    insureContractInfoBankcard.auditBFlag = auditBFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    insureContractInfoBankcard.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    insureContractInfoBankcard.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    insureContractInfoBankcard.auditCTime = auditCTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    insureContractInfoBankcard.auditCFlag = auditCFlag;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    insureContractInfoBankcard.auditDId = auditDId;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    insureContractInfoBankcard.auditDFlag = auditDFlag;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    insureContractInfoBankcard.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    insureContractInfoBankcard.auditDTime = auditDTime;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    insureContractInfoBankcard.auditEId = auditEId;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    insureContractInfoBankcard.auditEFlag = auditEFlag;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    insureContractInfoBankcard.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    insureContractInfoBankcard.auditETime = auditETime;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    insureContractInfoBankcard.auditFId = auditFId;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    insureContractInfoBankcard.auditFFlag = auditFFlag;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    insureContractInfoBankcard.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    insureContractInfoBankcard.auditFTime = auditFTime;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    insureContractInfoBankcard.auditGId = auditGId;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    insureContractInfoBankcard.auditGFlag = auditGFlag;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    insureContractInfoBankcard.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    insureContractInfoBankcard.auditGTime = auditGTime;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    insureContractInfoBankcard.auditHId = auditHId;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    insureContractInfoBankcard.auditHFlag = auditHFlag;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    insureContractInfoBankcard.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    insureContractInfoBankcard.auditHTime = auditHTime;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    insureContractInfoBankcard.auditLevel = auditLevel;
  }
  final String? currentAuditRoleId =
      jsonConvert.convert<String>(json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    insureContractInfoBankcard.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName =
      jsonConvert.convert<String>(json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    insureContractInfoBankcard.currentAuditRoleName = currentAuditRoleName;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    insureContractInfoBankcard.organizationNo = organizationNo;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    insureContractInfoBankcard.organizationName = organizationName;
  }
  final String? network = jsonConvert.convert<String>(json['network']);
  if (network != null) {
    insureContractInfoBankcard.network = network;
  }
  final String? lineNumber = jsonConvert.convert<String>(json['lineNumber']);
  if (lineNumber != null) {
    insureContractInfoBankcard.lineNumber = lineNumber;
  }
  return insureContractInfoBankcard;
}

Map<String, dynamic> $InsureContractInfoBankcardToJson(
    InsureContractInfoBankcard entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['bankAccountId'] = entity.bankAccountId;
  data['bankName'] = entity.bankName;
  data['bankAccount'] = entity.bankAccount;
  data['farmerId'] = entity.farmerId;
  data['source'] = entity.source;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['dataStatus'] = entity.dataStatus;
  data['orderNumber'] = entity.orderNumber;
  data['version'] = entity.version;
  data['creatorName'] = entity.creatorName;
  data['updaterName'] = entity.updaterName;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['farmerName'] = entity.farmerName;
  data['bankcardTypeNo'] = entity.bankcardTypeNo;
  data['isDefault'] = entity.isDefault;
  data['bankcardPhotoPath'] = entity.bankcardPhotoPath;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['approvalRemark'] = entity.approvalRemark;
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditDId'] = entity.auditDId;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEId'] = entity.auditEId;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFId'] = entity.auditFId;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGId'] = entity.auditGId;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHId'] = entity.auditHId;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['auditLevel'] = entity.auditLevel;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['organizationNo'] = entity.organizationNo;
  data['organizationName'] = entity.organizationName;
  data['network'] = entity.network;
  data['lineNumber'] = entity.lineNumber;
  return data;
}

extension InsureContractInfoBankcardExtension on InsureContractInfoBankcard {
  InsureContractInfoBankcard copyWith({
    String? bankAccountId,
    String? bankName,
    String? bankAccount,
    String? farmerId,
    String? source,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? dataStatus,
    String? orderNumber,
    String? version,
    String? creatorName,
    String? updaterName,
    String? farmerIdNumber,
    String? farmerName,
    String? bankcardTypeNo,
    String? isDefault,
    String? bankcardPhotoPath,
    String? approvalStatusNo,
    String? approvalRemark,
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditBFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? auditCFlag,
    String? auditDId,
    String? auditDFlag,
    String? auditDName,
    String? auditDTime,
    String? auditEId,
    String? auditEFlag,
    String? auditEName,
    String? auditETime,
    String? auditFId,
    String? auditFFlag,
    String? auditFName,
    String? auditFTime,
    String? auditGId,
    String? auditGFlag,
    String? auditGName,
    String? auditGTime,
    String? auditHId,
    String? auditHFlag,
    String? auditHName,
    String? auditHTime,
    String? auditLevel,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? organizationNo,
    String? organizationName,
    String? network,
    String? lineNumber,
  }) {
    return InsureContractInfoBankcard()
      ..bankAccountId = bankAccountId ?? this.bankAccountId
      ..bankName = bankName ?? this.bankName
      ..bankAccount = bankAccount ?? this.bankAccount
      ..farmerId = farmerId ?? this.farmerId
      ..source = source ?? this.source
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..dataStatus = dataStatus ?? this.dataStatus
      ..orderNumber = orderNumber ?? this.orderNumber
      ..version = version ?? this.version
      ..creatorName = creatorName ?? this.creatorName
      ..updaterName = updaterName ?? this.updaterName
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..farmerName = farmerName ?? this.farmerName
      ..bankcardTypeNo = bankcardTypeNo ?? this.bankcardTypeNo
      ..isDefault = isDefault ?? this.isDefault
      ..bankcardPhotoPath = bankcardPhotoPath ?? this.bankcardPhotoPath
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditDId = auditDId ?? this.auditDId
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEId = auditEId ?? this.auditEId
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFId = auditFId ?? this.auditFId
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGId = auditGId ?? this.auditGId
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHId = auditHId ?? this.auditHId
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..auditLevel = auditLevel ?? this.auditLevel
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..organizationNo = organizationNo ?? this.organizationNo
      ..organizationName = organizationName ?? this.organizationName
      ..network = network ?? this.network
      ..lineNumber = lineNumber ?? this.lineNumber;
  }
}
