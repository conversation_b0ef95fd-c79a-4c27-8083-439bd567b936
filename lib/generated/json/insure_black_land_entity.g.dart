import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_black_land_entity.dart';

InsureBlackLandEntity $InsureBlackLandEntityFromJson(
    Map<String, dynamic> json) {
  final InsureBlackLandEntity insureBlackLandEntity = InsureBlackLandEntity();
  final int? isBlack = jsonConvert.convert<int>(json['isBlack']);
  if (isBlack != null) {
    insureBlackLandEntity.isBlack = isBlack;
  }
  final List<String>? blackPlotNos = (json['blackPlotNos'] as List<dynamic>?)
      ?.map((e) => jsonConvert.convert<String>(e) as String)
      .toList();
  if (blackPlotNos != null) {
    insureBlackLandEntity.blackPlotNos = blackPlotNos;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    insureBlackLandEntity.message = message;
  }
  return insureBlackLandEntity;
}

Map<String, dynamic> $InsureBlackLandEntityToJson(
    InsureBlackLandEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['isBlack'] = entity.isBlack;
  data['blackPlotNos'] = entity.blackPlotNos;
  data['message'] = entity.message;
  return data;
}

extension InsureBlackLandEntityExtension on InsureBlackLandEntity {
  InsureBlackLandEntity copyWith({
    int? isBlack,
    List<String>? blackPlotNos,
    String? message,
  }) {
    return InsureBlackLandEntity()
      ..isBlack = isBlack ?? this.isBlack
      ..blackPlotNos = blackPlotNos ?? this.blackPlotNos
      ..message = message ?? this.message;
  }
}
