import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/news_of_subsidy_entity.dart';

NewsOfSubsidyEntity $NewsOfSubsidyEntityFromJson(Map<String, dynamic> json) {
  final NewsOfSubsidyEntity newsOfSubsidyEntity = NewsOfSubsidyEntity();
  final List<NewsOfSubsidyRecords>? records = (json['records']
          as List<dynamic>?)
      ?.map((e) =>
          jsonConvert.convert<NewsOfSubsidyRecords>(e) as NewsOfSubsidyRecords)
      .toList();
  if (records != null) {
    newsOfSubsidyEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    newsOfSubsidyEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    newsOfSubsidyEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    newsOfSubsidyEntity.current = current;
  }
  final bool? optimizeCountSql =
      jsonConvert.convert<bool>(json['optimizeCountSql']);
  if (optimizeCountSql != null) {
    newsOfSubsidyEntity.optimizeCountSql = optimizeCountSql;
  }
  final bool? hitCount = jsonConvert.convert<bool>(json['hitCount']);
  if (hitCount != null) {
    newsOfSubsidyEntity.hitCount = hitCount;
  }
  final int? countId = jsonConvert.convert<int>(json['countId']);
  if (countId != null) {
    newsOfSubsidyEntity.countId = countId;
  }
  final int? maxLimit = jsonConvert.convert<int>(json['maxLimit']);
  if (maxLimit != null) {
    newsOfSubsidyEntity.maxLimit = maxLimit;
  }
  final bool? searchCount = jsonConvert.convert<bool>(json['searchCount']);
  if (searchCount != null) {
    newsOfSubsidyEntity.searchCount = searchCount;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    newsOfSubsidyEntity.pages = pages;
  }
  return newsOfSubsidyEntity;
}

Map<String, dynamic> $NewsOfSubsidyEntityToJson(NewsOfSubsidyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records?.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['optimizeCountSql'] = entity.optimizeCountSql;
  data['hitCount'] = entity.hitCount;
  data['countId'] = entity.countId;
  data['maxLimit'] = entity.maxLimit;
  data['searchCount'] = entity.searchCount;
  data['pages'] = entity.pages;
  return data;
}

extension NewsOfSubsidyEntityExtension on NewsOfSubsidyEntity {
  NewsOfSubsidyEntity copyWith({
    List<NewsOfSubsidyRecords>? records,
    int? total,
    int? size,
    int? current,
    bool? optimizeCountSql,
    bool? hitCount,
    int? countId,
    int? maxLimit,
    bool? searchCount,
    int? pages,
  }) {
    return NewsOfSubsidyEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..optimizeCountSql = optimizeCountSql ?? this.optimizeCountSql
      ..hitCount = hitCount ?? this.hitCount
      ..countId = countId ?? this.countId
      ..maxLimit = maxLimit ?? this.maxLimit
      ..searchCount = searchCount ?? this.searchCount
      ..pages = pages ?? this.pages;
  }
}

NewsOfSubsidyRecords $NewsOfSubsidyRecordsFromJson(Map<String, dynamic> json) {
  final NewsOfSubsidyRecords newsOfSubsidyRecords = NewsOfSubsidyRecords();
  final String? releaseInfoId =
      jsonConvert.convert<String>(json['releaseInfoId']);
  if (releaseInfoId != null) {
    newsOfSubsidyRecords.releaseInfoId = releaseInfoId;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    newsOfSubsidyRecords.title = title;
  }
  final String? orgCode = jsonConvert.convert<String>(json['orgCode']);
  if (orgCode != null) {
    newsOfSubsidyRecords.orgCode = orgCode;
  }
  final String? orgName = jsonConvert.convert<String>(json['orgName']);
  if (orgName != null) {
    newsOfSubsidyRecords.orgName = orgName;
  }
  final int? columnLevelId = jsonConvert.convert<int>(json['columnLevelId']);
  if (columnLevelId != null) {
    newsOfSubsidyRecords.columnLevelId = columnLevelId;
  }
  final String? columnLevelName =
      jsonConvert.convert<String>(json['columnLevelName']);
  if (columnLevelName != null) {
    newsOfSubsidyRecords.columnLevelName = columnLevelName;
  }
  final int? orders = jsonConvert.convert<int>(json['orders']);
  if (orders != null) {
    newsOfSubsidyRecords.orders = orders;
  }
  final int? readNum = jsonConvert.convert<int>(json['readNum']);
  if (readNum != null) {
    newsOfSubsidyRecords.readNum = readNum;
  }
  final String? manuscriptStatus =
      jsonConvert.convert<String>(json['manuscriptStatus']);
  if (manuscriptStatus != null) {
    newsOfSubsidyRecords.manuscriptStatus = manuscriptStatus;
  }
  final String? thumbUrl = jsonConvert.convert<String>(json['thumbUrl']);
  if (thumbUrl != null) {
    newsOfSubsidyRecords.thumbUrl = thumbUrl;
  }
  final int? releaseBy = jsonConvert.convert<int>(json['releaseBy']);
  if (releaseBy != null) {
    newsOfSubsidyRecords.releaseBy = releaseBy;
  }
  final String? releaseByName =
      jsonConvert.convert<String>(json['releaseByName']);
  if (releaseByName != null) {
    newsOfSubsidyRecords.releaseByName = releaseByName;
  }
  final int? releaseTime = jsonConvert.convert<int>(json['releaseTime']);
  if (releaseTime != null) {
    newsOfSubsidyRecords.releaseTime = releaseTime;
  }
  final String? withdrawBy = jsonConvert.convert<String>(json['withdrawBy']);
  if (withdrawBy != null) {
    newsOfSubsidyRecords.withdrawBy = withdrawBy;
  }
  final String? withdrawByName =
      jsonConvert.convert<String>(json['withdrawByName']);
  if (withdrawByName != null) {
    newsOfSubsidyRecords.withdrawByName = withdrawByName;
  }
  final String? withdrawTime =
      jsonConvert.convert<String>(json['withdrawTime']);
  if (withdrawTime != null) {
    newsOfSubsidyRecords.withdrawTime = withdrawTime;
  }
  final String? deleteBy = jsonConvert.convert<String>(json['deleteBy']);
  if (deleteBy != null) {
    newsOfSubsidyRecords.deleteBy = deleteBy;
  }
  final String? deleteByName =
      jsonConvert.convert<String>(json['deleteByName']);
  if (deleteByName != null) {
    newsOfSubsidyRecords.deleteByName = deleteByName;
  }
  final String? deleteTime = jsonConvert.convert<String>(json['deleteTime']);
  if (deleteTime != null) {
    newsOfSubsidyRecords.deleteTime = deleteTime;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    newsOfSubsidyRecords.remark = remark;
  }
  final int? createBy = jsonConvert.convert<int>(json['createBy']);
  if (createBy != null) {
    newsOfSubsidyRecords.createBy = createBy;
  }
  final String? createByName =
      jsonConvert.convert<String>(json['createByName']);
  if (createByName != null) {
    newsOfSubsidyRecords.createByName = createByName;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    newsOfSubsidyRecords.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    newsOfSubsidyRecords.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    newsOfSubsidyRecords.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    newsOfSubsidyRecords.statusCd = statusCd;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    newsOfSubsidyRecords.params = params;
  }
  final String? upDownFlag = jsonConvert.convert<String>(json['upDownFlag']);
  if (upDownFlag != null) {
    newsOfSubsidyRecords.upDownFlag = upDownFlag;
  }
  final String? systemCode = jsonConvert.convert<String>(json['systemCode']);
  if (systemCode != null) {
    newsOfSubsidyRecords.systemCode = systemCode;
  }
  return newsOfSubsidyRecords;
}

Map<String, dynamic> $NewsOfSubsidyRecordsToJson(NewsOfSubsidyRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['releaseInfoId'] = entity.releaseInfoId;
  data['title'] = entity.title;
  data['orgCode'] = entity.orgCode;
  data['orgName'] = entity.orgName;
  data['columnLevelId'] = entity.columnLevelId;
  data['columnLevelName'] = entity.columnLevelName;
  data['orders'] = entity.orders;
  data['readNum'] = entity.readNum;
  data['manuscriptStatus'] = entity.manuscriptStatus;
  data['thumbUrl'] = entity.thumbUrl;
  data['releaseBy'] = entity.releaseBy;
  data['releaseByName'] = entity.releaseByName;
  data['releaseTime'] = entity.releaseTime;
  data['withdrawBy'] = entity.withdrawBy;
  data['withdrawByName'] = entity.withdrawByName;
  data['withdrawTime'] = entity.withdrawTime;
  data['deleteBy'] = entity.deleteBy;
  data['deleteByName'] = entity.deleteByName;
  data['deleteTime'] = entity.deleteTime;
  data['remark'] = entity.remark;
  data['createBy'] = entity.createBy;
  data['createByName'] = entity.createByName;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['params'] = entity.params;
  data['upDownFlag'] = entity.upDownFlag;
  data['systemCode'] = entity.systemCode;
  return data;
}

extension NewsOfSubsidyRecordsExtension on NewsOfSubsidyRecords {
  NewsOfSubsidyRecords copyWith({
    String? releaseInfoId,
    String? title,
    String? orgCode,
    String? orgName,
    int? columnLevelId,
    String? columnLevelName,
    int? orders,
    int? readNum,
    String? manuscriptStatus,
    String? thumbUrl,
    int? releaseBy,
    String? releaseByName,
    int? releaseTime,
    String? withdrawBy,
    String? withdrawByName,
    String? withdrawTime,
    String? deleteBy,
    String? deleteByName,
    String? deleteTime,
    String? remark,
    int? createBy,
    String? createByName,
    int? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? params,
    String? upDownFlag,
    String? systemCode,
  }) {
    return NewsOfSubsidyRecords()
      ..releaseInfoId = releaseInfoId ?? this.releaseInfoId
      ..title = title ?? this.title
      ..orgCode = orgCode ?? this.orgCode
      ..orgName = orgName ?? this.orgName
      ..columnLevelId = columnLevelId ?? this.columnLevelId
      ..columnLevelName = columnLevelName ?? this.columnLevelName
      ..orders = orders ?? this.orders
      ..readNum = readNum ?? this.readNum
      ..manuscriptStatus = manuscriptStatus ?? this.manuscriptStatus
      ..thumbUrl = thumbUrl ?? this.thumbUrl
      ..releaseBy = releaseBy ?? this.releaseBy
      ..releaseByName = releaseByName ?? this.releaseByName
      ..releaseTime = releaseTime ?? this.releaseTime
      ..withdrawBy = withdrawBy ?? this.withdrawBy
      ..withdrawByName = withdrawByName ?? this.withdrawByName
      ..withdrawTime = withdrawTime ?? this.withdrawTime
      ..deleteBy = deleteBy ?? this.deleteBy
      ..deleteByName = deleteByName ?? this.deleteByName
      ..deleteTime = deleteTime ?? this.deleteTime
      ..remark = remark ?? this.remark
      ..createBy = createBy ?? this.createBy
      ..createByName = createByName ?? this.createByName
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..params = params ?? this.params
      ..upDownFlag = upDownFlag ?? this.upDownFlag
      ..systemCode = systemCode ?? this.systemCode;
  }
}
