import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_item_info_entity.dart';

SubsidyItemInfoEntity $SubsidyItemInfoEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyItemInfoEntity subsidyItemInfoEntity = SubsidyItemInfoEntity();
  final List<SubsidyItemInfoRecords>? records =
      (json['records'] as List<dynamic>?)
          ?.map((e) => jsonConvert.convert<SubsidyItemInfoRecords>(e)
              as SubsidyItemInfoRecords)
          .toList();
  if (records != null) {
    subsidyItemInfoEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    subsidyItemInfoEntity.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    subsidyItemInfoEntity.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    subsidyItemInfoEntity.current = current;
  }
  final List<dynamic>? orders =
      (json['orders'] as List<dynamic>?)?.map((e) => e).toList();
  if (orders != null) {
    subsidyItemInfoEntity.orders = orders;
  }
  final bool? optimizeCountSql =
      jsonConvert.convert<bool>(json['optimizeCountSql']);
  if (optimizeCountSql != null) {
    subsidyItemInfoEntity.optimizeCountSql = optimizeCountSql;
  }
  final bool? hitCount = jsonConvert.convert<bool>(json['hitCount']);
  if (hitCount != null) {
    subsidyItemInfoEntity.hitCount = hitCount;
  }
  final String? countId = jsonConvert.convert<String>(json['countId']);
  if (countId != null) {
    subsidyItemInfoEntity.countId = countId;
  }
  final String? maxLimit = jsonConvert.convert<String>(json['maxLimit']);
  if (maxLimit != null) {
    subsidyItemInfoEntity.maxLimit = maxLimit;
  }
  final bool? searchCount = jsonConvert.convert<bool>(json['searchCount']);
  if (searchCount != null) {
    subsidyItemInfoEntity.searchCount = searchCount;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    subsidyItemInfoEntity.pages = pages;
  }
  return subsidyItemInfoEntity;
}

Map<String, dynamic> $SubsidyItemInfoEntityToJson(
    SubsidyItemInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records?.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['orders'] = entity.orders;
  data['optimizeCountSql'] = entity.optimizeCountSql;
  data['hitCount'] = entity.hitCount;
  data['countId'] = entity.countId;
  data['maxLimit'] = entity.maxLimit;
  data['searchCount'] = entity.searchCount;
  data['pages'] = entity.pages;
  return data;
}

extension SubsidyItemInfoEntityExtension on SubsidyItemInfoEntity {
  SubsidyItemInfoEntity copyWith({
    List<SubsidyItemInfoRecords>? records,
    int? total,
    int? size,
    int? current,
    List<dynamic>? orders,
    bool? optimizeCountSql,
    bool? hitCount,
    String? countId,
    String? maxLimit,
    bool? searchCount,
    int? pages,
  }) {
    return SubsidyItemInfoEntity()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..orders = orders ?? this.orders
      ..optimizeCountSql = optimizeCountSql ?? this.optimizeCountSql
      ..hitCount = hitCount ?? this.hitCount
      ..countId = countId ?? this.countId
      ..maxLimit = maxLimit ?? this.maxLimit
      ..searchCount = searchCount ?? this.searchCount
      ..pages = pages ?? this.pages;
  }
}

SubsidyItemInfoRecords $SubsidyItemInfoRecordsFromJson(
    Map<String, dynamic> json) {
  final SubsidyItemInfoRecords subsidyItemInfoRecords =
      SubsidyItemInfoRecords();
  final int? subsidyApplyDetailSubId =
      jsonConvert.convert<int>(json['subsidyApplyDetailSubId']);
  if (subsidyApplyDetailSubId != null) {
    subsidyItemInfoRecords.subsidyApplyDetailSubId = subsidyApplyDetailSubId;
  }
  final int? subsidyApplyDetailId =
      jsonConvert.convert<int>(json['subsidyApplyDetailId']);
  if (subsidyApplyDetailId != null) {
    subsidyItemInfoRecords.subsidyApplyDetailId = subsidyApplyDetailId;
  }
  final String? subsidyCropCode =
      jsonConvert.convert<String>(json['subsidyCropCode']);
  if (subsidyCropCode != null) {
    subsidyItemInfoRecords.subsidyCropCode = subsidyCropCode;
  }
  final String? landNumber = jsonConvert.convert<String>(json['landNumber']);
  if (landNumber != null) {
    subsidyItemInfoRecords.landNumber = landNumber;
  }
  final num? subsidyArea1 = jsonConvert.convert<num>(json['subsidyArea1']);
  if (subsidyArea1 != null) {
    subsidyItemInfoRecords.subsidyArea1 = subsidyArea1;
  }
  final num? subsidyArea2 = jsonConvert.convert<num>(json['subsidyArea2']);
  if (subsidyArea2 != null) {
    subsidyItemInfoRecords.subsidyArea2 = subsidyArea2;
  }
  final int? createBy = jsonConvert.convert<int>(json['createBy']);
  if (createBy != null) {
    subsidyItemInfoRecords.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    subsidyItemInfoRecords.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    subsidyItemInfoRecords.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    subsidyItemInfoRecords.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    subsidyItemInfoRecords.statusCd = statusCd;
  }
  final int? subsidyYear = jsonConvert.convert<int>(json['subsidyYear']);
  if (subsidyYear != null) {
    subsidyItemInfoRecords.subsidyYear = subsidyYear;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    subsidyItemInfoRecords.organizationNo = organizationNo;
  }
  final int? subsidyConfigId =
      jsonConvert.convert<int>(json['subsidyConfigId']);
  if (subsidyConfigId != null) {
    subsidyItemInfoRecords.subsidyConfigId = subsidyConfigId;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    subsidyItemInfoRecords.organizationName = organizationName;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    subsidyItemInfoRecords.remark = remark;
  }
  final String? serialNumber =
      jsonConvert.convert<String>(json['serialNumber']);
  if (serialNumber != null) {
    subsidyItemInfoRecords.serialNumber = serialNumber;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    subsidyItemInfoRecords.params = params;
  }
  return subsidyItemInfoRecords;
}

Map<String, dynamic> $SubsidyItemInfoRecordsToJson(
    SubsidyItemInfoRecords entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyApplyDetailSubId'] = entity.subsidyApplyDetailSubId;
  data['subsidyApplyDetailId'] = entity.subsidyApplyDetailId;
  data['subsidyCropCode'] = entity.subsidyCropCode;
  data['landNumber'] = entity.landNumber;
  data['subsidyArea1'] = entity.subsidyArea1;
  data['subsidyArea2'] = entity.subsidyArea2;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['subsidyYear'] = entity.subsidyYear;
  data['organizationNo'] = entity.organizationNo;
  data['subsidyConfigId'] = entity.subsidyConfigId;
  data['organizationName'] = entity.organizationName;
  data['remark'] = entity.remark;
  data['serialNumber'] = entity.serialNumber;
  data['params'] = entity.params;
  return data;
}

extension SubsidyItemInfoRecordsExtension on SubsidyItemInfoRecords {
  SubsidyItemInfoRecords copyWith({
    int? subsidyApplyDetailSubId,
    int? subsidyApplyDetailId,
    String? subsidyCropCode,
    String? landNumber,
    num? subsidyArea1,
    num? subsidyArea2,
    int? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    int? subsidyYear,
    String? organizationNo,
    int? subsidyConfigId,
    String? organizationName,
    String? remark,
    String? serialNumber,
    String? params,
  }) {
    return SubsidyItemInfoRecords()
      ..subsidyApplyDetailSubId =
          subsidyApplyDetailSubId ?? this.subsidyApplyDetailSubId
      ..subsidyApplyDetailId = subsidyApplyDetailId ?? this.subsidyApplyDetailId
      ..subsidyCropCode = subsidyCropCode ?? this.subsidyCropCode
      ..landNumber = landNumber ?? this.landNumber
      ..subsidyArea1 = subsidyArea1 ?? this.subsidyArea1
      ..subsidyArea2 = subsidyArea2 ?? this.subsidyArea2
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..subsidyYear = subsidyYear ?? this.subsidyYear
      ..organizationNo = organizationNo ?? this.organizationNo
      ..subsidyConfigId = subsidyConfigId ?? this.subsidyConfigId
      ..organizationName = organizationName ?? this.organizationName
      ..remark = remark ?? this.remark
      ..serialNumber = serialNumber ?? this.serialNumber
      ..params = params ?? this.params;
  }
}
