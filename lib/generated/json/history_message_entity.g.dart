import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/history_message_entity.dart';

HistoryMessageEntity $HistoryMessageEntityFromJson(Map<String, dynamic> json) {
  final HistoryMessageEntity historyMessageEntity = HistoryMessageEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    historyMessageEntity.id = id;
  }
  final String? conversationId = jsonConvert.convert<String>(
      json['conversation_id']);
  if (conversationId != null) {
    historyMessageEntity.conversationId = conversationId;
  }
  final HistoryMessageInputs? inputs = jsonConvert.convert<
      HistoryMessageInputs>(json['inputs']);
  if (inputs != null) {
    historyMessageEntity.inputs = inputs;
  }
  final String? query = jsonConvert.convert<String>(json['query']);
  if (query != null) {
    historyMessageEntity.query = query;
  }
  final String? answer = jsonConvert.convert<String>(json['answer']);
  if (answer != null) {
    historyMessageEntity.answer = answer;
  }
  final List<dynamic>? messageFiles = (json['message_files'] as List<dynamic>?)
      ?.map(
          (e) => e)
      .toList();
  if (messageFiles != null) {
    historyMessageEntity.messageFiles = messageFiles;
  }
  final dynamic feedback = json['feedback'];
  if (feedback != null) {
    historyMessageEntity.feedback = feedback;
  }
  final List<
      HistoryMessageRetrieverResources>? retrieverResources = (json['retriever_resources'] as List<
      dynamic>?)?.map(
          (e) =>
      jsonConvert.convert<HistoryMessageRetrieverResources>(
          e) as HistoryMessageRetrieverResources).toList();
  if (retrieverResources != null) {
    historyMessageEntity.retrieverResources = retrieverResources;
  }
  final int? createdAt = jsonConvert.convert<int>(json['created_at']);
  if (createdAt != null) {
    historyMessageEntity.createdAt = createdAt;
  }
  final List<dynamic>? agentThoughts = (json['agent_thoughts'] as List<
      dynamic>?)?.map(
          (e) => e).toList();
  if (agentThoughts != null) {
    historyMessageEntity.agentThoughts = agentThoughts;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    historyMessageEntity.status = status;
  }
  final dynamic error = json['error'];
  if (error != null) {
    historyMessageEntity.error = error;
  }
  return historyMessageEntity;
}

Map<String, dynamic> $HistoryMessageEntityToJson(HistoryMessageEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['conversation_id'] = entity.conversationId;
  data['inputs'] = entity.inputs?.toJson();
  data['query'] = entity.query;
  data['answer'] = entity.answer;
  data['message_files'] = entity.messageFiles;
  data['feedback'] = entity.feedback;
  data['retriever_resources'] =
      entity.retrieverResources?.map((v) => v.toJson()).toList();
  data['created_at'] = entity.createdAt;
  data['agent_thoughts'] = entity.agentThoughts;
  data['status'] = entity.status;
  data['error'] = entity.error;
  return data;
}

extension HistoryMessageEntityExtension on HistoryMessageEntity {
  HistoryMessageEntity copyWith({
    String? id,
    String? conversationId,
    HistoryMessageInputs? inputs,
    String? query,
    String? answer,
    List<dynamic>? messageFiles,
    dynamic feedback,
    List<HistoryMessageRetrieverResources>? retrieverResources,
    int? createdAt,
    List<dynamic>? agentThoughts,
    String? status,
    dynamic error,
  }) {
    return HistoryMessageEntity()
      ..id = id ?? this.id
      ..conversationId = conversationId ?? this.conversationId
      ..inputs = inputs ?? this.inputs
      ..query = query ?? this.query
      ..answer = answer ?? this.answer
      ..messageFiles = messageFiles ?? this.messageFiles
      ..feedback = feedback ?? this.feedback
      ..retrieverResources = retrieverResources ?? this.retrieverResources
      ..createdAt = createdAt ?? this.createdAt
      ..agentThoughts = agentThoughts ?? this.agentThoughts
      ..status = status ?? this.status
      ..error = error ?? this.error;
  }
}

HistoryMessageInputs $HistoryMessageInputsFromJson(Map<String, dynamic> json) {
  final HistoryMessageInputs historyMessageInputs = HistoryMessageInputs();
  return historyMessageInputs;
}

Map<String, dynamic> $HistoryMessageInputsToJson(HistoryMessageInputs entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension HistoryMessageInputsExtension on HistoryMessageInputs {
}

HistoryMessageRetrieverResources $HistoryMessageRetrieverResourcesFromJson(
    Map<String, dynamic> json) {
  final HistoryMessageRetrieverResources historyMessageRetrieverResources = HistoryMessageRetrieverResources();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    historyMessageRetrieverResources.id = id;
  }
  final String? messageId = jsonConvert.convert<String>(json['message_id']);
  if (messageId != null) {
    historyMessageRetrieverResources.messageId = messageId;
  }
  final int? position = jsonConvert.convert<int>(json['position']);
  if (position != null) {
    historyMessageRetrieverResources.position = position;
  }
  final String? datasetId = jsonConvert.convert<String>(json['dataset_id']);
  if (datasetId != null) {
    historyMessageRetrieverResources.datasetId = datasetId;
  }
  final String? datasetName = jsonConvert.convert<String>(json['dataset_name']);
  if (datasetName != null) {
    historyMessageRetrieverResources.datasetName = datasetName;
  }
  final String? documentId = jsonConvert.convert<String>(json['document_id']);
  if (documentId != null) {
    historyMessageRetrieverResources.documentId = documentId;
  }
  final String? documentName = jsonConvert.convert<String>(
      json['document_name']);
  if (documentName != null) {
    historyMessageRetrieverResources.documentName = documentName;
  }
  final String? dataSourceType = jsonConvert.convert<String>(
      json['data_source_type']);
  if (dataSourceType != null) {
    historyMessageRetrieverResources.dataSourceType = dataSourceType;
  }
  final String? segmentId = jsonConvert.convert<String>(json['segment_id']);
  if (segmentId != null) {
    historyMessageRetrieverResources.segmentId = segmentId;
  }
  final double? score = jsonConvert.convert<double>(json['score']);
  if (score != null) {
    historyMessageRetrieverResources.score = score;
  }
  final int? hitCount = jsonConvert.convert<int>(json['hit_count']);
  if (hitCount != null) {
    historyMessageRetrieverResources.hitCount = hitCount;
  }
  final int? wordCount = jsonConvert.convert<int>(json['word_count']);
  if (wordCount != null) {
    historyMessageRetrieverResources.wordCount = wordCount;
  }
  final int? segmentPosition = jsonConvert.convert<int>(
      json['segment_position']);
  if (segmentPosition != null) {
    historyMessageRetrieverResources.segmentPosition = segmentPosition;
  }
  final dynamic indexNodeHash = json['index_node_hash'];
  if (indexNodeHash != null) {
    historyMessageRetrieverResources.indexNodeHash = indexNodeHash;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    historyMessageRetrieverResources.content = content;
  }
  final int? createdAt = jsonConvert.convert<int>(json['created_at']);
  if (createdAt != null) {
    historyMessageRetrieverResources.createdAt = createdAt;
  }
  return historyMessageRetrieverResources;
}

Map<String, dynamic> $HistoryMessageRetrieverResourcesToJson(
    HistoryMessageRetrieverResources entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['message_id'] = entity.messageId;
  data['position'] = entity.position;
  data['dataset_id'] = entity.datasetId;
  data['dataset_name'] = entity.datasetName;
  data['document_id'] = entity.documentId;
  data['document_name'] = entity.documentName;
  data['data_source_type'] = entity.dataSourceType;
  data['segment_id'] = entity.segmentId;
  data['score'] = entity.score;
  data['hit_count'] = entity.hitCount;
  data['word_count'] = entity.wordCount;
  data['segment_position'] = entity.segmentPosition;
  data['index_node_hash'] = entity.indexNodeHash;
  data['content'] = entity.content;
  data['created_at'] = entity.createdAt;
  return data;
}

extension HistoryMessageRetrieverResourcesExtension on HistoryMessageRetrieverResources {
  HistoryMessageRetrieverResources copyWith({
    String? id,
    String? messageId,
    int? position,
    String? datasetId,
    String? datasetName,
    String? documentId,
    String? documentName,
    String? dataSourceType,
    String? segmentId,
    double? score,
    int? hitCount,
    int? wordCount,
    int? segmentPosition,
    dynamic indexNodeHash,
    String? content,
    int? createdAt,
  }) {
    return HistoryMessageRetrieverResources()
      ..id = id ?? this.id
      ..messageId = messageId ?? this.messageId
      ..position = position ?? this.position
      ..datasetId = datasetId ?? this.datasetId
      ..datasetName = datasetName ?? this.datasetName
      ..documentId = documentId ?? this.documentId
      ..documentName = documentName ?? this.documentName
      ..dataSourceType = dataSourceType ?? this.dataSourceType
      ..segmentId = segmentId ?? this.segmentId
      ..score = score ?? this.score
      ..hitCount = hitCount ?? this.hitCount
      ..wordCount = wordCount ?? this.wordCount
      ..segmentPosition = segmentPosition ?? this.segmentPosition
      ..indexNodeHash = indexNodeHash ?? this.indexNodeHash
      ..content = content ?? this.content
      ..createdAt = createdAt ?? this.createdAt;
  }
}