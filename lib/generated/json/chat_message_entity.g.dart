import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/chat_message_entity.dart';

ChatMessageEntity $ChatMessageEntityFromJson(Map<String, dynamic> json) {
  final ChatMessageEntity chatMessageEntity = ChatMessageEntity();
  final String? event = jsonConvert.convert<String>(json['event']);
  if (event != null) {
    chatMessageEntity.event = event;
  }
  final String? conversationId =
      jsonConvert.convert<String>(json['conversation_id']);
  if (conversationId != null) {
    chatMessageEntity.conversationId = conversationId;
  }
  final String? messageId = jsonConvert.convert<String>(json['message_id']);
  if (messageId != null) {
    chatMessageEntity.messageId = messageId;
  }
  final String? createdAt = jsonConvert.convert<String>(json['created_at']);
  if (createdAt != null) {
    chatMessageEntity.createdAt = createdAt;
  }
  final String? taskId = jsonConvert.convert<String>(json['task_id']);
  if (taskId != null) {
    chatMessageEntity.taskId = taskId;
  }
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    chatMessageEntity.id = id;
  }
  final String? answer = jsonConvert.convert<String>(json['answer']);
  if (answer != null) {
    chatMessageEntity.answer = answer;
  }
  final String? metadata = jsonConvert.convert<String>(json['metadata']);
  if (metadata != null) {
    chatMessageEntity.metadata = metadata;
  }
  return chatMessageEntity;
}

Map<String, dynamic> $ChatMessageEntityToJson(ChatMessageEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['event'] = entity.event;
  data['conversation_id'] = entity.conversationId;
  data['message_id'] = entity.messageId;
  data['created_at'] = entity.createdAt;
  data['task_id'] = entity.taskId;
  data['id'] = entity.id;
  data['answer'] = entity.answer;
  data['metadata'] = entity.metadata;
  return data;
}

extension ChatMessageEntityExtension on ChatMessageEntity {
  ChatMessageEntity copyWith({
    String? event,
    String? conversationId,
    String? messageId,
    String? createdAt,
    String? taskId,
    String? id,
    String? answer,
    String? metadata,
  }) {
    return ChatMessageEntity()
      ..event = event ?? this.event
      ..conversationId = conversationId ?? this.conversationId
      ..messageId = messageId ?? this.messageId
      ..createdAt = createdAt ?? this.createdAt
      ..taskId = taskId ?? this.taskId
      ..id = id ?? this.id
      ..answer = answer ?? this.answer
      ..metadata = metadata ?? this.metadata;
  }
}
