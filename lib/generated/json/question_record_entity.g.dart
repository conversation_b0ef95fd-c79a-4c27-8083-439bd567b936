import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/chat/question_record_entity.dart';

QuestionRecordEntity $QuestionRecordEntityFromJson(Map<String, dynamic> json) {
  final QuestionRecordEntity questionRecordEntity = QuestionRecordEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    questionRecordEntity.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    questionRecordEntity.name = name;
  }
  final QuestionRecordInputs? inputs =
      jsonConvert.convert<QuestionRecordInputs>(json['inputs']);
  if (inputs != null) {
    questionRecordEntity.inputs = inputs;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    questionRecordEntity.status = status;
  }
  final String? introduction =
      jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    questionRecordEntity.introduction = introduction;
  }
  final int? createdAt = jsonConvert.convert<int>(json['created_at']);
  if (createdAt != null) {
    questionRecordEntity.createdAt = createdAt;
  }
  return questionRecordEntity;
}

Map<String, dynamic> $QuestionRecordEntityToJson(QuestionRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['inputs'] = entity.inputs?.toJson();
  data['status'] = entity.status;
  data['introduction'] = entity.introduction;
  data['created_at'] = entity.createdAt;
  return data;
}

extension QuestionRecordEntityExtension on QuestionRecordEntity {
  QuestionRecordEntity copyWith({
    String? id,
    String? name,
    QuestionRecordInputs? inputs,
    String? status,
    String? introduction,
    int? createdAt,
  }) {
    return QuestionRecordEntity()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..inputs = inputs ?? this.inputs
      ..status = status ?? this.status
      ..introduction = introduction ?? this.introduction
      ..createdAt = createdAt ?? this.createdAt;
  }
}

QuestionRecordInputs $QuestionRecordInputsFromJson(Map<String, dynamic> json) {
  final QuestionRecordInputs questionRecordInputs = QuestionRecordInputs();
  return questionRecordInputs;
}

Map<String, dynamic> $QuestionRecordInputsToJson(QuestionRecordInputs entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension QuestionRecordInputsExtension on QuestionRecordInputs {}
