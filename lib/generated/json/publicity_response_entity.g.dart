import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity_response_entity.dart';

PublicityResponseEntity $PublicityResponseEntityFromJson(
    Map<String, dynamic> json) {
  final PublicityResponseEntity publicityResponseEntity =
      PublicityResponseEntity();
  final int? subsidyYear = jsonConvert.convert<int>(json['subsidyYear']);
  if (subsidyYear != null) {
    publicityResponseEntity.subsidyYear = subsidyYear;
  }
  final String? subsidyItemName =
      jsonConvert.convert<String>(json['subsidyItemName']);
  if (subsidyItemName != null) {
    publicityResponseEntity.subsidyItemName = subsidyItemName;
  }
  final PublicityDetailItemDetails? details =
      jsonConvert.convert<PublicityDetailItemDetails>(json['details']);
  if (details != null) {
    publicityResponseEntity.details = details;
  }
  return publicityResponseEntity;
}

Map<String, dynamic> $PublicityResponseEntityToJson(
    PublicityResponseEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyYear'] = entity.subsidyYear;
  data['subsidyItemName'] = entity.subsidyItemName;
  data['details'] = entity.details?.toJson();
  return data;
}

extension PublicityResponseEntityExtension on PublicityResponseEntity {
  PublicityResponseEntity copyWith({
    int? subsidyYear,
    String? subsidyItemName,
    PublicityDetailItemDetails? details,
  }) {
    return PublicityResponseEntity()
      ..subsidyYear = subsidyYear ?? this.subsidyYear
      ..subsidyItemName = subsidyItemName ?? this.subsidyItemName
      ..details = details ?? this.details;
  }
}

PublicityDetailItemDetails $PublicityDetailItemDetailsFromJson(
    Map<String, dynamic> json) {
  final PublicityDetailItemDetails publicityDetailItemDetails =
      PublicityDetailItemDetails();
  final List<dynamic>? records =
      (json['records'] as List<dynamic>?)?.map((e) => e).toList();
  if (records != null) {
    publicityDetailItemDetails.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    publicityDetailItemDetails.total = total;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    publicityDetailItemDetails.size = size;
  }
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    publicityDetailItemDetails.current = current;
  }
  final bool? optimizeCountSql =
      jsonConvert.convert<bool>(json['optimizeCountSql']);
  if (optimizeCountSql != null) {
    publicityDetailItemDetails.optimizeCountSql = optimizeCountSql;
  }
  final bool? hitCount = jsonConvert.convert<bool>(json['hitCount']);
  if (hitCount != null) {
    publicityDetailItemDetails.hitCount = hitCount;
  }
  final String? countId = jsonConvert.convert<String>(json['countId']);
  if (countId != null) {
    publicityDetailItemDetails.countId = countId;
  }
  final String? maxLimit = jsonConvert.convert<String>(json['maxLimit']);
  if (maxLimit != null) {
    publicityDetailItemDetails.maxLimit = maxLimit;
  }
  final bool? searchCount = jsonConvert.convert<bool>(json['searchCount']);
  if (searchCount != null) {
    publicityDetailItemDetails.searchCount = searchCount;
  }
  final int? pages = jsonConvert.convert<int>(json['pages']);
  if (pages != null) {
    publicityDetailItemDetails.pages = pages;
  }
  return publicityDetailItemDetails;
}

Map<String, dynamic> $PublicityDetailItemDetailsToJson(
    PublicityDetailItemDetails entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['records'] = entity.records;
  data['total'] = entity.total;
  data['size'] = entity.size;
  data['current'] = entity.current;
  data['optimizeCountSql'] = entity.optimizeCountSql;
  data['hitCount'] = entity.hitCount;
  data['countId'] = entity.countId;
  data['maxLimit'] = entity.maxLimit;
  data['searchCount'] = entity.searchCount;
  data['pages'] = entity.pages;
  return data;
}

extension PublicityDetailItemDetailsExtension on PublicityDetailItemDetails {
  PublicityDetailItemDetails copyWith({
    List<dynamic>? records,
    int? total,
    int? size,
    int? current,
    bool? optimizeCountSql,
    bool? hitCount,
    String? countId,
    String? maxLimit,
    bool? searchCount,
    int? pages,
  }) {
    return PublicityDetailItemDetails()
      ..records = records ?? this.records
      ..total = total ?? this.total
      ..size = size ?? this.size
      ..current = current ?? this.current
      ..optimizeCountSql = optimizeCountSql ?? this.optimizeCountSql
      ..hitCount = hitCount ?? this.hitCount
      ..countId = countId ?? this.countId
      ..maxLimit = maxLimit ?? this.maxLimit
      ..searchCount = searchCount ?? this.searchCount
      ..pages = pages ?? this.pages;
  }
}
