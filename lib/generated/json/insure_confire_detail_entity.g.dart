import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_confire_detail_entity.dart';

InsureConfirmDetailEntity $InsureConfirmDetailEntityFromJson(
    Map<String, dynamic> json) {
  final InsureConfirmDetailEntity insureConfirmDetailEntity =
      InsureConfirmDetailEntity();
  final int? insureConfId = jsonConvert.convert<int>(json['insureConfId']);
  if (insureConfId != null) {
    insureConfirmDetailEntity.insureConfId = insureConfId;
  }
  final String? statYear = jsonConvert.convert<String>(json['statYear']);
  if (statYear != null) {
    insureConfirmDetailEntity.statYear = statYear;
  }
  final String? personName = jsonConvert.convert<String>(json['personName']);
  if (personName != null) {
    insureConfirmDetailEntity.personName = personName;
  }
  final String? telephone = jsonConvert.convert<String>(json['telephone']);
  if (telephone != null) {
    insureConfirmDetailEntity.telephone = telephone;
  }
  final String? certNo = jsonConvert.convert<String>(json['certNo']);
  if (certNo != null) {
    insureConfirmDetailEntity.certNo = certNo;
  }
  final String? certBeginDate =
      jsonConvert.convert<String>(json['certBeginDate']);
  if (certBeginDate != null) {
    insureConfirmDetailEntity.certBeginDate = certBeginDate;
  }
  final String? certExpDate = jsonConvert.convert<String>(json['certExpDate']);
  if (certExpDate != null) {
    insureConfirmDetailEntity.certExpDate = certExpDate;
  }
  final String? certFrontUrl =
      jsonConvert.convert<String>(json['certFrontUrl']);
  if (certFrontUrl != null) {
    insureConfirmDetailEntity.certFrontUrl = certFrontUrl;
  }
  final String? certBackUrl = jsonConvert.convert<String>(json['certBackUrl']);
  if (certBackUrl != null) {
    insureConfirmDetailEntity.certBackUrl = certBackUrl;
  }
  final String? bankcardNo = jsonConvert.convert<String>(json['bankcardNo']);
  if (bankcardNo != null) {
    insureConfirmDetailEntity.bankcardNo = bankcardNo;
  }
  final String? bankcardType =
      jsonConvert.convert<String>(json['bankcardType']);
  if (bankcardType != null) {
    insureConfirmDetailEntity.bankcardType = bankcardType;
  }
  final String? bankcardTypeNm =
      jsonConvert.convert<String>(json['bankcardTypeNm']);
  if (bankcardTypeNm != null) {
    insureConfirmDetailEntity.bankcardTypeNm = bankcardTypeNm;
  }
  final String? bankCode = jsonConvert.convert<String>(json['bankCode']);
  if (bankCode != null) {
    insureConfirmDetailEntity.bankCode = bankCode;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    insureConfirmDetailEntity.bankName = bankName;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    insureConfirmDetailEntity.remark = remark;
  }
  final int? createBy = jsonConvert.convert<int>(json['createBy']);
  if (createBy != null) {
    insureConfirmDetailEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    insureConfirmDetailEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    insureConfirmDetailEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    insureConfirmDetailEntity.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    insureConfirmDetailEntity.statusCd = statusCd;
  }
  final String? contactNo = jsonConvert.convert<String>(json['contactNo']);
  if (contactNo != null) {
    insureConfirmDetailEntity.contactNo = contactNo;
  }
  final int? contactId = jsonConvert.convert<int>(json['contactId']);
  if (contactId != null) {
    insureConfirmDetailEntity.contactId = contactId;
  }
  final String? insureNo = jsonConvert.convert<String>(json['insureNo']);
  if (insureNo != null) {
    insureConfirmDetailEntity.insureNo = insureNo;
  }
  final String? orgCode = jsonConvert.convert<String>(json['orgCode']);
  if (orgCode != null) {
    insureConfirmDetailEntity.orgCode = orgCode;
  }
  final String? orgName = jsonConvert.convert<String>(json['orgName']);
  if (orgName != null) {
    insureConfirmDetailEntity.orgName = orgName;
  }
  final String? signImgUrl = jsonConvert.convert<String>(json['signImgUrl']);
  if (signImgUrl != null) {
    insureConfirmDetailEntity.signImgUrl = signImgUrl;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    insureConfirmDetailEntity.params = params;
  }
  final List<InsureConfireDetailBindPlotInfos>? bindPlotInfos =
      (json['bindPlotInfos'] as List<dynamic>?)
          ?.map((e) => jsonConvert.convert<InsureConfireDetailBindPlotInfos>(e)
              as InsureConfireDetailBindPlotInfos)
          .toList();
  if (bindPlotInfos != null) {
    insureConfirmDetailEntity.bindPlotInfos = bindPlotInfos;
  }
  return insureConfirmDetailEntity;
}

Map<String, dynamic> $InsureConfirmDetailEntityToJson(
    InsureConfirmDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['insureConfId'] = entity.insureConfId;
  data['statYear'] = entity.statYear;
  data['personName'] = entity.personName;
  data['telephone'] = entity.telephone;
  data['certNo'] = entity.certNo;
  data['certBeginDate'] = entity.certBeginDate;
  data['certExpDate'] = entity.certExpDate;
  data['certFrontUrl'] = entity.certFrontUrl;
  data['certBackUrl'] = entity.certBackUrl;
  data['bankcardNo'] = entity.bankcardNo;
  data['bankcardType'] = entity.bankcardType;
  data['bankcardTypeNm'] = entity.bankcardTypeNm;
  data['bankCode'] = entity.bankCode;
  data['bankName'] = entity.bankName;
  data['remark'] = entity.remark;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['contactNo'] = entity.contactNo;
  data['contactId'] = entity.contactId;
  data['insureNo'] = entity.insureNo;
  data['orgCode'] = entity.orgCode;
  data['orgName'] = entity.orgName;
  data['signImgUrl'] = entity.signImgUrl;
  data['params'] = entity.params;
  data['bindPlotInfos'] = entity.bindPlotInfos?.map((v) => v.toJson()).toList();
  return data;
}

extension InsureConfirmDetailEntityExtension on InsureConfirmDetailEntity {
  InsureConfirmDetailEntity copyWith({
    int? insureConfId,
    String? statYear,
    String? personName,
    String? telephone,
    String? certNo,
    String? certBeginDate,
    String? certExpDate,
    String? certFrontUrl,
    String? certBackUrl,
    String? bankcardNo,
    String? bankcardType,
    String? bankcardTypeNm,
    String? bankCode,
    String? bankName,
    String? remark,
    int? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? contactNo,
    int? contactId,
    String? insureNo,
    String? orgCode,
    String? orgName,
    String? signImgUrl,
    String? params,
    List<InsureConfireDetailBindPlotInfos>? bindPlotInfos,
  }) {
    return InsureConfirmDetailEntity()
      ..insureConfId = insureConfId ?? this.insureConfId
      ..statYear = statYear ?? this.statYear
      ..personName = personName ?? this.personName
      ..telephone = telephone ?? this.telephone
      ..certNo = certNo ?? this.certNo
      ..certBeginDate = certBeginDate ?? this.certBeginDate
      ..certExpDate = certExpDate ?? this.certExpDate
      ..certFrontUrl = certFrontUrl ?? this.certFrontUrl
      ..certBackUrl = certBackUrl ?? this.certBackUrl
      ..bankcardNo = bankcardNo ?? this.bankcardNo
      ..bankcardType = bankcardType ?? this.bankcardType
      ..bankcardTypeNm = bankcardTypeNm ?? this.bankcardTypeNm
      ..bankCode = bankCode ?? this.bankCode
      ..bankName = bankName ?? this.bankName
      ..remark = remark ?? this.remark
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..contactNo = contactNo ?? this.contactNo
      ..contactId = contactId ?? this.contactId
      ..insureNo = insureNo ?? this.insureNo
      ..orgCode = orgCode ?? this.orgCode
      ..orgName = orgName ?? this.orgName
      ..signImgUrl = signImgUrl ?? this.signImgUrl
      ..params = params ?? this.params
      ..bindPlotInfos = bindPlotInfos ?? this.bindPlotInfos;
  }
}

InsureConfireDetailBindPlotInfos $InsureConfireDetailBindPlotInfosFromJson(
    Map<String, dynamic> json) {
  final InsureConfireDetailBindPlotInfos insureConfireDetailBindPlotInfos =
      InsureConfireDetailBindPlotInfos();
  final String? plotName = jsonConvert.convert<String>(json['plotName']);
  if (plotName != null) {
    insureConfireDetailBindPlotInfos.plotName = plotName;
  }
  final String? landType = jsonConvert.convert<String>(json['landType']);
  if (landType != null) {
    insureConfireDetailBindPlotInfos.landType = landType;
  }
  final String? plotArea = jsonConvert.convert<String>(json['plotArea']);
  if (plotArea != null) {
    insureConfireDetailBindPlotInfos.plotArea = plotArea;
  }
  final String? cropCode = jsonConvert.convert<String>(json['cropCode']);
  if (cropCode != null) {
    insureConfireDetailBindPlotInfos.cropCode = cropCode;
  }
  final String? cropName = jsonConvert.convert<String>(json['cropName']);
  if (cropName != null) {
    insureConfireDetailBindPlotInfos.cropName = cropName;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    insureConfireDetailBindPlotInfos.remark = remark;
  }
  final int? createBy = jsonConvert.convert<int>(json['createBy']);
  if (createBy != null) {
    insureConfireDetailBindPlotInfos.createBy = createBy;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    insureConfireDetailBindPlotInfos.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    insureConfireDetailBindPlotInfos.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    insureConfireDetailBindPlotInfos.updateTime = updateTime;
  }
  final int? insureBindplotId =
      jsonConvert.convert<int>(json['insureBindplotId']);
  if (insureBindplotId != null) {
    insureConfireDetailBindPlotInfos.insureBindplotId = insureBindplotId;
  }
  final int? insureConfId = jsonConvert.convert<int>(json['insureConfId']);
  if (insureConfId != null) {
    insureConfireDetailBindPlotInfos.insureConfId = insureConfId;
  }
  final String? plotNo = jsonConvert.convert<String>(json['plotNo']);
  if (plotNo != null) {
    insureConfireDetailBindPlotInfos.plotNo = plotNo;
  }
  final String? orgCode = jsonConvert.convert<String>(json['orgCode']);
  if (orgCode != null) {
    insureConfireDetailBindPlotInfos.orgCode = orgCode;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    insureConfireDetailBindPlotInfos.statusCd = statusCd;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    insureConfireDetailBindPlotInfos.params = params;
  }
  return insureConfireDetailBindPlotInfos;
}

Map<String, dynamic> $InsureConfireDetailBindPlotInfosToJson(
    InsureConfireDetailBindPlotInfos entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['plotName'] = entity.plotName;
  data['landType'] = entity.landType;
  data['plotArea'] = entity.plotArea;
  data['cropCode'] = entity.cropCode;
  data['cropName'] = entity.cropName;
  data['remark'] = entity.remark;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['insureBindplotId'] = entity.insureBindplotId;
  data['insureConfId'] = entity.insureConfId;
  data['plotNo'] = entity.plotNo;
  data['orgCode'] = entity.orgCode;
  data['statusCd'] = entity.statusCd;
  data['params'] = entity.params;
  return data;
}

extension InsureConfireDetailBindPlotInfosExtension
    on InsureConfireDetailBindPlotInfos {
  InsureConfireDetailBindPlotInfos copyWith({
    String? plotName,
    String? landType,
    String? plotArea,
    String? cropCode,
    String? cropName,
    String? remark,
    int? createBy,
    int? createTime,
    String? updateBy,
    String? updateTime,
    int? insureBindplotId,
    int? insureConfId,
    String? plotNo,
    String? orgCode,
    String? statusCd,
    String? params,
  }) {
    return InsureConfireDetailBindPlotInfos()
      ..plotName = plotName ?? this.plotName
      ..landType = landType ?? this.landType
      ..plotArea = plotArea ?? this.plotArea
      ..cropCode = cropCode ?? this.cropCode
      ..cropName = cropName ?? this.cropName
      ..remark = remark ?? this.remark
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..insureBindplotId = insureBindplotId ?? this.insureBindplotId
      ..insureConfId = insureConfId ?? this.insureConfId
      ..plotNo = plotNo ?? this.plotNo
      ..orgCode = orgCode ?? this.orgCode
      ..statusCd = statusCd ?? this.statusCd
      ..params = params ?? this.params;
  }
}
