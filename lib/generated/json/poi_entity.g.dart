import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/poi_entity.dart';

POIEntity $POIEntityFromJson(Map<String, dynamic> json) {
  final POIEntity pOIEntity = POIEntity();
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    pOIEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    pOIEntity.updateTime = updateTime;
  }
  final int? statusCd = jsonConvert.convert<int>(json['statusCd']);
  if (statusCd != null) {
    pOIEntity.statusCd = statusCd;
  }
  final int? maregasId = jsonConvert.convert<int>(json['maregasId']);
  if (maregasId != null) {
    pOIEntity.maregasId = maregasId;
  }
  final String? maregasName = jsonConvert.convert<String>(json['maregasName']);
  if (maregasName != null) {
    pOIEntity.maregasName = maregasName;
  }
  final String? maregasType = jsonConvert.convert<String>(json['maregasType']);
  if (maregasType != null) {
    pOIEntity.maregasType = maregasType;
  }
  final String? residueGas = jsonConvert.convert<String>(json['residueGas']);
  if (residueGas != null) {
    pOIEntity.residueGas = residueGas;
  }
  final double? longitude = jsonConvert.convert<double>(json['longitude']);
  if (longitude != null) {
    pOIEntity.longitude = longitude;
  }
  final double? latitude = jsonConvert.convert<double>(json['latitude']);
  if (latitude != null) {
    pOIEntity.latitude = latitude;
  }
  final String? phontNo = jsonConvert.convert<String>(json['phontNo']);
  if (phontNo != null) {
    pOIEntity.phontNo = phontNo;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    pOIEntity.remark = remark;
  }
  final String? maregasAddress =
      jsonConvert.convert<String>(json['maregasAddress']);
  if (maregasAddress != null) {
    pOIEntity.maregasAddress = maregasAddress;
  }
  final String? picAddr = jsonConvert.convert<String>(json['picAddr']);
  if (picAddr != null) {
    pOIEntity.picAddr = picAddr;
  }
  final int? createBy = jsonConvert.convert<int>(json['createBy']);
  if (createBy != null) {
    pOIEntity.createBy = createBy;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    pOIEntity.createTime = createTime;
  }
  final int? regionId = jsonConvert.convert<int>(json['regionId']);
  if (regionId != null) {
    pOIEntity.regionId = regionId;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    pOIEntity.params = params;
  }
  final String? currPointLong =
      jsonConvert.convert<String>(json['currPointLong']);
  if (currPointLong != null) {
    pOIEntity.currPointLong = currPointLong;
  }
  final String? currPointLat =
      jsonConvert.convert<String>(json['currPointLat']);
  if (currPointLat != null) {
    pOIEntity.currPointLat = currPointLat;
  }
  final double? distance = jsonConvert.convert<double>(json['distance']);
  if (distance != null) {
    pOIEntity.distance = distance;
  }
  return pOIEntity;
}

Map<String, dynamic> $POIEntityToJson(POIEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['maregasId'] = entity.maregasId;
  data['maregasName'] = entity.maregasName;
  data['maregasType'] = entity.maregasType;
  data['residueGas'] = entity.residueGas;
  data['longitude'] = entity.longitude;
  data['latitude'] = entity.latitude;
  data['phontNo'] = entity.phontNo;
  data['remark'] = entity.remark;
  data['maregasAddress'] = entity.maregasAddress;
  data['picAddr'] = entity.picAddr;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['regionId'] = entity.regionId;
  data['params'] = entity.params;
  data['currPointLong'] = entity.currPointLong;
  data['currPointLat'] = entity.currPointLat;
  data['distance'] = entity.distance;
  return data;
}

extension POIEntityExtension on POIEntity {
  POIEntity copyWith({
    String? updateBy,
    String? updateTime,
    int? statusCd,
    int? maregasId,
    String? maregasName,
    String? maregasType,
    String? residueGas,
    double? longitude,
    double? latitude,
    String? phontNo,
    String? remark,
    String? maregasAddress,
    String? picAddr,
    int? createBy,
    int? createTime,
    int? regionId,
    String? params,
    String? currPointLong,
    String? currPointLat,
    double? distance,
  }) {
    return POIEntity()
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..maregasId = maregasId ?? this.maregasId
      ..maregasName = maregasName ?? this.maregasName
      ..maregasType = maregasType ?? this.maregasType
      ..residueGas = residueGas ?? this.residueGas
      ..longitude = longitude ?? this.longitude
      ..latitude = latitude ?? this.latitude
      ..phontNo = phontNo ?? this.phontNo
      ..remark = remark ?? this.remark
      ..maregasAddress = maregasAddress ?? this.maregasAddress
      ..picAddr = picAddr ?? this.picAddr
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..regionId = regionId ?? this.regionId
      ..params = params ?? this.params
      ..currPointLong = currPointLong ?? this.currPointLong
      ..currPointLat = currPointLat ?? this.currPointLat
      ..distance = distance ?? this.distance;
  }
}
