import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/information_entity.dart';

InformationEntity $InformationEntityFromJson(Map<String, dynamic> json) {
  final InformationEntity informationEntity = InformationEntity();
  final int? releaseInfoId = jsonConvert.convert<int>(json['releaseInfoId']);
  if (releaseInfoId != null) {
    informationEntity.releaseInfoId = releaseInfoId;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    informationEntity.title = title;
  }
  final String? orgCode = jsonConvert.convert<String>(json['orgCode']);
  if (orgCode != null) {
    informationEntity.orgCode = orgCode;
  }
  final String? orgName = jsonConvert.convert<String>(json['orgName']);
  if (orgName != null) {
    informationEntity.orgName = orgName;
  }
  final int? columnLevelId = jsonConvert.convert<int>(json['columnLevelId']);
  if (columnLevelId != null) {
    informationEntity.columnLevelId = columnLevelId;
  }
  final String? columnLevelName =
      jsonConvert.convert<String>(json['columnLevelName']);
  if (columnLevelName != null) {
    informationEntity.columnLevelName = columnLevelName;
  }
  final int? orders = jsonConvert.convert<int>(json['orders']);
  if (orders != null) {
    informationEntity.orders = orders;
  }
  final int? readNum = jsonConvert.convert<int>(json['readNum']);
  if (readNum != null) {
    informationEntity.readNum = readNum;
  }
  final String? manuscriptStatus =
      jsonConvert.convert<String>(json['manuscriptStatus']);
  if (manuscriptStatus != null) {
    informationEntity.manuscriptStatus = manuscriptStatus;
  }
  final String? thumbUrl = jsonConvert.convert<String>(json['thumbUrl']);
  if (thumbUrl != null) {
    informationEntity.thumbUrl = thumbUrl;
  }
  final int? releaseBy = jsonConvert.convert<int>(json['releaseBy']);
  if (releaseBy != null) {
    informationEntity.releaseBy = releaseBy;
  }
  final String? releaseByName =
      jsonConvert.convert<String>(json['releaseByName']);
  if (releaseByName != null) {
    informationEntity.releaseByName = releaseByName;
  }
  final int? releaseTime = jsonConvert.convert<int>(json['releaseTime']);
  if (releaseTime != null) {
    informationEntity.releaseTime = releaseTime;
  }
  final int? withdrawBy = jsonConvert.convert<int>(json['withdrawBy']);
  if (withdrawBy != null) {
    informationEntity.withdrawBy = withdrawBy;
  }
  final String? withdrawByName =
      jsonConvert.convert<String>(json['withdrawByName']);
  if (withdrawByName != null) {
    informationEntity.withdrawByName = withdrawByName;
  }
  final int? withdrawTime = jsonConvert.convert<int>(json['withdrawTime']);
  if (withdrawTime != null) {
    informationEntity.withdrawTime = withdrawTime;
  }
  final int? deleteBy = jsonConvert.convert<int>(json['deleteBy']);
  if (deleteBy != null) {
    informationEntity.deleteBy = deleteBy;
  }
  final String? deleteByName =
      jsonConvert.convert<String>(json['deleteByName']);
  if (deleteByName != null) {
    informationEntity.deleteByName = deleteByName;
  }
  final int? deleteTime = jsonConvert.convert<int>(json['deleteTime']);
  if (deleteTime != null) {
    informationEntity.deleteTime = deleteTime;
  }
  final dynamic remark = json['remark'];
  if (remark != null) {
    informationEntity.remark = remark;
  }
  final int? createBy = jsonConvert.convert<int>(json['createBy']);
  if (createBy != null) {
    informationEntity.createBy = createBy;
  }
  final String? createByName =
      jsonConvert.convert<String>(json['createByName']);
  if (createByName != null) {
    informationEntity.createByName = createByName;
  }
  final int? createTime = jsonConvert.convert<int>(json['createTime']);
  if (createTime != null) {
    informationEntity.createTime = createTime;
  }
  final dynamic updateBy = json['updateBy'];
  if (updateBy != null) {
    informationEntity.updateBy = updateBy;
  }
  final dynamic updateTime = json['updateTime'];
  if (updateTime != null) {
    informationEntity.updateTime = updateTime;
  }
  final dynamic statusCd = json['statusCd'];
  if (statusCd != null) {
    informationEntity.statusCd = statusCd;
  }
  final dynamic params = json['params'];
  if (params != null) {
    informationEntity.params = params;
  }
  final dynamic upDownFlag = json['upDownFlag'];
  if (upDownFlag != null) {
    informationEntity.upDownFlag = upDownFlag;
  }
  final dynamic systemCode = json['systemCode'];
  if (systemCode != null) {
    informationEntity.systemCode = systemCode;
  }
  return informationEntity;
}

Map<String, dynamic> $InformationEntityToJson(InformationEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['releaseInfoId'] = entity.releaseInfoId;
  data['title'] = entity.title;
  data['orgCode'] = entity.orgCode;
  data['orgName'] = entity.orgName;
  data['columnLevelId'] = entity.columnLevelId;
  data['columnLevelName'] = entity.columnLevelName;
  data['orders'] = entity.orders;
  data['readNum'] = entity.readNum;
  data['manuscriptStatus'] = entity.manuscriptStatus;
  data['thumbUrl'] = entity.thumbUrl;
  data['releaseBy'] = entity.releaseBy;
  data['releaseByName'] = entity.releaseByName;
  data['releaseTime'] = entity.releaseTime;
  data['withdrawBy'] = entity.withdrawBy;
  data['withdrawByName'] = entity.withdrawByName;
  data['withdrawTime'] = entity.withdrawTime;
  data['deleteBy'] = entity.deleteBy;
  data['deleteByName'] = entity.deleteByName;
  data['deleteTime'] = entity.deleteTime;
  data['remark'] = entity.remark;
  data['createBy'] = entity.createBy;
  data['createByName'] = entity.createByName;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['params'] = entity.params;
  data['upDownFlag'] = entity.upDownFlag;
  data['systemCode'] = entity.systemCode;
  return data;
}

extension InformationEntityExtension on InformationEntity {
  InformationEntity copyWith({
    int? releaseInfoId,
    String? title,
    String? orgCode,
    String? orgName,
    int? columnLevelId,
    String? columnLevelName,
    int? orders,
    int? readNum,
    String? manuscriptStatus,
    String? thumbUrl,
    int? releaseBy,
    String? releaseByName,
    int? releaseTime,
    int? withdrawBy,
    String? withdrawByName,
    int? withdrawTime,
    int? deleteBy,
    String? deleteByName,
    int? deleteTime,
    dynamic remark,
    int? createBy,
    String? createByName,
    int? createTime,
    dynamic updateBy,
    dynamic updateTime,
    dynamic statusCd,
    dynamic params,
    dynamic upDownFlag,
    dynamic systemCode,
  }) {
    return InformationEntity()
      ..releaseInfoId = releaseInfoId ?? this.releaseInfoId
      ..title = title ?? this.title
      ..orgCode = orgCode ?? this.orgCode
      ..orgName = orgName ?? this.orgName
      ..columnLevelId = columnLevelId ?? this.columnLevelId
      ..columnLevelName = columnLevelName ?? this.columnLevelName
      ..orders = orders ?? this.orders
      ..readNum = readNum ?? this.readNum
      ..manuscriptStatus = manuscriptStatus ?? this.manuscriptStatus
      ..thumbUrl = thumbUrl ?? this.thumbUrl
      ..releaseBy = releaseBy ?? this.releaseBy
      ..releaseByName = releaseByName ?? this.releaseByName
      ..releaseTime = releaseTime ?? this.releaseTime
      ..withdrawBy = withdrawBy ?? this.withdrawBy
      ..withdrawByName = withdrawByName ?? this.withdrawByName
      ..withdrawTime = withdrawTime ?? this.withdrawTime
      ..deleteBy = deleteBy ?? this.deleteBy
      ..deleteByName = deleteByName ?? this.deleteByName
      ..deleteTime = deleteTime ?? this.deleteTime
      ..remark = remark ?? this.remark
      ..createBy = createBy ?? this.createBy
      ..createByName = createByName ?? this.createByName
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..params = params ?? this.params
      ..upDownFlag = upDownFlag ?? this.upDownFlag
      ..systemCode = systemCode ?? this.systemCode;
  }
}
