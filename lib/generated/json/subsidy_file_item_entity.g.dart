import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_file_item_entity.dart';

SubsidyFileItemEntity $SubsidyFileItemEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyFileItemEntity subsidyFileItemEntity = SubsidyFileItemEntity();
  final String? fileId = jsonConvert.convert<String>(json['fileId']);
  if (fileId != null) {
    subsidyFileItemEntity.fileId = fileId;
  }
  final double? serviceId = jsonConvert.convert<double>(json['serviceId']);
  if (serviceId != null) {
    subsidyFileItemEntity.serviceId = serviceId;
  }
  final double? serviceCodeOne =
      jsonConvert.convert<double>(json['serviceCodeOne']);
  if (serviceCodeOne != null) {
    subsidyFileItemEntity.serviceCodeOne = serviceCodeOne;
  }
  final String? serviceNameOne =
      jsonConvert.convert<String>(json['serviceNameOne']);
  if (serviceNameOne != null) {
    subsidyFileItemEntity.serviceNameOne = serviceNameOne;
  }
  final double? serviceCodeTwo =
      jsonConvert.convert<double>(json['serviceCodeTwo']);
  if (serviceCodeTwo != null) {
    subsidyFileItemEntity.serviceCodeTwo = serviceCodeTwo;
  }
  final String? serviceNameTwo =
      jsonConvert.convert<String>(json['serviceNameTwo']);
  if (serviceNameTwo != null) {
    subsidyFileItemEntity.serviceNameTwo = serviceNameTwo;
  }
  final String? fileName = jsonConvert.convert<String>(json['fileName']);
  if (fileName != null) {
    subsidyFileItemEntity.fileName = fileName;
  }
  final String? fileType = jsonConvert.convert<String>(json['fileType']);
  if (fileType != null) {
    subsidyFileItemEntity.fileType = fileType;
  }
  final String? fileUrl = jsonConvert.convert<String>(json['fileUrl']);
  if (fileUrl != null) {
    subsidyFileItemEntity.fileUrl = fileUrl;
  }
  final String? sourceName = jsonConvert.convert<String>(json['sourceName']);
  if (sourceName != null) {
    subsidyFileItemEntity.sourceName = sourceName;
  }
  final double? createBy = jsonConvert.convert<double>(json['createBy']);
  if (createBy != null) {
    subsidyFileItemEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    subsidyFileItemEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    subsidyFileItemEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    subsidyFileItemEntity.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    subsidyFileItemEntity.statusCd = statusCd;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    subsidyFileItemEntity.remark = remark;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    subsidyFileItemEntity.params = params;
  }
  final bool? delete = jsonConvert.convert<bool>(json['delete']);
  if (delete != null) {
    subsidyFileItemEntity.delete = delete;
  }
  return subsidyFileItemEntity;
}

Map<String, dynamic> $SubsidyFileItemEntityToJson(
    SubsidyFileItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['fileId'] = entity.fileId;
  data['serviceId'] = entity.serviceId;
  data['serviceCodeOne'] = entity.serviceCodeOne;
  data['serviceNameOne'] = entity.serviceNameOne;
  data['serviceCodeTwo'] = entity.serviceCodeTwo;
  data['serviceNameTwo'] = entity.serviceNameTwo;
  data['fileName'] = entity.fileName;
  data['fileType'] = entity.fileType;
  data['fileUrl'] = entity.fileUrl;
  data['sourceName'] = entity.sourceName;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['remark'] = entity.remark;
  data['params'] = entity.params;
  data['delete'] = entity.delete;
  return data;
}

extension SubsidyFileItemEntityExtension on SubsidyFileItemEntity {
  SubsidyFileItemEntity copyWith({
    String? fileId,
    double? serviceId,
    double? serviceCodeOne,
    String? serviceNameOne,
    double? serviceCodeTwo,
    String? serviceNameTwo,
    String? fileName,
    String? fileType,
    String? fileUrl,
    String? sourceName,
    double? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? remark,
    String? params,
    bool? delete,
  }) {
    return SubsidyFileItemEntity()
      ..fileId = fileId ?? this.fileId
      ..serviceId = serviceId ?? this.serviceId
      ..serviceCodeOne = serviceCodeOne ?? this.serviceCodeOne
      ..serviceNameOne = serviceNameOne ?? this.serviceNameOne
      ..serviceCodeTwo = serviceCodeTwo ?? this.serviceCodeTwo
      ..serviceNameTwo = serviceNameTwo ?? this.serviceNameTwo
      ..fileName = fileName ?? this.fileName
      ..fileType = fileType ?? this.fileType
      ..fileUrl = fileUrl ?? this.fileUrl
      ..sourceName = sourceName ?? this.sourceName
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..remark = remark ?? this.remark
      ..params = params ?? this.params
      ..delete = delete ?? this.delete;
  }
}
