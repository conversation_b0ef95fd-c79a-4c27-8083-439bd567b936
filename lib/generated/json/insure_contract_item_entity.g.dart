import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/insure/entity/insure_contract_item_entity.dart';

InsureContractItemEntity $InsureContractItemEntityFromJson(
    Map<String, dynamic> json) {
  final InsureContractItemEntity insureContractItemEntity =
      InsureContractItemEntity();
  final InsureContractItemContractSource? contractSource = jsonConvert
      .convert<InsureContractItemContractSource>(json['contractSource']);
  if (contractSource != null) {
    insureContractItemEntity.contractSource = contractSource;
  }
  final String? contractId = jsonConvert.convert<String>(json['contractId']);
  if (contractId != null) {
    insureContractItemEntity.contractId = contractId;
  }
  final String? serialNumber =
      jsonConvert.convert<String>(json['serialNumber']);
  if (serialNumber != null) {
    insureContractItemEntity.serialNumber = serialNumber;
  }
  final String? orgName = jsonConvert.convert<String>(json['orgName']);
  if (orgName != null) {
    insureContractItemEntity.orgName = orgName;
  }
  final String? orgCode = jsonConvert.convert<String>(json['orgCode']);
  if (orgCode != null) {
    insureContractItemEntity.orgCode = orgCode;
  }
  final double? area = jsonConvert.convert<double>(json['area']);
  if (area != null) {
    insureContractItemEntity.area = area;
  }
  final bool? isConfirm = jsonConvert.convert<bool>(json['isConfirm']);
  if (isConfirm != null) {
    insureContractItemEntity.isConfirm = isConfirm;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    insureContractItemEntity.isSelected = isSelected;
  }
  final String? signImgUrl = jsonConvert.convert<String>(json['signImgUrl']);
  if (signImgUrl != null) {
    insureContractItemEntity.signImgUrl = signImgUrl;
  }
  final String? insureConfId =
      jsonConvert.convert<String>(json['insureConfId']);
  if (insureConfId != null) {
    insureContractItemEntity.insureConfId = insureConfId;
  }
  return insureContractItemEntity;
}

Map<String, dynamic> $InsureContractItemEntityToJson(
    InsureContractItemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['contractSource'] = entity.contractSource?.toJson();
  data['contractId'] = entity.contractId;
  data['serialNumber'] = entity.serialNumber;
  data['orgName'] = entity.orgName;
  data['orgCode'] = entity.orgCode;
  data['area'] = entity.area;
  data['isConfirm'] = entity.isConfirm;
  data['isSelected'] = entity.isSelected;
  data['signImgUrl'] = entity.signImgUrl;
  data['insureConfId'] = entity.insureConfId;
  return data;
}

extension InsureContractItemEntityExtension on InsureContractItemEntity {
  InsureContractItemEntity copyWith({
    InsureContractItemContractSource? contractSource,
    String? contractId,
    String? serialNumber,
    String? orgName,
    String? orgCode,
    double? area,
    bool? isConfirm,
    bool? isSelected,
    String? signImgUrl,
    String? insureConfId,
  }) {
    return InsureContractItemEntity()
      ..contractSource = contractSource ?? this.contractSource
      ..contractId = contractId ?? this.contractId
      ..serialNumber = serialNumber ?? this.serialNumber
      ..orgName = orgName ?? this.orgName
      ..orgCode = orgCode ?? this.orgCode
      ..area = area ?? this.area
      ..isConfirm = isConfirm ?? this.isConfirm
      ..isSelected = isSelected ?? this.isSelected
      ..signImgUrl = signImgUrl ?? this.signImgUrl
      ..insureConfId = insureConfId ?? this.insureConfId;
  }
}

InsureContractItemContractSource $InsureContractItemContractSourceFromJson(
    Map<String, dynamic> json) {
  final InsureContractItemContractSource insureContractItemContractSource =
      InsureContractItemContractSource();
  final String? yearNo = jsonConvert.convert<String>(json['yearNo']);
  if (yearNo != null) {
    insureContractItemContractSource.yearNo = yearNo;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    insureContractItemContractSource.organizationName = organizationName;
  }
  final String? precinctName =
      jsonConvert.convert<String>(json['precinctName']);
  if (precinctName != null) {
    insureContractItemContractSource.precinctName = precinctName;
  }
  final String? serialNumber =
      jsonConvert.convert<String>(json['serialNumber']);
  if (serialNumber != null) {
    insureContractItemContractSource.serialNumber = serialNumber;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    insureContractItemContractSource.farmerName = farmerName;
  }
  final String? farmerIdNumber =
      jsonConvert.convert<String>(json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    insureContractItemContractSource.farmerIdNumber = farmerIdNumber;
  }
  final String? scaleArea = jsonConvert.convert<String>(json['scaleArea']);
  if (scaleArea != null) {
    insureContractItemContractSource.scaleArea = scaleArea;
  }
  final double? chargeArea = jsonConvert.convert<double>(json['chargeArea']);
  if (chargeArea != null) {
    insureContractItemContractSource.chargeArea = chargeArea;
  }
  final double? growArea = jsonConvert.convert<double>(json['growArea']);
  if (growArea != null) {
    insureContractItemContractSource.growArea = growArea;
  }
  final double? totalFee = jsonConvert.convert<double>(json['totalFee']);
  if (totalFee != null) {
    insureContractItemContractSource.totalFee = totalFee;
  }
  final String? printName = jsonConvert.convert<String>(json['printName']);
  if (printName != null) {
    insureContractItemContractSource.printName = printName;
  }
  final double? printStatusNo =
      jsonConvert.convert<double>(json['printStatusNo']);
  if (printStatusNo != null) {
    insureContractItemContractSource.printStatusNo = printStatusNo;
  }
  final String? contractSignDate =
      jsonConvert.convert<String>(json['contractSignDate']);
  if (contractSignDate != null) {
    insureContractItemContractSource.contractSignDate = contractSignDate;
  }
  final double? printCount = jsonConvert.convert<double>(json['printCount']);
  if (printCount != null) {
    insureContractItemContractSource.printCount = printCount;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    insureContractItemContractSource.remark = remark;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    insureContractItemContractSource.address = address;
  }
  final String? contractStartDate =
      jsonConvert.convert<String>(json['contractStartDate']);
  if (contractStartDate != null) {
    insureContractItemContractSource.contractStartDate = contractStartDate;
  }
  final String? contractEndDate =
      jsonConvert.convert<String>(json['contractEndDate']);
  if (contractEndDate != null) {
    insureContractItemContractSource.contractEndDate = contractEndDate;
  }
  final String? contractH5Url =
      jsonConvert.convert<String>(json['contractH5Url']);
  if (contractH5Url != null) {
    insureContractItemContractSource.contractH5Url = contractH5Url;
  }
  final String? contractUrl = jsonConvert.convert<String>(json['contractUrl']);
  if (contractUrl != null) {
    insureContractItemContractSource.contractUrl = contractUrl;
  }
  final String? managementAuditId =
      jsonConvert.convert<String>(json['managementAuditId']);
  if (managementAuditId != null) {
    insureContractItemContractSource.managementAuditId = managementAuditId;
  }
  final String? managementAuditName =
      jsonConvert.convert<String>(json['managementAuditName']);
  if (managementAuditName != null) {
    insureContractItemContractSource.managementAuditName = managementAuditName;
  }
  final String? managementAuditDate =
      jsonConvert.convert<String>(json['management_audit_date']);
  if (managementAuditDate != null) {
    insureContractItemContractSource.managementAuditDate = managementAuditDate;
  }
  final String? farmName = jsonConvert.convert<String>(json['farmName']);
  if (farmName != null) {
    insureContractItemContractSource.farmName = farmName;
  }
  final String? workstationName =
      jsonConvert.convert<String>(json['workstationName']);
  if (workstationName != null) {
    insureContractItemContractSource.workstationName = workstationName;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    insureContractItemContractSource.organizationNo = organizationNo;
  }
  final double? contractId = jsonConvert.convert<double>(json['contractId']);
  if (contractId != null) {
    insureContractItemContractSource.contractId = contractId;
  }
  final String? filialeNo = jsonConvert.convert<String>(json['filialeNo']);
  if (filialeNo != null) {
    insureContractItemContractSource.filialeNo = filialeNo;
  }
  final double? farmNo = jsonConvert.convert<double>(json['farmNo']);
  if (farmNo != null) {
    insureContractItemContractSource.farmNo = farmNo;
  }
  final double? precinctNo = jsonConvert.convert<double>(json['precinctNo']);
  if (precinctNo != null) {
    insureContractItemContractSource.precinctNo = precinctNo;
  }
  final String? workstationNo =
      jsonConvert.convert<String>(json['workstationNo']);
  if (workstationNo != null) {
    insureContractItemContractSource.workstationNo = workstationNo;
  }
  final String? orderNumber = jsonConvert.convert<String>(json['orderNumber']);
  if (orderNumber != null) {
    insureContractItemContractSource.orderNumber = orderNumber;
  }
  final double? farmerId = jsonConvert.convert<double>(json['farmerId']);
  if (farmerId != null) {
    insureContractItemContractSource.farmerId = farmerId;
  }
  final String? farmerIdentityNo =
      jsonConvert.convert<String>(json['farmerIdentityNo']);
  if (farmerIdentityNo != null) {
    insureContractItemContractSource.farmerIdentityNo = farmerIdentityNo;
  }
  final double? paymentStatusNo =
      jsonConvert.convert<double>(json['paymentStatusNo']);
  if (paymentStatusNo != null) {
    insureContractItemContractSource.paymentStatusNo = paymentStatusNo;
  }
  final String? financeStatusNo =
      jsonConvert.convert<String>(json['financeStatusNo']);
  if (financeStatusNo != null) {
    insureContractItemContractSource.financeStatusNo = financeStatusNo;
  }
  final String? contractPrintDate =
      jsonConvert.convert<String>(json['contractPrintDate']);
  if (contractPrintDate != null) {
    insureContractItemContractSource.contractPrintDate = contractPrintDate;
  }
  final String? printUserId = jsonConvert.convert<String>(json['printUserId']);
  if (printUserId != null) {
    insureContractItemContractSource.printUserId = printUserId;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    insureContractItemContractSource.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    insureContractItemContractSource.createTime = createTime;
  }
  final double? updateBy = jsonConvert.convert<double>(json['updateBy']);
  if (updateBy != null) {
    insureContractItemContractSource.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    insureContractItemContractSource.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    insureContractItemContractSource.statusCd = statusCd;
  }
  final double? bidFee = jsonConvert.convert<double>(json['bidFee']);
  if (bidFee != null) {
    insureContractItemContractSource.bidFee = bidFee;
  }
  final String? standardPrice =
      jsonConvert.convert<String>(json['standardPrice']);
  if (standardPrice != null) {
    insureContractItemContractSource.standardPrice = standardPrice;
  }
  final String? bidPrice = jsonConvert.convert<String>(json['bidPrice']);
  if (bidPrice != null) {
    insureContractItemContractSource.bidPrice = bidPrice;
  }
  final double? rentFee = jsonConvert.convert<double>(json['rentFee']);
  if (rentFee != null) {
    insureContractItemContractSource.rentFee = rentFee;
  }
  final String? assuranceFee =
      jsonConvert.convert<String>(json['assuranceFee']);
  if (assuranceFee != null) {
    insureContractItemContractSource.assuranceFee = assuranceFee;
  }
  final String? paymentMethodNo =
      jsonConvert.convert<String>(json['paymentMethodNo']);
  if (paymentMethodNo != null) {
    insureContractItemContractSource.paymentMethodNo = paymentMethodNo;
  }
  final String? chargeTypeNo =
      jsonConvert.convert<String>(json['chargeTypeNo']);
  if (chargeTypeNo != null) {
    insureContractItemContractSource.chargeTypeNo = chargeTypeNo;
  }
  final String? creatorName = jsonConvert.convert<String>(json['creatorName']);
  if (creatorName != null) {
    insureContractItemContractSource.creatorName = creatorName;
  }
  final String? updaterName = jsonConvert.convert<String>(json['updaterName']);
  if (updaterName != null) {
    insureContractItemContractSource.updaterName = updaterName;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    insureContractItemContractSource.params = params;
  }
  final double? contractSignNo =
      jsonConvert.convert<double>(json['contractSignNo']);
  if (contractSignNo != null) {
    insureContractItemContractSource.contractSignNo = contractSignNo;
  }
  final String? additionArea =
      jsonConvert.convert<String>(json['additionArea']);
  if (additionArea != null) {
    insureContractItemContractSource.additionArea = additionArea;
  }
  final String? additionFee = jsonConvert.convert<String>(json['additionFee']);
  if (additionFee != null) {
    insureContractItemContractSource.additionFee = additionFee;
  }
  final String? signPicUrl = jsonConvert.convert<String>(json['signPicUrl']);
  if (signPicUrl != null) {
    insureContractItemContractSource.signPicUrl = signPicUrl;
  }
  final String? photoPath = jsonConvert.convert<String>(json['photoPath']);
  if (photoPath != null) {
    insureContractItemContractSource.photoPath = photoPath;
  }
  final double? auditNo = jsonConvert.convert<double>(json['auditNo']);
  if (auditNo != null) {
    insureContractItemContractSource.auditNo = auditNo;
  }
  final String? familyCount = jsonConvert.convert<String>(json['familyCount']);
  if (familyCount != null) {
    insureContractItemContractSource.familyCount = familyCount;
  }
  final String? practitionerCount =
      jsonConvert.convert<String>(json['practitionerCount']);
  if (practitionerCount != null) {
    insureContractItemContractSource.practitionerCount = practitionerCount;
  }
  final String? fddContractNo =
      jsonConvert.convert<String>(json['fddContractNo']);
  if (fddContractNo != null) {
    insureContractItemContractSource.fddContractNo = fddContractNo;
  }
  final double? auditAFlag = jsonConvert.convert<double>(json['auditAFlag']);
  if (auditAFlag != null) {
    insureContractItemContractSource.auditAFlag = auditAFlag;
  }
  final double? auditAId = jsonConvert.convert<double>(json['auditAId']);
  if (auditAId != null) {
    insureContractItemContractSource.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    insureContractItemContractSource.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    insureContractItemContractSource.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    insureContractItemContractSource.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    insureContractItemContractSource.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    insureContractItemContractSource.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    insureContractItemContractSource.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    insureContractItemContractSource.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    insureContractItemContractSource.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    insureContractItemContractSource.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    insureContractItemContractSource.auditCTime = auditCTime;
  }
  final double? approvalStatusNo =
      jsonConvert.convert<double>(json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    insureContractItemContractSource.approvalStatusNo = approvalStatusNo;
  }
  final String? approvalRemark =
      jsonConvert.convert<String>(json['approvalRemark']);
  if (approvalRemark != null) {
    insureContractItemContractSource.approvalRemark = approvalRemark;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    insureContractItemContractSource.auditDFlag = auditDFlag;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    insureContractItemContractSource.auditDId = auditDId;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    insureContractItemContractSource.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    insureContractItemContractSource.auditDTime = auditDTime;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    insureContractItemContractSource.auditEFlag = auditEFlag;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    insureContractItemContractSource.auditEId = auditEId;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    insureContractItemContractSource.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    insureContractItemContractSource.auditETime = auditETime;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    insureContractItemContractSource.auditFFlag = auditFFlag;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    insureContractItemContractSource.auditFId = auditFId;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    insureContractItemContractSource.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    insureContractItemContractSource.auditFTime = auditFTime;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    insureContractItemContractSource.auditGFlag = auditGFlag;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    insureContractItemContractSource.auditGId = auditGId;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    insureContractItemContractSource.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    insureContractItemContractSource.auditGTime = auditGTime;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    insureContractItemContractSource.auditHFlag = auditHFlag;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    insureContractItemContractSource.auditHId = auditHId;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    insureContractItemContractSource.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    insureContractItemContractSource.auditHTime = auditHTime;
  }
  final String? currentAuditRoleId =
      jsonConvert.convert<String>(json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    insureContractItemContractSource.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName =
      jsonConvert.convert<String>(json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    insureContractItemContractSource.currentAuditRoleName =
        currentAuditRoleName;
  }
  final double? auditLevel = jsonConvert.convert<double>(json['auditLevel']);
  if (auditLevel != null) {
    insureContractItemContractSource.auditLevel = auditLevel;
  }
  final String? payedAmount = jsonConvert.convert<String>(json['payedAmount']);
  if (payedAmount != null) {
    insureContractItemContractSource.payedAmount = payedAmount;
  }
  final String? listPlan = jsonConvert.convert<String>(json['listPlan']);
  if (listPlan != null) {
    insureContractItemContractSource.listPlan = listPlan;
  }
  final String? ids = jsonConvert.convert<String>(json['ids']);
  if (ids != null) {
    insureContractItemContractSource.ids = ids;
  }
  final double? contractTypeNo =
      jsonConvert.convert<double>(json['contractTypeNo']);
  if (contractTypeNo != null) {
    insureContractItemContractSource.contractTypeNo = contractTypeNo;
  }
  final String? relationContractId =
      jsonConvert.convert<String>(json['relationContractId']);
  if (relationContractId != null) {
    insureContractItemContractSource.relationContractId = relationContractId;
  }
  final String? relationSerialNumber =
      jsonConvert.convert<String>(json['relationSerialNumber']);
  if (relationSerialNumber != null) {
    insureContractItemContractSource.relationSerialNumber =
        relationSerialNumber;
  }
  final String? contractType =
      jsonConvert.convert<String>(json['contractType']);
  if (contractType != null) {
    insureContractItemContractSource.contractType = contractType;
  }
  final String? contractTemplateNo =
      jsonConvert.convert<String>(json['contractTemplateNo']);
  if (contractTemplateNo != null) {
    insureContractItemContractSource.contractTemplateNo = contractTemplateNo;
  }
  final String? chargeEndDate =
      jsonConvert.convert<String>(json['chargeEndDate']);
  if (chargeEndDate != null) {
    insureContractItemContractSource.chargeEndDate = chargeEndDate;
  }
  final double? penalty = jsonConvert.convert<double>(json['penalty']);
  if (penalty != null) {
    insureContractItemContractSource.penalty = penalty;
  }
  final String? otherMatters =
      jsonConvert.convert<String>(json['otherMatters']);
  if (otherMatters != null) {
    insureContractItemContractSource.otherMatters = otherMatters;
  }
  final double? retryTimes = jsonConvert.convert<double>(json['retryTimes']);
  if (retryTimes != null) {
    insureContractItemContractSource.retryTimes = retryTimes;
  }
  final double? farmSignStatusNo =
      jsonConvert.convert<double>(json['farmSignStatusNo']);
  if (farmSignStatusNo != null) {
    insureContractItemContractSource.farmSignStatusNo = farmSignStatusNo;
  }
  final String? farmSignCompleteTime =
      jsonConvert.convert<String>(json['farmSignCompleteTime']);
  if (farmSignCompleteTime != null) {
    insureContractItemContractSource.farmSignCompleteTime =
        farmSignCompleteTime;
  }
  final String? farmSignStartTime =
      jsonConvert.convert<String>(json['farmSignStartTime']);
  if (farmSignStartTime != null) {
    insureContractItemContractSource.farmSignStartTime = farmSignStartTime;
  }
  final String? farmSignMsg = jsonConvert.convert<String>(json['farmSignMsg']);
  if (farmSignMsg != null) {
    insureContractItemContractSource.farmSignMsg = farmSignMsg;
  }
  final double? farmSignBy = jsonConvert.convert<double>(json['farmSignBy']);
  if (farmSignBy != null) {
    insureContractItemContractSource.farmSignBy = farmSignBy;
  }
  final double? signVerifyMode =
      jsonConvert.convert<double>(json['signVerifyMode']);
  if (signVerifyMode != null) {
    insureContractItemContractSource.signVerifyMode = signVerifyMode;
  }
  final String? contractTotalQuantity =
      jsonConvert.convert<String>(json['contractTotalQuantity']);
  if (contractTotalQuantity != null) {
    insureContractItemContractSource.contractTotalQuantity =
        contractTotalQuantity;
  }
  final double? dissolutionStatus =
      jsonConvert.convert<double>(json['dissolutionStatus']);
  if (dissolutionStatus != null) {
    insureContractItemContractSource.dissolutionStatus = dissolutionStatus;
  }
  final String? dissolutionStatusCh =
      jsonConvert.convert<String>(json['dissolutionStatusCh']);
  if (dissolutionStatusCh != null) {
    insureContractItemContractSource.dissolutionStatusCh = dissolutionStatusCh;
  }
  final String? accountId = jsonConvert.convert<String>(json['accountId']);
  if (accountId != null) {
    insureContractItemContractSource.accountId = accountId;
  }
  final String? signPersonType =
      jsonConvert.convert<String>(json['signPersonType']);
  if (signPersonType != null) {
    insureContractItemContractSource.signPersonType = signPersonType;
  }
  final String? signPersonTypeApp =
      jsonConvert.convert<String>(json['signPersonTypeApp']);
  if (signPersonTypeApp != null) {
    insureContractItemContractSource.signPersonTypeApp = signPersonTypeApp;
  }
  final double? memberInSignFlag =
      jsonConvert.convert<double>(json['memberInSignFlag']);
  if (memberInSignFlag != null) {
    insureContractItemContractSource.memberInSignFlag = memberInSignFlag;
  }
  final double? guarantorSignFlag =
      jsonConvert.convert<double>(json['guarantorSignFlag']);
  if (guarantorSignFlag != null) {
    insureContractItemContractSource.guarantorSignFlag = guarantorSignFlag;
  }
  final double? contractSignType =
      jsonConvert.convert<double>(json['contractSignType']);
  if (contractSignType != null) {
    insureContractItemContractSource.contractSignType = contractSignType;
  }
  final String? partnerId = jsonConvert.convert<String>(json['partnerId']);
  if (partnerId != null) {
    insureContractItemContractSource.partnerId = partnerId;
  }
  final String? partnerName = jsonConvert.convert<String>(json['partnerName']);
  if (partnerName != null) {
    insureContractItemContractSource.partnerName = partnerName;
  }
  final String? partnerCode = jsonConvert.convert<String>(json['partnerCode']);
  if (partnerCode != null) {
    insureContractItemContractSource.partnerCode = partnerCode;
  }
  final String? contractPeriod =
      jsonConvert.convert<String>(json['contractPeriod']);
  if (contractPeriod != null) {
    insureContractItemContractSource.contractPeriod = contractPeriod;
  }
  final String? objectContractFeeDate =
      jsonConvert.convert<String>(json['objectContractFeeDate']);
  if (objectContractFeeDate != null) {
    insureContractItemContractSource.objectContractFeeDate =
        objectContractFeeDate;
  }
  final double? objectContractFeePenalty =
      jsonConvert.convert<double>(json['objectContractFeePenalty']);
  if (objectContractFeePenalty != null) {
    insureContractItemContractSource.objectContractFeePenalty =
        objectContractFeePenalty;
  }
  final double? ifVerifyLandArea =
      jsonConvert.convert<double>(json['ifVerifyLandArea']);
  if (ifVerifyLandArea != null) {
    insureContractItemContractSource.ifVerifyLandArea = ifVerifyLandArea;
  }
  final double? lcAccessMode =
      jsonConvert.convert<double>(json['lcAccessMode']);
  if (lcAccessMode != null) {
    insureContractItemContractSource.lcAccessMode = lcAccessMode;
  }
  final double? serialNumberLengthLimit =
      jsonConvert.convert<double>(json['serialNumberLengthLimit']);
  if (serialNumberLengthLimit != null) {
    insureContractItemContractSource.serialNumberLengthLimit =
        serialNumberLengthLimit;
  }
  final double? serialNumberLength =
      jsonConvert.convert<double>(json['serialNumberLength']);
  if (serialNumberLength != null) {
    insureContractItemContractSource.serialNumberLength = serialNumberLength;
  }
  final String? modifyPermit =
      jsonConvert.convert<String>(json['modifyPermit']);
  if (modifyPermit != null) {
    insureContractItemContractSource.modifyPermit = modifyPermit;
  }
  final double? extMemberInSignFlag =
      jsonConvert.convert<double>(json['extMemberInSignFlag']);
  if (extMemberInSignFlag != null) {
    insureContractItemContractSource.extMemberInSignFlag = extMemberInSignFlag;
  }
  final double? disMemberInSignFlag =
      jsonConvert.convert<double>(json['disMemberInSignFlag']);
  if (disMemberInSignFlag != null) {
    insureContractItemContractSource.disMemberInSignFlag = disMemberInSignFlag;
  }
  final double? ifVerifyPersonAccess =
      jsonConvert.convert<double>(json['ifVerifyPersonAccess']);
  if (ifVerifyPersonAccess != null) {
    insureContractItemContractSource.ifVerifyPersonAccess =
        ifVerifyPersonAccess;
  }
  final double? objectStandardLevel =
      jsonConvert.convert<double>(json['objectStandardLevel']);
  if (objectStandardLevel != null) {
    insureContractItemContractSource.objectStandardLevel = objectStandardLevel;
  }
  final String? contractTemplateVersion =
      jsonConvert.convert<String>(json['contractTemplateVersion']);
  if (contractTemplateVersion != null) {
    insureContractItemContractSource.contractTemplateVersion =
        contractTemplateVersion;
  }
  final String? chargeAreaSum =
      jsonConvert.convert<String>(json['chargeAreaSum']);
  if (chargeAreaSum != null) {
    insureContractItemContractSource.chargeAreaSum = chargeAreaSum;
  }
  final String? totalFeeSum = jsonConvert.convert<String>(json['totalFeeSum']);
  if (totalFeeSum != null) {
    insureContractItemContractSource.totalFeeSum = totalFeeSum;
  }
  final double? ifOaAuditFlag =
      jsonConvert.convert<double>(json['ifOaAuditFlag']);
  if (ifOaAuditFlag != null) {
    insureContractItemContractSource.ifOaAuditFlag = ifOaAuditFlag;
  }
  final String? oaWfRecordId =
      jsonConvert.convert<String>(json['oaWfRecordId']);
  if (oaWfRecordId != null) {
    insureContractItemContractSource.oaWfRecordId = oaWfRecordId;
  }
  final String? oaWfSummaryId =
      jsonConvert.convert<String>(json['oaWfSummaryId']);
  if (oaWfSummaryId != null) {
    insureContractItemContractSource.oaWfSummaryId = oaWfSummaryId;
  }
  final String? oaAuditBatchNo =
      jsonConvert.convert<String>(json['oaAuditBatchNo']);
  if (oaAuditBatchNo != null) {
    insureContractItemContractSource.oaAuditBatchNo = oaAuditBatchNo;
  }
  final double? oaApprovalStatusNo =
      jsonConvert.convert<double>(json['oaApprovalStatusNo']);
  if (oaApprovalStatusNo != null) {
    insureContractItemContractSource.oaApprovalStatusNo = oaApprovalStatusNo;
  }
  final double? totalTieredRentFee =
      jsonConvert.convert<double>(json['totalTieredRentFee']);
  if (totalTieredRentFee != null) {
    insureContractItemContractSource.totalTieredRentFee = totalTieredRentFee;
  }
  return insureContractItemContractSource;
}

Map<String, dynamic> $InsureContractItemContractSourceToJson(
    InsureContractItemContractSource entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['yearNo'] = entity.yearNo;
  data['organizationName'] = entity.organizationName;
  data['precinctName'] = entity.precinctName;
  data['serialNumber'] = entity.serialNumber;
  data['farmerName'] = entity.farmerName;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['scaleArea'] = entity.scaleArea;
  data['chargeArea'] = entity.chargeArea;
  data['growArea'] = entity.growArea;
  data['totalFee'] = entity.totalFee;
  data['printName'] = entity.printName;
  data['printStatusNo'] = entity.printStatusNo;
  data['contractSignDate'] = entity.contractSignDate;
  data['printCount'] = entity.printCount;
  data['remark'] = entity.remark;
  data['address'] = entity.address;
  data['contractStartDate'] = entity.contractStartDate;
  data['contractEndDate'] = entity.contractEndDate;
  data['contractH5Url'] = entity.contractH5Url;
  data['contractUrl'] = entity.contractUrl;
  data['managementAuditId'] = entity.managementAuditId;
  data['managementAuditName'] = entity.managementAuditName;
  data['management_audit_date'] = entity.managementAuditDate;
  data['farmName'] = entity.farmName;
  data['workstationName'] = entity.workstationName;
  data['organizationNo'] = entity.organizationNo;
  data['contractId'] = entity.contractId;
  data['filialeNo'] = entity.filialeNo;
  data['farmNo'] = entity.farmNo;
  data['precinctNo'] = entity.precinctNo;
  data['workstationNo'] = entity.workstationNo;
  data['orderNumber'] = entity.orderNumber;
  data['farmerId'] = entity.farmerId;
  data['farmerIdentityNo'] = entity.farmerIdentityNo;
  data['paymentStatusNo'] = entity.paymentStatusNo;
  data['financeStatusNo'] = entity.financeStatusNo;
  data['contractPrintDate'] = entity.contractPrintDate;
  data['printUserId'] = entity.printUserId;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['bidFee'] = entity.bidFee;
  data['standardPrice'] = entity.standardPrice;
  data['bidPrice'] = entity.bidPrice;
  data['rentFee'] = entity.rentFee;
  data['assuranceFee'] = entity.assuranceFee;
  data['paymentMethodNo'] = entity.paymentMethodNo;
  data['chargeTypeNo'] = entity.chargeTypeNo;
  data['creatorName'] = entity.creatorName;
  data['updaterName'] = entity.updaterName;
  data['params'] = entity.params;
  data['contractSignNo'] = entity.contractSignNo;
  data['additionArea'] = entity.additionArea;
  data['additionFee'] = entity.additionFee;
  data['signPicUrl'] = entity.signPicUrl;
  data['photoPath'] = entity.photoPath;
  data['auditNo'] = entity.auditNo;
  data['familyCount'] = entity.familyCount;
  data['practitionerCount'] = entity.practitionerCount;
  data['fddContractNo'] = entity.fddContractNo;
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['approvalRemark'] = entity.approvalRemark;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDId'] = entity.auditDId;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEId'] = entity.auditEId;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFId'] = entity.auditFId;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGId'] = entity.auditGId;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHId'] = entity.auditHId;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['auditLevel'] = entity.auditLevel;
  data['payedAmount'] = entity.payedAmount;
  data['listPlan'] = entity.listPlan;
  data['ids'] = entity.ids;
  data['contractTypeNo'] = entity.contractTypeNo;
  data['relationContractId'] = entity.relationContractId;
  data['relationSerialNumber'] = entity.relationSerialNumber;
  data['contractType'] = entity.contractType;
  data['contractTemplateNo'] = entity.contractTemplateNo;
  data['chargeEndDate'] = entity.chargeEndDate;
  data['penalty'] = entity.penalty;
  data['otherMatters'] = entity.otherMatters;
  data['retryTimes'] = entity.retryTimes;
  data['farmSignStatusNo'] = entity.farmSignStatusNo;
  data['farmSignCompleteTime'] = entity.farmSignCompleteTime;
  data['farmSignStartTime'] = entity.farmSignStartTime;
  data['farmSignMsg'] = entity.farmSignMsg;
  data['farmSignBy'] = entity.farmSignBy;
  data['signVerifyMode'] = entity.signVerifyMode;
  data['contractTotalQuantity'] = entity.contractTotalQuantity;
  data['dissolutionStatus'] = entity.dissolutionStatus;
  data['dissolutionStatusCh'] = entity.dissolutionStatusCh;
  data['accountId'] = entity.accountId;
  data['signPersonType'] = entity.signPersonType;
  data['signPersonTypeApp'] = entity.signPersonTypeApp;
  data['memberInSignFlag'] = entity.memberInSignFlag;
  data['guarantorSignFlag'] = entity.guarantorSignFlag;
  data['contractSignType'] = entity.contractSignType;
  data['partnerId'] = entity.partnerId;
  data['partnerName'] = entity.partnerName;
  data['partnerCode'] = entity.partnerCode;
  data['contractPeriod'] = entity.contractPeriod;
  data['objectContractFeeDate'] = entity.objectContractFeeDate;
  data['objectContractFeePenalty'] = entity.objectContractFeePenalty;
  data['ifVerifyLandArea'] = entity.ifVerifyLandArea;
  data['lcAccessMode'] = entity.lcAccessMode;
  data['serialNumberLengthLimit'] = entity.serialNumberLengthLimit;
  data['serialNumberLength'] = entity.serialNumberLength;
  data['modifyPermit'] = entity.modifyPermit;
  data['extMemberInSignFlag'] = entity.extMemberInSignFlag;
  data['disMemberInSignFlag'] = entity.disMemberInSignFlag;
  data['ifVerifyPersonAccess'] = entity.ifVerifyPersonAccess;
  data['objectStandardLevel'] = entity.objectStandardLevel;
  data['contractTemplateVersion'] = entity.contractTemplateVersion;
  data['chargeAreaSum'] = entity.chargeAreaSum;
  data['totalFeeSum'] = entity.totalFeeSum;
  data['ifOaAuditFlag'] = entity.ifOaAuditFlag;
  data['oaWfRecordId'] = entity.oaWfRecordId;
  data['oaWfSummaryId'] = entity.oaWfSummaryId;
  data['oaAuditBatchNo'] = entity.oaAuditBatchNo;
  data['oaApprovalStatusNo'] = entity.oaApprovalStatusNo;
  data['totalTieredRentFee'] = entity.totalTieredRentFee;
  return data;
}

extension InsureContractItemContractSourceExtension
    on InsureContractItemContractSource {
  InsureContractItemContractSource copyWith({
    String? yearNo,
    String? organizationName,
    String? precinctName,
    String? serialNumber,
    String? farmerName,
    String? farmerIdNumber,
    String? scaleArea,
    double? chargeArea,
    double? growArea,
    double? totalFee,
    String? printName,
    double? printStatusNo,
    String? contractSignDate,
    double? printCount,
    String? remark,
    String? address,
    String? contractStartDate,
    String? contractEndDate,
    String? contractH5Url,
    String? contractUrl,
    String? managementAuditId,
    String? managementAuditName,
    String? managementAuditDate,
    String? farmName,
    String? workstationName,
    String? organizationNo,
    double? contractId,
    String? filialeNo,
    double? farmNo,
    double? precinctNo,
    String? workstationNo,
    String? orderNumber,
    double? farmerId,
    String? farmerIdentityNo,
    double? paymentStatusNo,
    String? financeStatusNo,
    String? contractPrintDate,
    String? printUserId,
    String? createBy,
    String? createTime,
    double? updateBy,
    String? updateTime,
    String? statusCd,
    double? bidFee,
    String? standardPrice,
    String? bidPrice,
    double? rentFee,
    String? assuranceFee,
    String? paymentMethodNo,
    String? chargeTypeNo,
    String? creatorName,
    String? updaterName,
    String? params,
    double? contractSignNo,
    String? additionArea,
    String? additionFee,
    String? signPicUrl,
    String? photoPath,
    double? auditNo,
    String? familyCount,
    String? practitionerCount,
    String? fddContractNo,
    double? auditAFlag,
    double? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    double? approvalStatusNo,
    String? approvalRemark,
    String? auditDFlag,
    String? auditDId,
    String? auditDName,
    String? auditDTime,
    String? auditEFlag,
    String? auditEId,
    String? auditEName,
    String? auditETime,
    String? auditFFlag,
    String? auditFId,
    String? auditFName,
    String? auditFTime,
    String? auditGFlag,
    String? auditGId,
    String? auditGName,
    String? auditGTime,
    String? auditHFlag,
    String? auditHId,
    String? auditHName,
    String? auditHTime,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    double? auditLevel,
    String? payedAmount,
    String? listPlan,
    String? ids,
    double? contractTypeNo,
    String? relationContractId,
    String? relationSerialNumber,
    String? contractType,
    String? contractTemplateNo,
    String? chargeEndDate,
    double? penalty,
    String? otherMatters,
    double? retryTimes,
    double? farmSignStatusNo,
    String? farmSignCompleteTime,
    String? farmSignStartTime,
    String? farmSignMsg,
    double? farmSignBy,
    double? signVerifyMode,
    String? contractTotalQuantity,
    double? dissolutionStatus,
    String? dissolutionStatusCh,
    String? accountId,
    String? signPersonType,
    String? signPersonTypeApp,
    double? memberInSignFlag,
    double? guarantorSignFlag,
    double? contractSignType,
    String? partnerId,
    String? partnerName,
    String? partnerCode,
    String? contractPeriod,
    String? objectContractFeeDate,
    double? objectContractFeePenalty,
    double? ifVerifyLandArea,
    double? lcAccessMode,
    double? serialNumberLengthLimit,
    double? serialNumberLength,
    String? modifyPermit,
    double? extMemberInSignFlag,
    double? disMemberInSignFlag,
    double? ifVerifyPersonAccess,
    double? objectStandardLevel,
    String? contractTemplateVersion,
    String? chargeAreaSum,
    String? totalFeeSum,
    double? ifOaAuditFlag,
    String? oaWfRecordId,
    String? oaWfSummaryId,
    String? oaAuditBatchNo,
    double? oaApprovalStatusNo,
    double? totalTieredRentFee,
  }) {
    return InsureContractItemContractSource()
      ..yearNo = yearNo ?? this.yearNo
      ..organizationName = organizationName ?? this.organizationName
      ..precinctName = precinctName ?? this.precinctName
      ..serialNumber = serialNumber ?? this.serialNumber
      ..farmerName = farmerName ?? this.farmerName
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..scaleArea = scaleArea ?? this.scaleArea
      ..chargeArea = chargeArea ?? this.chargeArea
      ..growArea = growArea ?? this.growArea
      ..totalFee = totalFee ?? this.totalFee
      ..printName = printName ?? this.printName
      ..printStatusNo = printStatusNo ?? this.printStatusNo
      ..contractSignDate = contractSignDate ?? this.contractSignDate
      ..printCount = printCount ?? this.printCount
      ..remark = remark ?? this.remark
      ..address = address ?? this.address
      ..contractStartDate = contractStartDate ?? this.contractStartDate
      ..contractEndDate = contractEndDate ?? this.contractEndDate
      ..contractH5Url = contractH5Url ?? this.contractH5Url
      ..contractUrl = contractUrl ?? this.contractUrl
      ..managementAuditId = managementAuditId ?? this.managementAuditId
      ..managementAuditName = managementAuditName ?? this.managementAuditName
      ..managementAuditDate = managementAuditDate ?? this.managementAuditDate
      ..farmName = farmName ?? this.farmName
      ..workstationName = workstationName ?? this.workstationName
      ..organizationNo = organizationNo ?? this.organizationNo
      ..contractId = contractId ?? this.contractId
      ..filialeNo = filialeNo ?? this.filialeNo
      ..farmNo = farmNo ?? this.farmNo
      ..precinctNo = precinctNo ?? this.precinctNo
      ..workstationNo = workstationNo ?? this.workstationNo
      ..orderNumber = orderNumber ?? this.orderNumber
      ..farmerId = farmerId ?? this.farmerId
      ..farmerIdentityNo = farmerIdentityNo ?? this.farmerIdentityNo
      ..paymentStatusNo = paymentStatusNo ?? this.paymentStatusNo
      ..financeStatusNo = financeStatusNo ?? this.financeStatusNo
      ..contractPrintDate = contractPrintDate ?? this.contractPrintDate
      ..printUserId = printUserId ?? this.printUserId
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..bidFee = bidFee ?? this.bidFee
      ..standardPrice = standardPrice ?? this.standardPrice
      ..bidPrice = bidPrice ?? this.bidPrice
      ..rentFee = rentFee ?? this.rentFee
      ..assuranceFee = assuranceFee ?? this.assuranceFee
      ..paymentMethodNo = paymentMethodNo ?? this.paymentMethodNo
      ..chargeTypeNo = chargeTypeNo ?? this.chargeTypeNo
      ..creatorName = creatorName ?? this.creatorName
      ..updaterName = updaterName ?? this.updaterName
      ..params = params ?? this.params
      ..contractSignNo = contractSignNo ?? this.contractSignNo
      ..additionArea = additionArea ?? this.additionArea
      ..additionFee = additionFee ?? this.additionFee
      ..signPicUrl = signPicUrl ?? this.signPicUrl
      ..photoPath = photoPath ?? this.photoPath
      ..auditNo = auditNo ?? this.auditNo
      ..familyCount = familyCount ?? this.familyCount
      ..practitionerCount = practitionerCount ?? this.practitionerCount
      ..fddContractNo = fddContractNo ?? this.fddContractNo
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDId = auditDId ?? this.auditDId
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEId = auditEId ?? this.auditEId
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFId = auditFId ?? this.auditFId
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGId = auditGId ?? this.auditGId
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHId = auditHId ?? this.auditHId
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..auditLevel = auditLevel ?? this.auditLevel
      ..payedAmount = payedAmount ?? this.payedAmount
      ..listPlan = listPlan ?? this.listPlan
      ..ids = ids ?? this.ids
      ..contractTypeNo = contractTypeNo ?? this.contractTypeNo
      ..relationContractId = relationContractId ?? this.relationContractId
      ..relationSerialNumber = relationSerialNumber ?? this.relationSerialNumber
      ..contractType = contractType ?? this.contractType
      ..contractTemplateNo = contractTemplateNo ?? this.contractTemplateNo
      ..chargeEndDate = chargeEndDate ?? this.chargeEndDate
      ..penalty = penalty ?? this.penalty
      ..otherMatters = otherMatters ?? this.otherMatters
      ..retryTimes = retryTimes ?? this.retryTimes
      ..farmSignStatusNo = farmSignStatusNo ?? this.farmSignStatusNo
      ..farmSignCompleteTime = farmSignCompleteTime ?? this.farmSignCompleteTime
      ..farmSignStartTime = farmSignStartTime ?? this.farmSignStartTime
      ..farmSignMsg = farmSignMsg ?? this.farmSignMsg
      ..farmSignBy = farmSignBy ?? this.farmSignBy
      ..signVerifyMode = signVerifyMode ?? this.signVerifyMode
      ..contractTotalQuantity =
          contractTotalQuantity ?? this.contractTotalQuantity
      ..dissolutionStatus = dissolutionStatus ?? this.dissolutionStatus
      ..dissolutionStatusCh = dissolutionStatusCh ?? this.dissolutionStatusCh
      ..accountId = accountId ?? this.accountId
      ..signPersonType = signPersonType ?? this.signPersonType
      ..signPersonTypeApp = signPersonTypeApp ?? this.signPersonTypeApp
      ..memberInSignFlag = memberInSignFlag ?? this.memberInSignFlag
      ..guarantorSignFlag = guarantorSignFlag ?? this.guarantorSignFlag
      ..contractSignType = contractSignType ?? this.contractSignType
      ..partnerId = partnerId ?? this.partnerId
      ..partnerName = partnerName ?? this.partnerName
      ..partnerCode = partnerCode ?? this.partnerCode
      ..contractPeriod = contractPeriod ?? this.contractPeriod
      ..objectContractFeeDate =
          objectContractFeeDate ?? this.objectContractFeeDate
      ..objectContractFeePenalty =
          objectContractFeePenalty ?? this.objectContractFeePenalty
      ..ifVerifyLandArea = ifVerifyLandArea ?? this.ifVerifyLandArea
      ..lcAccessMode = lcAccessMode ?? this.lcAccessMode
      ..serialNumberLengthLimit =
          serialNumberLengthLimit ?? this.serialNumberLengthLimit
      ..serialNumberLength = serialNumberLength ?? this.serialNumberLength
      ..modifyPermit = modifyPermit ?? this.modifyPermit
      ..extMemberInSignFlag = extMemberInSignFlag ?? this.extMemberInSignFlag
      ..disMemberInSignFlag = disMemberInSignFlag ?? this.disMemberInSignFlag
      ..ifVerifyPersonAccess = ifVerifyPersonAccess ?? this.ifVerifyPersonAccess
      ..objectStandardLevel = objectStandardLevel ?? this.objectStandardLevel
      ..contractTemplateVersion =
          contractTemplateVersion ?? this.contractTemplateVersion
      ..chargeAreaSum = chargeAreaSum ?? this.chargeAreaSum
      ..totalFeeSum = totalFeeSum ?? this.totalFeeSum
      ..ifOaAuditFlag = ifOaAuditFlag ?? this.ifOaAuditFlag
      ..oaWfRecordId = oaWfRecordId ?? this.oaWfRecordId
      ..oaWfSummaryId = oaWfSummaryId ?? this.oaWfSummaryId
      ..oaAuditBatchNo = oaAuditBatchNo ?? this.oaAuditBatchNo
      ..oaApprovalStatusNo = oaApprovalStatusNo ?? this.oaApprovalStatusNo
      ..totalTieredRentFee = totalTieredRentFee ?? this.totalTieredRentFee;
  }
}
