import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_grain_feed_entity.dart';

SubsidyGrainFeedEntity $SubsidyGrainFeedEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyGrainFeedEntity subsidyGrainFeedEntity = SubsidyGrainFeedEntity();
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    subsidyGrainFeedEntity.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    subsidyGrainFeedEntity.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    subsidyGrainFeedEntity.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    subsidyGrainFeedEntity.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    subsidyGrainFeedEntity.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    subsidyGrainFeedEntity.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    subsidyGrainFeedEntity.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    subsidyGrainFeedEntity.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    subsidyGrainFeedEntity.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    subsidyGrainFeedEntity.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    subsidyGrainFeedEntity.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    subsidyGrainFeedEntity.auditCTime = auditCTime;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    subsidyGrainFeedEntity.auditDFlag = auditDFlag;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    subsidyGrainFeedEntity.auditDId = auditDId;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    subsidyGrainFeedEntity.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    subsidyGrainFeedEntity.auditDTime = auditDTime;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    subsidyGrainFeedEntity.auditEFlag = auditEFlag;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    subsidyGrainFeedEntity.auditEId = auditEId;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    subsidyGrainFeedEntity.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    subsidyGrainFeedEntity.auditETime = auditETime;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    subsidyGrainFeedEntity.auditFFlag = auditFFlag;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    subsidyGrainFeedEntity.auditFId = auditFId;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    subsidyGrainFeedEntity.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    subsidyGrainFeedEntity.auditFTime = auditFTime;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    subsidyGrainFeedEntity.auditGFlag = auditGFlag;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    subsidyGrainFeedEntity.auditGId = auditGId;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    subsidyGrainFeedEntity.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    subsidyGrainFeedEntity.auditGTime = auditGTime;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    subsidyGrainFeedEntity.auditHFlag = auditHFlag;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    subsidyGrainFeedEntity.auditHId = auditHId;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    subsidyGrainFeedEntity.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    subsidyGrainFeedEntity.auditHTime = auditHTime;
  }
  final String? subsidyDetailId = jsonConvert.convert<String>(
      json['subsidyDetailId']);
  if (subsidyDetailId != null) {
    subsidyGrainFeedEntity.subsidyDetailId = subsidyDetailId;
  }
  final String? subsidyPaymentNo = jsonConvert.convert<String>(
      json['subsidyPaymentNo']);
  if (subsidyPaymentNo != null) {
    subsidyGrainFeedEntity.subsidyPaymentNo = subsidyPaymentNo;
  }
  final String? subsidyYear = jsonConvert.convert<String>(json['subsidyYear']);
  if (subsidyYear != null) {
    subsidyGrainFeedEntity.subsidyYear = subsidyYear;
  }
  final String? subsidyProjectCode = jsonConvert.convert<String>(
      json['subsidyProjectCode']);
  if (subsidyProjectCode != null) {
    subsidyGrainFeedEntity.subsidyProjectCode = subsidyProjectCode;
  }
  final String? subsidyType = jsonConvert.convert<String>(json['subsidyType']);
  if (subsidyType != null) {
    subsidyGrainFeedEntity.subsidyType = subsidyType;
  }
  final String? organizationName = jsonConvert.convert<String>(
      json['organizationName']);
  if (organizationName != null) {
    subsidyGrainFeedEntity.organizationName = organizationName;
  }
  final String? organizationNo = jsonConvert.convert<String>(
      json['organizationNo']);
  if (organizationNo != null) {
    subsidyGrainFeedEntity.organizationNo = organizationNo;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    subsidyGrainFeedEntity.farmerId = farmerId;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    subsidyGrainFeedEntity.farmerName = farmerName;
  }
  final String? farmerIdNumber = jsonConvert.convert<String>(
      json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    subsidyGrainFeedEntity.farmerIdNumber = farmerIdNumber;
  }
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  if (bankAccount != null) {
    subsidyGrainFeedEntity.bankAccount = bankAccount;
  }
  final String? landNumber = jsonConvert.convert<String>(json['landNumber']);
  if (landNumber != null) {
    subsidyGrainFeedEntity.landNumber = landNumber;
  }
  final String? actualPaymentFee = jsonConvert.convert<String>(
      json['actualPaymentFee']);
  if (actualPaymentFee != null) {
    subsidyGrainFeedEntity.actualPaymentFee = actualPaymentFee;
  }
  final String? subsidyWeight = jsonConvert.convert<String>(
      json['subsidyWeight']);
  if (subsidyWeight != null) {
    subsidyGrainFeedEntity.subsidyWeight = subsidyWeight;
  }
  final String? plantArea = jsonConvert.convert<String>(json['plantArea']);
  if (plantArea != null) {
    subsidyGrainFeedEntity.plantArea = plantArea;
  }
  final String? subsidyArea = jsonConvert.convert<String>(json['subsidyArea']);
  if (subsidyArea != null) {
    subsidyGrainFeedEntity.subsidyArea = subsidyArea;
  }
  final String? subsidyStandard1 = jsonConvert.convert<String>(
      json['subsidyStandard1']);
  if (subsidyStandard1 != null) {
    subsidyGrainFeedEntity.subsidyStandard1 = subsidyStandard1;
  }
  final String? subsidyWeight1 = jsonConvert.convert<String>(
      json['subsidyWeight1']);
  if (subsidyWeight1 != null) {
    subsidyGrainFeedEntity.subsidyWeight1 = subsidyWeight1;
  }
  final String? subsidyFee1 = jsonConvert.convert<String>(json['subsidyFee1']);
  if (subsidyFee1 != null) {
    subsidyGrainFeedEntity.subsidyFee1 = subsidyFee1;
  }
  final String? partnerId = jsonConvert.convert<String>(json['partnerId']);
  if (partnerId != null) {
    subsidyGrainFeedEntity.partnerId = partnerId;
  }
  final String? subsidyArea1 = jsonConvert.convert<String>(
      json['subsidyArea1']);
  if (subsidyArea1 != null) {
    subsidyGrainFeedEntity.subsidyArea1 = subsidyArea1;
  }
  final String? plantArea1 = jsonConvert.convert<String>(json['plantArea1']);
  if (plantArea1 != null) {
    subsidyGrainFeedEntity.plantArea1 = plantArea1;
  }
  final String? subsidyStandard2 = jsonConvert.convert<String>(
      json['subsidyStandard2']);
  if (subsidyStandard2 != null) {
    subsidyGrainFeedEntity.subsidyStandard2 = subsidyStandard2;
  }
  final String? subsidyWeight2 = jsonConvert.convert<String>(
      json['subsidyWeight2']);
  if (subsidyWeight2 != null) {
    subsidyGrainFeedEntity.subsidyWeight2 = subsidyWeight2;
  }
  final String? subsidyFee2 = jsonConvert.convert<String>(json['subsidyFee2']);
  if (subsidyFee2 != null) {
    subsidyGrainFeedEntity.subsidyFee2 = subsidyFee2;
  }
  final String? subsidyArea2 = jsonConvert.convert<String>(
      json['subsidyArea2']);
  if (subsidyArea2 != null) {
    subsidyGrainFeedEntity.subsidyArea2 = subsidyArea2;
  }
  final String? plantArea2 = jsonConvert.convert<String>(json['plantArea2']);
  if (plantArea2 != null) {
    subsidyGrainFeedEntity.plantArea2 = plantArea2;
  }
  final String? subsidyStandard3 = jsonConvert.convert<String>(
      json['subsidyStandard3']);
  if (subsidyStandard3 != null) {
    subsidyGrainFeedEntity.subsidyStandard3 = subsidyStandard3;
  }
  final String? subsidyWeight3 = jsonConvert.convert<String>(
      json['subsidyWeight3']);
  if (subsidyWeight3 != null) {
    subsidyGrainFeedEntity.subsidyWeight3 = subsidyWeight3;
  }
  final String? subsidyFee3 = jsonConvert.convert<String>(json['subsidyFee3']);
  if (subsidyFee3 != null) {
    subsidyGrainFeedEntity.subsidyFee3 = subsidyFee3;
  }
  final String? subsidyArea3 = jsonConvert.convert<String>(
      json['subsidyArea3']);
  if (subsidyArea3 != null) {
    subsidyGrainFeedEntity.subsidyArea3 = subsidyArea3;
  }
  final String? plantArea3 = jsonConvert.convert<String>(json['plantArea3']);
  if (plantArea3 != null) {
    subsidyGrainFeedEntity.plantArea3 = plantArea3;
  }
  final String? approvalStatusNo = jsonConvert.convert<String>(
      json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    subsidyGrainFeedEntity.approvalStatusNo = approvalStatusNo;
  }
  final String? currentAuditRoleId = jsonConvert.convert<String>(
      json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    subsidyGrainFeedEntity.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName = jsonConvert.convert<String>(
      json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    subsidyGrainFeedEntity.currentAuditRoleName = currentAuditRoleName;
  }
  final String? approvalRemark = jsonConvert.convert<String>(
      json['approvalRemark']);
  if (approvalRemark != null) {
    subsidyGrainFeedEntity.approvalRemark = approvalRemark;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    subsidyGrainFeedEntity.auditLevel = auditLevel;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    subsidyGrainFeedEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    subsidyGrainFeedEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    subsidyGrainFeedEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    subsidyGrainFeedEntity.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    subsidyGrainFeedEntity.statusCd = statusCd;
  }
  final String? dataStatus = jsonConvert.convert<String>(json['dataStatus']);
  if (dataStatus != null) {
    subsidyGrainFeedEntity.dataStatus = dataStatus;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    subsidyGrainFeedEntity.bankName = bankName;
  }
  final String? subsidyConfigId = jsonConvert.convert<String>(
      json['subsidyConfigId']);
  if (subsidyConfigId != null) {
    subsidyGrainFeedEntity.subsidyConfigId = subsidyConfigId;
  }
  final String? contractSignType = jsonConvert.convert<String>(
      json['contractSignType']);
  if (contractSignType != null) {
    subsidyGrainFeedEntity.contractSignType = contractSignType;
  }
  final String? bankNum = jsonConvert.convert<String>(json['bankNum']);
  if (bankNum != null) {
    subsidyGrainFeedEntity.bankNum = bankNum;
  }
  final String? network = jsonConvert.convert<String>(json['network']);
  if (network != null) {
    subsidyGrainFeedEntity.network = network;
  }
  final String? lineNumber = jsonConvert.convert<String>(json['lineNumber']);
  if (lineNumber != null) {
    subsidyGrainFeedEntity.lineNumber = lineNumber;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    subsidyGrainFeedEntity.remark = remark;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    subsidyGrainFeedEntity.params = params;
  }
  final String? subsidyPaymentGrain2feedSubList = jsonConvert.convert<String>(
      json['subsidyPaymentGrain2feedSubList']);
  if (subsidyPaymentGrain2feedSubList != null) {
    subsidyGrainFeedEntity.subsidyPaymentGrain2feedSubList =
        subsidyPaymentGrain2feedSubList;
  }
  final String? ifUpdate = jsonConvert.convert<String>(json['ifUpdate']);
  if (ifUpdate != null) {
    subsidyGrainFeedEntity.ifUpdate = ifUpdate;
  }
  final String? subsidyItemName = jsonConvert.convert<String>(
      json['subsidyItemName']);
  if (subsidyItemName != null) {
    subsidyGrainFeedEntity.subsidyItemName = subsidyItemName;
  }
  return subsidyGrainFeedEntity;
}

Map<String, dynamic> $SubsidyGrainFeedEntityToJson(
    SubsidyGrainFeedEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDId'] = entity.auditDId;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEId'] = entity.auditEId;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFId'] = entity.auditFId;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGId'] = entity.auditGId;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHId'] = entity.auditHId;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['subsidyDetailId'] = entity.subsidyDetailId;
  data['subsidyPaymentNo'] = entity.subsidyPaymentNo;
  data['subsidyYear'] = entity.subsidyYear;
  data['subsidyProjectCode'] = entity.subsidyProjectCode;
  data['subsidyType'] = entity.subsidyType;
  data['organizationName'] = entity.organizationName;
  data['organizationNo'] = entity.organizationNo;
  data['farmerId'] = entity.farmerId;
  data['farmerName'] = entity.farmerName;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['bankAccount'] = entity.bankAccount;
  data['landNumber'] = entity.landNumber;
  data['actualPaymentFee'] = entity.actualPaymentFee;
  data['subsidyWeight'] = entity.subsidyWeight;
  data['plantArea'] = entity.plantArea;
  data['subsidyArea'] = entity.subsidyArea;
  data['subsidyStandard1'] = entity.subsidyStandard1;
  data['subsidyWeight1'] = entity.subsidyWeight1;
  data['subsidyFee1'] = entity.subsidyFee1;
  data['partnerId'] = entity.partnerId;
  data['subsidyArea1'] = entity.subsidyArea1;
  data['plantArea1'] = entity.plantArea1;
  data['subsidyStandard2'] = entity.subsidyStandard2;
  data['subsidyWeight2'] = entity.subsidyWeight2;
  data['subsidyFee2'] = entity.subsidyFee2;
  data['subsidyArea2'] = entity.subsidyArea2;
  data['plantArea2'] = entity.plantArea2;
  data['subsidyStandard3'] = entity.subsidyStandard3;
  data['subsidyWeight3'] = entity.subsidyWeight3;
  data['subsidyFee3'] = entity.subsidyFee3;
  data['subsidyArea3'] = entity.subsidyArea3;
  data['plantArea3'] = entity.plantArea3;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['approvalRemark'] = entity.approvalRemark;
  data['auditLevel'] = entity.auditLevel;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['dataStatus'] = entity.dataStatus;
  data['bankName'] = entity.bankName;
  data['subsidyConfigId'] = entity.subsidyConfigId;
  data['contractSignType'] = entity.contractSignType;
  data['bankNum'] = entity.bankNum;
  data['network'] = entity.network;
  data['lineNumber'] = entity.lineNumber;
  data['remark'] = entity.remark;
  data['params'] = entity.params;
  data['subsidyPaymentGrain2feedSubList'] =
      entity.subsidyPaymentGrain2feedSubList;
  data['ifUpdate'] = entity.ifUpdate;
  data['subsidyItemName'] = entity.subsidyItemName;
  return data;
}

extension SubsidyGrainFeedEntityExtension on SubsidyGrainFeedEntity {
  SubsidyGrainFeedEntity copyWith({
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? auditDFlag,
    String? auditDId,
    String? auditDName,
    String? auditDTime,
    String? auditEFlag,
    String? auditEId,
    String? auditEName,
    String? auditETime,
    String? auditFFlag,
    String? auditFId,
    String? auditFName,
    String? auditFTime,
    String? auditGFlag,
    String? auditGId,
    String? auditGName,
    String? auditGTime,
    String? auditHFlag,
    String? auditHId,
    String? auditHName,
    String? auditHTime,
    String? subsidyDetailId,
    String? subsidyPaymentNo,
    String? subsidyYear,
    String? subsidyProjectCode,
    String? subsidyType,
    String? organizationName,
    String? organizationNo,
    String? farmerId,
    String? farmerName,
    String? farmerIdNumber,
    String? bankAccount,
    String? landNumber,
    String? actualPaymentFee,
    String? subsidyWeight,
    String? plantArea,
    String? subsidyArea,
    String? subsidyStandard1,
    String? subsidyWeight1,
    String? subsidyFee1,
    String? partnerId,
    String? subsidyArea1,
    String? plantArea1,
    String? subsidyStandard2,
    String? subsidyWeight2,
    String? subsidyFee2,
    String? subsidyArea2,
    String? plantArea2,
    String? subsidyStandard3,
    String? subsidyWeight3,
    String? subsidyFee3,
    String? subsidyArea3,
    String? plantArea3,
    String? approvalStatusNo,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? approvalRemark,
    String? auditLevel,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? dataStatus,
    String? bankName,
    String? subsidyConfigId,
    String? contractSignType,
    String? bankNum,
    String? network,
    String? lineNumber,
    String? remark,
    String? params,
    String? subsidyPaymentGrain2feedSubList,
    String? ifUpdate,
    String? subsidyItemName,
  }) {
    return SubsidyGrainFeedEntity()
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDId = auditDId ?? this.auditDId
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEId = auditEId ?? this.auditEId
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFId = auditFId ?? this.auditFId
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGId = auditGId ?? this.auditGId
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHId = auditHId ?? this.auditHId
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..subsidyDetailId = subsidyDetailId ?? this.subsidyDetailId
      ..subsidyPaymentNo = subsidyPaymentNo ?? this.subsidyPaymentNo
      ..subsidyYear = subsidyYear ?? this.subsidyYear
      ..subsidyProjectCode = subsidyProjectCode ?? this.subsidyProjectCode
      ..subsidyType = subsidyType ?? this.subsidyType
      ..organizationName = organizationName ?? this.organizationName
      ..organizationNo = organizationNo ?? this.organizationNo
      ..farmerId = farmerId ?? this.farmerId
      ..farmerName = farmerName ?? this.farmerName
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..bankAccount = bankAccount ?? this.bankAccount
      ..landNumber = landNumber ?? this.landNumber
      ..actualPaymentFee = actualPaymentFee ?? this.actualPaymentFee
      ..subsidyWeight = subsidyWeight ?? this.subsidyWeight
      ..plantArea = plantArea ?? this.plantArea
      ..subsidyArea = subsidyArea ?? this.subsidyArea
      ..subsidyStandard1 = subsidyStandard1 ?? this.subsidyStandard1
      ..subsidyWeight1 = subsidyWeight1 ?? this.subsidyWeight1
      ..subsidyFee1 = subsidyFee1 ?? this.subsidyFee1
      ..partnerId = partnerId ?? this.partnerId
      ..subsidyArea1 = subsidyArea1 ?? this.subsidyArea1
      ..plantArea1 = plantArea1 ?? this.plantArea1
      ..subsidyStandard2 = subsidyStandard2 ?? this.subsidyStandard2
      ..subsidyWeight2 = subsidyWeight2 ?? this.subsidyWeight2
      ..subsidyFee2 = subsidyFee2 ?? this.subsidyFee2
      ..subsidyArea2 = subsidyArea2 ?? this.subsidyArea2
      ..plantArea2 = plantArea2 ?? this.plantArea2
      ..subsidyStandard3 = subsidyStandard3 ?? this.subsidyStandard3
      ..subsidyWeight3 = subsidyWeight3 ?? this.subsidyWeight3
      ..subsidyFee3 = subsidyFee3 ?? this.subsidyFee3
      ..subsidyArea3 = subsidyArea3 ?? this.subsidyArea3
      ..plantArea3 = plantArea3 ?? this.plantArea3
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..auditLevel = auditLevel ?? this.auditLevel
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..dataStatus = dataStatus ?? this.dataStatus
      ..bankName = bankName ?? this.bankName
      ..subsidyConfigId = subsidyConfigId ?? this.subsidyConfigId
      ..contractSignType = contractSignType ?? this.contractSignType
      ..bankNum = bankNum ?? this.bankNum
      ..network = network ?? this.network
      ..lineNumber = lineNumber ?? this.lineNumber
      ..remark = remark ?? this.remark
      ..params = params ?? this.params
      ..subsidyPaymentGrain2feedSubList = subsidyPaymentGrain2feedSubList ??
          this.subsidyPaymentGrain2feedSubList
      ..ifUpdate = ifUpdate ?? this.ifUpdate
      ..subsidyItemName = subsidyItemName ?? this.subsidyItemName;
  }
}