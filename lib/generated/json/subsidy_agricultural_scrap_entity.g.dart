import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/publicity/subsidy_agricultural_scrap_entity.dart';

SubsidyAgriculturalScrapEntity $SubsidyAgriculturalScrapEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyAgriculturalScrapEntity subsidyAgriculturalScrapEntity =
      SubsidyAgriculturalScrapEntity();
  final String? subsidyAgmachineScrapId =
      jsonConvert.convert<String>(json['subsidyAgmachineScrapId']);
  if (subsidyAgmachineScrapId != null) {
    subsidyAgriculturalScrapEntity.subsidyAgmachineScrapId =
        subsidyAgmachineScrapId;
  }
  final String? subsidyConfigId =
      jsonConvert.convert<String>(json['subsidyConfigId']);
  if (subsidyConfigId != null) {
    subsidyAgriculturalScrapEntity.subsidyConfigId = subsidyConfigId;
  }
  final String? subsidyYear = jsonConvert.convert<String>(json['subsidyYear']);
  if (subsidyYear != null) {
    subsidyAgriculturalScrapEntity.subsidyYear = subsidyYear;
  }
  final String? subsidyType = jsonConvert.convert<String>(json['subsidyType']);
  if (subsidyType != null) {
    subsidyAgriculturalScrapEntity.subsidyType = subsidyType;
  }
  final String? subsidyProjectCode =
      jsonConvert.convert<String>(json['subsidyProjectCode']);
  if (subsidyProjectCode != null) {
    subsidyAgriculturalScrapEntity.subsidyProjectCode = subsidyProjectCode;
  }
  final String? subsidyBudgetId =
      jsonConvert.convert<String>(json['subsidyBudgetId']);
  if (subsidyBudgetId != null) {
    subsidyAgriculturalScrapEntity.subsidyBudgetId = subsidyBudgetId;
  }
  final String? farmerName = jsonConvert.convert<String>(json['farmerName']);
  if (farmerName != null) {
    subsidyAgriculturalScrapEntity.farmerName = farmerName;
  }
  final String? farmerId = jsonConvert.convert<String>(json['farmerId']);
  if (farmerId != null) {
    subsidyAgriculturalScrapEntity.farmerId = farmerId;
  }
  final String? farmerIdNumber =
      jsonConvert.convert<String>(json['farmerIdNumber']);
  if (farmerIdNumber != null) {
    subsidyAgriculturalScrapEntity.farmerIdNumber = farmerIdNumber;
  }
  final String? certNo = jsonConvert.convert<String>(json['certNo']);
  if (certNo != null) {
    subsidyAgriculturalScrapEntity.certNo = certNo;
  }
  final String? bankAccount = jsonConvert.convert<String>(json['bankAccount']);
  if (bankAccount != null) {
    subsidyAgriculturalScrapEntity.bankAccount = bankAccount;
  }
  final String? bankName = jsonConvert.convert<String>(json['bankName']);
  if (bankName != null) {
    subsidyAgriculturalScrapEntity.bankName = bankName;
  }
  final String? network = jsonConvert.convert<String>(json['network']);
  if (network != null) {
    subsidyAgriculturalScrapEntity.network = network;
  }
  final String? lineNumber = jsonConvert.convert<String>(json['lineNumber']);
  if (lineNumber != null) {
    subsidyAgriculturalScrapEntity.lineNumber = lineNumber;
  }
  final String? amModelName = jsonConvert.convert<String>(json['amModelName']);
  if (amModelName != null) {
    subsidyAgriculturalScrapEntity.amModelName = amModelName;
  }
  final String? factoryCode = jsonConvert.convert<String>(json['factoryCode']);
  if (factoryCode != null) {
    subsidyAgriculturalScrapEntity.factoryCode = factoryCode;
  }
  final String? machineId = jsonConvert.convert<String>(json['machineId']);
  if (machineId != null) {
    subsidyAgriculturalScrapEntity.machineId = machineId;
  }
  final String? subsidyStandard =
      jsonConvert.convert<String>(json['subsidyStandard']);
  if (subsidyStandard != null) {
    subsidyAgriculturalScrapEntity.subsidyStandard = subsidyStandard;
  }
  final String? subsidyNum = jsonConvert.convert<String>(json['subsidyNum']);
  if (subsidyNum != null) {
    subsidyAgriculturalScrapEntity.subsidyNum = subsidyNum;
  }
  final String? actualPaymentFee =
      jsonConvert.convert<String>(json['actualPaymentFee']);
  if (actualPaymentFee != null) {
    subsidyAgriculturalScrapEntity.actualPaymentFee = actualPaymentFee;
  }
  final String? subsidyApplyTime =
      jsonConvert.convert<String>(json['subsidyApplyTime']);
  if (subsidyApplyTime != null) {
    subsidyAgriculturalScrapEntity.subsidyApplyTime = subsidyApplyTime;
  }
  final String? machineNo = jsonConvert.convert<String>(json['machineNo']);
  if (machineNo != null) {
    subsidyAgriculturalScrapEntity.machineNo = machineNo;
  }
  final String? engineNo = jsonConvert.convert<String>(json['engineNo']);
  if (engineNo != null) {
    subsidyAgriculturalScrapEntity.engineNo = engineNo;
  }
  final String? enginePower = jsonConvert.convert<String>(json['enginePower']);
  if (enginePower != null) {
    subsidyAgriculturalScrapEntity.enginePower = enginePower;
  }
  final String? purchaseDate =
      jsonConvert.convert<String>(json['purchaseDate']);
  if (purchaseDate != null) {
    subsidyAgriculturalScrapEntity.purchaseDate = purchaseDate;
  }
  final String? productDate = jsonConvert.convert<String>(json['productDate']);
  if (productDate != null) {
    subsidyAgriculturalScrapEntity.productDate = productDate;
  }
  final String? licenseNo = jsonConvert.convert<String>(json['licenseNo']);
  if (licenseNo != null) {
    subsidyAgriculturalScrapEntity.licenseNo = licenseNo;
  }
  final String? registerDate =
      jsonConvert.convert<String>(json['registerDate']);
  if (registerDate != null) {
    subsidyAgriculturalScrapEntity.registerDate = registerDate;
  }
  final String? amTypeCode1 = jsonConvert.convert<String>(json['amTypeCode1']);
  if (amTypeCode1 != null) {
    subsidyAgriculturalScrapEntity.amTypeCode1 = amTypeCode1;
  }
  final String? amTypeName1 = jsonConvert.convert<String>(json['amTypeName1']);
  if (amTypeName1 != null) {
    subsidyAgriculturalScrapEntity.amTypeName1 = amTypeName1;
  }
  final String? amTypeCode2 = jsonConvert.convert<String>(json['amTypeCode2']);
  if (amTypeCode2 != null) {
    subsidyAgriculturalScrapEntity.amTypeCode2 = amTypeCode2;
  }
  final String? amTypeName2 = jsonConvert.convert<String>(json['amTypeName2']);
  if (amTypeName2 != null) {
    subsidyAgriculturalScrapEntity.amTypeName2 = amTypeName2;
  }
  final String? amTypeCode3 = jsonConvert.convert<String>(json['amTypeCode3']);
  if (amTypeCode3 != null) {
    subsidyAgriculturalScrapEntity.amTypeCode3 = amTypeCode3;
  }
  final String? amTypeName3 = jsonConvert.convert<String>(json['amTypeName3']);
  if (amTypeName3 != null) {
    subsidyAgriculturalScrapEntity.amTypeName3 = amTypeName3;
  }
  final String? recycleConfirmNo =
      jsonConvert.convert<String>(json['recycleConfirmNo']);
  if (recycleConfirmNo != null) {
    subsidyAgriculturalScrapEntity.recycleConfirmNo = recycleConfirmNo;
  }
  final String? verifyStaffName =
      jsonConvert.convert<String>(json['verifyStaffName']);
  if (verifyStaffName != null) {
    subsidyAgriculturalScrapEntity.verifyStaffName = verifyStaffName;
  }
  final String? approvalStatusNo =
      jsonConvert.convert<String>(json['approvalStatusNo']);
  if (approvalStatusNo != null) {
    subsidyAgriculturalScrapEntity.approvalStatusNo = approvalStatusNo;
  }
  final String? contractSignType =
      jsonConvert.convert<String>(json['contractSignType']);
  if (contractSignType != null) {
    subsidyAgriculturalScrapEntity.contractSignType = contractSignType;
  }
  final String? partnerId = jsonConvert.convert<String>(json['partnerId']);
  if (partnerId != null) {
    subsidyAgriculturalScrapEntity.partnerId = partnerId;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    subsidyAgriculturalScrapEntity.organizationNo = organizationNo;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    subsidyAgriculturalScrapEntity.organizationName = organizationName;
  }
  final String? auditLevel = jsonConvert.convert<String>(json['auditLevel']);
  if (auditLevel != null) {
    subsidyAgriculturalScrapEntity.auditLevel = auditLevel;
  }
  final String? auditAFlag = jsonConvert.convert<String>(json['auditAFlag']);
  if (auditAFlag != null) {
    subsidyAgriculturalScrapEntity.auditAFlag = auditAFlag;
  }
  final String? auditAId = jsonConvert.convert<String>(json['auditAId']);
  if (auditAId != null) {
    subsidyAgriculturalScrapEntity.auditAId = auditAId;
  }
  final String? auditAName = jsonConvert.convert<String>(json['auditAName']);
  if (auditAName != null) {
    subsidyAgriculturalScrapEntity.auditAName = auditAName;
  }
  final String? auditATime = jsonConvert.convert<String>(json['auditATime']);
  if (auditATime != null) {
    subsidyAgriculturalScrapEntity.auditATime = auditATime;
  }
  final String? auditBFlag = jsonConvert.convert<String>(json['auditBFlag']);
  if (auditBFlag != null) {
    subsidyAgriculturalScrapEntity.auditBFlag = auditBFlag;
  }
  final String? auditBId = jsonConvert.convert<String>(json['auditBId']);
  if (auditBId != null) {
    subsidyAgriculturalScrapEntity.auditBId = auditBId;
  }
  final String? auditBName = jsonConvert.convert<String>(json['auditBName']);
  if (auditBName != null) {
    subsidyAgriculturalScrapEntity.auditBName = auditBName;
  }
  final String? auditBTime = jsonConvert.convert<String>(json['auditBTime']);
  if (auditBTime != null) {
    subsidyAgriculturalScrapEntity.auditBTime = auditBTime;
  }
  final String? auditCFlag = jsonConvert.convert<String>(json['auditCFlag']);
  if (auditCFlag != null) {
    subsidyAgriculturalScrapEntity.auditCFlag = auditCFlag;
  }
  final String? auditCId = jsonConvert.convert<String>(json['auditCId']);
  if (auditCId != null) {
    subsidyAgriculturalScrapEntity.auditCId = auditCId;
  }
  final String? auditCName = jsonConvert.convert<String>(json['auditCName']);
  if (auditCName != null) {
    subsidyAgriculturalScrapEntity.auditCName = auditCName;
  }
  final String? auditCTime = jsonConvert.convert<String>(json['auditCTime']);
  if (auditCTime != null) {
    subsidyAgriculturalScrapEntity.auditCTime = auditCTime;
  }
  final String? auditDId = jsonConvert.convert<String>(json['auditDId']);
  if (auditDId != null) {
    subsidyAgriculturalScrapEntity.auditDId = auditDId;
  }
  final String? auditDFlag = jsonConvert.convert<String>(json['auditDFlag']);
  if (auditDFlag != null) {
    subsidyAgriculturalScrapEntity.auditDFlag = auditDFlag;
  }
  final String? auditDName = jsonConvert.convert<String>(json['auditDName']);
  if (auditDName != null) {
    subsidyAgriculturalScrapEntity.auditDName = auditDName;
  }
  final String? auditDTime = jsonConvert.convert<String>(json['auditDTime']);
  if (auditDTime != null) {
    subsidyAgriculturalScrapEntity.auditDTime = auditDTime;
  }
  final String? auditEId = jsonConvert.convert<String>(json['auditEId']);
  if (auditEId != null) {
    subsidyAgriculturalScrapEntity.auditEId = auditEId;
  }
  final String? auditEFlag = jsonConvert.convert<String>(json['auditEFlag']);
  if (auditEFlag != null) {
    subsidyAgriculturalScrapEntity.auditEFlag = auditEFlag;
  }
  final String? auditEName = jsonConvert.convert<String>(json['auditEName']);
  if (auditEName != null) {
    subsidyAgriculturalScrapEntity.auditEName = auditEName;
  }
  final String? auditETime = jsonConvert.convert<String>(json['auditETime']);
  if (auditETime != null) {
    subsidyAgriculturalScrapEntity.auditETime = auditETime;
  }
  final String? auditFId = jsonConvert.convert<String>(json['auditFId']);
  if (auditFId != null) {
    subsidyAgriculturalScrapEntity.auditFId = auditFId;
  }
  final String? auditFFlag = jsonConvert.convert<String>(json['auditFFlag']);
  if (auditFFlag != null) {
    subsidyAgriculturalScrapEntity.auditFFlag = auditFFlag;
  }
  final String? auditFName = jsonConvert.convert<String>(json['auditFName']);
  if (auditFName != null) {
    subsidyAgriculturalScrapEntity.auditFName = auditFName;
  }
  final String? auditFTime = jsonConvert.convert<String>(json['auditFTime']);
  if (auditFTime != null) {
    subsidyAgriculturalScrapEntity.auditFTime = auditFTime;
  }
  final String? auditGId = jsonConvert.convert<String>(json['auditGId']);
  if (auditGId != null) {
    subsidyAgriculturalScrapEntity.auditGId = auditGId;
  }
  final String? auditGFlag = jsonConvert.convert<String>(json['auditGFlag']);
  if (auditGFlag != null) {
    subsidyAgriculturalScrapEntity.auditGFlag = auditGFlag;
  }
  final String? auditGName = jsonConvert.convert<String>(json['auditGName']);
  if (auditGName != null) {
    subsidyAgriculturalScrapEntity.auditGName = auditGName;
  }
  final String? auditGTime = jsonConvert.convert<String>(json['auditGTime']);
  if (auditGTime != null) {
    subsidyAgriculturalScrapEntity.auditGTime = auditGTime;
  }
  final String? auditHId = jsonConvert.convert<String>(json['auditHId']);
  if (auditHId != null) {
    subsidyAgriculturalScrapEntity.auditHId = auditHId;
  }
  final String? auditHFlag = jsonConvert.convert<String>(json['auditHFlag']);
  if (auditHFlag != null) {
    subsidyAgriculturalScrapEntity.auditHFlag = auditHFlag;
  }
  final String? auditHName = jsonConvert.convert<String>(json['auditHName']);
  if (auditHName != null) {
    subsidyAgriculturalScrapEntity.auditHName = auditHName;
  }
  final String? auditHTime = jsonConvert.convert<String>(json['auditHTime']);
  if (auditHTime != null) {
    subsidyAgriculturalScrapEntity.auditHTime = auditHTime;
  }
  final String? approvalRemark =
      jsonConvert.convert<String>(json['approvalRemark']);
  if (approvalRemark != null) {
    subsidyAgriculturalScrapEntity.approvalRemark = approvalRemark;
  }
  final String? currentAuditRoleId =
      jsonConvert.convert<String>(json['currentAuditRoleId']);
  if (currentAuditRoleId != null) {
    subsidyAgriculturalScrapEntity.currentAuditRoleId = currentAuditRoleId;
  }
  final String? currentAuditRoleName =
      jsonConvert.convert<String>(json['currentAuditRoleName']);
  if (currentAuditRoleName != null) {
    subsidyAgriculturalScrapEntity.currentAuditRoleName = currentAuditRoleName;
  }
  final String? createBy = jsonConvert.convert<String>(json['createBy']);
  if (createBy != null) {
    subsidyAgriculturalScrapEntity.createBy = createBy;
  }
  final String? createTime = jsonConvert.convert<String>(json['createTime']);
  if (createTime != null) {
    subsidyAgriculturalScrapEntity.createTime = createTime;
  }
  final String? updateBy = jsonConvert.convert<String>(json['updateBy']);
  if (updateBy != null) {
    subsidyAgriculturalScrapEntity.updateBy = updateBy;
  }
  final String? updateTime = jsonConvert.convert<String>(json['updateTime']);
  if (updateTime != null) {
    subsidyAgriculturalScrapEntity.updateTime = updateTime;
  }
  final String? statusCd = jsonConvert.convert<String>(json['statusCd']);
  if (statusCd != null) {
    subsidyAgriculturalScrapEntity.statusCd = statusCd;
  }
  final String? dataStatus = jsonConvert.convert<String>(json['dataStatus']);
  if (dataStatus != null) {
    subsidyAgriculturalScrapEntity.dataStatus = dataStatus;
  }
  final String? ifUpdate = jsonConvert.convert<String>(json['ifUpdate']);
  if (ifUpdate != null) {
    subsidyAgriculturalScrapEntity.ifUpdate = ifUpdate;
  }
  final String? subsidyItemName =
      jsonConvert.convert<String>(json['subsidyItemName']);
  if (subsidyItemName != null) {
    subsidyAgriculturalScrapEntity.subsidyItemName = subsidyItemName;
  }
  final String? subsidyBudgetTitle =
      jsonConvert.convert<String>(json['subsidyBudgetTitle']);
  if (subsidyBudgetTitle != null) {
    subsidyAgriculturalScrapEntity.subsidyBudgetTitle = subsidyBudgetTitle;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    subsidyAgriculturalScrapEntity.remark = remark;
  }
  final String? params = jsonConvert.convert<String>(json['params']);
  if (params != null) {
    subsidyAgriculturalScrapEntity.params = params;
  }
  return subsidyAgriculturalScrapEntity;
}

Map<String, dynamic> $SubsidyAgriculturalScrapEntityToJson(
    SubsidyAgriculturalScrapEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyAgmachineScrapId'] = entity.subsidyAgmachineScrapId;
  data['subsidyConfigId'] = entity.subsidyConfigId;
  data['subsidyYear'] = entity.subsidyYear;
  data['subsidyType'] = entity.subsidyType;
  data['subsidyProjectCode'] = entity.subsidyProjectCode;
  data['subsidyBudgetId'] = entity.subsidyBudgetId;
  data['farmerName'] = entity.farmerName;
  data['farmerId'] = entity.farmerId;
  data['farmerIdNumber'] = entity.farmerIdNumber;
  data['certNo'] = entity.certNo;
  data['bankAccount'] = entity.bankAccount;
  data['bankName'] = entity.bankName;
  data['network'] = entity.network;
  data['lineNumber'] = entity.lineNumber;
  data['amModelName'] = entity.amModelName;
  data['factoryCode'] = entity.factoryCode;
  data['machineId'] = entity.machineId;
  data['subsidyStandard'] = entity.subsidyStandard;
  data['subsidyNum'] = entity.subsidyNum;
  data['actualPaymentFee'] = entity.actualPaymentFee;
  data['subsidyApplyTime'] = entity.subsidyApplyTime;
  data['machineNo'] = entity.machineNo;
  data['engineNo'] = entity.engineNo;
  data['enginePower'] = entity.enginePower;
  data['purchaseDate'] = entity.purchaseDate;
  data['productDate'] = entity.productDate;
  data['licenseNo'] = entity.licenseNo;
  data['registerDate'] = entity.registerDate;
  data['amTypeCode1'] = entity.amTypeCode1;
  data['amTypeName1'] = entity.amTypeName1;
  data['amTypeCode2'] = entity.amTypeCode2;
  data['amTypeName2'] = entity.amTypeName2;
  data['amTypeCode3'] = entity.amTypeCode3;
  data['amTypeName3'] = entity.amTypeName3;
  data['recycleConfirmNo'] = entity.recycleConfirmNo;
  data['verifyStaffName'] = entity.verifyStaffName;
  data['approvalStatusNo'] = entity.approvalStatusNo;
  data['contractSignType'] = entity.contractSignType;
  data['partnerId'] = entity.partnerId;
  data['organizationNo'] = entity.organizationNo;
  data['organizationName'] = entity.organizationName;
  data['auditLevel'] = entity.auditLevel;
  data['auditAFlag'] = entity.auditAFlag;
  data['auditAId'] = entity.auditAId;
  data['auditAName'] = entity.auditAName;
  data['auditATime'] = entity.auditATime;
  data['auditBFlag'] = entity.auditBFlag;
  data['auditBId'] = entity.auditBId;
  data['auditBName'] = entity.auditBName;
  data['auditBTime'] = entity.auditBTime;
  data['auditCFlag'] = entity.auditCFlag;
  data['auditCId'] = entity.auditCId;
  data['auditCName'] = entity.auditCName;
  data['auditCTime'] = entity.auditCTime;
  data['auditDId'] = entity.auditDId;
  data['auditDFlag'] = entity.auditDFlag;
  data['auditDName'] = entity.auditDName;
  data['auditDTime'] = entity.auditDTime;
  data['auditEId'] = entity.auditEId;
  data['auditEFlag'] = entity.auditEFlag;
  data['auditEName'] = entity.auditEName;
  data['auditETime'] = entity.auditETime;
  data['auditFId'] = entity.auditFId;
  data['auditFFlag'] = entity.auditFFlag;
  data['auditFName'] = entity.auditFName;
  data['auditFTime'] = entity.auditFTime;
  data['auditGId'] = entity.auditGId;
  data['auditGFlag'] = entity.auditGFlag;
  data['auditGName'] = entity.auditGName;
  data['auditGTime'] = entity.auditGTime;
  data['auditHId'] = entity.auditHId;
  data['auditHFlag'] = entity.auditHFlag;
  data['auditHName'] = entity.auditHName;
  data['auditHTime'] = entity.auditHTime;
  data['approvalRemark'] = entity.approvalRemark;
  data['currentAuditRoleId'] = entity.currentAuditRoleId;
  data['currentAuditRoleName'] = entity.currentAuditRoleName;
  data['createBy'] = entity.createBy;
  data['createTime'] = entity.createTime;
  data['updateBy'] = entity.updateBy;
  data['updateTime'] = entity.updateTime;
  data['statusCd'] = entity.statusCd;
  data['dataStatus'] = entity.dataStatus;
  data['ifUpdate'] = entity.ifUpdate;
  data['subsidyItemName'] = entity.subsidyItemName;
  data['subsidyBudgetTitle'] = entity.subsidyBudgetTitle;
  data['remark'] = entity.remark;
  data['params'] = entity.params;
  return data;
}

extension SubsidyAgriculturalScrapEntityExtension
    on SubsidyAgriculturalScrapEntity {
  SubsidyAgriculturalScrapEntity copyWith({
    String? subsidyAgmachineScrapId,
    String? subsidyConfigId,
    String? subsidyYear,
    String? subsidyType,
    String? subsidyProjectCode,
    String? subsidyBudgetId,
    String? farmerName,
    String? farmerId,
    String? farmerIdNumber,
    String? certNo,
    String? bankAccount,
    String? bankName,
    String? network,
    String? lineNumber,
    String? amModelName,
    String? factoryCode,
    String? machineId,
    String? subsidyStandard,
    String? subsidyNum,
    String? actualPaymentFee,
    String? subsidyApplyTime,
    String? machineNo,
    String? engineNo,
    String? enginePower,
    String? purchaseDate,
    String? productDate,
    String? licenseNo,
    String? registerDate,
    String? amTypeCode1,
    String? amTypeName1,
    String? amTypeCode2,
    String? amTypeName2,
    String? amTypeCode3,
    String? amTypeName3,
    String? recycleConfirmNo,
    String? verifyStaffName,
    String? approvalStatusNo,
    String? contractSignType,
    String? partnerId,
    String? organizationNo,
    String? organizationName,
    String? auditLevel,
    String? auditAFlag,
    String? auditAId,
    String? auditAName,
    String? auditATime,
    String? auditBFlag,
    String? auditBId,
    String? auditBName,
    String? auditBTime,
    String? auditCFlag,
    String? auditCId,
    String? auditCName,
    String? auditCTime,
    String? auditDId,
    String? auditDFlag,
    String? auditDName,
    String? auditDTime,
    String? auditEId,
    String? auditEFlag,
    String? auditEName,
    String? auditETime,
    String? auditFId,
    String? auditFFlag,
    String? auditFName,
    String? auditFTime,
    String? auditGId,
    String? auditGFlag,
    String? auditGName,
    String? auditGTime,
    String? auditHId,
    String? auditHFlag,
    String? auditHName,
    String? auditHTime,
    String? approvalRemark,
    String? currentAuditRoleId,
    String? currentAuditRoleName,
    String? createBy,
    String? createTime,
    String? updateBy,
    String? updateTime,
    String? statusCd,
    String? dataStatus,
    String? ifUpdate,
    String? subsidyItemName,
    String? subsidyBudgetTitle,
    String? remark,
    String? params,
  }) {
    return SubsidyAgriculturalScrapEntity()
      ..subsidyAgmachineScrapId =
          subsidyAgmachineScrapId ?? this.subsidyAgmachineScrapId
      ..subsidyConfigId = subsidyConfigId ?? this.subsidyConfigId
      ..subsidyYear = subsidyYear ?? this.subsidyYear
      ..subsidyType = subsidyType ?? this.subsidyType
      ..subsidyProjectCode = subsidyProjectCode ?? this.subsidyProjectCode
      ..subsidyBudgetId = subsidyBudgetId ?? this.subsidyBudgetId
      ..farmerName = farmerName ?? this.farmerName
      ..farmerId = farmerId ?? this.farmerId
      ..farmerIdNumber = farmerIdNumber ?? this.farmerIdNumber
      ..certNo = certNo ?? this.certNo
      ..bankAccount = bankAccount ?? this.bankAccount
      ..bankName = bankName ?? this.bankName
      ..network = network ?? this.network
      ..lineNumber = lineNumber ?? this.lineNumber
      ..amModelName = amModelName ?? this.amModelName
      ..factoryCode = factoryCode ?? this.factoryCode
      ..machineId = machineId ?? this.machineId
      ..subsidyStandard = subsidyStandard ?? this.subsidyStandard
      ..subsidyNum = subsidyNum ?? this.subsidyNum
      ..actualPaymentFee = actualPaymentFee ?? this.actualPaymentFee
      ..subsidyApplyTime = subsidyApplyTime ?? this.subsidyApplyTime
      ..machineNo = machineNo ?? this.machineNo
      ..engineNo = engineNo ?? this.engineNo
      ..enginePower = enginePower ?? this.enginePower
      ..purchaseDate = purchaseDate ?? this.purchaseDate
      ..productDate = productDate ?? this.productDate
      ..licenseNo = licenseNo ?? this.licenseNo
      ..registerDate = registerDate ?? this.registerDate
      ..amTypeCode1 = amTypeCode1 ?? this.amTypeCode1
      ..amTypeName1 = amTypeName1 ?? this.amTypeName1
      ..amTypeCode2 = amTypeCode2 ?? this.amTypeCode2
      ..amTypeName2 = amTypeName2 ?? this.amTypeName2
      ..amTypeCode3 = amTypeCode3 ?? this.amTypeCode3
      ..amTypeName3 = amTypeName3 ?? this.amTypeName3
      ..recycleConfirmNo = recycleConfirmNo ?? this.recycleConfirmNo
      ..verifyStaffName = verifyStaffName ?? this.verifyStaffName
      ..approvalStatusNo = approvalStatusNo ?? this.approvalStatusNo
      ..contractSignType = contractSignType ?? this.contractSignType
      ..partnerId = partnerId ?? this.partnerId
      ..organizationNo = organizationNo ?? this.organizationNo
      ..organizationName = organizationName ?? this.organizationName
      ..auditLevel = auditLevel ?? this.auditLevel
      ..auditAFlag = auditAFlag ?? this.auditAFlag
      ..auditAId = auditAId ?? this.auditAId
      ..auditAName = auditAName ?? this.auditAName
      ..auditATime = auditATime ?? this.auditATime
      ..auditBFlag = auditBFlag ?? this.auditBFlag
      ..auditBId = auditBId ?? this.auditBId
      ..auditBName = auditBName ?? this.auditBName
      ..auditBTime = auditBTime ?? this.auditBTime
      ..auditCFlag = auditCFlag ?? this.auditCFlag
      ..auditCId = auditCId ?? this.auditCId
      ..auditCName = auditCName ?? this.auditCName
      ..auditCTime = auditCTime ?? this.auditCTime
      ..auditDId = auditDId ?? this.auditDId
      ..auditDFlag = auditDFlag ?? this.auditDFlag
      ..auditDName = auditDName ?? this.auditDName
      ..auditDTime = auditDTime ?? this.auditDTime
      ..auditEId = auditEId ?? this.auditEId
      ..auditEFlag = auditEFlag ?? this.auditEFlag
      ..auditEName = auditEName ?? this.auditEName
      ..auditETime = auditETime ?? this.auditETime
      ..auditFId = auditFId ?? this.auditFId
      ..auditFFlag = auditFFlag ?? this.auditFFlag
      ..auditFName = auditFName ?? this.auditFName
      ..auditFTime = auditFTime ?? this.auditFTime
      ..auditGId = auditGId ?? this.auditGId
      ..auditGFlag = auditGFlag ?? this.auditGFlag
      ..auditGName = auditGName ?? this.auditGName
      ..auditGTime = auditGTime ?? this.auditGTime
      ..auditHId = auditHId ?? this.auditHId
      ..auditHFlag = auditHFlag ?? this.auditHFlag
      ..auditHName = auditHName ?? this.auditHName
      ..auditHTime = auditHTime ?? this.auditHTime
      ..approvalRemark = approvalRemark ?? this.approvalRemark
      ..currentAuditRoleId = currentAuditRoleId ?? this.currentAuditRoleId
      ..currentAuditRoleName = currentAuditRoleName ?? this.currentAuditRoleName
      ..createBy = createBy ?? this.createBy
      ..createTime = createTime ?? this.createTime
      ..updateBy = updateBy ?? this.updateBy
      ..updateTime = updateTime ?? this.updateTime
      ..statusCd = statusCd ?? this.statusCd
      ..dataStatus = dataStatus ?? this.dataStatus
      ..ifUpdate = ifUpdate ?? this.ifUpdate
      ..subsidyItemName = subsidyItemName ?? this.subsidyItemName
      ..subsidyBudgetTitle = subsidyBudgetTitle ?? this.subsidyBudgetTitle
      ..remark = remark ?? this.remark
      ..params = params ?? this.params;
  }
}
