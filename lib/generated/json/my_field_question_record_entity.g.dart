import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/myfieldchat/my_field_question_record_entity.dart';

MyFieldQuestionRecordEntity $MyFieldQuestionRecordEntityFromJson(
    Map<String, dynamic> json) {
  final MyFieldQuestionRecordEntity myFieldQuestionRecordEntity =
      MyFieldQuestionRecordEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    myFieldQuestionRecordEntity.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    myFieldQuestionRecordEntity.name = name;
  }
  final MyFieldQuestionRecordInputs? inputs =
      jsonConvert.convert<MyFieldQuestionRecordInputs>(json['inputs']);
  if (inputs != null) {
    myFieldQuestionRecordEntity.inputs = inputs;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    myFieldQuestionRecordEntity.status = status;
  }
  final String? introduction =
      jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    myFieldQuestionRecordEntity.introduction = introduction;
  }
  final int? createdAt = jsonConvert.convert<int>(json['created_at']);
  if (createdAt != null) {
    myFieldQuestionRecordEntity.createdAt = createdAt;
  }
  return myFieldQuestionRecordEntity;
}

Map<String, dynamic> $MyFieldQuestionRecordEntityToJson(
    MyFieldQuestionRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['inputs'] = entity.inputs?.toJson();
  data['status'] = entity.status;
  data['introduction'] = entity.introduction;
  data['created_at'] = entity.createdAt;
  return data;
}

extension MyFieldQuestionRecordEntityExtension on MyFieldQuestionRecordEntity {
  MyFieldQuestionRecordEntity copyWith({
    String? id,
    String? name,
    MyFieldQuestionRecordInputs? inputs,
    String? status,
    String? introduction,
    int? createdAt,
  }) {
    return MyFieldQuestionRecordEntity()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..inputs = inputs ?? this.inputs
      ..status = status ?? this.status
      ..introduction = introduction ?? this.introduction
      ..createdAt = createdAt ?? this.createdAt;
  }
}

MyFieldQuestionRecordInputs $MyFieldQuestionRecordInputsFromJson(
    Map<String, dynamic> json) {
  final MyFieldQuestionRecordInputs myFieldQuestionRecordInputs =
      MyFieldQuestionRecordInputs();
  return myFieldQuestionRecordInputs;
}

Map<String, dynamic> $MyFieldQuestionRecordInputsToJson(
    MyFieldQuestionRecordInputs entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension MyFieldQuestionRecordInputsExtension on MyFieldQuestionRecordInputs {}
