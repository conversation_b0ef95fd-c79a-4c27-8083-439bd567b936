import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_publicity_entity.dart';

SubsidyPublicityEntity $SubsidyPublicityEntityFromJson(
    Map<String, dynamic> json) {
  final SubsidyPublicityEntity subsidyPublicityEntity =
      SubsidyPublicityEntity();
  final String? subsidyAnnounceConfigId =
      jsonConvert.convert<String>(json['subsidyAnnounceConfigId']);
  if (subsidyAnnounceConfigId != null) {
    subsidyPublicityEntity.subsidyAnnounceConfigId = subsidyAnnounceConfigId;
  }
  final String? announceTitle =
      jsonConvert.convert<String>(json['announceTitle']);
  if (announceTitle != null) {
    subsidyPublicityEntity.announceTitle = announceTitle;
  }
  final String? organizationNo =
      jsonConvert.convert<String>(json['organizationNo']);
  if (organizationNo != null) {
    subsidyPublicityEntity.organizationNo = organizationNo;
  }
  final String? organizationName =
      jsonConvert.convert<String>(json['organizationName']);
  if (organizationName != null) {
    subsidyPublicityEntity.organizationName = organizationName;
  }
  final String? subsidyProjectCode =
      jsonConvert.convert<String>(json['subsidyProjectCode']);
  if (subsidyProjectCode != null) {
    subsidyPublicityEntity.subsidyProjectCode = subsidyProjectCode;
  }
  final String? subsidyItemName =
      jsonConvert.convert<String>(json['subsidyItemName']);
  if (subsidyItemName != null) {
    subsidyPublicityEntity.subsidyItemName = subsidyItemName;
  }
  final dynamic details = json['details'];
  if (details != null) {
    subsidyPublicityEntity.details = details;
  }
  final int? feedbackStatus = jsonConvert.convert<int>(json['feedbackStatus']);
  if (feedbackStatus != null) {
    subsidyPublicityEntity.feedbackStatus = feedbackStatus;
  }
  final int? subsidyClassify =
      jsonConvert.convert<int>(json['subsidyClassify']);
  if (subsidyClassify != null) {
    subsidyPublicityEntity.subsidyClassify = subsidyClassify;
  }
  final dynamic subsidyAnnounceType = json['subsidyAnnounceType'];
  if (subsidyAnnounceType != null) {
    subsidyPublicityEntity.subsidyAnnounceType = subsidyAnnounceType;
  }
  return subsidyPublicityEntity;
}

Map<String, dynamic> $SubsidyPublicityEntityToJson(
    SubsidyPublicityEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyAnnounceConfigId'] = entity.subsidyAnnounceConfigId;
  data['announceTitle'] = entity.announceTitle;
  data['organizationNo'] = entity.organizationNo;
  data['organizationName'] = entity.organizationName;
  data['subsidyProjectCode'] = entity.subsidyProjectCode;
  data['subsidyItemName'] = entity.subsidyItemName;
  data['details'] = entity.details;
  data['feedbackStatus'] = entity.feedbackStatus;
  data['subsidyClassify'] = entity.subsidyClassify;
  data['subsidyAnnounceType'] = entity.subsidyAnnounceType;
  return data;
}

extension SubsidyPublicityEntityExtension on SubsidyPublicityEntity {
  SubsidyPublicityEntity copyWith({
    String? subsidyAnnounceConfigId,
    String? announceTitle,
    String? organizationNo,
    String? organizationName,
    String? subsidyProjectCode,
    String? subsidyItemName,
    dynamic details,
    int? feedbackStatus,
    int? subsidyClassify,
    dynamic subsidyAnnounceType,
  }) {
    return SubsidyPublicityEntity()
      ..subsidyAnnounceConfigId =
          subsidyAnnounceConfigId ?? this.subsidyAnnounceConfigId
      ..announceTitle = announceTitle ?? this.announceTitle
      ..organizationNo = organizationNo ?? this.organizationNo
      ..organizationName = organizationName ?? this.organizationName
      ..subsidyProjectCode = subsidyProjectCode ?? this.subsidyProjectCode
      ..subsidyItemName = subsidyItemName ?? this.subsidyItemName
      ..details = details ?? this.details
      ..feedbackStatus = feedbackStatus ?? this.feedbackStatus
      ..subsidyClassify = subsidyClassify ?? this.subsidyClassify
      ..subsidyAnnounceType = subsidyAnnounceType ?? this.subsidyAnnounceType;
  }
}
