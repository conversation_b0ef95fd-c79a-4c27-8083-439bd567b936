import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/purchase_subsidy_entity.dart';

PurchaseSubsidyEntity $PurchaseSubsidyEntityFromJson(
    Map<String, dynamic> json) {
  final PurchaseSubsidyEntity purchaseSubsidyEntity = PurchaseSubsidyEntity();
  final String? orgCode = jsonConvert.convert<String>(json['orgCode']);
  if (orgCode != null) {
    purchaseSubsidyEntity.orgCode = orgCode;
  }
  final String? orgName = jsonConvert.convert<String>(json['orgName']);
  if (orgName != null) {
    purchaseSubsidyEntity.orgName = orgName;
  }
  final String? amOwnerName = jsonConvert.convert<String>(json['amOwnerName']);
  if (amOwnerName != null) {
    purchaseSubsidyEntity.amOwnerName = amOwnerName;
  }
  final String? amTypeCode3 = jsonConvert.convert<String>(json['amTypeCode3']);
  if (amTypeCode3 != null) {
    purchaseSubsidyEntity.amTypeCode3 = amTypeCode3;
  }
  final String? amTypeName3 = jsonConvert.convert<String>(json['amTypeName3']);
  if (amTypeName3 != null) {
    purchaseSubsidyEntity.amTypeName3 = amTypeName3;
  }
  final String? companyName = jsonConvert.convert<String>(json['companyName']);
  if (companyName != null) {
    purchaseSubsidyEntity.companyName = companyName;
  }
  final String? productName = jsonConvert.convert<String>(json['productName']);
  if (productName != null) {
    purchaseSubsidyEntity.productName = productName;
  }
  final String? amModelName = jsonConvert.convert<String>(json['amModelName']);
  if (amModelName != null) {
    purchaseSubsidyEntity.amModelName = amModelName;
  }
  final String? totalSubsidy =
      jsonConvert.convert<String>(json['totalSubsidy']);
  if (totalSubsidy != null) {
    purchaseSubsidyEntity.totalSubsidy = totalSubsidy;
  }
  final String? stateName = jsonConvert.convert<String>(json['stateName']);
  if (stateName != null) {
    purchaseSubsidyEntity.stateName = stateName;
  }
  final String? factoryCode = jsonConvert.convert<String>(json['factoryCode']);
  if (factoryCode != null) {
    purchaseSubsidyEntity.factoryCode = factoryCode;
  }
  final String? purchaseDate =
      jsonConvert.convert<String>(json['purchaseDate']);
  if (purchaseDate != null) {
    purchaseSubsidyEntity.purchaseDate = purchaseDate;
  }
  final String? singleSalePrice =
      jsonConvert.convert<String>(json['singleSalePrice']);
  if (singleSalePrice != null) {
    purchaseSubsidyEntity.singleSalePrice = singleSalePrice;
  }
  final String? saleCount = jsonConvert.convert<String>(json['saleCount']);
  if (saleCount != null) {
    purchaseSubsidyEntity.saleCount = saleCount;
  }
  final dynamic certNo = json['certNo'];
  if (certNo != null) {
    purchaseSubsidyEntity.certNo = certNo;
  }
  final String? amTypeCode2 = jsonConvert.convert<String>(json['amTypeCode2']);
  if (amTypeCode2 != null) {
    purchaseSubsidyEntity.amTypeCode2 = amTypeCode2;
  }
  final String? amTypeName2 = jsonConvert.convert<String>(json['amTypeName2']);
  if (amTypeName2 != null) {
    purchaseSubsidyEntity.amTypeName2 = amTypeName2;
  }
  final String? amTypeCode1 = jsonConvert.convert<String>(json['amTypeCode1']);
  if (amTypeCode1 != null) {
    purchaseSubsidyEntity.amTypeCode1 = amTypeCode1;
  }
  final String? amTypeName1 = jsonConvert.convert<String>(json['amTypeName1']);
  if (amTypeName1 != null) {
    purchaseSubsidyEntity.amTypeName1 = amTypeName1;
  }
  final dynamic year = json['year'];
  if (year != null) {
    purchaseSubsidyEntity.year = year;
  }
  return purchaseSubsidyEntity;
}

Map<String, dynamic> $PurchaseSubsidyEntityToJson(
    PurchaseSubsidyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['orgCode'] = entity.orgCode;
  data['orgName'] = entity.orgName;
  data['amOwnerName'] = entity.amOwnerName;
  data['amTypeCode3'] = entity.amTypeCode3;
  data['amTypeName3'] = entity.amTypeName3;
  data['companyName'] = entity.companyName;
  data['productName'] = entity.productName;
  data['amModelName'] = entity.amModelName;
  data['totalSubsidy'] = entity.totalSubsidy;
  data['stateName'] = entity.stateName;
  data['factoryCode'] = entity.factoryCode;
  data['purchaseDate'] = entity.purchaseDate;
  data['singleSalePrice'] = entity.singleSalePrice;
  data['saleCount'] = entity.saleCount;
  data['certNo'] = entity.certNo;
  data['amTypeCode2'] = entity.amTypeCode2;
  data['amTypeName2'] = entity.amTypeName2;
  data['amTypeCode1'] = entity.amTypeCode1;
  data['amTypeName1'] = entity.amTypeName1;
  data['year'] = entity.year;
  return data;
}

extension PurchaseSubsidyEntityExtension on PurchaseSubsidyEntity {
  PurchaseSubsidyEntity copyWith({
    String? orgCode,
    String? orgName,
    String? amOwnerName,
    String? amTypeCode3,
    String? amTypeName3,
    String? companyName,
    String? productName,
    String? amModelName,
    String? totalSubsidy,
    String? stateName,
    String? factoryCode,
    String? purchaseDate,
    String? singleSalePrice,
    String? saleCount,
    dynamic certNo,
    String? amTypeCode2,
    String? amTypeName2,
    String? amTypeCode1,
    String? amTypeName1,
    dynamic year,
  }) {
    return PurchaseSubsidyEntity()
      ..orgCode = orgCode ?? this.orgCode
      ..orgName = orgName ?? this.orgName
      ..amOwnerName = amOwnerName ?? this.amOwnerName
      ..amTypeCode3 = amTypeCode3 ?? this.amTypeCode3
      ..amTypeName3 = amTypeName3 ?? this.amTypeName3
      ..companyName = companyName ?? this.companyName
      ..productName = productName ?? this.productName
      ..amModelName = amModelName ?? this.amModelName
      ..totalSubsidy = totalSubsidy ?? this.totalSubsidy
      ..stateName = stateName ?? this.stateName
      ..factoryCode = factoryCode ?? this.factoryCode
      ..purchaseDate = purchaseDate ?? this.purchaseDate
      ..singleSalePrice = singleSalePrice ?? this.singleSalePrice
      ..saleCount = saleCount ?? this.saleCount
      ..certNo = certNo ?? this.certNo
      ..amTypeCode2 = amTypeCode2 ?? this.amTypeCode2
      ..amTypeName2 = amTypeName2 ?? this.amTypeName2
      ..amTypeCode1 = amTypeCode1 ?? this.amTypeCode1
      ..amTypeName1 = amTypeName1 ?? this.amTypeName1
      ..year = year ?? this.year;
  }
}
