import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/myfield/myfieldchat/my_field_history_message_entity.dart';

MyFieldHistoryMessageEntity $MyFieldHistoryMessageEntityFromJson(
    Map<String, dynamic> json) {
  final MyFieldHistoryMessageEntity myFieldHistoryMessageEntity =
      MyFieldHistoryMessageEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    myFieldHistoryMessageEntity.id = id;
  }
  final String? conversationId =
      jsonConvert.convert<String>(json['conversation_id']);
  if (conversationId != null) {
    myFieldHistoryMessageEntity.conversationId = conversationId;
  }
  final MyFieldHistoryMessageInputs? inputs =
      jsonConvert.convert<MyFieldHistoryMessageInputs>(json['inputs']);
  if (inputs != null) {
    myFieldHistoryMessageEntity.inputs = inputs;
  }
  final String? query = jsonConvert.convert<String>(json['query']);
  if (query != null) {
    myFieldHistoryMessageEntity.query = query;
  }
  final String? answer = jsonConvert.convert<String>(json['answer']);
  if (answer != null) {
    myFieldHistoryMessageEntity.answer = answer;
  }
  final List<dynamic>? messageFiles =
      (json['message_files'] as List<dynamic>?)?.map((e) => e).toList();
  if (messageFiles != null) {
    myFieldHistoryMessageEntity.messageFiles = messageFiles;
  }
  final dynamic feedback = json['feedback'];
  if (feedback != null) {
    myFieldHistoryMessageEntity.feedback = feedback;
  }
  final List<MyFieldHistoryMessageRetrieverResources>? retrieverResources =
      (json['retriever_resources'] as List<dynamic>?)
          ?.map((e) =>
              jsonConvert.convert<MyFieldHistoryMessageRetrieverResources>(e)
                  as MyFieldHistoryMessageRetrieverResources)
          .toList();
  if (retrieverResources != null) {
    myFieldHistoryMessageEntity.retrieverResources = retrieverResources;
  }
  final int? createdAt = jsonConvert.convert<int>(json['created_at']);
  if (createdAt != null) {
    myFieldHistoryMessageEntity.createdAt = createdAt;
  }
  final List<dynamic>? agentThoughts =
      (json['agent_thoughts'] as List<dynamic>?)?.map((e) => e).toList();
  if (agentThoughts != null) {
    myFieldHistoryMessageEntity.agentThoughts = agentThoughts;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    myFieldHistoryMessageEntity.status = status;
  }
  final dynamic error = json['error'];
  if (error != null) {
    myFieldHistoryMessageEntity.error = error;
  }
  return myFieldHistoryMessageEntity;
}

Map<String, dynamic> $MyFieldHistoryMessageEntityToJson(
    MyFieldHistoryMessageEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['conversation_id'] = entity.conversationId;
  data['inputs'] = entity.inputs?.toJson();
  data['query'] = entity.query;
  data['answer'] = entity.answer;
  data['message_files'] = entity.messageFiles;
  data['feedback'] = entity.feedback;
  data['retriever_resources'] =
      entity.retrieverResources?.map((v) => v.toJson()).toList();
  data['created_at'] = entity.createdAt;
  data['agent_thoughts'] = entity.agentThoughts;
  data['status'] = entity.status;
  data['error'] = entity.error;
  return data;
}

extension MyFieldHistoryMessageEntityExtension on MyFieldHistoryMessageEntity {
  MyFieldHistoryMessageEntity copyWith({
    String? id,
    String? conversationId,
    MyFieldHistoryMessageInputs? inputs,
    String? query,
    String? answer,
    List<dynamic>? messageFiles,
    dynamic feedback,
    List<MyFieldHistoryMessageRetrieverResources>? retrieverResources,
    int? createdAt,
    List<dynamic>? agentThoughts,
    String? status,
    dynamic error,
  }) {
    return MyFieldHistoryMessageEntity()
      ..id = id ?? this.id
      ..conversationId = conversationId ?? this.conversationId
      ..inputs = inputs ?? this.inputs
      ..query = query ?? this.query
      ..answer = answer ?? this.answer
      ..messageFiles = messageFiles ?? this.messageFiles
      ..feedback = feedback ?? this.feedback
      ..retrieverResources = retrieverResources ?? this.retrieverResources
      ..createdAt = createdAt ?? this.createdAt
      ..agentThoughts = agentThoughts ?? this.agentThoughts
      ..status = status ?? this.status
      ..error = error ?? this.error;
  }
}

MyFieldHistoryMessageInputs $MyFieldHistoryMessageInputsFromJson(
    Map<String, dynamic> json) {
  final MyFieldHistoryMessageInputs myFieldHistoryMessageInputs =
      MyFieldHistoryMessageInputs();
  return myFieldHistoryMessageInputs;
}

Map<String, dynamic> $MyFieldHistoryMessageInputsToJson(
    MyFieldHistoryMessageInputs entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  return data;
}

extension MyFieldHistoryMessageInputsExtension on MyFieldHistoryMessageInputs {}

MyFieldHistoryMessageRetrieverResources
    $MyFieldHistoryMessageRetrieverResourcesFromJson(
        Map<String, dynamic> json) {
  final MyFieldHistoryMessageRetrieverResources
      myFieldHistoryMessageRetrieverResources =
      MyFieldHistoryMessageRetrieverResources();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    myFieldHistoryMessageRetrieverResources.id = id;
  }
  final String? messageId = jsonConvert.convert<String>(json['message_id']);
  if (messageId != null) {
    myFieldHistoryMessageRetrieverResources.messageId = messageId;
  }
  final int? position = jsonConvert.convert<int>(json['position']);
  if (position != null) {
    myFieldHistoryMessageRetrieverResources.position = position;
  }
  final String? datasetId = jsonConvert.convert<String>(json['dataset_id']);
  if (datasetId != null) {
    myFieldHistoryMessageRetrieverResources.datasetId = datasetId;
  }
  final String? datasetName = jsonConvert.convert<String>(json['dataset_name']);
  if (datasetName != null) {
    myFieldHistoryMessageRetrieverResources.datasetName = datasetName;
  }
  final String? documentId = jsonConvert.convert<String>(json['document_id']);
  if (documentId != null) {
    myFieldHistoryMessageRetrieverResources.documentId = documentId;
  }
  final String? documentName =
      jsonConvert.convert<String>(json['document_name']);
  if (documentName != null) {
    myFieldHistoryMessageRetrieverResources.documentName = documentName;
  }
  final String? dataSourceType =
      jsonConvert.convert<String>(json['data_source_type']);
  if (dataSourceType != null) {
    myFieldHistoryMessageRetrieverResources.dataSourceType = dataSourceType;
  }
  final String? segmentId = jsonConvert.convert<String>(json['segment_id']);
  if (segmentId != null) {
    myFieldHistoryMessageRetrieverResources.segmentId = segmentId;
  }
  final double? score = jsonConvert.convert<double>(json['score']);
  if (score != null) {
    myFieldHistoryMessageRetrieverResources.score = score;
  }
  final int? hitCount = jsonConvert.convert<int>(json['hit_count']);
  if (hitCount != null) {
    myFieldHistoryMessageRetrieverResources.hitCount = hitCount;
  }
  final int? wordCount = jsonConvert.convert<int>(json['word_count']);
  if (wordCount != null) {
    myFieldHistoryMessageRetrieverResources.wordCount = wordCount;
  }
  final int? segmentPosition =
      jsonConvert.convert<int>(json['segment_position']);
  if (segmentPosition != null) {
    myFieldHistoryMessageRetrieverResources.segmentPosition = segmentPosition;
  }
  final dynamic indexNodeHash = json['index_node_hash'];
  if (indexNodeHash != null) {
    myFieldHistoryMessageRetrieverResources.indexNodeHash = indexNodeHash;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    myFieldHistoryMessageRetrieverResources.content = content;
  }
  final int? createdAt = jsonConvert.convert<int>(json['created_at']);
  if (createdAt != null) {
    myFieldHistoryMessageRetrieverResources.createdAt = createdAt;
  }
  return myFieldHistoryMessageRetrieverResources;
}

Map<String, dynamic> $MyFieldHistoryMessageRetrieverResourcesToJson(
    MyFieldHistoryMessageRetrieverResources entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['message_id'] = entity.messageId;
  data['position'] = entity.position;
  data['dataset_id'] = entity.datasetId;
  data['dataset_name'] = entity.datasetName;
  data['document_id'] = entity.documentId;
  data['document_name'] = entity.documentName;
  data['data_source_type'] = entity.dataSourceType;
  data['segment_id'] = entity.segmentId;
  data['score'] = entity.score;
  data['hit_count'] = entity.hitCount;
  data['word_count'] = entity.wordCount;
  data['segment_position'] = entity.segmentPosition;
  data['index_node_hash'] = entity.indexNodeHash;
  data['content'] = entity.content;
  data['created_at'] = entity.createdAt;
  return data;
}

extension MyFieldHistoryMessageRetrieverResourcesExtension
    on MyFieldHistoryMessageRetrieverResources {
  MyFieldHistoryMessageRetrieverResources copyWith({
    String? id,
    String? messageId,
    int? position,
    String? datasetId,
    String? datasetName,
    String? documentId,
    String? documentName,
    String? dataSourceType,
    String? segmentId,
    double? score,
    int? hitCount,
    int? wordCount,
    int? segmentPosition,
    dynamic indexNodeHash,
    String? content,
    int? createdAt,
  }) {
    return MyFieldHistoryMessageRetrieverResources()
      ..id = id ?? this.id
      ..messageId = messageId ?? this.messageId
      ..position = position ?? this.position
      ..datasetId = datasetId ?? this.datasetId
      ..datasetName = datasetName ?? this.datasetName
      ..documentId = documentId ?? this.documentId
      ..documentName = documentName ?? this.documentName
      ..dataSourceType = dataSourceType ?? this.dataSourceType
      ..segmentId = segmentId ?? this.segmentId
      ..score = score ?? this.score
      ..hitCount = hitCount ?? this.hitCount
      ..wordCount = wordCount ?? this.wordCount
      ..segmentPosition = segmentPosition ?? this.segmentPosition
      ..indexNodeHash = indexNodeHash ?? this.indexNodeHash
      ..content = content ?? this.content
      ..createdAt = createdAt ?? this.createdAt;
  }
}
