import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/subsidy/entity/subsidy_of_mine_entity.dart';

SubsidyOfMineEntity $SubsidyOfMineEntityFromJson(Map<String, dynamic> json) {
  final SubsidyOfMineEntity subsidyOfMineEntity = SubsidyOfMineEntity();
  final double? total = jsonConvert.convert<double>(json['total']);
  if (total != null) {
    subsidyOfMineEntity.total = total;
  }
  final List<SubsidyOfMineDetails>? details = (json['details'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<SubsidyOfMineDetails>(e) as SubsidyOfMineDetails)
      .toList();
  if (details != null) {
    subsidyOfMineEntity.details = details;
  }
  final String? paymentTime = jsonConvert.convert<String>(json['paymentTime']);
  if (paymentTime != null) {
    subsidyOfMineEntity.paymentTime = paymentTime;
  }
  return subsidyOfMineEntity;
}

Map<String, dynamic> $SubsidyOfMineEntityToJson(SubsidyOfMineEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['total'] = entity.total;
  data['details'] = entity.details?.map((v) => v.toJson()).toList();
  data['paymentTime'] = entity.paymentTime;
  return data;
}

extension SubsidyOfMineEntityExtension on SubsidyOfMineEntity {
  SubsidyOfMineEntity copyWith({
    double? total,
    List<SubsidyOfMineDetails>? details,
    String? paymentTime,
  }) {
    return SubsidyOfMineEntity()
      ..total = total ?? this.total
      ..details = details ?? this.details
      ..paymentTime = paymentTime ?? this.paymentTime;
  }
}

SubsidyOfMineDetails $SubsidyOfMineDetailsFromJson(Map<String, dynamic> json) {
  final SubsidyOfMineDetails subsidyOfMineDetails = SubsidyOfMineDetails();
  final int? subsidyConfigId = jsonConvert.convert<int>(
      json['subsidyConfigId']);
  if (subsidyConfigId != null) {
    subsidyOfMineDetails.subsidyConfigId = subsidyConfigId;
  }
  final String? subsidyItemName = jsonConvert.convert<String>(
      json['subsidyItemName']);
  if (subsidyItemName != null) {
    subsidyOfMineDetails.subsidyItemName = subsidyItemName;
  }
  final String? organizationName = jsonConvert.convert<String>(
      json['organizationName']);
  if (organizationName != null) {
    subsidyOfMineDetails.organizationName = organizationName;
  }
  final String? organizationNo = jsonConvert.convert<String>(
      json['organizationNo']);
  if (organizationNo != null) {
    subsidyOfMineDetails.organizationNo = organizationNo;
  }
  final String? paymentTime = jsonConvert.convert<String>(json['paymentTime']);
  if (paymentTime != null) {
    subsidyOfMineDetails.paymentTime = paymentTime;
  }
  final double? actualPaymentFee = jsonConvert.convert<double>(
      json['actualPaymentFee']);
  if (actualPaymentFee != null) {
    subsidyOfMineDetails.actualPaymentFee = actualPaymentFee;
  }
  return subsidyOfMineDetails;
}

Map<String, dynamic> $SubsidyOfMineDetailsToJson(SubsidyOfMineDetails entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['subsidyConfigId'] = entity.subsidyConfigId;
  data['subsidyItemName'] = entity.subsidyItemName;
  data['organizationName'] = entity.organizationName;
  data['organizationNo'] = entity.organizationNo;
  data['paymentTime'] = entity.paymentTime;
  data['actualPaymentFee'] = entity.actualPaymentFee;
  return data;
}

extension SubsidyOfMineDetailsExtension on SubsidyOfMineDetails {
  SubsidyOfMineDetails copyWith({
    int? subsidyConfigId,
    String? subsidyItemName,
    String? organizationName,
    String? organizationNo,
    String? paymentTime,
    double? actualPaymentFee,
  }) {
    return SubsidyOfMineDetails()
      ..subsidyConfigId = subsidyConfigId ?? this.subsidyConfigId
      ..subsidyItemName = subsidyItemName ?? this.subsidyItemName
      ..organizationName = organizationName ?? this.organizationName
      ..organizationNo = organizationNo ?? this.organizationNo
      ..paymentTime = paymentTime ?? this.paymentTime
      ..actualPaymentFee = actualPaymentFee ?? this.actualPaymentFee;
  }
}