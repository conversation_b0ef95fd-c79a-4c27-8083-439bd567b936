import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/information_detail_entity.dart';

InformationDetailEntity $InformationDetailEntityFromJson(
    Map<String, dynamic> json) {
  final InformationDetailEntity informationDetailEntity =
      InformationDetailEntity();
  final int? releaseInfoId = jsonConvert.convert<int>(json['releaseInfoId']);
  if (releaseInfoId != null) {
    informationDetailEntity.releaseInfoId = releaseInfoId;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    informationDetailEntity.title = title;
  }
  final String? orgCode = jsonConvert.convert<String>(json['orgCode']);
  if (orgCode != null) {
    informationDetailEntity.orgCode = orgCode;
  }
  final String? orgName = jsonConvert.convert<String>(json['orgName']);
  if (orgName != null) {
    informationDetailEntity.orgName = orgName;
  }
  final String? dtlType = jsonConvert.convert<String>(json['dtlType']);
  if (dtlType != null) {
    informationDetailEntity.dtlType = dtlType;
  }
  final String? thumbUrl = jsonConvert.convert<String>(json['thumbUrl']);
  if (thumbUrl != null) {
    informationDetailEntity.thumbUrl = thumbUrl;
  }
  final String? releaseContent =
      jsonConvert.convert<String>(json['releaseContent']);
  if (releaseContent != null) {
    informationDetailEntity.releaseContent = releaseContent;
  }
  final int? releaseTime = jsonConvert.convert<int>(json['releaseTime']);
  if (releaseTime != null) {
    informationDetailEntity.releaseTime = releaseTime;
  }
  final List<dynamic>? annexList =
      (json['annexList'] as List<dynamic>?)?.map((e) => e).toList();
  if (annexList != null) {
    informationDetailEntity.annexList = annexList;
  }
  return informationDetailEntity;
}

Map<String, dynamic> $InformationDetailEntityToJson(
    InformationDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['releaseInfoId'] = entity.releaseInfoId;
  data['title'] = entity.title;
  data['orgCode'] = entity.orgCode;
  data['orgName'] = entity.orgName;
  data['dtlType'] = entity.dtlType;
  data['thumbUrl'] = entity.thumbUrl;
  data['releaseContent'] = entity.releaseContent;
  data['releaseTime'] = entity.releaseTime;
  data['annexList'] = entity.annexList;
  return data;
}

extension InformationDetailEntityExtension on InformationDetailEntity {
  InformationDetailEntity copyWith({
    int? releaseInfoId,
    String? title,
    String? orgCode,
    String? orgName,
    String? dtlType,
    String? thumbUrl,
    String? releaseContent,
    int? releaseTime,
    List<dynamic>? annexList,
  }) {
    return InformationDetailEntity()
      ..releaseInfoId = releaseInfoId ?? this.releaseInfoId
      ..title = title ?? this.title
      ..orgCode = orgCode ?? this.orgCode
      ..orgName = orgName ?? this.orgName
      ..dtlType = dtlType ?? this.dtlType
      ..thumbUrl = thumbUrl ?? this.thumbUrl
      ..releaseContent = releaseContent ?? this.releaseContent
      ..releaseTime = releaseTime ?? this.releaseTime
      ..annexList = annexList ?? this.annexList;
  }
}
