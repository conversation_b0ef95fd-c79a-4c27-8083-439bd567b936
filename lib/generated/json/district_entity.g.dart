import 'package:bdh_smart_agric_app/generated/json/base/json_convert_content.dart';
import 'package:bdh_smart_agric_app/pages/product/agricultural_manager/api_server/entity/district_entity.dart';

DistrictEntity $DistrictEntityFromJson(Map<String, dynamic> json) {
  final DistrictEntity districtEntity = DistrictEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    districtEntity.id = id;
  }
  final int? parentId = jsonConvert.convert<int>(json['parentId']);
  if (parentId != null) {
    districtEntity.parentId = parentId;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    districtEntity.name = name;
  }
  final int? weight = jsonConvert.convert<int>(json['weight']);
  if (weight != null) {
    districtEntity.weight = weight;
  }
  final List<DistrictEntity>? children = (json['children'] as List<dynamic>?)
      ?.map((e) => jsonConvert.convert<DistrictEntity>(e) as DistrictEntity)
      .toList();
  if (children != null) {
    districtEntity.children = children;
  }
  return districtEntity;
}

Map<String, dynamic> $DistrictEntityToJson(DistrictEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['parentId'] = entity.parentId;
  data['name'] = entity.name;
  data['weight'] = entity.weight;
  data['children'] = entity.children.map((v) => v.toJson()).toList();
  return data;
}

extension DistrictEntityExtension on DistrictEntity {
  DistrictEntity copyWith({
    int? id,
    int? parentId,
    String? name,
    int? weight,
    List<DistrictEntity>? children,
  }) {
    return DistrictEntity()
      ..id = id ?? this.id
      ..parentId = parentId ?? this.parentId
      ..name = name ?? this.name
      ..weight = weight ?? this.weight
      ..children = children ?? this.children;
  }
}
