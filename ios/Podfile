# Uncomment this line to define a global platform for your project
platform :ios, '12.0'
#platform :ios, '15.5'


use_modular_headers!
use_frameworks!

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
#  pod 'AMapLocation'
  pod 'Bugly'
#  pod 'UMCommon'
#  pod 'UMPush'
#  pod 'UMDevice'
#  pod 'UYuMao'
  pod 'UMCCommonLog'
# pod 'TCICSDK_Pro', '*******'
# pod 'TXLiteAVSDK_Professional', '12.1.16597'
  pod 'unimp', :subspecs => [
          'Core',               ##核心库(必需)
  #        'Accelerometer',      ##加速度传感器
  #        'Contacts',           ##通讯录
          'Audio',              ##音频
          'Camera&Gallery',     ##相机&相册
          'File',               ##文件
          'Video',              ##视频播放
  #        'LivePusher',         ##直播推流
          'NativeJS',           ##JS Reflection call Native
  #        'Orientation',        ##设备方向
          'Zip',                ##压缩
  #        'Proximity',          ##距离传感器
  #        'Sqlite',             ##数据库
          'Barcode',            ##扫码
          'XMLHttpRequest',     ##网络请求
  #        'Fingerprint',        ##指纹识别
  #        'FaceId',             ##人脸识别
  #        'Log',                ##打印Console.log日志，发布时可移除
  #        'IBeacon',            ##低功耗蓝牙
  #        'BlueTooth',          ##蓝
  #        'Speech-Baidu',       ##语音识别-百度
  #        'Statistic-Umeng',    ##友盟统计
  #        ##定位模块(百度高德不能同时引用)
        'Geolocation',        ##系统定位
          'Geolocation-Gaode',  ##高德定位
  #        'Geolocation-Baidu',  ##百度定位
  #        ##地图(二选一)
          'Map-Gaode',          ##高德地图
  #        'Map-Baidu',          ##百度地图
  #        ##分享
  #        'Share-Wechat',       ##微信分享-包含支付
  #        'Share-Wechat-Nopay', ##微信分享-不包含支付
          'Share-QQ',           ##QQ分享
  #        'Share-Sina',         ##新浪微博分享
    ]
  target 'RunnerTests' do
    inherit! :search_paths
  end
end



post_install do |installer|
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
  end
  
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      #... # Here are some configurations automatically generated by flutter
   
      # You can remove unused permissions here
      # for more infomation: https://github.com/BaseflowIT/flutter-permission-handler/blob/develop/permission_handler/ios/Classes/PermissionHandlerEnums.h
      # e.g. when you don't need camera permission, just add 'PERMISSION_CAMERA=0'
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
  
        ## dart: PermissionGroup.calendar
        # 'PERMISSION_EVENTS=0',
  
        ## dart: PermissionGroup.reminders
        # 'PERMISSION_REMINDERS=0',
  
        ## dart: PermissionGroup.contacts
        # 'PERMISSION_CONTACTS=0',
  
        ## dart: PermissionGroup.camera
         'PERMISSION_CAMERA=1',
  
        ## dart: PermissionGroup.microphone
         'PERMISSION_MICROPHONE=1',
  
        ## dart: PermissionGroup.speech
        # 'PERMISSION_SPEECH_RECOGNIZER=0',
  
        ## dart: PermissionGroup.photos
         'PERMISSION_PHOTOS=1',
  
        ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
         'PERMISSION_LOCATION=1',
          
        ## dart: PermissionGroup.notification
        # 'PERMISSION_NOTIFICATIONS=0',
  
        ## dart: PermissionGroup.mediaLibrary
        # 'PERMISSION_MEDIA_LIBRARY=0',
  
        ## dart: PermissionGroup.sensors
        # 'PERMISSION_SENSORS=0',
           
        ## dart: PermissionGroup.bluetooth
        # 'PERMISSION_BLUETOOTH=0'
      ]
  
    end
  end
  
end


