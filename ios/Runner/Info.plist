<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>数字北大荒</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleGetInfoString</key>
	<string></string>
	<key>CFBundleIconFile</key>
	<string></string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh_CN</string>
	</array>
	<key>CFBundleName</key>
	<string>bdh_smart_agric_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx189b180bf286207a</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>dev.com.icbc.eMallMobile</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>dev.com.icbc.eMallMobile</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>app.bdhic.com</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>bdh</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>baidumap</string>
		<string>iosamap</string>
		<string>qqmap</string>
		<string>http://maps.apple.com/</string>
		<string>mbspay</string>
		<string>loongpayapp</string>
		<string>bankabc</string>
		<string>com.icbc.elife </string>
		<string>cn.com.icbc.eMallMobileClient</string>
		<string>com.icbc.iphone.emall</string>
		<string>com.icbc.iphoneEChannel</string>
		<string>com.icbc.iphoneclient</string>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>weixinURLParamsAPI</string>
		<string>dd.work.exclusive4chinabdh</string>
		<string>dingtalk</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>请求访问媒体库</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>请求在后台访问蓝牙</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>请求使用蓝牙</string>
	<key>NSCalendarsUsageDescription</key>
	<string>请求使用日历</string>
	<key>NSCameraUsageDescription</key>
	<string>需要您的同意, 才能访问您的相册进行照片上传,  如果不允许将无法上传头像或使用相册中的照片</string>
	<key>NSContactsUsageDescription</key>
	<string>请求使用通讯录</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>若不允许, 您将无法获取所处位置的天气信息</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>若不允许, 您将无法获取所处位置的天气信息</string>
	<key>NSLocationUsageDescription</key>
	<string>若不允许, 您将无法获取所处位置的天气信息</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>若不允许, 您将无法获取所处位置的天气信息</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>请求使用麦克风</string>
	<key>NSMotionUsageDescription</key>
	<string>请求使用传感器</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>需要你的同意, 才能访问你的相册进行照片上传,  如果不允许将无法上传头像或使用相册中的照片</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>请求使用语音识别</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>kTCCServiceMediaLibrary</key>
	<string>请求使用音乐</string>
</dict>
</plist>
