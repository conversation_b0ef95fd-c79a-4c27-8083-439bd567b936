//
//  FlutterNativePlugin.m
//  Runner
//
//  Created by 马永欣 on 2024/8/28.
//

#import "FlutterNativePlugin.h"
#import "DCUniMP.h"
#import "CCBNetPaySDK/CCBNetPaySDK.h"
#import "ABCAppCaller.h"
#import "FddWebViewController.h"
#import "AmapLocationManager.h"

//#import "TCICSDK/TCICClassConfig.h"


@interface FlutterNativePlugin()
@property UINavigationController *navController;
@end


@interface FlutterNativePlugin()<WXApiDelegate>
@end
static FlutterNativePlugin *instance = nil;
@implementation FlutterNativePlugin
+ (instancetype)shareInstance{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[self alloc]init];
    });
    return instance;
}


+ (void)registerWithRegistrar:(nonnull NSObject<FlutterPluginRegistrar> *)registrar {
    FlutterMethodChannel *channel = [FlutterMethodChannel methodChannelWithName:@"com.bdh.smart" binaryMessenger:[registrar messenger]];
    [FlutterNativePlugin shareInstance].channel = channel;
    //   FlutterNativePlugin *instance = [[FlutterNativePlugin alloc]init];
    FlutterNativePlugin *instance = [FlutterNativePlugin shareInstance];
        [registrar addMethodCallDelegate:instance channel:channel];
}

- (void)onResp:(BaseResp *)resp{
    SendAuthResp *ress = (SendAuthResp *)resp;
    NSString *code = ress.code;
    self.result(code);
    NSLog(@"FlutterNativePlugin onResp");
}

- (void)handleMethodCall:(FlutterMethodCall *)call result:(FlutterResult)result{
    self.result = result;
    
    // TODO 所有Flutter请求都需要调用 result 不然有内存溢出风险
    if([call.method isEqualToString:@"virtualGPSLocaionJudgement"]) {//是否虚拟定位判断
        NSDictionary *dic = call.arguments;
        NSString *apiKey = [dic objectForKey:@"apiKeyIOS"];
        [AmapLocationManager shareInstance].apiKey = apiKey;
        [[AmapLocationManager shareInstance] startLocate];
        result(@"success");
    }
    else if([call.method isEqualToString:@"openMenu"]){
        DCUniMPConfiguration *configuration = [[DCUniMPConfiguration alloc] init];
        configuration.extraData = call.arguments;
        [DCUniMPSDKEngine openUniMP:@"__UNI__D302F11" configuration:configuration completed:^(DCUniMPInstance * _Nullable uniMPInstance, NSError * _Nullable error) {
            NSLog(@"报错");
        }];
        result(@"success");
        
    }
    else if([call.method isEqualToString:@"quit"]){
        result(@"success");
        exit(0);
    }
    
    else if([call.method isEqualToString:@"openWechatLoginForiOS"]){
          NSLog(@"openWechatLoginForiOS");
          SendAuthReq* req =[[SendAuthReq alloc]init];
              req.scope = @"snsapi_userinfo"; // 只能填 snsapi_userinfo
              req.state = @"123";
              //第三方向微信终端发送一个SendAuthReq消息结构
          [WXApi sendReq:req completion:^(BOOL success) {
              NSLog(@"sendReq");
          }];
        result(@"success");
      }
    
    //农行支付
    else if([call.method isEqualToString:@"openABCToPay"]){
        NSDictionary *dic = call.arguments;
        NSLog(@"跳转农行---%@",  dic);
        if(dic != NULL){
            bool isNotEmptyKey = [dic objectForKey:@"tokenId"];
            if(isNotEmptyKey){
                NSDictionary *paramsDic = dic[@"tokenId"];
                NSLog(@"paramsDic=%@",paramsDic);
                bool isNotEmptyKeyParamsDic = [paramsDic objectForKey:@"tokenId"];
                if(isNotEmptyKeyParamsDic){
                    NSString *tokenId = paramsDic[@"tokenId"];
                    if ([[ABCAppCaller sharedAppCaller] isABCePayAvailable:@"bankabc://"]) {
                        NSString *parameterString = [NSString stringWithFormat:@"CallbackID=railwaypay&TokenID=%@&Method=pay",tokenId];
                        [[ABCAppCaller sharedAppCaller] callBankABC:@"bankabc" param:parameterString];//如果已安装掌银则调起掌银，两个参数分别为掌银的URL标示以及送的参数，参数格式参考标准url传参格式
                    } else{
                        [self abnormalTipInfo:@"没安装农行掌银，或已安装农行掌银版本不支持"];
                    }
                }else{
                    [self abnormalTipInfo:@"农行支付参数异常"];
                }
            }else{
                [self abnormalTipInfo:@"农行支付参数异常"];
            }
        }else{
            [self abnormalTipInfo:@"农行支付参数异常"];
        }
        result(@"success");
    }
    
    //建行支付
    else if([call.method isEqualToString:@"openCCBToPay"]){
        NSDictionary *dic = call.arguments;
        NSLog(@"跳转建行---%@",  dic);
        if(dic != NULL){
            bool isNotEmptyKey = [dic objectForKey:@"tokenId"];
            if(isNotEmptyKey){
                NSString *tokenId = dic[@"tokenId"];
                [[CCBNetPay defaultService]payOrder:tokenId callback:^(NSDictionary *dic) {// support app and h5 pay
                    NSLog(@"跳转建行 支付---%@",  dic);
                }];
            }else{
                [self abnormalTipInfo:@"建行支付参数异常"];
            }
        }else{
            [self abnormalTipInfo:@"建行支付参数异常"];
        }
        result(@"success");
    }
    
    //工行支付
    else if([call.method isEqualToString:@"openICBCToPay"]){
        NSDictionary *dic = call.arguments;
        NSLog(@"跳转工行---%@",  dic);
        ICBCJFTManage *mag= [ICBCJFTManage shareManager];
        mag.ICBCJFTManageDelegate = self;
        UIWindow *keyWindow = [[UIApplication sharedApplication] keyWindow];
        UIViewController *rootViewController = keyWindow.rootViewController;
        NSDictionary *dic1 = [dic valueForKey:@"params"];
        [mag initPayManageWithTpye:1 withDictionary:dic1 withPayMethod:@"01" withPayChannel:@"01" platformType:0 withController:rootViewController miniProDict:nil];
        result(@"success");
    }

    //腾讯人脸
    else if([call.method isEqualToString:@"openTxFace"]){
        NSDictionary *options = call.arguments;
        [WBFaceVerifyCustomerService sharedInstance].delegate = self;
        WBFaceVerifySDKConfig *config = [self getSDKSettings];
        [[WBFaceVerifyCustomerService sharedInstance] initSDKWithUserId:[options objectForKey:@"userId"] 
                                                                  nonce:[options objectForKey:@"nonce"]
                                                                   sign:[options objectForKey:@"sign"]
                                                                  appid:[options objectForKey:@"webankAppId"]
                                                                orderNo:[options objectForKey:@"orderNo"]
                                                             apiVersion:[options objectForKey:@"version"] licence:@"Cl39Upw4m2dBMR963VxggPM/o+hBYPwcR7tG1Ifl/f/qGGY0DuTE/1cDhFOUpaeRdabglUfMbaLs67K2GIk4ERX1nqwHbTviTcNT9wC1OgRsohEcpj0hTf7GXALlI4b0aXAF1CcQvHl1p6s579A9imx4NxOdq0aSzmEnaZiqCJoVq9F7qU1pB6MDKtdcbb3Gpl+xI858cgYjEQxV7DhMdnk6ItLvroEbBiHLy4nvRK98fIN7TJp2sSFrC7p9AacjQYydHGrFWnEUTGvr4k05EeBPeMmDP0HZs2iykFLa6Y3rt7rYKkD/CKQPQK+o/Iyxkcx6g94fv/K6tp8EBQtA7w==" 
                                                                 faceId:[options objectForKey:@"faceId"] sdkConfig:config
                                                                success:^{
            NSLog(@"腾讯人脸 initSDK 成功 ");
            [[WBFaceVerifyCustomerService sharedInstance] startWbFaceVeirifySdk];
//            result(@"0");
        } failure:^(WBFaceError *_Nonnull error) {
            NSLog(@"腾讯人脸 initSDK 失败=%@",error);
//            result(@"1");
            NSString *message = [NSString stringWithFormat:@"desc:%@, code:%@, reason:%@", error.desc, @(error.code), error.reason];
            NSMutableDictionary *resDic = [NSMutableDictionary dictionary];
            [resDic setValue:@"1" forKey:@"code"];
            [resDic setValue:message forKey:@"msg"];
            result(resDic);
        }];
    }
    else if([call.method isEqualToString:@"openFdd"]){
        NSDictionary *options = call.arguments;
        UIViewController *rootViewController = [UIApplication sharedApplication].delegate.window.rootViewController;
        NSString *url = [options objectForKey:@"url"];
        FddWebViewController *browserVC = [[FddWebViewController alloc] init];
        browserVC.urlString = url;
        _navController = [[UINavigationController alloc] initWithRootViewController:browserVC];
        _navController.modalPresentationStyle = UIModalPresentationFullScreen;
            
            // 显示浏览器界面
        [rootViewController presentViewController:_navController animated:YES completion:nil];
        result(@"success");
        
        
    }
    else if([call.method isEqualToString:@"closeFdd"]){
        
        
        [_navController dismissViewControllerAnimated:false completion:^{
            
        }];
        result(@"success");
        
    }
    //腾讯课堂 - 加入教室
    else if([call.method isEqualToString:@"tcicJoinClass"]){
        NSDictionary *args = call.arguments;
        [self tcicJoinClassWithArgs:args result:result];
    }
    
    else{
        result(FlutterMethodNotImplemented);
    }
}

//腾讯人脸 sdkSettings
-(WBFaceVerifySDKConfig *)getSDKSettings{
    WBFaceVerifySDKConfig *config = [WBFaceVerifySDKConfig sdkConfig];
    NSUserDefaults *defaults = [NSUserDefaults standardUserDefaults];
    config.recordVideo = NO;
    config.theme = WBFaceVerifyThemeLightness;
    config.useSimpleMode = [defaults boolForKey:@"settingHome_use_simplemode"];
    config.language = (WBFaceVerifyLanguage)[defaults integerForKey:@"settingHome_language"];
    config.isIpv6 = NO;
    config.useAdvanceCompare = NO;
    return  config;
}

//腾讯人脸 delegate methord
-(void)wbfaceVerifyCustomerServiceDidFinishedWithFaceVerifyResult:(WBFaceVerifyResult *)faceVerifyResult{
//    if (faceVerifyResult.isSuccess) {
//        self.result(@"0");
//    }else {
//        self.result(@"1");
//    }
    if (faceVerifyResult.isSuccess) {
        NSString *message = [NSString stringWithFormat:@"liveRate: %@, similarity: %@", faceVerifyResult.liveRate, faceVerifyResult.similarity];
        NSMutableDictionary *resultDic = [NSMutableDictionary dictionary];
        [resultDic setValue:@"0" forKey:@"code"];
        [resultDic setValue:@"刷脸成功" forKey:@"msg"];
        NSString *resString = [self coverToJsonStringFromMap:resultDic];
        self.result(resString);
    }else {
        NSString *message = @"";
        if ([faceVerifyResult.error.domain isEqualToString:WBFaceErrorDomainCompareServer]) {
//            NSString *message = [NSString stringWithFormat:@"%@, liveRate:%@, similarity:%@, sign: %@", faceVerifyResult.error.desc, faceVerifyResult.liveRate, faceVerifyResult.similarity, faceVerifyResult.sign];
            NSLog(@"error: %@", faceVerifyResult.error);
            message = @"对比失败!面部特征变化较大!";
            NSMutableDictionary *resultDic = [NSMutableDictionary dictionary];
            [resultDic setValue:@"1" forKey:@"code"];
            [resultDic setValue:@"对比失败!面部特征变化较大!" forKey:@"msg"];
            NSString *resString = [self coverToJsonStringFromMap:resultDic];
            self.result(resString);
        } else{
//            NSString *message = [NSString stringWithFormat:@"%@, liveRate:%@, similarity:%@, sign: %@", faceVerifyResult.error.desc, faceVerifyResult.liveRate, faceVerifyResult.similarity, faceVerifyResult.sign];
            message = [NSString stringWithFormat:@"刷脸失败! desc=%@", faceVerifyResult.error.desc];
            NSLog(@"error: %@", faceVerifyResult.error);
            NSMutableDictionary *resultDic = [NSMutableDictionary dictionary];
            [resultDic setValue:@"1" forKey:@"code"];
            [resultDic setValue:@"对比失败!面部特征变化较大!" forKey:@"msg"];
            NSString *resString = [self coverToJsonStringFromMap:resultDic];
            self.result(resString);
        }
    }
}
-(NSString *)coverToJsonStringFromMap:(NSMutableDictionary *)mutableDictionary{
    // 尝试将字典转换为数据
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:mutableDictionary
                                                       options:NSJSONWritingPrettyPrinted
                                                         error:nil];
    // 然后将数据转换为字符串
    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    NSLog(@"JSON string: %@", jsonString);
    return jsonString;
}



//show tip info AlertView
-(void)abnormalTipInfo:(NSString *)tipInfo{
    UIAlertController *alertDialog = [UIAlertController alertControllerWithTitle:@"提示" message:tipInfo preferredStyle:UIAlertControllerStyleAlert];
    UIAlertAction *okAction = [UIAlertAction actionWithTitle:@"确定" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {}];
    [okAction setValue:[UIColor blackColor] forKey:@"titleTextColor"];
    [alertDialog addAction:okAction];
    UIWindow *keyWindow = [[UIApplication sharedApplication] keyWindow];
    UIViewController *rootViewController = keyWindow.rootViewController;
    [rootViewController presentViewController:alertDialog animated:YES completion:nil];
}

#pragma mark - =======================结果回调(ICBC)===========================
-(void)ICBCJFTManageGetPayResultWith:(NSDictionary *)responseDic andError:(NSError *)error{
    NSLog(@"%@====商户自行处理",responseDic);
    
    //tranCode为6666时为支付取消，如果使用sdk有界面模式，支付取消时想关闭收银台界面请使用下面代码，如果不需要则不用。
//    NSString *returnCode = responseDic[@"tranCode"];
//    if ([returnCode isEqualToString:@"6666"]) {
//        [self dismissViewControllerAnimated:YES completion:nil];
//    }
    
    //返回结果处理
//    NSString *isMiniDict = responseDic[@"isMiniDict"];
//    if (isMiniDict.length>0&&[isMiniDict isEqualToString:@"1"])
//    {
//        //小程序返回结果
//        //miniExtMsg对应JsApi navigateBackApplication中的extraData字段数据
//        NSString *miniExtMsg = responseDic[@"miniExtMsg"];
//    }else{
//        //其它支付返回结果，非小程序
//    }
}

- (void)ICBCJFTManageAskResultWith:(NSDictionary *)responseDic andError:(NSError *)error {
    NSLog(@"%@==请求结果==商户自行处理",responseDic);
}

//腾讯课堂 - 加入教室
- (void) tcicJoinClassWithArgs:(NSDictionary *)args result:(FlutterResult)result {
    NSNumber *schoolId = args[@"schoolId"];
    NSString *userId = args[@"userId"];
    NSString *token = args[@"token"];
    NSNumber *classId = args[@"classId"];
    
    if (!schoolId || !userId || !token || !classId) {
        result([FlutterError errorWithCode:@"INVALID_ARGUMENT"
                                   message:@"缺少必要参数"
                                   details:nil]);
        return;
    }
    
    // 配置课堂参数
//    TCICClassConfig *roomConfig = [[TCICClassConfig alloc] init];
//    roomConfig.schoolId = [schoolId intValue];
//    roomConfig.userId = userId;
//    roomConfig.token = token;
//    roomConfig.classId = [classId intValue];
//
//    [roomConfig setValue:@(1) forKey:@"preferPortrait"];
//      
//    // 调起课堂主页面
//    TCICClassController *vc = [TCICClassController classRoomWithConfig:roomConfig];
//    if (vc) {
//        UINavigationController *rootViewController = [UIApplication sharedApplication].delegate.window.rootViewController;
//        _navController = [[UINavigationController alloc] initWithRootViewController:vc];
//        _navController.modalPresentationStyle = UIModalPresentationFullScreen;
//        [rootViewController presentViewController:_navController animated:YES completion:nil];
//        result(@"success");
//    } else {
//        result([FlutterError errorWithCode:@"INVALID_PARAMETERS"
//                                   message:@"参数错误，无法进入课堂"
//                                   details:nil]);
//    }
}

@end
