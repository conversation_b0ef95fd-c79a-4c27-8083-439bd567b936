#import "AppDelegate.h"
#import "GeneratedPluginRegistrant.h"
#import "DCUniMP.h"
#import "FlutterNativePlugin.h"
#import "WeexSDK.h"
#import <Bugly/Bugly.h>
#import <UMCommon/UMConfigure.h>
#import <UserNotifications/UserNotifications.h>
#import <UMPush/UMessage.h>
#include <arpa/inet.h>
#if __has_include(<umeng_push_sdk/UmengPushSdkPlugin.h>)
#import <umeng_push_sdk/UmengPushSdkPlugin.h>
#else
@import umeng_push_sdk;
#endif
//#import <UMCommonLog/UMCommonLogHeaders.h>
//#import <UMCommon/UMConfigure.h>
//#import <UMCommon/UMCommon.h>
//#import <UMPush/UMessage.h>

#import "Runner-Swift.h"

@interface AppDelegate()<DCUniMPSDKEngineDelegate>

@end
@implementation AppDelegate

- (BOOL)application:(UIApplication *)application
didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    [Bugly startWithAppId:@"109157be9b"];
    [GeneratedPluginRegistrant registerWithRegistry:self];
    
    [AppLinksIosPlugin registerWithRegistrar:[self registrarForPlugin:@"AppLinksIosPlugin"]];
    [FlutterNativePlugin registerWithRegistrar:[self registrarForPlugin:@"FlutterNativePlugin"]];
    [[AppLinks shared] handleLinkWithLaunchOptions:launchOptions];

    [UNUserNotificationCenter currentNotificationCenter].delegate=self;
    [DCUniMPSDKEngine initSDKEnvironmentWithLaunchOptions:launchOptions];
    [self checkUniMPResource:@"__UNI__D302F11"];
    //注册离线插件
    [WXSDKEngine registerModule:@"uniplugin_amapSdk" withClass:NSClassFromString(@"AmapLocationSdk")];
    [WXSDKEngine registerModule:@"AThree-ccbPay" withClass:NSClassFromString(@"ccbPayModule")];
    [WXSDKEngine registerModule:@"AThree-jftPay" withClass:NSClassFromString(@"jftPayModule")];
    [WXSDKEngine registerModule:@"LibBDHPayPlugin-BDHPayModule" withClass:NSClassFromString(@"BDHPayModule")];
    [WXSDKEngine registerModule:@"LibFaceAuth-FaceAuthModule" withClass:NSClassFromString(@"BDHFaceAuthModule")];

    return [super application:application didFinishLaunchingWithOptions:launchOptions];
}

//友盟推送native
/*
- (void)initUmengPushWithOptions:(NSDictionary *)launchOptions {
    [UMCommonLogManager setUpUMCommonLogManager];
    [UMConfigure setLogEnabled:YES];
    [UMConfigure initWithAppkey:@"6722d46c7e5e6a4eeb897f57" channel:@"BDH_PUSH"];
     // Push组件基本功能配置
     UMessageRegisterEntity* entity =[[UMessageRegisterEntity alloc] init];
     //type是对推送的几个参数的选择，可以选择一个或者多个。默认是三个全部打开，即：声音，弹窗，角标
         entity.types =UMessageAuthorizationOptionBadge|UMessageAuthorizationOptionSound|UMessageAuthorizationOptionAlert;
     [UNUserNotificationCenter currentNotificationCenter].delegate=self;
     [UMessage registerForRemoteNotificationsWithLaunchOptions:launchOptions Entity:entity  completionHandler:^(BOOL granted,NSError*_Nullable error){
         if(granted){
         }else{
         }
     }];
     
}
*/
 
 
#pragma mark - UNUserNotificationCenterDelegate
//iOS10新增：处理前台<收到通知>代理方法
-(void)userNotificationCenter:(UNUserNotificationCenter*)center willPresentNotification:(UNNotification*)notification withCompletionHandler:(void(^)(UNNotificationPresentationOptions))completionHandler{
    NSDictionary* userInfo = notification.request.content.userInfo;
    if([notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]){
    [UMessage setAutoAlert:NO];
    //必须加这句代码
    [UMessage didReceiveRemoteNotification:userInfo];

    }else{
    //应用处于前台时的本地推送接受
    }
    completionHandler(UNNotificationPresentationOptionSound|UNNotificationPresentationOptionBadge|UNNotificationPresentationOptionAlert);
}

//iOS10新增：处理后台<点击通知>的代理方法
-(void)userNotificationCenter:(UNUserNotificationCenter*)center didReceiveNotificationResponse:(UNNotificationResponse*)response withCompletionHandler:(void(^)())completionHandler {
    NSMutableDictionary *dic = [NSMutableDictionary dictionary];
    NSDictionary *userInfo = response.notification.request.content.userInfo;
    [dic setValue:@"ios" forKey:@"platform"];
    [dic setValue:userInfo forKey:@"msgBody"];
    if([response.notification.request.trigger isKindOfClass:[UNPushNotificationTrigger class]]){
        //必须加这句代码
        [UMessage didReceiveRemoteNotification:userInfo];
        // native push
        if ([userInfo objectForKey:@"extra"] != nil) {
            NSLog(@"字典中存在键 key1");
            FlutterMethodChannel *channel = [FlutterNativePlugin shareInstance].channel;
            if(channel != nil){
                [channel invokeMethod:@"readOffLineNotification" arguments:dic];
            }
        } else {
            NSLog(@"字典中不存在键 key1");
        }
    }else{
        //应用处于后台时的本地推送接受
        NSLog(@"应用处于后台时的本地推送接受");
    }
}

// native push: get token
//- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
//    NSString *currentDeviceToken = [self deviceTokenStringFromData:deviceToken];
//    NSLog(@"currentDeviceToken: %@",  currentDeviceToken);
//    NSMutableDictionary *dic = [[NSMutableDictionary alloc]init];
//    [dic setValue:currentDeviceToken forKey:@"deviceToken"];
//    FlutterMethodChannel *channel = [FlutterNativePlugin shareInstance].channel;
//    if(channel != nil){
//        [channel invokeMethod:@"sendDeviceToken" arguments:dic];
//    }
//}

- (NSString *)deviceTokenStringFromData:(NSData *)deviceToken {
    if (!deviceToken || deviceToken.length == 0) {
        return @"";
    }
    const unsigned char *dataBuffer = (const unsigned char *)deviceToken.bytes;
    NSMutableString *hexString = [NSMutableString stringWithCapacity:(deviceToken.length * 2)];
    for (NSUInteger i = 0; i < deviceToken.length; ++i) {
        [hexString appendFormat:@"%02x", dataBuffer[i]]; // 小写字母
        // 若需大写，使用 "%02X"
    }
    return [hexString copy];
}


#pragma mark - App 生命周期方法
- (void)applicationDidBecomeActive:(UIApplication *)application {
    [DCUniMPSDKEngine applicationDidBecomeActive:application];
}

- (void)applicationWillResignActive:(UIApplication *)application {
    [DCUniMPSDKEngine applicationWillResignActive:application];
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
    [DCUniMPSDKEngine applicationDidEnterBackground:application];
}

- (void)applicationWillEnterForeground:(UIApplication *)application {
    [DCUniMPSDKEngine applicationWillEnterForeground:application];
}

- (void)applicationWillTerminate:(UIApplication *)application {
    [DCUniMPSDKEngine destory];
}

/// 检查运行目录是否存在应用资源，不存在将应用资源部署到运行目录
- (void)checkUniMPResource:(NSString *)appid {
    
    if (![DCUniMPSDKEngine isExistsUniMP:appid]) {
        // 读取导入到工程中的wgt应用资源
        NSString *appResourcePath = [[NSBundle mainBundle] pathForResource:appid ofType:@"wgt"];
        if (!appResourcePath) {
            NSLog(@"资源路径不正确，请检查");
            return;
        }
        // 将应用资源部署到运行路径中
        NSError *error;
        if ([DCUniMPSDKEngine installUniMPResourceWithAppid:appid resourceFilePath:appResourcePath password:nil error:&error]) {
            NSLog(@"小程序 %@ 应用资源文件部署成功，版本信息：%@",appid,[DCUniMPSDKEngine getUniMPVersionInfoWithAppid:appid]);
        } else {
            NSLog(@"小程序 %@ 应用资源部署失败： %@",appid,error);
        }
    } else {
        NSLog(@"已存在小程序 %@ 应用资源，版本信息：%@",appid,[DCUniMPSDKEngine getUniMPVersionInfoWithAppid:appid]);
    }
}

///9.0以后使用
- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url options:(NSDictionary<NSString*, id> *)options{
    if ([[url absoluteString] hasSuffix:@"ICBCB2CPAY"]) {
        [[ICBCJFTManage shareManager] ICBCJFTResultBackWithUrl:url];
    }
    
    if([url.scheme containsString:@"wx189b180bf286207a"]){
        if([url.query containsString:@"code"]){// wechat auth
            [WXApi handleOpenURL:url delegate:(id)[FlutterNativePlugin shareInstance]];
        }else {
            [WXApi handleOpenURL:url delegate:self];
        }
    }
    //NSLog(@"openURL：%@",url);
    [(FlutterNativePlugin *)[AppLinks shared] application:application openURL:url options:options];
   
    return YES;
}


- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler{
    if ([userActivity.activityType isEqualToString: NSUserActivityTypeBrowsingWeb]) {
        //        NSURL *url = userActivity.webpageURL;
        return [WXApi handleOpenUniversalLink:userActivity delegate:self];
    }
    return  YES;
}


@end
