// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		040EC82A2D9CD6B0006698EB /* ICBCJFTFrameWork.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 040EC8272D9CD6B0006698EB /* ICBCJFTFrameWork.framework */; };
		040EC82B2D9CD6B0006698EB /* ICBCPaySDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 040EC8292D9CD6B0006698EB /* ICBCPaySDK.framework */; };
		040EC82C2D9CD6B0006698EB /* ICBCJFTBuild.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 040EC8262D9CD6B0006698EB /* ICBCJFTBuild.bundle */; };
		040EC82D2D9CD6B0006698EB /* ICBCPaySDK.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 040EC8282D9CD6B0006698EB /* ICBCPaySDK.bundle */; };
		043AF8EC2D8D54FA00AE3FB0 /* AmapLocationManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 043AF8EB2D8D54FA00AE3FB0 /* AmapLocationManager.m */; };
		1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */ = {isa = PBXBuildFile; fileRef = 1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */; };
		331C80F4294D02FB00263BE5 /* RunnerTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 331C80F3294D02FB00263BE5 /* RunnerTests.m */; };
		3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */ = {isa = PBXBuildFile; fileRef = 3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */; };
		3F1B07AF2C8F14E300284629 /* uniplugin_amapSdk.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3F1B07AE2C8F14E300284629 /* uniplugin_amapSdk.framework */; };
		3F1B07B22C8F14FD00284629 /* liblibWeexDCccbPay.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3F1B07B02C8F14FD00284629 /* liblibWeexDCccbPay.a */; };
		3F1B07B32C8F14FD00284629 /* CCBNetPaySDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3F1B07B12C8F14FD00284629 /* CCBNetPaySDK.framework */; };
		3F1B07BA2C8F151600284629 /* liblibWeexDCjftPay.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3F1B07B52C8F151600284629 /* liblibWeexDCjftPay.a */; };
		3F1B07C02C8F152900284629 /* LibBDHPayPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3F1B07BE2C8F152900284629 /* LibBDHPayPlugin.framework */; };
		3F1B07C12C8F152900284629 /* libABCAppCaller.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 3F1B07BF2C8F152900284629 /* libABCAppCaller.a */; };
		3F57AADB2C7ECDAA00BC4FF4 /* FlutterNativePlugin.m in Sources */ = {isa = PBXBuildFile; fileRef = 3F57AADA2C7ECDAA00BC4FF4 /* FlutterNativePlugin.m */; };
		3FA1E3272C93032300EE232F /* AlipaySDK.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FA1E3262C93032300EE232F /* AlipaySDK.framework */; };
		3FE421D02D2E8F7800110013 /* FddWebViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 3FE421CF2D2E8F7800110013 /* FddWebViewController.m */; };
		3FEC87432D1911B700F87558 /* face-tracker-v003.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 3FEC87322D1911B400F87558 /* face-tracker-v003.bundle */; };
		3FEC87442D1911B700F87558 /* LibFaceAuth.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 3FEC87332D1911B400F87558 /* LibFaceAuth.bundle */; };
		3FEC87452D1911B700F87558 /* TencentCloudHuiyanSDKFace.bundle in Resources */ = {isa = PBXBuildFile; fileRef = 3FEC87342D1911B400F87558 /* TencentCloudHuiyanSDKFace.bundle */; };
		3FEC87462D1911B700F87558 /* tnnliveness.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC87362D1911B400F87558 /* tnnliveness.framework */; };
		3FEC87472D1911B700F87558 /* YTFaceAlignmentTinyLiveness.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC87372D1911B400F87558 /* YTFaceAlignmentTinyLiveness.framework */; };
		3FEC87482D1911B700F87558 /* YTFaceDetectorLiveness.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC87382D1911B500F87558 /* YTFaceDetectorLiveness.framework */; };
		3FEC87492D1911B700F87558 /* YTFaceTrackerLiveness.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC87392D1911B500F87558 /* YTFaceTrackerLiveness.framework */; };
		3FEC874A2D1911B700F87558 /* TencentCloudHuiyanSDKFace.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC873A2D1911B500F87558 /* TencentCloudHuiyanSDKFace.framework */; };
		3FEC874B2D1911B700F87558 /* YTFaceLiveReflect.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC873B2D1911B500F87558 /* YTFaceLiveReflect.framework */; };
		3FEC874C2D1911B700F87558 /* YTPoseDetector.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC873C2D1911B600F87558 /* YTPoseDetector.framework */; };
		3FEC874D2D1911B700F87558 /* YtSDKKitFrameworkTool.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC873D2D1911B600F87558 /* YtSDKKitFrameworkTool.framework */; };
		3FEC874E2D1911B700F87558 /* YTCommonLiveness.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC873E2D1911B600F87558 /* YTCommonLiveness.framework */; };
		3FEC874F2D1911B700F87558 /* YTCv.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC873F2D1911B600F87558 /* YTCv.framework */; };
		3FEC87502D1911B700F87558 /* YTSm.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC87402D1911B600F87558 /* YTSm.framework */; };
		3FEC87512D1911B700F87558 /* TuringShieldCamRisk.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC87412D1911B700F87558 /* TuringShieldCamRisk.framework */; };
		3FEC87522D1911B700F87558 /* LibFaceAuth.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 3FEC87422D1911B700F87558 /* LibFaceAuth.framework */; };
		53265EE32DEEDFF100671E41 /* __UNI__D302F11.wgt in Resources */ = {isa = PBXBuildFile; fileRef = 53265EE22DEEDFF100671E41 /* __UNI__D302F11.wgt */; };
		53265EE42DEEDFF100671E41 /* __UNI__D302F11.wgt in Resources */ = {isa = PBXBuildFile; fileRef = 53265EE22DEEDFF100671E41 /* __UNI__D302F11.wgt */; };
		826CAC272D928E400064F35F /* AppLinksIosPlugin.swift in Sources */ = {isa = PBXBuildFile; fileRef = 826CAC262D928E400064F35F /* AppLinksIosPlugin.swift */; };
		8EDFE5D03B163FCA7B7D8C0D /* Pods_RunnerTests.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 4587D6D65CADA22052B25E36 /* Pods_RunnerTests.framework */; };
		978B8F6F1D3862AE00F588F7 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 7AFFD8EE1D35381100E5BB4D /* AppDelegate.m */; };
		97C146F31CF9000F007C117D /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 97C146F21CF9000F007C117D /* main.m */; };
		97C146FC1CF9000F007C117D /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FA1CF9000F007C117D /* Main.storyboard */; };
		97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FD1CF9000F007C117D /* Assets.xcassets */; };
		97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */; };
		F9D35EA144B070BCEE89E5A6 /* Pods_Runner.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = C6ED372399B7145FA5490CFD /* Pods_Runner.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		331C80F5294D02FB00263BE5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 97C146E61CF9000F007C117D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 97C146ED1CF9000F007C117D;
			remoteInfo = Runner;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		9705A1C41CF9048500538489 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		040EC8262D9CD6B0006698EB /* ICBCJFTBuild.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = ICBCJFTBuild.bundle; sourceTree = "<group>"; };
		040EC8272D9CD6B0006698EB /* ICBCJFTFrameWork.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ICBCJFTFrameWork.framework; sourceTree = "<group>"; };
		040EC8282D9CD6B0006698EB /* ICBCPaySDK.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = ICBCPaySDK.bundle; sourceTree = "<group>"; };
		040EC8292D9CD6B0006698EB /* ICBCPaySDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = ICBCPaySDK.framework; sourceTree = "<group>"; };
		043AF8EA2D8D54FA00AE3FB0 /* AmapLocationManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AmapLocationManager.h; sourceTree = "<group>"; };
		043AF8EB2D8D54FA00AE3FB0 /* AmapLocationManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AmapLocationManager.m; sourceTree = "<group>"; };
		044F0ABC2D111C67002786D9 /* ABCAppCaller.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ABCAppCaller.h; sourceTree = "<group>"; };
		1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = GeneratedPluginRegistrant.h; sourceTree = "<group>"; };
		1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = GeneratedPluginRegistrant.m; sourceTree = "<group>"; };
		190B718B24560B5B839D9833 /* Pods-Runner.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.debug.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.debug.xcconfig"; sourceTree = "<group>"; };
		331C80F1294D02FB00263BE5 /* RunnerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = RunnerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		331C80F3294D02FB00263BE5 /* RunnerTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = RunnerTests.m; sourceTree = "<group>"; };
		3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = AppFrameworkInfo.plist; path = Flutter/AppFrameworkInfo.plist; sourceTree = "<group>"; };
		3F1B07AE2C8F14E300284629 /* uniplugin_amapSdk.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = uniplugin_amapSdk.framework; sourceTree = "<group>"; };
		3F1B07B02C8F14FD00284629 /* liblibWeexDCccbPay.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = liblibWeexDCccbPay.a; sourceTree = "<group>"; };
		3F1B07B12C8F14FD00284629 /* CCBNetPaySDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = CCBNetPaySDK.framework; sourceTree = "<group>"; };
		3F1B07B52C8F151600284629 /* liblibWeexDCjftPay.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = liblibWeexDCjftPay.a; sourceTree = "<group>"; };
		3F1B07BE2C8F152900284629 /* LibBDHPayPlugin.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = LibBDHPayPlugin.framework; sourceTree = "<group>"; };
		3F1B07BF2C8F152900284629 /* libABCAppCaller.a */ = {isa = PBXFileReference; lastKnownFileType = archive.ar; path = libABCAppCaller.a; sourceTree = "<group>"; };
		3F57AAD92C7ECDAA00BC4FF4 /* FlutterNativePlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FlutterNativePlugin.h; sourceTree = "<group>"; };
		3F57AADA2C7ECDAA00BC4FF4 /* FlutterNativePlugin.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FlutterNativePlugin.m; sourceTree = "<group>"; };
		3F67928D2C8C4EDD009FD264 /* Runner.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = Runner.entitlements; sourceTree = "<group>"; };
		3FA1E3262C93032300EE232F /* AlipaySDK.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = AlipaySDK.framework; sourceTree = "<group>"; };
		3FE421CE2D2E8F7800110013 /* FddWebViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = FddWebViewController.h; sourceTree = "<group>"; };
		3FE421CF2D2E8F7800110013 /* FddWebViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = FddWebViewController.m; sourceTree = "<group>"; };
		3FEC87322D1911B400F87558 /* face-tracker-v003.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = "face-tracker-v003.bundle"; sourceTree = "<group>"; };
		3FEC87332D1911B400F87558 /* LibFaceAuth.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = LibFaceAuth.bundle; sourceTree = "<group>"; };
		3FEC87342D1911B400F87558 /* TencentCloudHuiyanSDKFace.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = TencentCloudHuiyanSDKFace.bundle; sourceTree = "<group>"; };
		3FEC87362D1911B400F87558 /* tnnliveness.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = tnnliveness.framework; sourceTree = "<group>"; };
		3FEC87372D1911B400F87558 /* YTFaceAlignmentTinyLiveness.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTFaceAlignmentTinyLiveness.framework; sourceTree = "<group>"; };
		3FEC87382D1911B500F87558 /* YTFaceDetectorLiveness.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTFaceDetectorLiveness.framework; sourceTree = "<group>"; };
		3FEC87392D1911B500F87558 /* YTFaceTrackerLiveness.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTFaceTrackerLiveness.framework; sourceTree = "<group>"; };
		3FEC873A2D1911B500F87558 /* TencentCloudHuiyanSDKFace.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TencentCloudHuiyanSDKFace.framework; sourceTree = "<group>"; };
		3FEC873B2D1911B500F87558 /* YTFaceLiveReflect.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTFaceLiveReflect.framework; sourceTree = "<group>"; };
		3FEC873C2D1911B600F87558 /* YTPoseDetector.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTPoseDetector.framework; sourceTree = "<group>"; };
		3FEC873D2D1911B600F87558 /* YtSDKKitFrameworkTool.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YtSDKKitFrameworkTool.framework; sourceTree = "<group>"; };
		3FEC873E2D1911B600F87558 /* YTCommonLiveness.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTCommonLiveness.framework; sourceTree = "<group>"; };
		3FEC873F2D1911B600F87558 /* YTCv.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTCv.framework; sourceTree = "<group>"; };
		3FEC87402D1911B600F87558 /* YTSm.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = YTSm.framework; sourceTree = "<group>"; };
		3FEC87412D1911B700F87558 /* TuringShieldCamRisk.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = TuringShieldCamRisk.framework; sourceTree = "<group>"; };
		3FEC87422D1911B700F87558 /* LibFaceAuth.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; path = LibFaceAuth.framework; sourceTree = "<group>"; };
		4587D6D65CADA22052B25E36 /* Pods_RunnerTests.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_RunnerTests.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		52A41B5FD68CD05920DC872C /* Pods-RunnerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.debug.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.debug.xcconfig"; sourceTree = "<group>"; };
		53265EE22DEEDFF100671E41 /* __UNI__D302F11.wgt */ = {isa = PBXFileReference; lastKnownFileType = file; path = __UNI__D302F11.wgt; sourceTree = "<group>"; };
		7AFA3C8E1D35360C0083082E /* Release.xcconfig */ = {isa = PBXFileReference; lastKnownFileType = text.xcconfig; name = Release.xcconfig; path = Flutter/Release.xcconfig; sourceTree = "<group>"; };
		7AFFD8ED1D35381100E5BB4D /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		7AFFD8EE1D35381100E5BB4D /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		826CAC262D928E400064F35F /* AppLinksIosPlugin.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppLinksIosPlugin.swift; sourceTree = "<group>"; };
		826CAC282D928E420064F35F /* Runner-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Runner-Bridging-Header.h"; sourceTree = "<group>"; };
		9740EEB21CF90195004384FC /* Debug.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Debug.xcconfig; path = Flutter/Debug.xcconfig; sourceTree = "<group>"; };
		9740EEB31CF90195004384FC /* Generated.xcconfig */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.xcconfig; name = Generated.xcconfig; path = Flutter/Generated.xcconfig; sourceTree = "<group>"; };
		97C146EE1CF9000F007C117D /* Runner.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = Runner.app; sourceTree = BUILT_PRODUCTS_DIR; };
		97C146F21CF9000F007C117D /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		97C146FB1CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		97C146FD1CF9000F007C117D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		97C147001CF9000F007C117D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		97C147021CF9000F007C117D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		9B27C82BA8CBB4D7043158DB /* Pods-Runner.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.profile.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.profile.xcconfig"; sourceTree = "<group>"; };
		A69852F068F10AB55137E013 /* Pods-RunnerTests.profile.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.profile.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.profile.xcconfig"; sourceTree = "<group>"; };
		B70EBE05A89AF5FD1D5B31FD /* Pods-RunnerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-RunnerTests.release.xcconfig"; path = "Target Support Files/Pods-RunnerTests/Pods-RunnerTests.release.xcconfig"; sourceTree = "<group>"; };
		C6ED372399B7145FA5490CFD /* Pods_Runner.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Runner.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		D3AE8A4FC4F3FDD0A7C17231 /* Pods-Runner.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Runner.release.xcconfig"; path = "Target Support Files/Pods-Runner/Pods-Runner.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		331C80EE294D02FB00263BE5 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8EDFE5D03B163FCA7B7D8C0D /* Pods_RunnerTests.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EB1CF9000F007C117D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3FEC87492D1911B700F87558 /* YTFaceTrackerLiveness.framework in Frameworks */,
				3FEC87512D1911B700F87558 /* TuringShieldCamRisk.framework in Frameworks */,
				3FEC87462D1911B700F87558 /* tnnliveness.framework in Frameworks */,
				3FEC874C2D1911B700F87558 /* YTPoseDetector.framework in Frameworks */,
				3FEC87472D1911B700F87558 /* YTFaceAlignmentTinyLiveness.framework in Frameworks */,
				3FEC874E2D1911B700F87558 /* YTCommonLiveness.framework in Frameworks */,
				3F1B07AF2C8F14E300284629 /* uniplugin_amapSdk.framework in Frameworks */,
				3FEC874A2D1911B700F87558 /* TencentCloudHuiyanSDKFace.framework in Frameworks */,
				3FA1E3272C93032300EE232F /* AlipaySDK.framework in Frameworks */,
				3F1B07B32C8F14FD00284629 /* CCBNetPaySDK.framework in Frameworks */,
				3FEC87522D1911B700F87558 /* LibFaceAuth.framework in Frameworks */,
				3FEC874B2D1911B700F87558 /* YTFaceLiveReflect.framework in Frameworks */,
				3F1B07C02C8F152900284629 /* LibBDHPayPlugin.framework in Frameworks */,
				3FEC874F2D1911B700F87558 /* YTCv.framework in Frameworks */,
				3FEC87482D1911B700F87558 /* YTFaceDetectorLiveness.framework in Frameworks */,
				3FEC87502D1911B700F87558 /* YTSm.framework in Frameworks */,
				3F1B07BA2C8F151600284629 /* liblibWeexDCjftPay.a in Frameworks */,
				3FEC874D2D1911B700F87558 /* YtSDKKitFrameworkTool.framework in Frameworks */,
				3F1B07B22C8F14FD00284629 /* liblibWeexDCccbPay.a in Frameworks */,
				3F1B07C12C8F152900284629 /* libABCAppCaller.a in Frameworks */,
				040EC82A2D9CD6B0006698EB /* ICBCJFTFrameWork.framework in Frameworks */,
				040EC82B2D9CD6B0006698EB /* ICBCPaySDK.framework in Frameworks */,
				F9D35EA144B070BCEE89E5A6 /* Pods_Runner.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		331C80F2294D02FB00263BE5 /* RunnerTests */ = {
			isa = PBXGroup;
			children = (
				331C80F3294D02FB00263BE5 /* RunnerTests.m */,
			);
			path = RunnerTests;
			sourceTree = "<group>";
		};
		3F57AAD12C7EBF2500BC4FF4 /* UniMp */ = {
			isa = PBXGroup;
			children = (
				3F57AAD22C7EC05F00BC4FF4 /* apps */,
			);
			path = UniMp;
			sourceTree = "<group>";
		};
		3F57AAD22C7EC05F00BC4FF4 /* apps */ = {
			isa = PBXGroup;
			children = (
				53265EE22DEEDFF100671E41 /* __UNI__D302F11.wgt */,
			);
			path = apps;
			sourceTree = "<group>";
		};
		3FEC87352D1911B400F87558 /* BundleResources */ = {
			isa = PBXGroup;
			children = (
				3FEC87322D1911B400F87558 /* face-tracker-v003.bundle */,
				3FEC87332D1911B400F87558 /* LibFaceAuth.bundle */,
				3FEC87342D1911B400F87558 /* TencentCloudHuiyanSDKFace.bundle */,
			);
			path = BundleResources;
			sourceTree = "<group>";
		};
		9740EEB11CF90186004384FC /* Flutter */ = {
			isa = PBXGroup;
			children = (
				3B3967151E833CAA004F5970 /* AppFrameworkInfo.plist */,
				9740EEB21CF90195004384FC /* Debug.xcconfig */,
				7AFA3C8E1D35360C0083082E /* Release.xcconfig */,
				9740EEB31CF90195004384FC /* Generated.xcconfig */,
			);
			name = Flutter;
			sourceTree = "<group>";
		};
		97C146E51CF9000F007C117D = {
			isa = PBXGroup;
			children = (
				3FEC87352D1911B400F87558 /* BundleResources */,
				3FEC87422D1911B700F87558 /* LibFaceAuth.framework */,
				3FEC873A2D1911B500F87558 /* TencentCloudHuiyanSDKFace.framework */,
				3FEC87362D1911B400F87558 /* tnnliveness.framework */,
				3FEC87412D1911B700F87558 /* TuringShieldCamRisk.framework */,
				3FEC873E2D1911B600F87558 /* YTCommonLiveness.framework */,
				3FEC873F2D1911B600F87558 /* YTCv.framework */,
				3FEC87372D1911B400F87558 /* YTFaceAlignmentTinyLiveness.framework */,
				3FEC87382D1911B500F87558 /* YTFaceDetectorLiveness.framework */,
				3FEC873B2D1911B500F87558 /* YTFaceLiveReflect.framework */,
				3FEC87392D1911B500F87558 /* YTFaceTrackerLiveness.framework */,
				3FEC873C2D1911B600F87558 /* YTPoseDetector.framework */,
				3FEC873D2D1911B600F87558 /* YtSDKKitFrameworkTool.framework */,
				3FEC87402D1911B600F87558 /* YTSm.framework */,
				3FA1E3262C93032300EE232F /* AlipaySDK.framework */,
				3F1B07BF2C8F152900284629 /* libABCAppCaller.a */,
				3F1B07BE2C8F152900284629 /* LibBDHPayPlugin.framework */,
				3F1B07B12C8F14FD00284629 /* CCBNetPaySDK.framework */,
				3F1B07B02C8F14FD00284629 /* liblibWeexDCccbPay.a */,
				3F1B07AE2C8F14E300284629 /* uniplugin_amapSdk.framework */,
				3F1B07B52C8F151600284629 /* liblibWeexDCjftPay.a */,
				040EC8262D9CD6B0006698EB /* ICBCJFTBuild.bundle */,
				040EC8272D9CD6B0006698EB /* ICBCJFTFrameWork.framework */,
				040EC8282D9CD6B0006698EB /* ICBCPaySDK.bundle */,
				040EC8292D9CD6B0006698EB /* ICBCPaySDK.framework */,
				9740EEB11CF90186004384FC /* Flutter */,
				97C146F01CF9000F007C117D /* Runner */,
				331C80F2294D02FB00263BE5 /* RunnerTests */,
				97C146EF1CF9000F007C117D /* Products */,
				B124AB180BE49C6828BC04F5 /* Pods */,
				A76CC3BD71937BC8F5805E33 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		97C146EF1CF9000F007C117D /* Products */ = {
			isa = PBXGroup;
			children = (
				97C146EE1CF9000F007C117D /* Runner.app */,
				331C80F1294D02FB00263BE5 /* RunnerTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		97C146F01CF9000F007C117D /* Runner */ = {
			isa = PBXGroup;
			children = (
				3F67928D2C8C4EDD009FD264 /* Runner.entitlements */,
				3F57AAD12C7EBF2500BC4FF4 /* UniMp */,
				7AFFD8ED1D35381100E5BB4D /* AppDelegate.h */,
				7AFFD8EE1D35381100E5BB4D /* AppDelegate.m */,
				044F0ABC2D111C67002786D9 /* ABCAppCaller.h */,
				826CAC262D928E400064F35F /* AppLinksIosPlugin.swift */,
				97C146FA1CF9000F007C117D /* Main.storyboard */,
				97C146FD1CF9000F007C117D /* Assets.xcassets */,
				97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */,
				97C147021CF9000F007C117D /* Info.plist */,
				97C146F11CF9000F007C117D /* Supporting Files */,
				1498D2321E8E86230040F4C2 /* GeneratedPluginRegistrant.h */,
				1498D2331E8E89220040F4C2 /* GeneratedPluginRegistrant.m */,
				3F57AAD92C7ECDAA00BC4FF4 /* FlutterNativePlugin.h */,
				3F57AADA2C7ECDAA00BC4FF4 /* FlutterNativePlugin.m */,
				3FE421CE2D2E8F7800110013 /* FddWebViewController.h */,
				3FE421CF2D2E8F7800110013 /* FddWebViewController.m */,
				043AF8EA2D8D54FA00AE3FB0 /* AmapLocationManager.h */,
				043AF8EB2D8D54FA00AE3FB0 /* AmapLocationManager.m */,
				826CAC282D928E420064F35F /* Runner-Bridging-Header.h */,
			);
			path = Runner;
			sourceTree = "<group>";
		};
		97C146F11CF9000F007C117D /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				97C146F21CF9000F007C117D /* main.m */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		A76CC3BD71937BC8F5805E33 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C6ED372399B7145FA5490CFD /* Pods_Runner.framework */,
				4587D6D65CADA22052B25E36 /* Pods_RunnerTests.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		B124AB180BE49C6828BC04F5 /* Pods */ = {
			isa = PBXGroup;
			children = (
				190B718B24560B5B839D9833 /* Pods-Runner.debug.xcconfig */,
				D3AE8A4FC4F3FDD0A7C17231 /* Pods-Runner.release.xcconfig */,
				9B27C82BA8CBB4D7043158DB /* Pods-Runner.profile.xcconfig */,
				52A41B5FD68CD05920DC872C /* Pods-RunnerTests.debug.xcconfig */,
				B70EBE05A89AF5FD1D5B31FD /* Pods-RunnerTests.release.xcconfig */,
				A69852F068F10AB55137E013 /* Pods-RunnerTests.profile.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		331C80F0294D02FB00263BE5 /* RunnerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 331C80F7294D02FB00263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */;
			buildPhases = (
				708027D33D578D2EC60FF813 /* [CP] Check Pods Manifest.lock */,
				331C80ED294D02FB00263BE5 /* Sources */,
				331C80EE294D02FB00263BE5 /* Frameworks */,
				331C80EF294D02FB00263BE5 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				331C80F6294D02FB00263BE5 /* PBXTargetDependency */,
			);
			name = RunnerTests;
			productName = RunnerTests;
			productReference = 331C80F1294D02FB00263BE5 /* RunnerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		97C146ED1CF9000F007C117D /* Runner */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */;
			buildPhases = (
				ADFD83E4BE259CE6F4CE8CE1 /* [CP] Check Pods Manifest.lock */,
				9740EEB61CF901F6004384FC /* Run Script */,
				97C146EA1CF9000F007C117D /* Sources */,
				97C146EB1CF9000F007C117D /* Frameworks */,
				97C146EC1CF9000F007C117D /* Resources */,
				9705A1C41CF9048500538489 /* Embed Frameworks */,
				3B06AD1E1E4923F5004D2608 /* Thin Binary */,
				A83CDC9EEC7187C5F2E41EAD /* [CP] Copy Pods Resources */,
				0C1AD9298C60E1910C900803 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = Runner;
			productName = Runner;
			productReference = 97C146EE1CF9000F007C117D /* Runner.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		97C146E61CF9000F007C117D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = YES;
				LastUpgradeCheck = 1510;
				ORGANIZATIONNAME = "";
				TargetAttributes = {
					331C80F0294D02FB00263BE5 = {
						CreatedOnToolsVersion = 14.0;
						TestTargetID = 97C146ED1CF9000F007C117D;
					};
					97C146ED1CF9000F007C117D = {
						CreatedOnToolsVersion = 7.3.1;
						LastSwiftMigration = 1620;
					};
				};
			};
			buildConfigurationList = 97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 97C146E51CF9000F007C117D;
			productRefGroup = 97C146EF1CF9000F007C117D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				97C146ED1CF9000F007C117D /* Runner */,
				331C80F0294D02FB00263BE5 /* RunnerTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		331C80EF294D02FB00263BE5 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				53265EE32DEEDFF100671E41 /* __UNI__D302F11.wgt in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EC1CF9000F007C117D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				040EC82C2D9CD6B0006698EB /* ICBCJFTBuild.bundle in Resources */,
				53265EE42DEEDFF100671E41 /* __UNI__D302F11.wgt in Resources */,
				040EC82D2D9CD6B0006698EB /* ICBCPaySDK.bundle in Resources */,
				97C147011CF9000F007C117D /* LaunchScreen.storyboard in Resources */,
				3B3967161E833CAA004F5970 /* AppFrameworkInfo.plist in Resources */,
				3FEC87452D1911B700F87558 /* TencentCloudHuiyanSDKFace.bundle in Resources */,
				97C146FE1CF9000F007C117D /* Assets.xcassets in Resources */,
				3FEC87442D1911B700F87558 /* LibFaceAuth.bundle in Resources */,
				97C146FC1CF9000F007C117D /* Main.storyboard in Resources */,
				3FEC87432D1911B700F87558 /* face-tracker-v003.bundle in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		0C1AD9298C60E1910C900803 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		3B06AD1E1E4923F5004D2608 /* Thin Binary */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${TARGET_BUILD_DIR}/${INFOPLIST_PATH}",
			);
			name = "Thin Binary";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" embed_and_thin";
		};
		708027D33D578D2EC60FF813 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-RunnerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9740EEB61CF901F6004384FC /* Run Script */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			name = "Run Script";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "/bin/sh \"$FLUTTER_ROOT/packages/flutter_tools/bin/xcode_backend.sh\" build";
		};
		A83CDC9EEC7187C5F2E41EAD /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Runner/Pods-Runner-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		ADFD83E4BE259CE6F4CE8CE1 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Runner-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		331C80ED294D02FB00263BE5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				331C80F4294D02FB00263BE5 /* RunnerTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		97C146EA1CF9000F007C117D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				3F57AADB2C7ECDAA00BC4FF4 /* FlutterNativePlugin.m in Sources */,
				043AF8EC2D8D54FA00AE3FB0 /* AmapLocationManager.m in Sources */,
				978B8F6F1D3862AE00F588F7 /* AppDelegate.m in Sources */,
				826CAC272D928E400064F35F /* AppLinksIosPlugin.swift in Sources */,
				97C146F31CF9000F007C117D /* main.m in Sources */,
				3FE421D02D2E8F7800110013 /* FddWebViewController.m in Sources */,
				1498D2341E8E89220040F4C2 /* GeneratedPluginRegistrant.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		331C80F6294D02FB00263BE5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 97C146ED1CF9000F007C117D /* Runner */;
			targetProxy = 331C80F5294D02FB00263BE5 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		97C146FA1CF9000F007C117D /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C146FB1CF9000F007C117D /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		97C146FF1CF9000F007C117D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				97C147001CF9000F007C117D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		249021D3217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSDesktopFolderUsageDescription = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Profile;
		};
		249021D4217E4FDB00AE95B9 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				DEVELOPMENT_TEAM = 5859XF3S33;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.shuzibeidahuang.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Profile;
		};
		331C80F8294D02FB00263BE5 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 52A41B5FD68CD05920DC872C /* Pods-RunnerTests.debug.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.bdhSmartAgricApp.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Debug;
		};
		331C80F9294D02FB00263BE5 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B70EBE05A89AF5FD1D5B31FD /* Pods-RunnerTests.release.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.bdhSmartAgricApp.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Release;
		};
		331C80FA294D02FB00263BE5 /* Profile */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A69852F068F10AB55137E013 /* Pods-RunnerTests.profile.xcconfig */;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.example.bdhSmartAgricApp.RunnerTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/Runner.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/Runner";
			};
			name = Profile;
		};
		97C147031CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSDesktopFolderUsageDescription = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		97C147041CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_KEY_NSDesktopFolderUsageDescription = "";
				IPHONEOS_DEPLOYMENT_TARGET = 12.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SUPPORTED_PLATFORMS = iphoneos;
				TARGETED_DEVICE_FAMILY = "1,2";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		97C147061CF9000F007C117D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 9740EEB21CF90195004384FC /* Debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				DEVELOPMENT_TEAM = 5859XF3S33;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.shuzibeidahuang.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		97C147071CF9000F007C117D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7AFA3C8E1D35360C0083082E /* Release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = Runner/Runner.entitlements;
				DEVELOPMENT_TEAM = 5859XF3S33;
				ENABLE_BITCODE = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				INFOPLIST_FILE = Runner/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)",
				);
				PRODUCT_BUNDLE_IDENTIFIER = com.shuzibeidahuang.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_OBJC_BRIDGING_HEADER = "Runner/Runner-Bridging-Header.h";
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		331C80F7294D02FB00263BE5 /* Build configuration list for PBXNativeTarget "RunnerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				331C80F8294D02FB00263BE5 /* Debug */,
				331C80F9294D02FB00263BE5 /* Release */,
				331C80FA294D02FB00263BE5 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C146E91CF9000F007C117D /* Build configuration list for PBXProject "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147031CF9000F007C117D /* Debug */,
				97C147041CF9000F007C117D /* Release */,
				249021D3217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		97C147051CF9000F007C117D /* Build configuration list for PBXNativeTarget "Runner" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				97C147061CF9000F007C117D /* Debug */,
				97C147071CF9000F007C117D /* Release */,
				249021D4217E4FDB00AE95B9 /* Profile */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 97C146E61CF9000F007C117D /* Project object */;
}
