PODS:
  - AMap3DMap (9.7.0):
    - AMapFoundation (>= 1.8.0)
  - amap_flutter_location (0.0.1):
    - AMapLocation
    - Flutter
  - AMapFoundation (1.8.2)
  - AMapLocation (2.10.0):
    - AMapFoundation (>= 1.8.0)
  - AMapSearch (9.7.0):
    - AMapFoundation (>= 1.8.0)
  - app_settings (5.1.1):
    - Flutter
  - audio_session (0.0.1):
    - Flutter
  - Bugly (2.6.1)
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - external_app_launcher (0.0.1):
    - Flutter
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_app_badger (1.3.0):
    - Flutter
  - flutter_downloader (0.0.1):
    - Flutter
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview_ios (0.0.1):
    - Flutter
    - flutter_inappwebview_ios/Core (= 0.0.1)
    - OrderedSet (~> 6.0.3)
  - flutter_inappwebview_ios/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 6.0.3)
  - flutter_local_notifications_plus (0.0.1):
    - Flutter
  - flutter_tts (0.0.1):
    - Flutter
  - flutter_vlc_player (3.0.3):
    - Flutter
    - MobileVLCKit (~> 3.6.0b12)
  - fluwx (0.0.1):
    - Flutter
    - fluwx/pay (= 0.0.1)
  - fluwx/pay (0.0.1):
    - Flutter
    - WechatOpenSDK-XCFramework (~> 2.0.4)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleMLKit/BarcodeScanning (6.0.0):
    - GoogleMLKit/MLKitCore
    - MLKitBarcodeScanning (~> 5.0.0)
  - GoogleMLKit/MLKitCore (6.0.0):
    - MLKitCommon (~> 11.0.0)
  - GoogleToolboxForMac/Defines (4.2.1)
  - GoogleToolboxForMac/Logger (4.2.1):
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - "GoogleToolboxForMac/NSData+zlib (4.2.1)":
    - GoogleToolboxForMac/Defines (= 4.2.1)
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilitiesComponents (1.1.0):
    - GoogleUtilities/Logger
  - GTMSessionFetcher/Core (3.5.0)
  - image_picker_ios (0.0.1):
    - Flutter
  - install_plugin (2.0.0):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - MLImage (1.0.0-beta5)
  - MLKitBarcodeScanning (5.0.0):
    - MLKitCommon (~> 11.0)
    - MLKitVision (~> 7.0)
  - MLKitCommon (11.0.0):
    - GoogleDataTransport (< 10.0, >= 9.4.1)
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GoogleUtilities/UserDefaults (< 8.0, >= 7.13.0)
    - GoogleUtilitiesComponents (~> 1.0)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
  - MLKitVision (7.0.0):
    - GoogleToolboxForMac/Logger (< 5.0, >= 4.2.1)
    - "GoogleToolboxForMac/NSData+zlib (< 5.0, >= 4.2.1)"
    - GTMSessionFetcher/Core (< 4.0, >= 3.3.2)
    - MLImage (= 1.0.0-beta5)
    - MLKitCommon (~> 11.0)
  - mobile_scanner (5.2.3):
    - Flutter
    - GoogleMLKit/BarcodeScanning (~> 6.0.0)
  - MobileVLCKit (3.6.1b1)
  - move_to_background (0.0.1):
    - Flutter
  - MTBBarcodeScanner (5.0.11)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - open_filex (0.0.2):
    - Flutter
  - OrderedSet (6.0.3)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - PromisesObjC (2.4.0)
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - record (0.0.1):
    - Flutter
  - SDWebImage (5.19.2):
    - SDWebImage/Core (= 5.19.2)
  - SDWebImage/Core (5.19.2)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - sensors_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - smart_auth (0.0.1):
    - Flutter
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - syncfusion_flutter_pdfviewer (0.0.1):
    - Flutter
  - UMCCommonLog (2.0.2)
  - UMCommon (7.5.3):
    - UMDevice
  - UMDevice (3.4.0)
  - umeng_common_sdk (0.0.1):
    - Flutter
    - UMCommon
    - UMDevice
  - umeng_push_sdk (0.0.1):
    - Flutter
    - UMCCommonLog
    - UMCommon
    - UMDevice
    - UMPush
  - umeng_verify_sdk (0.0.1):
    - Flutter
    - UMVerify
  - UMPush (4.1.3):
    - UMCommon
  - UMVerify (3.1.0):
    - UMCommon
  - unimp/Audio (4.56)
  - unimp/Barcode (4.56)
  - "unimp/Camera&Gallery (4.56)"
  - unimp/Core (4.56):
    - SDWebImage (= 5.19.2)
  - unimp/File (4.56)
  - unimp/Geolocation (4.56)
  - unimp/Geolocation-Gaode (4.56):
    - AMapLocation (= 2.10.0)
    - AMapSearch (= 9.7.0)
    - unimp/Geolocation
  - unimp/Map (4.56)
  - unimp/Map-Gaode (4.56):
    - AMap3DMap (= 9.7.0)
    - unimp/Map
    - unimp/Masonry
  - unimp/Masonry (4.56)
  - unimp/NativeJS (4.56)
  - unimp/QQ (4.56)
  - unimp/Share (4.56)
  - unimp/Share-QQ (4.56):
    - unimp/QQ
    - unimp/Share
  - unimp/Video (4.56):
    - unimp/Masonry
  - unimp/XMLHttpRequest (4.56)
  - unimp/Zip (4.56)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - video_thumbnail (0.0.1):
    - Flutter
    - libwebp
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
    - FlutterMacOS
  - WechatOpenSDK-XCFramework (2.0.4)

DEPENDENCIES:
  - amap_flutter_location (from `.symlinks/plugins/amap_flutter_location/ios`)
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - Bugly
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - external_app_launcher (from `.symlinks/plugins/external_app_launcher/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_app_badger (from `.symlinks/plugins/flutter_app_badger/ios`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_inappwebview_ios (from `.symlinks/plugins/flutter_inappwebview_ios/ios`)
  - flutter_local_notifications_plus (from `.symlinks/plugins/flutter_local_notifications_plus/ios`)
  - flutter_tts (from `.symlinks/plugins/flutter_tts/ios`)
  - flutter_vlc_player (from `.symlinks/plugins/flutter_vlc_player/ios`)
  - fluwx (from `.symlinks/plugins/fluwx/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - install_plugin (from `.symlinks/plugins/install_plugin/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - mobile_scanner (from `.symlinks/plugins/mobile_scanner/ios`)
  - move_to_background (from `.symlinks/plugins/move_to_background/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - record (from `.symlinks/plugins/record/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - smart_auth (from `.symlinks/plugins/smart_auth/ios`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - syncfusion_flutter_pdfviewer (from `.symlinks/plugins/syncfusion_flutter_pdfviewer/ios`)
  - UMCCommonLog
  - umeng_common_sdk (from `.symlinks/plugins/umeng_common_sdk/ios`)
  - umeng_push_sdk (from `.symlinks/plugins/umeng_push_sdk/ios`)
  - umeng_verify_sdk (from `.symlinks/plugins/umeng_verify_sdk/ios`)
  - unimp/Audio
  - unimp/Barcode
  - "unimp/Camera&Gallery"
  - unimp/Core
  - unimp/File
  - unimp/Geolocation
  - unimp/Geolocation-Gaode
  - unimp/Map-Gaode
  - unimp/NativeJS
  - unimp/Share-QQ
  - unimp/Video
  - unimp/XMLHttpRequest
  - unimp/Zip
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - video_thumbnail (from `.symlinks/plugins/video_thumbnail/ios`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/darwin`)

SPEC REPOS:
  trunk:
    - AMap3DMap
    - AMapFoundation
    - AMapLocation
    - AMapSearch
    - Bugly
    - DKImagePickerController
    - DKPhotoGallery
    - GoogleDataTransport
    - GoogleMLKit
    - GoogleToolboxForMac
    - GoogleUtilities
    - GoogleUtilitiesComponents
    - GTMSessionFetcher
    - libwebp
    - Mantle
    - MLImage
    - MLKitBarcodeScanning
    - MLKitCommon
    - MLKitVision
    - MobileVLCKit
    - MTBBarcodeScanner
    - nanopb
    - OrderedSet
    - PromisesObjC
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif
    - UMCCommonLog
    - UMCommon
    - UMDevice
    - UMPush
    - UMVerify
    - unimp
    - WechatOpenSDK-XCFramework

EXTERNAL SOURCES:
  amap_flutter_location:
    :path: ".symlinks/plugins/amap_flutter_location/ios"
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  external_app_launcher:
    :path: ".symlinks/plugins/external_app_launcher/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_app_badger:
    :path: ".symlinks/plugins/flutter_app_badger/ios"
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_inappwebview_ios:
    :path: ".symlinks/plugins/flutter_inappwebview_ios/ios"
  flutter_local_notifications_plus:
    :path: ".symlinks/plugins/flutter_local_notifications_plus/ios"
  flutter_tts:
    :path: ".symlinks/plugins/flutter_tts/ios"
  flutter_vlc_player:
    :path: ".symlinks/plugins/flutter_vlc_player/ios"
  fluwx:
    :path: ".symlinks/plugins/fluwx/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  install_plugin:
    :path: ".symlinks/plugins/install_plugin/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  mobile_scanner:
    :path: ".symlinks/plugins/mobile_scanner/ios"
  move_to_background:
    :path: ".symlinks/plugins/move_to_background/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  record:
    :path: ".symlinks/plugins/record/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  smart_auth:
    :path: ".symlinks/plugins/smart_auth/ios"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  syncfusion_flutter_pdfviewer:
    :path: ".symlinks/plugins/syncfusion_flutter_pdfviewer/ios"
  umeng_common_sdk:
    :path: ".symlinks/plugins/umeng_common_sdk/ios"
  umeng_push_sdk:
    :path: ".symlinks/plugins/umeng_push_sdk/ios"
  umeng_verify_sdk:
    :path: ".symlinks/plugins/umeng_verify_sdk/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  video_thumbnail:
    :path: ".symlinks/plugins/video_thumbnail/ios"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/darwin"

SPEC CHECKSUMS:
  AMap3DMap: dce25dd3e51e6b92109caa7d0c97fc6055830fb3
  amap_flutter_location: f033c983c2d4319203ff7b523775579534d0d557
  AMapFoundation: 9885c48fc3a78fdfb84a0299a2293e56ea3c9fec
  AMapLocation: 5248aec2455ebb5d104b367813c946430a2ee033
  AMapSearch: 1f24a4acee521d993a8190348d27f5765841e32a
  app_settings: 5127ae0678de1dcc19f2293271c51d37c89428b2
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  Bugly: 217ac2ce5f0f2626d43dbaa4f70764c953a26a31
  camera_avfoundation: be3be85408cd4126f250386828e9b1dfa40ab436
  connectivity_plus: cb623214f4e1f6ef8fe7403d580fdad517d2f7dd
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  external_app_launcher: 3411245965270a74040a3de17e27bd02b8abb905
  file_picker: a0560bc09d61de87f12d246fc47d2119e6ef37be
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_app_badger: 16b371e989d04cd265df85be2c3851b49cb68d18
  flutter_downloader: 78da0da1084e709cbfd3b723c7ea349c71681f09
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_inappwebview_ios: b89ba3482b96fb25e00c967aae065701b66e9b99
  flutter_local_notifications_plus: 4c25c2ca361fe2b180425472d172fb7b8becae69
  flutter_tts: b88dbc8655d3dc961bc4a796e4e16a4cc1795833
  flutter_vlc_player: adffbf4816d69407ca712e4cc1ff1d257b4fd0a9
  fluwx: 6bf9c5a3a99ad31b0de137dd92370a0d10a60f4b
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleMLKit: 97ac7af399057e99182ee8edfa8249e3226a4065
  GoogleToolboxForMac: d1a2cbf009c453f4d6ded37c105e2f67a32206d8
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GoogleUtilitiesComponents: 679b2c881db3b615a2777504623df6122dd20afe
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  install_plugin: e17e38d6f504857748a3ec1299d8a2bbeeeea854
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  MLImage: 1824212150da33ef225fbd3dc49f184cf611046c
  MLKitBarcodeScanning: 10ca0845a6d15f2f6e911f682a1998b68b973e8b
  MLKitCommon: afec63980417d29ffbb4790529a1b0a2291699e1
  MLKitVision: e858c5f125ecc288e4a31127928301eaba9ae0c1
  mobile_scanner: 92e8812bf22a8f84131e2a7f9d0f44dad1a4742b
  MobileVLCKit: 2d9c7c373393ae43086aeeff890bf0b1afc15c5c
  move_to_background: 155f7bfbd34d43ad847cb630d2d2d87c17199710
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  open_filex: 432f3cd11432da3e39f47fcc0df2b1603854eff1
  OrderedSet: e539b66b644ff081c73a262d24ad552a69be3a94
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  qr_code_scanner: d77f94ecc9abf96d9b9b8fc04ef13f611e5a147a
  record: 426d0643542aaca81f738c5d214ad99626b2f134
  SDWebImage: dfe95b2466a9823cf9f0c6d01217c06550d7b29a
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  sensors_plus: 6a11ed0c2e1d0bd0b20b4029d3bad27d96e0c65b
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  smart_auth: 33668081c5f646af84f10492a067d25fdcd16951
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  syncfusion_flutter_pdfviewer: dfb514751af5b6b71e504c9c04a2e4ddbc1dd895
  UMCCommonLog: bea707e50c85cef4b0eb47cc5c7226bb843245ca
  UMCommon: 3b850836e8bc162b4e7f6b527d30071ed8ea75a1
  UMDevice: dcdf7ec167387837559d149fbc7d793d984faf82
  umeng_common_sdk: 095b63e6f83b71548725a7c03a6c17b23be6674b
  umeng_push_sdk: 557ebb6834df105bd828b86eeb8ae0b80959ee79
  umeng_verify_sdk: 47a9d15fd17eece30c8a3e76b781ad46c5aad4da
  UMPush: 5d4c4c28f050bf3898ddce00210432050cf8aead
  UMVerify: 832cbda6a895a798fd5619035594c9370561158d
  unimp: 3851ba2103eb4a008387c278ced14573d27bac10
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  video_thumbnail: b637e0ad5f588ca9945f6e2c927f73a69a661140
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 1821ceac936eba6f7984d89a9f3bcb4dea99ebb2
  WechatOpenSDK-XCFramework: 36fb2bea0754266c17184adf4963d7e6ff98b69f

PODFILE CHECKSUM: c867c6161cdc93bd6970a0a7f1cd665feb030a24

COCOAPODS: 1.16.2
