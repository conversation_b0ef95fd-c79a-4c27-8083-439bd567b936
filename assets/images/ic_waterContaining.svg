<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="8" y="3" width="24" height="13" rx="1.66667" fill="url(#paint0_linear_19_992)"/>
<g filter="url(#filter0_b_19_992)">
<path d="M4.69814 9.1306C4.80531 7.37165 6.26308 6 8.0253 6H31.9747C33.7369 6 35.1947 7.37165 35.3019 9.1306L36.7845 33.4639C36.9014 35.3811 35.3781 37 33.4574 37H6.54262C4.62194 37 3.09865 35.3811 3.21546 33.4639L4.69814 9.1306Z" fill="url(#paint1_linear_19_992)" fill-opacity="0.8"/>
</g>
<path d="M20.0001 12.4999C20.7701 12.4999 27.7581 18.4259 27.7581 24.7419C27.7581 26.7995 26.9407 28.7728 25.4858 30.2277C24.0309 31.6826 22.0576 32.4999 20.0001 32.4999C17.9425 32.4999 15.9692 31.6826 14.5143 30.2277C13.0594 28.7728 12.2421 26.7995 12.2421 24.7419C12.2421 18.4969 19.2291 12.4999 20.0001 12.4999ZM20.0001 25.1289C18.1661 22.0869 15.2001 22.4999 14.2421 24.7419C14.2421 26.3319 14.8861 27.7719 15.9281 28.8129C16.4622 29.3486 17.0969 29.7733 17.7957 30.0629C18.4945 30.3524 19.2437 30.5009 20.0001 30.4999C20.7565 30.5009 21.5056 30.3524 22.2044 30.0629C22.9033 29.7733 23.538 29.3486 24.0721 28.8129C24.6074 28.2789 25.0319 27.6443 25.3213 26.9457C25.6106 26.247 25.759 25.4981 25.7581 24.7419C25.0281 27.0629 21.8341 28.1699 20.0001 25.1289Z" fill="white"/>
<defs>
<filter id="filter0_b_19_992" x="-0.790833" y="2" width="41.5817" height="39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_19_992"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_19_992" result="shape"/>
</filter>
<linearGradient id="paint0_linear_19_992" x1="4.36364" y1="4.25806" x2="4.36364" y2="16" gradientUnits="userSpaceOnUse">
<stop stop-color="#24B1FF"/>
<stop offset="1" stop-color="#23B2FF"/>
</linearGradient>
<linearGradient id="paint1_linear_19_992" x1="20" y1="6" x2="20" y2="37" gradientUnits="userSpaceOnUse">
<stop stop-color="#1D92FF"/>
<stop offset="1" stop-color="#1DC3FF"/>
</linearGradient>
</defs>
</svg>
