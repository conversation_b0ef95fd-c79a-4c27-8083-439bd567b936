<svg width="403" height="200" viewBox="0 0 403 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M403 199.483C355.438 136.425 310.387 92.483 205.668 92.483C100.95 92.483 47.5928 139.75 0 199.483H403Z" fill="url(#paint0_linear_1_52)"/>
<path d="M256.76 0H173.533C166.33 0 160.491 5.83944 160.491 13.0428L160.49 101.174C160.49 108.377 166.33 114.217 173.533 114.217H256.76C263.964 114.217 269.803 108.377 269.803 101.174L269.803 13.0428C269.803 5.83944 263.964 0 256.76 0Z" fill="#04AB68"/>
<g filter="url(#filter0_bi_1_52)">
<path d="M237.408 21.1282H146.043C138.839 21.1282 133 26.9676 133 34.171V126.464C133 133.668 138.839 139.507 146.043 139.507H237.408C244.612 139.507 250.451 133.668 250.451 126.464V34.171C250.451 26.9676 244.612 21.1282 237.408 21.1282Z" fill="url(#paint1_linear_1_52)" fill-opacity="0.5"/>
</g>
<g filter="url(#filter1_bi_1_52)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M146.043 21.1282H237.408C244.612 21.1282 250.451 26.9676 250.451 34.171V43.953H133V34.171C133 26.9676 138.839 21.1282 146.043 21.1282Z" fill="white" fill-opacity="0.46"/>
</g>
<rect x="150.788" y="59.0238" width="17.788" height="17.788" rx="6" fill="white"/>
<rect x="150.788" y="88.1314" width="17.788" height="17.788" rx="6" fill="white"/>
<rect x="175.853" y="59.0238" width="56.5981" height="6.46836" rx="3.23418" fill="white"/>
<rect x="175.853" y="88.1314" width="56.5981" height="6.46836" rx="3.23418" fill="white"/>
<rect x="175.853" y="70.3434" width="29.9161" height="6.46836" rx="3.23418" fill="white"/>
<rect x="175.853" y="99.451" width="29.9161" height="6.46836" rx="3.23418" fill="white"/>
<defs>
<filter id="filter0_bi_1_52" x="103" y="-8.8718" width="177.451" height="178.379" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="15"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1_52"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1_52" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="3" dy="-1"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.33 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1_52"/>
</filter>
<filter id="filter1_bi_1_52" x="103" y="-8.8718" width="177.451" height="82.8248" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="15"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1_52"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1_52" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.42 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_1_52"/>
</filter>
<linearGradient id="paint0_linear_1_52" x1="201.5" y1="99.2982" x2="201.5" y2="227.396" gradientUnits="userSpaceOnUse">
<stop stop-color="#BEE9D8"/>
<stop offset="1" stop-color="#F3F5F9" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_1_52" x1="187.683" y1="21.1282" x2="187.683" y2="159.821" gradientUnits="userSpaceOnUse">
<stop stop-color="#C9FBE7"/>
<stop offset="1" stop-color="#EAFFF6"/>
</linearGradient>
</defs>
</svg>
