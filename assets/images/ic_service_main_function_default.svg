<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_20_722)">
<rect width="45.6194" height="82.0312" rx="10.0446" transform="matrix(-1 0 0 1 88.5251 9.76562)" fill="#3E9D6C"/>
</g>
<g filter="url(#filter1_i_20_722)">
<rect x="36" y="9.76562" width="49.8047" height="82.0312" rx="10.0446" fill="#1E2A48"/>
</g>
<rect x="36.1046" y="9.87026" width="49.5954" height="81.822" rx="9.94001" stroke="url(#paint0_linear_20_722)" stroke-width="0.209263"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M51.0671 12.4861L46.6724 12.4861C42.0495 12.4861 38.3019 16.2337 38.3019 20.8566V80.7059C38.3019 85.3289 42.0495 89.0765 46.6724 89.0765H75.1322C79.7552 89.0765 83.5028 85.3289 83.5028 80.7059V20.8566C83.5028 16.2337 79.7552 12.4861 75.1322 12.4861L70.7378 12.4861C70.7378 14.3353 69.2388 15.8343 67.3896 15.8343H54.4153C52.5661 15.8343 51.0671 14.3353 51.0671 12.4861Z" fill="url(#paint1_linear_20_722)"/>
<path opacity="0.4" d="M45.7222 37.2821C45.7222 36.6353 46.2663 36.1111 46.9375 36.1111H53.0139C56.7964 36.1107 59.9511 38.8973 60.2789 42.5283C60.2789 42.5412 60.2911 42.5518 60.3056 42.5518C60.3193 42.5518 60.3311 42.5415 60.3322 42.5283C60.66 38.8977 63.8151 36.1111 67.5972 36.1111H73.6736C74.3448 36.1111 74.8889 36.6353 74.8889 37.2821V40.795C74.8889 44.6755 71.6242 47.8208 67.5972 47.8208H63.3441C62.6729 47.8208 62.1289 48.3451 62.1289 48.9918V55.188C62.1289 56.1579 61.313 56.9444 60.3059 56.9444C59.2989 56.9444 58.483 56.1579 58.483 55.188V48.9918C58.483 48.3451 57.939 47.8208 57.2677 47.8208H53.0143C48.987 47.8208 45.7226 44.6751 45.7226 40.795L45.7222 37.2821Z" fill="white"/>
<g filter="url(#filter2_i_20_722)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M56.0819 48.6111H52.5524V48.6111H22.1111C19.0429 48.6111 16.5556 51.0984 16.5556 54.1667V91.6667H48.5C50.8012 91.6667 52.6667 89.8012 52.6667 87.5V53.7091H56.0819V48.6111Z" fill="url(#paint2_linear_20_722)"/>
</g>
<path d="M52.5524 52.1405C52.5524 50.1913 54.1326 48.6111 56.0818 48.6111C58.0311 48.6111 59.6113 50.1913 59.6113 52.1405V56.4542H52.5524V52.1405Z" fill="url(#paint3_linear_20_722)"/>
<g filter="url(#filter3_ii_20_722)">
<path d="M11 81.9445H45.3207V87.0985C45.3207 89.6214 47.3659 91.6667 49.8889 91.6667H15.183C12.8728 91.6667 11 89.7939 11 87.4837V81.9445Z" fill="url(#paint4_linear_20_722)"/>
</g>
<rect x="22.1111" y="55.5555" width="2.77778" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="22.1111" y="63.8889" width="2.77778" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="22.1111" y="72.2223" width="2.77778" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="22.1111" y="76.3889" width="2.77778" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="22.1111" y="68.0555" width="2.77778" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="22.1111" y="59.7223" width="2.77778" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="26.2778" y="55.5555" width="13.8889" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="26.2778" y="63.8889" width="20.8333" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="26.2778" y="72.2223" width="20.8333" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="26.2778" y="76.3889" width="20.8333" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="26.2778" y="68.0555" width="20.8333" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<rect x="26.2778" y="59.7223" width="20.8333" height="1.38889" rx="0.694444" fill="#CBCFD2"/>
<defs>
<filter id="filter0_i_20_722" x="42.9057" y="9.76562" width="46.4565" height="82.0312" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="0.837053"/>
<feGaussianBlur stdDeviation="0.837053"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.873161 0 0 0 0 1 0 0 0 0 0.93658 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_20_722"/>
</filter>
<filter id="filter1_i_20_722" x="36" y="9.76562" width="49.8047" height="82.0312" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.418527"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.629386 0 0 0 0 0.724011 0 0 0 0 0.887158 0 0 0 0.4 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_20_722"/>
</filter>
<filter id="filter2_i_20_722" x="16.5556" y="47.5653" width="39.5263" height="44.1013" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.04575"/>
<feGaussianBlur stdDeviation="1.50327"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_20_722"/>
</filter>
<filter id="filter3_ii_20_722" x="11" y="81.4216" width="38.8889" height="10.7679" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.522876"/>
<feGaussianBlur stdDeviation="1.19608"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_20_722"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.522876"/>
<feGaussianBlur stdDeviation="0.261438"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_20_722" result="effect2_innerShadow_20_722"/>
</filter>
<linearGradient id="paint0_linear_20_722" x1="60.9023" y1="9.76562" x2="60.9023" y2="91.7969" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0.6"/>
<stop offset="1" stop-color="white" stop-opacity="0.1"/>
</linearGradient>
<linearGradient id="paint1_linear_20_722" x1="60.9023" y1="12.4861" x2="60.9023" y2="89.0765" gradientUnits="userSpaceOnUse">
<stop stop-color="#60EA97"/>
<stop offset="1" stop-color="#32CCB2"/>
</linearGradient>
<linearGradient id="paint2_linear_20_722" x1="33.5979" y1="38.8889" x2="33.5979" y2="92.353" gradientUnits="userSpaceOnUse">
<stop stop-color="#ECEEF0"/>
<stop offset="0.045" stop-color="#F9FAFB"/>
<stop offset="0.20906" stop-color="#E8EBED"/>
<stop offset="0.63" stop-color="#EAEEF1"/>
<stop offset="0.915" stop-color="#E9EDF0"/>
<stop offset="1" stop-color="#F8FAFB"/>
</linearGradient>
<linearGradient id="paint3_linear_20_722" x1="56.0818" y1="48.6111" x2="56.0818" y2="56.4542" gradientUnits="userSpaceOnUse">
<stop stop-color="#D4D9DD"/>
<stop offset="1" stop-color="#B9BDC8"/>
</linearGradient>
<linearGradient id="paint4_linear_20_722" x1="29.3838" y1="81.9445" x2="29.3838" y2="91.6667" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBE2E8"/>
<stop offset="0.865" stop-color="#A5ACBA"/>
<stop offset="1" stop-color="#B9BDC8"/>
</linearGradient>
</defs>
</svg>
