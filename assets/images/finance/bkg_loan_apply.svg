<svg width="750" height="645" viewBox="0 0 750 645" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<mask id="mask0_7860_2756" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="750" height="645">
<rect width="750" height="645" fill="url(#paint0_linear_7860_2756)"/>
<rect width="750" height="645" fill="url(#paint1_linear_7860_2756)"/>
<rect width="750" height="645" fill="url(#paint2_radial_7860_2756)" fill-opacity="0.6"/>
</mask>
<g mask="url(#mask0_7860_2756)">
<rect width="750" height="645" fill="url(#paint3_linear_7860_2756)"/>
<rect width="750" height="645" fill="url(#paint4_linear_7860_2756)"/>
<rect width="750" height="645" fill="url(#paint5_radial_7860_2756)" fill-opacity="0.7"/>
<rect width="750" height="645" fill="url(#paint6_radial_7860_2756)" fill-opacity="0.3"/>
<foreignObject x="-82.0815" y="-230.081" width="249.276" height="322.107"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_0_7860_2756_clip_path);height:100%;width:100%"></div></foreignObject><g opacity="0.6" data-figma-bg-blur-radius="4">
<rect x="-81" y="-182.412" width="180" height="283" rx="13" transform="rotate(-15 -81 -182.412)" fill="url(#paint7_linear_7860_2756)" fill-opacity="0.25"/>
<rect x="-79.7753" y="-181.705" width="178" height="281" rx="12" transform="rotate(-15 -79.7753 -181.705)" stroke="url(#paint8_linear_7860_2756)" stroke-opacity="0.3" stroke-width="2"/>
</g>
<foreignObject x="567.63" y="-35.3701" width="364.611" height="416.049"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(2px);clip-path:url(#bgblur_1_7860_2756_clip_path);height:100%;width:100%"></div></foreignObject><g opacity="0.6" data-figma-bg-blur-radius="4">
<rect x="735.688" y="-36" width="224.484" height="352.939" rx="13" transform="rotate(28.5517 735.688 -36)" fill="url(#paint9_linear_7860_2756)" fill-opacity="0.25"/>
<rect x="736.088" y="-34.6437" width="222.484" height="350.939" rx="12" transform="rotate(28.5517 736.088 -34.6437)" stroke="url(#paint10_linear_7860_2756)" stroke-opacity="0.3" stroke-width="2"/>
</g>
<foreignObject x="18.9188" y="-185.082" width="254.241" height="326.367"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(3px);clip-path:url(#bgblur_2_7860_2756_clip_path);height:100%;width:100%"></div></foreignObject><g data-figma-bg-blur-radius="6">
<rect x="95.2461" y="-182" width="181" height="283" rx="13" transform="rotate(15 95.2461 -182)" fill="url(#paint11_linear_7860_2756)" fill-opacity="0.25"/>
<rect x="95.9532" y="-180.775" width="179" height="281" rx="12" transform="rotate(15 95.9532 -180.775)" stroke="url(#paint12_linear_7860_2756)" stroke-opacity="0.4" stroke-width="2"/>
</g>
<rect width="698" height="397" fill="url(#pattern0_7860_2756)"/>
<rect x="351" y="164" width="41" height="47" fill="url(#pattern1_7860_2756)"/>
<rect x="426" y="88" width="50" height="54" fill="url(#pattern2_7860_2756)"/>
<g filter="url(#filter3_d_7860_2756)">
<path d="M56.72 230.612C61.8427 227.212 66.172 223.404 69.708 219.188C73.2893 214.972 76.2133 210.144 78.48 204.704H67.6V211.64H58.76V196.272H81.268C81.7213 194.685 82.1293 193.008 82.492 191.24H91.604C91.1507 193.507 90.7653 195.184 90.448 196.272H117.24V211.64H108.4V204.704H97.248C97.384 214.677 99.356 222.905 103.164 229.388L109.76 214.836H118.396L111.596 229.864H103.436C106.972 235.667 112.027 240.155 118.6 243.328V253.664C108.853 249.267 101.487 242.92 96.5 234.624C91.5587 226.283 88.9973 216.309 88.816 204.704H87.864C84.6 213.453 80.0893 221.047 74.332 227.484H77.596V245.028H85.892V253.8H68.824V232.992C65.3787 236.075 61.344 238.885 56.72 241.424V230.612ZM131.316 220.82H180.684V240.132H171.912V228.436H140.088V240.812H131.316V220.82ZM124.38 206.336C129.639 202.936 133.265 198.357 135.26 192.6H144.304C143.352 196.136 142.037 199.332 140.36 202.188V219.46H131.588V212.388C129.367 214.201 126.964 215.765 124.38 217.08V206.336ZM125.4 246.048C140.133 244.915 149.449 239.543 153.348 229.932H162.8C161.803 232.879 160.511 235.553 158.924 237.956L186.6 246.048V254.48L152.94 244.688C146.004 250.445 136.824 253.619 125.4 254.208V246.048ZM144.304 206.608L159.4 203.684C156.952 199.921 154.799 196.227 152.94 192.6H163.14C164.772 195.683 166.653 198.788 168.784 201.916L186.6 198.448V206.88L174.36 209.26C177.035 212.615 180.072 216.015 183.472 219.46H172.252C169.849 217.148 167.333 214.36 164.704 211.096L144.304 215.04V206.608ZM176.876 192.6L180.616 198.856H172.32L168.58 192.6H176.876ZM193.4 243.872L196.12 243.736V194.368H221.212V242.58L223.388 242.512V249.448C225.745 244.688 227.4 239.883 228.352 235.032C229.349 230.136 229.848 224.696 229.848 218.712V211.912H223.592V203.14H229.848V192.192H238.552V203.14H253.92V253.8H237.328V245.096H245.216V211.912H238.416V218.712C238.416 225.24 237.872 231.337 236.784 237.004C235.696 242.671 233.928 248.269 231.48 253.8H221.008L222.436 251.352L193.4 252.644V243.872ZM212.78 242.988V235.372H204.552V243.396L212.78 242.988ZM212.78 226.804V218.44H204.552V226.804H212.78ZM212.78 209.872V203.072H204.552V209.872H212.78ZM275.612 245.096H288.804V233.876H260.72V225.376H288.804V217.76H267.044V209.464H288.804V202.188C280.1 202.415 271.94 202.528 264.324 202.528V194.028C283.545 194.028 301.996 193.325 319.676 191.92V200.42C313.601 200.919 306.348 201.372 297.916 201.78V209.464H316.956V217.76H297.916V225.376H323.28V233.876H297.916V253.936H275.612V245.096Z" fill="url(#paint13_linear_7860_2756)"/>
</g>
</g>
<defs>
<clipPath id="bgblur_0_7860_2756_clip_path" transform="translate(82.0815 230.081)"><rect x="-81" y="-182.412" width="180" height="283" rx="13" transform="rotate(-15 -81 -182.412)"/>
</clipPath><clipPath id="bgblur_1_7860_2756_clip_path" transform="translate(-567.63 35.3701)"><rect x="735.688" y="-36" width="224.484" height="352.939" rx="13" transform="rotate(28.5517 735.688 -36)"/>
</clipPath><clipPath id="bgblur_2_7860_2756_clip_path" transform="translate(-18.9188 185.082)"><rect x="95.2461" y="-182" width="181" height="283" rx="13" transform="rotate(15 95.2461 -182)"/>
</clipPath>


<filter id="filter3_d_7860_2756" x="52.72" y="191.24" width="274.56" height="71.2402" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.545098 0 0 0 0 0.364706 0 0 0 0.29 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7860_2756"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7860_2756" result="shape"/>
</filter>
<linearGradient id="paint0_linear_7860_2756" x1="16.1942" y1="27.8539" x2="750" y2="27.8539" gradientUnits="userSpaceOnUse">
<stop stop-color="#17D183"/>
<stop offset="1" stop-color="#028B5D"/>
</linearGradient>
<linearGradient id="paint1_linear_7860_2756" x1="375" y1="0" x2="375" y2="645" gradientUnits="userSpaceOnUse">
<stop stop-color="#F3F5F9" stop-opacity="0"/>
<stop offset="1" stop-color="#F3F5F9"/>
</linearGradient>
<radialGradient id="paint2_radial_7860_2756" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(730 -2.32803e-05) rotate(135.918) scale(309.045 308.913)">
<stop stop-color="#AAF8BB"/>
<stop offset="1" stop-color="#AAF8BB" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint3_linear_7860_2756" x1="16.1942" y1="27.8539" x2="750" y2="27.8539" gradientUnits="userSpaceOnUse">
<stop stop-color="#17D183"/>
<stop offset="1" stop-color="#028B5D"/>
</linearGradient>
<linearGradient id="paint4_linear_7860_2756" x1="375" y1="0" x2="375" y2="645" gradientUnits="userSpaceOnUse">
<stop offset="0.45" stop-color="#F3F5F9" stop-opacity="0"/>
<stop offset="1" stop-color="#F3F5F9"/>
</linearGradient>
<radialGradient id="paint5_radial_7860_2756" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(672 -36) rotate(106.299) scale(356.32 473.517)">
<stop stop-color="#F8EAAA"/>
<stop offset="1" stop-color="#F8EAAA" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint6_radial_7860_2756" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(500 292) rotate(-75.8745) scale(311.416 527.343)">
<stop stop-color="#48CEFF"/>
<stop offset="1" stop-color="#48CEFF" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint7_linear_7860_2756" x1="-0.999993" y1="-83.8078" x2="3.60327" y2="98.2483" gradientUnits="userSpaceOnUse">
<stop stop-color="#58C392"/>
<stop offset="1" stop-color="#A0F9F1"/>
</linearGradient>
<linearGradient id="paint8_linear_7860_2756" x1="-77.1111" y1="-132.831" x2="105.159" y2="91.1836" gradientUnits="userSpaceOnUse">
<stop stop-color="#CEFEFA" stop-opacity="0"/>
<stop offset="1" stop-color="#CEFEFA"/>
</linearGradient>
<linearGradient id="paint9_linear_7860_2756" x1="835.458" y1="86.9728" x2="841.199" y2="314.021" gradientUnits="userSpaceOnUse">
<stop stop-color="#58C392"/>
<stop offset="1" stop-color="#A0F9F1"/>
</linearGradient>
<linearGradient id="paint10_linear_7860_2756" x1="740.537" y1="25.8338" x2="967.852" y2="305.21" gradientUnits="userSpaceOnUse">
<stop stop-color="#CEFEFA" stop-opacity="0"/>
<stop offset="1" stop-color="#CEFEFA"/>
</linearGradient>
<linearGradient id="paint11_linear_7860_2756" x1="103.25" y1="-23.6768" x2="190.253" y2="136.804" gradientUnits="userSpaceOnUse">
<stop stop-color="#58C392"/>
<stop offset="1" stop-color="#A0F9F1"/>
</linearGradient>
<linearGradient id="paint12_linear_7860_2756" x1="134.862" y1="-91.1579" x2="281.331" y2="92.4862" gradientUnits="userSpaceOnUse">
<stop stop-color="#CEFEFA" stop-opacity="0"/>
<stop offset="1" stop-color="#E4FFFD"/>
</linearGradient>
<linearGradient id="paint13_linear_7860_2756" x1="54" y1="181" x2="340.035" y2="227.31" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#DDFFFA"/>
</linearGradient>

</defs>
</svg>
