<svg width="172" height="172" viewBox="0 0 172 172" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_141_788)">
<path d="M75.2947 0.806158L157.618 20.297C169.476 23.1044 174.001 30.437 171.194 42.2947L151.703 124.618C148.896 136.476 141.563 141.001 129.705 138.194L47.3816 118.703C35.5239 115.896 30.9987 108.563 33.8062 96.7053L53.297 14.3816C56.1044 2.5239 63.437 -2.00127 75.2947 0.806158Z" fill="#00AB66"/>
<g filter="url(#filter0_bi_141_788)">
<path d="M27.027 32.3179H112.571C130.589 32.3179 139.598 41.3269 139.598 59.3449V144.889C139.598 162.907 130.589 171.916 112.571 171.916H27.027C9.009 171.916 0 162.907 0 144.889V59.3449C0 41.3269 9.009 32.3179 27.027 32.3179Z" fill="#CAFFE9" fill-opacity="0.48"/>
</g>
<g filter="url(#filter1_d_141_788)">
<path d="M60.2436 111.83L94.0309 78.2028C94.8324 77.4049 95.9173 76.957 97.0483 76.957C98.1792 76.957 99.2641 77.4049 100.066 78.2028L103.183 81.3057C103.583 81.7031 103.9 82.1754 104.116 82.6956C104.332 83.2158 104.443 83.7736 104.443 84.3369C104.443 84.9002 104.332 85.458 104.116 85.9782C103.9 86.4984 103.583 86.9707 103.183 87.3681L63.3379 127.026C63.1336 127.229 62.9094 127.412 62.6685 127.571C61.8462 128.142 60.8496 128.406 59.8524 128.318C58.8551 128.229 57.9205 127.794 57.2112 127.088L37.2596 107.23C36.8603 106.833 36.5435 106.36 36.3273 105.84C36.1112 105.32 35.9999 104.762 35.9999 104.199C35.9999 103.635 36.1112 103.078 36.3273 102.557C36.5435 102.037 36.8603 101.565 37.2596 101.167L40.3774 98.0646C41.1789 97.2667 42.2638 96.8188 43.3948 96.8188C44.5257 96.8188 45.6106 97.2667 46.4121 98.0646L60.2436 111.83Z" fill="white"/>
</g>
</g>
<defs>
<filter id="filter0_bi_141_788" x="-30" y="2.31793" width="199.598" height="199.598" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="15"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_141_788"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_141_788" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="-1" dy="2"/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.31 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_141_788"/>
</filter>
<filter id="filter1_d_141_788" x="31.9999" y="76.957" width="76.4432" height="59.3774" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.00784314 0 0 0 0 0.545098 0 0 0 0 0.364706 0 0 0 0.22 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_141_788"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_141_788" result="shape"/>
</filter>
<clipPath id="clip0_141_788">
<rect width="172" height="172" fill="white"/>
</clipPath>
</defs>
</svg>
