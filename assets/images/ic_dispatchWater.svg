<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="7" y="3" width="26" height="13" rx="3.33" fill="url(#paint0_radial_19_1003)"/>
<g filter="url(#filter0_b_19_1003)">
<path d="M3 9.33333C3 7.49238 4.49238 6 6.33333 6H33.6667C35.5076 6 37 7.49238 37 9.33333V33.6667C37 35.5076 35.5076 37 33.6667 37H6.33333C4.49238 37 3 35.5076 3 33.6667L3 9.33333Z" fill="#1F6CFF" fill-opacity="0.8"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.0001 21.2631L21.7369 15H16.6156L19.8788 18.2632H12V21.2632H28L28 21.2631H28.0001Z" fill="white"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M12 23L18.2632 29.2632L23.3845 29.2632L20.1213 26L28.0001 26L28.0001 23L12.0001 23L12.0002 23L12 23Z" fill="white"/>
<defs>
<filter id="filter0_b_19_1003" x="-2" y="1" width="44" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2.5"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_19_1003"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_19_1003" result="shape"/>
</filter>
<radialGradient id="paint0_radial_19_1003" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(10.9722 4.625) rotate(25.0133) scale(24.306 18.6712)">
<stop offset="0.219533" stop-color="#F9A546"/>
<stop offset="0.765" stop-color="#FFD241"/>
</radialGradient>
</defs>
</svg>
