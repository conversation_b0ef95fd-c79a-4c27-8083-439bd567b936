<svg width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_20_929)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M33.0078 12.1094C26.1043 12.1094 20.5078 17.7058 20.5078 24.6094V91.9922H74.6094C78.9241 91.9922 82.4219 88.4944 82.4219 84.1797V19.7266H87.6953V12.1094H82.4219H33.0078Z" fill="url(#paint0_linear_20_929)"/>
</g>
<path d="M82.4219 17.3828C82.4219 14.4704 84.7829 12.1094 87.6953 12.1094C90.6078 12.1094 92.9688 14.4704 92.9688 17.3828V23.8281H82.4219V17.3828Z" fill="url(#paint1_linear_20_929)"/>
<g filter="url(#filter1_ii_20_929)">
<path d="M9.96094 74.2188H71.2184V87.6247C71.2184 90.0368 73.1738 91.9922 75.5859 91.9922H16.2109C12.7592 91.9922 9.96094 89.194 9.96094 85.7422V74.2188Z" fill="url(#paint2_linear_20_929)"/>
</g>
<rect x="30.2734" y="25.1953" width="3.90625" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<rect x="30.2734" y="35.3516" width="3.90625" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<rect x="30.2734" y="45.5078" width="3.90625" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<rect x="30.2734" y="55.6641" width="3.90625" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<rect x="30.2734" y="65.8203" width="3.90625" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<rect x="37.8906" y="25.1953" width="10.9375" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<rect x="37.8906" y="35.3516" width="34.7656" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<rect x="37.8906" y="45.5078" width="34.7656" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<rect x="37.8906" y="55.6641" width="34.7656" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<rect x="37.8906" y="65.8203" width="34.7656" height="3.90625" rx="1.95312" fill="#CBCFD2"/>
<g filter="url(#filter2_ii_20_929)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M70.3125 50.0671C81.0993 50.0671 89.8438 42.8503 89.8438 33.948C89.8438 25.0456 81.0993 17.8288 70.3125 17.8288C59.5257 17.8288 50.7812 25.0456 50.7812 33.948C50.7812 37.6632 52.3043 41.0849 54.8631 43.811L53.2941 51.6448L60.9961 48.1186C63.7651 49.3612 66.9389 50.0671 70.3125 50.0671Z" fill="url(#paint3_radial_20_929)"/>
</g>
<g filter="url(#filter3_dii_20_929)">
<path d="M61.1049 33.8802L67.4064 40.5273L78.9342 29.1016" stroke="#D4F9E8" stroke-width="5.46875" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<filter id="filter0_i_20_929" x="20.5078" y="10.5469" width="67.1875" height="81.4453" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-1.5625"/>
<feGaussianBlur stdDeviation="2.24609"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_20_929"/>
</filter>
<filter id="filter1_ii_20_929" x="9.96094" y="73.4375" width="65.625" height="19.3359" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.78125"/>
<feGaussianBlur stdDeviation="1.78711"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_20_929"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.78125"/>
<feGaussianBlur stdDeviation="0.390625"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_20_929" result="effect2_innerShadow_20_929"/>
</filter>
<filter id="filter2_ii_20_929" x="50.7812" y="16.8875" width="39.0625" height="36.6398" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.88253"/>
<feGaussianBlur stdDeviation="1.10599"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.865186 0 0 0 0 1 0 0 0 0 0.910124 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_20_929"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.941265"/>
<feGaussianBlur stdDeviation="0.929499"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0731977 0 0 0 0 0.472998 0 0 0 0 0.419691 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_20_929" result="effect2_innerShadow_20_929"/>
</filter>
<filter id="filter3_dii_20_929" x="57.8683" y="25.865" width="24.3025" height="18.4012" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="0.502232"/>
<feGaussianBlur stdDeviation="0.251116"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0307604 0 0 0 0 0.266862 0 0 0 0 0.168486 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_20_929"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_20_929" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1.00446"/>
<feGaussianBlur stdDeviation="0.502232"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect2_innerShadow_20_929"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-0.502232"/>
<feGaussianBlur stdDeviation="0.251116"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0350287 0 0 0 0 0.227718 0 0 0 0 0.181779 0 0 0 0.2 0"/>
<feBlend mode="normal" in2="effect2_innerShadow_20_929" result="effect3_innerShadow_20_929"/>
</filter>
<linearGradient id="paint0_linear_20_929" x1="54.1016" y1="12.1094" x2="54.1016" y2="91.9922" gradientUnits="userSpaceOnUse">
<stop stop-color="#ECEEF0"/>
<stop offset="0.045" stop-color="#F9FAFB"/>
<stop offset="0.20906" stop-color="#E8EBED"/>
<stop offset="0.63" stop-color="#EAEEF1"/>
<stop offset="0.915" stop-color="#E9EDF0"/>
<stop offset="1" stop-color="#F8FAFB"/>
</linearGradient>
<linearGradient id="paint1_linear_20_929" x1="87.6953" y1="12.1094" x2="87.6953" y2="23.8281" gradientUnits="userSpaceOnUse">
<stop stop-color="#D4D9DD"/>
<stop offset="1" stop-color="#B9BDC8"/>
</linearGradient>
<linearGradient id="paint2_linear_20_929" x1="42.7734" y1="74.2188" x2="42.7734" y2="91.9922" gradientUnits="userSpaceOnUse">
<stop stop-color="#DBE2E8"/>
<stop offset="0.865" stop-color="#A5ACBA"/>
<stop offset="1" stop-color="#B9BDC8"/>
</linearGradient>
<radialGradient id="paint3_radial_20_929" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(70.3125 24.5353) rotate(90) scale(22.5904 28.5783)">
<stop stop-color="#3FD6A4"/>
<stop offset="1" stop-color="#39D1B5"/>
</radialGradient>
</defs>
</svg>
