(function () {
    console.log("bdh bridge load start check");
    if (window._hbf){
        console.log("bdh bridge already loaded so return");
        return;
    } 
    console.log("bdh bridge load start ");
    var bridge = {
        call: function (method, args, cb) {
            var ret = '';
            if (typeof args == 'function') {
                cb = args;
                args = {};
            }
            var arg={data:args===undefined?null:args}
            if (typeof cb == 'function') {
                var cbName = 'hbcb' + window.hbcb++;
            
                window[cbName] = cb;
                arg['_hbcbstub'] = cbName;
            }
            arg = JSON.stringify(arg)
    
            //if in webview that hbBridge provided, call!
            if(window._hbbridge){
               ret=  _hbbridge.call(method, arg)
            }else if(window._hbwk || navigator.userAgent.indexOf("_hbbridge")!=-1){
               ret = prompt("_hbbridge=" + method, arg);
            }
    
           return  JSON.parse(ret||'{}').data
        },
        register: function (name, fun, asyn) {
            var q = asyn ? window._hbaf : window._hbf
            if (!window._hbInit) {
                window._hbInit = true;
                //notify native that js apis register successfully on next event loop
                setTimeout(function () {
                    bridge.call("_hbb.init");
                }, 0)
            }
            if (typeof fun == "object") {
                q._obs[name] = fun;
            } else {
                q[name] = fun
            }
        },
        registerAsyn: function (name, fun) {
            this.register(name, fun, true);
        },
        hasNativeMethod: function (name, type) {
            return this.call("_hbb.hasNativeMethod", {name: name, type:type||"all"});
        },
        disableJavascriptDialogBlock: function (disable) {
            this.call("_hbb.disableJavascriptDialogBlock", {
                disable: disable !== false
            })
        }
    };
    var _close=window.close;
    var ob = {
        //保存JS同步方法
        _hbf: {
            _obs: {}
        },
        //保存JS异步方法
        _hbaf: {
            _obs: {}
        },
        hbcb: 0,
        bdhBridge: bridge,
        close: function () {
            if(bridge.hasNativeMethod('_hbb.closePage')){
             bridge.call("_hbb.closePage")
            }else{
             _close.call(window)
            }
        },
        _handleMessageFromNative: function (info) {
            var arg = JSON.parse(info.data);
            var ret = {
                id: info.callbackId,
                complete: true
            }
            var f = this._hbf[info.method];
            var af = this._hbaf[info.method]
            var callSyn = function (f, ob) {
                ret.data = f.apply(ob, arg)
                bridge.call("_hbb.returnValue", ret)
            }
            var callAsyn = function (f, ob) {
                arg.push(function (data, complete) {
                    ret.data = data;
                    ret.complete = complete!==false;
                    bridge.call("_hbb.returnValue", ret)
                })
                f.apply(ob, arg)
            }
            if (f) {
                callSyn(f, this._hbf);
            } else if (af) {
                callAsyn(af, this._hbaf);
            } else {
                //with namespace
                var name = info.method.split('.');
                if (name.length<2) return;
                var method=name.pop();
                var namespace=name.join('.')
                var obs = this._hbf._obs;
                var ob = obs[namespace] || {};
                var m = ob[method];
                if (m && typeof m == "function") {
                    callSyn(m, ob);
                    return;
                }
                obs = this._hbaf._obs;
                ob = obs[namespace] || {};
                m = ob[method];
                if (m && typeof m == "function") {
                    callAsyn(m, ob);
                    return;
                }
            }
        }
    }

    for (var attr in ob) {
        window[attr] = ob[attr]
    }
   
    bridge.register("_hasJavascriptMethod", function (method, tag) {
        var name = method.split('.')
        if(name.length < 2) {
          return !!(_hbf[name]||_hbaf[name])
        }else{
          var method=name.pop()
          var namespace=name.join('.')
          var ob=_hbf._obs[namespace]||_hbaf._obs[namespace]
          return ob&&!!ob[method]
        }
    })

    console.log("bdh bridge load success");
})()