<!DOCTYPE html>
<html>
<head lang="zh-cmn-Hans">
    <meta charset="UTF-8">
    <title>HdbBridge Test</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=Edge,chrome=1">
    <meta name="viewport" content="width=device-width,initial-scale=0.5,user-scalable=no"/>
    <!--
    页面加载完成后会自动注入该 js 文件,不需要服务器再进行注入
    <script src="./bdh_js_bridge.js"> </script>
    -->
</head>
<style>
    .btn {
        text-align: center;
        background: #d8d8d8;
        color: #222;
        padding: 20px;
        margin: 30px;
        font-size: 24px;
        border-radius: 4px;
        box-shadow: 4px 2px 10px #999;
    }

    .btn:active {
        opacity: .7;
        box-shadow: 4px 2px 10px #555;
    }

</style>
<body>
<div class="btn" onclick="callSyn()">Synchronous call</div>
<div class="btn" onclick="callAsyn()">Asynchronous call</div>
<div class="btn" onclick="callNoArgSyn()">Sync call without argument</div>
<div class="btn" onclick="callNoArgAsyn()">Async call without argument</div>
<div class="btn" onclick="echoSyn()">echo.syn</div>
<div class="btn" onclick="echoAsyn()">echo.asyn</div>
<div class="btn" onclick="callAsyn_()">Stress test，2K times consecutive asynchronous API calls</div>
<!--<div class="btn" onclick="callNever()">Never call because without @JavascriptInterface annotation<br/>( This test is-->
<!--    just for Android ,should be ignored in IOS )-->
<!--</div>-->
<div class="btn" onclick="hasNativeMethod('xx')">hasNativeMethod("xx")</div>
<div class="btn" onclick="hasNativeMethod('testSyn')">hasNativeMethod("testSyn")</div>
<div class="btn" onclick="logout()">logout</div>
<div class="btn" onclick="userInfo()">userInfo</div>

<script>

    function callSyn() {
        alert(bdhBridge.call("testSyn", "testSyn"))
    }

    function callAsyn() {
        bdhBridge.call("testAsyn","testAsyn", function (v) {
            alert(v)
        })
    }

    function callAsyn_() {
        for (var i = 0; i < 2000; i++) {
            bdhBridge.call("testAsyn", "js+" + i, function (v) {
                if (v == "js+1999 [ asyn call]") {
                    alert("All tasks completed!")
                }
            })
        }
    }

    function callNoArgSyn() {
        alert(bdhBridge.call("testNoArgSyn"));
    }

    function callNoArgAsyn() {
        bdhBridge.call("testNoArgAsyn", function (v) {
            alert(v)
        });
    }

    function callNever() {
        alert(bdhBridge.call("testNever", {msg: "testSyn"}))
    }

    //带参数同步调用
    function echoSyn() {
        var ret=bdhBridge.call("echo.syn",{msg:" I am echoSyn call", tag:1});
        alert(JSON.stringify(ret))
    }

    //带参数异步调用
    function echoAsyn() {
        bdhBridge.call("echo.asyn",{msg:" I am echoAsyn call",tag:2},function (ret) {
            alert(JSON.stringify(ret));
        })
    }

    //退出登录
    function logout() {
        bdhBridge.call("logout")
    }

    //获取登录用户的信息，这么用有安全隐患,无法避免中间人攻击,后续应该考虑更加稳妥的获取Token的方法
    function userInfo() {
        var ret=bdhBridge.call("userInfo");
        alert(JSON.stringify(ret))
    }

    //判断本地接口是否存在
    function hasNativeMethod(name) {
        alert(bdhBridge.hasNativeMethod(name))
    }

</script>
</body>
</html>
