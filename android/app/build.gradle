plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file("local.properties")
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader("UTF-8") { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty("flutter.versionCode")
if (flutterVersionCode == null) {
    flutterVersionCode = "1"
}

def flutterVersionName = localProperties.getProperty("flutter.versionName")
if (flutterVersionName == null) {
    flutterVersionName = "1.0"
}

def mfph = [
        //包名
        "apk.applicationId" : "uni.UNI60F4B81",
        UMENG_APPKEY: '6826f9babc47b67d83683f42',
        UMENG_MESSAGE_SECRET: 'd53d247a62802dee21b975585c89fcd3',
        UMENG_CHANNEL: 'prod',
        VIVO_APP_ID: '105632330',
        VIVO_APP_KEY: '18fc0e4afa4f8f95e1721adbee27599e',
        HUAWEI_APP_ID: '105970633',
        HONOR_APP_ID: '104436158',
        XIAOMI_APP_ID: '2882303761520049474',
        XIAOMI_APP_KEY: '5382004929474',
        OPPO_APP_KEY: '57593748d4ef41faa8e6620efca10985',
        OPPO_APP_SECRET: '723e072781284ba9aab6da1fa2da95ec',
        MEIZU_APP_ID: '您申请的魅族通道appid',
        MEIZU_APP_KEY: '您申请的魅族通道appkey',
]

android {
    namespace = "com.example.bdh_smart_agric_app"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion
    compileSdk = 34

    packagingOptions {
        // Fixes duplicate libraries build issue,
        // when your project uses more than one plugin that depend on C++ libs.
        pickFirst 'lib/**/libc++_shared.so'
    }
    //此处配置必须添加 否则无法正确运行
    aaptOptions {
        additionalParameters '--auto-add-overlay'
        //noCompress 'foo', 'bar'
        ignoreAssetsPattern "!.svn:!.git:.*:!CVS:!thumbs.db:!picasa.ini:!*.scc:*~"
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "uni.UNI60F4B81"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdk = 24
        //noinspection ExpiredTargetSdkVersion
        targetSdk = 30
        versionCode = flutterVersionCode.toInteger()
        versionName = flutterVersionName
        multiDexEnabled true
        manifestPlaceholders = mfph
        ndk {
            abiFilters 'armeabi-v7a','arm64-v8a' //不支持armeabi
        }

    }
    signingConfigs {
       config {
           keyAlias '__uni__d302f11'
           keyPassword 'gaC6gJMz'
           storeFile file('../digization.keystore')
           storePassword 'gaC6gJMz'
           v1SigningEnabled true
           v2SigningEnabled true
       }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.config
            zipAlignEnabled true
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard.cfg'
            applicationVariants
                    .all{
                        variant ->
                            variant.outputs.all{
                                def createTime = new Date().format("YYYY-MM-dd", TimeZone.getTimeZone("GMT+08:00"))
                                // outputFileName = "${variant.productFlavors[0].name}" + defaultConfig.versionName + "_" + createTime + "_" + buildType.name + ".apk"
                                outputFileName = "bdh_digization_" + defaultConfig.versionName + "_" + createTime + "_" + buildType.name + ".apk"

                            }
                    }
        }
        debug {
            signingConfig signingConfigs.config
            zipAlignEnabled true
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard.cfg'
        }

        profile {
            signingConfig signingConfigs.config
            zipAlignEnabled true
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard.cfg'
        }

        packagingOptions {
            //解决 so 冲突
            // 2 files found with path 'lib/arm64-v8a/libc++_shared.so' from inputs:
            // jetified-uniapp-v8-release/jni/arm64-v8a/libc++_shared.so
            // jetified-TIWLogger-********/jni/arm64-v8a/libc++_shared.so
            pickFirst 'lib/arm64-v8a/libc++_shared.so'
            pickFirst 'lib/armeabi-v7a/libc++_shared.so'
            pickFirst 'lib/x86/libc++_shared.so'
            pickFirst 'lib/x86_64/libc++_shared.so'


        }
    }

}
repositories {
    flatDir {
        dirs 'libs'
    }
}
dependencies {
    implementation 'com.amap.api:3dmap:9.5.0'
    implementation 'com.amap.api:search:9.4.5'

    //必须添加的依赖
    implementation "androidx.recyclerview:recyclerview:1.1.0"
    implementation "androidx.legacy:legacy-support-v4:1.0.0"
    implementation "androidx.appcompat:appcompat:1.1.0"
    implementation 'com.alibaba:fastjson:1.2.83'
    implementation 'com.facebook.fresco:fresco:2.5.0'
    implementation 'com.facebook.fresco:animated-gif:2.5.0'
    implementation 'com.facebook.fresco:webpsupport:2.5.0'
    implementation 'com.facebook.fresco:animated-webp:2.5.0'
    implementation 'com.github.bumptech.glide:glide:4.9.0'
    implementation 'com.android.support:multidex:1.0.3'
    implementation 'com.squareup.okhttp3:okhttp:3.7.0'
    implementation 'com.squareup.okio:okio:1.8.0'
    // add since 2022-12-29
    implementation 'androidx.webkit:webkit:1.3.0'
    // add since 2023-03-30 微信支付/分享  需要新增配置
    implementation 'com.tencent.mm.opensdk:wechat-sdk-android:6.8.0'

    implementation 'androidx.core:core-ktx:1.10.0'

    implementation "androidx.cardview:cardview:1.0.0"
    implementation 'com.google.android.material:material:1.12.0'
    implementation 'androidx.activity:activity:1.8.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation("org.greenrobot:eventbus:3.3.1")
    implementation("androidx.navigation:navigation-fragment:2.5.3")
    implementation("androidx.navigation:navigation-ui:2.5.3")
    implementation("com.android.support:appcompat-v7:26.0.2")
    implementation 'com.tencent.bugly:crashreport:latest.release'


    //腾讯课堂相关
    //implementation 'com.tencent.edu:TCICSDK:1.8.12'

    //导入SDK相关依赖jar、aar
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation fileTree(include: ['*.aar'], dir: 'libs')

    implementation 'com.google.mlkit:barcode-scanning:17.2.0'
}



flutter {
    source = "../.."
}
