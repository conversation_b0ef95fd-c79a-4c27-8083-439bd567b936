<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
<!--    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES" />-->
    <queries>
        <package android:name="com.alibaba.android.rimet.chinabdh"/>
    </queries>

    <queries>
        <intent>
            <action android:name="android.intent.action.TTS_SERVICE" />
        </intent>
    </queries>

    <uses-feature
        android:name="android.hardware.camera"
        android:required="false" />

    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
    <uses-permission android:name="android.permission.READ_USER_DICTIONARY"/>
    <uses-permission android:name="android.permission.READ_MEDIA_VISUAL_USER_SELECTED" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_MEDIA_LOCATION" />
    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>

    <uses-permission
        android:name="android.permission.WRITE_SETTINGS"
        tools:ignore="ProtectedPermissions" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />


    <!--
 Required to query activities that can process text, see:
         https://developer.android.com/training/package-visibility and
         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
    -->
    <queries>
        <intent>
            <action android:name="android.intent.action.PROCESS_TEXT" />

            <data android:mimeType="text/plain" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />

            <data android:scheme="tel" />
        </intent>
    </queries>

    <application
        android:name=".BDHApp"
        android:icon="@mipmap/ic_launcher"
        android:label="数字北大荒"
        android:usesCleartextTraffic="true"
        android:directBootAware="true"
        android:defaultToDeviceProtectedStorage="true"
        android:extractNativeLibs="true"
        tools:targetApi="m">
          <!-- 添加以下provider配置 -->
        <provider
            android:name="vn.hunghd.flutterdownloader.DownloadedFileProvider"
            android:authorities="${applicationId}.flutter_downloader.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths" />
        </provider>
        <activity
            android:name=".H5Activity"
            android:exported="false" />
        <activity
            android:name="com.icbc.ndf.jft.icbcPay.PayResultHandler"
            android:exported="true"
            android:launchMode="singleTask"
            android:theme="@android:style/Theme.NoDisplay">
            <intent-filter>
                <category android:name="android.intent.category.DEFAULT"></category>
                <action android:name="com.icbc.pay.PayResultHandler.SHOW_ACTIVITY"></action>
            </intent-filter>
        </activity>
        <activity-alias
            android:name=".icbcPay.PayResultHandler"
            android:exported="true"
            android:targetActivity="com.icbc.ndf.jft.icbcPay.PayResultHandler" />
        <activity
            android:name=".MainActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:excludeFromRecents="false"
            android:exported="true"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:taskAffinity=""
            android:theme="@style/LaunchTheme"
            android:windowSoftInputMode="adjustResize"
            android:resizeableActivity="true">
            <!--
                 屏蔽 flutter 自带的 deeplink 功能，用第三方 applink 实现
            -->
            <meta-data android:name="flutter_deeplinking_enabled" android:value="false" />

            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <!-- Add optional android:host to distinguish your app
                    from others in case of conflicting scheme name -->
                <data android:scheme="bdh" android:host="app.bdhic.com" />
                <!-- <data android:scheme="sample" /> -->
            </intent-filter>

            <!--
                 Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI.
            -->
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/splash" />




            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        
<!--        <activity-->
<!--            android:name="com.example.bdh_smart_agric_app.MfrMessageActivity"-->
<!--            android:exported="true"-->
<!--            android:launchMode="singleTask">-->
<!--            <intent-filter>-->
<!--                <action android:name="android.intent.action.VIEW" />-->
<!--                <category android:name="android.intent.category.DEFAULT" />-->
<!--                <category android:name="android.intent.category.BROWSABLE" />-->
<!--                <data-->
<!--                    android:host="${applicationId}"-->
<!--                    android:path="/thirdpush"-->
<!--                    android:scheme="agoo" />-->
<!--            </intent-filter>-->
<!--        </activity>-->

        

        <provider
            android:name="io.dcloud.common.util.DCloud_FileProvider"
            android:authorities="${apk.applicationId}.dc.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/dcloud_file_provider" />
        </provider>

        <service android:name="com.amap.api.location.APSService" />

        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="037b9f222bb729e496fdcfbb63ca68b9"/>
        <!--VIVO厂商通道-->
<!--        <meta-data android:name="com.vivo.push.api_key" android:value="18fc0e4afa4f8f95e1721adbee27599e"/>-->
<!--        <meta-data android:name="com.vivo.push.app_id" android:value="105632330"/>-->
        <!--华为厂商通道-->
<!--        <meta-data android:name="com.huawei.hms.client.appid" android:value="appid=104658093"/>-->
        <!--荣耀厂商通道-->
<!--        <meta-data android:name="com.hihonor.push.app_id" android:value="104436158" />-->

        <!--
 Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
        -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

<!-- 下方为添加tcic依赖后需要修改的，现在还原-->
<!--        <meta-data-->
<!--            android:name="android.max_aspect"-->
<!--            android:value="2.4"-->
<!--            tools:replace="android:value"/>-->

<!--        <service-->
<!--            android:name="com.tencent.smtt.export.external.DexClassLoaderProviderService"-->
<!--            android:exported="false"-->
<!--            android:label="dexopt"-->
<!--            android:process=":dexopt"-->
<!--            tools:replace="android:exported"/>-->

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileProvider"
            android:exported="false"
            android:grantUriPermissions="true"
            tools:replace="android:authorities">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/filepaths"
                tools:replace="android:resource" />
        </provider>
    </application>

</manifest>
