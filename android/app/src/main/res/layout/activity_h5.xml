<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <RelativeLayout
        android:id="@+id/main_title_bar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="#ffffff">

        <LinearLayout
            android:id="@+id/main_left_button"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignParentLeft="true"
            android:paddingRight="20dp"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:src="@mipmap/return_back" />

        </LinearLayout>

    </RelativeLayout>

    <WebView
        android:id="@+id/main_webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/main_title_bar" />



</LinearLayout>