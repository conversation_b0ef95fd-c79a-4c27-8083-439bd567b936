<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Theme applied to the Android Window while the process is starting when the OS's Dark Mode setting is off -->
    <style name="LaunchTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!-- Show a splash screen on the activity. Automatically removed when
             the Flutter engine draws its first frame -->
<!--        <item name="android:windowTranslucentStatus">true</item>-->
        <item name="android:windowBackground">@drawable/launch_background</item>
        <!--不让windowBackground延申到navigation bar区域-->
<!--        <item name="android:windowDrawsSystemBarBackgrounds">false</item>-->
        <!--适配Android P刘海屏-->
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
        <!--全屏即无通知栏-->
        <item name="android:windowFullscreen">true</item>
    </style>
    <!-- Theme applied to the Android Window as soon as the process has started.
         This theme determines the color of the Android Window while your
         Flutter UI initializes, as well as behind your Flutter UI while its
         running.

         This Theme is only used starting with V2 of Flutter's Android embedding. -->
    <style name="NormalTheme" parent="@android:style/Theme.Light.NoTitleBar">
        <!--<item name="android:windowBackground">?android:colorBackground</item>-->
        <!--解决启动页(闪屏页)状态栏颜色问题-->
<!--        <item name="android:windowTranslucentStatus">true</item>-->
        <item name="android:windowBackground">@drawable/splash</item>
        <item name="android:windowLayoutInDisplayCutoutMode">shortEdges</item>
    </style>
</resources>
