package com.example.bdh_smart_agric_app.sdk;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;



import io.flutter.plugin.common.MethodChannel;

public class TencentTcicSDK {

    private static final String TAG = "TencentTcicSDK";

    public static void initX5Core(String licenseKey, MethodChannel.Result result){
        Log.d(TAG,"initX5Core start "+licenseKey);
        result.error("INIT_FAILED", "X5 kernel not support ", null);
//        TCICManager.getInstance().initX5Core(licenseKey, new TBSSdkManageCallback() {
//            @Override
//            public void onCoreInitFinished() {
//                Log.d(TAG,"initX5Core onCoreInitFinished");
//            }
//
//            @Override
//            public void onViewInitFinished(boolean isX5Core) {
//                Log.d(TAG,"initX5Core onViewInitFinished isX5Core:"+isX5Core);
//                if (isX5Core) {
//                    result.success("INIT_SUCCEED"); // X5 内核初始化成功
//                } else {
//                    result.error("INIT_FAILED", "X5 kernel initialization failed", null);
//                }
//            }
//        });
    }


    public static void joinClass(Activity activity, int schoolId, int classId, String userId, String token, MethodChannel.Result result){
        result.error("INIT_FAILED", "X5 kernel not support ", null);
//        Intent intent = new Intent(activity, TCICClassActivity.class);
//        Bundle bundle = new Bundle();
//        TCICClassConfig initConfig = new TCICClassConfig.Builder()
//                .schoolId(schoolId)
//                .classId(classId)
//                .userId(userId)
//                .token(token)
//                .preferPortrait(true)
//                .build();
//        bundle.putParcelable(TCICConstants.KEY_INIT_CONFIG, initConfig);
//        intent.putExtras(bundle);
//        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP);
//        activity.startActivity(intent);
//        result.success("success");
    }
}
