package com.example.bdh_smart_agric_app.sdk;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.hardware.usb.UsbManager;
import android.os.BatteryManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import com.alibaba.fastjson.JSONObject;
import com.amap.api.location.AMapLocation;
import com.amap.api.location.AMapLocationClient;
import com.amap.api.location.AMapLocationClientOption;
import com.amap.api.location.AMapLocationListener;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


//高德虚拟定位检测
public class AmapLocationManager implements AMapLocationListener {
    private static final String TAG = "AmapLocationManager";

    private static String[] permissions = new String[]{
            //通过GPS得到精确位置
            Manifest.permission.ACCESS_FINE_LOCATION,
            //通过网络得到粗略位置
            Manifest.permission.ACCESS_COARSE_LOCATION,
            //访问网络，某些位置信息需要从网络服务器获取
            Manifest.permission.INTERNET,
            //访问WiFi状态，需要WiFi信息用于网络定位
            Manifest.permission.ACCESS_WIFI_STATE,
            //修改WiFi状态，发起WiFi扫描, 需要WiFi信息用于网络定位
            Manifest.permission.CHANGE_WIFI_STATE,
            //用于获取运营商信息，用于支持提供运营商信息相关的接口
            Manifest.permission.ACCESS_NETWORK_STATE,
            //访问网络的变化, 需要某些信息用于网络定位
            Manifest.permission.CHANGE_NETWORK_STATE,
            //用于读取手机当前的状态
            Manifest.permission.READ_PHONE_STATE,
            //用于写入缓存数据到扩展存储卡
            Manifest.permission.WRITE_EXTERNAL_STORAGE,
            //蓝牙扫描权限
            Manifest.permission.BLUETOOTH,
            Manifest.permission.BLUETOOTH_ADMIN,
            //前台service权限
            Manifest.permission.FOREGROUND_SERVICE,
            //后台定位权限
//            Manifest.permission.ACCESS_BACKGROUND_LOCATION,
            //A-GPS辅助定位权限，方便GPS快速准确定位
            Manifest.permission.ACCESS_LOCATION_EXTRA_COMMANDS,
    };

    private final int REQUEST_CODE = 997;

    @SuppressLint("StaticFieldLeak")
    private static final AmapLocationManager instance = new AmapLocationManager();

    public static AmapLocationManager getInstance() {
        return instance;
    }

    private Activity mContext;
    private HandlerThread mHandlerThread;
    private Handler mHandler;
    private boolean inited = false;
    private LocationListener mLocationListener;
    private AMapLocationClient mLocationClient = null;
    private JSONObject _options;
    final List<AMapLocation> locationList = new ArrayList<>();
    boolean checkStarted = false;

    public synchronized void init(Activity context,LocationListener locationListener) {
        synchronized (this){
            if(inited){
                return;
            }
            mContext = context;
            mHandlerThread = new HandlerThread(TAG);
            mHandlerThread.start();
            mHandler = new EventHandler(mHandlerThread.getLooper());
            mLocationListener = locationListener;

            IntentFilter intentFilter = new IntentFilter();
            intentFilter.addAction(UsbManager.ACTION_USB_DEVICE_ATTACHED);
            intentFilter.addAction(UsbManager.ACTION_USB_DEVICE_DETACHED);
            intentFilter.addAction(Intent.ACTION_BATTERY_CHANGED);
            mContext.registerReceiver(mUsbBoardCastReceiver,intentFilter);
            inited = true;
        }

    }

    public synchronized void quit(){
        synchronized (this){
            mContext.unregisterReceiver(mUsbBoardCastReceiver);
            mHandlerThread.quit();
            mHandlerThread = null;
            mHandler = null;
            inited = false;
        }
    }

    public Context getContext() {
        return mContext;
    }

    public Handler getHandler() {
        return mHandler;
    }

    public HandlerThread getHandlerThread() {
        return mHandlerThread;
    }

    private static final int MSG_START = 0;
    private static final int MSG_STOP = 1;
    private static final int MSG_CHANGED = 2;
    private static final int MSG_CHECK_DELAY=3;
    private volatile boolean usbAttached = false;

    protected  void onHandleMessage(Message msg){
        switch (msg.what){
            case MSG_START:
                internalStart((JSONObject)msg.obj);
                return;
            case MSG_STOP:
                internalStop((JSONObject)msg.obj);
                return;
            case MSG_CHANGED:
               // internalChanged((AMapLocation)msg.obj);
                return;
            case MSG_CHECK_DELAY:
                internalCheck();
                return;
        }
    }


    public void start(String apiKey,Integer locateInterval) {
        JSONObject options = new JSONObject();
        options.put("apiKey",apiKey);
        options.put("locateInterval",locateInterval);
        start(options);
    }

    public void start(JSONObject options) {
        if (ActivityCompat.checkSelfPermission(mContext, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
            Message.obtain(getHandler(),MSG_START,options).sendToTarget();
        } else {
            _options = options;
            requestPermissions();
        }
    }

    private void startCheckDelay(){
        stopCheckDelay();
        //30s后检查
        //Log.d(TAG,"startCheckDelay");
        getHandler().sendEmptyMessageDelayed(MSG_CHECK_DELAY,30*1000);
    }

    private void stopCheckDelay(){
        //删除延时检查
        //Log.d(TAG,"stopCheckDelay");
        getHandler().removeMessages(MSG_CHECK_DELAY);
    }

    private void internalStart(JSONObject options){
        //检查是否是沙箱环境 //检查是否是插usb
        if(checkInSandbox() || checkUsbAttached()){
            return;
        }
        //初始化定位 sdk
        initLocationEnv(options);
        //开启定位
        startGetLocate();
        //开启延时检查 30s
        startCheckDelay();
        checkStarted = true;
    }

    final BroadcastReceiver mUsbBoardCastReceiver = new  BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if(UsbManager.ACTION_USB_DEVICE_ATTACHED.equals(intent.getAction())){
                usbAttached = true;
                //Log.d(TAG,"usbAttached");
            }else if(UsbManager.ACTION_USB_DEVICE_DETACHED.equals(intent.getAction())){
                //Log.d(TAG,"usbDetached");
                usbAttached = false;
            }else if(Intent.ACTION_BATTERY_CHANGED.equals(intent.getAction())){
                int chargePlug = intent.getIntExtra(BatteryManager.EXTRA_PLUGGED,-1);
                usbAttached = chargePlug == BatteryManager.BATTERY_PLUGGED_USB;
                //Log.d(TAG,"ACTION_BATTERY_CHANGED "+usbAttached);
            }
        }
    };

    private boolean checkInSandbox(){
        ApplicationInfo appInfo = mContext.getApplicationContext().getApplicationInfo();
        String dataDir = mContext.getDataDir().getAbsolutePath();
        if(!dataDir.equals("/data/user/0/"+appInfo.packageName)) {
            JSONObject result = new JSONObject();
            result.put("type", "StopLocation");
            result.put("result", "1");
            result.put("msg", "疑似沙箱环境");
            result.put("code", -6);
            //Log.d(TAG,"internalStart  result :"+result.toJSONString());
            Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
            return true;
        }
        return false;
    }

    private boolean checkUsbAttached(){
        if(usbAttached) {
            JSONObject result = new JSONObject();
            result.put("type", "StopLocation");
            result.put("result", "1");
            result.put("msg", "连接usb");
            result.put("code", -8);
            //Log.d(TAG,"internalStart  result :"+result.toJSONString());
            Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
            return true;
        }
        return false;
    }

    private void internalStop(JSONObject jsonObject){
        //Log.d(TAG,"internalStop result: "+jsonObject.toJSONString());
        stopCheckDelay();
        locationList.clear();
        if(mLocationClient != null){
            mLocationClient.stopLocation();
            mLocationClient.onDestroy();
            mLocationClient = null;
        }
        checkStarted = false;
        _notifyChanged(jsonObject);
    }


    private void internalChanged(AMapLocation loc){
        JSONObject result = new JSONObject();
        result.put("type", "LocationChanged");
        result.put("result", "1");
        result.put("code", 0);
        AMapLocation last = locationList.get(locationList.size() - 1);
        JSONObject data = getLocationJsonObject(last);
        result.put("data", data);
        _notifyChanged(result);
    }

    private void internalCheck(){
        //Log.d(TAG,"internalCheck start");
      //定位数据为空
      if(locationList.isEmpty()){
          JSONObject result = new JSONObject();
          result.put("type", "StopLocation");
          result.put("result", "1");
          result.put("msg", "定位数据为空");
          result.put("code", -3);
          Log.d(TAG,"internalCheck locationList.isEmpty() so return");
          Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
          return;
      }else if(locationList.size() <=5){
          JSONObject result = new JSONObject();
          result.put("type", "StopLocation");
          result.put("result", "1");
          result.put("msg", "定位数据少于5条");
          result.put("code", -3);
//          AMapLocation last = locationList.get(locationList.size() - 1);
//          JSONObject data = getLocationJsonObject(last);
//          result.put("data", data);
          //Log.d(TAG,"internalCheck locationList.isEmpty() so return");
          Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
          return;
      }
      double maxLong = locationList.get(0).getLongitude();
      double minLong = locationList.get(0).getLongitude();
      double maxLa = locationList.get(0).getLatitude();
      double minLa = locationList.get(0).getLatitude();
      AMapLocation loc;
      for(int i=0;i<locationList.size();i++){
          loc = locationList.get(i);
          maxLong = Math.max(maxLong,loc.getLongitude());
          maxLa = Math.max(maxLa,loc.getLatitude());
          minLong = Math.min(minLong,loc.getLongitude());
          minLa = Math.min(minLa,loc.getLatitude());
      }
      //定位幅度过大
      if(maxLong - minLong > 1
                || maxLa - minLa > 0.5 ) {
          JSONObject result = new JSONObject();
          result.put("type", "StopLocation");
          result.put("result", "1");
          result.put("msg", "定位幅度过大");
          result.put("code", -4);
//          AMapLocation last = locationList.get(locationList.size() - 1);
//          JSONObject data = getLocationJsonObject(last);
//          result.put("data", data);
          //Log.d(TAG,"internalCheck 定位幅度过大 so return "+maxLong+" "+maxLa+" "+minLong+" "+minLa+"  "+(maxLong - minLong)+" "+(maxLa - minLa));
          Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
      }
      //定位无变化
      else if(maxLong - minLong == 0
                && maxLa - minLa == 0 ) {
          JSONObject result = new JSONObject();
          result.put("type", "StopLocation");
          result.put("result", "2");
          result.put("msg", "定位无变化");
          result.put("code", -5);
//          AMapLocation last = locationList.get(locationList.size() - 1);
//          JSONObject data = getLocationJsonObject(last);
//          result.put("data", data);
          //Log.d(TAG,"internalCheck 定位无变化 so return");
          Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
      }else{
          JSONObject result = new JSONObject();
          result.put("type", "StopLocation");
          result.put("result", "0");
          result.put("code", 0);

//          AMapLocation last = locationList.get(locationList.size() - 1);
//          JSONObject data = getLocationJsonObject(last);
//          result.put("data", data);
          //Log.d(TAG,"定位正常");
          Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
      }
    }

    public JSONObject getLocationJsonObject(AMapLocation aMapLocation) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("latitude", aMapLocation.getLatitude()); //获取纬度信息
        jsonObject.put("longitude", aMapLocation.getLongitude());//获取经度信息
        jsonObject.put("altitude", aMapLocation.hasAltitude() ? aMapLocation.getAltitude() : -999);//获取高程
        jsonObject.put("accuracy", aMapLocation.getAccuracy());//获取定位精度，默认值为0.0f
        jsonObject.put("bearing", aMapLocation.getBearing());//获取方向角(单位：度） 默认值：0.0, 取值范围：【0，360】，其中0度表示正北方向，90度表示正东，180度表示正南，270度表示正西
        jsonObject.put("conScenario", aMapLocation.getConScenario());//室内外置信度 室内：且置信度取值在[1 ～ 100]，值越大在在室内的可能性越大 室外：且置信度取值在[-100 ～ -1] ,值越小在在室内的可能性越大 无法识别室内外：置信度返回值为 0
        jsonObject.put("coordType", aMapLocation.getCoordType()); //获取坐标系类型 高德定位sdk会返回两种坐标系 AMapLocation.COORD_TYPE_GCJ02 -- GCJ02坐标系 AMapLocation.COORD_TYPE_WGS84 -- WGS84坐标系,国外定位时返回的是WGS84坐标系
        jsonObject.put("gpsAccuracyStatus", aMapLocation.getGpsAccuracyStatus()); //获取卫星信号强度，仅在卫星定位时有效,值为 #GPS_ACCURACY_BAD，#GPS_ACCURACY_GOOD，#GPS_ACCURACY_UNKNOWN
        jsonObject.put("locationType", aMapLocation.getLocationType()); //获取定位结果来源
        jsonObject.put("provider", aMapLocation.getProvider()); //获取定位提供者,lbs:高德网络定位,gps:卫星定位
        jsonObject.put("satellites", aMapLocation.getSatellites()); //获取当前可用卫星数量, 仅在卫星定位时有效,
        //获取定位结果的可信度 只有在定位成功时才有意义
        //非常可信 AMapLocation.TRUSTED_LEVEL_HIGH
        //可信度一般AMapLocation.TRUSTED_LEVEL_NORMAL
        //可信度较低 AMapLocation.TRUSTED_LEVEL_LOW
        //非常不可信 AMapLocation.TRUSTED_LEVEL_BAD
        jsonObject.put("trustedLevel", aMapLocation.getTrustedLevel()); //获取定位结果的可信度 只有在定位成功时才有意义,
        jsonObject.put("isMock", aMapLocation.isMock()); //是否修改定位
        jsonObject.put("isFixLastLocation", aMapLocation.isFixLastLocation()); //?
        jsonObject.put("isOffset", aMapLocation.isOffset()); //?
        jsonObject.put("isFromMockProvider", aMapLocation.isFromMockProvider()); //?

        if(aMapLocation.getLocationQualityReport()!=null) {
            JSONObject locationQualityReport = new JSONObject();
            locationQualityReport.put("adviseMessage", aMapLocation.getLocationQualityReport().getAdviseMessage()); //获取提示语义,状态良好时，返回的是内容为空 根据当前的质量报告，给出相应的建议
            locationQualityReport.put("GPSSatellites", aMapLocation.getLocationQualityReport().getGPSSatellites()); //获取当前的卫星数， 只有在非低功耗模式下此值才有效
            locationQualityReport.put("GPSStatus", aMapLocation.getLocationQualityReport().getGPSStatus()); //获取卫星状态信息，只有在非低功耗模式下此值才有效
            locationQualityReport.put("netUseTime", aMapLocation.getLocationQualityReport().getNetUseTime()); //获取网络定位时的网络耗时 单位：毫秒
            locationQualityReport.put("networkType", aMapLocation.getLocationQualityReport().getNetworkType()); //获取网络连接类型（2G、3G、4G、WIFI)
            locationQualityReport.put("isInstalledHighDangerMockApp", aMapLocation.getLocationQualityReport().isInstalledHighDangerMockApp()); //是否安装了高危位置模拟软件 首次定位可能没有结果
            locationQualityReport.put("isWifiAble", aMapLocation.getLocationQualityReport().isWifiAble()); //wifi开关是否打开 如果wifi关闭建议打开wifi开关，提高定位质量
            jsonObject.put("locationQualityReport", locationQualityReport);
        }

        if(aMapLocation.getExtras()!=null) {
            Bundle exinfo = aMapLocation.getExtras();
            JSONObject extraInfoObj = new JSONObject();
            for(String key : exinfo.keySet()) {
                extraInfoObj.put(key, exinfo.get(key));
            }
            jsonObject.put("extraInfo", extraInfoObj);
           // Log.d("AMapLocation=receive", extraInfoObj.toJSONString());
        }
        return jsonObject;
    }

    private void startGetLocate(){
        mLocationClient.stopLocation();
        mLocationClient.startLocation();
        //Log.d(TAG,"startGetLocate");
        //_notifyStartLocation();
    }

    private void initLocationEnv(JSONObject options){
        if(mContext == null){
            return;
        }

        if(mLocationClient != null){
            return;
        }

        try {
            String appKey = options.getString("appKey");
            int locateInterval = options.getIntValue("locateInterval");
            if(locateInterval == 0){
                locateInterval = 1000;
            }
            AMapLocationClient.updatePrivacyAgree(mContext,true);
            AMapLocationClient.updatePrivacyShow(mContext,true, true);
            if(appKey!=null)
                AMapLocationClient.setApiKey(appKey);


            if(mLocationClient != null){
                mLocationClient.stopLocation();
                mLocationClient.onDestroy();
                mLocationClient = null;
            }
            mLocationClient = new AMapLocationClient(mContext);
            //设置定位回调监听
            mLocationClient.setLocationListener(this);
            //初始化AMapLocationClientOption对象
            AMapLocationClientOption mLocationOption = new AMapLocationClientOption();
            //设置定位场景，目前支持三种场景（签到、出行、运动，默认无场景）
//          mLocationOption.setLocationPurpose(AMapLocationClientOption.AMapLocationPurpose.SignIn);
            //设置定位模式为AMapLocationMode.Hight_Accuracy，高精度模式。会同时使用网络定位和GPS定位，优先返回最高精度的定位结果，以及对应的地址描述信息。
            //AMapLocationMode.Battery_Saving，低功耗模式。不会使用GPS和其他传感器，只会使用网络定位（Wi-Fi和基站定位）；
            //AMapLocationMode.Device_Sensors，仅设备模式。不需要连接网络，只使用GPS进行定位，这种模式下不支持室内环境的定位，需要在室外环境下才可以成功定位。
            mLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
            //设置定位协议
            mLocationOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTPS);
            //设置定位场景，根据场景快速修改option，当不需要场景时，可以设置为NULL， 注意： 不建议设置场景和自定义option混合使用 设置场景后
//          mLocationOption.setLocationPurpose(AMapLocationClientOption.AMapLocationPurpose.SignIn);
            //获取一次定位结果：该方法默认为false。
//          mLocationOption.setOnceLocation(true);
            //设置定位是否等待WIFI列表刷新 定位精度会更高，但是定位速度会变慢1-3秒
//          mLocationOption.setOnceLocationLatest(true);
            //设置定位间隔,单位毫秒,默认为2000ms，最低1000ms。
            mLocationOption.setInterval(locateInterval);
            //设置是否返回地址信息（默认返回地址信息）
            mLocationOption.setNeedAddress(false);
            //设置是否开启wifi始终扫描 只有设置了android.permission.WRITE_SECURE_SETTINGS权限后才会开启 开启后，即使关闭wifi开关的情况下也会扫描wifi 默认值为：true, 开启wifi始终扫描 此方法为静态方法
            mLocationOption.setOpenAlwaysScanWifi(true);
            //设置是否使用设备传感器 默认值：false 不使用设备传感器
            mLocationOption.setSensorEnable(false);
            //isWifiActiveScan是布尔型参数，true表示会主动刷新设备wifi模块，获取到最新鲜的wifi列表（wifi新鲜程度决定定位精度）；false表示不主动刷新。
            mLocationOption.setWifiScan(true);
            //设置是否允许模拟位置,默认为true，允许模拟位置
            mLocationOption.setMockEnable(false);
            // 设置首次定位是否等待卫星定位结果
            mLocationOption.setGpsFirst(true);
            //置优先返回卫星定位信息时等待卫星定位结果的超时时间
            mLocationOption.setGpsFirstTimeout(3000);
            //单位是毫秒，默认30000毫秒，建议超时时间不要低于8000毫秒。
            mLocationOption.setHttpTimeOut(30000);
            //设置退出时是否杀死进程
            mLocationOption.setKillProcess(true);
            //启定位缓存功能，在高精度模式和低功耗模式下进行的网络定位结果均会生成本地缓存，不区分单次定位还是连续定位。GPS定位结果不会被缓存。
            mLocationOption.setLocationCacheEnable(true);
            //设置场景模式后最好调用一次stop，再调用start以保证场景模式生效
            mLocationClient.setLocationOption(mLocationOption);
            Log.d(TAG,"mLocationClient init success");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private void _notifyStartLocation(){
        LocationListener locationListener = mLocationListener;
        if(locationListener != null){
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("type","StartLocation");
            //jsonObject.put("success", true);
            //Log.d(TAG,"get _notifyStartLocation "+jsonObject.toJSONString());
            locationListener.onNotify(jsonObject);
        }
    }

    private void _notifyChanged(JSONObject data){
        LocationListener locationListener = mLocationListener;
        if(locationListener != null){
            //Log.d(TAG,"get _notifyChanged "+data.toJSONString());
            locationListener.onNotify(data);
        }
    }

    public void requestPermissions() {
        if (mContext != null) {
            mContext.requestPermissions(permissions,REQUEST_CODE);
        }
    }

    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        if (requestCode == REQUEST_CODE) {
            for (int i = 0; i < permissions.length; i++) {
                String preName = permissions[i];
                int granted = grantResults[i];
                if (Manifest.permission.ACCESS_FINE_LOCATION.equals(preName)) {
                    if(granted == PackageManager.PERMISSION_GRANTED){
                        start(_options);
                    }else{
                        JSONObject result = new JSONObject();
                        result.put("type", "StopLocation");
                        result.put("result", "-1");
                        result.put("code", -7);
                        result.put("msg", "权限不足");
                        _notifyChanged(result);
                    }

                }
            }
        }
    }

    @Override
    public void onLocationChanged(AMapLocation aMapLocation) {

        if(!checkStarted){
            //Log.d(TAG,"onLocationChanged !checkStarted so return");
            return;
        }
        if(aMapLocation.getErrorCode() == AMapLocation.LOCATION_SUCCESS) {
            //Log.d(TAG,"onLocationChanged aMapLocation.getErrorCode() == AMapLocation.LOCATION_SUCCESS");
            try {
                JSONObject result = new JSONObject();

                if(checkUsbAttached()){
                    return;
                }

                if(aMapLocation.isFromMockProvider()){
                    result.put("type", "StopLocation");
                    result.put("result", "1");
                    result.put("msg", "定位失败,疑似篡改定位信息E0");
                    result.put("code", -999);
//                    JSONObject data = getLocationJsonObject(aMapLocation);
//                    result.put("data", data);
                    Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
                    return;
                }

                if(aMapLocation.isMock()) {
                    result.put("type", "StopLocation");
                    result.put("result", "1");
                    result.put("msg", "定位失败,疑似篡改定位信息E1");
                    result.put("code", -999);
//                    JSONObject data = getLocationJsonObject(aMapLocation);
//                    result.put("data", data);
                    Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
                    return;
                }

                if(aMapLocation.getLocationQualityReport() != null
                        && aMapLocation.getLocationQualityReport().isInstalledHighDangerMockApp()){
                    result.put("type", "StopLocation");
                    result.put("result", "1");
                    result.put("msg", "检测到安装模拟定位软件");
                    result.put("code", -999);
//                    JSONObject data = getLocationJsonObject(aMapLocation);
//                    result.put("data", data);
                    Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
                    return;
                }

                if(aMapLocation.getLocationType() == AMapLocation.LOCATION_TYPE_GPS
                        && aMapLocation.hasAltitude()
                        && aMapLocation.getAltitude() == 0) {
                    result.put("type", "StopLocation");
                    result.put("result", "1");
                    result.put("msg", "定位失败,疑似篡改定位信息E2");
                    result.put("code", -999);
//                    JSONObject data = getLocationJsonObject(aMapLocation);
//                    result.put("data", data);
                    Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
                    return;
                }
                //Log.d(TAG,"get location "+aMapLocation.getLongitude()+" "+ aMapLocation.getLatitude());
                //定位成功
                locationList.add(aMapLocation);
                getHandler().removeMessages(MSG_CHANGED);
                Message.obtain(getHandler(),MSG_CHANGED,aMapLocation).sendToTarget();
            } catch (Exception e) {
                JSONObject result = new JSONObject();
                result.put("result","-1");
                result.put("code", -1);
                result.put("msg", e.getMessage());
                Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
            }
        } else {
            JSONObject result = new JSONObject();
            result.put("result", "-1");
            result.put("code", -2);
            result.put("msg", aMapLocation.getErrorInfo());
            //Log.d(TAG,"onLocationChanged aMapLocation.getErrorCode() != AMapLocation.LOCATION_SUCCESS");
            Message.obtain(getHandler(),MSG_STOP,result).sendToTarget();
        }
    }

    private class EventHandler extends Handler {
        public EventHandler(Looper looper) {
            super(looper);
        }

        @Override
        public void handleMessage(Message msg) {
            onHandleMessage(msg);
        }
    }

    public static interface LocationListener{
        public void onNotify(Map<String,Object> data);

    }
}
