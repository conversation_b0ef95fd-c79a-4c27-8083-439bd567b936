//package com.example.bdh_smart_agric_app.sdk;
//
//import android.Manifest;
//import android.app.Activity;
//import android.content.Context;
//import android.content.pm.ApplicationInfo;
//import android.content.pm.PackageInfo;
//import android.content.pm.PackageManager;
//import android.content.pm.Signature;
//import android.net.wifi.ScanResult;
//import android.net.wifi.WifiInfo;
//import android.net.wifi.WifiManager;
//import android.os.Build;
//import android.os.Bundle;
//import android.util.Log;
//
//import androidx.core.app.ActivityCompat;
//
//import com.alibaba.fastjson.JSONObject;
//import com.amap.api.location.AMapLocation;
//import com.amap.api.location.AMapLocationClient;
//import com.amap.api.location.AMapLocationClientOption;
//import com.amap.api.location.AMapLocationListener;
//
//
//import java.lang.reflect.Method;
//import java.util.Arrays;
//
//import io.dcloud.common.core.permission.PermissionControler;
//import io.dcloud.feature.uniapp.annotation.UniJSMethod;
//import io.dcloud.feature.uniapp.bridge.UniJSCallback;
//import io.dcloud.feature.uniapp.common.UniModule;
//
///**
// * 参考：
// *      https://juejin.cn/post/7194743378830409783
// *      https://nativesupport.dcloud.net.cn/NativePlugin/
// *      https://lbs.amap.com/api/android-location-sdk/locationsummary
// *      https://blog.csdn.net/Ysmooth_Alone/article/details/130176427
// *      https://blog.csdn.net/qq_38387996/article/details/128655883
// *
// */
//public class AmapLocationSdk extends UniModule implements AMapLocationListener {
//
//    private final int REQUEST_CODE = 997;
//
//    private static String[] permissions = new String[]{
//            //通过GPS得到精确位置
//            Manifest.permission.ACCESS_FINE_LOCATION,
//            //通过网络得到粗略位置
//            Manifest.permission.ACCESS_COARSE_LOCATION,
//            //访问网络，某些位置信息需要从网络服务器获取
//            Manifest.permission.INTERNET,
//            //访问WiFi状态，需要WiFi信息用于网络定位
//            Manifest.permission.ACCESS_WIFI_STATE,
//            //修改WiFi状态，发起WiFi扫描, 需要WiFi信息用于网络定位
//            Manifest.permission.CHANGE_WIFI_STATE,
//            //用于获取运营商信息，用于支持提供运营商信息相关的接口
//            Manifest.permission.ACCESS_NETWORK_STATE,
//            //访问网络的变化, 需要某些信息用于网络定位
//            Manifest.permission.CHANGE_NETWORK_STATE,
//            //用于读取手机当前的状态
//            Manifest.permission.READ_PHONE_STATE,
//            //用于写入缓存数据到扩展存储卡
//            Manifest.permission.WRITE_EXTERNAL_STORAGE,
//            //蓝牙扫描权限
//            Manifest.permission.BLUETOOTH,
//            Manifest.permission.BLUETOOTH_ADMIN,
//            //前台service权限
//            Manifest.permission.FOREGROUND_SERVICE,
//            //后台定位权限
////            Manifest.permission.ACCESS_BACKGROUND_LOCATION,
//            //A-GPS辅助定位权限，方便GPS快速准确定位
//            Manifest.permission.ACCESS_LOCATION_EXTRA_COMMANDS
//
//    };
//    //声明AMapLocationClient类对象
//    public AMapLocationClient mLocationClient = null;
//    private  UniJSCallback uniJSCallback;
//    private JSONObject _options;
//
//    public AmapLocationSdk() {
//        super();
//
//
//    }
//
////    @AfterPermissionGranted(1)
////    private void requirePermission() {
////        String[] permissions = {
////                Manifest.permission.ACCESS_COARSE_LOCATION,
////                Manifest.permission.ACCESS_FINE_LOCATION,
////                Manifest.permission.READ_PHONE_STATE,
////                Manifest.permission.WRITE_EXTERNAL_STORAGE
////        };
////        String[] permissionsForQ = {
////                Manifest.permission.ACCESS_COARSE_LOCATION,
////                Manifest.permission.ACCESS_FINE_LOCATION,
////                Manifest.permission.ACCESS_BACKGROUND_LOCATION, //target为Q时，动态请求后台定位权限
////                Manifest.permission.READ_PHONE_STATE,
////                Manifest.permission.WRITE_EXTERNAL_STORAGE
////        };
////        if (Build.VERSION.SDK_INT >= 29 ? EasyPermissions.hasPermissions(this, permissionsForQ) :
////                EasyPermissions.hasPermissions(this, permissions)) {
////            Toast.makeText(this, "权限OK", Toast.LENGTH_LONG).show();
////        } else {
////            EasyPermissions.requestPermissions(this, "需要权限",
////                    1, Build.VERSION.SDK_INT >= 29 ? permissionsForQ : permissions);
////        }
////    }
//
//
//    @UniJSMethod(uiThread = true)
//    public void startLocate(JSONObject options, UniJSCallback callback) {
//        _options = options;
//        if(uniJSCallback == null){
//            uniJSCallback  = callback;
//        }
//        if (ActivityCompat.checkSelfPermission(mUniSDKInstance.getContext(), Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED) {
//            _startLocate(_options);
//        } else {
//            requestPermissions();
//        }
//
//
//    }
//
//    @UniJSMethod(uiThread = false)
//    public JSONObject stopLocate() {
//        JSONObject data = new JSONObject();
//        data.put("success", true);
//        data.put("type", "StopLocation");
//        if(mLocationClient != null) {
//            try {
//                uniJSCallback = null;
//                mLocationClient.stopLocation();//停止定位后，本地定位服务并不会被销毁
//                mLocationClient.onDestroy();//销毁定位客户端，同时销毁本地定位服务。
//                data.put("msg", "成功停止定位");
//            }catch (Throwable t) {
//                data.put("msg", "停止定位失败：" + t.getMessage());
//                t.printStackTrace();
//            }
//
//        }
//        mLocationClient = null;
//        data.put("success", true);
//        data.put("type", "StopLocation");
//        return data;
//    }
//
//    private void _startLocate(JSONObject options){
//        String appKey = options.getString("appKey");
//        int locateInterval = options.getIntValue("locateInterval");
//        String coorType = options.getString("coorType");
//        boolean isMock = false;
//        try {
//            Context thisContent = this.mWXSDKInstance.getContext();
//            JSONObject appEnv = getApplicationEnv(this.mWXSDKInstance.getContext());
//            if(appEnv.getBoolean("success") && appEnv.getString("dataDir") != null) {
//                if(!appEnv.getString("dataDir").equals("/data/user/0/"+appEnv.getString("packageName"))) {
//                    isMock = true;
//                    throw new RuntimeException("环境检查不通过,疑似篡改定位信息");
//                }
//            }
//            AMapLocationClient.updatePrivacyAgree(thisContent,true);
//            AMapLocationClient.updatePrivacyShow(thisContent,true, true);
//            if(appKey!=null)
//                AMapLocationClient.setApiKey(appKey);
//            //初始化定位
//            mLocationClient = new AMapLocationClient(thisContent);
//            //设置定位回调监听
//            mLocationClient.setLocationListener(this);
//
//            //初始化AMapLocationClientOption对象
//            AMapLocationClientOption mLocationOption = new AMapLocationClientOption();
//
//            //设置定位场景，目前支持三种场景（签到、出行、运动，默认无场景）
////            mLocationOption.setLocationPurpose(AMapLocationClientOption.AMapLocationPurpose.SignIn);
//
//            //设置定位模式为AMapLocationMode.Hight_Accuracy，高精度模式。会同时使用网络定位和GPS定位，优先返回最高精度的定位结果，以及对应的地址描述信息。
//            //AMapLocationMode.Battery_Saving，低功耗模式。不会使用GPS和其他传感器，只会使用网络定位（Wi-Fi和基站定位）；
//            //AMapLocationMode.Device_Sensors，仅设备模式。不需要连接网络，只使用GPS进行定位，这种模式下不支持室内环境的定位，需要在室外环境下才可以成功定位。
//            mLocationOption.setLocationMode(AMapLocationClientOption.AMapLocationMode.Hight_Accuracy);
//
//            //设置定位协议
//            mLocationOption.setLocationProtocol(AMapLocationClientOption.AMapLocationProtocol.HTTPS);
//
//            //设置定位场景，根据场景快速修改option，当不需要场景时，可以设置为NULL， 注意： 不建议设置场景和自定义option混合使用 设置场景后
////            mLocationOption.setLocationPurpose(AMapLocationClientOption.AMapLocationPurpose.SignIn);
//            //获取一次定位结果：该方法默认为false。
////            mLocationOption.setOnceLocation(true);
//
//            //设置定位是否等待WIFI列表刷新 定位精度会更高，但是定位速度会变慢1-3秒
////            mLocationOption.setOnceLocationLatest(true);
//
//            //设置定位间隔,单位毫秒,默认为2000ms，最低1000ms。
//            mLocationOption.setInterval(locateInterval);
//
//            //设置是否返回地址信息（默认返回地址信息）
//            mLocationOption.setNeedAddress(false);
//            //设置是否开启wifi始终扫描 只有设置了android.permission.WRITE_SECURE_SETTINGS权限后才会开启 开启后，即使关闭wifi开关的情况下也会扫描wifi 默认值为：true, 开启wifi始终扫描 此方法为静态方法
//            mLocationOption.setOpenAlwaysScanWifi(true);
//            //设置是否使用设备传感器 默认值：false 不使用设备传感器
//            mLocationOption.setSensorEnable(false);
//            //isWifiActiveScan是布尔型参数，true表示会主动刷新设备wifi模块，获取到最新鲜的wifi列表（wifi新鲜程度决定定位精度）；false表示不主动刷新。
//            mLocationOption.setWifiScan(true);
//
//            //设置是否允许模拟位置,默认为true，允许模拟位置
//            mLocationOption.setMockEnable(false);
//
//           // 设置首次定位是否等待卫星定位结果
//            mLocationOption.setGpsFirst(true);
//
//            //置优先返回卫星定位信息时等待卫星定位结果的超时时间
//            mLocationOption.setGpsFirstTimeout(3000);
//            //单位是毫秒，默认30000毫秒，建议超时时间不要低于8000毫秒。
//            mLocationOption.setHttpTimeOut(30000);
//            //设置退出时是否杀死进程
//            mLocationOption.setKillProcess(true);
//            //启定位缓存功能，在高精度模式和低功耗模式下进行的网络定位结果均会生成本地缓存，不区分单次定位还是连续定位。GPS定位结果不会被缓存。
//            mLocationOption.setLocationCacheEnable(true);
//            //设置场景模式后最好调用一次stop，再调用start以保证场景模式生效
//
//            mLocationClient.setLocationOption(mLocationOption);
//            mLocationClient.stopLocation();
//            mLocationClient.startLocation();
//
//            JSONObject result = new JSONObject();
//            result.put("success", true);
//            result.put("type", "StartLocation");
////            result.put("msg", e.getMessage());
//
//            uniJSCallback.invokeAndKeepAlive(result);
//
//
//        } catch (Exception e) {
//            JSONObject result = new JSONObject();
//            result.put("success", false);
//            result.put("type", "StartLocation");
//            result.put("msg", e.getMessage());
//
//            JSONObject jsonData = new JSONObject();
//            jsonData.put("isMock", isMock);
//            result.put("data", jsonData
//            );
//
//
//            uniJSCallback.invokeAndKeepAlive(result);
//        }
//    }
//
//    @UniJSMethod(uiThread = false)
//    private JSONObject getApplicationEnv(Context context) {
//        JSONObject result = new JSONObject();
//        result.put("success", true);
//        //
//        ApplicationInfo appInfo = context.getApplicationContext().getApplicationInfo();
//        result.put("packageName", appInfo.packageName);
//        result.put("className", appInfo.className);
//        result.put("icon", appInfo.icon);
//        result.put("BOARD", Build.BOARD);
//        result.put("MODEL", Build.MODEL);
//        result.put("DEVICE", Build.DEVICE);
//
//
//        try {
//            PackageManager packageManager = context.getPackageManager();
//            PackageInfo packageInfo = packageManager.getPackageInfo("com.bdh.sjc.loctest", 0);
//            result.put("packageName1", packageInfo.packageName);
//            Signature[] signatures = packageInfo.signatures;
//            String sStr = "";
//            for(Signature s : signatures) {
//                sStr += s.toCharsString()+",";
//            }
//            result.put("signatures", sStr);
//        } catch (Exception e) {
//
//        }
//
//
//        if(appInfo.metaData != null) {
//            Bundle metaData = appInfo.metaData;
//            JSONObject extraInfoObj = new JSONObject();
//            for(String key : metaData.keySet()) {
//                extraInfoObj.put(key, metaData.get(key));
//            }
//            result.put("metaData", extraInfoObj);
//        }
//
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
//            if(context.getDataDir() != null)
//                result.put("dataDir", context.getDataDir().getAbsolutePath());
//        }
//        result.put("type", "AppInfo");
//
//        WifiManager wm = (WifiManager) context.getSystemService(context.WIFI_SERVICE);
//        if(wm != null) {
//            WifiInfo wmInfo = wm.getConnectionInfo();
//            JSONObject extraInfoObj = new JSONObject();
//            extraInfoObj.put("bssid", wmInfo.getBSSID());
//            extraInfoObj.put("ip", wmInfo.getIpAddress());
//            extraInfoObj.put("ssid", wmInfo.getSSID());
//            extraInfoObj.put("mac", wmInfo.getMacAddress());
//            String scanResStr = "";
//            if(wm.getScanResults() != null) {
//                for(ScanResult wfRes : wm.getScanResults()) {
//                    scanResStr += wfRes.BSSID + ",";
//                }
//            }
//            extraInfoObj.put("bssids", scanResStr);
//            result.put("wmInfo", extraInfoObj);
//        }
//
//        uniJSCallback.invokeAndKeepAlive(result);
//        return result;
//    }
//
//    @Override
//    public void onLocationChanged(AMapLocation aMapLocation) {
//        JSONObject result = new JSONObject();
//        result.put("type", "LocationChanged");
////        result.put("msg", reason);
//        if(aMapLocation.getErrorCode() == AMapLocation.LOCATION_SUCCESS) {
//            try {
//                result.put("success", true);
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("latitude", aMapLocation.getLatitude()); //获取纬度信息
//                jsonObject.put("longitude", aMapLocation.getLongitude());//获取经度信息
//                jsonObject.put("altitude", aMapLocation.hasAltitude() ? aMapLocation.getAltitude() : -999);//获取高程
//                jsonObject.put("accuracy", aMapLocation.getAccuracy());//获取定位精度，默认值为0.0f
//                jsonObject.put("bearing", aMapLocation.getBearing());//获取方向角(单位：度） 默认值：0.0, 取值范围：【0，360】，其中0度表示正北方向，90度表示正东，180度表示正南，270度表示正西
//                jsonObject.put("conScenario", aMapLocation.getConScenario());//室内外置信度 室内：且置信度取值在[1 ～ 100]，值越大在在室内的可能性越大 室外：且置信度取值在[-100 ～ -1] ,值越小在在室内的可能性越大 无法识别室内外：置信度返回值为 0
//                jsonObject.put("coordType", aMapLocation.getCoordType()); //获取坐标系类型 高德定位sdk会返回两种坐标系 AMapLocation.COORD_TYPE_GCJ02 -- GCJ02坐标系 AMapLocation.COORD_TYPE_WGS84 -- WGS84坐标系,国外定位时返回的是WGS84坐标系
//                jsonObject.put("gpsAccuracyStatus", aMapLocation.getGpsAccuracyStatus()); //获取卫星信号强度，仅在卫星定位时有效,值为 #GPS_ACCURACY_BAD，#GPS_ACCURACY_GOOD，#GPS_ACCURACY_UNKNOWN
//                jsonObject.put("locationType", aMapLocation.getLocationType()); //获取定位结果来源
//                jsonObject.put("provider", aMapLocation.getProvider()); //获取定位提供者,lbs:高德网络定位,gps:卫星定位
//                jsonObject.put("satellites", aMapLocation.getSatellites()); //获取当前可用卫星数量, 仅在卫星定位时有效,
//
//
//                //获取定位结果的可信度 只有在定位成功时才有意义
//                //非常可信 AMapLocation.TRUSTED_LEVEL_HIGH
//                //可信度一般AMapLocation.TRUSTED_LEVEL_NORMAL
//                //可信度较低 AMapLocation.TRUSTED_LEVEL_LOW
//                //非常不可信 AMapLocation.TRUSTED_LEVEL_BAD
//                jsonObject.put("trustedLevel", aMapLocation.getTrustedLevel()); //获取定位结果的可信度 只有在定位成功时才有意义,
//                jsonObject.put("isMock", aMapLocation.isMock()); //是否修改定位
//                jsonObject.put("isFixLastLocation", aMapLocation.isFixLastLocation()); //?
//                jsonObject.put("isOffset", aMapLocation.isOffset()); //?
//                jsonObject.put("isFromMockProvider", aMapLocation.isFromMockProvider()); //?
//
//
//                if(aMapLocation.isMock()) {
//                    result.put("success", false);
//                    result.put("msg", "定位失败,疑似篡改定位信息E1");
//                    result.put("code", -999);
//                } else if(aMapLocation.getLocationType() == AMapLocation.LOCATION_TYPE_GPS
//                        && aMapLocation.hasAltitude()
//                        && aMapLocation.getAltitude() == 0) {
//
//                    result.put("success", false);
//                    result.put("msg", "定位失败,疑似篡改定位信息E2");
//                    result.put("code", -999);
//                    jsonObject.put("isMock", true); //是否修改定位
//                } else {
//                    result.put("success", true);
//                }
//
//
//
//                if(aMapLocation.getLocationQualityReport()!=null) {
//                    JSONObject locationQualityReport = new JSONObject();
//                    locationQualityReport.put("adviseMessage", aMapLocation.getLocationQualityReport().getAdviseMessage()); //获取提示语义,状态良好时，返回的是内容为空 根据当前的质量报告，给出相应的建议
//                    locationQualityReport.put("GPSSatellites", aMapLocation.getLocationQualityReport().getGPSSatellites()); //获取当前的卫星数， 只有在非低功耗模式下此值才有效
//                    locationQualityReport.put("GPSStatus", aMapLocation.getLocationQualityReport().getGPSStatus()); //获取卫星状态信息，只有在非低功耗模式下此值才有效
//                    locationQualityReport.put("netUseTime", aMapLocation.getLocationQualityReport().getNetUseTime()); //获取网络定位时的网络耗时 单位：毫秒
//                    locationQualityReport.put("networkType", aMapLocation.getLocationQualityReport().getNetworkType()); //获取网络连接类型（2G、3G、4G、WIFI)
//                    locationQualityReport.put("isInstalledHighDangerMockApp", aMapLocation.getLocationQualityReport().isInstalledHighDangerMockApp()); //是否安装了高危位置模拟软件 首次定位可能没有结果
//                    locationQualityReport.put("isWifiAble", aMapLocation.getLocationQualityReport().isWifiAble()); //wifi开关是否打开 如果wifi关闭建议打开wifi开关，提高定位质量
//                    jsonObject.put("locationQualityReport", locationQualityReport);
////                Log.d("AMapLocation=receive", locationQualityReport.toJSONString());
//                }
//
//                if(aMapLocation.getExtras()!=null) {
//                    Bundle exinfo = aMapLocation.getExtras();
//                    JSONObject extraInfoObj = new JSONObject();
//                    for(String key : exinfo.keySet()) {
//                        extraInfoObj.put(key, exinfo.get(key));
//                    }
//                    jsonObject.put("extraInfo", extraInfoObj);
//                    Log.d("AMapLocation=receive", extraInfoObj.toJSONString());
//                }
//
//
////            String adb_enable=getSystemProperty("persist.sys.usb.config","") ;
////            boolean adbOpen = false;
////            if(adb_enable.equals("adb")){
////                adbOpen = true;
////            }else {
////                adbOpen = false;
////            }
////
////
////            jsonObject.put("adbEnable", adbOpen);
//                result.put("data", jsonObject);
//            } catch (Exception e) {
//                result.put("success", false);
//                result.put("code", e.getMessage());
//            }
//
//
//        } else {
//            result.put("success", false);
//            result.put("code", aMapLocation.getErrorInfo());
//        }
//        if(!result.getBoolean("success")) {
//            this.stopLocate();
//        }
//        uniJSCallback.invokeAndKeepAlive(result);
//    }
//
//    private static String getSystemProperty(String key, String defaultValue) {
//        String value = defaultValue;
//        try {
//            Class<?> clazz= Class.forName("android.os.SystemProperties");
//            Method get = clazz.getMethod("get", String.class, String.class);
//            value = (String)(get.invoke(clazz, key, ""));
//        } catch (Exception e) {
//
//        }
//        return value;
//    }
//    //权限管理
//
//    void requestPermissions() {
//        if (mUniSDKInstance.getContext() != null) {
//            Activity activity = (Activity) mUniSDKInstance.getContext();
//            PermissionControler.requestPermissions(activity, permissions, REQUEST_CODE);
//        }
//    }
//
//    @Override
//    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
//        if (requestCode == REQUEST_CODE) {
//            for (int i = 0; i < permissions.length; i++) {
//                String preName = permissions[i];
//                int granted = grantResults[i];
//                if (Manifest.permission.ACCESS_FINE_LOCATION.equals(preName) && granted == PackageManager.PERMISSION_GRANTED) {
//                    _startLocate(_options);
//                }
//            }
//        }
//        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
//    }
//
//
//
//}
