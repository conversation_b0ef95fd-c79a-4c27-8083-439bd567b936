package com.example.bdh_smart_agric_app;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.widget.TextView;

//import com.umeng.message.UmengNotifyClick;
//import com.umeng.message.entity.UMessage;
//import com.umeng.message.sample.R;

public class MfrMessageActivity extends Activity {

    private static final String TAG = "MfrMessageActivity";

//    private final UmengNotifyClick mNotificationClick = new UmengNotifyClick() {
//        @Override
//        public void onMessage(UMessage msg) {
//            final String body = msg.getRaw().toString();
//            Log.d(TAG, "body: " + body);
//            if (!TextUtils.isEmpty(body)) {
//                runOnUiThread(new Runnable() {
//                    @Override
//                    public void run() {
//                        ((TextView) findViewById(R.id.tv)).setText(body);
//                    }
//                });
//            }
//        }
//    };

    @Override
    protected void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        setContentView(R.layout.mfr_message_layout);
//        mNotificationClick.onCreate(this, getIntent());
        // 启动 MainActivity
//        Intent mainIntent = new Intent(this, MainActivity.class);
//        mainIntent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
//        startActivity(mainIntent);
//        finish();

    }

//    @Override
//    protected void onNewIntent(Intent intent) {
//        super.onNewIntent(intent);
////        mNotificationClick.onNewIntent(intent);
//    }
}
