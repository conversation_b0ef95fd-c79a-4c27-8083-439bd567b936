package com.example.bdh_smart_agric_app;
import android.content.Intent;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;
import android.util.Log;

import static com.tekartik.sqflite.Constant.TAG;

import android.content.Context;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.amap.api.location.AMapLocationClient;
import com.ccb.ccbnetpay.message.CcbPayResultListener;
import com.ccb.ccbnetpay.platform.CcbPayPlatform;
import com.ccb.ccbnetpay.platform.Platform;
import com.example.bdh_smart_agric_app.sdk.AmapLocationManager;
import com.example.bdh_smart_agric_app.sdk.TencentTcicSDK;
import com.example.caller.BankABCCaller;
import com.tencent.cloud.huiyansdkface.facelight.api.WbCloudFaceContant;
import com.tencent.cloud.huiyansdkface.facelight.api.WbCloudFaceVerifySdk;
import com.tencent.cloud.huiyansdkface.facelight.api.listeners.WbCloudFaceVerifyLoginListener;
import com.tencent.cloud.huiyansdkface.facelight.api.listeners.WbCloudFaceVerifyResultListener;
import com.tencent.cloud.huiyansdkface.facelight.api.result.WbFaceError;
import com.tencent.cloud.huiyansdkface.facelight.api.result.WbFaceVerifyResult;
import com.tencent.cloud.huiyansdkface.facelight.process.FaceVerifyStatus;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Map;

import io.dcloud.feature.sdk.DCUniMPSDK;
import io.dcloud.feature.sdk.Interface.IUniMP;
import io.dcloud.feature.unimp.config.UniMPOpenConfiguration;
import io.flutter.embedding.android.FlutterActivity;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;

import com.icbc.ndf.jft.PayActivity;
import com.icbc.ndf.jft.contants.PayResultVO;
import com.icbc.ndf.jft.utils.OrderResultCallBack;


public class MainActivity extends FlutterActivity {

    private static final String TAG = "MainActivity";
    private IUniMP uniMP;
    private MethodChannel channel ;

    private static final int OPEN_FDD_RESULT = 0x100001;


    CcbPayResultListener listener = new CcbPayResultListener() {
        @Override
        public void onSuccess(Map<String, String> result) {
            Log.d(TAG, "接口请求成功 --"+result);
            for (Map.Entry entry : result.entrySet()) {
                Log.d(TAG, "key --"+entry.getKey()+"  value --"+entry.getValue());
            }
        }
        @Override
        public void onFailed(String msg) {
            Log.d(TAG, "接口请求失败 --"+msg);
        }
    };

    final AmapLocationManager.LocationListener locationListener = new AmapLocationManager.LocationListener() {
        @Override
        public void onNotify(Map<String,Object> data) {
            runOnUiThread(new Runnable() {
                @Override
                public void run() {
                    channel.invokeMethod("GetVirtualGPSLocaionJudgementResult",data);
                }
            });
        }
    };

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        //在flutter 里设置高德定位隐私合规不起作用写到这里
        AMapLocationClient.updatePrivacyShow(getContext(),true,true);
        AMapLocationClient.updatePrivacyAgree(getContext(),true);
        Log.i("amp","隐私合规初始化");

        AmapLocationManager.getInstance().init(this,locationListener);
        //GeneratedPluginRegistrant.registerWith(new FlutterEngine(this));
        try {
            getFlutterEngine().getPlugins().add(new com.example.bdh_smart_agric_app.app_links.AppLinksPlugin());
        } catch (Exception e) {
            io.flutter.Log.e(TAG, "Error registering plugin app_links, com.example.bdh_smart_agric_app.app_links.AppLinksPlugin", e);
        }

//        MethodChannel channel = new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), "com.bdh.smart");
         channel = new MethodChannel(getFlutterEngine().getDartExecutor().getBinaryMessenger(), "com.bdh.smart");
        Log.i("amp","创建方法通道");

        channel.setMethodCallHandler(new MethodChannel.MethodCallHandler() {
            @Override
            public void onMethodCall(@NonNull MethodCall call, @NonNull MethodChannel.Result result) {
                if(call.method.equals("nativePop")) {
                    onBackPressed();
                    result.success("success");
                }
                else if(call.method.equals("openMenu")){
                    try {
                        if(uniMP != null){
                            uniMP.closeUniMP();
                        }

                        HashMap<String,Object> hashMap = (HashMap) (call.arguments);
                        UniMPOpenConfiguration uniMPOpenConfiguration = new UniMPOpenConfiguration();
//                        uniMPOpenConfiguration.path = (String) (hashMap.get("path"));

                        JSONObject userInfoObject = new JSONObject((HashMap)(hashMap.get("userInfo")));

                        uniMPOpenConfiguration.extraData.put("userInfo",userInfoObject);
                        uniMPOpenConfiguration.extraData.put("token",hashMap.get("token"));
                        uniMPOpenConfiguration.extraData.put("path",hashMap.get("path"));
                         uniMP = DCUniMPSDK.getInstance().openUniMP(getContext(),"__UNI__D302F11",uniMPOpenConfiguration);
                        result.success("success");
                    } catch (Exception e) {
                        result.error("failed",e.toString(),null);
                        throw new RuntimeException(e);
                    }
                }
                else if(call.method.equals("quit")){
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.JELLY_BEAN) {
                        finishAffinity();
                    }
                    result.success("success");
                    System.exit(0);
                }

                else if(call.method.equals("openFdd")){
                    HashMap<String,String> hashMap = (HashMap) (call.arguments);
                    String url = hashMap.get("url");
                    Intent intent = new Intent(MainActivity.this, H5Activity.class);
                    intent.putExtra("url", url);
                    startActivityForResult(intent,OPEN_FDD_RESULT);
                    result.success("success");
                }

                else if(call.method.equals("closeFdd")){
                    EventBus.getDefault().post(new  MessageEvent("closeFdd"));
                    result.success("success");
                }

                // 跳转农业银行支付
                else if(call.method.equals("openABCToPay")){
                    // 获取传递的数据
                    Map<String, Object> options = (Map<String, Object>) call.arguments;
                    Map<String, Object> params = (Map<String, Object>) options.get("tokenId");
                    Log.d(TAG, "农行参数: "+params);
                    String tokenId = params.get("tokenId").toString();
                    /**
                     * 判断手机上是否具备调起农行掌银的条件
                     */
                    if (BankABCCaller.isBankABCAvaiable(MainActivity.this)) {
                        /**
                         * 调起农行掌银
                         */
                        BankABCCaller.startBankABC(MainActivity.this, "com.beidahuangxinxi.app", "com.example.bdh_smart_agric_app.MainActivity", "pay", tokenId);
                        result.success("success");
                    } else {//客户手机未安装农行掌银APP的处理逻辑，由第三方APP自行实现
                        Toast.makeText(MainActivity.this, "没安装农行掌银，或已安装农行掌银版本不支持", Toast.LENGTH_LONG).show();
                        result.error("not_install","没安装农行掌银，或已安装农行掌银版本不支持",null);
                    }
                }

                // 跳转建设银行支付
                else if(call.method.equals("openCCBToPay")){
                    // 获取传递的数据
                    Map<String, Object> options = (Map<String, Object>) call.arguments;
                    String tokenId = (String) options.get("tokenId");
                    Log.d(TAG, "建行参数: "+tokenId);
                    Platform ccbPayPlatform = new CcbPayPlatform
                            .Builder()
                            .setActivity(MainActivity.this)
                            // 支付回调
                            .setListener(listener)
                            // 商户串 格式见 （3.1）
                            .setParams(tokenId)
                            // 支付模式  建行APP 支付
                            .setPayStyle(Platform.PayStyle.APP_PAY)
                            .build();
                    ccbPayPlatform.pay();
                    result.success("success");
                }

                // 跳转工行支付
                else if(call.method.equals("openICBCToPay")){
                    Map<String, Object> options = (Map<String, Object>) call.arguments;
                    String params = (String) options.get("params");
                    Log.d(TAG, "工行参数: "+params);
                    PayActivity.openPayActivity(MainActivity.this, params , new OrderResultCallBack() {
                        @Override
                        public void onSuccess(PayResultVO str) {
                            result.success("success");
                        }
                        @Override
                        public void onError(PayResultVO str) {
                            //支付失败或取消，根据返回码判断
                            result.error("failed",str.toString(),null);
                        }
                    });
                }

                //跳转腾讯人脸
                else if(call.method.equals("openTxFace")){
                    // 获取传递的数据
                    Bundle data = new Bundle();
                    Map<String, String> options = (Map<String, String>) call.arguments;
                    WbCloudFaceVerifySdk.InputData inputData = new WbCloudFaceVerifySdk.InputData(
                            options.get("faceId"),
                            options.get("orderNo"),
                            options.get("appId"),
                            options.get("version"),
                            options.get("nonce"),
                            options.get("userId"),
                            options.get("sign"),
                            FaceVerifyStatus.Mode.GRADE,
                            "Cl39Upw4m2dBMR963VxggPM/o+hBYPwcR7tG1Ifl/f/qGGY0DuTE/1cDhFOUpaeRdabglUfMbaLs67K2GIk4ERX1nqwHbTviTcNT9wC1OgRsohEcpj0hTf7GXALlI4b0aXAF1CcQvHl1p6s579A9imx4NxOdq0aSzmEnaZiqCJoVq9F7qU1pB6MDKtdcbb3Gpl+xI858cgYjEQxV7DhMdnk6ItLvroEbBiHLy4nvRK98fIN7TJp2sSFrC7p9AacjQYydHGrFWnEUTGvr4k05EeBPeMmDP0HZs2iykFLa6Y3rt7rYKkD/CKQPQK+o/Iyxkcx6g94fv/K6tp8EBQtA7w==");

                    data.putSerializable(WbCloudFaceContant.INPUT_DATA, inputData);
                    data.putString(WbCloudFaceContant.LANGUAGE, WbCloudFaceContant.LANGUAGE_ZH_CN);
                    data.putString(WbCloudFaceContant.COLOR_MODE, WbCloudFaceContant.WHITE);
                    data.putBoolean(WbCloudFaceContant.VIDEO_UPLOAD, false);
                    data.putBoolean(WbCloudFaceContant.PLAY_VOICE, false);
                    data.putBoolean(WbCloudFaceContant.IS_LANDSCAPE, false);
                    data.putBoolean(WbCloudFaceContant.IS_FOLLOW_SYSTEM, false);
                    data.putString(WbCloudFaceContant.COMPARE_TYPE, WbCloudFaceContant.ID_CARD);
                    data.putBoolean(WbCloudFaceContant.IS_ENABLE_LOG, false);
                    Log.d(TAG, "WbCloudFaceVerifySdk initSdk");
                    WbCloudFaceVerifySdk.getInstance().initSdk(getContext(), data, new WbCloudFaceVerifyLoginListener() {
                        @Override
                        public void onLoginSuccess() {
                            //登录sdk成功
                            Log.i(TAG, "onLoginSuccess");
                            WbCloudFaceVerifySdk.getInstance().startWbFaceVerifySdk(getContext(), new WbCloudFaceVerifyResultListener() {
                                @Override
                                public void onFinish(WbFaceVerifyResult wbFaceVerifyResult) {


                                    if(wbFaceVerifyResult.isSuccess()){
                                        JSONObject jsonObject = new JSONObject();
                                        try {
                                            jsonObject.put("code","0");
                                            jsonObject.put("msg","刷脸成功! Sign=" + wbFaceVerifyResult.getSign() + "; liveRate=" + wbFaceVerifyResult.getLiveRate() +
                                                    "; similarity=" + wbFaceVerifyResult.getSimilarity() + "userImageString=" + wbFaceVerifyResult.getUserImageString());
                                            result.success(jsonObject.toString());
                                        } catch (JSONException e) {

                                            result.success("{\"code\":\"\",\"msg\":\"" + e.toString() + "\"}");
                                        }



                                    }else {
                                        JSONObject jsonObject = new JSONObject();
                                        try {
                                            WbFaceError error = wbFaceVerifyResult.getError();
                                            jsonObject.put("code","1");
                                            String msg = "刷脸失败！desc=" + error.getDesc();
                                            if (error.getDomain().equals(WbFaceError.WBFaceErrorDomainCompareServer)) {
                                                //虽然对比失败，但这个domain也可以去后台查询刷脸结果
                                                msg = "对比失败!面部特征变化较大！";
                                            }
                                            jsonObject.put("msg",msg);
                                            result.success(jsonObject.toString());
                                        } catch (JSONException e) {

                                            result.success("{\"code\":\"\",\"msg\":\"" + e.toString() + "\"}");
                                        }
                                    }

                                    WbCloudFaceVerifySdk.getInstance().release();
                                }
                            });
                        }

                        @Override
                        public void onLoginFailed(WbFaceError error) {
                            //登录失败
                            Log.i(TAG, "onLoginFailed!");
                            JSONObject jsonObject = new JSONObject();
                            try {
                                jsonObject.put("code","1");
                                jsonObject.put("msg","登录失败！domain=" + error.getDomain() + " ;code= " + error.getCode()
                                        + " ;desc=" + error.getDesc() + ";reason=" + error.getReason());
                                result.success(jsonObject.toString());
                            } catch (JSONException e) {
                                result.success("{\"code\":\"\",\"msg\":\"" + e.toString() + "\"}");
                                throw new RuntimeException(e);
                            }

                            //刷脸结束后，释放资源
                            WbCloudFaceVerifySdk.getInstance().release();
                        }
                    });
                }

                //pos机支付
                else if(call.method.equals("openPosRecharge")){
                    Map<String, String> options = (Map<String, String>) call.arguments;
                    Log.i(TAG, "openPosRecharge="+options);
//                    print(options);
                    // intent.setComponent(newComponentName("com.abc.smartpos.bankpay" ,"com.ab c.smartpos.bankpay.ui.MainActivity"));
                    // intent.putExtra("appName", "农行收单应用");
                    // intent.putExtra("transId", "消费");
                    // JSONObject jsonObject = new JSONObject();
                    // try {
                    //     jsonObject.put("amt","************");
                    //     intent.putExtra("transData", jsonObject.toString());
                    // } catch (JSONException e) {
                    //     e.printStackTrace();
                    // }
                    // startActivityForResult(intent, 1);
                    result.notImplemented();
                }
                //腾讯课堂-初始化
                else if (call.method.equals("tcicInitX5Core")) {
                    String licenseKey = call.argument("licenseKey");
                    TencentTcicSDK.initX5Core(licenseKey, result);
                }
                //腾讯课堂-进入教室
                else if (call.method.equals("tcicJoinClass")) {
                    int schoolId = call.argument("schoolId");
                    int classId = call.argument("classId");
                    String userId = call.argument("userId");
                    String token = call.argument("token");
                    TencentTcicSDK.joinClass(MainActivity.this,schoolId, classId, userId, token, result);
                }
                //友盟推送初始化
//                else if (call.method.equals("initUmengPush")) {
//                    PushAgent api = PushAgent.getInstance(getContext());
//                    boolean  isNotificationEnabled = api.isNotificationEnabled();
//                    if(isNotificationEnabled){
//                        //建议在子线程中初始化
//                        boolean isMainProcess = UMUtils.isMainProgress(getContext());
//                        if(isMainProcess) {
//                            new Thread(new Runnable() {
//                                @Override
//                                public void run() {
//                                    initUmeng(getContext());
//                                }
//                            }).start();
//                        }else {
//                            initUmeng(getContext());
//                        }
//                    }
//
//                }
                //虚拟定位检查
                else if(call.method.equals("virtualGPSLocaionJudgement")){
                    String apiKey = call.argument("apiKey");

                    if(apiKey == null){
                        result.error("failed","apiKey is empty",null);
                        return;
                    }
                    Integer locateInterval = call.argument("locateInterval");
                    if(locateInterval == null){
                        locateInterval = 1000;
                    }

                    AmapLocationManager.getInstance().start(apiKey,locateInterval);
                    result.success("success");
                }
                //打开定位设置页面
                else if(call.method.equals("openAndroidLocationSecureSettings")){
                    Intent intent = new Intent(Settings.ACTION_LOCATION_SOURCE_SETTINGS);
                    startActivity(intent);
                    result.success("success");
                }

                else {

                    result.notImplemented();
                }

            }
        });
    }


    @Override
    protected void onResume() {
        super.onResume();
        String param = getIntent().getStringExtra("from_bankabc_param");
        if (param != null) {
            Log.i("MainActivity","收到来自农行支付回调:"+ param);
        }

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if(requestCode == OPEN_FDD_RESULT){
            if(isFinishing() || isDestroyed()){
                return;
            }
            channel.invokeMethod("fddClosed",null);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        AmapLocationManager.getInstance().onRequestPermissionsResult(requestCode,permissions,grantResults);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        AmapLocationManager.getInstance().quit();
    }
}

