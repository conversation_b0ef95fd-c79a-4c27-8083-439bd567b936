allprojects {
   configurations.all {
        // resolutionStrategy {
        //     force 'androidx.webkit:webkit:1.8.0'
        // }
    }
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
// subprojects {
//      afterEvaluate { project ->
//         if (project.plugins.hasPlugin("com.android.application") ||
//                 project.plugins.hasPlugin("com.android.library")) {
//             project.android {
//                 compileSdkVersion 35
//                 buildToolsVersion "35.0.0"
//             }
//         }
//     }
// }
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

configurations.all {
    resolutionStrategy {
        force 'androidx.core:core-ktx:1.9.0'
    }
}