name: bdh_smart_agric_app
description: "北大荒数字农业"
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 40.00.47+400047

environment:
  sdk: '>=3.4.1 <4.0.0'
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.6
  dio: 5.4.3
  record: ^4.4.4
  pretty_dio_logger: ^1.1.1
  video_editor: ^3.0.0
  localstorage: ^5.0.0
  shared_preferences: ^2.2.3
  provider: ^6.1.2
  permission_handler: ^11.4.0
  image_picker: ^1.1.2
  oktoast: ^3.4.0
  path_provider: ^2.1.3
  logger: ^2.3.0
  sqflite: ^2.3.0
  cached_network_image: ^3.3.1
  encrypt: ^5.0.3
  uuid: ^4.4.0
  crypto: ^3.0.3
  webview_flutter: ^4.8.0
  flutter_widget_from_html_core: ^0.14.12
  flutter_localizations:
    sdk: flutter
  pull_to_refresh_flutter3: ^2.0.2
  badges: ^3.1.2
  #  video_player: ^2.9.2
  video_player_android: 2.5.0 #华为p40运行此版本
  audio_video_progress_bar: ^2.0.3
  flutter_echarts: ^2.5.0
  bruno: ^3.4.3
  mime: ^1.0.4
  http_parser: ^4.0.2
  card_swiper: ^3.0.1
  photo_view: ^0.15.0
  flutter_slidable: ^3.1.0
  flutter_staggered_grid_view: ^0.7.0
  lottie: ^3.1.2
  # photo_album_manager:
  # 最新8.1.5 版本会报错
  file_picker: ^9.2.1
  video_thumbnail: ^0.5.3
  url_launcher: ^6.3.0
  amap_flutter_location: ^3.0.0
  intl: ^0.19.0
  # fluwx: 4.5.6
  fluwx: ^5.4.1
  install_plugin: ^2.1.0
  connectivity_plus: ^6.1.1
  # wakelock_plus: ^1.2.10
  package_info_plus: ^8.0.2
  event_bus: ^2.0.0
  device_info_plus: ^10.1.2
  flutter_map: ^7.0.2
  latlong2: ^0.9.1
  sensors_plus: ^6.0.1
  flutter_inappwebview: ^6.1.5
  dart_jts: ^0.3.0+1
  flutter_spinkit: ^5.2.1
  gif: ^2.3.0
  umeng_common_sdk: ^1.2.8
  tdesign_flutter: ^0.1.7
  dotted_line: ^3.2.3
  flutter_card_swiper: ^7.0.2
  # 返回后台运行
  move_to_background: ^1.0.2
  # 手写签名
  signature: ^5.5.0
  # 拍照
  # camera: ^0.11.0+2
  camera: 0.10.6
  # object 自动生成序列化方法
  freezed_annotation: ^2.4.4
  json_annotation: ^4.9.0
  # 加载 svg 图片
  flutter_svg: ^2.0.16
  # 侧滑删除
  flutter_swipe_action_cell: ^3.1.5
  # 压缩图片
  flutter_image_compress: ^2.3.0
  pointycastle: ^3.9.1
  # 内存溢出检测工具
  leak_tracker: ^10.0.5
  rsa_encrypt: ^2.0.0
  umeng_push_sdk: ^2.3.0
  umeng_verify_sdk: ^2.0.0
  pinput: ^5.0.1
  smart_auth: ^2.0.0
  # markdown库,用于chat功能
  flutter_markdown: ^0.7.6+2
  decimal: ^3.2.1
  # HTML解析库
  flutter_widget_from_html: ^0.14.11
  # stepper 步骤指示器
  easy_stepper: ^0.8.5+1
  flutter_local_notifications_plus: ^17.2.5
  # 文件浏览
  open_filex: ^4.7.0
  # 滑动窗下方原点特效
  smooth_page_indicator: ^1.2.1
  # mobile_scanner: ^6.0.10
  mobile_scanner: 5.2.3
  get: ^4.7.2
  app_settings: ^6.1.1
  external_app_launcher: ^4.0.3
  image: ^4.3.0
  # appcheck: ^1.5.4+1
  # 文字转语音
  flutter_tts: ^4.2.3
  flutter_downloader: ^1.11.8
  syncfusion_flutter_pdfviewer: ^26.1.42
  # 级联选择
  flutter_picker_plus: ^1.5.2
  # 日期选择器
  date_picker_plus: 3.0.2
  qr_code_scanner: ^1.0.1
  # 原生chart,解决Echart横向滑动不流畅，部分布局h5实现困难等问题
  charts_painter: ^3.1.1
  flutter_app_badger: ^1.5.0


dependency_overrides:
  intl: ^0.19.0
  photo_view: ^0.15.0
  flutter_plugin_android_lifecycle: 2.0.24
  fl_chart: ^0.69.0
  loader_overlay: ^4.0.0
  flutter_picker_plus: ^1.5.0
  flutter_vlc_player: ^7.4.0
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.13
  freezed: ^2.5.7
  json_serializable: ^6.9.0

flutter:
  uses-material-design: true
  fonts:
    - family: iconfont
      fonts:
        - asset: assets/fonts/iconfont.ttf
    - family: kuaile # 站酷 快乐字体  https://fonts.google.com/?selection.family=Noto+Sans+SC&subset=chinese-simplified
      fonts:
        - asset: assets/fonts/ZCOOLKuaiLe-Regular.ttf
    - family: bdh
      fonts:
        - asset: assets/fonts/bdh.ttf
    - family: bayon
      fonts:
        - asset: assets/fonts/bayon-regular.ttf
    - family: BEBAS
      fonts:
        - asset: assets/fonts/BEBAS.ttf

    - family: AlimamaShuHeiTi-Bold
      fonts:
        - asset: assets/fonts/AlimamaShuHeiTi-Bold.ttf

    - family: YouSheBiaoTiHei
      fonts:
        - asset: assets/fonts/YouSheBiaoTiHei.ttf
  assets:
    - assets/images/
    - assets/json/
    - assets/images/menu/
    - assets/images/weather/
    - assets/images/weather2/
    - assets/images/weather2/xiao/
    - assets/images/guideImage/
    - assets/images/pay/
    - assets/images/chat/
    - assets/images/finance/
    - assets/images/finance/pfImgs/
    - assets/images/pay/bankLogo/
    - assets/images/costAnalysis/
    - assets/js/
    - assets/images/subsidy/
    - assets/images/insure/
    - assets/html/notice.htm
    - assets/images/cal/
    - assets/releaseAudit/
    - assets/images/serviceApplicationIcon/
    - assets/images/agriculturalrecords/
    - assets/images/bdhDigitalImages/
    - assets/images/patrol/
    - assets/images/agricultural/
    - assets/images/agricultural/subsidy/
    - assets/images/three/
    - assets/images/diaLand/
    - assets/images/agriculturalcondition/

  
